<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>LICENSE</key>
		<data>
		42oX9oAD1yj/wA6aqkh1wyx+qqA=
		</data>
		<key>ios-arm64/FBSDKLoginKit.framework/FBSDKLoginKit</key>
		<data>
		GuYTsbvwNT50ezUrOClUeaBDcDE=
		</data>
		<key>ios-arm64/FBSDKLoginKit.framework/Headers/FBSDKLoginAuthType.h</key>
		<data>
		dbPjG4QiKnnxeSs+knOQIJW0JVg=
		</data>
		<key>ios-arm64/FBSDKLoginKit.framework/Headers/FBSDKLoginCompletionParametersBlock.h</key>
		<data>
		fPr6aC3BILzRqRBn1j6oocs3jMc=
		</data>
		<key>ios-arm64/FBSDKLoginKit.framework/Headers/FBSDKLoginErrorDomain.h</key>
		<data>
		cneem1mczyhnLYxbDjbbR5mEmzM=
		</data>
		<key>ios-arm64/FBSDKLoginKit.framework/Headers/FBSDKLoginKit-Swift.h</key>
		<data>
		PF51NW/pGV+IWoOrpBJGUhDbN30=
		</data>
		<key>ios-arm64/FBSDKLoginKit.framework/Headers/FBSDKLoginKit.h</key>
		<data>
		OUJgU2YPcKajMNRVhOXvWNOomHs=
		</data>
		<key>ios-arm64/FBSDKLoginKit.framework/Headers/FBSDKLoginManagerLoginResultBlock.h</key>
		<data>
		hdGFhcZwp8w9OhQTkCmMyUDevXU=
		</data>
		<key>ios-arm64/FBSDKLoginKit.framework/Info.plist</key>
		<data>
		KVKW4hH6cqoHXSWusRLq2sWW/Zc=
		</data>
		<key>ios-arm64/FBSDKLoginKit.framework/Modules/FBSDKLoginKit.swiftmodule/arm64-apple-ios.abi.json</key>
		<data>
		54b2zs1bfpk6hiYuhiW6naCu+h0=
		</data>
		<key>ios-arm64/FBSDKLoginKit.framework/Modules/FBSDKLoginKit.swiftmodule/arm64-apple-ios.private.swiftinterface</key>
		<data>
		Qy1jW7TeaoFT1sLZ34yzpG5prLU=
		</data>
		<key>ios-arm64/FBSDKLoginKit.framework/Modules/FBSDKLoginKit.swiftmodule/arm64-apple-ios.swiftdoc</key>
		<data>
		8J2FE7HMOp+RXbPwCDl/OxT8S7c=
		</data>
		<key>ios-arm64/FBSDKLoginKit.framework/Modules/FBSDKLoginKit.swiftmodule/arm64-apple-ios.swiftinterface</key>
		<data>
		Qy1jW7TeaoFT1sLZ34yzpG5prLU=
		</data>
		<key>ios-arm64/FBSDKLoginKit.framework/Modules/module.modulemap</key>
		<data>
		KrDKcg24trXz/SqZuZkQjJYkUsc=
		</data>
		<key>ios-arm64/FBSDKLoginKit.framework/PrivacyInfo.xcprivacy</key>
		<data>
		c/GhFel+DoHL4HradxiX7nIkOl4=
		</data>
		<key>ios-arm64/FBSDKLoginKit.framework/_CodeSignature/CodeResources</key>
		<data>
		m8kUCE07dzWpHU91/ktlyDjjaQM=
		</data>
		<key>ios-arm64/dSYMs/FBSDKLoginKit.framework.dSYM/Contents/Info.plist</key>
		<data>
		vTorsb8xnOasw4pKU7HkDL0frEo=
		</data>
		<key>ios-arm64/dSYMs/FBSDKLoginKit.framework.dSYM/Contents/Resources/DWARF/FBSDKLoginKit</key>
		<data>
		Y8HIt4hZdvaZS02lyo/G3ObAhRg=
		</data>
		<key>ios-arm64/dSYMs/FBSDKLoginKit.framework.dSYM/Contents/Resources/Relocations/aarch64/FBSDKLoginKit.yml</key>
		<data>
		bg5lxx+abu9vQ38mpg4wo2FBW08=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKLoginKit.framework/Versions/A/FBSDKLoginKit</key>
		<data>
		VamiGN2Y4jd3rCer7w5VfvSdgPQ=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKLoginKit.framework/Versions/A/Headers/FBSDKLoginAuthType.h</key>
		<data>
		dbPjG4QiKnnxeSs+knOQIJW0JVg=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKLoginKit.framework/Versions/A/Headers/FBSDKLoginCompletionParametersBlock.h</key>
		<data>
		fPr6aC3BILzRqRBn1j6oocs3jMc=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKLoginKit.framework/Versions/A/Headers/FBSDKLoginErrorDomain.h</key>
		<data>
		cneem1mczyhnLYxbDjbbR5mEmzM=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKLoginKit.framework/Versions/A/Headers/FBSDKLoginKit-Swift.h</key>
		<data>
		xjD7iydkJ0Thh2ruGVwGJndMUDA=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKLoginKit.framework/Versions/A/Headers/FBSDKLoginKit.h</key>
		<data>
		OUJgU2YPcKajMNRVhOXvWNOomHs=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKLoginKit.framework/Versions/A/Headers/FBSDKLoginManagerLoginResultBlock.h</key>
		<data>
		hdGFhcZwp8w9OhQTkCmMyUDevXU=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKLoginKit.framework/Versions/A/Modules/FBSDKLoginKit.swiftmodule/arm64-apple-ios-macabi.abi.json</key>
		<data>
		54b2zs1bfpk6hiYuhiW6naCu+h0=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKLoginKit.framework/Versions/A/Modules/FBSDKLoginKit.swiftmodule/arm64-apple-ios-macabi.private.swiftinterface</key>
		<data>
		VHDfya8vkHPqrtMW5FtgK/LrH5w=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKLoginKit.framework/Versions/A/Modules/FBSDKLoginKit.swiftmodule/arm64-apple-ios-macabi.swiftdoc</key>
		<data>
		2aMNvv8o/5r4PiLppdETi3wlr60=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKLoginKit.framework/Versions/A/Modules/FBSDKLoginKit.swiftmodule/arm64-apple-ios-macabi.swiftinterface</key>
		<data>
		VHDfya8vkHPqrtMW5FtgK/LrH5w=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKLoginKit.framework/Versions/A/Modules/FBSDKLoginKit.swiftmodule/x86_64-apple-ios-macabi.abi.json</key>
		<data>
		54b2zs1bfpk6hiYuhiW6naCu+h0=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKLoginKit.framework/Versions/A/Modules/FBSDKLoginKit.swiftmodule/x86_64-apple-ios-macabi.private.swiftinterface</key>
		<data>
		qK2dWaAD2HvETYp24+93t0taE9c=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKLoginKit.framework/Versions/A/Modules/FBSDKLoginKit.swiftmodule/x86_64-apple-ios-macabi.swiftdoc</key>
		<data>
		NGtUbPgFM0zsXj+cBKBSa5+Ws4I=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKLoginKit.framework/Versions/A/Modules/FBSDKLoginKit.swiftmodule/x86_64-apple-ios-macabi.swiftinterface</key>
		<data>
		qK2dWaAD2HvETYp24+93t0taE9c=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKLoginKit.framework/Versions/A/Modules/module.modulemap</key>
		<data>
		KrDKcg24trXz/SqZuZkQjJYkUsc=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKLoginKit.framework/Versions/A/Resources/Info.plist</key>
		<data>
		UcZqwKk49CHSSU1nSSqGLaywRWs=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKLoginKit.framework/Versions/A/Resources/PrivacyInfo.xcprivacy</key>
		<data>
		c/GhFel+DoHL4HradxiX7nIkOl4=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKLoginKit.framework/Versions/A/_CodeSignature/CodeResources</key>
		<data>
		80TE89E6WW5w4O2PNUuzEP5J5yM=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/dSYMs/FBSDKLoginKit.framework.dSYM/Contents/Info.plist</key>
		<data>
		vTorsb8xnOasw4pKU7HkDL0frEo=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/dSYMs/FBSDKLoginKit.framework.dSYM/Contents/Resources/DWARF/FBSDKLoginKit</key>
		<data>
		U5WmpYQwS4RMX4m75VtzOmoLJZw=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/dSYMs/FBSDKLoginKit.framework.dSYM/Contents/Resources/Relocations/aarch64/FBSDKLoginKit.yml</key>
		<data>
		cgMbvEvSOM/eX1XvzPzJoa0psCk=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/dSYMs/FBSDKLoginKit.framework.dSYM/Contents/Resources/Relocations/x86_64/FBSDKLoginKit.yml</key>
		<data>
		7y3snvTaWHlUCG1JBSNlhcOkK9g=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKLoginKit.framework/FBSDKLoginKit</key>
		<data>
		e+GrD0QfcrQPRiJoF2soDPYV5S4=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKLoginKit.framework/Headers/FBSDKLoginAuthType.h</key>
		<data>
		dbPjG4QiKnnxeSs+knOQIJW0JVg=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKLoginKit.framework/Headers/FBSDKLoginCompletionParametersBlock.h</key>
		<data>
		fPr6aC3BILzRqRBn1j6oocs3jMc=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKLoginKit.framework/Headers/FBSDKLoginErrorDomain.h</key>
		<data>
		cneem1mczyhnLYxbDjbbR5mEmzM=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKLoginKit.framework/Headers/FBSDKLoginKit-Swift.h</key>
		<data>
		xjD7iydkJ0Thh2ruGVwGJndMUDA=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKLoginKit.framework/Headers/FBSDKLoginKit.h</key>
		<data>
		OUJgU2YPcKajMNRVhOXvWNOomHs=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKLoginKit.framework/Headers/FBSDKLoginManagerLoginResultBlock.h</key>
		<data>
		hdGFhcZwp8w9OhQTkCmMyUDevXU=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKLoginKit.framework/Info.plist</key>
		<data>
		5Iismi9QMj5R3rmLK7d0RMjCo9Q=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKLoginKit.framework/Modules/FBSDKLoginKit.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<data>
		54b2zs1bfpk6hiYuhiW6naCu+h0=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKLoginKit.framework/Modules/FBSDKLoginKit.swiftmodule/arm64-apple-ios-simulator.private.swiftinterface</key>
		<data>
		ouBVeevyUeuZ7++seMzrJM8Pw5E=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKLoginKit.framework/Modules/FBSDKLoginKit.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<data>
		xmmHYSkmYmTTcUwGspxqqjfVtag=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKLoginKit.framework/Modules/FBSDKLoginKit.swiftmodule/arm64-apple-ios-simulator.swiftinterface</key>
		<data>
		ouBVeevyUeuZ7++seMzrJM8Pw5E=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKLoginKit.framework/Modules/FBSDKLoginKit.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<data>
		54b2zs1bfpk6hiYuhiW6naCu+h0=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKLoginKit.framework/Modules/FBSDKLoginKit.swiftmodule/x86_64-apple-ios-simulator.private.swiftinterface</key>
		<data>
		9xLvEcb57VLJ95OxV9NpjwodpQM=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKLoginKit.framework/Modules/FBSDKLoginKit.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<data>
		hUkBOw6N/o3v/hxeaTy/ysIFoCs=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKLoginKit.framework/Modules/FBSDKLoginKit.swiftmodule/x86_64-apple-ios-simulator.swiftinterface</key>
		<data>
		9xLvEcb57VLJ95OxV9NpjwodpQM=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKLoginKit.framework/Modules/module.modulemap</key>
		<data>
		KrDKcg24trXz/SqZuZkQjJYkUsc=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKLoginKit.framework/PrivacyInfo.xcprivacy</key>
		<data>
		c/GhFel+DoHL4HradxiX7nIkOl4=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKLoginKit.framework/_CodeSignature/CodeResources</key>
		<data>
		1jzMvzaqxqxZgJf81PUrfTmcGCY=
		</data>
		<key>ios-arm64_x86_64-simulator/dSYMs/FBSDKLoginKit.framework.dSYM/Contents/Info.plist</key>
		<data>
		vTorsb8xnOasw4pKU7HkDL0frEo=
		</data>
		<key>ios-arm64_x86_64-simulator/dSYMs/FBSDKLoginKit.framework.dSYM/Contents/Resources/DWARF/FBSDKLoginKit</key>
		<data>
		5YHUvsGdyQCX064Z1tLRdBAjkE8=
		</data>
		<key>ios-arm64_x86_64-simulator/dSYMs/FBSDKLoginKit.framework.dSYM/Contents/Resources/Relocations/aarch64/FBSDKLoginKit.yml</key>
		<data>
		XCK4XgQ3kPAmA4bA8VpW1hdIxys=
		</data>
		<key>ios-arm64_x86_64-simulator/dSYMs/FBSDKLoginKit.framework.dSYM/Contents/Resources/Relocations/x86_64/FBSDKLoginKit.yml</key>
		<data>
		KTcnzwA8YnstkPv1ObpAJuvjquU=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>LICENSE</key>
		<dict>
			<key>hash</key>
			<data>
			42oX9oAD1yj/wA6aqkh1wyx+qqA=
			</data>
			<key>hash2</key>
			<data>
			JGiNyKThXtEPUCL2A80E+FzHN+UTW+RkFoApZE8iHm8=
			</data>
		</dict>
		<key>ios-arm64/FBSDKLoginKit.framework/FBSDKLoginKit</key>
		<dict>
			<key>hash</key>
			<data>
			GuYTsbvwNT50ezUrOClUeaBDcDE=
			</data>
			<key>hash2</key>
			<data>
			StOS00H3HDVt15JczyXCOxq3irEJz1Kewu8XT+QCPs0=
			</data>
		</dict>
		<key>ios-arm64/FBSDKLoginKit.framework/Headers/FBSDKLoginAuthType.h</key>
		<dict>
			<key>hash</key>
			<data>
			dbPjG4QiKnnxeSs+knOQIJW0JVg=
			</data>
			<key>hash2</key>
			<data>
			En8JspBXmCZrSWkWaxJV5tKzr8At6tqf53zIGNa2VYY=
			</data>
		</dict>
		<key>ios-arm64/FBSDKLoginKit.framework/Headers/FBSDKLoginCompletionParametersBlock.h</key>
		<dict>
			<key>hash</key>
			<data>
			fPr6aC3BILzRqRBn1j6oocs3jMc=
			</data>
			<key>hash2</key>
			<data>
			7WGMXXull6LrlXHwbqyalo/ZMN0JSBtHbC6cWu8k2eI=
			</data>
		</dict>
		<key>ios-arm64/FBSDKLoginKit.framework/Headers/FBSDKLoginErrorDomain.h</key>
		<dict>
			<key>hash</key>
			<data>
			cneem1mczyhnLYxbDjbbR5mEmzM=
			</data>
			<key>hash2</key>
			<data>
			zFgW2vVnY7X9MEoilZ5/3iQAYiab+N4zlq9kMvgkl/4=
			</data>
		</dict>
		<key>ios-arm64/FBSDKLoginKit.framework/Headers/FBSDKLoginKit-Swift.h</key>
		<dict>
			<key>hash</key>
			<data>
			PF51NW/pGV+IWoOrpBJGUhDbN30=
			</data>
			<key>hash2</key>
			<data>
			D3li8kobOOJ4PbkOf7ON37Mqrx13pBP0uJ6CjdQ/xUQ=
			</data>
		</dict>
		<key>ios-arm64/FBSDKLoginKit.framework/Headers/FBSDKLoginKit.h</key>
		<dict>
			<key>hash</key>
			<data>
			OUJgU2YPcKajMNRVhOXvWNOomHs=
			</data>
			<key>hash2</key>
			<data>
			8b6xB2UCDMQ82B5SNTD1CpaH+pyHg+K3Yy0MxzTdgTE=
			</data>
		</dict>
		<key>ios-arm64/FBSDKLoginKit.framework/Headers/FBSDKLoginManagerLoginResultBlock.h</key>
		<dict>
			<key>hash</key>
			<data>
			hdGFhcZwp8w9OhQTkCmMyUDevXU=
			</data>
			<key>hash2</key>
			<data>
			Zb9GWGgaSc6xOtE0UsSlBLPOxWMfbTeNdSsr0fvIp74=
			</data>
		</dict>
		<key>ios-arm64/FBSDKLoginKit.framework/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			KVKW4hH6cqoHXSWusRLq2sWW/Zc=
			</data>
			<key>hash2</key>
			<data>
			bpt4/Ufyy+ASeLEUQbMQ1PJjHCguwNqKf41bxfk9Rk8=
			</data>
		</dict>
		<key>ios-arm64/FBSDKLoginKit.framework/Modules/FBSDKLoginKit.swiftmodule/arm64-apple-ios.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			54b2zs1bfpk6hiYuhiW6naCu+h0=
			</data>
			<key>hash2</key>
			<data>
			otLvCaFFvvsH2p2rr92zyti2Xrh/LUYkV/dlnLl/VDw=
			</data>
		</dict>
		<key>ios-arm64/FBSDKLoginKit.framework/Modules/FBSDKLoginKit.swiftmodule/arm64-apple-ios.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			Qy1jW7TeaoFT1sLZ34yzpG5prLU=
			</data>
			<key>hash2</key>
			<data>
			W8vLX4P+Hcv5I9wT4f4A5UMDF2p8UzUzmd/c6SKqCcw=
			</data>
		</dict>
		<key>ios-arm64/FBSDKLoginKit.framework/Modules/FBSDKLoginKit.swiftmodule/arm64-apple-ios.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			8J2FE7HMOp+RXbPwCDl/OxT8S7c=
			</data>
			<key>hash2</key>
			<data>
			2GpveiGwuliFv0HuLNq2LkoqEc04XZEydtpx9i96cQM=
			</data>
		</dict>
		<key>ios-arm64/FBSDKLoginKit.framework/Modules/FBSDKLoginKit.swiftmodule/arm64-apple-ios.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			Qy1jW7TeaoFT1sLZ34yzpG5prLU=
			</data>
			<key>hash2</key>
			<data>
			W8vLX4P+Hcv5I9wT4f4A5UMDF2p8UzUzmd/c6SKqCcw=
			</data>
		</dict>
		<key>ios-arm64/FBSDKLoginKit.framework/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			KrDKcg24trXz/SqZuZkQjJYkUsc=
			</data>
			<key>hash2</key>
			<data>
			/LNPo6mK3Ap58ptMqxKbx/hlGBOSkSGDoDN3+CL3VlA=
			</data>
		</dict>
		<key>ios-arm64/FBSDKLoginKit.framework/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash</key>
			<data>
			c/GhFel+DoHL4HradxiX7nIkOl4=
			</data>
			<key>hash2</key>
			<data>
			V+yTPiE3CaHxIWdHy5KWEryxwgIcGAfRgIV8XZH0Qpc=
			</data>
		</dict>
		<key>ios-arm64/FBSDKLoginKit.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash</key>
			<data>
			m8kUCE07dzWpHU91/ktlyDjjaQM=
			</data>
			<key>hash2</key>
			<data>
			ySmQm1SSf6DTnRN6+2WYjcmTfiYWIi73tA8j+bJ2c3M=
			</data>
		</dict>
		<key>ios-arm64/dSYMs/FBSDKLoginKit.framework.dSYM/Contents/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			vTorsb8xnOasw4pKU7HkDL0frEo=
			</data>
			<key>hash2</key>
			<data>
			CT5B41zup2nZ/nPDQMhhzccdUxkSEiSlf2wBNdMJUP8=
			</data>
		</dict>
		<key>ios-arm64/dSYMs/FBSDKLoginKit.framework.dSYM/Contents/Resources/DWARF/FBSDKLoginKit</key>
		<dict>
			<key>hash</key>
			<data>
			Y8HIt4hZdvaZS02lyo/G3ObAhRg=
			</data>
			<key>hash2</key>
			<data>
			bA+rVi6RyCrQMMu4JkObKVoE+YlYP0HYlgb6jIKOw7g=
			</data>
		</dict>
		<key>ios-arm64/dSYMs/FBSDKLoginKit.framework.dSYM/Contents/Resources/Relocations/aarch64/FBSDKLoginKit.yml</key>
		<dict>
			<key>hash</key>
			<data>
			bg5lxx+abu9vQ38mpg4wo2FBW08=
			</data>
			<key>hash2</key>
			<data>
			NA94UHuwKTR9TYLy7GU3v0hEJgdrQrmmOy5ukuJU0zk=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKLoginKit.framework/FBSDKLoginKit</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/FBSDKLoginKit</string>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKLoginKit.framework/Headers</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/Headers</string>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKLoginKit.framework/Modules</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/Modules</string>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKLoginKit.framework/Resources</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/Resources</string>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKLoginKit.framework/Versions/A/FBSDKLoginKit</key>
		<dict>
			<key>hash</key>
			<data>
			VamiGN2Y4jd3rCer7w5VfvSdgPQ=
			</data>
			<key>hash2</key>
			<data>
			TCrYAmmrPpNdNUlwcxILNZyYmEblgF8VB4gj785fJYI=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKLoginKit.framework/Versions/A/Headers/FBSDKLoginAuthType.h</key>
		<dict>
			<key>hash</key>
			<data>
			dbPjG4QiKnnxeSs+knOQIJW0JVg=
			</data>
			<key>hash2</key>
			<data>
			En8JspBXmCZrSWkWaxJV5tKzr8At6tqf53zIGNa2VYY=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKLoginKit.framework/Versions/A/Headers/FBSDKLoginCompletionParametersBlock.h</key>
		<dict>
			<key>hash</key>
			<data>
			fPr6aC3BILzRqRBn1j6oocs3jMc=
			</data>
			<key>hash2</key>
			<data>
			7WGMXXull6LrlXHwbqyalo/ZMN0JSBtHbC6cWu8k2eI=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKLoginKit.framework/Versions/A/Headers/FBSDKLoginErrorDomain.h</key>
		<dict>
			<key>hash</key>
			<data>
			cneem1mczyhnLYxbDjbbR5mEmzM=
			</data>
			<key>hash2</key>
			<data>
			zFgW2vVnY7X9MEoilZ5/3iQAYiab+N4zlq9kMvgkl/4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKLoginKit.framework/Versions/A/Headers/FBSDKLoginKit-Swift.h</key>
		<dict>
			<key>hash</key>
			<data>
			xjD7iydkJ0Thh2ruGVwGJndMUDA=
			</data>
			<key>hash2</key>
			<data>
			jMaPKo/8VzW30tDEgaa7AFlzku9Zp1zdjoGZik1KZjQ=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKLoginKit.framework/Versions/A/Headers/FBSDKLoginKit.h</key>
		<dict>
			<key>hash</key>
			<data>
			OUJgU2YPcKajMNRVhOXvWNOomHs=
			</data>
			<key>hash2</key>
			<data>
			8b6xB2UCDMQ82B5SNTD1CpaH+pyHg+K3Yy0MxzTdgTE=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKLoginKit.framework/Versions/A/Headers/FBSDKLoginManagerLoginResultBlock.h</key>
		<dict>
			<key>hash</key>
			<data>
			hdGFhcZwp8w9OhQTkCmMyUDevXU=
			</data>
			<key>hash2</key>
			<data>
			Zb9GWGgaSc6xOtE0UsSlBLPOxWMfbTeNdSsr0fvIp74=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKLoginKit.framework/Versions/A/Modules/FBSDKLoginKit.swiftmodule/arm64-apple-ios-macabi.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			54b2zs1bfpk6hiYuhiW6naCu+h0=
			</data>
			<key>hash2</key>
			<data>
			otLvCaFFvvsH2p2rr92zyti2Xrh/LUYkV/dlnLl/VDw=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKLoginKit.framework/Versions/A/Modules/FBSDKLoginKit.swiftmodule/arm64-apple-ios-macabi.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			VHDfya8vkHPqrtMW5FtgK/LrH5w=
			</data>
			<key>hash2</key>
			<data>
			6ZSsdNFZNR+wvFwhvwVT/yXi0TprTCUHixhi8y1fHJ8=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKLoginKit.framework/Versions/A/Modules/FBSDKLoginKit.swiftmodule/arm64-apple-ios-macabi.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			2aMNvv8o/5r4PiLppdETi3wlr60=
			</data>
			<key>hash2</key>
			<data>
			ZK7rrAqObw2CwCzYg0PZKUIzbHijuRC5AvqXkJmqnmA=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKLoginKit.framework/Versions/A/Modules/FBSDKLoginKit.swiftmodule/arm64-apple-ios-macabi.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			VHDfya8vkHPqrtMW5FtgK/LrH5w=
			</data>
			<key>hash2</key>
			<data>
			6ZSsdNFZNR+wvFwhvwVT/yXi0TprTCUHixhi8y1fHJ8=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKLoginKit.framework/Versions/A/Modules/FBSDKLoginKit.swiftmodule/x86_64-apple-ios-macabi.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			54b2zs1bfpk6hiYuhiW6naCu+h0=
			</data>
			<key>hash2</key>
			<data>
			otLvCaFFvvsH2p2rr92zyti2Xrh/LUYkV/dlnLl/VDw=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKLoginKit.framework/Versions/A/Modules/FBSDKLoginKit.swiftmodule/x86_64-apple-ios-macabi.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			qK2dWaAD2HvETYp24+93t0taE9c=
			</data>
			<key>hash2</key>
			<data>
			WLWBvqlu56aZbs130MmxC00ykDj1gRT3ZjX3RiwW3pI=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKLoginKit.framework/Versions/A/Modules/FBSDKLoginKit.swiftmodule/x86_64-apple-ios-macabi.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			NGtUbPgFM0zsXj+cBKBSa5+Ws4I=
			</data>
			<key>hash2</key>
			<data>
			9BPrGiCpu9MooNIIqjYNh7Yb0BS5W6n8+B3lKdPSTEU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKLoginKit.framework/Versions/A/Modules/FBSDKLoginKit.swiftmodule/x86_64-apple-ios-macabi.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			qK2dWaAD2HvETYp24+93t0taE9c=
			</data>
			<key>hash2</key>
			<data>
			WLWBvqlu56aZbs130MmxC00ykDj1gRT3ZjX3RiwW3pI=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKLoginKit.framework/Versions/A/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			KrDKcg24trXz/SqZuZkQjJYkUsc=
			</data>
			<key>hash2</key>
			<data>
			/LNPo6mK3Ap58ptMqxKbx/hlGBOSkSGDoDN3+CL3VlA=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKLoginKit.framework/Versions/A/Resources/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			UcZqwKk49CHSSU1nSSqGLaywRWs=
			</data>
			<key>hash2</key>
			<data>
			Ryse+CGzrE6CUzusGTDM67WSTJ8g3e1S0DPxKuyIL4I=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKLoginKit.framework/Versions/A/Resources/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash</key>
			<data>
			c/GhFel+DoHL4HradxiX7nIkOl4=
			</data>
			<key>hash2</key>
			<data>
			V+yTPiE3CaHxIWdHy5KWEryxwgIcGAfRgIV8XZH0Qpc=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKLoginKit.framework/Versions/A/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash</key>
			<data>
			80TE89E6WW5w4O2PNUuzEP5J5yM=
			</data>
			<key>hash2</key>
			<data>
			5a5NiIS27kCvp1xjKnIPmXnExGKHvnFDP5SLKlioyaE=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKLoginKit.framework/Versions/Current</key>
		<dict>
			<key>symlink</key>
			<string>A</string>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/dSYMs/FBSDKLoginKit.framework.dSYM/Contents/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			vTorsb8xnOasw4pKU7HkDL0frEo=
			</data>
			<key>hash2</key>
			<data>
			CT5B41zup2nZ/nPDQMhhzccdUxkSEiSlf2wBNdMJUP8=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/dSYMs/FBSDKLoginKit.framework.dSYM/Contents/Resources/DWARF/FBSDKLoginKit</key>
		<dict>
			<key>hash</key>
			<data>
			U5WmpYQwS4RMX4m75VtzOmoLJZw=
			</data>
			<key>hash2</key>
			<data>
			UCGrMAM2cx36pPzxJcXOuTSoYs9phoUoVkoiyXM/kls=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/dSYMs/FBSDKLoginKit.framework.dSYM/Contents/Resources/Relocations/aarch64/FBSDKLoginKit.yml</key>
		<dict>
			<key>hash</key>
			<data>
			cgMbvEvSOM/eX1XvzPzJoa0psCk=
			</data>
			<key>hash2</key>
			<data>
			TX4sdFIM1OUigs/do7ISjQVW5NAngxNj47IMefZiIsw=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/dSYMs/FBSDKLoginKit.framework.dSYM/Contents/Resources/Relocations/x86_64/FBSDKLoginKit.yml</key>
		<dict>
			<key>hash</key>
			<data>
			7y3snvTaWHlUCG1JBSNlhcOkK9g=
			</data>
			<key>hash2</key>
			<data>
			h/9eC2RB1BC7PSDzINHjE3K6K/kF0JZNQjK9yB4oZOI=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKLoginKit.framework/FBSDKLoginKit</key>
		<dict>
			<key>hash</key>
			<data>
			e+GrD0QfcrQPRiJoF2soDPYV5S4=
			</data>
			<key>hash2</key>
			<data>
			kjqJknT+/oHwutsrqRnfPf/ZXKzUA/DmPF3Kvwvp9PI=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKLoginKit.framework/Headers/FBSDKLoginAuthType.h</key>
		<dict>
			<key>hash</key>
			<data>
			dbPjG4QiKnnxeSs+knOQIJW0JVg=
			</data>
			<key>hash2</key>
			<data>
			En8JspBXmCZrSWkWaxJV5tKzr8At6tqf53zIGNa2VYY=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKLoginKit.framework/Headers/FBSDKLoginCompletionParametersBlock.h</key>
		<dict>
			<key>hash</key>
			<data>
			fPr6aC3BILzRqRBn1j6oocs3jMc=
			</data>
			<key>hash2</key>
			<data>
			7WGMXXull6LrlXHwbqyalo/ZMN0JSBtHbC6cWu8k2eI=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKLoginKit.framework/Headers/FBSDKLoginErrorDomain.h</key>
		<dict>
			<key>hash</key>
			<data>
			cneem1mczyhnLYxbDjbbR5mEmzM=
			</data>
			<key>hash2</key>
			<data>
			zFgW2vVnY7X9MEoilZ5/3iQAYiab+N4zlq9kMvgkl/4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKLoginKit.framework/Headers/FBSDKLoginKit-Swift.h</key>
		<dict>
			<key>hash</key>
			<data>
			xjD7iydkJ0Thh2ruGVwGJndMUDA=
			</data>
			<key>hash2</key>
			<data>
			jMaPKo/8VzW30tDEgaa7AFlzku9Zp1zdjoGZik1KZjQ=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKLoginKit.framework/Headers/FBSDKLoginKit.h</key>
		<dict>
			<key>hash</key>
			<data>
			OUJgU2YPcKajMNRVhOXvWNOomHs=
			</data>
			<key>hash2</key>
			<data>
			8b6xB2UCDMQ82B5SNTD1CpaH+pyHg+K3Yy0MxzTdgTE=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKLoginKit.framework/Headers/FBSDKLoginManagerLoginResultBlock.h</key>
		<dict>
			<key>hash</key>
			<data>
			hdGFhcZwp8w9OhQTkCmMyUDevXU=
			</data>
			<key>hash2</key>
			<data>
			Zb9GWGgaSc6xOtE0UsSlBLPOxWMfbTeNdSsr0fvIp74=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKLoginKit.framework/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			5Iismi9QMj5R3rmLK7d0RMjCo9Q=
			</data>
			<key>hash2</key>
			<data>
			pFKazk/4kgWf03kWzHnwu7QmERTKorsrJcCIg/lqh/k=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKLoginKit.framework/Modules/FBSDKLoginKit.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			54b2zs1bfpk6hiYuhiW6naCu+h0=
			</data>
			<key>hash2</key>
			<data>
			otLvCaFFvvsH2p2rr92zyti2Xrh/LUYkV/dlnLl/VDw=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKLoginKit.framework/Modules/FBSDKLoginKit.swiftmodule/arm64-apple-ios-simulator.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			ouBVeevyUeuZ7++seMzrJM8Pw5E=
			</data>
			<key>hash2</key>
			<data>
			wkOUEumaMOZRIE73EpGFHRFOE/E6POi8Xpd+VVevC8U=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKLoginKit.framework/Modules/FBSDKLoginKit.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			xmmHYSkmYmTTcUwGspxqqjfVtag=
			</data>
			<key>hash2</key>
			<data>
			/zjaVRoFwEGicRWJ7mMqKDEe0Dj/mDVDluWz9IwvmoE=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKLoginKit.framework/Modules/FBSDKLoginKit.swiftmodule/arm64-apple-ios-simulator.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			ouBVeevyUeuZ7++seMzrJM8Pw5E=
			</data>
			<key>hash2</key>
			<data>
			wkOUEumaMOZRIE73EpGFHRFOE/E6POi8Xpd+VVevC8U=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKLoginKit.framework/Modules/FBSDKLoginKit.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			54b2zs1bfpk6hiYuhiW6naCu+h0=
			</data>
			<key>hash2</key>
			<data>
			otLvCaFFvvsH2p2rr92zyti2Xrh/LUYkV/dlnLl/VDw=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKLoginKit.framework/Modules/FBSDKLoginKit.swiftmodule/x86_64-apple-ios-simulator.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			9xLvEcb57VLJ95OxV9NpjwodpQM=
			</data>
			<key>hash2</key>
			<data>
			1bezunNhsNtcxBcqIgXu0PggzGasID2rtN8n5ewKUW8=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKLoginKit.framework/Modules/FBSDKLoginKit.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			hUkBOw6N/o3v/hxeaTy/ysIFoCs=
			</data>
			<key>hash2</key>
			<data>
			Edh3hLALLHPiPEpSTtcBB+9WoUkRCrs6Iimr+lPa+gM=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKLoginKit.framework/Modules/FBSDKLoginKit.swiftmodule/x86_64-apple-ios-simulator.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			9xLvEcb57VLJ95OxV9NpjwodpQM=
			</data>
			<key>hash2</key>
			<data>
			1bezunNhsNtcxBcqIgXu0PggzGasID2rtN8n5ewKUW8=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKLoginKit.framework/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			KrDKcg24trXz/SqZuZkQjJYkUsc=
			</data>
			<key>hash2</key>
			<data>
			/LNPo6mK3Ap58ptMqxKbx/hlGBOSkSGDoDN3+CL3VlA=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKLoginKit.framework/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash</key>
			<data>
			c/GhFel+DoHL4HradxiX7nIkOl4=
			</data>
			<key>hash2</key>
			<data>
			V+yTPiE3CaHxIWdHy5KWEryxwgIcGAfRgIV8XZH0Qpc=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKLoginKit.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash</key>
			<data>
			1jzMvzaqxqxZgJf81PUrfTmcGCY=
			</data>
			<key>hash2</key>
			<data>
			kFykHYGg2A4A2NlZh+4qH1JOGbze9lz9k/0jXBzMMG4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/dSYMs/FBSDKLoginKit.framework.dSYM/Contents/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			vTorsb8xnOasw4pKU7HkDL0frEo=
			</data>
			<key>hash2</key>
			<data>
			CT5B41zup2nZ/nPDQMhhzccdUxkSEiSlf2wBNdMJUP8=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/dSYMs/FBSDKLoginKit.framework.dSYM/Contents/Resources/DWARF/FBSDKLoginKit</key>
		<dict>
			<key>hash</key>
			<data>
			5YHUvsGdyQCX064Z1tLRdBAjkE8=
			</data>
			<key>hash2</key>
			<data>
			okkz78wfIBmN3azDAQcJ+kINAtatwwWT+QVaoofPAQk=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/dSYMs/FBSDKLoginKit.framework.dSYM/Contents/Resources/Relocations/aarch64/FBSDKLoginKit.yml</key>
		<dict>
			<key>hash</key>
			<data>
			XCK4XgQ3kPAmA4bA8VpW1hdIxys=
			</data>
			<key>hash2</key>
			<data>
			yUXGAw4GXsIdZcOfB29JpQsgBSPl+4craFmQnrRBg6Q=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/dSYMs/FBSDKLoginKit.framework.dSYM/Contents/Resources/Relocations/x86_64/FBSDKLoginKit.yml</key>
		<dict>
			<key>hash</key>
			<data>
			KTcnzwA8YnstkPv1ObpAJuvjquU=
			</data>
			<key>hash2</key>
			<data>
			BF0yuPxjd5b7dGqSCBANbe10RWLmmR/PEE1YNhqqw74=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
