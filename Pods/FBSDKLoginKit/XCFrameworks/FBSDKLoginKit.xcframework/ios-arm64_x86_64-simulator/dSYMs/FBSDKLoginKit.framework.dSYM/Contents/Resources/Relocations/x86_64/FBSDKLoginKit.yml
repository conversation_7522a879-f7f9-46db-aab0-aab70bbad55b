---
triple:          'x86_64-apple-darwin'
binary-path:     '/data/sandcastle/boxes/trunk-git-facebook-ios-sdk/build/Build/Intermediates.noindex/ArchiveIntermediates/FBSDKLoginKit-Dynamic/InstallationBuildProductsLocation/@rpath/FBSDKLoginKit.framework/FBSDKLoginKit'
relocations:
  - { offsetInCU: 0x33, offset: 0x33, size: 0x8, addend: 0x0, symName: _FBSDKLoginKitVersionString, symObjAddr: 0x0, symBinAddr: 0x48BA0, symSize: 0x0 }
  - { offsetInCU: 0x67, offset: 0x67, size: 0x8, addend: 0x0, symName: _FBSDKLoginKitVersionNumber, symObjAddr: 0x40, symBinAddr: 0x48BE0, symSize: 0x0 }
  - { offsetInCU: 0x34, offset: 0xB0, size: 0x8, addend: 0x0, symName: _FBSDKLoginAuthTypeRerequest, symObjAddr: 0x58, symBinAddr: 0x57030, symSize: 0x0 }
  - { offsetInCU: 0xB7, offset: 0x133, size: 0x8, addend: 0x0, symName: _FBSDKLoginAuthTypeReauthorize, symObjAddr: 0x60, symBinAddr: 0x57038, symSize: 0x0 }
  - { offsetInCU: 0x34, offset: 0x184, size: 0x8, addend: 0x0, symName: _FBSDKLoginErrorDomain, symObjAddr: 0x38, symBinAddr: 0x57040, symSize: 0x0 }
  - { offsetInCU: 0x3D, offset: 0x23F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5frame15elementProvider06stringG005loginG019graphRequestFactoryACSo6CGRectV_AA29UserInterfaceElementProviding_pAA0no6StringQ0_pAA05LoginQ0_pSo010FBSDKGraphkL0_ptcfcfA_', symObjAddr: 0xE0, symBinAddr: 0x1E10, symSize: 0x20 }
  - { offsetInCU: 0x56, offset: 0x258, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5frame11permissionsACSo6CGRectV_Say09FBSDKCoreB010PermissionOGtcfcfA_', symObjAddr: 0x100, symBinAddr: 0x1E30, symSize: 0x20 }
  - { offsetInCU: 0x6F, offset: 0x271, size: 0x8, addend: 0x0, symName: ___swift_instantiateConcreteTypeFromMangledName, symObjAddr: 0x130, symBinAddr: 0x1E60, symSize: 0x40 }
  - { offsetInCU: 0x82, offset: 0x284, size: 0x8, addend: 0x0, symName: ___swift_memcpy16_8, symObjAddr: 0x250, symBinAddr: 0x1F80, symSize: 0x10 }
  - { offsetInCU: 0x95, offset: 0x297, size: 0x8, addend: 0x0, symName: ___swift_noop_void_return, symObjAddr: 0x260, symBinAddr: 0x1F90, symSize: 0x10 }
  - { offsetInCU: 0xA8, offset: 0x2AA, size: 0x8, addend: 0x0, symName: '_$sSo7CGPointVwet', symObjAddr: 0x270, symBinAddr: 0x1FA0, symSize: 0x20 }
  - { offsetInCU: 0xBB, offset: 0x2BD, size: 0x8, addend: 0x0, symName: '_$sSo7CGPointVwst', symObjAddr: 0x290, symBinAddr: 0x1FC0, symSize: 0x30 }
  - { offsetInCU: 0xCE, offset: 0x2D0, size: 0x8, addend: 0x0, symName: '_$sSo18FBSDKLoginAuthTypeas20_SwiftNewtypeWrapperSCSYWb', symObjAddr: 0x6C0, symBinAddr: 0x2350, symSize: 0x20 }
  - { offsetInCU: 0xE1, offset: 0x2E3, size: 0x8, addend: 0x0, symName: '_$sSo18FBSDKLoginAuthTypeas20_SwiftNewtypeWrapperSCs35_HasCustomAnyHashableRepresentationPWb', symObjAddr: 0x6E0, symBinAddr: 0x2370, symSize: 0x20 }
  - { offsetInCU: 0xF4, offset: 0x2F6, size: 0x8, addend: 0x0, symName: '_$sSo18FBSDKLoginAuthTypeaSHSCSQWb', symObjAddr: 0x730, symBinAddr: 0x23C0, symSize: 0x20 }
  - { offsetInCU: 0x107, offset: 0x309, size: 0x8, addend: 0x0, symName: '_$sSo26FBSDKAppEventParameterNameas20_SwiftNewtypeWrapperSCSYWb', symObjAddr: 0x8D0, symBinAddr: 0x2560, symSize: 0x20 }
  - { offsetInCU: 0x11A, offset: 0x31C, size: 0x8, addend: 0x0, symName: '_$sSo26FBSDKAppEventParameterNameas20_SwiftNewtypeWrapperSCs35_HasCustomAnyHashableRepresentationPWb', symObjAddr: 0x8F0, symBinAddr: 0x2580, symSize: 0x20 }
  - { offsetInCU: 0x12D, offset: 0x32F, size: 0x8, addend: 0x0, symName: '_$sSo26FBSDKAppEventParameterNameaSHSCSQWb', symObjAddr: 0x910, symBinAddr: 0x25A0, symSize: 0x20 }
  - { offsetInCU: 0x15B, offset: 0x35D, size: 0x8, addend: 0x0, symName: '_$sSo26FBSDKAppEventParameterNameas21_ObjectiveCBridgeableSCsACP016_forceBridgeFromE1C_6resulty01_E5CTypeQz_xSgztFZTW', symObjAddr: 0x360, symBinAddr: 0x2090, symSize: 0x10 }
  - { offsetInCU: 0x176, offset: 0x378, size: 0x8, addend: 0x0, symName: '_$sSo26FBSDKAppEventParameterNameas21_ObjectiveCBridgeableSCsACP024_conditionallyBridgeFromE1C_6resultSb01_E5CTypeQz_xSgztFZTW', symObjAddr: 0x370, symBinAddr: 0x20A0, symSize: 0x10 }
  - { offsetInCU: 0x191, offset: 0x393, size: 0x8, addend: 0x0, symName: '_$sSo18FBSDKLoginAuthTypeas21_ObjectiveCBridgeableSCsACP016_forceBridgeFromD1C_6resulty01_D5CTypeQz_xSgztFZTW', symObjAddr: 0x410, symBinAddr: 0x20E0, symSize: 0x10 }
  - { offsetInCU: 0x1AC, offset: 0x3AE, size: 0x8, addend: 0x0, symName: '_$sSo18FBSDKLoginAuthTypeas21_ObjectiveCBridgeableSCsACP024_conditionallyBridgeFromD1C_6resultSb01_D5CTypeQz_xSgztFZTW', symObjAddr: 0x420, symBinAddr: 0x20F0, symSize: 0x10 }
  - { offsetInCU: 0x1CD, offset: 0x3CF, size: 0x8, addend: 0x0, symName: '_$sSo26FBSDKAppEventParameterNameas35_HasCustomAnyHashableRepresentationSCsACP03_tofgH0s0gH0VSgyFTW', symObjAddr: 0x5E0, symBinAddr: 0x2270, symSize: 0x70 }
  - { offsetInCU: 0x1E8, offset: 0x3EA, size: 0x8, addend: 0x0, symName: '_$sSo18FBSDKLoginAuthTypeas35_HasCustomAnyHashableRepresentationSCsACP03_toefG0s0fG0VSgyFTW', symObjAddr: 0x650, symBinAddr: 0x22E0, symSize: 0x70 }
  - { offsetInCU: 0x23C, offset: 0x43E, size: 0x8, addend: 0x0, symName: '_$sSo14FBSDKAppEventsC13FBSDKLoginKit17LoginEventLoggingA2cDP13flushBehaviorSo0ab5FlushI0VvgTW', symObjAddr: 0x0, symBinAddr: 0x1D30, symSize: 0x20 }
  - { offsetInCU: 0x28D, offset: 0x48F, size: 0x8, addend: 0x0, symName: '_$sSo14FBSDKAppEventsC13FBSDKLoginKit17LoginEventLoggingA2cDP011logInternalF0_10parameters18isImplicitlyLoggedySo0aF4Namea_SDySo0af9ParameterN0aypGSgSbtFTW', symObjAddr: 0x20, symBinAddr: 0x1D50, symSize: 0xA0 }
  - { offsetInCU: 0x2CF, offset: 0x4D1, size: 0x8, addend: 0x0, symName: '_$sSo14FBSDKAppEventsC13FBSDKLoginKit17LoginEventLoggingA2cDP5flushyyFTW', symObjAddr: 0xC0, symBinAddr: 0x1DF0, symSize: 0x20 }
  - { offsetInCU: 0x35A, offset: 0x55C, size: 0x8, addend: 0x0, symName: '_$sSo18FBSDKLoginAuthTypeaSYSCSY8rawValue03RawE0QzvgTW', symObjAddr: 0x570, symBinAddr: 0x2200, symSize: 0x20 }
  - { offsetInCU: 0x1D7, offset: 0x87D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryCAA0cD8CreatingA2aDP06createD011tokenString5nonce11graphDomain10completionySS_S2SySo019FBSDKAuthenticationD0CSgctFTW', symObjAddr: 0x1970, symBinAddr: 0x4060, symSize: 0x30 }
  - { offsetInCU: 0x414, offset: 0xABA, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataVSgSo13NSURLResponseCSgs5Error_pSgIegggg_So6NSDataCSgAGSo7NSErrorCSgIeyByyy_TR', symObjAddr: 0x1730, symBinAddr: 0x3E20, symSize: 0xF0 }
  - { offsetInCU: 0x42B, offset: 0xAD1, size: 0x8, addend: 0x0, symName: '_$s10Foundation13__DataStorageC15withUnsafeBytes2in5applyxSnySiG_xSWKXEtKlFyt_Tg5153$s13FBSDKLoginKit26AuthenticationTokenFactoryC15verifySignature_6header6claims14certificateKey10completionySS_S3SySbctFySo03SecK3RefaSgcfU_yycfU_ySWXEfU_AA0B0VSays5UInt8VGTf1nncn_n', symObjAddr: 0x19A0, symBinAddr: 0x4090, symSize: 0xC0 }
  - { offsetInCU: 0x4EE, offset: 0xB94, size: 0x8, addend: 0x0, symName: '_$sS2SSysWl', symObjAddr: 0x1B40, symBinAddr: 0x4230, symSize: 0x30 }
  - { offsetInCU: 0x501, offset: 0xBA7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC06createD011tokenString5nonce11graphDomain10completionySS_S2SySo019FBSDKAuthenticationD0CSgctFySbcfU_TA', symObjAddr: 0x1BB0, symBinAddr: 0x42A0, symSize: 0x40 }
  - { offsetInCU: 0x514, offset: 0xBBA, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV15_RepresentationOWOe', symObjAddr: 0x1C50, symBinAddr: 0x4340, symSize: 0x40 }
  - { offsetInCU: 0x527, offset: 0xBCD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC15verifySignature_6header6claims14certificateKey10completionySS_S3SySbctFySo03SecK3RefaSgcfU_TA', symObjAddr: 0x1C90, symBinAddr: 0x4380, symSize: 0x30 }
  - { offsetInCU: 0x53A, offset: 0xBE0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC16getPublicKeyWith011certificateH010completionySS_ySo03SecH3RefaSgctFySo0l11CertificateM0aSgcfU_TA', symObjAddr: 0x1CE0, symBinAddr: 0x43D0, symSize: 0x20 }
  - { offsetInCU: 0x54D, offset: 0xBF3, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataVSgWOy', symObjAddr: 0x1D00, symBinAddr: 0x43F0, symSize: 0x20 }
  - { offsetInCU: 0x560, offset: 0xC06, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV15_RepresentationOWOy', symObjAddr: 0x1D20, symBinAddr: 0x4410, symSize: 0x40 }
  - { offsetInCU: 0x573, offset: 0xC19, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataVSgWOe', symObjAddr: 0x1D60, symBinAddr: 0x4450, symSize: 0x20 }
  - { offsetInCU: 0x586, offset: 0xC2C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC18getCertificateWith14certificateKey10completionySS_ySo03SecG3RefaSgctFy10Foundation4DataVSg_So13NSURLResponseCSgs5Error_pSgtcfU_TA', symObjAddr: 0x1DC0, symBinAddr: 0x44B0, symSize: 0x30 }
  - { offsetInCU: 0x599, offset: 0xC3F, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x1DF0, symBinAddr: 0x44E0, symSize: 0x20 }
  - { offsetInCU: 0x5AC, offset: 0xC52, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x1E10, symBinAddr: 0x4500, symSize: 0x10 }
  - { offsetInCU: 0x5BF, offset: 0xC65, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryCMa', symObjAddr: 0x1E20, symBinAddr: 0x4510, symSize: 0x20 }
  - { offsetInCU: 0x89D, offset: 0xF43, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC15verifySignature_6header6claims14certificateKey10completionySS_S3SySbctFySo03SecK3RefaSgcfU_yycfU_TA', symObjAddr: 0x2410, symBinAddr: 0x4AC0, symSize: 0x40 }
  - { offsetInCU: 0x8B0, offset: 0xF56, size: 0x8, addend: 0x0, symName: '_$sSS_yptWOc', symObjAddr: 0x2450, symBinAddr: 0x4B00, symSize: 0x40 }
  - { offsetInCU: 0x8C3, offset: 0xF69, size: 0x8, addend: 0x0, symName: '_$sypWOb', symObjAddr: 0x2490, symBinAddr: 0x4B40, symSize: 0x20 }
  - { offsetInCU: 0xA92, offset: 0x1138, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_ypTgm5Tf4g_n', symObjAddr: 0x1A60, symBinAddr: 0x4150, symSize: 0xE0 }
  - { offsetInCU: 0xBC9, offset: 0x126F, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_SbTgm5Tf4g_n', symObjAddr: 0x1E70, symBinAddr: 0x4560, symSize: 0xF0 }
  - { offsetInCU: 0xD13, offset: 0x13B9, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_SSSgTgm5Tf4g_n', symObjAddr: 0x1F60, symBinAddr: 0x4650, symSize: 0x110 }
  - { offsetInCU: 0xE51, offset: 0x14F7, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_SSTgm5Tf4g_n', symObjAddr: 0x2070, symBinAddr: 0x4760, symSize: 0x110 }
  - { offsetInCU: 0xFA1, offset: 0x1647, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_SdTgm5Tf4g_n', symObjAddr: 0x2180, symBinAddr: 0x4870, symSize: 0xF0 }
  - { offsetInCU: 0x10EB, offset: 0x1791, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_So8NSNumberCTgm5Tf4g_n', symObjAddr: 0x2270, symBinAddr: 0x4960, symSize: 0xF0 }
  - { offsetInCU: 0x1540, offset: 0x1BE6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC16beginCertificateSSvg', symObjAddr: 0x0, symBinAddr: 0x26F0, symSize: 0x30 }
  - { offsetInCU: 0x1553, offset: 0x1BF9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC14endCertificateSSvg', symObjAddr: 0x30, symBinAddr: 0x2720, symSize: 0x30 }
  - { offsetInCU: 0x1566, offset: 0x1C0C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC15sessionProviderSo24FBSDKURLSessionProviding_pvg', symObjAddr: 0x60, symBinAddr: 0x2750, symSize: 0x30 }
  - { offsetInCU: 0x157F, offset: 0x1C25, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC15sessionProviderSo24FBSDKURLSessionProviding_pvs', symObjAddr: 0x90, symBinAddr: 0x2780, symSize: 0x40 }
  - { offsetInCU: 0x1592, offset: 0x1C38, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC15sessionProviderSo24FBSDKURLSessionProviding_pvM', symObjAddr: 0xD0, symBinAddr: 0x27C0, symSize: 0x30 }
  - { offsetInCU: 0x15A5, offset: 0x1C4B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC15sessionProviderSo24FBSDKURLSessionProviding_pvM.resume.0', symObjAddr: 0x100, symBinAddr: 0x27F0, symSize: 0x10 }
  - { offsetInCU: 0x15B8, offset: 0x1C5E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC19certificateEndpoint10Foundation3URLVvg', symObjAddr: 0x110, symBinAddr: 0x2800, symSize: 0x150 }
  - { offsetInCU: 0x161A, offset: 0x1CC0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC15sessionProviderACSo24FBSDKURLSessionProviding_p_tcfC', symObjAddr: 0x260, symBinAddr: 0x2950, symSize: 0x100 }
  - { offsetInCU: 0x16AA, offset: 0x1D50, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC06createD011tokenString5nonce11graphDomain10completionySS_S2SySo019FBSDKAuthenticationD0CSgctF', symObjAddr: 0x360, symBinAddr: 0x2A50, symSize: 0x390 }
  - { offsetInCU: 0x184F, offset: 0x1EF5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC06createD011tokenString5nonce11graphDomain10completionySS_S2SySo019FBSDKAuthenticationD0CSgctFySbcfU_', symObjAddr: 0x6F0, symBinAddr: 0x2DE0, symSize: 0x120 }
  - { offsetInCU: 0x195A, offset: 0x2000, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC15verifySignature_6header6claims14certificateKey10completionySS_S3SySbctF', symObjAddr: 0x810, symBinAddr: 0x2F00, symSize: 0x290 }
  - { offsetInCU: 0x1A9D, offset: 0x2143, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC15verifySignature_6header6claims14certificateKey10completionySS_S3SySbctFySo03SecK3RefaSgcfU_', symObjAddr: 0xAA0, symBinAddr: 0x3190, symSize: 0x100 }
  - { offsetInCU: 0x1B26, offset: 0x21CC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC15verifySignature_6header6claims14certificateKey10completionySS_S3SySbctFySo03SecK3RefaSgcfU_yycfU_', symObjAddr: 0xBA0, symBinAddr: 0x3290, symSize: 0x310 }
  - { offsetInCU: 0x1E85, offset: 0x252B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC15verifySignature_6header6claims14certificateKey10completionySS_S3SySbctFySo03SecK3RefaSgcfU_yycfU_ySWXEfU_', symObjAddr: 0xEB0, symBinAddr: 0x35A0, symSize: 0xD0 }
  - { offsetInCU: 0x20A3, offset: 0x2749, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC16getPublicKeyWith011certificateH010completionySS_ySo03SecH3RefaSgctF', symObjAddr: 0xF80, symBinAddr: 0x3670, symSize: 0x80 }
  - { offsetInCU: 0x20D1, offset: 0x2777, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC16getPublicKeyWith011certificateH010completionySS_ySo03SecH3RefaSgctFySo0l11CertificateM0aSgcfU_', symObjAddr: 0x1000, symBinAddr: 0x36F0, symSize: 0xB0 }
  - { offsetInCU: 0x2194, offset: 0x283A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC18getCertificateWith14certificateKey10completionySS_ySo03SecG3RefaSgctF', symObjAddr: 0x10B0, symBinAddr: 0x37A0, symSize: 0x1E0 }
  - { offsetInCU: 0x21E9, offset: 0x288F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC18getCertificateWith14certificateKey10completionySS_ySo03SecG3RefaSgctFy10Foundation4DataVSg_So13NSURLResponseCSgs5Error_pSgtcfU_', symObjAddr: 0x1290, symBinAddr: 0x3980, symSize: 0x4A0 }
  - { offsetInCU: 0x22F7, offset: 0x299D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryCfd', symObjAddr: 0x1820, symBinAddr: 0x3F10, symSize: 0x30 }
  - { offsetInCU: 0x2324, offset: 0x29CA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryCfD', symObjAddr: 0x1850, symBinAddr: 0x3F40, symSize: 0x40 }
  - { offsetInCU: 0x2359, offset: 0x29FF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryCACycfC', symObjAddr: 0x1890, symBinAddr: 0x3F80, symSize: 0x30 }
  - { offsetInCU: 0x236C, offset: 0x2A12, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryCACycfc', symObjAddr: 0x18C0, symBinAddr: 0x3FB0, symSize: 0xB0 }
  - { offsetInCU: 0xF6, offset: 0x2D34, size: 0x8, addend: 0x0, symName: '_$sypSgWOh', symObjAddr: 0x770, symBinAddr: 0x5290, symSize: 0x30 }
  - { offsetInCU: 0x109, offset: 0x2D47, size: 0x8, addend: 0x0, symName: '_$sypWOc', symObjAddr: 0x7A0, symBinAddr: 0x52C0, symSize: 0x30 }
  - { offsetInCU: 0x11C, offset: 0x2D5A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit25AuthenticationTokenHeaderCMa', symObjAddr: 0x7D0, symBinAddr: 0x52F0, symSize: 0x20 }
  - { offsetInCU: 0x1DE, offset: 0x2E1C, size: 0x8, addend: 0x0, symName: '_$ss11AnyHashableVWOc', symObjAddr: 0xBF0, symBinAddr: 0x5710, symSize: 0x30 }
  - { offsetInCU: 0x1F1, offset: 0x2E2F, size: 0x8, addend: 0x0, symName: '_$ss11AnyHashableVWOh', symObjAddr: 0xC20, symBinAddr: 0x5740, symSize: 0x21 }
  - { offsetInCU: 0x341, offset: 0x2F7F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit25AuthenticationTokenHeaderC17fromEncodedStringACSgSS_tcfC', symObjAddr: 0x0, symBinAddr: 0x4BA0, symSize: 0x40 }
  - { offsetInCU: 0x354, offset: 0x2F92, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit25AuthenticationTokenHeaderC3kidSSvg', symObjAddr: 0x40, symBinAddr: 0x4BE0, symSize: 0x30 }
  - { offsetInCU: 0x37F, offset: 0x2FBD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit25AuthenticationTokenHeaderC17fromEncodedStringACSgSS_tcfc', symObjAddr: 0x70, symBinAddr: 0x4C10, symSize: 0x5E0 }
  - { offsetInCU: 0x469, offset: 0x30A7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit25AuthenticationTokenHeaderCfd', symObjAddr: 0x6D0, symBinAddr: 0x51F0, symSize: 0x20 }
  - { offsetInCU: 0x4A1, offset: 0x30DF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit25AuthenticationTokenHeaderCfD', symObjAddr: 0x6F0, symBinAddr: 0x5210, symSize: 0x20 }
  - { offsetInCU: 0x4EA, offset: 0x3128, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4findys10_HashTableV6BucketV6bucket_Sb5foundtxSHRzlFSS_Tg5', symObjAddr: 0x710, symBinAddr: 0x5230, symSize: 0x60 }
  - { offsetInCU: 0x525, offset: 0x3163, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4findys10_HashTableV6BucketV6bucket_Sb5foundtxSHRzlFs11AnyHashableV_Tg5', symObjAddr: 0x820, symBinAddr: 0x5340, symSize: 0x30 }
  - { offsetInCU: 0x552, offset: 0x3190, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4findys10_HashTableV6BucketV6bucket_Sb5foundtxSHRzlFSo26FBSDKAppEventParameterNamea_Tg5', symObjAddr: 0x850, symBinAddr: 0x5370, symSize: 0x80 }
  - { offsetInCU: 0x58D, offset: 0x31CB, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4find_9hashValues10_HashTableV6BucketV6bucket_Sb5foundtx_SitSHRzlFSS_Tg5', symObjAddr: 0x8D0, symBinAddr: 0x53F0, symSize: 0xE0 }
  - { offsetInCU: 0x5EB, offset: 0x3229, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4find_9hashValues10_HashTableV6BucketV6bucket_Sb5foundtx_SitSHRzlFs11AnyHashableV_Tg5', symObjAddr: 0x9B0, symBinAddr: 0x54D0, symSize: 0xC0 }
  - { offsetInCU: 0x612, offset: 0x3250, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4find_9hashValues10_HashTableV6BucketV6bucket_Sb5foundtx_SitSHRzlFSo26FBSDKAppEventParameterNamea_Tg5', symObjAddr: 0xA70, symBinAddr: 0x5590, symSize: 0x180 }
  - { offsetInCU: 0x2B, offset: 0x32EF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12CodeVerifierC5valueSSvgTo', symObjAddr: 0x0, symBinAddr: 0x5770, symSize: 0x50 }
  - { offsetInCU: 0x61, offset: 0x3325, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12CodeVerifierC5valueSSvgTo', symObjAddr: 0x0, symBinAddr: 0x5770, symSize: 0x50 }
  - { offsetInCU: 0x99, offset: 0x335D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12CodeVerifierC9challengeSSvgTo', symObjAddr: 0x80, symBinAddr: 0x57F0, symSize: 0x50 }
  - { offsetInCU: 0x14B, offset: 0x340F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12CodeVerifierC6stringACSgSS_tcfcTo', symObjAddr: 0xB20, symBinAddr: 0x6260, symSize: 0x30 }
  - { offsetInCU: 0x190, offset: 0x3454, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12CodeVerifierCACycfcTo', symObjAddr: 0xE40, symBinAddr: 0x6580, symSize: 0x20 }
  - { offsetInCU: 0x41E, offset: 0x36E2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12CodeVerifierCfETo', symObjAddr: 0xE90, symBinAddr: 0x65D0, symSize: 0x20 }
  - { offsetInCU: 0x44B, offset: 0x370F, size: 0x8, addend: 0x0, symName: '_$s10Foundation13__DataStorageC15withUnsafeBytes2in5applyxSnySiG_xSWKXEtKlFyt_Tg554$s13FBSDKLoginKit12CodeVerifierC9challengeSSvgySWXEfU_AA0B0VSays5UInt8VGTf1nncn_n', symObjAddr: 0xEB0, symBinAddr: 0x65F0, symSize: 0xC0 }
  - { offsetInCU: 0x473, offset: 0x3737, size: 0x8, addend: 0x0, symName: '_$sSays5UInt8VGSayxG10Foundation15ContiguousBytesAeBRszlWl', symObjAddr: 0x1070, symBinAddr: 0x66B0, symSize: 0x40 }
  - { offsetInCU: 0x486, offset: 0x374A, size: 0x8, addend: 0x0, symName: '_$sSays5UInt8VGMa', symObjAddr: 0x10B0, symBinAddr: 0x66F0, symSize: 0x30 }
  - { offsetInCU: 0x499, offset: 0x375D, size: 0x8, addend: 0x0, symName: ___swift_project_boxed_opaque_existential_1, symObjAddr: 0x10E0, symBinAddr: 0x6720, symSize: 0x30 }
  - { offsetInCU: 0x772, offset: 0x3A36, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV06InlineB0VyAESWcfCTf4nd_n', symObjAddr: 0x14D0, symBinAddr: 0x6B10, symSize: 0xC0 }
  - { offsetInCU: 0x7E2, offset: 0x3AA6, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV10LargeSliceVyAESWcfCTf4nd_n', symObjAddr: 0x1590, symBinAddr: 0x6BD0, symSize: 0x80 }
  - { offsetInCU: 0x80D, offset: 0x3AD1, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV11InlineSliceVyAESWcfCTf4nd_n', symObjAddr: 0x1610, symBinAddr: 0x6C50, symSize: 0x80 }
  - { offsetInCU: 0x89A, offset: 0x3B5E, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV15_RepresentationOyAESWcfCTf4nd_n', symObjAddr: 0x1690, symBinAddr: 0x6CD0, symSize: 0x70 }
  - { offsetInCU: 0x8EB, offset: 0x3BAF, size: 0x8, addend: 0x0, symName: ___swift_destroy_boxed_opaque_existential_1, symObjAddr: 0x1700, symBinAddr: 0x6D40, symSize: 0x30 }
  - { offsetInCU: 0x8FE, offset: 0x3BC2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12CodeVerifierCMa', symObjAddr: 0x1730, symBinAddr: 0x6D70, symSize: 0x20 }
  - { offsetInCU: 0xB63, offset: 0x3E27, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12CodeVerifierC5valueSSvg', symObjAddr: 0x50, symBinAddr: 0x57C0, symSize: 0x30 }
  - { offsetInCU: 0xBC0, offset: 0x3E84, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12CodeVerifierC9challengeSSvg', symObjAddr: 0xD0, symBinAddr: 0x5840, symSize: 0x4F0 }
  - { offsetInCU: 0xED6, offset: 0x419A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12CodeVerifierC9challengeSSvgySWXEfU_', symObjAddr: 0x5C0, symBinAddr: 0x5D30, symSize: 0xD0 }
  - { offsetInCU: 0x1156, offset: 0x441A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12CodeVerifierC6stringACSgSS_tcfC', symObjAddr: 0x6C0, symBinAddr: 0x5E00, symSize: 0x230 }
  - { offsetInCU: 0x120F, offset: 0x44D3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12CodeVerifierC6stringACSgSS_tcfc', symObjAddr: 0x8F0, symBinAddr: 0x6030, symSize: 0x230 }
  - { offsetInCU: 0x12BC, offset: 0x4580, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12CodeVerifierCACycfC', symObjAddr: 0xB50, symBinAddr: 0x6290, symSize: 0x20 }
  - { offsetInCU: 0x12F3, offset: 0x45B7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12CodeVerifierCACycfc', symObjAddr: 0xB70, symBinAddr: 0x62B0, symSize: 0x2D0 }
  - { offsetInCU: 0x1432, offset: 0x46F6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12CodeVerifierCfD', symObjAddr: 0xE60, symBinAddr: 0x65A0, symSize: 0x30 }
  - { offsetInCU: 0x1477, offset: 0x473B, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtFs5UInt8V_Tg5', symObjAddr: 0x1110, symBinAddr: 0x6750, symSize: 0xA0 }
  - { offsetInCU: 0x1562, offset: 0x4826, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtF13FBSDKLoginKit18DeviceLoginManagerC_Tg5', symObjAddr: 0x11B0, symBinAddr: 0x67F0, symSize: 0x170 }
  - { offsetInCU: 0x16A7, offset: 0x496B, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtFSS_Tg5', symObjAddr: 0x1320, symBinAddr: 0x6960, symSize: 0xB0 }
  - { offsetInCU: 0x177A, offset: 0x4A3E, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV13_copyContents8subRange12initializingSpyxGSnySiG_AFtF13FBSDKLoginKit18DeviceLoginManagerC_Tg5', symObjAddr: 0x13D0, symBinAddr: 0x6A10, symSize: 0x100 }
  - { offsetInCU: 0x27, offset: 0x4B79, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15DefaultAudienceO8rawValueACSgSu_tcfC', symObjAddr: 0x0, symBinAddr: 0x6DB0, symSize: 0x20 }
  - { offsetInCU: 0x73, offset: 0x4BC5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15DefaultAudienceOSYAASY8rawValuexSg03RawF0Qz_tcfCTW', symObjAddr: 0x120, symBinAddr: 0x6ED0, symSize: 0x20 }
  - { offsetInCU: 0xA2, offset: 0x4BF4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15DefaultAudienceOSYAASY8rawValue03RawF0QzvgTW', symObjAddr: 0x140, symBinAddr: 0x6EF0, symSize: 0x10 }
  - { offsetInCU: 0xC8, offset: 0x4C1A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15DefaultAudienceOSHAASQWb', symObjAddr: 0x40, symBinAddr: 0x6DF0, symSize: 0x10 }
  - { offsetInCU: 0xDB, offset: 0x4C2D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15DefaultAudienceOACSQAAWl', symObjAddr: 0x50, symBinAddr: 0x6E00, symSize: 0x30 }
  - { offsetInCU: 0x10C, offset: 0x4C5E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15DefaultAudienceOMa', symObjAddr: 0x150, symBinAddr: 0x6F00, symSize: 0xA }
  - { offsetInCU: 0x14D, offset: 0x4C9F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15DefaultAudienceOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x30, symBinAddr: 0x6DE0, symSize: 0x10 }
  - { offsetInCU: 0x1F5, offset: 0x4D47, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15DefaultAudienceOSHAASH9hashValueSivgTW', symObjAddr: 0x80, symBinAddr: 0x6E30, symSize: 0x40 }
  - { offsetInCU: 0x29C, offset: 0x4DEE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15DefaultAudienceOSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0xC0, symBinAddr: 0x6E70, symSize: 0x20 }
  - { offsetInCU: 0x2EB, offset: 0x4E3D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15DefaultAudienceOSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0xE0, symBinAddr: 0x6E90, symSize: 0x40 }
  - { offsetInCU: 0x3F0, offset: 0x4F42, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15DefaultAudienceO8rawValueACSgSu_tcfC', symObjAddr: 0x0, symBinAddr: 0x6DB0, symSize: 0x20 }
  - { offsetInCU: 0x40B, offset: 0x4F5D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15DefaultAudienceO8rawValueSuvg', symObjAddr: 0x20, symBinAddr: 0x6DD0, symSize: 0x10 }
  - { offsetInCU: 0xE4, offset: 0x5098, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerCAA17DependentAsObjectA2aDP15setDependenciesyy0hJ0QzFTW', symObjAddr: 0x3D0, symBinAddr: 0x72E0, symSize: 0x60 }
  - { offsetInCU: 0x1BD, offset: 0x5171, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCAA17DependentAsObjectA2aDP15setDependenciesyy0gI0QzFTW', symObjAddr: 0x430, symBinAddr: 0x7340, symSize: 0x70 }
  - { offsetInCU: 0x23D, offset: 0x51F1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesVWOc', symObjAddr: 0x4A0, symBinAddr: 0x73B0, symSize: 0x30 }
  - { offsetInCU: 0x250, offset: 0x5204, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesVWOc', symObjAddr: 0x510, symBinAddr: 0x73E0, symSize: 0x30 }
  - { offsetInCU: 0x353, offset: 0x5307, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17DependentAsObjectPAAE15setDependenciesyy0eG0QzF', symObjAddr: 0x0, symBinAddr: 0x6F10, symSize: 0xC0 }
  - { offsetInCU: 0x392, offset: 0x5346, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17DependentAsObjectPAAE15getDependencies0eG0QzyKF', symObjAddr: 0xC0, symBinAddr: 0x6FD0, symSize: 0x1D0 }
  - { offsetInCU: 0x3C2, offset: 0x5376, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17DependentAsObjectPAAE13dynamicMemberqd__Sgs7KeyPathCy0E12DependenciesQzqd__G_tcluig', symObjAddr: 0x290, symBinAddr: 0x71A0, symSize: 0xF0 }
  - { offsetInCU: 0xE7, offset: 0x55AB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerCAA15DependentAsTypeA2aDP15setDependenciesyy0hJ0QzFZTW', symObjAddr: 0x3D0, symBinAddr: 0x7810, symSize: 0x90 }
  - { offsetInCU: 0x19A, offset: 0x565E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterCAA15DependentAsTypeA2aDP15setDependenciesyy0hJ0QzFZTW', symObjAddr: 0x460, symBinAddr: 0x78A0, symSize: 0x90 }
  - { offsetInCU: 0x24D, offset: 0x5711, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterVAA15DependentAsTypeA2aDP15setDependenciesyy0gI0QzFZTW', symObjAddr: 0x4F0, symBinAddr: 0x7930, symSize: 0xA0 }
  - { offsetInCU: 0x2A5, offset: 0x5769, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesVWOc', symObjAddr: 0x590, symBinAddr: 0x79D0, symSize: 0x30 }
  - { offsetInCU: 0x2B8, offset: 0x577C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesVWOc', symObjAddr: 0x600, symBinAddr: 0x7A00, symSize: 0x30 }
  - { offsetInCU: 0x2CB, offset: 0x578F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC16TypeDependenciesVWOc', symObjAddr: 0x630, symBinAddr: 0x7A30, symSize: 0x30 }
  - { offsetInCU: 0x3AD, offset: 0x5871, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15DependentAsTypePAAE15setDependenciesyy0eG0QzFZ', symObjAddr: 0x0, symBinAddr: 0x7440, symSize: 0xC0 }
  - { offsetInCU: 0x3EC, offset: 0x58B0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15DependentAsTypePAAE15getDependencies0eG0QzyKFZ', symObjAddr: 0xC0, symBinAddr: 0x7500, symSize: 0x1D0 }
  - { offsetInCU: 0x41C, offset: 0x58E0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15DependentAsTypePAAE13dynamicMemberqd__Sgs7KeyPathCy0E12DependenciesQzqd__G_tcluigZ', symObjAddr: 0x290, symBinAddr: 0x76D0, symSize: 0xF0 }
  - { offsetInCU: 0x8B, offset: 0x5A7E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoC15verificationURL10Foundation0H0VvgTo', symObjAddr: 0xE0, symBinAddr: 0x7BA0, symSize: 0x30 }
  - { offsetInCU: 0xD8, offset: 0x5ACB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoC14expirationDate10Foundation0H0VvgTo', symObjAddr: 0x130, symBinAddr: 0x7BF0, symSize: 0x30 }
  - { offsetInCU: 0x125, offset: 0x5B18, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoC15pollingIntervalSuvgTo', symObjAddr: 0x1B0, symBinAddr: 0x7C70, symSize: 0x20 }
  - { offsetInCU: 0x1B9, offset: 0x5BAC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoC10identifier05loginE015verificationURL14expirationDate15pollingIntervalACSS_SS10Foundation0J0VAI0L0VSutcfcTo', symObjAddr: 0x410, symBinAddr: 0x7ED0, symSize: 0x180 }
  - { offsetInCU: 0x24D, offset: 0x5C40, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoCACycfcTo', symObjAddr: 0x5E0, symBinAddr: 0x80A0, symSize: 0x30 }
  - { offsetInCU: 0x2D9, offset: 0x5CCC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoCfETo', symObjAddr: 0x640, symBinAddr: 0x8100, symSize: 0x70 }
  - { offsetInCU: 0x306, offset: 0x5CF9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoCMU', symObjAddr: 0x6B0, symBinAddr: 0x8170, symSize: 0x10 }
  - { offsetInCU: 0x319, offset: 0x5D0C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoCMa', symObjAddr: 0x6C0, symBinAddr: 0x8180, symSize: 0x30 }
  - { offsetInCU: 0x32C, offset: 0x5D1F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoCMr', symObjAddr: 0x6F0, symBinAddr: 0x81B0, symSize: 0xA0 }
  - { offsetInCU: 0x416, offset: 0x5E09, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoC10identifierSSvg', symObjAddr: 0x20, symBinAddr: 0x7AE0, symSize: 0x30 }
  - { offsetInCU: 0x437, offset: 0x5E2A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoC05loginE0SSvg', symObjAddr: 0xB0, symBinAddr: 0x7B70, symSize: 0x30 }
  - { offsetInCU: 0x493, offset: 0x5E86, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoC15pollingIntervalSuvg', symObjAddr: 0x1D0, symBinAddr: 0x7C90, symSize: 0x20 }
  - { offsetInCU: 0x511, offset: 0x5F04, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoC10identifier05loginE015verificationURL14expirationDate15pollingIntervalACSS_SS10Foundation0J0VAI0L0VSutcfC', symObjAddr: 0x1F0, symBinAddr: 0x7CB0, symSize: 0x110 }
  - { offsetInCU: 0x57B, offset: 0x5F6E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoC10identifier05loginE015verificationURL14expirationDate15pollingIntervalACSS_SS10Foundation0J0VAI0L0VSutcfc', symObjAddr: 0x300, symBinAddr: 0x7DC0, symSize: 0x110 }
  - { offsetInCU: 0x5D8, offset: 0x5FCB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoCACycfC', symObjAddr: 0x590, symBinAddr: 0x8050, symSize: 0x20 }
  - { offsetInCU: 0x5F1, offset: 0x5FE4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoCACycfc', symObjAddr: 0x5B0, symBinAddr: 0x8070, symSize: 0x30 }
  - { offsetInCU: 0x644, offset: 0x6037, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoCfD', symObjAddr: 0x610, symBinAddr: 0x80D0, symSize: 0x30 }
  - { offsetInCU: 0x17E, offset: 0x61D6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV10Foundation13CustomNSErrorAadEP11errorDomainSSvgZTW', symObjAddr: 0x240, symBinAddr: 0x84C0, symSize: 0x50 }
  - { offsetInCU: 0x1B3, offset: 0x620B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV10Foundation13CustomNSErrorAadEP9errorCodeSivgTW', symObjAddr: 0x290, symBinAddr: 0x8510, symSize: 0x10 }
  - { offsetInCU: 0x1E3, offset: 0x623B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV10Foundation13CustomNSErrorAadEP13errorUserInfoSDySSypGvgTW', symObjAddr: 0x2A0, symBinAddr: 0x8520, symSize: 0x10 }
  - { offsetInCU: 0x213, offset: 0x626B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVSHAASH9hashValueSivgTW', symObjAddr: 0x2B0, symBinAddr: 0x8530, symSize: 0x40 }
  - { offsetInCU: 0x2EE, offset: 0x6346, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0x2F0, symBinAddr: 0x8570, symSize: 0x20 }
  - { offsetInCU: 0x35C, offset: 0x63B4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x3B0, symBinAddr: 0x8630, symSize: 0x30 }
  - { offsetInCU: 0x40D, offset: 0x6465, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceLoginErrorCodeOSYAASY8rawValuexSg03RawH0Qz_tcfCTW', symObjAddr: 0x490, symBinAddr: 0x8710, symSize: 0x30 }
  - { offsetInCU: 0x43C, offset: 0x6494, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceLoginErrorCodeOSYAASY8rawValue03RawH0QzvgTW', symObjAddr: 0x4C0, symBinAddr: 0x8740, symSize: 0x10 }
  - { offsetInCU: 0x458, offset: 0x64B0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceLoginErrorCodeO8rawValueACSgSi_tcfCTf4nd_n', symObjAddr: 0x4E0, symBinAddr: 0x8760, symSize: 0x30 }
  - { offsetInCU: 0x4B9, offset: 0x6511, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV10Foundation13CustomNSErrorAAs0E0PWb', symObjAddr: 0x510, symBinAddr: 0x8790, symSize: 0x10 }
  - { offsetInCU: 0x4CC, offset: 0x6524, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVACs0E0AAWl', symObjAddr: 0x520, symBinAddr: 0x87A0, symSize: 0x30 }
  - { offsetInCU: 0x4DF, offset: 0x6537, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVSHAASQWb', symObjAddr: 0x550, symBinAddr: 0x87D0, symSize: 0x10 }
  - { offsetInCU: 0x4F2, offset: 0x654A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVACSQAAWl', symObjAddr: 0x560, symBinAddr: 0x87E0, symSize: 0x30 }
  - { offsetInCU: 0x505, offset: 0x655D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceLoginErrorCodeOSHAASQWb', symObjAddr: 0x590, symBinAddr: 0x8810, symSize: 0x10 }
  - { offsetInCU: 0x518, offset: 0x6570, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceLoginErrorCodeOACSQAAWl', symObjAddr: 0x5A0, symBinAddr: 0x8820, symSize: 0x30 }
  - { offsetInCU: 0x52B, offset: 0x6583, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVwxx', symObjAddr: 0x5E0, symBinAddr: 0x8860, symSize: 0x30 }
  - { offsetInCU: 0x53E, offset: 0x6596, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVwca', symObjAddr: 0x660, symBinAddr: 0x88E0, symSize: 0x60 }
  - { offsetInCU: 0x551, offset: 0x65A9, size: 0x8, addend: 0x0, symName: ___swift_memcpy24_8, symObjAddr: 0x6C0, symBinAddr: 0x8940, symSize: 0x20 }
  - { offsetInCU: 0x564, offset: 0x65BC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVwta', symObjAddr: 0x6E0, symBinAddr: 0x8960, symSize: 0x40 }
  - { offsetInCU: 0x577, offset: 0x65CF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVwet', symObjAddr: 0x720, symBinAddr: 0x89A0, symSize: 0x40 }
  - { offsetInCU: 0x58A, offset: 0x65E2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVwst', symObjAddr: 0x760, symBinAddr: 0x89E0, symSize: 0x40 }
  - { offsetInCU: 0x59D, offset: 0x65F5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVMa', symObjAddr: 0x7A0, symBinAddr: 0x8A20, symSize: 0x10 }
  - { offsetInCU: 0x5B0, offset: 0x6608, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceLoginErrorCodeOMa', symObjAddr: 0x7B0, symBinAddr: 0x8A30, symSize: 0x10 }
  - { offsetInCU: 0x5C3, offset: 0x661B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVAC10Foundation13CustomNSErrorAAWl', symObjAddr: 0x7C0, symBinAddr: 0x8A40, symSize: 0x2E }
  - { offsetInCU: 0x657, offset: 0x66AF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0x310, symBinAddr: 0x8590, symSize: 0x40 }
  - { offsetInCU: 0x6EE, offset: 0x6746, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVs0E0AAsADP9_userInfoyXlSgvgTW', symObjAddr: 0x390, symBinAddr: 0x8610, symSize: 0x10 }
  - { offsetInCU: 0x709, offset: 0x6761, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVs0E0AAsADP19_getEmbeddedNSErroryXlSgyFTW', symObjAddr: 0x3A0, symBinAddr: 0x8620, symSize: 0x10 }
  - { offsetInCU: 0x789, offset: 0x67E1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceLoginErrorCodeOSHAASH9hashValueSivgTW', symObjAddr: 0x3F0, symBinAddr: 0x8670, symSize: 0x40 }
  - { offsetInCU: 0x830, offset: 0x6888, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceLoginErrorCodeOSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0x430, symBinAddr: 0x86B0, symSize: 0x20 }
  - { offsetInCU: 0x87F, offset: 0x68D7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceLoginErrorCodeOSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0x450, symBinAddr: 0x86D0, symSize: 0x40 }
  - { offsetInCU: 0x904, offset: 0x695C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceLoginErrorCodeOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x4D0, symBinAddr: 0x8750, symSize: 0x10 }
  - { offsetInCU: 0x997, offset: 0x69EF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVs0E0AAsADP7_domainSSvgTW', symObjAddr: 0x350, symBinAddr: 0x85D0, symSize: 0x20 }
  - { offsetInCU: 0x9B3, offset: 0x6A0B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVs0E0AAsADP5_codeSivgTW', symObjAddr: 0x370, symBinAddr: 0x85F0, symSize: 0x20 }
  - { offsetInCU: 0xA3B, offset: 0x6A93, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV03_nsE0So7NSErrorCSgvg', symObjAddr: 0x0, symBinAddr: 0x8280, symSize: 0x20 }
  - { offsetInCU: 0xA4E, offset: 0x6AA6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV9errorCodeSivg', symObjAddr: 0x20, symBinAddr: 0x82A0, symSize: 0x10 }
  - { offsetInCU: 0xA61, offset: 0x6AB9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV13errorUserInfoSDySSypGvg', symObjAddr: 0x30, symBinAddr: 0x82B0, symSize: 0x10 }
  - { offsetInCU: 0xA7F, offset: 0x6AD7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV03_nsE0ACSo7NSErrorC_tcfC', symObjAddr: 0x40, symBinAddr: 0x82C0, symSize: 0xB0 }
  - { offsetInCU: 0xAA2, offset: 0x6AFA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV_8userInfoAcA0cdE4CodeO_SDySSypGtcfC', symObjAddr: 0xF0, symBinAddr: 0x8370, symSize: 0x20 }
  - { offsetInCU: 0xAD2, offset: 0x6B2A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceLoginErrorCodeO8rawValueSivg', symObjAddr: 0x110, symBinAddr: 0x8390, symSize: 0x10 }
  - { offsetInCU: 0xAF7, offset: 0x6B4F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV11errorDomainSSvgZ', symObjAddr: 0x120, symBinAddr: 0x83A0, symSize: 0x50 }
  - { offsetInCU: 0xB1E, offset: 0x6B76, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV16excessivePollingAA0cdE4CodeOvgZ', symObjAddr: 0x170, symBinAddr: 0x83F0, symSize: 0x10 }
  - { offsetInCU: 0xB3F, offset: 0x6B97, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV21authorizationDeclinedAA0cdE4CodeOvgZ', symObjAddr: 0x180, symBinAddr: 0x8400, symSize: 0x10 }
  - { offsetInCU: 0xB60, offset: 0x6BB8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV20authorizationPendingAA0cdE4CodeOvgZ', symObjAddr: 0x190, symBinAddr: 0x8410, symSize: 0x10 }
  - { offsetInCU: 0xB81, offset: 0x6BD9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV11codeExpiredAA0cdE4CodeOvgZ', symObjAddr: 0x1A0, symBinAddr: 0x8420, symSize: 0x10 }
  - { offsetInCU: 0xBA2, offset: 0x6BFA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV2eeoiySbAC_ACtFZ', symObjAddr: 0x1B0, symBinAddr: 0x8430, symSize: 0x30 }
  - { offsetInCU: 0xC11, offset: 0x6C69, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV4hash4intoys6HasherVz_tF', symObjAddr: 0x1E0, symBinAddr: 0x8460, symSize: 0x20 }
  - { offsetInCU: 0xC83, offset: 0x6CDB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV9hashValueSivg', symObjAddr: 0x200, symBinAddr: 0x8480, symSize: 0x40 }
  - { offsetInCU: 0xDBC, offset: 0x6E14, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceLoginErrorCodeO8rawValueACSgSi_tcfC', symObjAddr: 0x3E0, symBinAddr: 0x8660, symSize: 0x10 }
  - { offsetInCU: 0x4D, offset: 0x6EA6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC05loginE9Instances33_2E1868FF91A815585B124C0140A60DCBLLSayACGvpZ', symObjAddr: 0x7AB0, symBinAddr: 0x5FA98, symSize: 0x0 }
  - { offsetInCU: 0x7B, offset: 0x6ED4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC8delegateAA0cdE8Delegate_pSgvgTo', symObjAddr: 0x20, symBinAddr: 0x8A90, symSize: 0x40 }
  - { offsetInCU: 0xCF, offset: 0x6F28, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC8delegateAA0cdE8Delegate_pSgvsTo', symObjAddr: 0xA0, symBinAddr: 0x8B10, symSize: 0x40 }
  - { offsetInCU: 0x14F, offset: 0x6FA8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC11permissionsSaySSGvgTo', symObjAddr: 0x200, symBinAddr: 0x8C70, symSize: 0x40 }
  - { offsetInCU: 0x19C, offset: 0x6FF5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC11redirectURL10Foundation0G0VSgvgTo', symObjAddr: 0x260, symBinAddr: 0x8CD0, symSize: 0xB0 }
  - { offsetInCU: 0x1F0, offset: 0x7049, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC11redirectURL10Foundation0G0VSgvsTo', symObjAddr: 0x390, symBinAddr: 0x8DC0, symSize: 0xE0 }
  - { offsetInCU: 0x26A, offset: 0x70C3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC8codeInfoAA0cd4CodeG0CSgvgTo', symObjAddr: 0x520, symBinAddr: 0x8F50, symSize: 0x40 }
  - { offsetInCU: 0x2BC, offset: 0x7115, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC8codeInfoAA0cd4CodeG0CSgvsTo', symObjAddr: 0x5A0, symBinAddr: 0x8FD0, symSize: 0x60 }
  - { offsetInCU: 0x378, offset: 0x71D1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC11permissions011enableSmartD0ACSaySSG_SbtcfcTo', symObjAddr: 0xAD0, symBinAddr: 0x9500, symSize: 0x30 }
  - { offsetInCU: 0x43C, offset: 0x7295, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC5startyyFTo', symObjAddr: 0x2660, symBinAddr: 0xB090, symSize: 0x30 }
  - { offsetInCU: 0x46C, offset: 0x72C5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC6cancelyyFTo', symObjAddr: 0x2850, symBinAddr: 0xB280, symSize: 0x30 }
  - { offsetInCU: 0x4AB, offset: 0x7304, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC14notifyDelegate5token14expirationDate020dataAccessExpirationJ0ySSSg_10Foundation0J0VSgALtFTo', symObjAddr: 0x3CF0, symBinAddr: 0xC720, symSize: 0x150 }
  - { offsetInCU: 0x4C6, offset: 0x731F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC12processErroryys0G0_pFTo', symObjAddr: 0x3E40, symBinAddr: 0xC870, symSize: 0x50 }
  - { offsetInCU: 0x4E1, offset: 0x733A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC12schedulePoll8intervalySu_tFTo', symObjAddr: 0x5130, symBinAddr: 0xDB60, symSize: 0x30 }
  - { offsetInCU: 0x526, offset: 0x737F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerCACycfcTo', symObjAddr: 0x51B0, symBinAddr: 0xDBE0, symSize: 0x30 }
  - { offsetInCU: 0x5B7, offset: 0x7410, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerCAA17DependentAsObjectA2aDP22configuredDependencies0hJ0QzSgvsTW', symObjAddr: 0x54F0, symBinAddr: 0xDF20, symSize: 0x60 }
  - { offsetInCU: 0x5F9, offset: 0x7452, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerCAA17DependentAsObjectA2aDP22configuredDependencies0hJ0QzSgvMTW', symObjAddr: 0x5550, symBinAddr: 0xDF80, symSize: 0x40 }
  - { offsetInCU: 0x634, offset: 0x748D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC05loginE9Instances33_2E1868FF91A815585B124C0140A60DCBLL_WZ', symObjAddr: 0x0, symBinAddr: 0x8A70, symSize: 0x20 }
  - { offsetInCU: 0x82A, offset: 0x7683, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13DevicePolling_pWOb', symObjAddr: 0x790, symBinAddr: 0x91C0, symSize: 0x20 }
  - { offsetInCU: 0xBD4, offset: 0x7A2D, size: 0x8, addend: 0x0, symName: '_$sSo27FBSDKGraphRequestConnecting_pSgypSgs5Error_pSgIeggng_AByXlSgSo7NSErrorCSgIeyByyy_TR', symObjAddr: 0x25A0, symBinAddr: 0xAFD0, symSize: 0xC0 }
  - { offsetInCU: 0xE23, offset: 0x7C7C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerCfETo', symObjAddr: 0x5210, symBinAddr: 0xDC40, symSize: 0x80 }
  - { offsetInCU: 0xE6D, offset: 0x7CC6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC10netService_13didNotPublishySo05NSNetG0C_SDySSSo8NSNumberCGtFTo', symObjAddr: 0x52A0, symBinAddr: 0xDCD0, symSize: 0x50 }
  - { offsetInCU: 0xEE5, offset: 0x7D3E, size: 0x8, addend: 0x0, symName: '_$sSaySSGMa', symObjAddr: 0x5760, symBinAddr: 0xE190, symSize: 0x30 }
  - { offsetInCU: 0xF21, offset: 0x7D7A, size: 0x8, addend: 0x0, symName: '_$ss13_parseInteger5ascii5radixq_Sgx_SitSyRzs010FixedWidthB0R_r0_lFSS_SiTg5', symObjAddr: 0x5790, symBinAddr: 0xE1C0, symSize: 0x120 }
  - { offsetInCU: 0xF58, offset: 0x7DB1, size: 0x8, addend: 0x0, symName: '_$ss13_parseInteger5ascii5radixq_Sgx_SitSyRzs010FixedWidthB0R_r0_lFADSRys5UInt8VGXEfU_SS_SiTg5', symObjAddr: 0x58B0, symBinAddr: 0xE2E0, symSize: 0x290 }
  - { offsetInCU: 0x10A8, offset: 0x7F01, size: 0x8, addend: 0x0, symName: '_$ss32_copyCollectionToContiguousArrayys0dE0Vy7ElementQzGxSlRzlFSs8UTF8ViewV_Tgq5', symObjAddr: 0x5D40, symBinAddr: 0xE770, symSize: 0xA0 }
  - { offsetInCU: 0x10F6, offset: 0x7F4F, size: 0x8, addend: 0x0, symName: '_$ss22_ContiguousArrayBufferV19_uninitializedCount15minimumCapacityAByxGSi_SitcfCs5UInt8V_Tgmq5', symObjAddr: 0x5EE0, symBinAddr: 0xE910, symSize: 0x70 }
  - { offsetInCU: 0x1258, offset: 0x80B1, size: 0x8, addend: 0x0, symName: '_$sSS11withCStringyxxSPys4Int8VGKXEKlFSb_Tg5024$sSdySdSgxcSyRzlufcSbSpyf6GXEfU_j5SPys4C7VGXEfU_SpySdGTf1cn_n', symObjAddr: 0x6320, symBinAddr: 0xED50, symSize: 0x110 }
  - { offsetInCU: 0x1394, offset: 0x81ED, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC5startyyFySo27FBSDKGraphRequestConnecting_pSg_ypSgs5Error_pSgtcfU_TA', symObjAddr: 0x6470, symBinAddr: 0xEE70, symSize: 0x20 }
  - { offsetInCU: 0x13A7, offset: 0x8200, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x6490, symBinAddr: 0xEE90, symSize: 0x20 }
  - { offsetInCU: 0x13BA, offset: 0x8213, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x64B0, symBinAddr: 0xEEB0, symSize: 0x10 }
  - { offsetInCU: 0x13CD, offset: 0x8226, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesVWOh', symObjAddr: 0x64C0, symBinAddr: 0xEEC0, symSize: 0x20 }
  - { offsetInCU: 0x13E0, offset: 0x8239, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13DevicePolling_pWOc', symObjAddr: 0x6510, symBinAddr: 0xEEE0, symSize: 0x30 }
  - { offsetInCU: 0x13F3, offset: 0x824C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC12schedulePoll8intervalySu_tFyycfU_TA', symObjAddr: 0x65D0, symBinAddr: 0xEF70, symSize: 0x20 }
  - { offsetInCU: 0x1555, offset: 0x83AE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC14notifyDelegate5token14expirationDate020dataAccessExpirationJ0ySSSg_10Foundation0J0VSgALtFyAA0cdE6ResultCcfU_TA', symObjAddr: 0x6F50, symBinAddr: 0xF8F0, symSize: 0x10 }
  - { offsetInCU: 0x157D, offset: 0x83D6, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DateVSgWOb', symObjAddr: 0x7090, symBinAddr: 0xFA30, symSize: 0x40 }
  - { offsetInCU: 0x1590, offset: 0x83E9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC14notifyDelegate5token14expirationDate020dataAccessExpirationJ0ySSSg_10Foundation0J0VSgALtFySo27FBSDKGraphRequestConnecting_pSg_ypSgs5Error_pSgtcfU0_TA', symObjAddr: 0x70D0, symBinAddr: 0xFA70, symSize: 0xA0 }
  - { offsetInCU: 0x15BF, offset: 0x8418, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerCMU', symObjAddr: 0x7320, symBinAddr: 0xFCC0, symSize: 0x10 }
  - { offsetInCU: 0x15D2, offset: 0x842B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerCMa', symObjAddr: 0x7330, symBinAddr: 0xFCD0, symSize: 0x30 }
  - { offsetInCU: 0x15E5, offset: 0x843E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerCMr', symObjAddr: 0x7360, symBinAddr: 0xFD00, symSize: 0xA0 }
  - { offsetInCU: 0x15F8, offset: 0x8451, size: 0x8, addend: 0x0, symName: '_$s10Foundation3URLVSgMa', symObjAddr: 0x7430, symBinAddr: 0xFDD0, symSize: 0x50 }
  - { offsetInCU: 0x160B, offset: 0x8464, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesVwCP', symObjAddr: 0x7480, symBinAddr: 0xFE20, symSize: 0x30 }
  - { offsetInCU: 0x161E, offset: 0x8477, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesVwxx', symObjAddr: 0x74B0, symBinAddr: 0xFE50, symSize: 0x40 }
  - { offsetInCU: 0x1631, offset: 0x848A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesVwcp', symObjAddr: 0x74F0, symBinAddr: 0xFE90, symSize: 0x70 }
  - { offsetInCU: 0x1644, offset: 0x849D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesVwca', symObjAddr: 0x7560, symBinAddr: 0xFF00, symSize: 0x90 }
  - { offsetInCU: 0x1657, offset: 0x84B0, size: 0x8, addend: 0x0, symName: ___swift_assign_boxed_opaque_existential_1, symObjAddr: 0x75F0, symBinAddr: 0xFF90, symSize: 0x130 }
  - { offsetInCU: 0x166A, offset: 0x84C3, size: 0x8, addend: 0x0, symName: ___swift_memcpy72_8, symObjAddr: 0x7720, symBinAddr: 0x100C0, symSize: 0x30 }
  - { offsetInCU: 0x167D, offset: 0x84D6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesVwta', symObjAddr: 0x7750, symBinAddr: 0x100F0, symSize: 0x80 }
  - { offsetInCU: 0x1690, offset: 0x84E9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesVwet', symObjAddr: 0x77D0, symBinAddr: 0x10170, symSize: 0x40 }
  - { offsetInCU: 0x16A3, offset: 0x84FC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesVwst', symObjAddr: 0x7810, symBinAddr: 0x101B0, symSize: 0x50 }
  - { offsetInCU: 0x16B6, offset: 0x850F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesVMa', symObjAddr: 0x7860, symBinAddr: 0x10200, symSize: 0x10 }
  - { offsetInCU: 0x16C9, offset: 0x8522, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26DeviceLoginManagerDelegate_pSgXwWOh', symObjAddr: 0x7870, symBinAddr: 0x10210, symSize: 0x20 }
  - { offsetInCU: 0x16DC, offset: 0x8535, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC12schedulePoll8intervalySu_tFyycfU_ySo27FBSDKGraphRequestConnecting_pSg_ypSgs5Error_pSgtcfU_TA', symObjAddr: 0x7980, symBinAddr: 0x10300, symSize: 0x20 }
  - { offsetInCU: 0x16EF, offset: 0x8548, size: 0x8, addend: 0x0, symName: '_$sSdySdSgxcSyRzlufcSbSpySdGXEfU_SbSPys4Int8VGXEfU_TA', symObjAddr: 0x79D0, symBinAddr: 0x10350, symSize: 0x50 }
  - { offsetInCU: 0x19CF, offset: 0x8828, size: 0x8, addend: 0x0, symName: '_$ss17FixedWidthIntegerPsEyxSgSScfCSi_Tgm5', symObjAddr: 0x4DA0, symBinAddr: 0xD7D0, symSize: 0x390 }
  - { offsetInCU: 0x20A0, offset: 0x8EF9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC8delegateAA0cdE8Delegate_pSgvg', symObjAddr: 0x60, symBinAddr: 0x8AD0, symSize: 0x40 }
  - { offsetInCU: 0x20E5, offset: 0x8F3E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC8delegateAA0cdE8Delegate_pSgvs', symObjAddr: 0xE0, symBinAddr: 0x8B50, symSize: 0x50 }
  - { offsetInCU: 0x210B, offset: 0x8F64, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC8delegateAA0cdE8Delegate_pSgvM', symObjAddr: 0x130, symBinAddr: 0x8BA0, symSize: 0x70 }
  - { offsetInCU: 0x212C, offset: 0x8F85, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC8delegateAA0cdE8Delegate_pSgvM.resume.0', symObjAddr: 0x1A0, symBinAddr: 0x8C10, symSize: 0x60 }
  - { offsetInCU: 0x215D, offset: 0x8FB6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC11permissionsSaySSGvg', symObjAddr: 0x240, symBinAddr: 0x8CB0, symSize: 0x20 }
  - { offsetInCU: 0x218A, offset: 0x8FE3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC11redirectURL10Foundation0G0VSgvg', symObjAddr: 0x310, symBinAddr: 0x8D80, symSize: 0x40 }
  - { offsetInCU: 0x21C9, offset: 0x9022, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC11redirectURL10Foundation0G0VSgvs', symObjAddr: 0x470, symBinAddr: 0x8EA0, symSize: 0x60 }
  - { offsetInCU: 0x21EF, offset: 0x9048, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC11redirectURL10Foundation0G0VSgvM', symObjAddr: 0x4D0, symBinAddr: 0x8F00, symSize: 0x40 }
  - { offsetInCU: 0x2212, offset: 0x906B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC11redirectURL10Foundation0G0VSgvM.resume.0', symObjAddr: 0x510, symBinAddr: 0x8F40, symSize: 0x10 }
  - { offsetInCU: 0x2243, offset: 0x909C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC8codeInfoAA0cd4CodeG0CSgvg', symObjAddr: 0x560, symBinAddr: 0x8F90, symSize: 0x40 }
  - { offsetInCU: 0x2280, offset: 0x90D9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC8codeInfoAA0cd4CodeG0CSgvs', symObjAddr: 0x600, symBinAddr: 0x9030, symSize: 0x50 }
  - { offsetInCU: 0x22A4, offset: 0x90FD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC8codeInfoAA0cd4CodeG0CSgvM', symObjAddr: 0x650, symBinAddr: 0x9080, symSize: 0x40 }
  - { offsetInCU: 0x22C7, offset: 0x9120, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC22configuredDependenciesAC06ObjectG0VSgvM', symObjAddr: 0x700, symBinAddr: 0x9130, symSize: 0x40 }
  - { offsetInCU: 0x22EA, offset: 0x9143, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV12devicePoller12errorFactory012graphRequestK015internalUtility8settingsAeA0C7Polling_p_So18FBSDKErrorCreating_pSo010FBSDKGraphmK0_pSo013FBSDKInternalO0_p09FBSDKCoreB016SettingsProtocol_ptcfC', symObjAddr: 0x740, symBinAddr: 0x9170, symSize: 0x50 }
  - { offsetInCU: 0x22FD, offset: 0x9156, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC19defaultDependenciesAC06ObjectG0VSgvM', symObjAddr: 0x880, symBinAddr: 0x92B0, symSize: 0x40 }
  - { offsetInCU: 0x2326, offset: 0x917F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC11permissions011enableSmartD0ACSaySSG_SbtcfC', symObjAddr: 0x8C0, symBinAddr: 0x92F0, symSize: 0x30 }
  - { offsetInCU: 0x235D, offset: 0x91B6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC11permissions011enableSmartD0ACSaySSG_Sbtcfc', symObjAddr: 0x8F0, symBinAddr: 0x9320, symSize: 0x1E0 }
  - { offsetInCU: 0x2497, offset: 0x92F0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC5startyyF', symObjAddr: 0xB00, symBinAddr: 0x9530, symSize: 0x6A0 }
  - { offsetInCU: 0x26FA, offset: 0x9553, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC5startyyFySo27FBSDKGraphRequestConnecting_pSg_ypSgs5Error_pSgtcfU_', symObjAddr: 0x11A0, symBinAddr: 0x9BD0, symSize: 0xCA0 }
  - { offsetInCU: 0x2B76, offset: 0x99CF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC12processErroryys0G0_pF', symObjAddr: 0x1E40, symBinAddr: 0xA870, symSize: 0x340 }
  - { offsetInCU: 0x2CDB, offset: 0x9B34, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC14notifyDelegate33_2E1868FF91A815585B124C0140A60DCBLL5errorys5Error_p_tF', symObjAddr: 0x2180, symBinAddr: 0xABB0, symSize: 0x220 }
  - { offsetInCU: 0x2E64, offset: 0x9CBD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC12schedulePoll8intervalySu_tF', symObjAddr: 0x23A0, symBinAddr: 0xADD0, symSize: 0x200 }
  - { offsetInCU: 0x2F22, offset: 0x9D7B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC12schedulePoll8intervalySu_tFyycfU_', symObjAddr: 0x3E90, symBinAddr: 0xC8C0, symSize: 0x3B0 }
  - { offsetInCU: 0x3086, offset: 0x9EDF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC12schedulePoll8intervalySu_tFyycfU_ySo27FBSDKGraphRequestConnecting_pSg_ypSgs5Error_pSgtcfU_', symObjAddr: 0x4240, symBinAddr: 0xCC70, symSize: 0xB60 }
  - { offsetInCU: 0x34AB, offset: 0xA304, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC6cancelyyF', symObjAddr: 0x2690, symBinAddr: 0xB0C0, symSize: 0x1C0 }
  - { offsetInCU: 0x3668, offset: 0xA4C1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC14notifyDelegate5token14expirationDate020dataAccessExpirationJ0ySSSg_10Foundation0J0VSgALtF', symObjAddr: 0x2880, symBinAddr: 0xB2B0, symSize: 0x810 }
  - { offsetInCU: 0x3A8F, offset: 0xA8E8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC14notifyDelegate5token14expirationDate020dataAccessExpirationJ0ySSSg_10Foundation0J0VSgALtFySo27FBSDKGraphRequestConnecting_pSg_ypSgs5Error_pSgtcfU0_', symObjAddr: 0x3090, symBinAddr: 0xBAC0, symSize: 0xC60 }
  - { offsetInCU: 0x3FF0, offset: 0xAE49, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC14notifyDelegate5token14expirationDate020dataAccessExpirationJ0ySSSg_10Foundation0J0VSgALtFyAA0cdE6ResultCcfU_Tf4nnd_n', symObjAddr: 0x68E0, symBinAddr: 0xF280, symSize: 0x150 }
  - { offsetInCU: 0x41E3, offset: 0xB03C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerCACycfC', symObjAddr: 0x5160, symBinAddr: 0xDB90, symSize: 0x20 }
  - { offsetInCU: 0x41F6, offset: 0xB04F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerCACycfc', symObjAddr: 0x5180, symBinAddr: 0xDBB0, symSize: 0x30 }
  - { offsetInCU: 0x4249, offset: 0xB0A2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerCfD', symObjAddr: 0x51E0, symBinAddr: 0xDC10, symSize: 0x30 }
  - { offsetInCU: 0x426A, offset: 0xB0C3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC10netService_13didNotPublishySo05NSNetG0C_SDySSSo8NSNumberCGtF', symObjAddr: 0x5290, symBinAddr: 0xDCC0, symSize: 0x10 }
  - { offsetInCU: 0x4283, offset: 0xB0DC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV12devicePollerAA0C7Polling_pvg', symObjAddr: 0x52F0, symBinAddr: 0xDD20, symSize: 0x10 }
  - { offsetInCU: 0x4296, offset: 0xB0EF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV12devicePollerAA0C7Polling_pvs', symObjAddr: 0x5300, symBinAddr: 0xDD30, symSize: 0x30 }
  - { offsetInCU: 0x42A9, offset: 0xB102, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV12devicePollerAA0C7Polling_pvM', symObjAddr: 0x5330, symBinAddr: 0xDD60, symSize: 0x10 }
  - { offsetInCU: 0x42BC, offset: 0xB115, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV12devicePollerAA0C7Polling_pvM.resume.0', symObjAddr: 0x5340, symBinAddr: 0xDD70, symSize: 0x10 }
  - { offsetInCU: 0x42CF, offset: 0xB128, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV12errorFactorySo18FBSDKErrorCreating_pvg', symObjAddr: 0x5350, symBinAddr: 0xDD80, symSize: 0x10 }
  - { offsetInCU: 0x42E2, offset: 0xB13B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV12errorFactorySo18FBSDKErrorCreating_pvs', symObjAddr: 0x5360, symBinAddr: 0xDD90, symSize: 0x20 }
  - { offsetInCU: 0x42F5, offset: 0xB14E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV12errorFactorySo18FBSDKErrorCreating_pvM', symObjAddr: 0x5380, symBinAddr: 0xDDB0, symSize: 0x20 }
  - { offsetInCU: 0x4308, offset: 0xB161, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV12errorFactorySo18FBSDKErrorCreating_pvM.resume.0', symObjAddr: 0x53A0, symBinAddr: 0xDDD0, symSize: 0x10 }
  - { offsetInCU: 0x431B, offset: 0xB174, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV19graphRequestFactorySo010FBSDKGraphiJ0_pvg', symObjAddr: 0x53B0, symBinAddr: 0xDDE0, symSize: 0x10 }
  - { offsetInCU: 0x432E, offset: 0xB187, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV19graphRequestFactorySo010FBSDKGraphiJ0_pvs', symObjAddr: 0x53C0, symBinAddr: 0xDDF0, symSize: 0x20 }
  - { offsetInCU: 0x4341, offset: 0xB19A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV19graphRequestFactorySo010FBSDKGraphiJ0_pvM', symObjAddr: 0x53E0, symBinAddr: 0xDE10, symSize: 0x20 }
  - { offsetInCU: 0x4354, offset: 0xB1AD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV19graphRequestFactorySo010FBSDKGraphiJ0_pvM.resume.0', symObjAddr: 0x5400, symBinAddr: 0xDE30, symSize: 0x10 }
  - { offsetInCU: 0x4367, offset: 0xB1C0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV15internalUtilitySo013FBSDKInternalI0_pvg', symObjAddr: 0x5410, symBinAddr: 0xDE40, symSize: 0x10 }
  - { offsetInCU: 0x437A, offset: 0xB1D3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV15internalUtilitySo013FBSDKInternalI0_pvs', symObjAddr: 0x5420, symBinAddr: 0xDE50, symSize: 0x20 }
  - { offsetInCU: 0x438D, offset: 0xB1E6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV15internalUtilitySo013FBSDKInternalI0_pvM', symObjAddr: 0x5440, symBinAddr: 0xDE70, symSize: 0x20 }
  - { offsetInCU: 0x43A0, offset: 0xB1F9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV15internalUtilitySo013FBSDKInternalI0_pvM.resume.0', symObjAddr: 0x5460, symBinAddr: 0xDE90, symSize: 0x10 }
  - { offsetInCU: 0x43B3, offset: 0xB20C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV8settings09FBSDKCoreB016SettingsProtocol_pvg', symObjAddr: 0x5470, symBinAddr: 0xDEA0, symSize: 0x10 }
  - { offsetInCU: 0x43C6, offset: 0xB21F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV8settings09FBSDKCoreB016SettingsProtocol_pvs', symObjAddr: 0x5480, symBinAddr: 0xDEB0, symSize: 0x20 }
  - { offsetInCU: 0x43D9, offset: 0xB232, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV8settings09FBSDKCoreB016SettingsProtocol_pvM', symObjAddr: 0x54A0, symBinAddr: 0xDED0, symSize: 0x20 }
  - { offsetInCU: 0x43EC, offset: 0xB245, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV8settings09FBSDKCoreB016SettingsProtocol_pvM.resume.0', symObjAddr: 0x54C0, symBinAddr: 0xDEF0, symSize: 0x10 }
  - { offsetInCU: 0x443F, offset: 0xB298, size: 0x8, addend: 0x0, symName: '_$sSa20_reserveCapacityImpl07minimumB013growForAppendySi_SbtF13FBSDKLoginKit18DeviceLoginManagerC_Tg5', symObjAddr: 0x55F0, symBinAddr: 0xE020, symSize: 0xD0 }
  - { offsetInCU: 0x44EB, offset: 0xB344, size: 0x8, addend: 0x0, symName: '_$sSa034_makeUniqueAndReserveCapacityIfNotB0yyF13FBSDKLoginKit18DeviceLoginManagerC_Tg5', symObjAddr: 0x56C0, symBinAddr: 0xE0F0, symSize: 0xA0 }
  - { offsetInCU: 0x45A2, offset: 0xB3FB, size: 0x8, addend: 0x0, symName: '_$sSS8_copyingyS2SFZ', symObjAddr: 0x5B40, symBinAddr: 0xE570, symSize: 0x80 }
  - { offsetInCU: 0x45B6, offset: 0xB40F, size: 0x8, addend: 0x0, symName: '_$sSlsEy11SubSequenceQzqd__cSXRd__5BoundQyd__5IndexRtzluigSS_s16PartialRangeFromVySSAEVGTgq5', symObjAddr: 0x5BC0, symBinAddr: 0xE5F0, symSize: 0x60 }
  - { offsetInCU: 0x45DD, offset: 0xB436, size: 0x8, addend: 0x0, symName: '_$sSS8_copyingySSSsFZ', symObjAddr: 0x5C20, symBinAddr: 0xE650, symSize: 0x120 }
  - { offsetInCU: 0x461B, offset: 0xB474, size: 0x8, addend: 0x0, symName: '_$sSlsE5countSivgSs8UTF8ViewV_Tgq5', symObjAddr: 0x5DE0, symBinAddr: 0xE810, symSize: 0x100 }
  - { offsetInCU: 0x4640, offset: 0xB499, size: 0x8, addend: 0x0, symName: '_$sSTsE21_copySequenceContents12initializing8IteratorQz_SitSry7ElementQzG_tFSs8UTF8ViewV_Tgq5', symObjAddr: 0x5F50, symBinAddr: 0xE980, symSize: 0x2D0 }
  - { offsetInCU: 0x4672, offset: 0xB4CB, size: 0x8, addend: 0x0, symName: '_$ss11_StringGutsV27_slowEnsureMatchingEncodingySS5IndexVAEF', symObjAddr: 0x6220, symBinAddr: 0xEC50, symSize: 0xA0 }
  - { offsetInCU: 0x4686, offset: 0xB4DF, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV20_consumeAndCreateNewAByxGyF13FBSDKLoginKit18DeviceLoginManagerC_Tg5', symObjAddr: 0x62C0, symBinAddr: 0xECF0, symSize: 0x60 }
  - { offsetInCU: 0x4717, offset: 0xB570, size: 0x8, addend: 0x0, symName: '_$ss20_ArrayBufferProtocolPsE15replaceSubrange_4with10elementsOfySnySiG_Siqd__ntSlRd__7ElementQyd__AGRtzlFs01_aB0Vy13FBSDKLoginKit18DeviceLoginManagerCG_s15EmptyCollectionVyANGTg5Tf4nndn_n', symObjAddr: 0x65F0, symBinAddr: 0xEF90, symSize: 0x1E0 }
  - { offsetInCU: 0x49C5, offset: 0xB81E, size: 0x8, addend: 0x0, symName: '_$sSa15replaceSubrange_4withySnySiG_qd__nt7ElementQyd__RszSlRd__lF13FBSDKLoginKit18DeviceLoginManagerC_s15EmptyCollectionVyAHGTg5Tf4ndn_n', symObjAddr: 0x67D0, symBinAddr: 0xF170, symSize: 0x110 }
  - { offsetInCU: 0x4AD5, offset: 0xB92E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC10netService_13didNotPublishySo05NSNetG0C_SDySSSo8NSNumberCGtFTf4ndn_n', symObjAddr: 0x7170, symBinAddr: 0xFB10, symSize: 0x130 }
  - { offsetInCU: 0x27, offset: 0xBABF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultC5token11isCancelledACSo16FBSDKAccessTokenCSg_SbtcfC', symObjAddr: 0x0, symBinAddr: 0x10420, symSize: 0xD0 }
  - { offsetInCU: 0x9B, offset: 0xBB33, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultC11accessTokenSo011FBSDKAccessH0CSgvgTo', symObjAddr: 0xD0, symBinAddr: 0x104F0, symSize: 0x40 }
  - { offsetInCU: 0xED, offset: 0xBB85, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultC11accessTokenSo011FBSDKAccessH0CSgvsTo', symObjAddr: 0x150, symBinAddr: 0x10570, symSize: 0x60 }
  - { offsetInCU: 0x141, offset: 0xBBD9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultC11isCancelledSbvgTo', symObjAddr: 0x1B0, symBinAddr: 0x105D0, symSize: 0x40 }
  - { offsetInCU: 0x193, offset: 0xBC2B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultC11isCancelledSbvsTo', symObjAddr: 0x220, symBinAddr: 0x10640, symSize: 0x40 }
  - { offsetInCU: 0x1CB, offset: 0xBC63, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultC5token11isCancelledACSo16FBSDKAccessTokenCSg_SbtcfcTo', symObjAddr: 0x330, symBinAddr: 0x10750, symSize: 0xD0 }
  - { offsetInCU: 0x230, offset: 0xBCC8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultCACycfcTo', symObjAddr: 0x450, symBinAddr: 0x10870, symSize: 0x30 }
  - { offsetInCU: 0x2BC, offset: 0xBD54, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultCfETo', symObjAddr: 0x4B0, symBinAddr: 0x108D0, symSize: 0x20 }
  - { offsetInCU: 0x2E9, offset: 0xBD81, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultCMa', symObjAddr: 0x4D0, symBinAddr: 0x108F0, symSize: 0x20 }
  - { offsetInCU: 0x3FB, offset: 0xBE93, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultC5token11isCancelledACSo16FBSDKAccessTokenCSg_SbtcfC', symObjAddr: 0x0, symBinAddr: 0x10420, symSize: 0xD0 }
  - { offsetInCU: 0x449, offset: 0xBEE1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultC11accessTokenSo011FBSDKAccessH0CSgvg', symObjAddr: 0x110, symBinAddr: 0x10530, symSize: 0x40 }
  - { offsetInCU: 0x499, offset: 0xBF31, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultC11isCancelledSbvg', symObjAddr: 0x1F0, symBinAddr: 0x10610, symSize: 0x30 }
  - { offsetInCU: 0x4D1, offset: 0xBF69, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultC5token11isCancelledACSo16FBSDKAccessTokenCSg_Sbtcfc', symObjAddr: 0x260, symBinAddr: 0x10680, symSize: 0xD0 }
  - { offsetInCU: 0x500, offset: 0xBF98, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultCACycfC', symObjAddr: 0x400, symBinAddr: 0x10820, symSize: 0x20 }
  - { offsetInCU: 0x513, offset: 0xBFAB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultCACycfc', symObjAddr: 0x420, symBinAddr: 0x10840, symSize: 0x30 }
  - { offsetInCU: 0x566, offset: 0xBFFE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultCfD', symObjAddr: 0x480, symBinAddr: 0x108A0, symSize: 0x30 }
  - { offsetInCU: 0x75, offset: 0xC0A4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12DevicePollerVAA0C7PollingA2aDP8schedule8interval5blockySu_yyctFTW', symObjAddr: 0x20, symBinAddr: 0x10960, symSize: 0x10 }
  - { offsetInCU: 0xA5, offset: 0xC0D4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12DevicePollerV8schedule8interval5blockySu_yyctFTf4nnd_n', symObjAddr: 0x30, symBinAddr: 0x10970, symSize: 0x2B0 }
  - { offsetInCU: 0x155, offset: 0xC184, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12DevicePollerVMa', symObjAddr: 0x2E0, symBinAddr: 0x10C20, symSize: 0x10 }
  - { offsetInCU: 0x168, offset: 0xC197, size: 0x8, addend: 0x0, symName: '_$sSo17OS_dispatch_queueCMa', symObjAddr: 0x2F0, symBinAddr: 0x10C30, symSize: 0x30 }
  - { offsetInCU: 0x17B, offset: 0xC1AA, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x320, symBinAddr: 0x10C60, symSize: 0x20 }
  - { offsetInCU: 0x18E, offset: 0xC1BD, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x340, symBinAddr: 0x10C80, symSize: 0x10 }
  - { offsetInCU: 0x1A1, offset: 0xC1D0, size: 0x8, addend: 0x0, symName: '_$sSay8Dispatch0A13WorkItemFlagsVGMa', symObjAddr: 0x3C0, symBinAddr: 0x10CC0, symSize: 0x43 }
  - { offsetInCU: 0x25D, offset: 0xC28C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12DevicePollerVACycfC', symObjAddr: 0x0, symBinAddr: 0x10940, symSize: 0x10 }
  - { offsetInCU: 0x281, offset: 0xC2B0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12DevicePollerV8schedule8interval5blockySu_yyctF', symObjAddr: 0x10, symBinAddr: 0x10950, symSize: 0x10 }
  - { offsetInCU: 0x2B, offset: 0xC343, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperO03getC4InfoSSyFZ', symObjAddr: 0x0, symBinAddr: 0x10D20, symSize: 0x10 }
  - { offsetInCU: 0x4D, offset: 0xC365, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperO25mdnsAdvertisementServicesSo10NSMapTableCySo20NSNetServiceDelegate_pyXlGvpZ', symObjAddr: 0x5200, symBinAddr: 0x62198, symSize: 0x0 }
  - { offsetInCU: 0x67, offset: 0xC37F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperO16NetServiceValues33_D51BDA1F8F5A5E4604761D8AFA9F40BBLLO10sdkVersionSSvpZ', symObjAddr: 0xA20, symBinAddr: 0x5FC08, symSize: 0x0 }
  - { offsetInCU: 0x11C, offset: 0xC434, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperO16NetServiceValues33_D51BDA1F8F5A5E4604761D8AFA9F40BBLLO10sdkVersion_WZ', symObjAddr: 0x150, symBinAddr: 0x10E70, symSize: 0x150 }
  - { offsetInCU: 0x168, offset: 0xC480, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperO27cleanUpAdvertisementService3forySo05NSNetI8Delegate_p_tFZTf4en_n', symObjAddr: 0x2E0, symBinAddr: 0x11000, symSize: 0xE0 }
  - { offsetInCU: 0x1E2, offset: 0xC4FA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperO25startAdvertisementService9loginCode8delegateSbSS_So05NSNetH8Delegate_ptFZTf4nen_n', symObjAddr: 0x3C0, symBinAddr: 0x110E0, symSize: 0x230 }
  - { offsetInCU: 0x370, offset: 0xC688, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperO10isDelegate_23forAdvertisementServiceSbSo05NSNetjG0_p_So0kJ0CtFZTf4enn_n', symObjAddr: 0x5F0, symBinAddr: 0x11310, symSize: 0xC0 }
  - { offsetInCU: 0x3EC, offset: 0xC704, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperO03getC4InfoSSyFZTf4d_n', symObjAddr: 0x6B0, symBinAddr: 0x113D0, symSize: 0x2C0 }
  - { offsetInCU: 0x618, offset: 0xC930, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperO25mdnsAdvertisementServices_WZ', symObjAddr: 0xA0, symBinAddr: 0x10DC0, symSize: 0x30 }
  - { offsetInCU: 0x631, offset: 0xC949, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperO25mdnsAdvertisementServicesSo10NSMapTableCySo20NSNetServiceDelegate_pyXlGvau', symObjAddr: 0xD0, symBinAddr: 0x10DF0, symSize: 0x30 }
  - { offsetInCU: 0x6E0, offset: 0xC9F8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperOMa', symObjAddr: 0x970, symBinAddr: 0x11690, symSize: 0x10 }
  - { offsetInCU: 0x6F3, offset: 0xCA0B, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataVACSTAAWl', symObjAddr: 0x9B0, symBinAddr: 0x116A0, symSize: 0x30 }
  - { offsetInCU: 0x895, offset: 0xCBAD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperO03getC4InfoSSyFZ', symObjAddr: 0x0, symBinAddr: 0x10D20, symSize: 0x10 }
  - { offsetInCU: 0x8A8, offset: 0xCBC0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperO25startAdvertisementService9loginCode8delegateSbSS_So05NSNetH8Delegate_ptFZ', symObjAddr: 0x10, symBinAddr: 0x10D30, symSize: 0x40 }
  - { offsetInCU: 0x8C1, offset: 0xCBD9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperO27cleanUpAdvertisementService3forySo05NSNetI8Delegate_p_tFZ', symObjAddr: 0x50, symBinAddr: 0x10D70, symSize: 0x20 }
  - { offsetInCU: 0x8D4, offset: 0xCBEC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperO10isDelegate_23forAdvertisementServiceSbSo05NSNetjG0_p_So0kJ0CtFZ', symObjAddr: 0x70, symBinAddr: 0x10D90, symSize: 0x30 }
  - { offsetInCU: 0x8ED, offset: 0xCC05, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperO25mdnsAdvertisementServicesSo10NSMapTableCySo20NSNetServiceDelegate_pyXlGvgZ', symObjAddr: 0x100, symBinAddr: 0x10E20, symSize: 0x50 }
  - { offsetInCU: 0x91D, offset: 0xCC35, size: 0x8, addend: 0x0, symName: '_$sSlsE5first7ElementQzSgvgSS_Tg5', symObjAddr: 0x2A0, symBinAddr: 0x10FC0, symSize: 0x40 }
  - { offsetInCU: 0x61, offset: 0xCD95, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15defaultAudienceAA07DefaultF0OvgTo', symObjAddr: 0x0, symBinAddr: 0x116D0, symSize: 0x90 }
  - { offsetInCU: 0xAD, offset: 0xCDE1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15defaultAudienceAA07DefaultF0OvsTo', symObjAddr: 0x140, symBinAddr: 0x117E0, symSize: 0xA0 }
  - { offsetInCU: 0x127, offset: 0xCE5B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC8delegateAA05LoginD8Delegate_pSgvgTo', symObjAddr: 0x4A0, symBinAddr: 0x11B40, symSize: 0x40 }
  - { offsetInCU: 0x17B, offset: 0xCEAF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC8delegateAA05LoginD8Delegate_pSgvsTo', symObjAddr: 0x520, symBinAddr: 0x11BC0, symSize: 0x40 }
  - { offsetInCU: 0x1FB, offset: 0xCF2F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC11permissionsSaySSGvgTo', symObjAddr: 0x680, symBinAddr: 0x11D20, symSize: 0x60 }
  - { offsetInCU: 0x24D, offset: 0xCF81, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC11permissionsSaySSGvsTo', symObjAddr: 0x700, symBinAddr: 0x11DA0, symSize: 0x60 }
  - { offsetInCU: 0x301, offset: 0xD035, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15TooltipBehaviorOSYAASY8rawValuexSg03RawH0Qz_tcfCTW', symObjAddr: 0x1C60, symBinAddr: 0x13290, symSize: 0x20 }
  - { offsetInCU: 0x330, offset: 0xD064, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15TooltipBehaviorOSYAASY8rawValue03RawH0QzvgTW', symObjAddr: 0x1C80, symBinAddr: 0x132B0, symSize: 0x10 }
  - { offsetInCU: 0x34D, offset: 0xD081, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15tooltipBehaviorAC07TooltipF0OvgTo', symObjAddr: 0x7D0, symBinAddr: 0x11E70, symSize: 0x40 }
  - { offsetInCU: 0x39F, offset: 0xD0D3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15tooltipBehaviorAC07TooltipF0OvsTo', symObjAddr: 0x840, symBinAddr: 0x11EE0, symSize: 0x40 }
  - { offsetInCU: 0x408, offset: 0xD13C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC17tooltipColorStyleAA13FBTooltipViewC0fG0OvgTo', symObjAddr: 0x900, symBinAddr: 0x11FA0, symSize: 0x40 }
  - { offsetInCU: 0x45A, offset: 0xD18E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC17tooltipColorStyleAA13FBTooltipViewC0fG0OvsTo', symObjAddr: 0x970, symBinAddr: 0x12010, symSize: 0x40 }
  - { offsetInCU: 0x4C3, offset: 0xD1F7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC13loginTrackingAA05LoginF0OvgTo', symObjAddr: 0xA30, symBinAddr: 0x120D0, symSize: 0x40 }
  - { offsetInCU: 0x515, offset: 0xD249, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC13loginTrackingAA05LoginF0OvsTo', symObjAddr: 0xAA0, symBinAddr: 0x12140, symSize: 0x40 }
  - { offsetInCU: 0x57E, offset: 0xD2B2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5nonceSSSgvgTo', symObjAddr: 0xB60, symBinAddr: 0x12200, symSize: 0x50 }
  - { offsetInCU: 0x5AE, offset: 0xD2E2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5nonceSSSgvsTo', symObjAddr: 0xBE0, symBinAddr: 0x12280, symSize: 0x60 }
  - { offsetInCU: 0x637, offset: 0xD36B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC8authTypeSo0a4AuthF0aSgvgTo', symObjAddr: 0x1030, symBinAddr: 0x12660, symSize: 0x40 }
  - { offsetInCU: 0x689, offset: 0xD3BD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC8authTypeSo0a4AuthF0aSgvsTo', symObjAddr: 0x10B0, symBinAddr: 0x126E0, symSize: 0x60 }
  - { offsetInCU: 0x6F2, offset: 0xD426, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC12codeVerifierAA04CodeF0CvgTo', symObjAddr: 0x11A0, symBinAddr: 0x127D0, symSize: 0x40 }
  - { offsetInCU: 0x744, offset: 0xD478, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC12codeVerifierAA04CodeF0CvsTo', symObjAddr: 0x1220, symBinAddr: 0x12850, symSize: 0x60 }
  - { offsetInCU: 0x816, offset: 0xD54A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC19graphRequestFactorySo010FBSDKGraphfG0_pvgTo', symObjAddr: 0x18B0, symBinAddr: 0x12EE0, symSize: 0x50 }
  - { offsetInCU: 0x86A, offset: 0xD59E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC19graphRequestFactorySo010FBSDKGraphfG0_pvsTo', symObjAddr: 0x1960, symBinAddr: 0x12F90, symSize: 0x60 }
  - { offsetInCU: 0x8D3, offset: 0xD607, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15isAuthenticatedSbvgTo', symObjAddr: 0x1A70, symBinAddr: 0x130A0, symSize: 0xA0 }
  - { offsetInCU: 0x993, offset: 0xD6C7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5frameACSo6CGRectV_tcfcTo', symObjAddr: 0x2410, symBinAddr: 0x13A40, symSize: 0x30 }
  - { offsetInCU: 0x9E0, offset: 0xD714, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5coderACSgSo7NSCoderC_tcfcTo', symObjAddr: 0x24B0, symBinAddr: 0x13AE0, symSize: 0x40 }
  - { offsetInCU: 0xADA, offset: 0xD80E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15didMoveToWindowyyFTo', symObjAddr: 0x2C50, symBinAddr: 0x14280, symSize: 0x30 }
  - { offsetInCU: 0xB0F, offset: 0xD843, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC9imageRect010forContentF0So6CGRectVAG_tFTo', symObjAddr: 0x2CE0, symBinAddr: 0x14310, symSize: 0x70 }
  - { offsetInCU: 0xB6E, offset: 0xD8A2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC9titleRect010forContentF0So6CGRectVAG_tFTo', symObjAddr: 0x2F10, symBinAddr: 0x14540, symSize: 0x1C0 }
  - { offsetInCU: 0xC75, offset: 0xD9A9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC14layoutSubviewsyyFTo', symObjAddr: 0x34A0, symBinAddr: 0x14AD0, symSize: 0x30 }
  - { offsetInCU: 0xCAA, offset: 0xD9DE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC12sizeThatFitsySo6CGSizeVAFFTo', symObjAddr: 0x39A0, symBinAddr: 0x14FD0, symSize: 0x60 }
  - { offsetInCU: 0xCF8, offset: 0xDA2C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC20accessTokenDidChangeyy10Foundation12NotificationVFTo', symObjAddr: 0x3CE0, symBinAddr: 0x15310, symSize: 0x80 }
  - { offsetInCU: 0xD2F, offset: 0xDA63, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC16profileDidChangeyy10Foundation12NotificationVFTo', symObjAddr: 0x3FA0, symBinAddr: 0x155D0, symSize: 0x80 }
  - { offsetInCU: 0xD94, offset: 0xDAC8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC13buttonPressedyyypFTo', symObjAddr: 0x4A70, symBinAddr: 0x160A0, symSize: 0x60 }
  - { offsetInCU: 0xDC6, offset: 0xDAFA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC22makeLoginConfigurationAA0fG0CSgyFTo', symObjAddr: 0x4DD0, symBinAddr: 0x16400, symSize: 0x30 }
  - { offsetInCU: 0xDE1, offset: 0xDB15, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC9logInUser33_2D5546723C2E9E390359F57C16888789LLyyFyAA11LoginResultOcfU_', symObjAddr: 0x4E00, symBinAddr: 0x16430, symSize: 0xE0 }
  - { offsetInCU: 0xE36, offset: 0xDB6A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC17initializeContentyyFTo', symObjAddr: 0x50B0, symBinAddr: 0x166E0, symSize: 0x30 }
  - { offsetInCU: 0xE68, offset: 0xDB9C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC27updateContentForAccessTokenyyFTo', symObjAddr: 0x5300, symBinAddr: 0x16930, symSize: 0x30 }
  - { offsetInCU: 0xE83, offset: 0xDBB7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC18fetchAndSetContentyyFTo', symObjAddr: 0x55C0, symBinAddr: 0x16BF0, symSize: 0x30 }
  - { offsetInCU: 0xEBA, offset: 0xDBEE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC20updateContentForUseryy09FBSDKCoreB07ProfileCSgFTo', symObjAddr: 0x5700, symBinAddr: 0x16D30, symSize: 0x60 }
  - { offsetInCU: 0xED5, offset: 0xDC09, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC6logoutyyFTo', symObjAddr: 0x5760, symBinAddr: 0x16D90, symSize: 0x30 }
  - { offsetInCU: 0xF20, offset: 0xDC54, size: 0x8, addend: 0x0, symName: ___swift_mutable_project_boxed_opaque_existential_1, symObjAddr: 0x250, symBinAddr: 0x118F0, symSize: 0x30 }
  - { offsetInCU: 0xF33, offset: 0xDC67, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15defaultAudienceAA07DefaultF0OvpACTK', symObjAddr: 0x280, symBinAddr: 0x11920, symSize: 0x80 }
  - { offsetInCU: 0xF69, offset: 0xDC9D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15defaultAudienceAA07DefaultF0OvpACTk', symObjAddr: 0x300, symBinAddr: 0x119A0, symSize: 0x80 }
  - { offsetInCU: 0x106D, offset: 0xDDA1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit29UserInterfaceElementProviding_pWOb', symObjAddr: 0x1640, symBinAddr: 0x12C70, symSize: 0x20 }
  - { offsetInCU: 0x1080, offset: 0xDDB4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28UserInterfaceStringProviding_pWOb', symObjAddr: 0x1780, symBinAddr: 0x12DB0, symSize: 0x20 }
  - { offsetInCU: 0x1093, offset: 0xDDC7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14LoginProviding_pWOb', symObjAddr: 0x1850, symBinAddr: 0x12E80, symSize: 0x20 }
  - { offsetInCU: 0x10EA, offset: 0xDE1E, size: 0x8, addend: 0x0, symName: '_$sSo20FBSDKInternalUtilityCMa', symObjAddr: 0x1FD0, symBinAddr: 0x13600, symSize: 0x30 }
  - { offsetInCU: 0x142A, offset: 0xE15E, size: 0x8, addend: 0x0, symName: '_$sSo13UIAlertActionCIegg_ABIeyBy_TR', symObjAddr: 0x4EE0, symBinAddr: 0x16510, symSize: 0x50 }
  - { offsetInCU: 0x14BE, offset: 0xE1F2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonCfETo', symObjAddr: 0x57C0, symBinAddr: 0x16DF0, symSize: 0xE0 }
  - { offsetInCU: 0x1649, offset: 0xE37D, size: 0x8, addend: 0x0, symName: '_$sypSgWOb', symObjAddr: 0x5D10, symBinAddr: 0x17310, symSize: 0x40 }
  - { offsetInCU: 0x165C, offset: 0xE390, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC9logInUser33_2D5546723C2E9E390359F57C16888789LLyyFyAA11LoginResultOcfU_TA', symObjAddr: 0x5DE0, symBinAddr: 0x17380, symSize: 0x10 }
  - { offsetInCU: 0x16BF, offset: 0xE3F3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC18fetchAndSetContentyyFySo27FBSDKGraphRequestConnecting_pSg_ypSgs5Error_pSgtcfU_TA', symObjAddr: 0x5F80, symBinAddr: 0x17520, symSize: 0x10 }
  - { offsetInCU: 0x16D2, offset: 0xE406, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x5F90, symBinAddr: 0x17530, symSize: 0x20 }
  - { offsetInCU: 0x16E5, offset: 0xE419, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x5FB0, symBinAddr: 0x17550, symSize: 0x10 }
  - { offsetInCU: 0x16F8, offset: 0xE42C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15TooltipBehaviorOSHAASQWb', symObjAddr: 0x5FC0, symBinAddr: 0x17560, symSize: 0x10 }
  - { offsetInCU: 0x170B, offset: 0xE43F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15TooltipBehaviorOAESQAAWl', symObjAddr: 0x5FD0, symBinAddr: 0x17570, symSize: 0x30 }
  - { offsetInCU: 0x171E, offset: 0xE452, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonCMa', symObjAddr: 0x6110, symBinAddr: 0x176B0, symSize: 0x20 }
  - { offsetInCU: 0x1731, offset: 0xE465, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15TooltipBehaviorOMa', symObjAddr: 0x6130, symBinAddr: 0x176D0, symSize: 0x10 }
  - { offsetInCU: 0x1744, offset: 0xE478, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19LoginButtonDelegate_pSgXwWOh', symObjAddr: 0x6170, symBinAddr: 0x17710, symSize: 0x20 }
  - { offsetInCU: 0x1757, offset: 0xE48B, size: 0x8, addend: 0x0, symName: '_$sypSgWOc', symObjAddr: 0x6190, symBinAddr: 0x17730, symSize: 0x40 }
  - { offsetInCU: 0x176A, offset: 0xE49E, size: 0x8, addend: 0x0, symName: '_$s12FBSDKCoreKit10PermissionOACSHAAWl', symObjAddr: 0x61D0, symBinAddr: 0x17770, symSize: 0x40 }
  - { offsetInCU: 0x177D, offset: 0xE4B1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC26presentAlertViewController33_2D5546723C2E9E390359F57C16888789LLyyFySo13UIAlertActionCcfU_TA', symObjAddr: 0x6210, symBinAddr: 0x177B0, symSize: 0x10 }
  - { offsetInCU: 0x1790, offset: 0xE4C4, size: 0x8, addend: 0x0, symName: '_$sS2Ss7CVarArg10FoundationWl', symObjAddr: 0x6220, symBinAddr: 0x177C0, symSize: 0x30 }
  - { offsetInCU: 0x17A3, offset: 0xE4D7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultOWOe', symObjAddr: 0x6250, symBinAddr: 0x177F0, symSize: 0x50 }
  - { offsetInCU: 0x18A5, offset: 0xE5D9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15TooltipBehaviorOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x1BB0, symBinAddr: 0x131E0, symSize: 0x10 }
  - { offsetInCU: 0x193F, offset: 0xE673, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15TooltipBehaviorOSHAASH9hashValueSivgTW', symObjAddr: 0x1BC0, symBinAddr: 0x131F0, symSize: 0x40 }
  - { offsetInCU: 0x19E6, offset: 0xE71A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15TooltipBehaviorOSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0x1C00, symBinAddr: 0x13230, symSize: 0x20 }
  - { offsetInCU: 0x1A35, offset: 0xE769, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15TooltipBehaviorOSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0x1C20, symBinAddr: 0x13250, symSize: 0x40 }
  - { offsetInCU: 0x1CF5, offset: 0xEA29, size: 0x8, addend: 0x0, symName: '_$sShyShyxGqd__nc7ElementQyd__RszSTRd__lufC12FBSDKCoreKit10PermissionO_SayAFGTgm5Tf4g_n', symObjAddr: 0x5DF0, symBinAddr: 0x17390, symSize: 0x100 }
  - { offsetInCU: 0x1E27, offset: 0xEB5B, size: 0x8, addend: 0x0, symName: '_$sShyShyxGqd__nc7ElementQyd__RszSTRd__lufCSS_SaySSGTgm5Tf4g_n', symObjAddr: 0x5EF0, symBinAddr: 0x17490, symSize: 0x90 }
  - { offsetInCU: 0x219C, offset: 0xEED0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15defaultAudienceAA07DefaultF0Ovg', symObjAddr: 0x90, symBinAddr: 0x11760, symSize: 0x80 }
  - { offsetInCU: 0x21E1, offset: 0xEF15, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15defaultAudienceAA07DefaultF0Ovs', symObjAddr: 0x1E0, symBinAddr: 0x11880, symSize: 0x70 }
  - { offsetInCU: 0x2207, offset: 0xEF3B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15defaultAudienceAA07DefaultF0OvM', symObjAddr: 0x380, symBinAddr: 0x11A20, symSize: 0xA0 }
  - { offsetInCU: 0x2247, offset: 0xEF7B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15defaultAudienceAA07DefaultF0OvM.resume.0', symObjAddr: 0x420, symBinAddr: 0x11AC0, symSize: 0x80 }
  - { offsetInCU: 0x2283, offset: 0xEFB7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC8delegateAA05LoginD8Delegate_pSgvg', symObjAddr: 0x4E0, symBinAddr: 0x11B80, symSize: 0x40 }
  - { offsetInCU: 0x22C2, offset: 0xEFF6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC8delegateAA05LoginD8Delegate_pSgvs', symObjAddr: 0x560, symBinAddr: 0x11C00, symSize: 0x50 }
  - { offsetInCU: 0x22E8, offset: 0xF01C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC8delegateAA05LoginD8Delegate_pSgvM', symObjAddr: 0x5B0, symBinAddr: 0x11C50, symSize: 0x70 }
  - { offsetInCU: 0x2309, offset: 0xF03D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC8delegateAA05LoginD8Delegate_pSgvM.resume.0', symObjAddr: 0x620, symBinAddr: 0x11CC0, symSize: 0x60 }
  - { offsetInCU: 0x235C, offset: 0xF090, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC11permissionsSaySSGvM', symObjAddr: 0x780, symBinAddr: 0x11E20, symSize: 0x40 }
  - { offsetInCU: 0x237F, offset: 0xF0B3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC11permissionsSaySSGvM.resume.0', symObjAddr: 0x7C0, symBinAddr: 0x11E60, symSize: 0x10 }
  - { offsetInCU: 0x23B0, offset: 0xF0E4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15tooltipBehaviorAC07TooltipF0Ovg', symObjAddr: 0x810, symBinAddr: 0x11EB0, symSize: 0x30 }
  - { offsetInCU: 0x23ED, offset: 0xF121, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15tooltipBehaviorAC07TooltipF0Ovs', symObjAddr: 0x880, symBinAddr: 0x11F20, symSize: 0x40 }
  - { offsetInCU: 0x2411, offset: 0xF145, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15tooltipBehaviorAC07TooltipF0OvM', symObjAddr: 0x8C0, symBinAddr: 0x11F60, symSize: 0x40 }
  - { offsetInCU: 0x2446, offset: 0xF17A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC17tooltipColorStyleAA13FBTooltipViewC0fG0Ovg', symObjAddr: 0x940, symBinAddr: 0x11FE0, symSize: 0x30 }
  - { offsetInCU: 0x2483, offset: 0xF1B7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC17tooltipColorStyleAA13FBTooltipViewC0fG0Ovs', symObjAddr: 0x9B0, symBinAddr: 0x12050, symSize: 0x40 }
  - { offsetInCU: 0x24A7, offset: 0xF1DB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC17tooltipColorStyleAA13FBTooltipViewC0fG0OvM', symObjAddr: 0x9F0, symBinAddr: 0x12090, symSize: 0x40 }
  - { offsetInCU: 0x24DC, offset: 0xF210, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC13loginTrackingAA05LoginF0Ovg', symObjAddr: 0xA70, symBinAddr: 0x12110, symSize: 0x30 }
  - { offsetInCU: 0x2519, offset: 0xF24D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC13loginTrackingAA05LoginF0Ovs', symObjAddr: 0xAE0, symBinAddr: 0x12180, symSize: 0x40 }
  - { offsetInCU: 0x253D, offset: 0xF271, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC13loginTrackingAA05LoginF0OvM', symObjAddr: 0xB20, symBinAddr: 0x121C0, symSize: 0x40 }
  - { offsetInCU: 0x2572, offset: 0xF2A6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5nonceSSSgvg', symObjAddr: 0xBB0, symBinAddr: 0x12250, symSize: 0x30 }
  - { offsetInCU: 0x25E0, offset: 0xF314, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5nonceSSSgvs', symObjAddr: 0xC40, symBinAddr: 0x122E0, symSize: 0x220 }
  - { offsetInCU: 0x2722, offset: 0xF456, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5nonceSSSgvM', symObjAddr: 0xED0, symBinAddr: 0x12500, symSize: 0x40 }
  - { offsetInCU: 0x275E, offset: 0xF492, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5nonceSSSgvM.resume.0', symObjAddr: 0xF10, symBinAddr: 0x12540, symSize: 0x60 }
  - { offsetInCU: 0x277D, offset: 0xF4B1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15messengerPageIdSSSgvM', symObjAddr: 0xFF0, symBinAddr: 0x12620, symSize: 0x40 }
  - { offsetInCU: 0x27B2, offset: 0xF4E6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC8authTypeSo0a4AuthF0aSgvg', symObjAddr: 0x1070, symBinAddr: 0x126A0, symSize: 0x40 }
  - { offsetInCU: 0x27EF, offset: 0xF523, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC8authTypeSo0a4AuthF0aSgvs', symObjAddr: 0x1110, symBinAddr: 0x12740, symSize: 0x50 }
  - { offsetInCU: 0x2813, offset: 0xF547, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC8authTypeSo0a4AuthF0aSgvM', symObjAddr: 0x1160, symBinAddr: 0x12790, symSize: 0x40 }
  - { offsetInCU: 0x2848, offset: 0xF57C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC12codeVerifierAA04CodeF0Cvg', symObjAddr: 0x11E0, symBinAddr: 0x12810, symSize: 0x40 }
  - { offsetInCU: 0x2885, offset: 0xF5B9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC12codeVerifierAA04CodeF0Cvs', symObjAddr: 0x1280, symBinAddr: 0x128B0, symSize: 0x50 }
  - { offsetInCU: 0x28A9, offset: 0xF5DD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC12codeVerifierAA04CodeF0CvM', symObjAddr: 0x12D0, symBinAddr: 0x12900, symSize: 0x40 }
  - { offsetInCU: 0x28CC, offset: 0xF600, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC6userIDSSSgvM', symObjAddr: 0x1390, symBinAddr: 0x129C0, symSize: 0x40 }
  - { offsetInCU: 0x28EF, offset: 0xF623, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC8userNameSSSgvM', symObjAddr: 0x15C0, symBinAddr: 0x12BF0, symSize: 0x40 }
  - { offsetInCU: 0x2912, offset: 0xF646, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15elementProviderAA29UserInterfaceElementProviding_pvM', symObjAddr: 0x1660, symBinAddr: 0x12C90, symSize: 0x40 }
  - { offsetInCU: 0x2935, offset: 0xF669, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC14stringProviderAA28UserInterfaceStringProviding_pvM', symObjAddr: 0x17A0, symBinAddr: 0x12DD0, symSize: 0x40 }
  - { offsetInCU: 0x2958, offset: 0xF68C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC13loginProviderAA14LoginProviding_pvM', symObjAddr: 0x1870, symBinAddr: 0x12EA0, symSize: 0x40 }
  - { offsetInCU: 0x29AF, offset: 0xF6E3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC19graphRequestFactorySo010FBSDKGraphfG0_pvM', symObjAddr: 0x1A30, symBinAddr: 0x13060, symSize: 0x40 }
  - { offsetInCU: 0x29E4, offset: 0xF718, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15isAuthenticatedSbvg', symObjAddr: 0x1B10, symBinAddr: 0x13140, symSize: 0x70 }
  - { offsetInCU: 0x2A03, offset: 0xF737, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15TooltipBehaviorO8rawValueAESgSu_tcfC', symObjAddr: 0x1B80, symBinAddr: 0x131B0, symSize: 0x20 }
  - { offsetInCU: 0x2A1E, offset: 0xF752, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15TooltipBehaviorO8rawValueSuvg', symObjAddr: 0x1BA0, symBinAddr: 0x131D0, symSize: 0x10 }
  - { offsetInCU: 0x2A96, offset: 0xF7CA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5frameACSo6CGRectV_tcfC', symObjAddr: 0x1C90, symBinAddr: 0x132C0, symSize: 0x80 }
  - { offsetInCU: 0x2AC7, offset: 0xF7FB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5frameACSo6CGRectV_tcfc', symObjAddr: 0x1D10, symBinAddr: 0x13340, symSize: 0x2C0 }
  - { offsetInCU: 0x2B8C, offset: 0xF8C0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC09configureD033_2D5546723C2E9E390359F57C16888789LLyyF', symObjAddr: 0x2000, symBinAddr: 0x13630, symSize: 0x410 }
  - { offsetInCU: 0x2C3E, offset: 0xF972, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5coderACSgSo7NSCoderC_tcfC', symObjAddr: 0x2440, symBinAddr: 0x13A70, symSize: 0x40 }
  - { offsetInCU: 0x2C51, offset: 0xF985, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5coderACSgSo7NSCoderC_tcfc', symObjAddr: 0x2480, symBinAddr: 0x13AB0, symSize: 0x30 }
  - { offsetInCU: 0x2C6A, offset: 0xF99E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5frame15elementProvider06stringG005loginG019graphRequestFactoryACSo6CGRectV_AA29UserInterfaceElementProviding_pAA0no6StringQ0_pAA05LoginQ0_pSo010FBSDKGraphkL0_ptcfC', symObjAddr: 0x24F0, symBinAddr: 0x13B20, symSize: 0x180 }
  - { offsetInCU: 0x2D4F, offset: 0xFA83, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5frame11permissionsACSo6CGRectV_Say09FBSDKCoreB010PermissionOGtcfC', symObjAddr: 0x2670, symBinAddr: 0x13CA0, symSize: 0x260 }
  - { offsetInCU: 0x2F79, offset: 0xFCAD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC17initializeContentyyF', symObjAddr: 0x28D0, symBinAddr: 0x13F00, symSize: 0x180 }
  - { offsetInCU: 0x2FCA, offset: 0xFCFE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15didMoveToWindowyyF', symObjAddr: 0x2A50, symBinAddr: 0x14080, symSize: 0xA0 }
  - { offsetInCU: 0x307A, offset: 0xFDAE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC19showTooltipIfNeeded33_2D5546723C2E9E390359F57C16888789LLyyF', symObjAddr: 0x2AF0, symBinAddr: 0x14120, symSize: 0x160 }
  - { offsetInCU: 0x3237, offset: 0xFF6B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC9imageRect010forContentF0So6CGRectVAG_tF', symObjAddr: 0x2C80, symBinAddr: 0x142B0, symSize: 0x60 }
  - { offsetInCU: 0x32A7, offset: 0xFFDB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC9titleRect010forContentF0So6CGRectVAG_tF', symObjAddr: 0x2D50, symBinAddr: 0x14380, symSize: 0x1C0 }
  - { offsetInCU: 0x33F2, offset: 0x10126, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC14layoutSubviewsyyF', symObjAddr: 0x30D0, symBinAddr: 0x14700, symSize: 0x3D0 }
  - { offsetInCU: 0x34C6, offset: 0x101FA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC12sizeThatFitsySo6CGSizeVAFF', symObjAddr: 0x34D0, symBinAddr: 0x14B00, symSize: 0x4D0 }
  - { offsetInCU: 0x35DD, offset: 0x10311, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC20accessTokenDidChangeyy10Foundation12NotificationVF', symObjAddr: 0x3A00, symBinAddr: 0x15030, symSize: 0x1B0 }
  - { offsetInCU: 0x3682, offset: 0x103B6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC27updateContentForAccessTokenyyF', symObjAddr: 0x3BB0, symBinAddr: 0x151E0, symSize: 0x130 }
  - { offsetInCU: 0x36D1, offset: 0x10405, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC16profileDidChangeyy10Foundation12NotificationVF', symObjAddr: 0x3D60, symBinAddr: 0x15390, symSize: 0x140 }
  - { offsetInCU: 0x3728, offset: 0x1045C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC20updateContentForUseryy09FBSDKCoreB07ProfileCSgF', symObjAddr: 0x3EA0, symBinAddr: 0x154D0, symSize: 0x100 }
  - { offsetInCU: 0x3790, offset: 0x104C4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC13buttonPressedyyypF', symObjAddr: 0x4020, symBinAddr: 0x15650, symSize: 0x260 }
  - { offsetInCU: 0x383E, offset: 0x10572, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC26presentAlertViewController33_2D5546723C2E9E390359F57C16888789LLyyF', symObjAddr: 0x4280, symBinAddr: 0x158B0, symSize: 0x7F0 }
  - { offsetInCU: 0x39B5, offset: 0x106E9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC26presentAlertViewController33_2D5546723C2E9E390359F57C16888789LLyyFySo13UIAlertActionCcfU_', symObjAddr: 0x4F30, symBinAddr: 0x16560, symSize: 0xC0 }
  - { offsetInCU: 0x3A5A, offset: 0x1078E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC22makeLoginConfigurationAA0fG0CSgyF', symObjAddr: 0x4AD0, symBinAddr: 0x16100, symSize: 0x300 }
  - { offsetInCU: 0x3CAF, offset: 0x109E3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC6logoutyyF', symObjAddr: 0x4FF0, symBinAddr: 0x16620, symSize: 0xC0 }
  - { offsetInCU: 0x3CFC, offset: 0x10A30, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC18fetchAndSetContentyyF', symObjAddr: 0x50E0, symBinAddr: 0x16710, symSize: 0x220 }
  - { offsetInCU: 0x3DEC, offset: 0x10B20, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC18fetchAndSetContentyyFySo27FBSDKGraphRequestConnecting_pSg_ypSgs5Error_pSgtcfU_', symObjAddr: 0x5330, symBinAddr: 0x16960, symSize: 0x290 }
  - { offsetInCU: 0x3F05, offset: 0x10C39, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC27userInformationDoesNotMatch33_2D5546723C2E9E390359F57C16888789LLySb09FBSDKCoreB07ProfileCF', symObjAddr: 0x55F0, symBinAddr: 0x16C20, symSize: 0x110 }
  - { offsetInCU: 0x3F7E, offset: 0x10CB2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonCfD', symObjAddr: 0x5790, symBinAddr: 0x16DC0, symSize: 0x30 }
  - { offsetInCU: 0x3F9F, offset: 0x10CD3, size: 0x8, addend: 0x0, symName: '_$ss15ContiguousArrayV16_createNewBuffer14bufferIsUnique15minimumCapacity13growForAppendySb_SiSbtFSS_Tg5', symObjAddr: 0x58A0, symBinAddr: 0x16ED0, symSize: 0x20 }
  - { offsetInCU: 0x3FB3, offset: 0x10CE7, size: 0x8, addend: 0x0, symName: '_$ss15ContiguousArrayV16_createNewBuffer14bufferIsUnique15minimumCapacity13growForAppendySb_SiSbtF12FBSDKCoreKit10PermissionO_Tg5', symObjAddr: 0x58C0, symBinAddr: 0x16EF0, symSize: 0x20 }
  - { offsetInCU: 0x3FEB, offset: 0x10D1F, size: 0x8, addend: 0x0, symName: '_$ss22_ContiguousArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtFSS_Tg5', symObjAddr: 0x58E0, symBinAddr: 0x16F10, symSize: 0xB0 }
  - { offsetInCU: 0x4090, offset: 0x10DC4, size: 0x8, addend: 0x0, symName: '_$ss22_ContiguousArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtF12FBSDKCoreKit10PermissionO_Tg5', symObjAddr: 0x5990, symBinAddr: 0x16FC0, symSize: 0xE0 }
  - { offsetInCU: 0x4137, offset: 0x10E6B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5coderACSgSo7NSCoderC_tcfcTf4gn_n', symObjAddr: 0x5A70, symBinAddr: 0x170A0, symSize: 0x270 }
  - { offsetInCU: 0x76, offset: 0x11007, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC8delegateAA05LogindE8Delegate_pSgvgTo', symObjAddr: 0x20, symBinAddr: 0x17960, symSize: 0x40 }
  - { offsetInCU: 0xCA, offset: 0x1105B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC8delegateAA05LogindE8Delegate_pSgvsTo', symObjAddr: 0xA0, symBinAddr: 0x179E0, symSize: 0x40 }
  - { offsetInCU: 0x14A, offset: 0x110DB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC12forceDisplaySbvgTo', symObjAddr: 0x200, symBinAddr: 0x17B40, symSize: 0x40 }
  - { offsetInCU: 0x19C, offset: 0x1112D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC12forceDisplaySbvsTo', symObjAddr: 0x270, symBinAddr: 0x17BB0, symSize: 0x40 }
  - { offsetInCU: 0x2C7, offset: 0x11258, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewCACycfcTo', symObjAddr: 0x4E0, symBinAddr: 0x17E20, symSize: 0xA0 }
  - { offsetInCU: 0x3A4, offset: 0x11335, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC7tagline7message10colorStyleACSSSg_AgA09FBTooltipE0C05ColorI0OtcfcTo', symObjAddr: 0x7B0, symBinAddr: 0x180F0, symSize: 0x70 }
  - { offsetInCU: 0x423, offset: 0x113B4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC7present2in13arrowPosition9directionySo6UIViewC_So7CGPointVAA09FBTooltipE0C14ArrowDirectionOtFTo', symObjAddr: 0xAB0, symBinAddr: 0x183F0, symSize: 0x80 }
  - { offsetInCU: 0x43E, offset: 0x113CF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC05fetchD13Configuration015_3798D0DCC12906H16A17D23FC0B3F34C1LL4with13arrowPosition9directionySo6UIViewC_So7CGPointVAA09FBTooltipE0C14ArrowDirectionOtFySo0aD0CSg_s5Error_pSgtcfU_', symObjAddr: 0xB30, symBinAddr: 0x18470, symSize: 0x250 }
  - { offsetInCU: 0x593, offset: 0x11524, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC27serverConfigurationProvider06stringH0AcA06ServerG9Providing_p_AA019UserInterfaceStringK0_ptcfcTf4een_n09FBSDKCoreB00jgH0C_So20FBSDKInternalUtilityCTg5', symObjAddr: 0xE30, symBinAddr: 0x18770, symSize: 0x160 }
  - { offsetInCU: 0x62F, offset: 0x115C0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC27serverConfigurationProvider06stringH0AcA06ServerG9Providing_p_AA019UserInterfaceStringK0_ptcfcTf4een_n', symObjAddr: 0xFF0, symBinAddr: 0x188D0, symSize: 0x170 }
  - { offsetInCU: 0x6BD, offset: 0x1164E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC27serverConfigurationProvider06stringH0AcA06ServerG9Providing_p_AA019UserInterfaceStringK0_ptcfCTfq4een_nTf4ngn_nTf4gnn_n', symObjAddr: 0x1160, symBinAddr: 0x18A40, symSize: 0xC0 }
  - { offsetInCU: 0x7CC, offset: 0x1175D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewCfETo', symObjAddr: 0xDF0, symBinAddr: 0x18730, symSize: 0x40 }
  - { offsetInCU: 0x7F9, offset: 0x1178A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC05fetchD13Configuration015_3798D0DCC12906H16A17D23FC0B3F34C1LL4with13arrowPosition9directionySo6UIViewC_So7CGPointVAA09FBTooltipE0C14ArrowDirectionOtFySo0aD0CSg_s5Error_pSgtcfU_TA', symObjAddr: 0x12C0, symBinAddr: 0x18B40, symSize: 0x30 }
  - { offsetInCU: 0x80C, offset: 0x1179D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewCMa', symObjAddr: 0x13F0, symBinAddr: 0x18C70, symSize: 0x20 }
  - { offsetInCU: 0x81F, offset: 0x117B0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24LoginTooltipViewDelegate_pSgXwWOh', symObjAddr: 0x1440, symBinAddr: 0x18CC0, symSize: 0x20 }
  - { offsetInCU: 0x832, offset: 0x117C3, size: 0x8, addend: 0x0, symName: ___swift_allocate_boxed_opaque_existential_1, symObjAddr: 0x1460, symBinAddr: 0x18CE0, symSize: 0x30 }
  - { offsetInCU: 0x9DF, offset: 0x11970, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewCACycfC', symObjAddr: 0x0, symBinAddr: 0x17940, symSize: 0x20 }
  - { offsetInCU: 0xA09, offset: 0x1199A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC8delegateAA05LogindE8Delegate_pSgvg', symObjAddr: 0x60, symBinAddr: 0x179A0, symSize: 0x40 }
  - { offsetInCU: 0xA4E, offset: 0x119DF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC8delegateAA05LogindE8Delegate_pSgvs', symObjAddr: 0xE0, symBinAddr: 0x17A20, symSize: 0x50 }
  - { offsetInCU: 0xA74, offset: 0x11A05, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC8delegateAA05LogindE8Delegate_pSgvM', symObjAddr: 0x130, symBinAddr: 0x17A70, symSize: 0x70 }
  - { offsetInCU: 0xA95, offset: 0x11A26, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC8delegateAA05LogindE8Delegate_pSgvM.resume.0', symObjAddr: 0x1A0, symBinAddr: 0x17AE0, symSize: 0x60 }
  - { offsetInCU: 0xAC6, offset: 0x11A57, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC12forceDisplaySbvg', symObjAddr: 0x240, symBinAddr: 0x17B80, symSize: 0x30 }
  - { offsetInCU: 0xB03, offset: 0x11A94, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC12forceDisplaySbvs', symObjAddr: 0x2B0, symBinAddr: 0x17BF0, symSize: 0x40 }
  - { offsetInCU: 0xB27, offset: 0x11AB8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC12forceDisplaySbvM', symObjAddr: 0x2F0, symBinAddr: 0x17C30, symSize: 0x40 }
  - { offsetInCU: 0xB4A, offset: 0x11ADB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC12forceDisplaySbvM.resume.0', symObjAddr: 0x330, symBinAddr: 0x17C70, symSize: 0x10 }
  - { offsetInCU: 0xB7B, offset: 0x11B0C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC18shouldForceDisplaySbvM', symObjAddr: 0x340, symBinAddr: 0x17C80, symSize: 0x70 }
  - { offsetInCU: 0xBB7, offset: 0x11B48, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC18shouldForceDisplaySbvM.resume.0', symObjAddr: 0x3B0, symBinAddr: 0x17CF0, symSize: 0x20 }
  - { offsetInCU: 0xBE9, offset: 0x11B7A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC27serverConfigurationProviderAA06ServerG9Providing_pvg', symObjAddr: 0x3D0, symBinAddr: 0x17D10, symSize: 0x20 }
  - { offsetInCU: 0xC0A, offset: 0x11B9B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC14stringProviderAA28UserInterfaceStringProviding_pvg', symObjAddr: 0x3F0, symBinAddr: 0x17D30, symSize: 0x20 }
  - { offsetInCU: 0xC43, offset: 0x11BD4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewCACycfc', symObjAddr: 0x440, symBinAddr: 0x17D80, symSize: 0xA0 }
  - { offsetInCU: 0xD20, offset: 0x11CB1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC7tagline7message10colorStyleACSSSg_AgA09FBTooltipE0C05ColorI0OtcfC', symObjAddr: 0x580, symBinAddr: 0x17EC0, symSize: 0x120 }
  - { offsetInCU: 0xD70, offset: 0x11D01, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC7tagline7message10colorStyleACSSSg_AgA09FBTooltipE0C05ColorI0Otcfc', symObjAddr: 0x6A0, symBinAddr: 0x17FE0, symSize: 0x110 }
  - { offsetInCU: 0xDBD, offset: 0x11D4E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC27serverConfigurationProvider06stringH0AcA06ServerG9Providing_p_AA019UserInterfaceStringK0_ptcfC', symObjAddr: 0x820, symBinAddr: 0x18160, symSize: 0x90 }
  - { offsetInCU: 0xDF9, offset: 0x11D8A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC27serverConfigurationProvider06stringH0AcA06ServerG9Providing_p_AA019UserInterfaceStringK0_ptcfc', symObjAddr: 0x8B0, symBinAddr: 0x181F0, symSize: 0xE0 }
  - { offsetInCU: 0xE69, offset: 0x11DFA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC7present2in13arrowPosition9directionySo6UIViewC_So7CGPointVAA09FBTooltipE0C14ArrowDirectionOtF', symObjAddr: 0x990, symBinAddr: 0x182D0, symSize: 0x120 }
  - { offsetInCU: 0xF61, offset: 0x11EF2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewCfE', symObjAddr: 0xD80, symBinAddr: 0x186C0, symSize: 0x40 }
  - { offsetInCU: 0xF84, offset: 0x11F15, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewCfD', symObjAddr: 0xDC0, symBinAddr: 0x18700, symSize: 0x30 }
  - { offsetInCU: 0x168, offset: 0x120E6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionC4hashSivgTo', symObjAddr: 0x3E0, symBinAddr: 0x19130, symSize: 0x40 }
  - { offsetInCU: 0x1D6, offset: 0x12154, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionC6stringACSgSS_tcfcTo', symObjAddr: 0x650, symBinAddr: 0x19370, symSize: 0x30 }
  - { offsetInCU: 0x209, offset: 0x12187, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionC11permissions18fromRawPermissionsShyACGSgShySSG_tFZTo', symObjAddr: 0x690, symBinAddr: 0x193B0, symSize: 0x90 }
  - { offsetInCU: 0x239, offset: 0x121B7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionC14rawPermissions4fromShySSGShyACG_tFZTo', symObjAddr: 0x760, symBinAddr: 0x19480, symSize: 0x90 }
  - { offsetInCU: 0x29F, offset: 0x1221D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionC7isEqualySbypSgFTo', symObjAddr: 0x8D0, symBinAddr: 0x195F0, symSize: 0x90 }
  - { offsetInCU: 0x2E4, offset: 0x12262, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionCACycfcTo', symObjAddr: 0x9B0, symBinAddr: 0x196D0, symSize: 0x30 }
  - { offsetInCU: 0x35B, offset: 0x122D9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionC11permissions18fromRawPermissionsShyACGSgShySSG_tFZTf4nd_n', symObjAddr: 0xA30, symBinAddr: 0x19750, symSize: 0x3E0 }
  - { offsetInCU: 0x520, offset: 0x1249E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionCfETo', symObjAddr: 0xA10, symBinAddr: 0x19730, symSize: 0x20 }
  - { offsetInCU: 0x56B, offset: 0x124E9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionCMa', symObjAddr: 0xEC0, symBinAddr: 0x19B30, symSize: 0x20 }
  - { offsetInCU: 0x57E, offset: 0x124FC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionCSo8NSObjectCSH10ObjectiveCWl', symObjAddr: 0xF10, symBinAddr: 0x19B80, symSize: 0x30 }
  - { offsetInCU: 0x591, offset: 0x1250F, size: 0x8, addend: 0x0, symName: '_$sSh5IndexV8_VariantOyx__GSHRzlWOe', symObjAddr: 0xF40, symBinAddr: 0x19BB0, symSize: 0x20 }
  - { offsetInCU: 0x5E3, offset: 0x12561, size: 0x8, addend: 0x0, symName: '_$sSlsE3mapySayqd__Gqd__7ElementQzKXEKlFShy13FBSDKLoginKit12FBPermissionCG_SSTg504$s13c5Kit12E93C14rawPermissions4fromShySSGShyACG_tFZSSACcfu_32e0d58b938ad0b6cb17de1b825049cc00ACSSTf3nnpk_nTf1cn_n', symObjAddr: 0x0, symBinAddr: 0x18D50, symSize: 0x340 }
  - { offsetInCU: 0x994, offset: 0x12912, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionC14rawPermissions4fromShySSGShyACG_tFZ', symObjAddr: 0x720, symBinAddr: 0x19440, symSize: 0x40 }
  - { offsetInCU: 0xA17, offset: 0x12995, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionC11descriptionSSvg', symObjAddr: 0x3B0, symBinAddr: 0x19100, symSize: 0x30 }
  - { offsetInCU: 0xA4F, offset: 0x129CD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionC4hashSivg', symObjAddr: 0x420, symBinAddr: 0x19170, symSize: 0x40 }
  - { offsetInCU: 0xA6C, offset: 0x129EA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionC6stringACSgSS_tcfC', symObjAddr: 0x490, symBinAddr: 0x191B0, symSize: 0x40 }
  - { offsetInCU: 0xA85, offset: 0x12A03, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionC6stringACSgSS_tcfc', symObjAddr: 0x4D0, symBinAddr: 0x191F0, symSize: 0x180 }
  - { offsetInCU: 0xACE, offset: 0x12A4C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionC11permissions18fromRawPermissionsShyACGSgShySSG_tFZ', symObjAddr: 0x680, symBinAddr: 0x193A0, symSize: 0x10 }
  - { offsetInCU: 0xB1A, offset: 0x12A98, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionC7isEqualySbypSgF', symObjAddr: 0x7F0, symBinAddr: 0x19510, symSize: 0xE0 }
  - { offsetInCU: 0xB62, offset: 0x12AE0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionCACycfC', symObjAddr: 0x960, symBinAddr: 0x19680, symSize: 0x20 }
  - { offsetInCU: 0xB75, offset: 0x12AF3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionCACycfc', symObjAddr: 0x980, symBinAddr: 0x196A0, symSize: 0x30 }
  - { offsetInCU: 0xBC8, offset: 0x12B46, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionCfD', symObjAddr: 0x9E0, symBinAddr: 0x19700, symSize: 0x30 }
  - { offsetInCU: 0x4D, offset: 0x12C45, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO13kNUXRectInset12CoreGraphics7CGFloatVvpZ', symObjAddr: 0x6D20, symBinAddr: 0x5FD80, symSize: 0x0 }
  - { offsetInCU: 0x67, offset: 0x12C5F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO16kNUXBubbleMargin12CoreGraphics7CGFloatVvpZ', symObjAddr: 0x6D28, symBinAddr: 0x5FD88, symSize: 0x0 }
  - { offsetInCU: 0x81, offset: 0x12C79, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO15kNUXPointMargin12CoreGraphics7CGFloatVvpZ', symObjAddr: 0x6D30, symBinAddr: 0x5FD90, symSize: 0x0 }
  - { offsetInCU: 0x9B, offset: 0x12C93, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO16kNUXCornerRadius12CoreGraphics7CGFloatVvpZ', symObjAddr: 0x6D38, symBinAddr: 0x5FD98, symSize: 0x0 }
  - { offsetInCU: 0xB5, offset: 0x12CAD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO11kNUXSideCap12CoreGraphics7CGFloatVvpZ', symObjAddr: 0x6D40, symBinAddr: 0x5FDA0, symSize: 0x0 }
  - { offsetInCU: 0xCF, offset: 0x12CC7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO12kNUXFontSize12CoreGraphics7CGFloatVvpZ', symObjAddr: 0x6D48, symBinAddr: 0x5FDA8, symSize: 0x0 }
  - { offsetInCU: 0xE9, offset: 0x12CE1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO18kNUXCrossGlyphSize12CoreGraphics7CGFloatVvpZ', symObjAddr: 0x6D50, symBinAddr: 0x5FDB0, symSize: 0x0 }
  - { offsetInCU: 0x103, offset: 0x12CFB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO27kFriendlyBlueGradientColorsSaySo10CGColorRefaGvpZ', symObjAddr: 0x6D58, symBinAddr: 0x5FDB8, symSize: 0x0 }
  - { offsetInCU: 0x11D, offset: 0x12D15, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO12kNeutralGraySaySo10CGColorRefaGvpZ', symObjAddr: 0x6D60, symBinAddr: 0x5FDC0, symSize: 0x0 }
  - { offsetInCU: 0x12B, offset: 0x12D23, size: 0x8, addend: 0x0, symName: '_$sIeg_IeyB_TR', symObjAddr: 0x0, symBinAddr: 0x19BE0, symSize: 0x30 }
  - { offsetInCU: 0x1A7, offset: 0x12D9F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC10ColorStyleOSYAASY8rawValuexSg03RawH0Qz_tcfCTW', symObjAddr: 0x2990, symBinAddr: 0x1C570, symSize: 0x30 }
  - { offsetInCU: 0x1D6, offset: 0x12DCE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC10ColorStyleOSYAASY8rawValue03RawH0QzvgTW', symObjAddr: 0x29C0, symBinAddr: 0x1C5A0, symSize: 0x10 }
  - { offsetInCU: 0x67B, offset: 0x13273, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO13kNUXRectInset_WZ', symObjAddr: 0x29D0, symBinAddr: 0x1C5B0, symSize: 0x20 }
  - { offsetInCU: 0x694, offset: 0x1328C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO16kNUXBubbleMargin_WZ', symObjAddr: 0x29F0, symBinAddr: 0x1C5D0, symSize: 0x40 }
  - { offsetInCU: 0x6BC, offset: 0x132B4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO15kNUXPointMargin_WZ', symObjAddr: 0x2A30, symBinAddr: 0x1C610, symSize: 0x20 }
  - { offsetInCU: 0x6D5, offset: 0x132CD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO16kNUXCornerRadius_WZ', symObjAddr: 0x2A50, symBinAddr: 0x1C630, symSize: 0x20 }
  - { offsetInCU: 0x6EE, offset: 0x132E6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO11kNUXSideCap_WZ', symObjAddr: 0x2A70, symBinAddr: 0x1C650, symSize: 0x20 }
  - { offsetInCU: 0x707, offset: 0x132FF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO12kNUXFontSize_WZ', symObjAddr: 0x2A90, symBinAddr: 0x1C670, symSize: 0x20 }
  - { offsetInCU: 0x720, offset: 0x13318, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO18kNUXCrossGlyphSize_WZ', symObjAddr: 0x2AB0, symBinAddr: 0x1C690, symSize: 0x20 }
  - { offsetInCU: 0x739, offset: 0x13331, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO27kFriendlyBlueGradientColors_WZ', symObjAddr: 0x2AD0, symBinAddr: 0x1C6B0, symSize: 0x120 }
  - { offsetInCU: 0x7F4, offset: 0x133EC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO12kNeutralGray_WZ', symObjAddr: 0x2BF0, symBinAddr: 0x1C7D0, symSize: 0x120 }
  - { offsetInCU: 0x8AF, offset: 0x134A7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC15displayDurationSdvgTo', symObjAddr: 0x2D10, symBinAddr: 0x1C8F0, symSize: 0x40 }
  - { offsetInCU: 0x8E7, offset: 0x134DF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC15displayDurationSdvsTo', symObjAddr: 0x2D50, symBinAddr: 0x1C930, symSize: 0x50 }
  - { offsetInCU: 0x92A, offset: 0x13522, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC10colorStyleAC05ColorF0OvgTo', symObjAddr: 0x2DA0, symBinAddr: 0x1C980, symSize: 0x40 }
  - { offsetInCU: 0x962, offset: 0x1355A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC10colorStyleAC05ColorF0OvsTo', symObjAddr: 0x2DE0, symBinAddr: 0x1C9C0, symSize: 0x60 }
  - { offsetInCU: 0x9B4, offset: 0x135AC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9textLabelSo7UILabelCvpfiAFyXEfU_', symObjAddr: 0x2F90, symBinAddr: 0x1CB70, symSize: 0x140 }
  - { offsetInCU: 0xA2E, offset: 0x13626, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9textLabelSo7UILabelCvgTo', symObjAddr: 0x3110, symBinAddr: 0x1CCF0, symSize: 0x20 }
  - { offsetInCU: 0xA92, offset: 0x1368A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewCACycfcTo', symObjAddr: 0x3190, symBinAddr: 0x1CD70, symSize: 0x20 }
  - { offsetInCU: 0xAEF, offset: 0x136E7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7tagline7message10colorStyleACSSSg_AgC05ColorH0OtcfcTo', symObjAddr: 0x3200, symBinAddr: 0x1CDE0, symSize: 0x70 }
  - { offsetInCU: 0xB3C, offset: 0x13734, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC5coderACSgSo7NSCoderC_tcfcTo', symObjAddr: 0x32C0, symBinAddr: 0x1CEA0, symSize: 0x20 }
  - { offsetInCU: 0xB81, offset: 0x13779, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewCfDTo', symObjAddr: 0x3330, symBinAddr: 0x1CF10, symSize: 0x70 }
  - { offsetInCU: 0xBB1, offset: 0x137A9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7present4fromySo6UIViewC_tFTo', symObjAddr: 0x3430, symBinAddr: 0x1D010, symSize: 0x50 }
  - { offsetInCU: 0xBCC, offset: 0x137C4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7present2in13arrowPosition9directionySo6UIViewC_So7CGPointVAC14ArrowDirectionOtFTo', symObjAddr: 0x3480, symBinAddr: 0x1D060, symSize: 0x80 }
  - { offsetInCU: 0xBE7, offset: 0x137DF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7dismissyyFTo', symObjAddr: 0x35D0, symBinAddr: 0x1D1B0, symSize: 0x90 }
  - { offsetInCU: 0xC66, offset: 0x1385E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC13animateFadeInyyFTo', symObjAddr: 0x4320, symBinAddr: 0x1DF00, symSize: 0x30 }
  - { offsetInCU: 0xC9D, offset: 0x13895, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC14onTapInTooltip33_1C39B2F52DDA14663AEF238AF411735ALLyySo19UIGestureRecognizerCFTo', symObjAddr: 0x4350, symBinAddr: 0x1DF30, symSize: 0x70 }
  - { offsetInCU: 0xD19, offset: 0x13911, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC4drawyySo6CGRectVFTo', symObjAddr: 0x43D0, symBinAddr: 0x1DFA0, symSize: 0x30 }
  - { offsetInCU: 0xD62, offset: 0x1395A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC14layoutSubviewsyyFTo', symObjAddr: 0x4430, symBinAddr: 0x1DFD0, symSize: 0x60 }
  - { offsetInCU: 0xD93, offset: 0x1398B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC47scheduleFadeoutRespectingMinimumDisplayDuration33_1C39B2F52DDA14663AEF238AF411735ALLyyFTo', symObjAddr: 0x4490, symBinAddr: 0x1E030, symSize: 0x80 }
  - { offsetInCU: 0xE27, offset: 0x13A1F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC5frameACSo6CGRectV_tcfcTo', symObjAddr: 0x45C0, symBinAddr: 0x1E160, symSize: 0x30 }
  - { offsetInCU: 0xE89, offset: 0x13A81, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC35fbsdkCreateUpPointingBubbleWithRect33_1C39B2F52DDA14663AEF238AF411735ALLySo16CGMutablePathRefaSo6CGRectV_12CoreGraphics7CGFloatVA2LtFTf4nnnnd_n', symObjAddr: 0x4690, symBinAddr: 0x1E230, symSize: 0x640 }
  - { offsetInCU: 0x10CD, offset: 0x13CC5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC37fbsdkCreateDownPointingBubbleWithRect33_1C39B2F52DDA14663AEF238AF411735ALLySo16CGMutablePathRefaSo6CGRectV_12CoreGraphics7CGFloatVA2LtFTf4nnnnd_n', symObjAddr: 0x4CD0, symBinAddr: 0x1E870, symSize: 0x640 }
  - { offsetInCU: 0x1311, offset: 0x13F09, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC29createCloseCrossGlyphWithRect33_1C39B2F52DDA14663AEF238AF411735ALLySo16CGMutablePathRefaSo6CGRectVFTf4nd_n', symObjAddr: 0x5310, symBinAddr: 0x1EEB0, symSize: 0x600 }
  - { offsetInCU: 0x1678, offset: 0x14270, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewCMa', symObjAddr: 0x30D0, symBinAddr: 0x1CCB0, symSize: 0x20 }
  - { offsetInCU: 0x168B, offset: 0x14283, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewCfETo', symObjAddr: 0x33A0, symBinAddr: 0x1CF80, symSize: 0x90 }
  - { offsetInCU: 0x16B8, offset: 0x142B0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7dismissyyFyycfU_TA', symObjAddr: 0x35C0, symBinAddr: 0x1D1A0, symSize: 0x10 }
  - { offsetInCU: 0x16CB, offset: 0x142C3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC13animateFadeInyyFyycfU_TA', symObjAddr: 0x3AD0, symBinAddr: 0x1D6B0, symSize: 0x20 }
  - { offsetInCU: 0x16DE, offset: 0x142D6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC13animateFadeInyyFyycfU0_TA', symObjAddr: 0x3F60, symBinAddr: 0x1DB40, symSize: 0x20 }
  - { offsetInCU: 0x16F1, offset: 0x142E9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC13animateFadeInyyFyycfU1_TA', symObjAddr: 0x4060, symBinAddr: 0x1DC40, symSize: 0x10 }
  - { offsetInCU: 0x1704, offset: 0x142FC, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x4070, symBinAddr: 0x1DC50, symSize: 0x20 }
  - { offsetInCU: 0x1717, offset: 0x1430F, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x4090, symBinAddr: 0x1DC70, symSize: 0x10 }
  - { offsetInCU: 0x172A, offset: 0x14322, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC13animateFadeInyyFySbcfU2_TA', symObjAddr: 0x4210, symBinAddr: 0x1DDF0, symSize: 0x20 }
  - { offsetInCU: 0x173D, offset: 0x14335, size: 0x8, addend: 0x0, symName: '_$sSbIegy_SbIeyBy_TR', symObjAddr: 0x42E0, symBinAddr: 0x1DEC0, symSize: 0x40 }
  - { offsetInCU: 0x177C, offset: 0x14374, size: 0x8, addend: 0x0, symName: '_$ss29getContiguousArrayStorageType3fors01_bcD0CyxGmxm_tlF13FBSDKLoginKit18DeviceLoginManagerC_Tgm5', symObjAddr: 0x45F0, symBinAddr: 0x1E190, symSize: 0x50 }
  - { offsetInCU: 0x17A7, offset: 0x1439F, size: 0x8, addend: 0x0, symName: '_$ss29getContiguousArrayStorageType3fors01_bcD0CyxGmxm_tlFSo10CGColorRefa_Tgm5', symObjAddr: 0x4640, symBinAddr: 0x1E1E0, symSize: 0x50 }
  - { offsetInCU: 0x17D2, offset: 0x143CA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC14ArrowDirectionOSHAASQWb', symObjAddr: 0x6420, symBinAddr: 0x1FFC0, symSize: 0x10 }
  - { offsetInCU: 0x17E5, offset: 0x143DD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC14ArrowDirectionOAESQAAWl', symObjAddr: 0x6430, symBinAddr: 0x1FFD0, symSize: 0x30 }
  - { offsetInCU: 0x17F8, offset: 0x143F0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC10ColorStyleOSHAASQWb', symObjAddr: 0x6460, symBinAddr: 0x20000, symSize: 0x10 }
  - { offsetInCU: 0x180B, offset: 0x14403, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC10ColorStyleOAESQAAWl', symObjAddr: 0x6470, symBinAddr: 0x20010, symSize: 0x30 }
  - { offsetInCU: 0x181E, offset: 0x14416, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC14ArrowDirectionOMa', symObjAddr: 0x6890, symBinAddr: 0x20430, symSize: 0x10 }
  - { offsetInCU: 0x1831, offset: 0x14429, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC10ColorStyleOMa', symObjAddr: 0x68A0, symBinAddr: 0x20440, symSize: 0x10 }
  - { offsetInCU: 0x1844, offset: 0x1443C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7dismissyyFyycfU_TA.23', symObjAddr: 0x68B0, symBinAddr: 0x20450, symSize: 0x10 }
  - { offsetInCU: 0x1857, offset: 0x1444F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC13animateFadeInyyFySbcfU2_ySbcfU_TA', symObjAddr: 0x68E0, symBinAddr: 0x20480, symSize: 0x20 }
  - { offsetInCU: 0x186A, offset: 0x14462, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC14animateFadeOut33_1C39B2F52DDA14663AEF238AF411735ALL17completionHandleryyyc_tFyycfU_TA', symObjAddr: 0x6930, symBinAddr: 0x204D0, symSize: 0x20 }
  - { offsetInCU: 0x189A, offset: 0x14492, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC14animateFadeOut33_1C39B2F52DDA14663AEF238AF411735ALL17completionHandleryyyc_tFySbcfU0_TA', symObjAddr: 0x6950, symBinAddr: 0x204F0, symSize: 0x20 }
  - { offsetInCU: 0x19CA, offset: 0x145C2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC10ColorStyleOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x28C0, symBinAddr: 0x1C4A0, symSize: 0x10 }
  - { offsetInCU: 0x1A25, offset: 0x1461D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC10ColorStyleOSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0x2920, symBinAddr: 0x1C500, symSize: 0x20 }
  - { offsetInCU: 0x1DB7, offset: 0x149AF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7tagline7message10colorStyleACSSSg_AgC05ColorH0Otcfc', symObjAddr: 0x30, symBinAddr: 0x19C10, symSize: 0x570 }
  - { offsetInCU: 0x1F03, offset: 0x14AFB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7present2in13arrowPosition9directionySo6UIViewC_So7CGPointVAC14ArrowDirectionOtF', symObjAddr: 0x5A0, symBinAddr: 0x1A180, symSize: 0x120 }
  - { offsetInCU: 0x2002, offset: 0x14BFA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC15displayDurationSdvg', symObjAddr: 0x6C0, symBinAddr: 0x1A2A0, symSize: 0x30 }
  - { offsetInCU: 0x201D, offset: 0x14C15, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC15displayDurationSdvs', symObjAddr: 0x6F0, symBinAddr: 0x1A2D0, symSize: 0x40 }
  - { offsetInCU: 0x2041, offset: 0x14C39, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC15displayDurationSdvM', symObjAddr: 0x730, symBinAddr: 0x1A310, symSize: 0x40 }
  - { offsetInCU: 0x2064, offset: 0x14C5C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC15displayDurationSdvM.resume.0', symObjAddr: 0x770, symBinAddr: 0x1A350, symSize: 0x10 }
  - { offsetInCU: 0x2083, offset: 0x14C7B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC10colorStyleAC05ColorF0Ovg', symObjAddr: 0x780, symBinAddr: 0x1A360, symSize: 0x30 }
  - { offsetInCU: 0x20B0, offset: 0x14CA8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC10colorStyleAC05ColorF0Ovs', symObjAddr: 0x7B0, symBinAddr: 0x1A390, symSize: 0x40 }
  - { offsetInCU: 0x20EF, offset: 0x14CE7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC10colorStyleAC05ColorF0OvM', symObjAddr: 0x7F0, symBinAddr: 0x1A3D0, symSize: 0x40 }
  - { offsetInCU: 0x2112, offset: 0x14D0A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC10colorStyleAC05ColorF0OvM.resume.0', symObjAddr: 0x830, symBinAddr: 0x1A410, symSize: 0x30 }
  - { offsetInCU: 0x217C, offset: 0x14D74, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7messageSSSgvs', symObjAddr: 0x880, symBinAddr: 0x1A460, symSize: 0xC0 }
  - { offsetInCU: 0x21EC, offset: 0x14DE4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7messageSSSgvM', symObjAddr: 0x940, symBinAddr: 0x1A520, symSize: 0x80 }
  - { offsetInCU: 0x222A, offset: 0x14E22, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7messageSSSgvM.resume.0', symObjAddr: 0x9C0, symBinAddr: 0x1A5A0, symSize: 0x170 }
  - { offsetInCU: 0x232E, offset: 0x14F26, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7taglineSSSgvs', symObjAddr: 0xB90, symBinAddr: 0x1A770, symSize: 0xC0 }
  - { offsetInCU: 0x239E, offset: 0x14F96, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7taglineSSSgvM', symObjAddr: 0xC50, symBinAddr: 0x1A830, symSize: 0x80 }
  - { offsetInCU: 0x23DC, offset: 0x14FD4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7taglineSSSgvM.resume.0', symObjAddr: 0xCD0, symBinAddr: 0x1A8B0, symSize: 0x170 }
  - { offsetInCU: 0x25C4, offset: 0x151BC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7present4fromySo6UIViewC_tF', symObjAddr: 0xE40, symBinAddr: 0x1AA20, symSize: 0x2F0 }
  - { offsetInCU: 0x26FD, offset: 0x152F5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7dismissyyF', symObjAddr: 0x1130, symBinAddr: 0x1AD10, symSize: 0x70 }
  - { offsetInCU: 0x2763, offset: 0x1535B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7dismissyyFyycfU_', symObjAddr: 0x3500, symBinAddr: 0x1D0E0, symSize: 0xC0 }
  - { offsetInCU: 0x27EC, offset: 0x153E4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC13animateFadeInyyF', symObjAddr: 0x11A0, symBinAddr: 0x1AD80, symSize: 0x6E0 }
  - { offsetInCU: 0x2939, offset: 0x15531, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC13animateFadeInyyFyycfU_', symObjAddr: 0x3660, symBinAddr: 0x1D240, symSize: 0x430 }
  - { offsetInCU: 0x2A34, offset: 0x1562C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC13animateFadeInyyFyycfU0_', symObjAddr: 0x3AF0, symBinAddr: 0x1D6D0, symSize: 0x430 }
  - { offsetInCU: 0x2B1E, offset: 0x15716, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC13animateFadeInyyFyycfU1_', symObjAddr: 0x3F80, symBinAddr: 0x1DB60, symSize: 0xE0 }
  - { offsetInCU: 0x2B4B, offset: 0x15743, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC13animateFadeInyyFySbcfU2_', symObjAddr: 0x40A0, symBinAddr: 0x1DC80, symSize: 0x140 }
  - { offsetInCU: 0x2B9A, offset: 0x15792, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC13animateFadeInyyFySbcfU2_ySbcfU_', symObjAddr: 0x4230, symBinAddr: 0x1DE10, symSize: 0xB0 }
  - { offsetInCU: 0x2BE4, offset: 0x157DC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC14animateFadeOut33_1C39B2F52DDA14663AEF238AF411735ALL17completionHandleryyyc_tF', symObjAddr: 0x1880, symBinAddr: 0x1B460, symSize: 0x150 }
  - { offsetInCU: 0x2CBA, offset: 0x158B2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC12updateColors33_1C39B2F52DDA14663AEF238AF411735ALLyyF', symObjAddr: 0x19D0, symBinAddr: 0x1B5B0, symSize: 0x260 }
  - { offsetInCU: 0x2EBC, offset: 0x15AB4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC31layoutSubviewsAndDetermineFrame33_1C39B2F52DDA14663AEF238AF411735ALLSo6CGRectVyF', symObjAddr: 0x1C30, symBinAddr: 0x1B810, symSize: 0x6F0 }
  - { offsetInCU: 0x31DB, offset: 0x15DD3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC3set33_1C39B2F52DDA14663AEF238AF411735ALL7message7taglineySSSg_AHtF', symObjAddr: 0x2320, symBinAddr: 0x1BF00, symSize: 0x3B0 }
  - { offsetInCU: 0x33DF, offset: 0x15FD7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC24scheduleAutomaticFadeout33_1C39B2F52DDA14663AEF238AF411735ALLyyF', symObjAddr: 0x26D0, symBinAddr: 0x1C2B0, symSize: 0x130 }
  - { offsetInCU: 0x34A2, offset: 0x1609A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC32cancelAllScheduledFadeOutMethods33_1C39B2F52DDA14663AEF238AF411735ALLyyF', symObjAddr: 0x2800, symBinAddr: 0x1C3E0, symSize: 0x60 }
  - { offsetInCU: 0x34C5, offset: 0x160BD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC14ArrowDirectionO8rawValueSuvg', symObjAddr: 0x2860, symBinAddr: 0x1C440, symSize: 0x10 }
  - { offsetInCU: 0x34EA, offset: 0x160E2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC10ColorStyleO8rawValueAESgSu_tcfC', symObjAddr: 0x2890, symBinAddr: 0x1C470, symSize: 0x20 }
  - { offsetInCU: 0x3505, offset: 0x160FD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC10ColorStyleO8rawValueSuvg', symObjAddr: 0x28B0, symBinAddr: 0x1C490, symSize: 0x10 }
  - { offsetInCU: 0x35E3, offset: 0x161DB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9textLabelSo7UILabelCvg', symObjAddr: 0x3130, symBinAddr: 0x1CD10, symSize: 0x20 }
  - { offsetInCU: 0x3604, offset: 0x161FC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewCACycfC', symObjAddr: 0x3150, symBinAddr: 0x1CD30, symSize: 0x20 }
  - { offsetInCU: 0x3617, offset: 0x1620F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewCACycfc', symObjAddr: 0x3170, symBinAddr: 0x1CD50, symSize: 0x20 }
  - { offsetInCU: 0x3656, offset: 0x1624E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7tagline7message10colorStyleACSSSg_AgC05ColorH0OtcfC', symObjAddr: 0x31B0, symBinAddr: 0x1CD90, symSize: 0x50 }
  - { offsetInCU: 0x3669, offset: 0x16261, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC5coderACSgSo7NSCoderC_tcfC', symObjAddr: 0x3270, symBinAddr: 0x1CE50, symSize: 0x40 }
  - { offsetInCU: 0x367C, offset: 0x16274, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC5coderACSgSo7NSCoderC_tcfc', symObjAddr: 0x32B0, symBinAddr: 0x1CE90, symSize: 0x10 }
  - { offsetInCU: 0x3695, offset: 0x1628D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewCfD', symObjAddr: 0x32E0, symBinAddr: 0x1CEC0, symSize: 0x50 }
  - { offsetInCU: 0x3716, offset: 0x1630E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC5frameACSo6CGRectV_tcfC', symObjAddr: 0x4510, symBinAddr: 0x1E0B0, symSize: 0x80 }
  - { offsetInCU: 0x3729, offset: 0x16321, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC5frameACSo6CGRectV_tcfc', symObjAddr: 0x4590, symBinAddr: 0x1E130, symSize: 0x30 }
  - { offsetInCU: 0x37A1, offset: 0x16399, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC5coderACSgSo7NSCoderC_tcfcTf4dn_n', symObjAddr: 0x5910, symBinAddr: 0x1F4B0, symSize: 0x200 }
  - { offsetInCU: 0x3816, offset: 0x1640E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC4drawyySo6CGRectVFTf4dn_n', symObjAddr: 0x5B10, symBinAddr: 0x1F6B0, symSize: 0x910 }
  - { offsetInCU: 0x2B, offset: 0x169B1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVyACyAA0d7ManagerdE0CSg_s5Error_pSgtccfC', symObjAddr: 0x0, symBinAddr: 0x20640, symSize: 0x40 }
  - { offsetInCU: 0xA4, offset: 0x16A2A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0xC0, symBinAddr: 0x20700, symSize: 0x10 }
  - { offsetInCU: 0xE6, offset: 0x16A6C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVMa', symObjAddr: 0x40, symBinAddr: 0x20680, symSize: 0x30 }
  - { offsetInCU: 0xF9, offset: 0x16A7F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVwCP', symObjAddr: 0xD0, symBinAddr: 0x20710, symSize: 0x80 }
  - { offsetInCU: 0x10C, offset: 0x16A92, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVwxx', symObjAddr: 0x150, symBinAddr: 0x20790, symSize: 0x40 }
  - { offsetInCU: 0x11F, offset: 0x16AA5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVwcp', symObjAddr: 0x190, symBinAddr: 0x207D0, symSize: 0x60 }
  - { offsetInCU: 0x132, offset: 0x16AB8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVwca', symObjAddr: 0x1F0, symBinAddr: 0x20830, symSize: 0x60 }
  - { offsetInCU: 0x145, offset: 0x16ACB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVwtk', symObjAddr: 0x250, symBinAddr: 0x20890, symSize: 0x50 }
  - { offsetInCU: 0x158, offset: 0x16ADE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVwta', symObjAddr: 0x2A0, symBinAddr: 0x208E0, symSize: 0x50 }
  - { offsetInCU: 0x16B, offset: 0x16AF1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVwet', symObjAddr: 0x2F0, symBinAddr: 0x20930, symSize: 0x20 }
  - { offsetInCU: 0x17E, offset: 0x16B04, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVwst', symObjAddr: 0x370, symBinAddr: 0x209B0, symSize: 0x20 }
  - { offsetInCU: 0x191, offset: 0x16B17, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVMr', symObjAddr: 0x3F0, symBinAddr: 0x20A30, symSize: 0x64 }
  - { offsetInCU: 0x28F, offset: 0x16C15, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVyACyAA0d7ManagerdE0CSg_s5Error_pSgtccfC', symObjAddr: 0x0, symBinAddr: 0x20640, symSize: 0x40 }
  - { offsetInCU: 0x2B8, offset: 0x16C3E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerV14callAsFunctionyyAA0d7ManagerdE0CSg_s5Error_pSgtF', symObjAddr: 0x70, symBinAddr: 0x206B0, symSize: 0x40 }
  - { offsetInCU: 0x2F9, offset: 0x16C7F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerV2eeoiySbAC_ACtFZ', symObjAddr: 0xB0, symBinAddr: 0x206F0, symSize: 0x10 }
  - { offsetInCU: 0x27, offset: 0x16D12, size: 0x8, addend: 0x0, symName: '_$sSo20FBSDKInternalUtilityC13FBSDKLoginKit29UserInterfaceElementProvidingA2cDP21topMostViewControllerSo06UIViewL0CSgyFTW', symObjAddr: 0x0, symBinAddr: 0x20AA0, symSize: 0x20 }
  - { offsetInCU: 0x7E, offset: 0x16D69, size: 0x8, addend: 0x0, symName: '_$sSo20FBSDKInternalUtilityC13FBSDKLoginKit29UserInterfaceElementProvidingA2cDP21topMostViewControllerSo06UIViewL0CSgyFTW', symObjAddr: 0x0, symBinAddr: 0x20AA0, symSize: 0x20 }
  - { offsetInCU: 0xC5, offset: 0x16DB0, size: 0x8, addend: 0x0, symName: '_$sSo20FBSDKInternalUtilityC13FBSDKLoginKit29UserInterfaceElementProvidingA2cDP14viewController3forSo06UIViewJ0CSgSo0L0C_tFTW', symObjAddr: 0x20, symBinAddr: 0x20AC0, symSize: 0x30 }
  - { offsetInCU: 0x107, offset: 0x16DF2, size: 0x8, addend: 0x0, symName: '_$sSo20FBSDKInternalUtilityC13FBSDKLoginKit28UserInterfaceStringProvidingA2cDP16bundleForStringsSo8NSBundleCvgTW', symObjAddr: 0x50, symBinAddr: 0x20AF0, symSize: 0x1E }
  - { offsetInCU: 0x49, offset: 0x16F12, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE17loginButtonDidTapABvpZ', symObjAddr: 0x28D0, symBinAddr: 0x621A0, symSize: 0x0 }
  - { offsetInCU: 0x63, offset: 0x16F2C, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE17smartLoginServiceABvpZ', symObjAddr: 0x28D8, symBinAddr: 0x621A8, symSize: 0x0 }
  - { offsetInCU: 0x7D, offset: 0x16F46, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE16sessionAuthStartABvpZ', symObjAddr: 0x28E0, symBinAddr: 0x621B0, symSize: 0x0 }
  - { offsetInCU: 0x97, offset: 0x16F60, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE14sessionAuthEndABvpZ', symObjAddr: 0x28E8, symBinAddr: 0x621B8, symSize: 0x0 }
  - { offsetInCU: 0xB1, offset: 0x16F7A, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE22sessionAuthMethodStartABvpZ', symObjAddr: 0x28F0, symBinAddr: 0x621C0, symSize: 0x0 }
  - { offsetInCU: 0xCB, offset: 0x16F94, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE20sessionAuthMethodEndABvpZ', symObjAddr: 0x28F8, symBinAddr: 0x621C8, symSize: 0x0 }
  - { offsetInCU: 0xE5, offset: 0x16FAE, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE20sessionAuthHeartbeatABvpZ', symObjAddr: 0x2900, symBinAddr: 0x621D0, symSize: 0x0 }
  - { offsetInCU: 0xF3, offset: 0x16FBC, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE17loginButtonDidTap_WZ', symObjAddr: 0x0, symBinAddr: 0x20B10, symSize: 0x30 }
  - { offsetInCU: 0x10C, offset: 0x16FD5, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE17loginButtonDidTapABvau', symObjAddr: 0x30, symBinAddr: 0x20B40, symSize: 0x30 }
  - { offsetInCU: 0x12A, offset: 0x16FF3, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE17smartLoginService_WZ', symObjAddr: 0x80, symBinAddr: 0x20B90, symSize: 0x30 }
  - { offsetInCU: 0x143, offset: 0x1700C, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE17smartLoginServiceABvau', symObjAddr: 0xB0, symBinAddr: 0x20BC0, symSize: 0x30 }
  - { offsetInCU: 0x161, offset: 0x1702A, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE16sessionAuthStart_WZ', symObjAddr: 0x100, symBinAddr: 0x20C10, symSize: 0x30 }
  - { offsetInCU: 0x17A, offset: 0x17043, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE16sessionAuthStartABvau', symObjAddr: 0x130, symBinAddr: 0x20C40, symSize: 0x30 }
  - { offsetInCU: 0x198, offset: 0x17061, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE14sessionAuthEnd_WZ', symObjAddr: 0x180, symBinAddr: 0x20C90, symSize: 0x30 }
  - { offsetInCU: 0x1B1, offset: 0x1707A, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE14sessionAuthEndABvau', symObjAddr: 0x1B0, symBinAddr: 0x20CC0, symSize: 0x30 }
  - { offsetInCU: 0x1CF, offset: 0x17098, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE22sessionAuthMethodStart_WZ', symObjAddr: 0x200, symBinAddr: 0x20D10, symSize: 0x30 }
  - { offsetInCU: 0x1E8, offset: 0x170B1, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE22sessionAuthMethodStartABvau', symObjAddr: 0x230, symBinAddr: 0x20D40, symSize: 0x30 }
  - { offsetInCU: 0x206, offset: 0x170CF, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE20sessionAuthMethodEnd_WZ', symObjAddr: 0x280, symBinAddr: 0x20D90, symSize: 0x30 }
  - { offsetInCU: 0x21F, offset: 0x170E8, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE20sessionAuthMethodEndABvau', symObjAddr: 0x2B0, symBinAddr: 0x20DC0, symSize: 0x30 }
  - { offsetInCU: 0x23D, offset: 0x17106, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE20sessionAuthHeartbeat_WZ', symObjAddr: 0x300, symBinAddr: 0x20E10, symSize: 0x30 }
  - { offsetInCU: 0x256, offset: 0x1711F, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE20sessionAuthHeartbeatABvau', symObjAddr: 0x330, symBinAddr: 0x20E40, symSize: 0x30 }
  - { offsetInCU: 0x27, offset: 0x17214, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit21LoginCompleterFactoryV06createcD013urlParameters5appIDAA0C10Completing_pSDySSypG_SStF', symObjAddr: 0x0, symBinAddr: 0x20EC0, symSize: 0x60 }
  - { offsetInCU: 0xA0, offset: 0x1728D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit21LoginCompleterFactoryVAA0cdE8ProtocolA2aDP06createcD013urlParameters5appIDAA0C10Completing_pSDySSypG_SStFTW', symObjAddr: 0x70, symBinAddr: 0x20F30, symSize: 0x60 }
  - { offsetInCU: 0xFC, offset: 0x172E9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit21LoginCompleterFactoryVMa', symObjAddr: 0xD0, symBinAddr: 0x20F90, symSize: 0xA }
  - { offsetInCU: 0x1D1, offset: 0x173BE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit21LoginCompleterFactoryV06createcD013urlParameters5appIDAA0C10Completing_pSDySSypG_SStF', symObjAddr: 0x0, symBinAddr: 0x20EC0, symSize: 0x60 }
  - { offsetInCU: 0x218, offset: 0x17405, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit21LoginCompleterFactoryVACycfC', symObjAddr: 0x60, symBinAddr: 0x20F20, symSize: 0x10 }
  - { offsetInCU: 0x8E, offset: 0x174E3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC5nonceSSvgTo', symObjAddr: 0x10, symBinAddr: 0x20FE0, symSize: 0x50 }
  - { offsetInCU: 0xDB, offset: 0x17530, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC8trackingAA0C8TrackingOvgTo', symObjAddr: 0x90, symBinAddr: 0x21060, symSize: 0x20 }
  - { offsetInCU: 0x128, offset: 0x1757D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC20requestedPermissionsShyAA12FBPermissionCGvgTo', symObjAddr: 0xD0, symBinAddr: 0x210A0, symSize: 0x60 }
  - { offsetInCU: 0x175, offset: 0x175CA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC15messengerPageIdSSSgvgTo', symObjAddr: 0x150, symBinAddr: 0x21120, symSize: 0x50 }
  - { offsetInCU: 0x1BA, offset: 0x1760F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC8authTypeSo0a4AuthF0aSgvgTo', symObjAddr: 0x1D0, symBinAddr: 0x211A0, symSize: 0x20 }
  - { offsetInCU: 0x209, offset: 0x1765E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC12codeVerifierAA04CodeF0CvgTo', symObjAddr: 0x220, symBinAddr: 0x211F0, symSize: 0x20 }
  - { offsetInCU: 0x295, offset: 0x176EA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking5nonce15messengerPageIdACSgSaySSG_AA0C8TrackingOS2SSgtcfcTo', symObjAddr: 0x410, symBinAddr: 0x213E0, symSize: 0x110 }
  - { offsetInCU: 0x347, offset: 0x1779C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking5nonce15messengerPageId8authTypeACSgSaySSG_AA0C8TrackingOS2SSgSo0a4AuthL0aSgtcfcTo', symObjAddr: 0x690, symBinAddr: 0x21660, symSize: 0xA0 }
  - { offsetInCU: 0x3AA, offset: 0x177FF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking5nonceACSgSaySSG_AA0C8TrackingOSStcfcTo', symObjAddr: 0x860, symBinAddr: 0x21830, symSize: 0xB0 }
  - { offsetInCU: 0x43C, offset: 0x17891, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking15messengerPageIdACSgSaySSG_AA0C8TrackingOSSSgtcfcTo', symObjAddr: 0xA70, symBinAddr: 0x21A40, symSize: 0x60 }
  - { offsetInCU: 0x4A9, offset: 0x178FE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking15messengerPageId8authTypeACSgSaySSG_AA0C8TrackingOSSSgSo0a4AuthK0aSgtcfcTo', symObjAddr: 0xC40, symBinAddr: 0x21C10, symSize: 0x80 }
  - { offsetInCU: 0x52A, offset: 0x1797F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking5nonce15messengerPageId8authType12codeVerifierACSgSaySSG_AA0C8TrackingOS2SSgSo0a4AuthL0aSgAA04CodeN0CtcfcTo', symObjAddr: 0x14B0, symBinAddr: 0x22480, symSize: 0xB0 }
  - { offsetInCU: 0x583, offset: 0x179D8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8trackingACSgSaySSG_AA0C8TrackingOtcfcTo', symObjAddr: 0x1720, symBinAddr: 0x226F0, symSize: 0xF0 }
  - { offsetInCU: 0x5FB, offset: 0x17A50, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC8trackingACSgAA0C8TrackingO_tcfcTo', symObjAddr: 0x18F0, symBinAddr: 0x228C0, symSize: 0x70 }
  - { offsetInCU: 0x65D, offset: 0x17AB2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationCACycfcTo', symObjAddr: 0x19B0, symBinAddr: 0x22980, symSize: 0x30 }
  - { offsetInCU: 0x6F5, offset: 0x17B4A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking5nonce15messengerPageId8authType12codeVerifierACSgShy09FBSDKCoreB010PermissionOG_AA0C8TrackingOS2SSgSo0a4AuthL0aSgAA04CodeN0CtcfCTf4nnnnnnd_n', symObjAddr: 0x1A80, symBinAddr: 0x22A50, symSize: 0x2A0 }
  - { offsetInCU: 0xBDE, offset: 0x18033, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationCfETo', symObjAddr: 0x1A10, symBinAddr: 0x229E0, symSize: 0x70 }
  - { offsetInCU: 0xD82, offset: 0x181D7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationCMa', symObjAddr: 0x1D90, symBinAddr: 0x22CF0, symSize: 0x20 }
  - { offsetInCU: 0x10B3, offset: 0x18508, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking5nonce15messengerPageId8authType12codeVerifierACSgShy09FBSDKCoreB010PermissionOG_AA0C8TrackingOS2SSgSo0a4AuthL0aSgAA04CodeN0CtcfC', symObjAddr: 0x0, symBinAddr: 0x20FD0, symSize: 0x10 }
  - { offsetInCU: 0x10DD, offset: 0x18532, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC5nonceSSvg', symObjAddr: 0x60, symBinAddr: 0x21030, symSize: 0x30 }
  - { offsetInCU: 0x110A, offset: 0x1855F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC8trackingAA0C8TrackingOvg', symObjAddr: 0xB0, symBinAddr: 0x21080, symSize: 0x20 }
  - { offsetInCU: 0x1137, offset: 0x1858C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC20requestedPermissionsShyAA12FBPermissionCGvg', symObjAddr: 0x130, symBinAddr: 0x21100, symSize: 0x20 }
  - { offsetInCU: 0x1164, offset: 0x185B9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC15messengerPageIdSSSgvg', symObjAddr: 0x1A0, symBinAddr: 0x21170, symSize: 0x30 }
  - { offsetInCU: 0x1191, offset: 0x185E6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC8authTypeSo0a4AuthF0aSgvg', symObjAddr: 0x1F0, symBinAddr: 0x211C0, symSize: 0x30 }
  - { offsetInCU: 0x11BE, offset: 0x18613, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC12codeVerifierAA04CodeF0Cvg', symObjAddr: 0x240, symBinAddr: 0x21210, symSize: 0x20 }
  - { offsetInCU: 0x122B, offset: 0x18680, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking5nonce15messengerPageIdACSgSaySSG_AA0C8TrackingOS2SSgtcfC', symObjAddr: 0x260, symBinAddr: 0x21230, symSize: 0xE0 }
  - { offsetInCU: 0x1270, offset: 0x186C5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking5nonce15messengerPageIdACSgSaySSG_AA0C8TrackingOS2SSgtcfc', symObjAddr: 0x340, symBinAddr: 0x21310, symSize: 0xD0 }
  - { offsetInCU: 0x12B1, offset: 0x18706, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking5nonce15messengerPageId8authTypeACSgSaySSG_AA0C8TrackingOS2SSgSo0a4AuthL0aSgtcfC', symObjAddr: 0x520, symBinAddr: 0x214F0, symSize: 0x60 }
  - { offsetInCU: 0x12CA, offset: 0x1871F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking5nonce15messengerPageId8authTypeACSgSaySSG_AA0C8TrackingOS2SSgSo0a4AuthL0aSgtcfc', symObjAddr: 0x580, symBinAddr: 0x21550, symSize: 0x110 }
  - { offsetInCU: 0x137F, offset: 0x187D4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking5nonceACSgSaySSG_AA0C8TrackingOSStcfC', symObjAddr: 0x730, symBinAddr: 0x21700, symSize: 0xA0 }
  - { offsetInCU: 0x13C2, offset: 0x18817, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking5nonceACSgSaySSG_AA0C8TrackingOSStcfc', symObjAddr: 0x7D0, symBinAddr: 0x217A0, symSize: 0x90 }
  - { offsetInCU: 0x13F9, offset: 0x1884E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking15messengerPageIdACSgSaySSG_AA0C8TrackingOSSSgtcfC', symObjAddr: 0x910, symBinAddr: 0x218E0, symSize: 0x50 }
  - { offsetInCU: 0x140C, offset: 0x18861, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking15messengerPageIdACSgSaySSG_AA0C8TrackingOSSSgtcfc', symObjAddr: 0x960, symBinAddr: 0x21930, symSize: 0x110 }
  - { offsetInCU: 0x145C, offset: 0x188B1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking15messengerPageId8authTypeACSgSaySSG_AA0C8TrackingOSSSgSo0a4AuthK0aSgtcfC', symObjAddr: 0xAD0, symBinAddr: 0x21AA0, symSize: 0x50 }
  - { offsetInCU: 0x146F, offset: 0x188C4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking15messengerPageId8authTypeACSgSaySSG_AA0C8TrackingOSSSgSo0a4AuthK0aSgtcfc', symObjAddr: 0xB20, symBinAddr: 0x21AF0, symSize: 0x120 }
  - { offsetInCU: 0x14CE, offset: 0x18923, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking5nonce15messengerPageId8authType12codeVerifierACSgSaySSG_AA0C8TrackingOS2SSgSo0a4AuthL0aSgAA04CodeN0CtcfC', symObjAddr: 0xCC0, symBinAddr: 0x21C90, symSize: 0x60 }
  - { offsetInCU: 0x1565, offset: 0x189BA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking5nonce15messengerPageId8authType12codeVerifierACSgSaySSG_AA0C8TrackingOS2SSgSo0a4AuthL0aSgAA04CodeN0Ctcfc', symObjAddr: 0xD20, symBinAddr: 0x21CF0, symSize: 0x570 }
  - { offsetInCU: 0x185A, offset: 0x18CAF, size: 0x8, addend: 0x0, symName: '_$sSTsSQ7ElementRpzrlE8containsySbABFSaySo18FBSDKLoginAuthTypeaG_Tg5', symObjAddr: 0x1290, symBinAddr: 0x22260, symSize: 0x170 }
  - { offsetInCU: 0x1A3D, offset: 0x18E92, size: 0x8, addend: 0x0, symName: '_$sSTsSQ7ElementRpzrlE8containsySbABFSaySSG_Tg5', symObjAddr: 0x1400, symBinAddr: 0x223D0, symSize: 0xB0 }
  - { offsetInCU: 0x1BDF, offset: 0x19034, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8trackingACSgSaySSG_AA0C8TrackingOtcfC', symObjAddr: 0x1560, symBinAddr: 0x22530, symSize: 0xE0 }
  - { offsetInCU: 0x1C1B, offset: 0x19070, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8trackingACSgSaySSG_AA0C8TrackingOtcfc', symObjAddr: 0x1640, symBinAddr: 0x22610, symSize: 0xE0 }
  - { offsetInCU: 0x1C67, offset: 0x190BC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC8trackingACSgAA0C8TrackingO_tcfC', symObjAddr: 0x1810, symBinAddr: 0x227E0, symSize: 0x70 }
  - { offsetInCU: 0x1C97, offset: 0x190EC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC8trackingACSgAA0C8TrackingO_tcfc', symObjAddr: 0x1880, symBinAddr: 0x22850, symSize: 0x70 }
  - { offsetInCU: 0x1CBC, offset: 0x19111, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationCACycfC', symObjAddr: 0x1960, symBinAddr: 0x22930, symSize: 0x20 }
  - { offsetInCU: 0x1CCF, offset: 0x19124, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationCACycfc', symObjAddr: 0x1980, symBinAddr: 0x22950, symSize: 0x30 }
  - { offsetInCU: 0x1D22, offset: 0x19177, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationCfD', symObjAddr: 0x19E0, symBinAddr: 0x229B0, symSize: 0x30 }
  - { offsetInCU: 0x211, offset: 0x1944B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV10Foundation13CustomNSErrorAadEP11errorDomainSSvgZTW', symObjAddr: 0x2B0, symBinAddr: 0x22FF0, symSize: 0x50 }
  - { offsetInCU: 0x246, offset: 0x19480, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV10Foundation13CustomNSErrorAadEP9errorCodeSivgTW', symObjAddr: 0x300, symBinAddr: 0x23040, symSize: 0x10 }
  - { offsetInCU: 0x276, offset: 0x194B0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV10Foundation13CustomNSErrorAadEP13errorUserInfoSDySSypGvgTW', symObjAddr: 0x310, symBinAddr: 0x23050, symSize: 0x10 }
  - { offsetInCU: 0x2A6, offset: 0x194E0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVSHAASH9hashValueSivgTW', symObjAddr: 0x320, symBinAddr: 0x23060, symSize: 0x40 }
  - { offsetInCU: 0x381, offset: 0x195BB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0x360, symBinAddr: 0x230A0, symSize: 0x20 }
  - { offsetInCU: 0x3EF, offset: 0x19629, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x420, symBinAddr: 0x23160, symSize: 0x30 }
  - { offsetInCU: 0x4A0, offset: 0x196DA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14LoginErrorCodeOSYAASY8rawValuexSg03RawG0Qz_tcfCTW', symObjAddr: 0x500, symBinAddr: 0x23240, symSize: 0x30 }
  - { offsetInCU: 0x4CF, offset: 0x19709, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14LoginErrorCodeOSYAASY8rawValue03RawG0QzvgTW', symObjAddr: 0x530, symBinAddr: 0x23270, symSize: 0x10 }
  - { offsetInCU: 0x4EB, offset: 0x19725, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14LoginErrorCodeO8rawValueACSgSi_tcfCTf4nd_n', symObjAddr: 0x550, symBinAddr: 0x23290, symSize: 0x20 }
  - { offsetInCU: 0x54C, offset: 0x19786, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV10Foundation13CustomNSErrorAAs0D0PWb', symObjAddr: 0x570, symBinAddr: 0x232B0, symSize: 0x10 }
  - { offsetInCU: 0x55F, offset: 0x19799, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVACs0D0AAWl', symObjAddr: 0x580, symBinAddr: 0x232C0, symSize: 0x30 }
  - { offsetInCU: 0x572, offset: 0x197AC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVSHAASQWb', symObjAddr: 0x5B0, symBinAddr: 0x232F0, symSize: 0x10 }
  - { offsetInCU: 0x585, offset: 0x197BF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVACSQAAWl', symObjAddr: 0x5C0, symBinAddr: 0x23300, symSize: 0x30 }
  - { offsetInCU: 0x598, offset: 0x197D2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14LoginErrorCodeOSHAASQWb', symObjAddr: 0x5F0, symBinAddr: 0x23330, symSize: 0x10 }
  - { offsetInCU: 0x5AB, offset: 0x197E5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14LoginErrorCodeOACSQAAWl', symObjAddr: 0x600, symBinAddr: 0x23340, symSize: 0x30 }
  - { offsetInCU: 0x5BE, offset: 0x197F8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVwxx', symObjAddr: 0x640, symBinAddr: 0x23380, symSize: 0x30 }
  - { offsetInCU: 0x5D1, offset: 0x1980B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVwca', symObjAddr: 0x6C0, symBinAddr: 0x23400, symSize: 0x60 }
  - { offsetInCU: 0x5E4, offset: 0x1981E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVwta', symObjAddr: 0x740, symBinAddr: 0x23460, symSize: 0x40 }
  - { offsetInCU: 0x5F7, offset: 0x19831, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVwet', symObjAddr: 0x780, symBinAddr: 0x234A0, symSize: 0x40 }
  - { offsetInCU: 0x60A, offset: 0x19844, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVwst', symObjAddr: 0x7C0, symBinAddr: 0x234E0, symSize: 0x40 }
  - { offsetInCU: 0x61D, offset: 0x19857, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVMa', symObjAddr: 0x800, symBinAddr: 0x23520, symSize: 0x10 }
  - { offsetInCU: 0x630, offset: 0x1986A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14LoginErrorCodeOMa', symObjAddr: 0x810, symBinAddr: 0x23530, symSize: 0x10 }
  - { offsetInCU: 0x643, offset: 0x1987D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVAC10Foundation13CustomNSErrorAAWl', symObjAddr: 0x820, symBinAddr: 0x23540, symSize: 0x2E }
  - { offsetInCU: 0x6B3, offset: 0x198ED, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0x380, symBinAddr: 0x230C0, symSize: 0x40 }
  - { offsetInCU: 0x74A, offset: 0x19984, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVs0D0AAsADP9_userInfoyXlSgvgTW', symObjAddr: 0x400, symBinAddr: 0x23140, symSize: 0x10 }
  - { offsetInCU: 0x765, offset: 0x1999F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVs0D0AAsADP19_getEmbeddedNSErroryXlSgyFTW', symObjAddr: 0x410, symBinAddr: 0x23150, symSize: 0x10 }
  - { offsetInCU: 0x7E5, offset: 0x19A1F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14LoginErrorCodeOSHAASH9hashValueSivgTW', symObjAddr: 0x460, symBinAddr: 0x231A0, symSize: 0x40 }
  - { offsetInCU: 0x88C, offset: 0x19AC6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14LoginErrorCodeOSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0x4A0, symBinAddr: 0x231E0, symSize: 0x20 }
  - { offsetInCU: 0x8DB, offset: 0x19B15, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14LoginErrorCodeOSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0x4C0, symBinAddr: 0x23200, symSize: 0x40 }
  - { offsetInCU: 0x960, offset: 0x19B9A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14LoginErrorCodeOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x540, symBinAddr: 0x23280, symSize: 0x10 }
  - { offsetInCU: 0x9F3, offset: 0x19C2D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVs0D0AAsADP7_domainSSvgTW', symObjAddr: 0x3C0, symBinAddr: 0x23100, symSize: 0x20 }
  - { offsetInCU: 0xA0F, offset: 0x19C49, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVs0D0AAsADP5_codeSivgTW', symObjAddr: 0x3E0, symBinAddr: 0x23120, symSize: 0x20 }
  - { offsetInCU: 0xA97, offset: 0x19CD1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV03_nsD0So7NSErrorCSgvg', symObjAddr: 0x0, symBinAddr: 0x22D40, symSize: 0x20 }
  - { offsetInCU: 0xAAA, offset: 0x19CE4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV9errorCodeSivg', symObjAddr: 0x20, symBinAddr: 0x22D60, symSize: 0x10 }
  - { offsetInCU: 0xABD, offset: 0x19CF7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV13errorUserInfoSDySSypGvg', symObjAddr: 0x30, symBinAddr: 0x22D70, symSize: 0x10 }
  - { offsetInCU: 0xADB, offset: 0x19D15, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV03_nsD0ACSo7NSErrorC_tcfC', symObjAddr: 0x40, symBinAddr: 0x22D80, symSize: 0xB0 }
  - { offsetInCU: 0xAFE, offset: 0x19D38, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV_8userInfoAcA0cD4CodeO_SDySSypGtcfC', symObjAddr: 0xF0, symBinAddr: 0x22E30, symSize: 0x20 }
  - { offsetInCU: 0xB2E, offset: 0x19D68, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14LoginErrorCodeO8rawValueSivg', symObjAddr: 0x110, symBinAddr: 0x22E50, symSize: 0x10 }
  - { offsetInCU: 0xB53, offset: 0x19D8D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV11errorDomainSSvgZ', symObjAddr: 0x120, symBinAddr: 0x22E60, symSize: 0x50 }
  - { offsetInCU: 0xB7A, offset: 0x19DB4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV8reservedAA0cD4CodeOvgZ', symObjAddr: 0x170, symBinAddr: 0x22EB0, symSize: 0x10 }
  - { offsetInCU: 0xB9B, offset: 0x19DD5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV7unknownAA0cD4CodeOvgZ', symObjAddr: 0x180, symBinAddr: 0x22EC0, symSize: 0x10 }
  - { offsetInCU: 0xBBC, offset: 0x19DF6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV15passwordChangedAA0cD4CodeOvgZ', symObjAddr: 0x190, symBinAddr: 0x22ED0, symSize: 0x10 }
  - { offsetInCU: 0xBDD, offset: 0x19E17, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV16userCheckpointedAA0cD4CodeOvgZ', symObjAddr: 0x1A0, symBinAddr: 0x22EE0, symSize: 0x10 }
  - { offsetInCU: 0xBFE, offset: 0x19E38, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV12userMismatchAA0cD4CodeOvgZ', symObjAddr: 0x1B0, symBinAddr: 0x22EF0, symSize: 0x10 }
  - { offsetInCU: 0xC1F, offset: 0x19E59, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV15unconfirmedUserAA0cD4CodeOvgZ', symObjAddr: 0x1C0, symBinAddr: 0x22F00, symSize: 0x10 }
  - { offsetInCU: 0xC40, offset: 0x19E7A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV24systemAccountAppDisabledAA0cD4CodeOvgZ', symObjAddr: 0x1D0, symBinAddr: 0x22F10, symSize: 0x10 }
  - { offsetInCU: 0xC61, offset: 0x19E9B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV24systemAccountUnavailableAA0cD4CodeOvgZ', symObjAddr: 0x1E0, symBinAddr: 0x22F20, symSize: 0x10 }
  - { offsetInCU: 0xC82, offset: 0x19EBC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV18badChallengeStringAA0cD4CodeOvgZ', symObjAddr: 0x1F0, symBinAddr: 0x22F30, symSize: 0x10 }
  - { offsetInCU: 0xCA3, offset: 0x19EDD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV14invalidIDTokenAA0cD4CodeOvgZ', symObjAddr: 0x200, symBinAddr: 0x22F40, symSize: 0x10 }
  - { offsetInCU: 0xCC4, offset: 0x19EFE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV18missingAccessTokenAA0cD4CodeOvgZ', symObjAddr: 0x210, symBinAddr: 0x22F50, symSize: 0x10 }
  - { offsetInCU: 0xCE5, offset: 0x19F1F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV2eeoiySbAC_ACtFZ', symObjAddr: 0x220, symBinAddr: 0x22F60, symSize: 0x30 }
  - { offsetInCU: 0xD54, offset: 0x19F8E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV4hash4intoys6HasherVz_tF', symObjAddr: 0x250, symBinAddr: 0x22F90, symSize: 0x20 }
  - { offsetInCU: 0xDC6, offset: 0x1A000, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV9hashValueSivg', symObjAddr: 0x270, symBinAddr: 0x22FB0, symSize: 0x40 }
  - { offsetInCU: 0xEFF, offset: 0x1A139, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14LoginErrorCodeO8rawValueACSgSi_tcfC', symObjAddr: 0x450, symBinAddr: 0x23190, symSize: 0x10 }
  - { offsetInCU: 0x27, offset: 0x1A1A5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16LoginErrorDomain_WZ', symObjAddr: 0x0, symBinAddr: 0x23570, symSize: 0x30 }
  - { offsetInCU: 0x49, offset: 0x1A1C7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16LoginErrorDomainSSvp', symObjAddr: 0x78, symBinAddr: 0x5FFD8, symSize: 0x0 }
  - { offsetInCU: 0x57, offset: 0x1A1D5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16LoginErrorDomain_WZ', symObjAddr: 0x0, symBinAddr: 0x23570, symSize: 0x30 }
  - { offsetInCU: 0x92, offset: 0x1A210, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16LoginErrorDomainSSvg', symObjAddr: 0x30, symBinAddr: 0x235A0, symSize: 0x47 }
  - { offsetInCU: 0xC0, offset: 0x1A39F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC15defaultAudienceAA07DefaultF0OvgTo', symObjAddr: 0x5C0, symBinAddr: 0x23BE0, symSize: 0x40 }
  - { offsetInCU: 0x112, offset: 0x1A3F1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC15defaultAudienceAA07DefaultF0OvsTo', symObjAddr: 0x630, symBinAddr: 0x23C50, symSize: 0x40 }
  - { offsetInCU: 0x1D4, offset: 0x1A4B3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC13configurationAA0C13ConfigurationCSgvgTo', symObjAddr: 0x820, symBinAddr: 0x23E00, symSize: 0x40 }
  - { offsetInCU: 0x226, offset: 0x1A505, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC13configurationAA0C13ConfigurationCSgvsTo', symObjAddr: 0x8A0, symBinAddr: 0x23E80, symSize: 0x60 }
  - { offsetInCU: 0x27A, offset: 0x1A559, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC20requestedPermissionsShyAA12FBPermissionCGSgvgTo', symObjAddr: 0x900, symBinAddr: 0x23EE0, symSize: 0x90 }
  - { offsetInCU: 0x2CC, offset: 0x1A5AB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC20requestedPermissionsShyAA12FBPermissionCGSgvsTo', symObjAddr: 0x9D0, symBinAddr: 0x23FB0, symSize: 0x90 }
  - { offsetInCU: 0x3A3, offset: 0x1A682, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC17usedSafariSessionSbvgTo', symObjAddr: 0xC60, symBinAddr: 0x24240, symSize: 0x40 }
  - { offsetInCU: 0x3F5, offset: 0x1A6D4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC17usedSafariSessionSbvsTo', symObjAddr: 0xCD0, symBinAddr: 0x242B0, symSize: 0x40 }
  - { offsetInCU: 0x457, offset: 0x1A736, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC012isPerformingC0SbvgTo', symObjAddr: 0xD90, symBinAddr: 0x24370, symSize: 0x40 }
  - { offsetInCU: 0x588, offset: 0x1A867, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC15defaultAudienceAcA07DefaultF0O_tcfcTo', symObjAddr: 0x15C0, symBinAddr: 0x24BA0, symSize: 0x50 }
  - { offsetInCU: 0x64E, offset: 0x1A92D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5logIn4from13configuration10completionySo16UIViewControllerCSg_AA0C13ConfigurationCSgyAA0cdC6ResultCSg_s5Error_pSgtctFTo', symObjAddr: 0x1A30, symBinAddr: 0x25010, symSize: 0xB0 }
  - { offsetInCU: 0x6D2, offset: 0x1A9B1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5logIn11permissions4from7handlerySaySSG_So16UIViewControllerCSgyAA0cdC6ResultCSg_s5Error_pSgtcSgtFTo', symObjAddr: 0x21E0, symBinAddr: 0x257C0, symSize: 0xD0 }
  - { offsetInCU: 0x715, offset: 0x1A9F4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5logIn33_C218275A97333B874EDDFE627110566CLLyyFySb_s5Error_pSgtcfU_', symObjAddr: 0x23C0, symBinAddr: 0x259A0, symSize: 0x1F0 }
  - { offsetInCU: 0x8BA, offset: 0x1AB99, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC21reauthorizeDataAccess4from7handlerySo16UIViewControllerC_yAA0cdC6ResultCSg_s5Error_pSgtctFTo', symObjAddr: 0x3BD0, symBinAddr: 0x271B0, symSize: 0x80 }
  - { offsetInCU: 0x91F, offset: 0x1ABFE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC6logOutyyFTo', symObjAddr: 0x3EA0, symBinAddr: 0x27480, symSize: 0x30 }
  - { offsetInCU: 0xA2E, offset: 0x1AD0D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC22completeAuthentication10parameters15expectChallengeyAA01_C20CompletionParametersC_SbtFTo', symObjAddr: 0x55E0, symBinAddr: 0x28BC0, symSize: 0x60 }
  - { offsetInCU: 0xABE, offset: 0x1AD9D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC15logInParameters13configuration12loggingToken20authenticationMethodSDyS2SGSgAA0C13ConfigurationCSg_SSSgSStFTo', symObjAddr: 0x76B0, symBinAddr: 0x2AC90, symSize: 0xF0 }
  - { offsetInCU: 0xAF5, offset: 0x1ADD4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC24validateReauthentication11accessToken11loginResult04userH5NonceySo011FBSDKAccessH0C_AA0cdcJ0CSgSSSgtFTo', symObjAddr: 0x7C30, symBinAddr: 0x2B210, symSize: 0xA0 }
  - { offsetInCU: 0xB2C, offset: 0x1AE0B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18storeExpectedNonceyySSSgFTo', symObjAddr: 0x8070, symBinAddr: 0x2B650, symSize: 0x60 }
  - { offsetInCU: 0xB5C, offset: 0x1AE3B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCACycfcTo', symObjAddr: 0x82C0, symBinAddr: 0x2B8A0, symSize: 0x20 }
  - { offsetInCU: 0xB8C, offset: 0x1AE6B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCAA0C9ProvidingA2aDP15defaultAudienceAA07DefaultG0OvgTW', symObjAddr: 0x8A80, symBinAddr: 0x2C060, symSize: 0x40 }
  - { offsetInCU: 0xBC4, offset: 0x1AEA3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCAA0C9ProvidingA2aDP15defaultAudienceAA07DefaultG0OvsTW', symObjAddr: 0x8AC0, symBinAddr: 0x2C0A0, symSize: 0x40 }
  - { offsetInCU: 0xC03, offset: 0x1AEE2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCAA0C9ProvidingA2aDP15defaultAudienceAA07DefaultG0OvMTW', symObjAddr: 0x8B00, symBinAddr: 0x2C0E0, symSize: 0x40 }
  - { offsetInCU: 0xC3D, offset: 0x1AF1C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCAA0C9ProvidingA2aDP5logIn14viewController13configuration10completionySo06UIViewI0CSg_AA0C13ConfigurationCSgyAA0C6ResultOctFTW', symObjAddr: 0x8B40, symBinAddr: 0x2C120, symSize: 0x80 }
  - { offsetInCU: 0xCA4, offset: 0x1AF83, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCAA0C9ProvidingA2aDP5logIn11permissions4from7handlerySaySSG_So16UIViewControllerCSgyAA0cdC6ResultCSg_s5Error_pSgtcSgtFTW', symObjAddr: 0x8BC0, symBinAddr: 0x2C1A0, symSize: 0x20 }
  - { offsetInCU: 0xCBF, offset: 0x1AF9E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCAA0C9ProvidingA2aDP6logOutyyFTW', symObjAddr: 0x8BE0, symBinAddr: 0x2C1C0, symSize: 0x20 }
  - { offsetInCU: 0xCDA, offset: 0x1AFB9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCAA17DependentAsObjectA2aDP22configuredDependencies0gI0QzSgvgTW', symObjAddr: 0x8FA0, symBinAddr: 0x2C580, symSize: 0x40 }
  - { offsetInCU: 0xD0C, offset: 0x1AFEB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCAA17DependentAsObjectA2aDP22configuredDependencies0gI0QzSgvsTW', symObjAddr: 0x8FE0, symBinAddr: 0x2C5C0, symSize: 0x60 }
  - { offsetInCU: 0xD4E, offset: 0x1B02D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCAA17DependentAsObjectA2aDP22configuredDependencies0gI0QzSgvMTW', symObjAddr: 0x9040, symBinAddr: 0x2C620, symSize: 0x40 }
  - { offsetInCU: 0xD88, offset: 0x1B067, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCAA17DependentAsObjectA2aDP19defaultDependencies0gI0QzSgvgTW', symObjAddr: 0x9080, symBinAddr: 0x2C660, symSize: 0x10 }
  - { offsetInCU: 0xDE6, offset: 0x1B0C5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC21reauthorizeDataAccess4from7handlerySo16UIViewControllerC_yAA0cdC6ResultCSg_s5Error_pSgtctF04$s13a6Kit012cd2C6l13CSgSo7NSErrorq11IeyByy_ADs5M12_pSgIeggg_TRAKSo0S0CSgIeyByy_Tf1ncn_nTf4dng_n', symObjAddr: 0xF0A0, symBinAddr: 0x325B0, symSize: 0x560 }
  - { offsetInCU: 0x14D4, offset: 0x1B7B3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC19defaultDependenciesAC06ObjectF0VSgvpACTk', symObjAddr: 0xFD0, symBinAddr: 0x245B0, symSize: 0x90 }
  - { offsetInCU: 0x1589, offset: 0x1B868, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultCSgSo7NSErrorCSgIeyByy_ADs5Error_pSgIeggg_TR', symObjAddr: 0x1AE0, symBinAddr: 0x250C0, symSize: 0x50 }
  - { offsetInCU: 0x15C5, offset: 0x1B8A4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultCSgs5Error_pSgytIegnnr_AdFIeggg_TR', symObjAddr: 0x1C20, symBinAddr: 0x25200, symSize: 0x60 }
  - { offsetInCU: 0x19EC, offset: 0x1BCCB, size: 0x8, addend: 0x0, symName: '_$sSbs5Error_pSgIegyg_SbSo7NSErrorCSgIeyByy_TR', symObjAddr: 0x7CF0, symBinAddr: 0x2B2D0, symSize: 0x60 }
  - { offsetInCU: 0x1A03, offset: 0x1BCE2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCfETo', symObjAddr: 0x8310, symBinAddr: 0x2B8F0, symSize: 0xA0 }
  - { offsetInCU: 0x1A47, offset: 0x1BD26, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC10makeOpenerACyFZTo', symObjAddr: 0x83D0, symBinAddr: 0x2B9B0, symSize: 0x30 }
  - { offsetInCU: 0x1ADE, offset: 0x1BDBD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC11application_4open17sourceApplication10annotationSbSo13UIApplicationCSg_10Foundation3URLVSgSSSgypSgtFTo', symObjAddr: 0x8420, symBinAddr: 0x2BA00, symSize: 0x160 }
  - { offsetInCU: 0x1B10, offset: 0x1BDEF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC7canOpen_3for17sourceApplication10annotationSb10Foundation3URLV_So13UIApplicationCSgSSSgypSgtFTo', symObjAddr: 0x8580, symBinAddr: 0x2BB60, symSize: 0x130 }
  - { offsetInCU: 0x1B5E, offset: 0x1BE3D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC26applicationDidBecomeActiveyySo13UIApplicationCFTo', symObjAddr: 0x86F0, symBinAddr: 0x2BCD0, symSize: 0x70 }
  - { offsetInCU: 0x1BF1, offset: 0x1BED0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC19isAuthenticationURLySb10Foundation0G0VFTo', symObjAddr: 0x87B0, symBinAddr: 0x2BD90, symSize: 0xB0 }
  - { offsetInCU: 0x1C52, offset: 0x1BF31, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC21shouldStopPropagation2ofSb10Foundation3URLV_tFTo', symObjAddr: 0x89F0, symBinAddr: 0x2BFD0, symSize: 0x90 }
  - { offsetInCU: 0x1C6D, offset: 0x1BF4C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit29LoginCompleterFactoryProtocol_pWOb', symObjAddr: 0x90C0, symBinAddr: 0x2C6A0, symSize: 0x20 }
  - { offsetInCU: 0x1C80, offset: 0x1BF5F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5logIn14viewController13configuration10completionySo06UIViewH0CSg_AA0C13ConfigurationCSgyAA0C6ResultOctFyAA0cdcM0CSg_s5Error_pSgtcfU_TA', symObjAddr: 0x9100, symBinAddr: 0x2C6E0, symSize: 0x20 }
  - { offsetInCU: 0x1C93, offset: 0x1BF72, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5logIn11permissions14viewController10completionySay09FBSDKCoreB010PermissionOG_So06UIViewI0CSgyAA0C6ResultOcSgtFyAA0cdcN0CSg_s5Error_pSgtcfU_TA', symObjAddr: 0x9150, symBinAddr: 0x2C730, symSize: 0x20 }
  - { offsetInCU: 0x1CA6, offset: 0x1BF85, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesVWOh', symObjAddr: 0x9170, symBinAddr: 0x2C750, symSize: 0x20 }
  - { offsetInCU: 0x1D5E, offset: 0x1C03D, size: 0x8, addend: 0x0, symName: '_$ss22_ContiguousArrayBufferV19_uninitializedCount15minimumCapacityAByxGSi_SitcfC13FBSDKLoginKit18DeviceLoginManagerC_Tgm5', symObjAddr: 0x97A0, symBinAddr: 0x2CD30, symSize: 0x90 }
  - { offsetInCU: 0x1DDC, offset: 0x1C0BB, size: 0x8, addend: 0x0, symName: '_$ss22_ContiguousArrayBufferV19_uninitializedCount15minimumCapacityAByxGSi_SitcfCSS_Tgm5', symObjAddr: 0x9830, symBinAddr: 0x2CDC0, symSize: 0x80 }
  - { offsetInCU: 0x1E5A, offset: 0x1C139, size: 0x8, addend: 0x0, symName: '_$ss22_ContiguousArrayBufferV19_uninitializedCount15minimumCapacityAByxGSi_SitcfC12FBSDKCoreKit10PermissionO_Tgm5', symObjAddr: 0x98B0, symBinAddr: 0x2CE40, symSize: 0xF0 }
  - { offsetInCU: 0x201B, offset: 0x1C2FA, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV_8capacityAByxGs07__CocoaB0Vn_SitcfC13FBSDKLoginKit12FBPermissionC_Tgm5', symObjAddr: 0xA140, symBinAddr: 0x2D6D0, symSize: 0x210 }
  - { offsetInCU: 0x245D, offset: 0x1C73C, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV19genericIntersectionyAByxGqd__7ElementQyd__RszSTRd__lFADs13_UnsafeBitsetVXEfU_13FBSDKLoginKit12FBPermissionC_ShyAKGTg5', symObjAddr: 0xD2C0, symBinAddr: 0x30850, symSize: 0x4C0 }
  - { offsetInCU: 0x25AE, offset: 0x1C88D, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV12intersectionyAByxGADFADs13_UnsafeBitsetVXEfU_13FBSDKLoginKit12FBPermissionC_Tg5', symObjAddr: 0xD780, symBinAddr: 0x30D10, symSize: 0x5B0 }
  - { offsetInCU: 0x26B2, offset: 0x1C991, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC24validateReauthentication11accessToken11loginResult04userH5NonceySo011FBSDKAccessH0C_AA0cdcJ0CSgSSSgtFySo27FBSDKGraphRequestConnecting_pSg_ypSgs5Error_pSgtcfU_TA', symObjAddr: 0xE590, symBinAddr: 0x31AF0, symSize: 0x40 }
  - { offsetInCU: 0x26C5, offset: 0x1C9A4, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0xE5D0, symBinAddr: 0x31B30, symSize: 0x20 }
  - { offsetInCU: 0x26D8, offset: 0x1C9B7, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0xE5F0, symBinAddr: 0x31B50, symSize: 0x10 }
  - { offsetInCU: 0x27BA, offset: 0x1CA99, size: 0x8, addend: 0x0, symName: '_$sSTsE6reduce4into_qd__qd__n_yqd__z_7ElementQztKXEtKlFSDyS2SSgG_s17_NativeDictionaryVyS2SGTg5051$sSD16compactMapValuesySDyxqd__Gqd__Sgq_KXEKlFys17_dE44Vyxqd__Gz_x3key_q_5valuettKXEfU_SS_SSSgSSTG5xq_Sgs5Error_pr0_lyAESSIsgnrzo_Tf1ncn_nTf4nng_n', symObjAddr: 0xE600, symBinAddr: 0x31B60, symSize: 0x3F0 }
  - { offsetInCU: 0x2A25, offset: 0x1CD04, size: 0x8, addend: 0x0, symName: '_$sSh21_nonEmptyArrayLiteralShyxGSayxG_tcfC13FBSDKLoginKit12FBPermissionC_Tgm5Tf4g_n', symObjAddr: 0xEC40, symBinAddr: 0x32150, symSize: 0x460 }
  - { offsetInCU: 0x2C26, offset: 0x1CF05, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV12intersectionyAByxGADF13FBSDKLoginKit12FBPermissionC_Tg5Tf4ng_n', symObjAddr: 0xF600, symBinAddr: 0x32B10, symSize: 0x140 }
  - { offsetInCU: 0x2D75, offset: 0x1D054, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV19genericIntersectionyAByxGqd__7ElementQyd__RszSTRd__lF13FBSDKLoginKit12FBPermissionC_ShyAIGTg5Tf4ng_n', symObjAddr: 0xF740, symBinAddr: 0x32C50, symSize: 0x140 }
  - { offsetInCU: 0x2F69, offset: 0x1D248, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit29LoginCompleterFactoryProtocol_pWOc', symObjAddr: 0x10070, symBinAddr: 0x33580, symSize: 0x30 }
  - { offsetInCU: 0x2F7C, offset: 0x1D25B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCMU', symObjAddr: 0x100D0, symBinAddr: 0x335E0, symSize: 0x10 }
  - { offsetInCU: 0x2F8F, offset: 0x1D26E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCMa', symObjAddr: 0x100E0, symBinAddr: 0x335F0, symSize: 0x30 }
  - { offsetInCU: 0x2FA2, offset: 0x1D281, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCMr', symObjAddr: 0x10110, symBinAddr: 0x33620, symSize: 0xC0 }
  - { offsetInCU: 0x2FB5, offset: 0x1D294, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVSgMa', symObjAddr: 0x101D0, symBinAddr: 0x336E0, symSize: 0x50 }
  - { offsetInCU: 0x2FC8, offset: 0x1D2A7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesVwCP', symObjAddr: 0x10220, symBinAddr: 0x33730, symSize: 0x30 }
  - { offsetInCU: 0x2FDB, offset: 0x1D2BA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesVwxx', symObjAddr: 0x10250, symBinAddr: 0x33760, symSize: 0x50 }
  - { offsetInCU: 0x2FEE, offset: 0x1D2CD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesVwcp', symObjAddr: 0x102A0, symBinAddr: 0x337B0, symSize: 0xD0 }
  - { offsetInCU: 0x3001, offset: 0x1D2E0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesVwca', symObjAddr: 0x10370, symBinAddr: 0x33880, symSize: 0xE0 }
  - { offsetInCU: 0x3014, offset: 0x1D2F3, size: 0x8, addend: 0x0, symName: ___swift_memcpy112_8, symObjAddr: 0x10580, symBinAddr: 0x33960, symSize: 0x40 }
  - { offsetInCU: 0x3027, offset: 0x1D306, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesVwta', symObjAddr: 0x105C0, symBinAddr: 0x339A0, symSize: 0xB0 }
  - { offsetInCU: 0x303A, offset: 0x1D319, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesVwet', symObjAddr: 0x10670, symBinAddr: 0x33A50, symSize: 0x40 }
  - { offsetInCU: 0x304D, offset: 0x1D32C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesVwst', symObjAddr: 0x106B0, symBinAddr: 0x33A90, symSize: 0x60 }
  - { offsetInCU: 0x3060, offset: 0x1D33F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesVMa', symObjAddr: 0x10710, symBinAddr: 0x33AF0, symSize: 0x10 }
  - { offsetInCU: 0x3073, offset: 0x1D352, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultCSgSo7NSErrorCSgIeyByy_ADs5Error_pSgIeggg_TRTA', symObjAddr: 0x10740, symBinAddr: 0x33B20, symSize: 0x10 }
  - { offsetInCU: 0x3086, offset: 0x1D365, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultCSgs5Error_pSgIeggg_SgWOe', symObjAddr: 0x10750, symBinAddr: 0x33B30, symSize: 0x20 }
  - { offsetInCU: 0x3099, offset: 0x1D378, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC11application_4open17sourceApplication10annotationSbSo13UIApplicationCSg_10Foundation3URLVSgSSSgypSgtFyAA01_C20CompletionParametersCcfU_TA', symObjAddr: 0x10830, symBinAddr: 0x33BB0, symSize: 0x20 }
  - { offsetInCU: 0x30D0, offset: 0x1D3AF, size: 0x8, addend: 0x0, symName: '_$sSh5IndexV8_VariantOyx__GSHRzlWOy', symObjAddr: 0x10870, symBinAddr: 0x33BD0, symSize: 0x20 }
  - { offsetInCU: 0x30E3, offset: 0x1D3C2, size: 0x8, addend: 0x0, symName: '_$sSD8IteratorV8_VariantOyxq___GSHRzr0_lWOe', symObjAddr: 0x10890, symBinAddr: 0x33BF0, symSize: 0x20 }
  - { offsetInCU: 0x30F6, offset: 0x1D3D5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVWOb', symObjAddr: 0x108B0, symBinAddr: 0x33C10, symSize: 0x30 }
  - { offsetInCU: 0x3109, offset: 0x1D3E8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVWOh', symObjAddr: 0x108E0, symBinAddr: 0x33C40, symSize: 0x30 }
  - { offsetInCU: 0x3127, offset: 0x1D406, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultCSgs5Error_pSgIeggg_AdFytIegnnr_TRTA', symObjAddr: 0x109C0, symBinAddr: 0x33CD0, symSize: 0x20 }
  - { offsetInCU: 0x314F, offset: 0x1D42E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultCSgs5Error_pSgytIegnnr_AdFIeggg_TRTA', symObjAddr: 0x109E0, symBinAddr: 0x33CF0, symSize: 0x20 }
  - { offsetInCU: 0x3162, offset: 0x1D441, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultCSgs5Error_pSgIeggg_SgWOy', symObjAddr: 0x10A00, symBinAddr: 0x33D10, symSize: 0x20 }
  - { offsetInCU: 0x3175, offset: 0x1D454, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5logIn33_C218275A97333B874EDDFE627110566CLLyyFySb_s5Error_pSgtcfU_TA', symObjAddr: 0x10A20, symBinAddr: 0x33D30, symSize: 0x10 }
  - { offsetInCU: 0x3193, offset: 0x1D472, size: 0x8, addend: 0x0, symName: '_$sSbs5Error_pSgytIegnnr_SbABIegyg_TRTA', symObjAddr: 0x10A30, symBinAddr: 0x33D40, symSize: 0x50 }
  - { offsetInCU: 0x31C6, offset: 0x1D4A5, size: 0x8, addend: 0x0, symName: '_$sSbs5Error_pSgIegyg_SbABytIegnnr_TRTA', symObjAddr: 0x10A80, symBinAddr: 0x33D90, symSize: 0x20 }
  - { offsetInCU: 0x3263, offset: 0x1D542, size: 0x8, addend: 0x0, symName: '_$sSlsE3mapySayqd__Gqd__7ElementQzKXEKlFSay12FBSDKCoreKit10PermissionOG_SSTg5085$s13FBSDKLoginKit12LoginManagerC5logIn11permissions14viewController10completionySay09c4B010E91OG_So06UIViewI0CSgyAA0C6ResultOcSgtFSSAJcfu_32f5ef5a0b9ade21eb65ffea7b618f60adAJSSTf3nnpk_nTf1cn_n', symObjAddr: 0x170, symBinAddr: 0x23790, symSize: 0x110 }
  - { offsetInCU: 0x340F, offset: 0x1D6EE, size: 0x8, addend: 0x0, symName: '_$sSlsE3mapySayqd__Gqd__7ElementQzKXEKlFShy13FBSDKLoginKit12FBPermissionCG_SSTg504$s13c132Kit12LoginManagerC15logInParameters13configuration12loggingToken20authenticationMethodSDyS2SGSgAA0C13ConfigurationCSg_SSSgSStFSSAA12E52Ccfu_32e0d58b938ad0b6cb17de1b825049cc00AOSSTf3nnpk_nTf1cn_n', symObjAddr: 0x280, symBinAddr: 0x238A0, symSize: 0x340 }
  - { offsetInCU: 0x3B71, offset: 0x1DE50, size: 0x8, addend: 0x0, symName: '_$sSaySayxGqd__c7ElementQyd__RszSTRd__lufCSS_ShySSGTgm5Tf4g_n', symObjAddr: 0xF880, symBinAddr: 0x32D90, symSize: 0xC0 }
  - { offsetInCU: 0x40AD, offset: 0x1E38C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCACycfC', symObjAddr: 0x150, symBinAddr: 0x23770, symSize: 0x20 }
  - { offsetInCU: 0x4102, offset: 0x1E3E1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5logIn11permissions14viewController10completionySay09FBSDKCoreB010PermissionOG_So06UIViewI0CSgyAA0C6ResultOcSgtF', symObjAddr: 0x22B0, symBinAddr: 0x25890, symSize: 0x90 }
  - { offsetInCU: 0x4162, offset: 0x1E441, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5logIn11permissions14viewController10completionySay09FBSDKCoreB010PermissionOG_So06UIViewI0CSgyAA0C6ResultOcSgtFyAA0cdcN0CSg_s5Error_pSgtcfU_', symObjAddr: 0x2340, symBinAddr: 0x25920, symSize: 0x80 }
  - { offsetInCU: 0x420E, offset: 0x1E4ED, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC15logInParameters13configuration12loggingToken20authenticationMethodSDyS2SGSgAA0C13ConfigurationCSg_SSSgSStF', symObjAddr: 0x5640, symBinAddr: 0x28C20, symSize: 0x1E10 }
  - { offsetInCU: 0x50BC, offset: 0x1F39B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC15logInParameters13configuration12loggingToken20authenticationMethodSDyS2SGSgAA0C13ConfigurationCSg_SSSgSStFA2MXEfU_', symObjAddr: 0x7450, symBinAddr: 0x2AA30, symSize: 0x30 }
  - { offsetInCU: 0x5124, offset: 0x1F403, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC15defaultAudienceAA07DefaultF0Ovg', symObjAddr: 0x600, symBinAddr: 0x23C20, symSize: 0x30 }
  - { offsetInCU: 0x5161, offset: 0x1F440, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC15defaultAudienceAA07DefaultF0Ovs', symObjAddr: 0x670, symBinAddr: 0x23C90, symSize: 0x40 }
  - { offsetInCU: 0x5185, offset: 0x1F464, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC15defaultAudienceAA07DefaultF0OvM', symObjAddr: 0x6B0, symBinAddr: 0x23CD0, symSize: 0x40 }
  - { offsetInCU: 0x51A2, offset: 0x1F481, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC15defaultAudienceAA07DefaultF0OvM.resume.0', symObjAddr: 0x6F0, symBinAddr: 0x23D10, symSize: 0x10 }
  - { offsetInCU: 0x51C1, offset: 0x1F4A0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC7handlerAA010IdentifiedC13ResultHandlerVSgvg', symObjAddr: 0x700, symBinAddr: 0x23D20, symSize: 0x40 }
  - { offsetInCU: 0x51E4, offset: 0x1F4C3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC7handlerAA010IdentifiedC13ResultHandlerVSgvs', symObjAddr: 0x780, symBinAddr: 0x23D60, symSize: 0x60 }
  - { offsetInCU: 0x5216, offset: 0x1F4F5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC7handlerAA010IdentifiedC13ResultHandlerVSgvM', symObjAddr: 0x7E0, symBinAddr: 0x23DC0, symSize: 0x40 }
  - { offsetInCU: 0x524B, offset: 0x1F52A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC13configurationAA0C13ConfigurationCSgvg', symObjAddr: 0x860, symBinAddr: 0x23E40, symSize: 0x40 }
  - { offsetInCU: 0x529A, offset: 0x1F579, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC20requestedPermissionsShyAA12FBPermissionCGSgvg', symObjAddr: 0x990, symBinAddr: 0x23F70, symSize: 0x40 }
  - { offsetInCU: 0x52D7, offset: 0x1F5B6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC20requestedPermissionsShyAA12FBPermissionCGSgvM', symObjAddr: 0xA80, symBinAddr: 0x24060, symSize: 0x40 }
  - { offsetInCU: 0x52FA, offset: 0x1F5D9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC6loggerAA0cD6LoggerCSgvg', symObjAddr: 0xAC0, symBinAddr: 0x240A0, symSize: 0x40 }
  - { offsetInCU: 0x531B, offset: 0x1F5FA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC6loggerAA0cD6LoggerCSgvM', symObjAddr: 0xB70, symBinAddr: 0x24150, symSize: 0x40 }
  - { offsetInCU: 0x533E, offset: 0x1F61D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5stateAA0cD5StateOvg', symObjAddr: 0xBB0, symBinAddr: 0x24190, symSize: 0x30 }
  - { offsetInCU: 0x535F, offset: 0x1F63E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5stateAA0cD5StateOvs', symObjAddr: 0xBE0, symBinAddr: 0x241C0, symSize: 0x40 }
  - { offsetInCU: 0x538F, offset: 0x1F66E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5stateAA0cD5StateOvM', symObjAddr: 0xC20, symBinAddr: 0x24200, symSize: 0x40 }
  - { offsetInCU: 0x53C4, offset: 0x1F6A3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC17usedSafariSessionSbvg', symObjAddr: 0xCA0, symBinAddr: 0x24280, symSize: 0x30 }
  - { offsetInCU: 0x5401, offset: 0x1F6E0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC17usedSafariSessionSbvs', symObjAddr: 0xD10, symBinAddr: 0x242F0, symSize: 0x40 }
  - { offsetInCU: 0x5425, offset: 0x1F704, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC17usedSafariSessionSbvM', symObjAddr: 0xD50, symBinAddr: 0x24330, symSize: 0x40 }
  - { offsetInCU: 0x545A, offset: 0x1F739, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC012isPerformingC0Sbvg', symObjAddr: 0xDD0, symBinAddr: 0x243B0, symSize: 0x40 }
  - { offsetInCU: 0x549D, offset: 0x1F77C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC22configuredDependenciesAC06ObjectF0VSgvg', symObjAddr: 0xE10, symBinAddr: 0x243F0, symSize: 0x40 }
  - { offsetInCU: 0x54BA, offset: 0x1F799, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC22configuredDependenciesAC06ObjectF0VSgvs', symObjAddr: 0xE50, symBinAddr: 0x24430, symSize: 0x60 }
  - { offsetInCU: 0x54E0, offset: 0x1F7BF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC22configuredDependenciesAC06ObjectF0VSgvM', symObjAddr: 0xEB0, symBinAddr: 0x24490, symSize: 0x40 }
  - { offsetInCU: 0x54FD, offset: 0x1F7DC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC19defaultDependenciesAC06ObjectF0VSgvg', symObjAddr: 0xEF0, symBinAddr: 0x244D0, symSize: 0xE0 }
  - { offsetInCU: 0x551F, offset: 0x1F7FE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC19defaultDependenciesAC06ObjectF0VSgvgAGyXEfU_', symObjAddr: 0x10D0, symBinAddr: 0x246B0, symSize: 0x2B0 }
  - { offsetInCU: 0x5675, offset: 0x1F954, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC19defaultDependenciesAC06ObjectF0VSgvs', symObjAddr: 0x1060, symBinAddr: 0x24640, symSize: 0x70 }
  - { offsetInCU: 0x5713, offset: 0x1F9F2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV17accessTokenWallet014authenticationhI012errorFactory012graphRequestL015internalUtility13keychainStore014loginCompleterL015profileProvider8settings9urlOpenerAESo011FBSDKAccessH9Providing_pXp_So019FBSDKAuthenticationH9Providing_pXpSo18FBSDKErrorCreating_pSo010FBSDKGraphnL0_pSo27FBSDKAppAvailabilityChecker_So26FBSDKAppURLSchemeProvidingSo15FBSDKURLHostingpSo013FBSDKKeychainR0_pAA0ctL8Protocol_p09FBSDKCoreB016ProfileProviding_pXpAY16SettingsProtocol_pSo14FBSDKURLOpener_ptcfC', symObjAddr: 0x1380, symBinAddr: 0x24960, symSize: 0x50 }
  - { offsetInCU: 0x5726, offset: 0x1FA05, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC19defaultDependenciesAC06ObjectF0VSgvM', symObjAddr: 0x13D0, symBinAddr: 0x249B0, symSize: 0x40 }
  - { offsetInCU: 0x5747, offset: 0x1FA26, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC19defaultDependenciesAC06ObjectF0VSgvM.resume.0', symObjAddr: 0x1410, symBinAddr: 0x249F0, symSize: 0x100 }
  - { offsetInCU: 0x57C7, offset: 0x1FAA6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC15defaultAudienceAcA07DefaultF0O_tcfC', symObjAddr: 0x1510, symBinAddr: 0x24AF0, symSize: 0x60 }
  - { offsetInCU: 0x5809, offset: 0x1FAE8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC15defaultAudienceAcA07DefaultF0O_tcfc', symObjAddr: 0x1570, symBinAddr: 0x24B50, symSize: 0x50 }
  - { offsetInCU: 0x5897, offset: 0x1FB76, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5logIn4from13configuration10completionySo16UIViewControllerCSg_AA0C13ConfigurationCSgyAA0cdC6ResultCSg_s5Error_pSgtctF', symObjAddr: 0x1610, symBinAddr: 0x24BF0, symSize: 0x100 }
  - { offsetInCU: 0x5939, offset: 0x1FC18, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC11commonLogIn33_C218275A97333B874EDDFE627110566CLL4from13configuration10completionySo16UIViewControllerCSg_AA0C13ConfigurationCSgyAA0cdC6ResultCSg_s5Error_pSgtcSgtF', symObjAddr: 0x1710, symBinAddr: 0x24CF0, symSize: 0x320 }
  - { offsetInCU: 0x5A64, offset: 0x1FD43, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5logIn14viewController13configuration10completionySo06UIViewH0CSg_AA0C13ConfigurationCSgyAA0C6ResultOctF', symObjAddr: 0x1B30, symBinAddr: 0x25110, symSize: 0x80 }
  - { offsetInCU: 0x5AB2, offset: 0x1FD91, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5logIn14viewController13configuration10completionySo06UIViewH0CSg_AA0C13ConfigurationCSgyAA0C6ResultOctFyAA0cdcM0CSg_s5Error_pSgtcfU_', symObjAddr: 0x1BB0, symBinAddr: 0x25190, symSize: 0x70 }
  - { offsetInCU: 0x5BA2, offset: 0x1FE81, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC13invokeHandler33_C218275A97333B874EDDFE627110566CLL6result5erroryAA0cdC6ResultCSg_s5Error_pSgtF', symObjAddr: 0x1C80, symBinAddr: 0x25260, symSize: 0x390 }
  - { offsetInCU: 0x5CBD, offset: 0x1FF9C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5logIn11permissions4from7handlerySaySSG_So16UIViewControllerCSgyAA0cdC6ResultCSg_s5Error_pSgtcSgtF', symObjAddr: 0x2010, symBinAddr: 0x255F0, symSize: 0x1D0 }
  - { offsetInCU: 0x5E87, offset: 0x20166, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC27handleImplicitCancelOfLogIn33_C218275A97333B874EDDFE627110566CLLyyF', symObjAddr: 0x25B0, symBinAddr: 0x25B90, symSize: 0x170 }
  - { offsetInCU: 0x5FAF, offset: 0x2028E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC19performBrowserLogIn33_C218275A97333B874EDDFE627110566CLL7handleryySb_s5Error_pSgtcSg_tF', symObjAddr: 0x2720, symBinAddr: 0x25D00, symSize: 0xFC0 }
  - { offsetInCU: 0x62DE, offset: 0x205BD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC19performBrowserLogIn33_C218275A97333B874EDDFE627110566CLL7handleryySb_s5Error_pSgtcSg_tFySb_AHtcyKXEfu1_ySb_AHtcfU_', symObjAddr: 0x7CD0, symBinAddr: 0x2B2B0, symSize: 0x10 }
  - { offsetInCU: 0x62F9, offset: 0x205D8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC19performBrowserLogIn33_C218275A97333B874EDDFE627110566CLL7handleryySb_s5Error_pSgtcSg_tFySb_AHtcyKXEfu2_ySb_AHtcfU0_', symObjAddr: 0x7CE0, symBinAddr: 0x2B2C0, symSize: 0x10 }
  - { offsetInCU: 0x6353, offset: 0x20632, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC21reauthorizeDataAccess4from7handlerySo16UIViewControllerC_yAA0cdC6ResultCSg_s5Error_pSgtctF', symObjAddr: 0x36E0, symBinAddr: 0x26CC0, symSize: 0x4F0 }
  - { offsetInCU: 0x6535, offset: 0x20814, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC6logOutyyF', symObjAddr: 0x3C50, symBinAddr: 0x27230, symSize: 0x250 }
  - { offsetInCU: 0x668B, offset: 0x2096A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC22completeAuthentication10parameters15expectChallengeyAA01_C20CompletionParametersC_SbtF', symObjAddr: 0x3ED0, symBinAddr: 0x274B0, symSize: 0x580 }
  - { offsetInCU: 0x67F6, offset: 0x20AD5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC22storeExpectedChallenge33_C218275A97333B874EDDFE627110566CLLyySSSgF', symObjAddr: 0x4450, symBinAddr: 0x27A30, symSize: 0x100 }
  - { offsetInCU: 0x6856, offset: 0x20B35, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC16getSuccessResult33_C218275A97333B874EDDFE627110566CLL4fromAA0cdcG0CAA01_C20CompletionParametersC_tF', symObjAddr: 0x4550, symBinAddr: 0x27B30, symSize: 0x770 }
  - { offsetInCU: 0x6DB9, offset: 0x21098, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC24validateReauthentication11accessToken11loginResult04userH5NonceySo011FBSDKAccessH0C_AA0cdcJ0CSgSSSgtF', symObjAddr: 0x4CC0, symBinAddr: 0x282A0, symSize: 0x4A0 }
  - { offsetInCU: 0x6F51, offset: 0x21230, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC24validateReauthentication11accessToken11loginResult04userH5NonceySo011FBSDKAccessH0C_AA0cdcJ0CSgSSSgtFySo27FBSDKGraphRequestConnecting_pSg_ypSgs5Error_pSgtcfU_', symObjAddr: 0x78B0, symBinAddr: 0x2AE90, symSize: 0x380 }
  - { offsetInCU: 0x7047, offset: 0x21326, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18getCancelledResult33_C218275A97333B874EDDFE627110566CLL4fromAA0cdcG0CAA01_C20CompletionParametersC_tF', symObjAddr: 0x5160, symBinAddr: 0x28740, symSize: 0x210 }
  - { offsetInCU: 0x718B, offset: 0x2146A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC19setGlobalProperties33_C218275A97333B874EDDFE627110566CLL10parameters11loginResultyAA01_C20CompletionParametersC_AA0cdcN0CSgtF', symObjAddr: 0x5370, symBinAddr: 0x28950, symSize: 0x270 }
  - { offsetInCU: 0x73D2, offset: 0x216B1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC010addLimitedC14ShimParameters33_C218275A97333B874EDDFE627110566CLL10parameters13configurationySDyS2SGz_AA0C13ConfigurationCtF', symObjAddr: 0x7480, symBinAddr: 0x2AA60, symSize: 0x120 }
  - { offsetInCU: 0x74B7, offset: 0x21796, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC25storeExpectedCodeVerifier33_C218275A97333B874EDDFE627110566CLLyyAA0gH0CSgF', symObjAddr: 0x77A0, symBinAddr: 0x2AD80, symSize: 0x110 }
  - { offsetInCU: 0x74F8, offset: 0x217D7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC29getRecentlyGrantedPermissions4fromShyAA12FBPermissionCGAH_tF', symObjAddr: 0x7D50, symBinAddr: 0x2B330, symSize: 0x150 }
  - { offsetInCU: 0x75B5, offset: 0x21894, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC30getRecentlyDeclinedPermissions4fromShyAA12FBPermissionCGAH_tF', symObjAddr: 0x7EA0, symBinAddr: 0x2B480, symSize: 0xE0 }
  - { offsetInCU: 0x765E, offset: 0x2193D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCACycfc', symObjAddr: 0x8180, symBinAddr: 0x2B760, symSize: 0x140 }
  - { offsetInCU: 0x7681, offset: 0x21960, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCfD', symObjAddr: 0x82E0, symBinAddr: 0x2B8C0, symSize: 0x30 }
  - { offsetInCU: 0x76A8, offset: 0x21987, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC10makeOpenerACyFZ', symObjAddr: 0x83B0, symBinAddr: 0x2B990, symSize: 0x20 }
  - { offsetInCU: 0x76F2, offset: 0x219D1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC11application_4open17sourceApplication10annotationSbSo13UIApplicationCSg_10Foundation3URLVSgSSSgypSgtF', symObjAddr: 0x8400, symBinAddr: 0x2B9E0, symSize: 0x10 }
  - { offsetInCU: 0x772F, offset: 0x21A0E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC7canOpen_3for17sourceApplication10annotationSb10Foundation3URLV_So13UIApplicationCSgSSSgypSgtF', symObjAddr: 0x8410, symBinAddr: 0x2B9F0, symSize: 0x10 }
  - { offsetInCU: 0x774E, offset: 0x21A2D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC26applicationDidBecomeActiveyySo13UIApplicationCF', symObjAddr: 0x86B0, symBinAddr: 0x2BC90, symSize: 0x40 }
  - { offsetInCU: 0x77DA, offset: 0x21AB9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC19isAuthenticationURLySb10Foundation0G0VF', symObjAddr: 0x8760, symBinAddr: 0x2BD40, symSize: 0x50 }
  - { offsetInCU: 0x7822, offset: 0x21B01, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC21shouldStopPropagation2ofSb10Foundation3URLV_tF', symObjAddr: 0x8860, symBinAddr: 0x2BE40, symSize: 0x190 }
  - { offsetInCU: 0x797F, offset: 0x21C5E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV17accessTokenWalletSo011FBSDKAccessH9Providing_pXpvg', symObjAddr: 0x8C00, symBinAddr: 0x2C1E0, symSize: 0x10 }
  - { offsetInCU: 0x7992, offset: 0x21C71, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV17accessTokenWalletSo011FBSDKAccessH9Providing_pXpvs', symObjAddr: 0x8C10, symBinAddr: 0x2C1F0, symSize: 0x10 }
  - { offsetInCU: 0x79A5, offset: 0x21C84, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV17accessTokenWalletSo011FBSDKAccessH9Providing_pXpvM', symObjAddr: 0x8C20, symBinAddr: 0x2C200, symSize: 0x10 }
  - { offsetInCU: 0x79B8, offset: 0x21C97, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV17accessTokenWalletSo011FBSDKAccessH9Providing_pXpvM.resume.0', symObjAddr: 0x8C30, symBinAddr: 0x2C210, symSize: 0x10 }
  - { offsetInCU: 0x79CB, offset: 0x21CAA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV25authenticationTokenWalletSo019FBSDKAuthenticationH9Providing_pXpvg', symObjAddr: 0x8C40, symBinAddr: 0x2C220, symSize: 0x10 }
  - { offsetInCU: 0x79DE, offset: 0x21CBD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV25authenticationTokenWalletSo019FBSDKAuthenticationH9Providing_pXpvs', symObjAddr: 0x8C50, symBinAddr: 0x2C230, symSize: 0x10 }
  - { offsetInCU: 0x79F1, offset: 0x21CD0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV25authenticationTokenWalletSo019FBSDKAuthenticationH9Providing_pXpvM', symObjAddr: 0x8C60, symBinAddr: 0x2C240, symSize: 0x20 }
  - { offsetInCU: 0x7A04, offset: 0x21CE3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV25authenticationTokenWalletSo019FBSDKAuthenticationH9Providing_pXpvM.resume.0', symObjAddr: 0x8C80, symBinAddr: 0x2C260, symSize: 0x10 }
  - { offsetInCU: 0x7A17, offset: 0x21CF6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV12errorFactorySo18FBSDKErrorCreating_pvg', symObjAddr: 0x8C90, symBinAddr: 0x2C270, symSize: 0x10 }
  - { offsetInCU: 0x7A2A, offset: 0x21D09, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV12errorFactorySo18FBSDKErrorCreating_pvs', symObjAddr: 0x8CA0, symBinAddr: 0x2C280, symSize: 0x20 }
  - { offsetInCU: 0x7A3D, offset: 0x21D1C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV12errorFactorySo18FBSDKErrorCreating_pvM', symObjAddr: 0x8CC0, symBinAddr: 0x2C2A0, symSize: 0x20 }
  - { offsetInCU: 0x7A50, offset: 0x21D2F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV12errorFactorySo18FBSDKErrorCreating_pvM.resume.0', symObjAddr: 0x8CE0, symBinAddr: 0x2C2C0, symSize: 0x10 }
  - { offsetInCU: 0x7A63, offset: 0x21D42, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV19graphRequestFactorySo010FBSDKGraphhI0_pvg', symObjAddr: 0x8CF0, symBinAddr: 0x2C2D0, symSize: 0x10 }
  - { offsetInCU: 0x7A76, offset: 0x21D55, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV19graphRequestFactorySo010FBSDKGraphhI0_pvs', symObjAddr: 0x8D00, symBinAddr: 0x2C2E0, symSize: 0x20 }
  - { offsetInCU: 0x7A89, offset: 0x21D68, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV19graphRequestFactorySo010FBSDKGraphhI0_pvM', symObjAddr: 0x8D20, symBinAddr: 0x2C300, symSize: 0x20 }
  - { offsetInCU: 0x7A9C, offset: 0x21D7B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV19graphRequestFactorySo010FBSDKGraphhI0_pvM.resume.0', symObjAddr: 0x8D40, symBinAddr: 0x2C320, symSize: 0x10 }
  - { offsetInCU: 0x7AAF, offset: 0x21D8E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV15internalUtilitySo27FBSDKAppAvailabilityChecker_So0I18URLSchemeProvidingSo15FBSDKURLHostingpvg', symObjAddr: 0x8D50, symBinAddr: 0x2C330, symSize: 0x10 }
  - { offsetInCU: 0x7AC2, offset: 0x21DA1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV15internalUtilitySo27FBSDKAppAvailabilityChecker_So0I18URLSchemeProvidingSo15FBSDKURLHostingpvs', symObjAddr: 0x8D60, symBinAddr: 0x2C340, symSize: 0x20 }
  - { offsetInCU: 0x7AD5, offset: 0x21DB4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV15internalUtilitySo27FBSDKAppAvailabilityChecker_So0I18URLSchemeProvidingSo15FBSDKURLHostingpvM', symObjAddr: 0x8D80, symBinAddr: 0x2C360, symSize: 0x20 }
  - { offsetInCU: 0x7AE8, offset: 0x21DC7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV15internalUtilitySo27FBSDKAppAvailabilityChecker_So0I18URLSchemeProvidingSo15FBSDKURLHostingpvM.resume.0', symObjAddr: 0x8DA0, symBinAddr: 0x2C380, symSize: 0x10 }
  - { offsetInCU: 0x7AFB, offset: 0x21DDA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV13keychainStoreSo013FBSDKKeychainH0_pvg', symObjAddr: 0x8DB0, symBinAddr: 0x2C390, symSize: 0x10 }
  - { offsetInCU: 0x7B0E, offset: 0x21DED, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV13keychainStoreSo013FBSDKKeychainH0_pvs', symObjAddr: 0x8DC0, symBinAddr: 0x2C3A0, symSize: 0x20 }
  - { offsetInCU: 0x7B21, offset: 0x21E00, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV13keychainStoreSo013FBSDKKeychainH0_pvM', symObjAddr: 0x8DE0, symBinAddr: 0x2C3C0, symSize: 0x20 }
  - { offsetInCU: 0x7B34, offset: 0x21E13, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV13keychainStoreSo013FBSDKKeychainH0_pvM.resume.0', symObjAddr: 0x8E00, symBinAddr: 0x2C3E0, symSize: 0x10 }
  - { offsetInCU: 0x7B47, offset: 0x21E26, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV21loginCompleterFactoryAA0chI8Protocol_pvg', symObjAddr: 0x8E10, symBinAddr: 0x2C3F0, symSize: 0x20 }
  - { offsetInCU: 0x7B5A, offset: 0x21E39, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV21loginCompleterFactoryAA0chI8Protocol_pvs', symObjAddr: 0x8E30, symBinAddr: 0x2C410, symSize: 0x30 }
  - { offsetInCU: 0x7B6D, offset: 0x21E4C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV21loginCompleterFactoryAA0chI8Protocol_pvM', symObjAddr: 0x8E60, symBinAddr: 0x2C440, symSize: 0x20 }
  - { offsetInCU: 0x7B80, offset: 0x21E5F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV21loginCompleterFactoryAA0chI8Protocol_pvM.resume.0', symObjAddr: 0x8E80, symBinAddr: 0x2C460, symSize: 0x10 }
  - { offsetInCU: 0x7B93, offset: 0x21E72, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV15profileProvider09FBSDKCoreB016ProfileProviding_pXpvg', symObjAddr: 0x8E90, symBinAddr: 0x2C470, symSize: 0x10 }
  - { offsetInCU: 0x7BA6, offset: 0x21E85, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV15profileProvider09FBSDKCoreB016ProfileProviding_pXpvs', symObjAddr: 0x8EA0, symBinAddr: 0x2C480, symSize: 0x10 }
  - { offsetInCU: 0x7BB9, offset: 0x21E98, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV15profileProvider09FBSDKCoreB016ProfileProviding_pXpvM', symObjAddr: 0x8EB0, symBinAddr: 0x2C490, symSize: 0x20 }
  - { offsetInCU: 0x7BCC, offset: 0x21EAB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV15profileProvider09FBSDKCoreB016ProfileProviding_pXpvM.resume.0', symObjAddr: 0x8ED0, symBinAddr: 0x2C4B0, symSize: 0x10 }
  - { offsetInCU: 0x7BDF, offset: 0x21EBE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV8settings09FBSDKCoreB016SettingsProtocol_pvg', symObjAddr: 0x8EE0, symBinAddr: 0x2C4C0, symSize: 0x10 }
  - { offsetInCU: 0x7BF2, offset: 0x21ED1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV8settings09FBSDKCoreB016SettingsProtocol_pvs', symObjAddr: 0x8EF0, symBinAddr: 0x2C4D0, symSize: 0x20 }
  - { offsetInCU: 0x7C05, offset: 0x21EE4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV8settings09FBSDKCoreB016SettingsProtocol_pvM', symObjAddr: 0x8F10, symBinAddr: 0x2C4F0, symSize: 0x20 }
  - { offsetInCU: 0x7C18, offset: 0x21EF7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV8settings09FBSDKCoreB016SettingsProtocol_pvM.resume.0', symObjAddr: 0x8F30, symBinAddr: 0x2C510, symSize: 0x10 }
  - { offsetInCU: 0x7C2B, offset: 0x21F0A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV9urlOpenerSo14FBSDKURLOpener_pvg', symObjAddr: 0x8F40, symBinAddr: 0x2C520, symSize: 0x10 }
  - { offsetInCU: 0x7C3E, offset: 0x21F1D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV9urlOpenerSo14FBSDKURLOpener_pvs', symObjAddr: 0x8F50, symBinAddr: 0x2C530, symSize: 0x20 }
  - { offsetInCU: 0x7C51, offset: 0x21F30, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV9urlOpenerSo14FBSDKURLOpener_pvM', symObjAddr: 0x8F70, symBinAddr: 0x2C550, symSize: 0x20 }
  - { offsetInCU: 0x7C64, offset: 0x21F43, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV9urlOpenerSo14FBSDKURLOpener_pvM.resume.0', symObjAddr: 0x8F90, symBinAddr: 0x2C570, symSize: 0x10 }
  - { offsetInCU: 0x7CA6, offset: 0x21F85, size: 0x8, addend: 0x0, symName: '_$sSo16FBSDKAccessTokenC11tokenString11permissions19declinedPermissions07expiredG05appID04userJ014expirationDate07refreshM0020dataAccessExpirationM0ABSS_SaySSGA2LS2S10Foundation0M0VSgA2PtcfcTO', symObjAddr: 0x91C0, symBinAddr: 0x2C770, symSize: 0x200 }
  - { offsetInCU: 0x7CC5, offset: 0x21FA4, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV7_insert2at3key5valueys10_HashTableV6BucketV_xnq_ntFSS_ypTg5', symObjAddr: 0x93C0, symBinAddr: 0x2C970, symSize: 0x60 }
  - { offsetInCU: 0x7D31, offset: 0x22010, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV7_insert2at3key5valueys10_HashTableV6BucketV_xnq_ntFSo26FBSDKAppEventParameterNamea_ypTg5', symObjAddr: 0x9420, symBinAddr: 0x2C9D0, symSize: 0x60 }
  - { offsetInCU: 0x7D91, offset: 0x22070, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV7_insert2at3key5valueys10_HashTableV6BucketV_xnq_ntFSS_SSTg5', symObjAddr: 0x9480, symBinAddr: 0x2CA30, symSize: 0x50 }
  - { offsetInCU: 0x7E34, offset: 0x22113, size: 0x8, addend: 0x0, symName: '_$sSh8_VariantV6insertySb8inserted_x17memberAfterInserttxnF13FBSDKLoginKit12FBPermissionC_Tg5', symObjAddr: 0x99A0, symBinAddr: 0x2CF30, symSize: 0x350 }
  - { offsetInCU: 0x7EFE, offset: 0x221DD, size: 0x8, addend: 0x0, symName: '_$sSh8_VariantV6insertySb8inserted_x17memberAfterInserttxnFSS_Tg5', symObjAddr: 0x9CF0, symBinAddr: 0x2D280, symSize: 0x210 }
  - { offsetInCU: 0x7FCB, offset: 0x222AA, size: 0x8, addend: 0x0, symName: '_$sSh8_VariantV6insertySb8inserted_x17memberAfterInserttxnF12FBSDKCoreKit10PermissionO_Tg5', symObjAddr: 0x9F00, symBinAddr: 0x2D490, symSize: 0x240 }
  - { offsetInCU: 0x8023, offset: 0x22302, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV9insertNew_2at8isUniqueyxn_s10_HashTableV6BucketVSbtF13FBSDKLoginKit12FBPermissionC_Tg5', symObjAddr: 0xA350, symBinAddr: 0x2D8E0, symSize: 0x1C0 }
  - { offsetInCU: 0x80A3, offset: 0x22382, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV9insertNew_2at8isUniqueyxn_s10_HashTableV6BucketVSbtFSS_Tg5', symObjAddr: 0xA510, symBinAddr: 0x2DAA0, symSize: 0x190 }
  - { offsetInCU: 0x8145, offset: 0x22424, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV9insertNew_2at8isUniqueyxn_s10_HashTableV6BucketVSbtF12FBSDKCoreKit10PermissionO_Tg5', symObjAddr: 0xA6A0, symBinAddr: 0x2DC30, symSize: 0x200 }
  - { offsetInCU: 0x8191, offset: 0x22470, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV4copyyyF13FBSDKLoginKit12FBPermissionC_Tg5', symObjAddr: 0xA8A0, symBinAddr: 0x2DE30, symSize: 0x1F0 }
  - { offsetInCU: 0x81FC, offset: 0x224DB, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV4copyyyFSS_Tg5', symObjAddr: 0xAA90, symBinAddr: 0x2E020, symSize: 0x200 }
  - { offsetInCU: 0x8267, offset: 0x22546, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV4copyyyF12FBSDKCoreKit10PermissionO_Tg5', symObjAddr: 0xAC90, symBinAddr: 0x2E220, symSize: 0x260 }
  - { offsetInCU: 0x82AC, offset: 0x2258B, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV13copyAndResize8capacityySi_tF13FBSDKLoginKit12FBPermissionC_Tg5', symObjAddr: 0xAEF0, symBinAddr: 0x2E480, symSize: 0x2B0 }
  - { offsetInCU: 0x8348, offset: 0x22627, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV13copyAndResize8capacityySi_tFSS_Tg5', symObjAddr: 0xB1A0, symBinAddr: 0x2E730, symSize: 0x2F0 }
  - { offsetInCU: 0x83E8, offset: 0x226C7, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV13copyAndResize8capacityySi_tF12FBSDKCoreKit10PermissionO_Tg5', symObjAddr: 0xB490, symBinAddr: 0x2EA20, symSize: 0x330 }
  - { offsetInCU: 0x8451, offset: 0x22730, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV6resize8capacityySi_tF13FBSDKLoginKit12FBPermissionC_Tg5', symObjAddr: 0xB7C0, symBinAddr: 0x2ED50, symSize: 0x300 }
  - { offsetInCU: 0x8513, offset: 0x227F2, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV6resize8capacityySi_tFSS_Tg5', symObjAddr: 0xBAC0, symBinAddr: 0x2F050, symSize: 0x330 }
  - { offsetInCU: 0x85E9, offset: 0x228C8, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV6resize8capacityySi_tF12FBSDKCoreKit10PermissionO_Tg5', symObjAddr: 0xBDF0, symBinAddr: 0x2F380, symSize: 0x3A0 }
  - { offsetInCU: 0x8664, offset: 0x22943, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV16_unsafeInsertNewyyxnF13FBSDKLoginKit12FBPermissionC_Tg5', symObjAddr: 0xC190, symBinAddr: 0x2F720, symSize: 0x60 }
  - { offsetInCU: 0x86BD, offset: 0x2299C, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV12mutatingFind_8isUniques10_HashTableV6BucketV6bucket_Sb5foundtx_SbtFSo26FBSDKAppEventParameterNamea_ypTg5', symObjAddr: 0xC1F0, symBinAddr: 0x2F780, symSize: 0xB0 }
  - { offsetInCU: 0x8709, offset: 0x229E8, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV20_copyOrMoveAndResize8capacity12moveElementsySi_SbtFSo26FBSDKAppEventParameterNamea_ypTg5', symObjAddr: 0xC390, symBinAddr: 0x2F920, symSize: 0x3E0 }
  - { offsetInCU: 0x87DF, offset: 0x22ABE, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV20_copyOrMoveAndResize8capacity12moveElementsySi_SbtFSS_ypTg5', symObjAddr: 0xC770, symBinAddr: 0x2FD00, symSize: 0x3C0 }
  - { offsetInCU: 0x88B5, offset: 0x22B94, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV20_copyOrMoveAndResize8capacity12moveElementsySi_SbtFSS_SSTg5', symObjAddr: 0xCB30, symBinAddr: 0x300C0, symSize: 0x3D0 }
  - { offsetInCU: 0x8991, offset: 0x22C70, size: 0x8, addend: 0x0, symName: '_$sSh8_VariantV12intersectionys10_NativeSetVyxGShyxGF13FBSDKLoginKit12FBPermissionC_Tg5', symObjAddr: 0xCF00, symBinAddr: 0x30490, symSize: 0x3C0 }
  - { offsetInCU: 0x8AF1, offset: 0x22DD0, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV13extractSubset5using5countAByxGs13_UnsafeBitsetV_SitF13FBSDKLoginKit12FBPermissionC_Tg5', symObjAddr: 0xDD30, symBinAddr: 0x312C0, symSize: 0x2A0 }
  - { offsetInCU: 0x8B9A, offset: 0x22E79, size: 0x8, addend: 0x0, symName: '_$sShyxSh5IndexVyx_Gcig13FBSDKLoginKit12FBPermissionC_Tg5', symObjAddr: 0xDFD0, symBinAddr: 0x31560, symSize: 0x300 }
  - { offsetInCU: 0x8C2A, offset: 0x22F09, size: 0x8, addend: 0x0, symName: '_$sSTsE21_copySequenceContents12initializing8IteratorQz_SitSry7ElementQzG_tFShySSG_Tg5', symObjAddr: 0xE2D0, symBinAddr: 0x31860, symSize: 0x200 }
  - { offsetInCU: 0x8D67, offset: 0x23046, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5logIn33_C218275A97333B874EDDFE627110566CLL11permissions7handleryShyAA12FBPermissionCG_yAA0cdC6ResultCSg_s5Error_pSgtcSgtFTf4dnn_n', symObjAddr: 0xEA40, symBinAddr: 0x31F50, symSize: 0x200 }
  - { offsetInCU: 0x8E92, offset: 0x23171, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC7canOpen_3for17sourceApplication10annotationSb10Foundation3URLV_So13UIApplicationCSgSSSgypSgtFTf4nddnn_n', symObjAddr: 0xF940, symBinAddr: 0x32E50, symSize: 0x190 }
  - { offsetInCU: 0x8FE1, offset: 0x232C0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC11application_4open17sourceApplication10annotationSbSo13UIApplicationCSg_10Foundation3URLVSgSSSgypSgtFTf4dndnn_n', symObjAddr: 0xFAD0, symBinAddr: 0x32FE0, symSize: 0x5A0 }
  - { offsetInCU: 0x4D, offset: 0x23615, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO10identifierSo013FBSDKAppEventF4NameavpZ', symObjAddr: 0x67A0, symBinAddr: 0x60120, symSize: 0x0 }
  - { offsetInCU: 0x67, offset: 0x2362F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO9timestampSo013FBSDKAppEventF4NameavpZ', symObjAddr: 0x67A8, symBinAddr: 0x60128, symSize: 0x0 }
  - { offsetInCU: 0x81, offset: 0x23649, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO6resultSo013FBSDKAppEventF4NameavpZ', symObjAddr: 0x67B0, symBinAddr: 0x60130, symSize: 0x0 }
  - { offsetInCU: 0x9B, offset: 0x23663, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO10authMethodSo013FBSDKAppEventF4NameavpZ', symObjAddr: 0x67B8, symBinAddr: 0x60138, symSize: 0x0 }
  - { offsetInCU: 0xB5, offset: 0x2367D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO9errorCodeSo013FBSDKAppEventF4NameavpZ', symObjAddr: 0x67C0, symBinAddr: 0x60140, symSize: 0x0 }
  - { offsetInCU: 0xCF, offset: 0x23697, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO12errorMessageSo013FBSDKAppEventF4NameavpZ', symObjAddr: 0x67C8, symBinAddr: 0x60148, symSize: 0x0 }
  - { offsetInCU: 0xE9, offset: 0x236B1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO6extrasSo013FBSDKAppEventF4NameavpZ', symObjAddr: 0x67D0, symBinAddr: 0x60150, symSize: 0x0 }
  - { offsetInCU: 0x103, offset: 0x236CB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO12loggingTokenSo013FBSDKAppEventF4NameavpZ', symObjAddr: 0x67D8, symBinAddr: 0x60158, symSize: 0x0 }
  - { offsetInCU: 0x11D, offset: 0x236E5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO19declinedPermissionsSo013FBSDKAppEventF4NameavpZ', symObjAddr: 0x67E0, symBinAddr: 0x60160, symSize: 0x0 }
  - { offsetInCU: 0x138, offset: 0x23700, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC19defaultDependenciesAC04TypeG0VSgvpZ', symObjAddr: 0x1A858, symBinAddr: 0x621D8, symSize: 0x0 }
  - { offsetInCU: 0x1C7, offset: 0x2378F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC22configuredDependenciesAC04TypeG0VSgvpZ', symObjAddr: 0x1A880, symBinAddr: 0x62200, symSize: 0x0 }
  - { offsetInCU: 0x4AF, offset: 0x23A77, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO10identifier_WZ', symObjAddr: 0x1260, symBinAddr: 0x35220, symSize: 0x30 }
  - { offsetInCU: 0x4C8, offset: 0x23A90, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO9timestamp_WZ', symObjAddr: 0x1290, symBinAddr: 0x35250, symSize: 0x30 }
  - { offsetInCU: 0x4E1, offset: 0x23AA9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO6result_WZ', symObjAddr: 0x12C0, symBinAddr: 0x35280, symSize: 0x30 }
  - { offsetInCU: 0x4FA, offset: 0x23AC2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO10authMethod_WZ', symObjAddr: 0x12F0, symBinAddr: 0x352B0, symSize: 0x30 }
  - { offsetInCU: 0x513, offset: 0x23ADB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO9errorCode_WZ', symObjAddr: 0x1320, symBinAddr: 0x352E0, symSize: 0x30 }
  - { offsetInCU: 0x52C, offset: 0x23AF4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO12errorMessage_WZ', symObjAddr: 0x1350, symBinAddr: 0x35310, symSize: 0x30 }
  - { offsetInCU: 0x545, offset: 0x23B0D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO6extras_WZ', symObjAddr: 0x1380, symBinAddr: 0x35340, symSize: 0x30 }
  - { offsetInCU: 0x55E, offset: 0x23B26, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO12loggingToken_WZ', symObjAddr: 0x13B0, symBinAddr: 0x35370, symSize: 0x30 }
  - { offsetInCU: 0x577, offset: 0x23B3F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO19declinedPermissions_WZ', symObjAddr: 0x13E0, symBinAddr: 0x353A0, symSize: 0x30 }
  - { offsetInCU: 0x6E0, offset: 0x23CA8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC21heartbeatTimerDidFireyyFTo', symObjAddr: 0x40D0, symBinAddr: 0x38090, symSize: 0xD0 }
  - { offsetInCU: 0x748, offset: 0x23D10, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC14getClientState20authenticationMethod08existingH06loggerSSSgAH_SDySSypGSgACSgtFZTf4nnnd_n', symObjAddr: 0x5C30, symBinAddr: 0x39BA0, symSize: 0x3D0 }
  - { offsetInCU: 0xC6F, offset: 0x24237, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC19defaultDependencies_WZ', symObjAddr: 0x4670, symBinAddr: 0x38630, symSize: 0x70 }
  - { offsetInCU: 0xC8A, offset: 0x24252, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC19defaultDependenciesAC04TypeG0VSgvau', symObjAddr: 0x46E0, symBinAddr: 0x386A0, symSize: 0x30 }
  - { offsetInCU: 0xCC1, offset: 0x24289, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC22configuredDependencies_WZ', symObjAddr: 0x47B0, symBinAddr: 0x38770, symSize: 0x30 }
  - { offsetInCU: 0xCDC, offset: 0x242A4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC22configuredDependenciesAC04TypeG0VSgvau', symObjAddr: 0x47E0, symBinAddr: 0x387A0, symSize: 0x30 }
  - { offsetInCU: 0xD2B, offset: 0x242F3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerCAA15DependentAsTypeA2aDP22configuredDependencies0hJ0QzSgvsZTW', symObjAddr: 0x4990, symBinAddr: 0x38950, symSize: 0x80 }
  - { offsetInCU: 0xD60, offset: 0x24328, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerCAA15DependentAsTypeA2aDP22configuredDependencies0hJ0QzSgvMZTW', symObjAddr: 0x4A10, symBinAddr: 0x389D0, symSize: 0x60 }
  - { offsetInCU: 0xDB2, offset: 0x2437A, size: 0x8, addend: 0x0, symName: '_$sxq_xq_Iegnnrr_x3key_q_5valuetx_q_tIegnr_SHRzr0_lTRSS_ypTg575$sSD5merge_16uniquingKeysWithySDyxq_Gn_q_q__q_tKXEtKFx_q_tx_q_tcfU_SS_ypTG5Tf3nnpf_n', symObjAddr: 0x4AE0, symBinAddr: 0x38AA0, symSize: 0x40 }
  - { offsetInCU: 0xFA9, offset: 0x24571, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginEventLogging_pWOb', symObjAddr: 0x5BE0, symBinAddr: 0x39B80, symSize: 0x20 }
  - { offsetInCU: 0xFBC, offset: 0x24584, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginEventLogging_pWOc', symObjAddr: 0x62B0, symBinAddr: 0x3A120, symSize: 0x30 }
  - { offsetInCU: 0xFCF, offset: 0x24597, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC16TypeDependenciesVSgWOf', symObjAddr: 0x62E0, symBinAddr: 0x3A150, symSize: 0x40 }
  - { offsetInCU: 0xFE2, offset: 0x245AA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerCMa', symObjAddr: 0x6320, symBinAddr: 0x3A190, symSize: 0x20 }
  - { offsetInCU: 0xFF5, offset: 0x245BD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC16TypeDependenciesVwCP', symObjAddr: 0x6370, symBinAddr: 0x3A1E0, symSize: 0x30 }
  - { offsetInCU: 0x1008, offset: 0x245D0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC16TypeDependenciesVwxx', symObjAddr: 0x63A0, symBinAddr: 0x3A210, symSize: 0x10 }
  - { offsetInCU: 0x101B, offset: 0x245E3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC16TypeDependenciesVwcp', symObjAddr: 0x63B0, symBinAddr: 0x3A220, symSize: 0x30 }
  - { offsetInCU: 0x102E, offset: 0x245F6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC16TypeDependenciesVwca', symObjAddr: 0x63E0, symBinAddr: 0x3A250, symSize: 0x20 }
  - { offsetInCU: 0x1041, offset: 0x24609, size: 0x8, addend: 0x0, symName: ___swift_memcpy40_8, symObjAddr: 0x6530, symBinAddr: 0x3A270, symSize: 0x20 }
  - { offsetInCU: 0x1054, offset: 0x2461C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC16TypeDependenciesVwta', symObjAddr: 0x6550, symBinAddr: 0x3A290, symSize: 0x40 }
  - { offsetInCU: 0x1067, offset: 0x2462F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC16TypeDependenciesVwet', symObjAddr: 0x6590, symBinAddr: 0x3A2D0, symSize: 0x40 }
  - { offsetInCU: 0x107A, offset: 0x24642, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC16TypeDependenciesVwst', symObjAddr: 0x65D0, symBinAddr: 0x3A310, symSize: 0x40 }
  - { offsetInCU: 0x108D, offset: 0x24655, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC16TypeDependenciesVMa', symObjAddr: 0x6610, symBinAddr: 0x3A350, symSize: 0x10 }
  - { offsetInCU: 0x12C4, offset: 0x2488C, size: 0x8, addend: 0x0, symName: '_$sSTsE6reduceyqd__qd___qd__qd___7ElementQztKXEtKlFShy13FBSDKLoginKit12FBPermissionCG_SSTg504$s13c61Kit18LoginManagerLoggerC12startSession3foryAA0cD0C_tFS2S_AA12E7CtXEfU_Tf1ncn_n', symObjAddr: 0x1C90, symBinAddr: 0x35C50, symSize: 0x430 }
  - { offsetInCU: 0x14E8, offset: 0x24AB0, size: 0x8, addend: 0x0, symName: '_$ss17_dictionaryUpCastySDyq0_q1_GSDyxq_GSHRzSHR0_r2_lFSS_SbSSypTg5', symObjAddr: 0x41A0, symBinAddr: 0x38160, symSize: 0x3D0 }
  - { offsetInCU: 0x1816, offset: 0x24DDE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC12startSession3foryAA0cD0C_tF', symObjAddr: 0x0, symBinAddr: 0x34000, symSize: 0x3B0 }
  - { offsetInCU: 0x1A55, offset: 0x2501D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC03endC06result5erroryAA0cdC6ResultCSg_So7NSErrorCSgtF', symObjAddr: 0x3B0, symBinAddr: 0x343B0, symSize: 0x560 }
  - { offsetInCU: 0x1CB1, offset: 0x25279, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC10endSessionyyF', symObjAddr: 0x910, symBinAddr: 0x34910, symSize: 0x190 }
  - { offsetInCU: 0x1D31, offset: 0x252F9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC04postC9HeartbeatyyF', symObjAddr: 0xAA0, symBinAddr: 0x34AA0, symSize: 0x50 }
  - { offsetInCU: 0x1D58, offset: 0x25320, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC14getClientState20authenticationMethod08existingH06loggerSSSgAH_SDySSypGSgACSgtFZ', symObjAddr: 0xB30, symBinAddr: 0x34AF0, symSize: 0x10 }
  - { offsetInCU: 0x1D9B, offset: 0x25363, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC31willAttemptAppSwitchingBehavior9urlSchemeySS_tF', symObjAddr: 0xB40, symBinAddr: 0x34B00, symSize: 0x270 }
  - { offsetInCU: 0x1EF0, offset: 0x254B8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC5start20authenticationMethodySS_tF', symObjAddr: 0xDB0, symBinAddr: 0x34D70, symSize: 0x90 }
  - { offsetInCU: 0x1F2C, offset: 0x254F4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC10identifierSSSgvg', symObjAddr: 0xE40, symBinAddr: 0x34E00, symSize: 0x40 }
  - { offsetInCU: 0x1F3F, offset: 0x25507, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC10identifierSSSgvs', symObjAddr: 0xE80, symBinAddr: 0x34E40, symSize: 0x40 }
  - { offsetInCU: 0x1F52, offset: 0x2551A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC10identifierSSSgvM', symObjAddr: 0xEC0, symBinAddr: 0x34E80, symSize: 0x30 }
  - { offsetInCU: 0x1F65, offset: 0x2552D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC6extrasSDySSypGvg', symObjAddr: 0xEF0, symBinAddr: 0x34EB0, symSize: 0x30 }
  - { offsetInCU: 0x1F78, offset: 0x25540, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC6extrasSDySSypGvs', symObjAddr: 0xF20, symBinAddr: 0x34EE0, symSize: 0x40 }
  - { offsetInCU: 0x1F8B, offset: 0x25553, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC6extrasSDySSypGvM', symObjAddr: 0xF60, symBinAddr: 0x34F20, symSize: 0x30 }
  - { offsetInCU: 0x1F9E, offset: 0x25566, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC10lastResultSSvg', symObjAddr: 0xF90, symBinAddr: 0x34F50, symSize: 0x40 }
  - { offsetInCU: 0x1FB1, offset: 0x25579, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC10lastResultSSvs', symObjAddr: 0xFD0, symBinAddr: 0x34F90, symSize: 0x40 }
  - { offsetInCU: 0x1FC4, offset: 0x2558C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC10lastResultSSvM', symObjAddr: 0x1010, symBinAddr: 0x34FD0, symSize: 0x30 }
  - { offsetInCU: 0x1FD7, offset: 0x2559F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC9lastErrorSo7NSErrorCSgvg', symObjAddr: 0x1040, symBinAddr: 0x35000, symSize: 0x40 }
  - { offsetInCU: 0x1FEA, offset: 0x255B2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC9lastErrorSo7NSErrorCSgvs', symObjAddr: 0x1080, symBinAddr: 0x35040, symSize: 0x40 }
  - { offsetInCU: 0x1FFD, offset: 0x255C5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC9lastErrorSo7NSErrorCSgvM', symObjAddr: 0x10C0, symBinAddr: 0x35080, symSize: 0x30 }
  - { offsetInCU: 0x2010, offset: 0x255D8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC10authMethodSSSgvg', symObjAddr: 0x10F0, symBinAddr: 0x350B0, symSize: 0x40 }
  - { offsetInCU: 0x2023, offset: 0x255EB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC10authMethodSSSgvs', symObjAddr: 0x1130, symBinAddr: 0x350F0, symSize: 0x40 }
  - { offsetInCU: 0x2036, offset: 0x255FE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC10authMethodSSSgvM', symObjAddr: 0x1170, symBinAddr: 0x35130, symSize: 0x30 }
  - { offsetInCU: 0x2049, offset: 0x25611, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC10authMethodSSSgvM.resume.0', symObjAddr: 0x11A0, symBinAddr: 0x35160, symSize: 0x10 }
  - { offsetInCU: 0x205C, offset: 0x25624, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC12loggingTokenSSSgvg', symObjAddr: 0x11B0, symBinAddr: 0x35170, symSize: 0x40 }
  - { offsetInCU: 0x206F, offset: 0x25637, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC12loggingTokenSSSgvs', symObjAddr: 0x11F0, symBinAddr: 0x351B0, symSize: 0x40 }
  - { offsetInCU: 0x2082, offset: 0x2564A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC12loggingTokenSSSgvM', symObjAddr: 0x1230, symBinAddr: 0x351F0, symSize: 0x30 }
  - { offsetInCU: 0x20A7, offset: 0x2566F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC10parameters8trackingACSgSDySSypGSg_AA0C8TrackingOtcfC', symObjAddr: 0x1410, symBinAddr: 0x353D0, symSize: 0x800 }
  - { offsetInCU: 0x21F7, offset: 0x257BF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC12loggingToken8trackingACSgSSSg_AA0C8TrackingOtcfC', symObjAddr: 0x1C10, symBinAddr: 0x35BD0, symSize: 0x50 }
  - { offsetInCU: 0x221F, offset: 0x257E7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC12loggingToken8trackingACSgSSSg_AA0C8TrackingOtcfc', symObjAddr: 0x1C60, symBinAddr: 0x35C20, symSize: 0x30 }
  - { offsetInCU: 0x22A2, offset: 0x2586A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC21parametersForNewEventSDySo08FBSDKAppI13ParameterNameaypGyF', symObjAddr: 0x2130, symBinAddr: 0x360F0, symSize: 0x950 }
  - { offsetInCU: 0x25E4, offset: 0x25BAC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC8logEvent_6paramsySo08FBSDKAppG4Namea_SDySo0ig9ParameterJ0aypGSgtF', symObjAddr: 0x2A80, symBinAddr: 0x36A40, symSize: 0x3A0 }
  - { offsetInCU: 0x26E7, offset: 0x25CAF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC8logEvent_6result5errorySo08FBSDKAppG4Namea_SSSo7NSErrorCSgtF', symObjAddr: 0x2E20, symBinAddr: 0x36DE0, symSize: 0x11F0 }
  - { offsetInCU: 0x2B06, offset: 0x260CE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC21heartbeatTimerDidFireyyF', symObjAddr: 0x4010, symBinAddr: 0x37FD0, symSize: 0xC0 }
  - { offsetInCU: 0x2B80, offset: 0x26148, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerCfd', symObjAddr: 0x4570, symBinAddr: 0x38530, symSize: 0x40 }
  - { offsetInCU: 0x2BAD, offset: 0x26175, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerCfD', symObjAddr: 0x45B0, symBinAddr: 0x38570, symSize: 0x50 }
  - { offsetInCU: 0x2BE2, offset: 0x261AA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC16TypeDependenciesV05eventE0AA0C12EventLogging_pvg', symObjAddr: 0x4600, symBinAddr: 0x385C0, symSize: 0x10 }
  - { offsetInCU: 0x2BF5, offset: 0x261BD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC16TypeDependenciesV05eventE0AA0C12EventLogging_pvs', symObjAddr: 0x4610, symBinAddr: 0x385D0, symSize: 0x30 }
  - { offsetInCU: 0x2C08, offset: 0x261D0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC16TypeDependenciesV05eventE0AA0C12EventLogging_pvM', symObjAddr: 0x4640, symBinAddr: 0x38600, symSize: 0x10 }
  - { offsetInCU: 0x2C1B, offset: 0x261E3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC16TypeDependenciesV05eventE0AA0C12EventLogging_pvM.resume.0', symObjAddr: 0x4650, symBinAddr: 0x38610, symSize: 0x10 }
  - { offsetInCU: 0x2C34, offset: 0x261FC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC16TypeDependenciesV05eventE0AeA0C12EventLogging_p_tcfC', symObjAddr: 0x4660, symBinAddr: 0x38620, symSize: 0x10 }
  - { offsetInCU: 0x2C47, offset: 0x2620F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC19defaultDependenciesAC04TypeG0VSgvMZ', symObjAddr: 0x4750, symBinAddr: 0x38710, symSize: 0x60 }
  - { offsetInCU: 0x2C66, offset: 0x2622E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC22configuredDependenciesAC04TypeG0VSgvMZ', symObjAddr: 0x4910, symBinAddr: 0x388D0, symSize: 0x60 }
  - { offsetInCU: 0x2CA3, offset: 0x2626B, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV4copyyyFSo26FBSDKAppEventParameterNamea_ypTg5', symObjAddr: 0x4FA0, symBinAddr: 0x38F60, symSize: 0x240 }
  - { offsetInCU: 0x2D21, offset: 0x262E9, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV4copyyyFSS_ypTg5', symObjAddr: 0x51E0, symBinAddr: 0x391A0, symSize: 0x260 }
  - { offsetInCU: 0x2DAB, offset: 0x26373, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV4copyyyFSS_SSTg5', symObjAddr: 0x5440, symBinAddr: 0x39400, symSize: 0x220 }
  - { offsetInCU: 0x2E6D, offset: 0x26435, size: 0x8, addend: 0x0, symName: '_$sSD11removeValue6forKeyq_Sgx_tFSS_ypTg5', symObjAddr: 0x5660, symBinAddr: 0x39620, symSize: 0xE0 }
  - { offsetInCU: 0x2F1B, offset: 0x264E3, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV7_delete2atys10_HashTableV6BucketV_tFSo26FBSDKAppEventParameterNamea_ypTg5', symObjAddr: 0x5740, symBinAddr: 0x39700, symSize: 0x260 }
  - { offsetInCU: 0x2FBA, offset: 0x26582, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV7_delete2atys10_HashTableV6BucketV_tFSS_ypTg5', symObjAddr: 0x59A0, symBinAddr: 0x39960, symSize: 0x220 }
  - { offsetInCU: 0x3047, offset: 0x2660F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC12loggingToken8trackingACSgSSSg_AA0C8TrackingOtcfcTf4gnn_n', symObjAddr: 0x60C0, symBinAddr: 0x39F70, symSize: 0x180 }
  - { offsetInCU: 0xD8, offset: 0x26778, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC5tokenSo16FBSDKAccessTokenCSgvgTo', symObjAddr: 0x130, symBinAddr: 0x3A5A0, symSize: 0x20 }
  - { offsetInCU: 0x127, offset: 0x267C7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC19authenticationTokenSo019FBSDKAuthenticationG0CSgvgTo', symObjAddr: 0x180, symBinAddr: 0x3A5F0, symSize: 0x20 }
  - { offsetInCU: 0x176, offset: 0x26816, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC11isCancelledSbvgTo', symObjAddr: 0x1D0, symBinAddr: 0x3A640, symSize: 0x20 }
  - { offsetInCU: 0x1ED, offset: 0x2688D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC13loggingExtrasSDySSypGvgTo', symObjAddr: 0x2E0, symBinAddr: 0x3A750, symSize: 0x70 }
  - { offsetInCU: 0x23F, offset: 0x268DF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC13loggingExtrasSDySSypGvsTo', symObjAddr: 0x390, symBinAddr: 0x3A800, symSize: 0x70 }
  - { offsetInCU: 0x27E, offset: 0x2691E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC5token19authenticationToken11isCancelled18grantedPermissions08declinedL0ACSo011FBSDKAccessH0CSg_So019FBSDKAuthenticationH0CSgSbShySSGAOtcfcTo', symObjAddr: 0x4E0, symBinAddr: 0x3A920, symSize: 0xF0 }
  - { offsetInCU: 0x2D0, offset: 0x26970, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC15addLoggingExtra_6forKeyyyp_SStFTo', symObjAddr: 0x5D0, symBinAddr: 0x3AA10, symSize: 0xF0 }
  - { offsetInCU: 0x342, offset: 0x269E2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultCACycfcTo', symObjAddr: 0x710, symBinAddr: 0x3AB50, symSize: 0x30 }
  - { offsetInCU: 0x3CE, offset: 0x26A6E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultCfETo', symObjAddr: 0x770, symBinAddr: 0x3ABB0, symSize: 0x70 }
  - { offsetInCU: 0x4AA, offset: 0x26B4A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultCMa', symObjAddr: 0x950, symBinAddr: 0x3AD90, symSize: 0x20 }
  - { offsetInCU: 0x4BD, offset: 0x26B5D, size: 0x8, addend: 0x0, symName: ___swift_destroy_boxed_opaque_existential_0, symObjAddr: 0x9A0, symBinAddr: 0x3ADE0, symSize: 0x30 }
  - { offsetInCU: 0x6B4, offset: 0x26D54, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC5token19authenticationToken11isCancelled18grantedPermissions08declinedL0ACSo011FBSDKAccessH0CSg_So019FBSDKAuthenticationH0CSgSbShySSGAOtcfC', symObjAddr: 0x0, symBinAddr: 0x3A470, symSize: 0xB0 }
  - { offsetInCU: 0x725, offset: 0x26DC5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC15addLoggingExtra_6forKeyyyp_SStF', symObjAddr: 0xB0, symBinAddr: 0x3A520, symSize: 0x80 }
  - { offsetInCU: 0x772, offset: 0x26E12, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC5tokenSo16FBSDKAccessTokenCSgvg', symObjAddr: 0x150, symBinAddr: 0x3A5C0, symSize: 0x30 }
  - { offsetInCU: 0x79F, offset: 0x26E3F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC19authenticationTokenSo019FBSDKAuthenticationG0CSgvg', symObjAddr: 0x1A0, symBinAddr: 0x3A610, symSize: 0x30 }
  - { offsetInCU: 0x7CC, offset: 0x26E6C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC11isCancelledSbvg', symObjAddr: 0x1F0, symBinAddr: 0x3A660, symSize: 0x20 }
  - { offsetInCU: 0x7E7, offset: 0x26E87, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC18grantedPermissionsShySSGvg', symObjAddr: 0x230, symBinAddr: 0x3A6A0, symSize: 0x20 }
  - { offsetInCU: 0x808, offset: 0x26EA8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC19declinedPermissionsShySSGvg', symObjAddr: 0x2C0, symBinAddr: 0x3A730, symSize: 0x20 }
  - { offsetInCU: 0x83B, offset: 0x26EDB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC13loggingExtrasSDySSypGvg', symObjAddr: 0x350, symBinAddr: 0x3A7C0, symSize: 0x40 }
  - { offsetInCU: 0x878, offset: 0x26F18, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC5token19authenticationToken11isCancelled18grantedPermissions08declinedL0ACSo011FBSDKAccessH0CSg_So019FBSDKAuthenticationH0CSgSbShySSGAOtcfc', symObjAddr: 0x400, symBinAddr: 0x3A870, symSize: 0xB0 }
  - { offsetInCU: 0x8F4, offset: 0x26F94, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultCACycfC', symObjAddr: 0x6C0, symBinAddr: 0x3AB00, symSize: 0x20 }
  - { offsetInCU: 0x907, offset: 0x26FA7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultCACycfc', symObjAddr: 0x6E0, symBinAddr: 0x3AB20, symSize: 0x30 }
  - { offsetInCU: 0x95A, offset: 0x26FFA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultCfD', symObjAddr: 0x740, symBinAddr: 0x3AB80, symSize: 0x30 }
  - { offsetInCU: 0x98D, offset: 0x2702D, size: 0x8, addend: 0x0, symName: '_$sSD8_VariantV8setValue_6forKeyyq_n_xtFSS_SSTg5', symObjAddr: 0x7E0, symBinAddr: 0x3AC20, symSize: 0xC0 }
  - { offsetInCU: 0x9F2, offset: 0x27092, size: 0x8, addend: 0x0, symName: '_$sSD8_VariantV8setValue_6forKeyyq_n_xtFSS_ypTg5', symObjAddr: 0x8A0, symBinAddr: 0x3ACE0, symSize: 0xB0 }
  - { offsetInCU: 0x27, offset: 0x27116, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateO8rawValueACSgSi_tcfC', symObjAddr: 0x0, symBinAddr: 0x3AE10, symSize: 0x20 }
  - { offsetInCU: 0x73, offset: 0x27162, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateOSYAASY8rawValuexSg03RawG0Qz_tcfCTW', symObjAddr: 0x120, symBinAddr: 0x3AF30, symSize: 0x20 }
  - { offsetInCU: 0xA2, offset: 0x27191, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateOSYAASY8rawValue03RawG0QzvgTW', symObjAddr: 0x140, symBinAddr: 0x3AF50, symSize: 0x10 }
  - { offsetInCU: 0xC9, offset: 0x271B8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateOSHAASQWb', symObjAddr: 0x40, symBinAddr: 0x3AE50, symSize: 0x10 }
  - { offsetInCU: 0xDC, offset: 0x271CB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateOACSQAAWl', symObjAddr: 0x50, symBinAddr: 0x3AE60, symSize: 0x30 }
  - { offsetInCU: 0x10D, offset: 0x271FC, size: 0x8, addend: 0x0, symName: ___swift_memcpy1_1, symObjAddr: 0x150, symBinAddr: 0x3AF60, symSize: 0x10 }
  - { offsetInCU: 0x120, offset: 0x2720F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateOwet', symObjAddr: 0x170, symBinAddr: 0x3AF70, symSize: 0x80 }
  - { offsetInCU: 0x133, offset: 0x27222, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateOwst', symObjAddr: 0x1F0, symBinAddr: 0x3AFF0, symSize: 0xD0 }
  - { offsetInCU: 0x146, offset: 0x27235, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateOwug', symObjAddr: 0x2C0, symBinAddr: 0x3B0C0, symSize: 0x10 }
  - { offsetInCU: 0x159, offset: 0x27248, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateOwup', symObjAddr: 0x2D0, symBinAddr: 0x3B0D0, symSize: 0x10 }
  - { offsetInCU: 0x16C, offset: 0x2725B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateOwui', symObjAddr: 0x2E0, symBinAddr: 0x3B0E0, symSize: 0x10 }
  - { offsetInCU: 0x17F, offset: 0x2726E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateOMa', symObjAddr: 0x2F0, symBinAddr: 0x3B0F0, symSize: 0xA }
  - { offsetInCU: 0x1B5, offset: 0x272A4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x30, symBinAddr: 0x3AE40, symSize: 0x10 }
  - { offsetInCU: 0x257, offset: 0x27346, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateOSHAASH9hashValueSivgTW', symObjAddr: 0x80, symBinAddr: 0x3AE90, symSize: 0x40 }
  - { offsetInCU: 0x2FE, offset: 0x273ED, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateOSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0xC0, symBinAddr: 0x3AED0, symSize: 0x20 }
  - { offsetInCU: 0x34D, offset: 0x2743C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateOSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0xE0, symBinAddr: 0x3AEF0, symSize: 0x40 }
  - { offsetInCU: 0x450, offset: 0x2753F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateO8rawValueACSgSi_tcfC', symObjAddr: 0x0, symBinAddr: 0x3AE10, symSize: 0x20 }
  - { offsetInCU: 0x46B, offset: 0x2755A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateO8rawValueSivg', symObjAddr: 0x20, symBinAddr: 0x3AE30, symSize: 0x10 }
  - { offsetInCU: 0x49, offset: 0x275FA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC19defaultDependenciesAC04TypeG0VSgvpZ', symObjAddr: 0x7F00, symBinAddr: 0x62228, symSize: 0x0 }
  - { offsetInCU: 0x130, offset: 0x276E1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC22configuredDependenciesAC04TypeG0VSgvpZ', symObjAddr: 0x7F30, symBinAddr: 0x62258, symSize: 0x0 }
  - { offsetInCU: 0x21F, offset: 0x277D0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC07attemptD04from10completionys5Error_p_ySbctFTo', symObjAddr: 0x250, symBinAddr: 0x3B3B0, symSize: 0x80 }
  - { offsetInCU: 0x372, offset: 0x27923, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC19defaultDependencies_WZ', symObjAddr: 0x400, symBinAddr: 0x3B560, symSize: 0x60 }
  - { offsetInCU: 0x3B5, offset: 0x27966, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC19defaultDependenciesAC04TypeG0VSgvau', symObjAddr: 0x460, symBinAddr: 0x3B5C0, symSize: 0x30 }
  - { offsetInCU: 0x3FE, offset: 0x279AF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC22configuredDependencies_WZ', symObjAddr: 0x540, symBinAddr: 0x3B6A0, symSize: 0x20 }
  - { offsetInCU: 0x417, offset: 0x279C8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC22configuredDependenciesAC04TypeG0VSgvau', symObjAddr: 0x560, symBinAddr: 0x3B6C0, symSize: 0x30 }
  - { offsetInCU: 0x465, offset: 0x27A16, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterCAA15DependentAsTypeA2aDP22configuredDependencies0hJ0QzSgvsZTW', symObjAddr: 0x710, symBinAddr: 0x3B870, symSize: 0x80 }
  - { offsetInCU: 0x499, offset: 0x27A4A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterCAA15DependentAsTypeA2aDP22configuredDependencies0hJ0QzSgvMZTW', symObjAddr: 0x790, symBinAddr: 0x3B8F0, symSize: 0x60 }
  - { offsetInCU: 0x4EE, offset: 0x27A9F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14LoginProviding_pWOc', symObjAddr: 0xC40, symBinAddr: 0x3BDA0, symSize: 0x30 }
  - { offsetInCU: 0x501, offset: 0x27AB2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesVSgWOc', symObjAddr: 0xCC0, symBinAddr: 0x3BDD0, symSize: 0x40 }
  - { offsetInCU: 0x514, offset: 0x27AC5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesVSgWOf', symObjAddr: 0xD40, symBinAddr: 0x3BE10, symSize: 0x40 }
  - { offsetInCU: 0x527, offset: 0x27AD8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesVSgWOh', symObjAddr: 0xD80, symBinAddr: 0x3BE50, symSize: 0x30 }
  - { offsetInCU: 0x53A, offset: 0x27AEB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterCMa', symObjAddr: 0xDB0, symBinAddr: 0x3BE80, symSize: 0x20 }
  - { offsetInCU: 0x54D, offset: 0x27AFE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesVwCP', symObjAddr: 0xE00, symBinAddr: 0x3BED0, symSize: 0x30 }
  - { offsetInCU: 0x560, offset: 0x27B11, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesVwxx', symObjAddr: 0xE30, symBinAddr: 0x3BF00, symSize: 0x10 }
  - { offsetInCU: 0x573, offset: 0x27B24, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesVwcp', symObjAddr: 0xE40, symBinAddr: 0x3BF10, symSize: 0x40 }
  - { offsetInCU: 0x586, offset: 0x27B37, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesVwca', symObjAddr: 0xE80, symBinAddr: 0x3BF50, symSize: 0x30 }
  - { offsetInCU: 0x599, offset: 0x27B4A, size: 0x8, addend: 0x0, symName: ___swift_memcpy48_8, symObjAddr: 0xFE0, symBinAddr: 0x3BF80, symSize: 0x20 }
  - { offsetInCU: 0x5AC, offset: 0x27B5D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesVwta', symObjAddr: 0x1000, symBinAddr: 0x3BFA0, symSize: 0x40 }
  - { offsetInCU: 0x5BF, offset: 0x27B70, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesVwet', symObjAddr: 0x1040, symBinAddr: 0x3BFE0, symSize: 0x40 }
  - { offsetInCU: 0x5D2, offset: 0x27B83, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesVwst', symObjAddr: 0x1080, symBinAddr: 0x3C020, symSize: 0x50 }
  - { offsetInCU: 0x5E5, offset: 0x27B96, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesVMa', symObjAddr: 0x10D0, symBinAddr: 0x3C070, symSize: 0x10 }
  - { offsetInCU: 0x603, offset: 0x27BB4, size: 0x8, addend: 0x0, symName: '_$s10ObjectiveC8ObjCBoolVIeyBy_SbIegy_TRTA', symObjAddr: 0x1100, symBinAddr: 0x3C0A0, symSize: 0x20 }
  - { offsetInCU: 0x62B, offset: 0x27BDC, size: 0x8, addend: 0x0, symName: '_$sSo16FBSDKAccessTokenCMa', symObjAddr: 0x1120, symBinAddr: 0x3C0C0, symSize: 0x30 }
  - { offsetInCU: 0x63E, offset: 0x27BEF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC07attemptD04from10completionys5Error_p_ySbctFyAA0c7ManagerC6ResultCSg_sAG_pSgtcfU_TA', symObjAddr: 0x11A0, symBinAddr: 0x3C110, symSize: 0x60 }
  - { offsetInCU: 0x6A8, offset: 0x27C59, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesVWOh', symObjAddr: 0x1200, symBinAddr: 0x3C170, symSize: 0x20 }
  - { offsetInCU: 0x714, offset: 0x27CC5, size: 0x8, addend: 0x0, symName: '_$sSlsE3mapySayqd__Gqd__7ElementQzKXEKlFShy12FBSDKCoreKit10PermissionOG_SSTg5091$s13FBSDKLoginKit22LoginRecoveryAttempterC07attemptD04from10completionys5Error_p_ySbctFSS09c4B010E52Ocfu_32f5ef5a0b9ade21eb65ffea7b618f60adAJSSTf3nnpk_nTf1cn_n', symObjAddr: 0x0, symBinAddr: 0x3B160, symSize: 0x240 }
  - { offsetInCU: 0xA11, offset: 0x27FC2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC07attemptD04from10completionys5Error_p_ySbctF', symObjAddr: 0x240, symBinAddr: 0x3B3A0, symSize: 0x10 }
  - { offsetInCU: 0xAB9, offset: 0x2806A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterCfd', symObjAddr: 0x2D0, symBinAddr: 0x3B430, symSize: 0x10 }
  - { offsetInCU: 0xADA, offset: 0x2808B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterCfD', symObjAddr: 0x2E0, symBinAddr: 0x3B440, symSize: 0x20 }
  - { offsetInCU: 0xB01, offset: 0x280B2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterCACycfC', symObjAddr: 0x300, symBinAddr: 0x3B460, symSize: 0x20 }
  - { offsetInCU: 0xB14, offset: 0x280C5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterCACycfc', symObjAddr: 0x320, symBinAddr: 0x3B480, symSize: 0x10 }
  - { offsetInCU: 0xB35, offset: 0x280E6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesV13loginProviderAA0C9Providing_pvg', symObjAddr: 0x330, symBinAddr: 0x3B490, symSize: 0x10 }
  - { offsetInCU: 0xB48, offset: 0x280F9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesV13loginProviderAA0C9Providing_pvs', symObjAddr: 0x340, symBinAddr: 0x3B4A0, symSize: 0x30 }
  - { offsetInCU: 0xB5B, offset: 0x2810C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesV13loginProviderAA0C9Providing_pvM', symObjAddr: 0x370, symBinAddr: 0x3B4D0, symSize: 0x10 }
  - { offsetInCU: 0xB6E, offset: 0x2811F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesV13loginProviderAA0C9Providing_pvM.resume.0', symObjAddr: 0x380, symBinAddr: 0x3B4E0, symSize: 0x10 }
  - { offsetInCU: 0xB87, offset: 0x28138, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesV19accessTokenProviderSo011FBSDKAccessI9Providing_pXpvg', symObjAddr: 0x390, symBinAddr: 0x3B4F0, symSize: 0x10 }
  - { offsetInCU: 0xB9A, offset: 0x2814B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesV19accessTokenProviderSo011FBSDKAccessI9Providing_pXpvs', symObjAddr: 0x3A0, symBinAddr: 0x3B500, symSize: 0x10 }
  - { offsetInCU: 0xBAD, offset: 0x2815E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesV19accessTokenProviderSo011FBSDKAccessI9Providing_pXpvM', symObjAddr: 0x3B0, symBinAddr: 0x3B510, symSize: 0x20 }
  - { offsetInCU: 0xBC0, offset: 0x28171, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesV19accessTokenProviderSo011FBSDKAccessI9Providing_pXpvM.resume.0', symObjAddr: 0x3D0, symBinAddr: 0x3B530, symSize: 0x10 }
  - { offsetInCU: 0xBD9, offset: 0x2818A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesV13loginProvider011accessTokenI0AeA0C9Providing_p_So011FBSDKAccesskL0_pXptcfC', symObjAddr: 0x3E0, symBinAddr: 0x3B540, symSize: 0x20 }
  - { offsetInCU: 0xBFE, offset: 0x281AF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC19defaultDependenciesAC04TypeG0VSgvMZ', symObjAddr: 0x4D0, symBinAddr: 0x3B630, symSize: 0x60 }
  - { offsetInCU: 0xC1D, offset: 0x281CE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC19defaultDependenciesAC04TypeG0VSgvMZ.resume.0', symObjAddr: 0x530, symBinAddr: 0x3B690, symSize: 0x10 }
  - { offsetInCU: 0xC31, offset: 0x281E2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC22configuredDependenciesAC04TypeG0VSgvMZ', symObjAddr: 0x690, symBinAddr: 0x3B7F0, symSize: 0x60 }
  - { offsetInCU: 0xC68, offset: 0x28219, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC07attemptD04from10completionys5Error_p_ySbctFTf4dnn_n', symObjAddr: 0xA70, symBinAddr: 0x3BBD0, symSize: 0x1D0 }
  - { offsetInCU: 0x2B, offset: 0x28305, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultO012loginManagerD0AA0cfcD0CSgvg', symObjAddr: 0x240, symBinAddr: 0x3C3F0, symSize: 0x140 }
  - { offsetInCU: 0x10A, offset: 0x283E4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultO6result5errorAcA0c7ManagercD0CSg_s5Error_pSgtcfCTf4nnd_n', symObjAddr: 0x5E0, symBinAddr: 0x3C790, symSize: 0x4F0 }
  - { offsetInCU: 0x5A4, offset: 0x2887E, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_ypTgmq5Tf4g_n', symObjAddr: 0x500, symBinAddr: 0x3C6B0, symSize: 0xE0 }
  - { offsetInCU: 0x791, offset: 0x28A6B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultOwCP', symObjAddr: 0xAD0, symBinAddr: 0x3CC80, symSize: 0x30 }
  - { offsetInCU: 0x7A4, offset: 0x28A7E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultOWOy', symObjAddr: 0xB00, symBinAddr: 0x3CCB0, symSize: 0x50 }
  - { offsetInCU: 0x7B7, offset: 0x28A91, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultOwxx', symObjAddr: 0xB50, symBinAddr: 0x3CD00, symSize: 0x20 }
  - { offsetInCU: 0x7CA, offset: 0x28AA4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultOwcp', symObjAddr: 0xBC0, symBinAddr: 0x3CD20, symSize: 0x60 }
  - { offsetInCU: 0x7DD, offset: 0x28AB7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultOwca', symObjAddr: 0xC20, symBinAddr: 0x3CD80, symSize: 0x70 }
  - { offsetInCU: 0x7F0, offset: 0x28ACA, size: 0x8, addend: 0x0, symName: ___swift_memcpy25_8, symObjAddr: 0xC90, symBinAddr: 0x3CDF0, symSize: 0x20 }
  - { offsetInCU: 0x803, offset: 0x28ADD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultOwta', symObjAddr: 0xCB0, symBinAddr: 0x3CE10, symSize: 0x40 }
  - { offsetInCU: 0x816, offset: 0x28AF0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultOwet', symObjAddr: 0xCF0, symBinAddr: 0x3CE50, symSize: 0x50 }
  - { offsetInCU: 0x829, offset: 0x28B03, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultOwst', symObjAddr: 0xD40, symBinAddr: 0x3CEA0, symSize: 0x50 }
  - { offsetInCU: 0x83C, offset: 0x28B16, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultOwug', symObjAddr: 0xD90, symBinAddr: 0x3CEF0, symSize: 0x20 }
  - { offsetInCU: 0x84F, offset: 0x28B29, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultOwup', symObjAddr: 0xDB0, symBinAddr: 0x3CF10, symSize: 0x10 }
  - { offsetInCU: 0x862, offset: 0x28B3C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultOwui', symObjAddr: 0xDC0, symBinAddr: 0x3CF20, symSize: 0x20 }
  - { offsetInCU: 0x875, offset: 0x28B4F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultOMa', symObjAddr: 0xDE0, symBinAddr: 0x3CF40, symSize: 0x10 }
  - { offsetInCU: 0xB3B, offset: 0x28E15, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultO012loginManagerD0AA0cfcD0CSgvg', symObjAddr: 0x240, symBinAddr: 0x3C3F0, symSize: 0x140 }
  - { offsetInCU: 0xC5F, offset: 0x28F39, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultO5errors5Error_pSgvg', symObjAddr: 0x380, symBinAddr: 0x3C530, symSize: 0x30 }
  - { offsetInCU: 0xC88, offset: 0x28F62, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultO6result5errorAcA0c7ManagercD0CSg_s5Error_pSgtcfC', symObjAddr: 0x3B0, symBinAddr: 0x3C560, symSize: 0x10 }
  - { offsetInCU: 0xCA1, offset: 0x28F7B, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4findys10_HashTableV6BucketV6bucket_Sb5foundtxSHRzlFSS_Tgq5', symObjAddr: 0x3C0, symBinAddr: 0x3C570, symSize: 0x60 }
  - { offsetInCU: 0xCC1, offset: 0x28F9B, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4find_9hashValues10_HashTableV6BucketV6bucket_Sb5foundtx_SitSHRzlFSS_Tgq5', symObjAddr: 0x420, symBinAddr: 0x3C5D0, symSize: 0xE0 }
  - { offsetInCU: 0x27, offset: 0x290C5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13LoginTrackingO8rawValueACSgSu_tcfC', symObjAddr: 0x0, symBinAddr: 0x3CF50, symSize: 0x20 }
  - { offsetInCU: 0x73, offset: 0x29111, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13LoginTrackingOSYAASY8rawValuexSg03RawF0Qz_tcfCTW', symObjAddr: 0x120, symBinAddr: 0x3D070, symSize: 0x30 }
  - { offsetInCU: 0xA2, offset: 0x29140, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13LoginTrackingOSYAASY8rawValue03RawF0QzvgTW', symObjAddr: 0x150, symBinAddr: 0x3D0A0, symSize: 0x10 }
  - { offsetInCU: 0xC9, offset: 0x29167, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13LoginTrackingOSHAASQWb', symObjAddr: 0x40, symBinAddr: 0x3CF90, symSize: 0x10 }
  - { offsetInCU: 0xDC, offset: 0x2917A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13LoginTrackingOACSQAAWl', symObjAddr: 0x50, symBinAddr: 0x3CFA0, symSize: 0x30 }
  - { offsetInCU: 0x10D, offset: 0x291AB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13LoginTrackingOMa', symObjAddr: 0x160, symBinAddr: 0x3D0B0, symSize: 0xA }
  - { offsetInCU: 0x143, offset: 0x291E1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13LoginTrackingOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x30, symBinAddr: 0x3CF80, symSize: 0x10 }
  - { offsetInCU: 0x1E5, offset: 0x29283, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13LoginTrackingOSHAASH9hashValueSivgTW', symObjAddr: 0x80, symBinAddr: 0x3CFD0, symSize: 0x40 }
  - { offsetInCU: 0x28C, offset: 0x2932A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13LoginTrackingOSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0xC0, symBinAddr: 0x3D010, symSize: 0x20 }
  - { offsetInCU: 0x2DB, offset: 0x29379, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13LoginTrackingOSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0xE0, symBinAddr: 0x3D030, symSize: 0x40 }
  - { offsetInCU: 0x3DE, offset: 0x2947C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13LoginTrackingO8rawValueACSgSu_tcfC', symObjAddr: 0x0, symBinAddr: 0x3CF50, symSize: 0x20 }
  - { offsetInCU: 0x3F9, offset: 0x29497, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13LoginTrackingO8rawValueSuvg', symObjAddr: 0x20, symBinAddr: 0x3CF70, symSize: 0x10 }
  - { offsetInCU: 0x4A, offset: 0x29538, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV22configuredDependenciesAC04TypeF0VSgvpZ', symObjAddr: 0x19588, symBinAddr: 0x62288, symSize: 0x0 }
  - { offsetInCU: 0x236, offset: 0x29724, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV19defaultDependenciesAC04TypeF0VSgvpZ', symObjAddr: 0x195F0, symBinAddr: 0x622F0, symSize: 0x0 }
  - { offsetInCU: 0x3C8, offset: 0x298B6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterVAA0C10CompletingA2aDP08completeC07handleryyAA01_C20CompletionParametersCc_tFTW', symObjAddr: 0x39A0, symBinAddr: 0x40A60, symSize: 0x30 }
  - { offsetInCU: 0x409, offset: 0x298F7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterVAA0C10CompletingA2aDP08completeC05nonce12codeVerifier7handlerySSSg_AJyAA01_C20CompletionParametersCctFTW', symObjAddr: 0x39D0, symBinAddr: 0x40A90, symSize: 0x20 }
  - { offsetInCU: 0x424, offset: 0x29912, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV9challenge4fromSSSgSDySSypG_tFTf4nd_n', symObjAddr: 0x4230, symBinAddr: 0x412F0, symSize: 0x390 }
  - { offsetInCU: 0x4B5, offset: 0x299A3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV5error4froms5Error_pSgSDySSypG_tFTf4nd_n', symObjAddr: 0x45C0, symBinAddr: 0x41680, symSize: 0x830 }
  - { offsetInCU: 0x6CE, offset: 0x29BBC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV13urlParameters5appIDACSDySSypG_SStcfCTf4nnd_n', symObjAddr: 0x4DF0, symBinAddr: 0x41EB0, symSize: 0xA10 }
  - { offsetInCU: 0x85D, offset: 0x29D4B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV7profile4with11permissions09FBSDKCoreB07ProfileCSgAG25AuthenticationTokenClaimsC_ShySSGSgtFTf4nnd_n', symObjAddr: 0x5B20, symBinAddr: 0x42AE0, symSize: 0x910 }
  - { offsetInCU: 0xBAB, offset: 0x2A099, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV22configuredDependencies_WZ', symObjAddr: 0x3C50, symBinAddr: 0x40D10, symSize: 0x40 }
  - { offsetInCU: 0xBC6, offset: 0x2A0B4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV22configuredDependenciesAC04TypeF0VSgvau', symObjAddr: 0x3C90, symBinAddr: 0x40D50, symSize: 0x30 }
  - { offsetInCU: 0xC08, offset: 0x2A0F6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV19defaultDependencies_WZ', symObjAddr: 0x3D60, symBinAddr: 0x40E20, symSize: 0x1C0 }
  - { offsetInCU: 0xCC6, offset: 0x2A1B4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV19defaultDependenciesAC04TypeF0VSgvau', symObjAddr: 0x3F20, symBinAddr: 0x40FE0, symSize: 0x30 }
  - { offsetInCU: 0xD17, offset: 0x2A205, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterVAA15DependentAsTypeA2aDP22configuredDependencies0gI0QzSgvsZTW', symObjAddr: 0x40E0, symBinAddr: 0x411A0, symSize: 0x80 }
  - { offsetInCU: 0xD4C, offset: 0x2A23A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterVAA15DependentAsTypeA2aDP22configuredDependencies0gI0QzSgvMZTW', symObjAddr: 0x4160, symBinAddr: 0x41220, symSize: 0x60 }
  - { offsetInCU: 0xDD0, offset: 0x2A2BE, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DateVSgWOd', symObjAddr: 0x5870, symBinAddr: 0x428C0, symSize: 0x40 }
  - { offsetInCU: 0xDE3, offset: 0x2A2D1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesVWOh', symObjAddr: 0x58E0, symBinAddr: 0x42900, symSize: 0x20 }
  - { offsetInCU: 0xDF6, offset: 0x2A2E4, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x5940, symBinAddr: 0x42960, symSize: 0x20 }
  - { offsetInCU: 0xE09, offset: 0x2A2F7, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x5960, symBinAddr: 0x42980, symSize: 0x10 }
  - { offsetInCU: 0xE1C, offset: 0x2A30A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV24fetchAndSetPropertiesFor10parameters5nonce7handleryAA01_C20CompletionParametersC_SSyAIctFySo24FBSDKAuthenticationTokenCSgcfU_TA', symObjAddr: 0x5AF0, symBinAddr: 0x42AB0, symSize: 0x30 }
  - { offsetInCU: 0xE2F, offset: 0x2A31D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15ProfileCreating_pWOb', symObjAddr: 0x6460, symBinAddr: 0x43420, symSize: 0x20 }
  - { offsetInCU: 0xE42, offset: 0x2A330, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesVSgWOf', symObjAddr: 0x6480, symBinAddr: 0x43440, symSize: 0x40 }
  - { offsetInCU: 0xE55, offset: 0x2A343, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterVMa', symObjAddr: 0x64C0, symBinAddr: 0x43480, symSize: 0x10 }
  - { offsetInCU: 0xE68, offset: 0x2A356, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesVwCP', symObjAddr: 0x64D0, symBinAddr: 0x43490, symSize: 0x30 }
  - { offsetInCU: 0xE7B, offset: 0x2A369, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesVwxx', symObjAddr: 0x6500, symBinAddr: 0x434C0, symSize: 0x40 }
  - { offsetInCU: 0xE8E, offset: 0x2A37C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesVwcp', symObjAddr: 0x6540, symBinAddr: 0x43500, symSize: 0x80 }
  - { offsetInCU: 0xEA1, offset: 0x2A38F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesVwca', symObjAddr: 0x65C0, symBinAddr: 0x43580, symSize: 0x80 }
  - { offsetInCU: 0xEB4, offset: 0x2A3A2, size: 0x8, addend: 0x0, symName: ___swift_memcpy104_8, symObjAddr: 0x6770, symBinAddr: 0x43600, symSize: 0x40 }
  - { offsetInCU: 0xEC7, offset: 0x2A3B5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesVwta', symObjAddr: 0x67B0, symBinAddr: 0x43640, symSize: 0x90 }
  - { offsetInCU: 0xEDA, offset: 0x2A3C8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesVwet', symObjAddr: 0x6840, symBinAddr: 0x436D0, symSize: 0x40 }
  - { offsetInCU: 0xEED, offset: 0x2A3DB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesVwst', symObjAddr: 0x6880, symBinAddr: 0x43710, symSize: 0x50 }
  - { offsetInCU: 0xF00, offset: 0x2A3EE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesVMa', symObjAddr: 0x68D0, symBinAddr: 0x43760, symSize: 0x10 }
  - { offsetInCU: 0xF13, offset: 0x2A401, size: 0x8, addend: 0x0, symName: '_$sSo8NSNumberCMa', symObjAddr: 0x68E0, symBinAddr: 0x43770, symSize: 0x30 }
  - { offsetInCU: 0x1307, offset: 0x2A7F5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV13urlParameters5appIDACSDySSypG_SStcfC', symObjAddr: 0x0, symBinAddr: 0x3D0C0, symSize: 0x10 }
  - { offsetInCU: 0x131A, offset: 0x2A808, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV10parametersAA01_C20CompletionParametersCvg', symObjAddr: 0x10, symBinAddr: 0x3D0D0, symSize: 0x10 }
  - { offsetInCU: 0x136F, offset: 0x2A85D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV13setParameters6values5appIDySDySSypG_SStF', symObjAddr: 0x20, symBinAddr: 0x3D0E0, symSize: 0xEE0 }
  - { offsetInCU: 0x1652, offset: 0x2AB40, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV5error4froms5Error_pSgSDySSypG_tF', symObjAddr: 0xF00, symBinAddr: 0x3DFC0, symSize: 0x10 }
  - { offsetInCU: 0x1670, offset: 0x2AB5E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV08completeC07handleryyAA01_C20CompletionParametersCc_tF', symObjAddr: 0xF10, symBinAddr: 0x3DFD0, symSize: 0x30 }
  - { offsetInCU: 0x16A8, offset: 0x2AB96, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV08completeC05nonce12codeVerifier7handlerySSSg_AHyAA01_C20CompletionParametersCctF', symObjAddr: 0xF40, symBinAddr: 0x3E000, symSize: 0x3B0 }
  - { offsetInCU: 0x17B0, offset: 0x2AC9E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV25exchangeCodeForTokensWith5nonce12codeVerifier7handlerySSSg_AHyAA01_C20CompletionParametersCctF', symObjAddr: 0x12F0, symBinAddr: 0x3E3B0, symSize: 0xA40 }
  - { offsetInCU: 0x19E2, offset: 0x2AED0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV25exchangeCodeForTokensWith5nonce12codeVerifier7handlerySSSg_AHyAA01_C20CompletionParametersCctFySo27FBSDKGraphRequestConnecting_pSg_ypSgs5Error_pSgtcfU_', symObjAddr: 0x3270, symBinAddr: 0x40330, symSize: 0x730 }
  - { offsetInCU: 0x1B11, offset: 0x2AFFF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV25exchangeNonceForTokenWith7handler014authenticationF0yyAA01_C20CompletionParametersCc_SStF', symObjAddr: 0x1D30, symBinAddr: 0x3EDF0, symSize: 0x6D0 }
  - { offsetInCU: 0x1CD9, offset: 0x2B1C7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV25exchangeNonceForTokenWith7handler014authenticationF0yyAA01_C20CompletionParametersCc_SStFySo27FBSDKGraphRequestConnecting_pSg_ypSgs5Error_pSgtcfU_', symObjAddr: 0x2E40, symBinAddr: 0x3FF00, symSize: 0x430 }
  - { offsetInCU: 0x1D7B, offset: 0x2B269, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV24fetchAndSetPropertiesFor10parameters5nonce7handleryAA01_C20CompletionParametersC_SSyAIctF', symObjAddr: 0x2400, symBinAddr: 0x3F4C0, symSize: 0x420 }
  - { offsetInCU: 0x1E38, offset: 0x2B326, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV24fetchAndSetPropertiesFor10parameters5nonce7handleryAA01_C20CompletionParametersC_SSyAIctFySo24FBSDKAuthenticationTokenCSgcfU_', symObjAddr: 0x2820, symBinAddr: 0x3F8E0, symSize: 0x1F0 }
  - { offsetInCU: 0x1F50, offset: 0x2B43E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV7profile4with11permissions09FBSDKCoreB07ProfileCSgAG25AuthenticationTokenClaimsC_ShySSGSgtF', symObjAddr: 0x2A10, symBinAddr: 0x3FAD0, symSize: 0x10 }
  - { offsetInCU: 0x1F63, offset: 0x2B451, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV18expirationDateFrom10parameters10Foundation0F0VSDySSypG_tF', symObjAddr: 0x2A20, symBinAddr: 0x3FAE0, symSize: 0x310 }
  - { offsetInCU: 0x2045, offset: 0x2B533, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV28dataAccessExpirationDateFrom10parameters10Foundation0H0VSDySSypG_tF', symObjAddr: 0x2D30, symBinAddr: 0x3FDF0, symSize: 0x100 }
  - { offsetInCU: 0x209C, offset: 0x2B58A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV9challenge4fromSSSgSDySSypG_tF', symObjAddr: 0x2E30, symBinAddr: 0x3FEF0, symSize: 0x10 }
  - { offsetInCU: 0x20D6, offset: 0x2B5C4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV14profileFactoryAA15ProfileCreating_pvg', symObjAddr: 0x39F0, symBinAddr: 0x40AB0, symSize: 0x20 }
  - { offsetInCU: 0x20E9, offset: 0x2B5D7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV14profileFactoryAA15ProfileCreating_pvs', symObjAddr: 0x3A10, symBinAddr: 0x40AD0, symSize: 0x30 }
  - { offsetInCU: 0x20FC, offset: 0x2B5EA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV14profileFactoryAA15ProfileCreating_pvM', symObjAddr: 0x3A40, symBinAddr: 0x40B00, symSize: 0x10 }
  - { offsetInCU: 0x210F, offset: 0x2B5FD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV14profileFactoryAA15ProfileCreating_pvM.resume.0', symObjAddr: 0x3A50, symBinAddr: 0x40B10, symSize: 0x10 }
  - { offsetInCU: 0x2122, offset: 0x2B610, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV26authenticationTokenCreatorAA014AuthenticationH8Creating_pvg', symObjAddr: 0x3A60, symBinAddr: 0x40B20, symSize: 0x20 }
  - { offsetInCU: 0x2135, offset: 0x2B623, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV26authenticationTokenCreatorAA014AuthenticationH8Creating_pvs', symObjAddr: 0x3A80, symBinAddr: 0x40B40, symSize: 0x30 }
  - { offsetInCU: 0x2148, offset: 0x2B636, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV26authenticationTokenCreatorAA014AuthenticationH8Creating_pvM', symObjAddr: 0x3AB0, symBinAddr: 0x40B70, symSize: 0x20 }
  - { offsetInCU: 0x215B, offset: 0x2B649, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV26authenticationTokenCreatorAA014AuthenticationH8Creating_pvM.resume.0', symObjAddr: 0x3AD0, symBinAddr: 0x40B90, symSize: 0x10 }
  - { offsetInCU: 0x216E, offset: 0x2B65C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV19graphRequestFactorySo010FBSDKGraphhI0_pvg', symObjAddr: 0x3AE0, symBinAddr: 0x40BA0, symSize: 0x10 }
  - { offsetInCU: 0x2181, offset: 0x2B66F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV19graphRequestFactorySo010FBSDKGraphhI0_pvs', symObjAddr: 0x3AF0, symBinAddr: 0x40BB0, symSize: 0x20 }
  - { offsetInCU: 0x2194, offset: 0x2B682, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV19graphRequestFactorySo010FBSDKGraphhI0_pvM', symObjAddr: 0x3B10, symBinAddr: 0x40BD0, symSize: 0x20 }
  - { offsetInCU: 0x21A7, offset: 0x2B695, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV19graphRequestFactorySo010FBSDKGraphhI0_pvM.resume.0', symObjAddr: 0x3B30, symBinAddr: 0x40BF0, symSize: 0x10 }
  - { offsetInCU: 0x21BA, offset: 0x2B6A8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV15internalUtilitySo15FBSDKURLHosting_pvg', symObjAddr: 0x3B40, symBinAddr: 0x40C00, symSize: 0x10 }
  - { offsetInCU: 0x21CD, offset: 0x2B6BB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV15internalUtilitySo15FBSDKURLHosting_pvs', symObjAddr: 0x3B50, symBinAddr: 0x40C10, symSize: 0x20 }
  - { offsetInCU: 0x21E0, offset: 0x2B6CE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV15internalUtilitySo15FBSDKURLHosting_pvM', symObjAddr: 0x3B70, symBinAddr: 0x40C30, symSize: 0x20 }
  - { offsetInCU: 0x21F3, offset: 0x2B6E1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV15internalUtilitySo15FBSDKURLHosting_pvM.resume.0', symObjAddr: 0x3B90, symBinAddr: 0x40C50, symSize: 0x10 }
  - { offsetInCU: 0x2206, offset: 0x2B6F4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV12errorFactorySo18FBSDKErrorCreating_pvg', symObjAddr: 0x3BA0, symBinAddr: 0x40C60, symSize: 0x10 }
  - { offsetInCU: 0x2219, offset: 0x2B707, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV12errorFactorySo18FBSDKErrorCreating_pvs', symObjAddr: 0x3BB0, symBinAddr: 0x40C70, symSize: 0x20 }
  - { offsetInCU: 0x222C, offset: 0x2B71A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV12errorFactorySo18FBSDKErrorCreating_pvM', symObjAddr: 0x3BD0, symBinAddr: 0x40C90, symSize: 0x20 }
  - { offsetInCU: 0x223F, offset: 0x2B72D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV12errorFactorySo18FBSDKErrorCreating_pvM.resume.0', symObjAddr: 0x3BF0, symBinAddr: 0x40CB0, symSize: 0x10 }
  - { offsetInCU: 0x2258, offset: 0x2B746, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV14profileFactory26authenticationTokenCreator012graphRequestH015internalUtility05errorH0AeA15ProfileCreating_p_AA014AuthenticationjR0_pSo010FBSDKGraphmH0_pSo15FBSDKURLHosting_pSo010FBSDKErrorR0_ptcfC', symObjAddr: 0x3C00, symBinAddr: 0x40CC0, symSize: 0x50 }
  - { offsetInCU: 0x226B, offset: 0x2B759, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV22configuredDependenciesAC04TypeF0VSgvMZ', symObjAddr: 0x3D00, symBinAddr: 0x40DC0, symSize: 0x60 }
  - { offsetInCU: 0x22D1, offset: 0x2B7BF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV19defaultDependenciesAC04TypeF0VSgvMZ', symObjAddr: 0x4050, symBinAddr: 0x41110, symSize: 0x60 }
  - { offsetInCU: 0x22F0, offset: 0x2B7DE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV19defaultDependenciesAC04TypeF0VSgvMZ.resume.0', symObjAddr: 0x40B0, symBinAddr: 0x41170, symSize: 0x10 }
  - { offsetInCU: 0x8D, offset: 0x2B964, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginUtilityO9getUserID4fromSSSgAF_tFZTf4nd_n', symObjAddr: 0x80, symBinAddr: 0x43920, symSize: 0x340 }
  - { offsetInCU: 0x154, offset: 0x2BA2B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginUtilityO18getQueryParameters4fromSDySSypGSg10Foundation3URLV_tFZTf4nd_n', symObjAddr: 0x3C0, symBinAddr: 0x43C60, symSize: 0x5F0 }
  - { offsetInCU: 0x2B3, offset: 0x2BB8A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginUtilityOMa', symObjAddr: 0x9B0, symBinAddr: 0x44250, symSize: 0x10 }
  - { offsetInCU: 0x2C6, offset: 0x2BB9D, size: 0x8, addend: 0x0, symName: '_$s10Foundation3URLVSgWOc', symObjAddr: 0xAA0, symBinAddr: 0x44260, symSize: 0x40 }
  - { offsetInCU: 0x47F, offset: 0x2BD56, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginUtilityO17stringForAudienceySSAA07DefaultG0OFZ', symObjAddr: 0x0, symBinAddr: 0x438A0, symSize: 0x60 }
  - { offsetInCU: 0x4AE, offset: 0x2BD85, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginUtilityO18getQueryParameters4fromSDySSypGSg10Foundation3URLV_tFZ', symObjAddr: 0x60, symBinAddr: 0x43900, symSize: 0x10 }
  - { offsetInCU: 0x4C1, offset: 0x2BD98, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginUtilityO9getUserID4fromSSSgAF_tFZ', symObjAddr: 0x70, symBinAddr: 0x43910, symSize: 0x10 }
  - { offsetInCU: 0x27, offset: 0x2BE42, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24MissingDependenciesErrorV3forACyxGxm_tcfC', symObjAddr: 0x0, symBinAddr: 0x442D0, symSize: 0x10 }
  - { offsetInCU: 0x72, offset: 0x2BE8D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24MissingDependenciesErrorVyxGs23CustomStringConvertibleAAsAEP11descriptionSSvgTW', symObjAddr: 0x110, symBinAddr: 0x443E0, symSize: 0x10 }
  - { offsetInCU: 0xA3, offset: 0x2BEBE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24MissingDependenciesErrorVMi', symObjAddr: 0x120, symBinAddr: 0x443F0, symSize: 0x10 }
  - { offsetInCU: 0xB6, offset: 0x2BED1, size: 0x8, addend: 0x0, symName: ___swift_memcpy8_8, symObjAddr: 0x130, symBinAddr: 0x44400, symSize: 0x10 }
  - { offsetInCU: 0xC9, offset: 0x2BEE4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24MissingDependenciesErrorVwet', symObjAddr: 0x150, symBinAddr: 0x44410, symSize: 0x40 }
  - { offsetInCU: 0xDC, offset: 0x2BEF7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24MissingDependenciesErrorVwst', symObjAddr: 0x190, symBinAddr: 0x44450, symSize: 0x40 }
  - { offsetInCU: 0xEF, offset: 0x2BF0A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24MissingDependenciesErrorVMa', symObjAddr: 0x1D0, symBinAddr: 0x44490, symSize: 0x10 }
  - { offsetInCU: 0x102, offset: 0x2BF1D, size: 0x8, addend: 0x0, symName: ___swift_instantiateGenericMetadata, symObjAddr: 0x1E0, symBinAddr: 0x444A0, symSize: 0x26 }
  - { offsetInCU: 0x18E, offset: 0x2BFA9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24MissingDependenciesErrorVyxGs0E0AAsAEP7_domainSSvgTW', symObjAddr: 0xD0, symBinAddr: 0x443A0, symSize: 0x10 }
  - { offsetInCU: 0x1AA, offset: 0x2BFC5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24MissingDependenciesErrorVyxGs0E0AAsAEP5_codeSivgTW', symObjAddr: 0xE0, symBinAddr: 0x443B0, symSize: 0x10 }
  - { offsetInCU: 0x1C6, offset: 0x2BFE1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24MissingDependenciesErrorVyxGs0E0AAsAEP9_userInfoyXlSgvgTW', symObjAddr: 0xF0, symBinAddr: 0x443C0, symSize: 0x10 }
  - { offsetInCU: 0x1E1, offset: 0x2BFFC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24MissingDependenciesErrorVyxGs0E0AAsAEP19_getEmbeddedNSErroryXlSgyFTW', symObjAddr: 0x100, symBinAddr: 0x443D0, symSize: 0x10 }
  - { offsetInCU: 0x299, offset: 0x2C0B4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24MissingDependenciesErrorV3forACyxGxm_tcfC', symObjAddr: 0x0, symBinAddr: 0x442D0, symSize: 0x10 }
  - { offsetInCU: 0x2F5, offset: 0x2C110, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24MissingDependenciesErrorV11descriptionSSvg', symObjAddr: 0x10, symBinAddr: 0x442E0, symSize: 0xC0 }
  - { offsetInCU: 0x27, offset: 0x2C1EC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14NonceValidatorO7isValid5nonceSbSS_tFZ', symObjAddr: 0x0, symBinAddr: 0x444D0, symSize: 0xD0 }
  - { offsetInCU: 0x3D, offset: 0x2C202, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14NonceValidatorOMa', symObjAddr: 0x100, symBinAddr: 0x445A0, symSize: 0xA }
  - { offsetInCU: 0x10B, offset: 0x2C2D0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14NonceValidatorO7isValid5nonceSbSS_tFZ', symObjAddr: 0x0, symBinAddr: 0x444D0, symSize: 0xD0 }
  - { offsetInCU: 0x27, offset: 0x2C347, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14ProfileFactoryCACycfC', symObjAddr: 0x0, symBinAddr: 0x445D0, symSize: 0x20 }
  - { offsetInCU: 0xDA, offset: 0x2C3FA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14ProfileFactoryCACycfcTo', symObjAddr: 0x60, symBinAddr: 0x44630, symSize: 0x30 }
  - { offsetInCU: 0x129, offset: 0x2C449, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14ProfileFactoryCAA0C8CreatingA2aDP06createC06userID9firstName06middleJ004lastJ04name7linkURL11refreshDate05imageO05email9friendIDs8birthday8ageRange8hometown8location6gender11permissions9isLimited09FBSDKCoreB00C0CSS_SSSgA_A_A_10Foundation0O0VSgA0_0Q0VSgA3_A_SaySSGSgA6_So012FBSDKUserAgeX0CSgSo13FBSDKLocationCSgA14_A_ShySSGSgSbtFTW', symObjAddr: 0xC0, symBinAddr: 0x44690, symSize: 0x10 }
  - { offsetInCU: 0x159, offset: 0x2C479, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14ProfileFactoryC06createC06userID9firstName06middleI004lastI04name7linkURL11refreshDate05imageN05email9friendIDs8birthday8ageRange8hometown8location6gender11permissions9isLimited09FBSDKCoreB00C0CSS_SSSgA3Y10Foundation0N0VSgAZ0P0VSgA1_AYSaySSGSgA4_So012FBSDKUserAgeW0CSgSo13FBSDKLocationCSgA12_AYShySSGSgSbtFTf4nnnnnnnnnnnnnnnnnd_n', symObjAddr: 0xD0, symBinAddr: 0x446A0, symSize: 0x1D0 }
  - { offsetInCU: 0x28B, offset: 0x2C5AB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14ProfileFactoryCMa', symObjAddr: 0x2A0, symBinAddr: 0x44870, symSize: 0x20 }
  - { offsetInCU: 0x4B0, offset: 0x2C7D0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14ProfileFactoryCACycfC', symObjAddr: 0x0, symBinAddr: 0x445D0, symSize: 0x20 }
  - { offsetInCU: 0x4C3, offset: 0x2C7E3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14ProfileFactoryC06createC06userID9firstName06middleI004lastI04name7linkURL11refreshDate05imageN05email9friendIDs8birthday8ageRange8hometown8location6gender11permissions9isLimited09FBSDKCoreB00C0CSS_SSSgA3Y10Foundation0N0VSgAZ0P0VSgA1_AYSaySSGSgA4_So012FBSDKUserAgeW0CSgSo13FBSDKLocationCSgA12_AYShySSGSgSbtF', symObjAddr: 0x20, symBinAddr: 0x445F0, symSize: 0x10 }
  - { offsetInCU: 0x4D6, offset: 0x2C7F6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14ProfileFactoryCACycfc', symObjAddr: 0x30, symBinAddr: 0x44600, symSize: 0x30 }
  - { offsetInCU: 0x510, offset: 0x2C830, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14ProfileFactoryCfD', symObjAddr: 0x90, symBinAddr: 0x44660, symSize: 0x30 }
  - { offsetInCU: 0x27, offset: 0x2C8B7, size: 0x8, addend: 0x0, symName: '_$s12FBSDKCoreKit27ServerConfigurationProviderC010FBSDKLoginB00cD9ProvidingA2dEP04loadcD010completionyySo0F7TooltipCSg_s5Error_pSgtcSg_tFTW', symObjAddr: 0x0, symBinAddr: 0x448C0, symSize: 0x18 }
  - { offsetInCU: 0x64, offset: 0x2C8F4, size: 0x8, addend: 0x0, symName: '_$s12FBSDKCoreKit27ServerConfigurationProviderC010FBSDKLoginB00cD9ProvidingA2dEP04loadcD010completionyySo0F7TooltipCSg_s5Error_pSgtcSg_tFTW', symObjAddr: 0x0, symBinAddr: 0x448C0, symSize: 0x18 }
  - { offsetInCU: 0x183, offset: 0x2CB05, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC5errors5Error_pSgvgTo', symObjAddr: 0xAC0, symBinAddr: 0x453E0, symSize: 0x60 }
  - { offsetInCU: 0x1D5, offset: 0x2CB57, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC5errors5Error_pSgvsTo', symObjAddr: 0xB60, symBinAddr: 0x45480, symSize: 0x60 }
  - { offsetInCU: 0x2BC, offset: 0x2CC3E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersCACycfcTo', symObjAddr: 0x15F0, symBinAddr: 0x45E90, symSize: 0x20 }
  - { offsetInCU: 0x2ED, offset: 0x2CC6F, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DateVSgWOc', symObjAddr: 0xCB0, symBinAddr: 0x455D0, symSize: 0x40 }
  - { offsetInCU: 0x300, offset: 0x2CC82, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersCfETo', symObjAddr: 0x1640, symBinAddr: 0x45EE0, symSize: 0x130 }
  - { offsetInCU: 0x32D, offset: 0x2CCAF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersCMU', symObjAddr: 0x1770, symBinAddr: 0x46010, symSize: 0x10 }
  - { offsetInCU: 0x340, offset: 0x2CCC2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersCMa', symObjAddr: 0x1780, symBinAddr: 0x46020, symSize: 0x30 }
  - { offsetInCU: 0x353, offset: 0x2CCD5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersCMr', symObjAddr: 0x17B0, symBinAddr: 0x46050, symSize: 0xC0 }
  - { offsetInCU: 0x366, offset: 0x2CCE8, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DateVSgMa', symObjAddr: 0x1870, symBinAddr: 0x46110, symSize: 0x50 }
  - { offsetInCU: 0x379, offset: 0x2CCFB, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DateVSgWOh', symObjAddr: 0x1900, symBinAddr: 0x46160, symSize: 0x30 }
  - { offsetInCU: 0x459, offset: 0x2CDDB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersCACycfC', symObjAddr: 0x0, symBinAddr: 0x44920, symSize: 0x20 }
  - { offsetInCU: 0x46C, offset: 0x2CDEE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC19authenticationTokenSo019FBSDKAuthenticationG0CSgvg', symObjAddr: 0x40, symBinAddr: 0x44960, symSize: 0x40 }
  - { offsetInCU: 0x493, offset: 0x2CE15, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC19authenticationTokenSo019FBSDKAuthenticationG0CSgvM', symObjAddr: 0xC0, symBinAddr: 0x449E0, symSize: 0x40 }
  - { offsetInCU: 0x4B6, offset: 0x2CE38, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC7profile09FBSDKCoreB07ProfileCSgvg', symObjAddr: 0x160, symBinAddr: 0x44A80, symSize: 0x40 }
  - { offsetInCU: 0x4D7, offset: 0x2CE59, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC7profile09FBSDKCoreB07ProfileCSgvM', symObjAddr: 0x280, symBinAddr: 0x44BA0, symSize: 0x40 }
  - { offsetInCU: 0x4FA, offset: 0x2CE7C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC17accessTokenStringSSSgvM', symObjAddr: 0x340, symBinAddr: 0x44C60, symSize: 0x40 }
  - { offsetInCU: 0x51D, offset: 0x2CE9F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC11nonceStringSSSgvM', symObjAddr: 0x400, symBinAddr: 0x44D20, symSize: 0x40 }
  - { offsetInCU: 0x540, offset: 0x2CEC2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC25authenticationTokenStringSSSgvM', symObjAddr: 0x4C0, symBinAddr: 0x44DE0, symSize: 0x40 }
  - { offsetInCU: 0x563, offset: 0x2CEE5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC4codeSSSgvM', symObjAddr: 0x580, symBinAddr: 0x44EA0, symSize: 0x40 }
  - { offsetInCU: 0x586, offset: 0x2CF08, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC11permissionsShyAA12FBPermissionCGSgvM', symObjAddr: 0x640, symBinAddr: 0x44F60, symSize: 0x40 }
  - { offsetInCU: 0x5A9, offset: 0x2CF2B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC19declinedPermissionsShyAA12FBPermissionCGSgvM', symObjAddr: 0x700, symBinAddr: 0x45020, symSize: 0x40 }
  - { offsetInCU: 0x5CC, offset: 0x2CF4E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC18expiredPermissionsShyAA12FBPermissionCGSgvM', symObjAddr: 0x900, symBinAddr: 0x45220, symSize: 0x40 }
  - { offsetInCU: 0x5EF, offset: 0x2CF71, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC5appIDSSSgvM', symObjAddr: 0x9C0, symBinAddr: 0x452E0, symSize: 0x40 }
  - { offsetInCU: 0x612, offset: 0x2CF94, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC6userIDSSSgvM', symObjAddr: 0xA80, symBinAddr: 0x453A0, symSize: 0x40 }
  - { offsetInCU: 0x64C, offset: 0x2CFCE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC5errors5Error_pSgvg', symObjAddr: 0xB20, symBinAddr: 0x45440, symSize: 0x40 }
  - { offsetInCU: 0x689, offset: 0x2D00B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC5errors5Error_pSgvM', symObjAddr: 0xC30, symBinAddr: 0x45550, symSize: 0x40 }
  - { offsetInCU: 0x6AC, offset: 0x2D02E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC14expirationDate10Foundation0G0VSgvM', symObjAddr: 0xDB0, symBinAddr: 0x45650, symSize: 0x40 }
  - { offsetInCU: 0x6CF, offset: 0x2D051, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC24dataAccessExpirationDate10Foundation0I0VSgvM', symObjAddr: 0x1080, symBinAddr: 0x45920, symSize: 0x40 }
  - { offsetInCU: 0x6F2, offset: 0x2D074, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC9challengeSSSgvM', symObjAddr: 0x1140, symBinAddr: 0x459E0, symSize: 0x40 }
  - { offsetInCU: 0x715, offset: 0x2D097, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC11graphDomainSSSgvM', symObjAddr: 0x1200, symBinAddr: 0x45AA0, symSize: 0x40 }
  - { offsetInCU: 0x738, offset: 0x2D0BA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC11graphDomainSSSgvM.resume.0', symObjAddr: 0x1240, symBinAddr: 0x45AE0, symSize: 0x10 }
  - { offsetInCU: 0x757, offset: 0x2D0D9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC14userTokenNonceSSSgvM', symObjAddr: 0x1440, symBinAddr: 0x45CE0, symSize: 0x40 }
  - { offsetInCU: 0x77A, offset: 0x2D0FC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersCACycfc', symObjAddr: 0x1480, symBinAddr: 0x45D20, symSize: 0x170 }
  - { offsetInCU: 0x79D, offset: 0x2D11F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersCfD', symObjAddr: 0x1610, symBinAddr: 0x45EB0, symSize: 0x30 }
...
