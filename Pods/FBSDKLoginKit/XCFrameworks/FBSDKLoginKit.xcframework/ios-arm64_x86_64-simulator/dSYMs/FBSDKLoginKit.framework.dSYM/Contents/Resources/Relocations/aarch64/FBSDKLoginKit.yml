---
triple:          'arm64-apple-darwin'
binary-path:     '/data/sandcastle/boxes/trunk-git-facebook-ios-sdk/build/Build/Intermediates.noindex/ArchiveIntermediates/FBSDKLoginKit-Dynamic/InstallationBuildProductsLocation/@rpath/FBSDKLoginKit.framework/FBSDKLoginKit'
relocations:
  - { offsetInCU: 0x33, offset: 0x33, size: 0x8, addend: 0x0, symName: _FBSDKLoginKitVersionString, symObjAddr: 0x0, symBinAddr: 0x46C50, symSize: 0x0 }
  - { offsetInCU: 0x67, offset: 0x67, size: 0x8, addend: 0x0, symName: _FBSDKLoginKitVersionNumber, symObjAddr: 0x40, symBinAddr: 0x46C90, symSize: 0x0 }
  - { offsetInCU: 0x34, offset: 0xB0, size: 0x8, addend: 0x0, symName: _FBSDKLoginAuthTypeRerequest, symObjAddr: 0x58, symBinAddr: 0x543F0, symSize: 0x0 }
  - { offsetInCU: 0xB7, offset: 0x133, size: 0x8, addend: 0x0, symName: _FBSDKLoginAuthTypeReauthorize, symObjAddr: 0x60, symBinAddr: 0x543F8, symSize: 0x0 }
  - { offsetInCU: 0x34, offset: 0x184, size: 0x8, addend: 0x0, symName: _FBSDKLoginErrorDomain, symObjAddr: 0x38, symBinAddr: 0x54400, symSize: 0x0 }
  - { offsetInCU: 0x3D, offset: 0x23F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5frame15elementProvider06stringG005loginG019graphRequestFactoryACSo6CGRectV_AA29UserInterfaceElementProviding_pAA0no6StringQ0_pAA05LoginQ0_pSo010FBSDKGraphkL0_ptcfcfA_', symObjAddr: 0xC4, symBinAddr: 0x48F4, symSize: 0x14 }
  - { offsetInCU: 0x56, offset: 0x258, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5frame11permissionsACSo6CGRectV_Say09FBSDKCoreB010PermissionOGtcfcfA_', symObjAddr: 0xD8, symBinAddr: 0x4908, symSize: 0x14 }
  - { offsetInCU: 0x6F, offset: 0x271, size: 0x8, addend: 0x0, symName: ___swift_instantiateConcreteTypeFromMangledName, symObjAddr: 0xF0, symBinAddr: 0x4920, symSize: 0x40 }
  - { offsetInCU: 0x82, offset: 0x284, size: 0x8, addend: 0x0, symName: ___swift_memcpy16_8, symObjAddr: 0x1EC, symBinAddr: 0x4A1C, symSize: 0xC }
  - { offsetInCU: 0x95, offset: 0x297, size: 0x8, addend: 0x0, symName: ___swift_noop_void_return, symObjAddr: 0x1F8, symBinAddr: 0x4A28, symSize: 0x4 }
  - { offsetInCU: 0xA8, offset: 0x2AA, size: 0x8, addend: 0x0, symName: '_$sSo7CGPointVwet', symObjAddr: 0x1FC, symBinAddr: 0x4A2C, symSize: 0x20 }
  - { offsetInCU: 0xBB, offset: 0x2BD, size: 0x8, addend: 0x0, symName: '_$sSo7CGPointVwst', symObjAddr: 0x21C, symBinAddr: 0x4A4C, symSize: 0x28 }
  - { offsetInCU: 0xCE, offset: 0x2D0, size: 0x8, addend: 0x0, symName: '_$sSo18FBSDKLoginAuthTypeas20_SwiftNewtypeWrapperSCSYWb', symObjAddr: 0x5D4, symBinAddr: 0x4D7C, symSize: 0x2C }
  - { offsetInCU: 0xE1, offset: 0x2E3, size: 0x8, addend: 0x0, symName: '_$sSo18FBSDKLoginAuthTypeas20_SwiftNewtypeWrapperSCs35_HasCustomAnyHashableRepresentationPWb', symObjAddr: 0x600, symBinAddr: 0x4DA8, symSize: 0x2C }
  - { offsetInCU: 0xF4, offset: 0x2F6, size: 0x8, addend: 0x0, symName: '_$sSo18FBSDKLoginAuthTypeaSHSCSQWb', symObjAddr: 0x66C, symBinAddr: 0x4E14, symSize: 0x2C }
  - { offsetInCU: 0x107, offset: 0x309, size: 0x8, addend: 0x0, symName: '_$sSo26FBSDKAppEventParameterNameas20_SwiftNewtypeWrapperSCSYWb', symObjAddr: 0x804, symBinAddr: 0x4FAC, symSize: 0x2C }
  - { offsetInCU: 0x11A, offset: 0x31C, size: 0x8, addend: 0x0, symName: '_$sSo26FBSDKAppEventParameterNameas20_SwiftNewtypeWrapperSCs35_HasCustomAnyHashableRepresentationPWb', symObjAddr: 0x830, symBinAddr: 0x4FD8, symSize: 0x2C }
  - { offsetInCU: 0x12D, offset: 0x32F, size: 0x8, addend: 0x0, symName: '_$sSo26FBSDKAppEventParameterNameaSHSCSQWb', symObjAddr: 0x85C, symBinAddr: 0x5004, symSize: 0x2C }
  - { offsetInCU: 0x15B, offset: 0x35D, size: 0x8, addend: 0x0, symName: '_$sSo26FBSDKAppEventParameterNameas21_ObjectiveCBridgeableSCsACP016_forceBridgeFromE1C_6resulty01_E5CTypeQz_xSgztFZTW', symObjAddr: 0x294, symBinAddr: 0x4AC4, symSize: 0x14 }
  - { offsetInCU: 0x176, offset: 0x378, size: 0x8, addend: 0x0, symName: '_$sSo26FBSDKAppEventParameterNameas21_ObjectiveCBridgeableSCsACP024_conditionallyBridgeFromE1C_6resultSb01_E5CTypeQz_xSgztFZTW', symObjAddr: 0x2A8, symBinAddr: 0x4AD8, symSize: 0x18 }
  - { offsetInCU: 0x197, offset: 0x399, size: 0x8, addend: 0x0, symName: '_$sSo18FBSDKLoginAuthTypeas21_ObjectiveCBridgeableSCsACP016_forceBridgeFromD1C_6resulty01_D5CTypeQz_xSgztFZTW', symObjAddr: 0x324, symBinAddr: 0x4B00, symSize: 0x14 }
  - { offsetInCU: 0x1B2, offset: 0x3B4, size: 0x8, addend: 0x0, symName: '_$sSo18FBSDKLoginAuthTypeas21_ObjectiveCBridgeableSCsACP024_conditionallyBridgeFromD1C_6resultSb01_D5CTypeQz_xSgztFZTW', symObjAddr: 0x338, symBinAddr: 0x4B14, symSize: 0x18 }
  - { offsetInCU: 0x1D3, offset: 0x3D5, size: 0x8, addend: 0x0, symName: '_$sSo26FBSDKAppEventParameterNameas35_HasCustomAnyHashableRepresentationSCsACP03_tofgH0s0gH0VSgyFTW', symObjAddr: 0x4CC, symBinAddr: 0x4C74, symSize: 0x84 }
  - { offsetInCU: 0x1EE, offset: 0x3F0, size: 0x8, addend: 0x0, symName: '_$sSo18FBSDKLoginAuthTypeas35_HasCustomAnyHashableRepresentationSCsACP03_toefG0s0fG0VSgyFTW', symObjAddr: 0x550, symBinAddr: 0x4CF8, symSize: 0x84 }
  - { offsetInCU: 0x242, offset: 0x444, size: 0x8, addend: 0x0, symName: '_$sSo14FBSDKAppEventsC13FBSDKLoginKit17LoginEventLoggingA2cDP13flushBehaviorSo0ab5FlushI0VvgTW', symObjAddr: 0x0, symBinAddr: 0x4830, symSize: 0x10 }
  - { offsetInCU: 0x293, offset: 0x495, size: 0x8, addend: 0x0, symName: '_$sSo14FBSDKAppEventsC13FBSDKLoginKit17LoginEventLoggingA2cDP011logInternalF0_10parameters18isImplicitlyLoggedySo0aF4Namea_SDySo0af9ParameterN0aypGSgSbtFTW', symObjAddr: 0x10, symBinAddr: 0x4840, symSize: 0xA4 }
  - { offsetInCU: 0x2D5, offset: 0x4D7, size: 0x8, addend: 0x0, symName: '_$sSo14FBSDKAppEventsC13FBSDKLoginKit17LoginEventLoggingA2cDP5flushyyFTW', symObjAddr: 0xB4, symBinAddr: 0x48E4, symSize: 0x10 }
  - { offsetInCU: 0x360, offset: 0x562, size: 0x8, addend: 0x0, symName: '_$sSo18FBSDKLoginAuthTypeaSYSCSY8rawValue03RawE0QzvgTW', symObjAddr: 0x45C, symBinAddr: 0x4C04, symSize: 0x28 }
  - { offsetInCU: 0x1D7, offset: 0x891, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryCAA0cD8CreatingA2aDP06createD011tokenString5nonce11graphDomain10completionySS_S2SySo019FBSDKAuthenticationD0CSgctFTW', symObjAddr: 0x1864, symBinAddr: 0x69BC, symSize: 0x20 }
  - { offsetInCU: 0x41E, offset: 0xAD8, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataVSgSo13NSURLResponseCSgs5Error_pSgIegggg_So6NSDataCSgAGSo7NSErrorCSgIeyByyy_TR', symObjAddr: 0x1658, symBinAddr: 0x67B0, symSize: 0xC8 }
  - { offsetInCU: 0x435, offset: 0xAEF, size: 0x8, addend: 0x0, symName: '_$s10Foundation13__DataStorageC15withUnsafeBytes2in5applyxSnySiG_xSWKXEtKlFyt_Tg5153$s13FBSDKLoginKit26AuthenticationTokenFactoryC15verifySignature_6header6claims14certificateKey10completionySS_S3SySbctFySo03SecK3RefaSgcfU_yycfU_ySWXEfU_AA0B0VSays5UInt8VGTf1nncn_n', symObjAddr: 0x1884, symBinAddr: 0x69DC, symSize: 0xCC }
  - { offsetInCU: 0x507, offset: 0xBC1, size: 0x8, addend: 0x0, symName: '_$sS2SSysWl', symObjAddr: 0x1A4C, symBinAddr: 0x6BA4, symSize: 0x44 }
  - { offsetInCU: 0x51A, offset: 0xBD4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC06createD011tokenString5nonce11graphDomain10completionySS_S2SySo019FBSDKAuthenticationD0CSgctFySbcfU_TA', symObjAddr: 0x1ACC, symBinAddr: 0x6C24, symSize: 0x30 }
  - { offsetInCU: 0x52D, offset: 0xBE7, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV15_RepresentationOWOe', symObjAddr: 0x1B50, symBinAddr: 0x6CA8, symSize: 0x44 }
  - { offsetInCU: 0x540, offset: 0xBFA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC15verifySignature_6header6claims14certificateKey10completionySS_S3SySbctFySo03SecK3RefaSgcfU_TA', symObjAddr: 0x1B94, symBinAddr: 0x6CEC, symSize: 0x10 }
  - { offsetInCU: 0x553, offset: 0xC0D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC16getPublicKeyWith011certificateH010completionySS_ySo03SecH3RefaSgctFySo0l11CertificateM0aSgcfU_TA', symObjAddr: 0x1BC8, symBinAddr: 0x6D20, symSize: 0x8 }
  - { offsetInCU: 0x566, offset: 0xC20, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataVSgWOy', symObjAddr: 0x1BD0, symBinAddr: 0x6D28, symSize: 0x14 }
  - { offsetInCU: 0x579, offset: 0xC33, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV15_RepresentationOWOy', symObjAddr: 0x1BE4, symBinAddr: 0x6D3C, symSize: 0x44 }
  - { offsetInCU: 0x58C, offset: 0xC46, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataVSgWOe', symObjAddr: 0x1C28, symBinAddr: 0x6D80, symSize: 0x14 }
  - { offsetInCU: 0x59F, offset: 0xC59, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC18getCertificateWith14certificateKey10completionySS_ySo03SecG3RefaSgctFy10Foundation4DataVSg_So13NSURLResponseCSgs5Error_pSgtcfU_TA', symObjAddr: 0x1C70, symBinAddr: 0x6DC8, symSize: 0x2C }
  - { offsetInCU: 0x5B2, offset: 0xC6C, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x1C9C, symBinAddr: 0x6DF4, symSize: 0x10 }
  - { offsetInCU: 0x5C5, offset: 0xC7F, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x1CAC, symBinAddr: 0x6E04, symSize: 0x8 }
  - { offsetInCU: 0x5D8, offset: 0xC92, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryCMa', symObjAddr: 0x1CB4, symBinAddr: 0x6E0C, symSize: 0x20 }
  - { offsetInCU: 0x8B6, offset: 0xF70, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC15verifySignature_6header6claims14certificateKey10completionySS_S3SySbctFySo03SecK3RefaSgcfU_yycfU_TA', symObjAddr: 0x223C, symBinAddr: 0x7354, symSize: 0x14 }
  - { offsetInCU: 0x8C9, offset: 0xF83, size: 0x8, addend: 0x0, symName: '_$sSS_yptWOc', symObjAddr: 0x2250, symBinAddr: 0x7368, symSize: 0x48 }
  - { offsetInCU: 0x8DC, offset: 0xF96, size: 0x8, addend: 0x0, symName: '_$sypWOb', symObjAddr: 0x2298, symBinAddr: 0x73B0, symSize: 0x10 }
  - { offsetInCU: 0xAC4, offset: 0x117E, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_ypTgm5Tf4g_n', symObjAddr: 0x1950, symBinAddr: 0x6AA8, symSize: 0xFC }
  - { offsetInCU: 0xC16, offset: 0x12D0, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_SbTgm5Tf4g_n', symObjAddr: 0x1CE8, symBinAddr: 0x6E40, symSize: 0xE4 }
  - { offsetInCU: 0xD68, offset: 0x1422, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_SSSgTgm5Tf4g_n', symObjAddr: 0x1DCC, symBinAddr: 0x6F24, symSize: 0xFC }
  - { offsetInCU: 0xEAE, offset: 0x1568, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_SSTgm5Tf4g_n', symObjAddr: 0x1EC8, symBinAddr: 0x7020, symSize: 0xFC }
  - { offsetInCU: 0x1006, offset: 0x16C0, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_SdTgm5Tf4g_n', symObjAddr: 0x1FC4, symBinAddr: 0x711C, symSize: 0xEC }
  - { offsetInCU: 0x1158, offset: 0x1812, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_So8NSNumberCTgm5Tf4g_n', symObjAddr: 0x20B0, symBinAddr: 0x7208, symSize: 0xF0 }
  - { offsetInCU: 0x15B5, offset: 0x1C6F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC16beginCertificateSSvg', symObjAddr: 0x0, symBinAddr: 0x5158, symSize: 0x2C }
  - { offsetInCU: 0x15C8, offset: 0x1C82, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC14endCertificateSSvg', symObjAddr: 0x2C, symBinAddr: 0x5184, symSize: 0x2C }
  - { offsetInCU: 0x15DB, offset: 0x1C95, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC15sessionProviderSo24FBSDKURLSessionProviding_pvg', symObjAddr: 0x58, symBinAddr: 0x51B0, symSize: 0x34 }
  - { offsetInCU: 0x15F4, offset: 0x1CAE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC15sessionProviderSo24FBSDKURLSessionProviding_pvs', symObjAddr: 0x8C, symBinAddr: 0x51E4, symSize: 0x44 }
  - { offsetInCU: 0x1607, offset: 0x1CC1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC15sessionProviderSo24FBSDKURLSessionProviding_pvM', symObjAddr: 0xD0, symBinAddr: 0x5228, symSize: 0x3C }
  - { offsetInCU: 0x161A, offset: 0x1CD4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC15sessionProviderSo24FBSDKURLSessionProviding_pvM.resume.0', symObjAddr: 0x10C, symBinAddr: 0x5264, symSize: 0x4 }
  - { offsetInCU: 0x162D, offset: 0x1CE7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC19certificateEndpoint10Foundation3URLVvg', symObjAddr: 0x110, symBinAddr: 0x5268, symSize: 0x144 }
  - { offsetInCU: 0x168F, offset: 0x1D49, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC15sessionProviderACSo24FBSDKURLSessionProviding_p_tcfC', symObjAddr: 0x254, symBinAddr: 0x53AC, symSize: 0xF4 }
  - { offsetInCU: 0x1731, offset: 0x1DEB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC06createD011tokenString5nonce11graphDomain10completionySS_S2SySo019FBSDKAuthenticationD0CSgctF', symObjAddr: 0x348, symBinAddr: 0x54A0, symSize: 0x300 }
  - { offsetInCU: 0x1912, offset: 0x1FCC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC06createD011tokenString5nonce11graphDomain10completionySS_S2SySo019FBSDKAuthenticationD0CSgctFySbcfU_', symObjAddr: 0x648, symBinAddr: 0x57A0, symSize: 0x134 }
  - { offsetInCU: 0x1A1D, offset: 0x20D7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC15verifySignature_6header6claims14certificateKey10completionySS_S3SySbctF', symObjAddr: 0x77C, symBinAddr: 0x58D4, symSize: 0x278 }
  - { offsetInCU: 0x1B69, offset: 0x2223, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC15verifySignature_6header6claims14certificateKey10completionySS_S3SySbctFySo03SecK3RefaSgcfU_', symObjAddr: 0x9F4, symBinAddr: 0x5B4C, symSize: 0x100 }
  - { offsetInCU: 0x1BF2, offset: 0x22AC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC15verifySignature_6header6claims14certificateKey10completionySS_S3SySbctFySo03SecK3RefaSgcfU_yycfU_', symObjAddr: 0xAF4, symBinAddr: 0x5C4C, symSize: 0x324 }
  - { offsetInCU: 0x1F28, offset: 0x25E2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC15verifySignature_6header6claims14certificateKey10completionySS_S3SySbctFySo03SecK3RefaSgcfU_yycfU_ySWXEfU_', symObjAddr: 0xE28, symBinAddr: 0x5F80, symSize: 0xDC }
  - { offsetInCU: 0x2120, offset: 0x27DA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC16getPublicKeyWith011certificateH010completionySS_ySo03SecH3RefaSgctF', symObjAddr: 0xF14, symBinAddr: 0x606C, symSize: 0x78 }
  - { offsetInCU: 0x214E, offset: 0x2808, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC16getPublicKeyWith011certificateH010completionySS_ySo03SecH3RefaSgctFySo0l11CertificateM0aSgcfU_', symObjAddr: 0xF8C, symBinAddr: 0x60E4, symSize: 0xCC }
  - { offsetInCU: 0x2211, offset: 0x28CB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC18getCertificateWith14certificateKey10completionySS_ySo03SecG3RefaSgctF', symObjAddr: 0x1058, symBinAddr: 0x61B0, symSize: 0x1D4 }
  - { offsetInCU: 0x2266, offset: 0x2920, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC18getCertificateWith14certificateKey10completionySS_ySo03SecG3RefaSgctFy10Foundation4DataVSg_So13NSURLResponseCSgs5Error_pSgtcfU_', symObjAddr: 0x122C, symBinAddr: 0x6384, symSize: 0x42C }
  - { offsetInCU: 0x2374, offset: 0x2A2E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryCfd', symObjAddr: 0x1720, symBinAddr: 0x6878, symSize: 0x2C }
  - { offsetInCU: 0x23A1, offset: 0x2A5B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryCfD', symObjAddr: 0x174C, symBinAddr: 0x68A4, symSize: 0x34 }
  - { offsetInCU: 0x23D6, offset: 0x2A90, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryCACycfC', symObjAddr: 0x1780, symBinAddr: 0x68D8, symSize: 0x34 }
  - { offsetInCU: 0x23E9, offset: 0x2AA3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryCACycfc', symObjAddr: 0x17B4, symBinAddr: 0x690C, symSize: 0xB0 }
  - { offsetInCU: 0x105, offset: 0x2DDC, size: 0x8, addend: 0x0, symName: '_$sypSgWOh', symObjAddr: 0x6A0, symBinAddr: 0x79EC, symSize: 0x40 }
  - { offsetInCU: 0x118, offset: 0x2DEF, size: 0x8, addend: 0x0, symName: '_$sypWOc', symObjAddr: 0x6E0, symBinAddr: 0x7A2C, symSize: 0x3C }
  - { offsetInCU: 0x12B, offset: 0x2E02, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit25AuthenticationTokenHeaderCMa', symObjAddr: 0x71C, symBinAddr: 0x7A68, symSize: 0x20 }
  - { offsetInCU: 0x1ED, offset: 0x2EC4, size: 0x8, addend: 0x0, symName: '_$ss11AnyHashableVWOc', symObjAddr: 0xB18, symBinAddr: 0x7E64, symSize: 0x3C }
  - { offsetInCU: 0x200, offset: 0x2ED7, size: 0x8, addend: 0x0, symName: '_$ss11AnyHashableVWOh', symObjAddr: 0xB54, symBinAddr: 0x7EA0, symSize: 0x34 }
  - { offsetInCU: 0x35C, offset: 0x3033, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit25AuthenticationTokenHeaderC17fromEncodedStringACSgSS_tcfC', symObjAddr: 0x0, symBinAddr: 0x73D0, symSize: 0x48 }
  - { offsetInCU: 0x36F, offset: 0x3046, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit25AuthenticationTokenHeaderC3kidSSvg', symObjAddr: 0x48, symBinAddr: 0x7418, symSize: 0x2C }
  - { offsetInCU: 0x3A0, offset: 0x3077, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit25AuthenticationTokenHeaderC17fromEncodedStringACSgSS_tcfc', symObjAddr: 0x74, symBinAddr: 0x7444, symSize: 0x504 }
  - { offsetInCU: 0x4C6, offset: 0x319D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit25AuthenticationTokenHeaderCfd', symObjAddr: 0x5FC, symBinAddr: 0x7948, symSize: 0x1C }
  - { offsetInCU: 0x4FE, offset: 0x31D5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit25AuthenticationTokenHeaderCfD', symObjAddr: 0x618, symBinAddr: 0x7964, symSize: 0x24 }
  - { offsetInCU: 0x547, offset: 0x321E, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4findys10_HashTableV6BucketV6bucket_Sb5foundtxSHRzlFSS_Tg5', symObjAddr: 0x63C, symBinAddr: 0x7988, symSize: 0x64 }
  - { offsetInCU: 0x582, offset: 0x3259, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4findys10_HashTableV6BucketV6bucket_Sb5foundtxSHRzlFs11AnyHashableV_Tg5', symObjAddr: 0x750, symBinAddr: 0x7A9C, symSize: 0x30 }
  - { offsetInCU: 0x5AF, offset: 0x3286, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4findys10_HashTableV6BucketV6bucket_Sb5foundtxSHRzlFSo26FBSDKAppEventParameterNamea_Tg5', symObjAddr: 0x780, symBinAddr: 0x7ACC, symSize: 0x80 }
  - { offsetInCU: 0x5EA, offset: 0x32C1, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4find_9hashValues10_HashTableV6BucketV6bucket_Sb5foundtx_SitSHRzlFSS_Tg5', symObjAddr: 0x800, symBinAddr: 0x7B4C, symSize: 0xE0 }
  - { offsetInCU: 0x658, offset: 0x332F, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4find_9hashValues10_HashTableV6BucketV6bucket_Sb5foundtx_SitSHRzlFs11AnyHashableV_Tg5', symObjAddr: 0x8E0, symBinAddr: 0x7C2C, symSize: 0xC4 }
  - { offsetInCU: 0x67F, offset: 0x3356, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4find_9hashValues10_HashTableV6BucketV6bucket_Sb5foundtx_SitSHRzlFSo26FBSDKAppEventParameterNamea_Tg5', symObjAddr: 0x9A4, symBinAddr: 0x7CF0, symSize: 0x174 }
  - { offsetInCU: 0x2B, offset: 0x33E5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12CodeVerifierC5valueSSvgTo', symObjAddr: 0x0, symBinAddr: 0x7ED4, symSize: 0x4C }
  - { offsetInCU: 0x61, offset: 0x341B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12CodeVerifierC5valueSSvgTo', symObjAddr: 0x0, symBinAddr: 0x7ED4, symSize: 0x4C }
  - { offsetInCU: 0x99, offset: 0x3453, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12CodeVerifierC9challengeSSvgTo', symObjAddr: 0x84, symBinAddr: 0x7F58, symSize: 0x58 }
  - { offsetInCU: 0x14B, offset: 0x3505, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12CodeVerifierC6stringACSgSS_tcfcTo', symObjAddr: 0xA5C, symBinAddr: 0x88EC, symSize: 0x28 }
  - { offsetInCU: 0x190, offset: 0x354A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12CodeVerifierCACycfcTo', symObjAddr: 0xD40, symBinAddr: 0x8BD0, symSize: 0x20 }
  - { offsetInCU: 0x400, offset: 0x37BA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12CodeVerifierCfETo', symObjAddr: 0xD94, symBinAddr: 0x8C24, symSize: 0x14 }
  - { offsetInCU: 0x42D, offset: 0x37E7, size: 0x8, addend: 0x0, symName: '_$s10Foundation13__DataStorageC15withUnsafeBytes2in5applyxSnySiG_xSWKXEtKlFyt_Tg554$s13FBSDKLoginKit12CodeVerifierC9challengeSSvgySWXEfU_AA0B0VSays5UInt8VGTf1nncn_n', symObjAddr: 0xDA8, symBinAddr: 0x8C38, symSize: 0xCC }
  - { offsetInCU: 0x455, offset: 0x380F, size: 0x8, addend: 0x0, symName: '_$sSays5UInt8VGSayxG10Foundation15ContiguousBytesAeBRszlWl', symObjAddr: 0xF64, symBinAddr: 0x8D04, symSize: 0x4C }
  - { offsetInCU: 0x468, offset: 0x3822, size: 0x8, addend: 0x0, symName: ___swift_instantiateConcreteTypeFromMangledNameAbstract, symObjAddr: 0xFB0, symBinAddr: 0x8D50, symSize: 0x44 }
  - { offsetInCU: 0x47B, offset: 0x3835, size: 0x8, addend: 0x0, symName: ___swift_project_boxed_opaque_existential_1, symObjAddr: 0xFF4, symBinAddr: 0x8D94, symSize: 0x24 }
  - { offsetInCU: 0x78B, offset: 0x3B45, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV06InlineB0VyAESWcfCTf4nd_n', symObjAddr: 0x1460, symBinAddr: 0x9200, symSize: 0xC4 }
  - { offsetInCU: 0x7FB, offset: 0x3BB5, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV10LargeSliceVyAESWcfCTf4nd_n', symObjAddr: 0x1524, symBinAddr: 0x92C4, symSize: 0x78 }
  - { offsetInCU: 0x826, offset: 0x3BE0, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV11InlineSliceVyAESWcfCTf4nd_n', symObjAddr: 0x159C, symBinAddr: 0x933C, symSize: 0x80 }
  - { offsetInCU: 0x8B3, offset: 0x3C6D, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV15_RepresentationOyAESWcfCTf4nd_n', symObjAddr: 0x161C, symBinAddr: 0x93BC, symSize: 0x68 }
  - { offsetInCU: 0x904, offset: 0x3CBE, size: 0x8, addend: 0x0, symName: ___swift_destroy_boxed_opaque_existential_1, symObjAddr: 0x1684, symBinAddr: 0x9424, symSize: 0x20 }
  - { offsetInCU: 0x917, offset: 0x3CD1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12CodeVerifierCMa', symObjAddr: 0x16A4, symBinAddr: 0x9444, symSize: 0x20 }
  - { offsetInCU: 0xB89, offset: 0x3F43, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12CodeVerifierC5valueSSvg', symObjAddr: 0x4C, symBinAddr: 0x7F20, symSize: 0x38 }
  - { offsetInCU: 0xBF9, offset: 0x3FB3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12CodeVerifierC9challengeSSvg', symObjAddr: 0xDC, symBinAddr: 0x7FB0, symSize: 0x448 }
  - { offsetInCU: 0xED3, offset: 0x428D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12CodeVerifierC9challengeSSvgySWXEfU_', symObjAddr: 0x534, symBinAddr: 0x8408, symSize: 0xDC }
  - { offsetInCU: 0x111C, offset: 0x44D6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12CodeVerifierC6stringACSgSS_tcfC', symObjAddr: 0x664, symBinAddr: 0x84F4, symSize: 0x1F8 }
  - { offsetInCU: 0x11AE, offset: 0x4568, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12CodeVerifierC6stringACSgSS_tcfc', symObjAddr: 0x85C, symBinAddr: 0x86EC, symSize: 0x200 }
  - { offsetInCU: 0x123C, offset: 0x45F6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12CodeVerifierCACycfC', symObjAddr: 0xA84, symBinAddr: 0x8914, symSize: 0x20 }
  - { offsetInCU: 0x1279, offset: 0x4633, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12CodeVerifierCACycfc', symObjAddr: 0xAA4, symBinAddr: 0x8934, symSize: 0x29C }
  - { offsetInCU: 0x13CD, offset: 0x4787, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12CodeVerifierCfD', symObjAddr: 0xD60, symBinAddr: 0x8BF0, symSize: 0x34 }
  - { offsetInCU: 0x1418, offset: 0x47D2, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtFs5UInt8V_Tg5', symObjAddr: 0x1018, symBinAddr: 0x8DB8, symSize: 0xE8 }
  - { offsetInCU: 0x153F, offset: 0x48F9, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtF13FBSDKLoginKit18DeviceLoginManagerC_Tg5', symObjAddr: 0x1100, symBinAddr: 0x8EA0, symSize: 0x160 }
  - { offsetInCU: 0x169A, offset: 0x4A54, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtFSS_Tg5', symObjAddr: 0x1260, symBinAddr: 0x9000, symSize: 0x104 }
  - { offsetInCU: 0x17AF, offset: 0x4B69, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV13_copyContents8subRange12initializingSpyxGSnySiG_AFtF13FBSDKLoginKit18DeviceLoginManagerC_Tg5', symObjAddr: 0x1364, symBinAddr: 0x9104, symSize: 0xFC }
  - { offsetInCU: 0x27, offset: 0x4D00, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15DefaultAudienceO8rawValueACSgSu_tcfC', symObjAddr: 0x0, symBinAddr: 0x9470, symSize: 0x14 }
  - { offsetInCU: 0x73, offset: 0x4D4C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15DefaultAudienceOSYAASY8rawValuexSg03RawF0Qz_tcfCTW', symObjAddr: 0x120, symBinAddr: 0x9590, symSize: 0x20 }
  - { offsetInCU: 0xA2, offset: 0x4D7B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15DefaultAudienceOSYAASY8rawValue03RawF0QzvgTW', symObjAddr: 0x140, symBinAddr: 0x95B0, symSize: 0xC }
  - { offsetInCU: 0xC8, offset: 0x4DA1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15DefaultAudienceOSHAASQWb', symObjAddr: 0x2C, symBinAddr: 0x949C, symSize: 0x4 }
  - { offsetInCU: 0xDB, offset: 0x4DB4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15DefaultAudienceOACSQAAWl', symObjAddr: 0x30, symBinAddr: 0x94A0, symSize: 0x44 }
  - { offsetInCU: 0x10C, offset: 0x4DE5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15DefaultAudienceOMa', symObjAddr: 0x14C, symBinAddr: 0x95BC, symSize: 0x10 }
  - { offsetInCU: 0x14D, offset: 0x4E26, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15DefaultAudienceOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x18, symBinAddr: 0x9488, symSize: 0x14 }
  - { offsetInCU: 0x1F5, offset: 0x4ECE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15DefaultAudienceOSHAASH9hashValueSivgTW', symObjAddr: 0x74, symBinAddr: 0x94E4, symSize: 0x44 }
  - { offsetInCU: 0x29C, offset: 0x4F75, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15DefaultAudienceOSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0xB8, symBinAddr: 0x9528, symSize: 0x28 }
  - { offsetInCU: 0x2EB, offset: 0x4FC4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15DefaultAudienceOSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0xE0, symBinAddr: 0x9550, symSize: 0x40 }
  - { offsetInCU: 0x3F0, offset: 0x50C9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15DefaultAudienceO8rawValueACSgSu_tcfC', symObjAddr: 0x0, symBinAddr: 0x9470, symSize: 0x14 }
  - { offsetInCU: 0x40D, offset: 0x50E6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15DefaultAudienceO8rawValueSuvg', symObjAddr: 0x14, symBinAddr: 0x9484, symSize: 0x4 }
  - { offsetInCU: 0xE4, offset: 0x5221, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerCAA17DependentAsObjectA2aDP15setDependenciesyy0hJ0QzFTW', symObjAddr: 0x434, symBinAddr: 0x9A00, symSize: 0x68 }
  - { offsetInCU: 0x1B9, offset: 0x52F6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCAA17DependentAsObjectA2aDP15setDependenciesyy0gI0QzFTW', symObjAddr: 0x49C, symBinAddr: 0x9A68, symSize: 0x68 }
  - { offsetInCU: 0x235, offset: 0x5372, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesVWOc', symObjAddr: 0x504, symBinAddr: 0x9AD0, symSize: 0x3C }
  - { offsetInCU: 0x248, offset: 0x5385, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesVWOc', symObjAddr: 0x580, symBinAddr: 0x9B0C, symSize: 0x3C }
  - { offsetInCU: 0x34B, offset: 0x5488, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17DependentAsObjectPAAE15setDependenciesyy0eG0QzF', symObjAddr: 0x0, symBinAddr: 0x95CC, symSize: 0xDC }
  - { offsetInCU: 0x38B, offset: 0x54C8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17DependentAsObjectPAAE15getDependencies0eG0QzyKF', symObjAddr: 0xDC, symBinAddr: 0x96A8, symSize: 0x214 }
  - { offsetInCU: 0x3BC, offset: 0x54F9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17DependentAsObjectPAAE13dynamicMemberqd__Sgs7KeyPathCy0E12DependenciesQzqd__G_tcluig', symObjAddr: 0x2F0, symBinAddr: 0x98BC, symSize: 0x11C }
  - { offsetInCU: 0xED, offset: 0x573F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerCAA15DependentAsTypeA2aDP15setDependenciesyy0hJ0QzFZTW', symObjAddr: 0x434, symBinAddr: 0x9FC0, symSize: 0x98 }
  - { offsetInCU: 0x1A4, offset: 0x57F6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterCAA15DependentAsTypeA2aDP15setDependenciesyy0hJ0QzFZTW', symObjAddr: 0x4CC, symBinAddr: 0xA058, symSize: 0x98 }
  - { offsetInCU: 0x259, offset: 0x58AB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterVAA15DependentAsTypeA2aDP15setDependenciesyy0gI0QzFZTW', symObjAddr: 0x564, symBinAddr: 0xA0F0, symSize: 0x98 }
  - { offsetInCU: 0x2B2, offset: 0x5904, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesVWOc', symObjAddr: 0x5FC, symBinAddr: 0xA188, symSize: 0x3C }
  - { offsetInCU: 0x2C5, offset: 0x5917, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesVWOc', symObjAddr: 0x678, symBinAddr: 0xA1C4, symSize: 0x3C }
  - { offsetInCU: 0x2D8, offset: 0x592A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC16TypeDependenciesVWOc', symObjAddr: 0x6B4, symBinAddr: 0xA200, symSize: 0x3C }
  - { offsetInCU: 0x3BA, offset: 0x5A0C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15DependentAsTypePAAE15setDependenciesyy0eG0QzFZ', symObjAddr: 0x0, symBinAddr: 0x9B8C, symSize: 0xDC }
  - { offsetInCU: 0x3FA, offset: 0x5A4C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15DependentAsTypePAAE15getDependencies0eG0QzyKFZ', symObjAddr: 0xDC, symBinAddr: 0x9C68, symSize: 0x214 }
  - { offsetInCU: 0x42B, offset: 0x5A7D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15DependentAsTypePAAE13dynamicMemberqd__Sgs7KeyPathCy0E12DependenciesQzqd__G_tcluigZ', symObjAddr: 0x2F0, symBinAddr: 0x9E7C, symSize: 0x11C }
  - { offsetInCU: 0x8C, offset: 0x5C25, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoC15verificationURL10Foundation0H0VvgTo', symObjAddr: 0xD0, symBinAddr: 0xA38C, symSize: 0x28 }
  - { offsetInCU: 0xD9, offset: 0x5C72, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoC14expirationDate10Foundation0H0VvgTo', symObjAddr: 0x10C, symBinAddr: 0xA3C8, symSize: 0x28 }
  - { offsetInCU: 0x126, offset: 0x5CBF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoC15pollingIntervalSuvgTo', symObjAddr: 0x188, symBinAddr: 0xA444, symSize: 0x10 }
  - { offsetInCU: 0x146, offset: 0x5CDF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoC15pollingIntervalSuvgTo', symObjAddr: 0x188, symBinAddr: 0xA444, symSize: 0x10 }
  - { offsetInCU: 0x1BB, offset: 0x5D54, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoC10identifier05loginE015verificationURL14expirationDate15pollingIntervalACSS_SS10Foundation0J0VAI0L0VSutcfcTo', symObjAddr: 0x428, symBinAddr: 0xA6E4, symSize: 0x19C }
  - { offsetInCU: 0x259, offset: 0x5DF2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoCACycfcTo', symObjAddr: 0x610, symBinAddr: 0xA8CC, symSize: 0x2C }
  - { offsetInCU: 0x2E6, offset: 0x5E7F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoCfETo', symObjAddr: 0x670, symBinAddr: 0xA92C, symSize: 0x90 }
  - { offsetInCU: 0x314, offset: 0x5EAD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoCMU', symObjAddr: 0x700, symBinAddr: 0xA9BC, symSize: 0x8 }
  - { offsetInCU: 0x327, offset: 0x5EC0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoCMa', symObjAddr: 0x708, symBinAddr: 0xA9C4, symSize: 0x3C }
  - { offsetInCU: 0x33A, offset: 0x5ED3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoCMr', symObjAddr: 0x744, symBinAddr: 0xAA00, symSize: 0xA0 }
  - { offsetInCU: 0x424, offset: 0x5FBD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoC10identifierSSvg', symObjAddr: 0xC, symBinAddr: 0xA2C8, symSize: 0x38 }
  - { offsetInCU: 0x447, offset: 0x5FE0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoC05loginE0SSvg', symObjAddr: 0x98, symBinAddr: 0xA354, symSize: 0x38 }
  - { offsetInCU: 0x4A5, offset: 0x603E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoC15pollingIntervalSuvg', symObjAddr: 0x198, symBinAddr: 0xA454, symSize: 0x10 }
  - { offsetInCU: 0x523, offset: 0x60BC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoC10identifier05loginE015verificationURL14expirationDate15pollingIntervalACSS_SS10Foundation0J0VAI0L0VSutcfC', symObjAddr: 0x1A8, symBinAddr: 0xA464, symSize: 0x140 }
  - { offsetInCU: 0x591, offset: 0x612A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoC10identifier05loginE015verificationURL14expirationDate15pollingIntervalACSS_SS10Foundation0J0VAI0L0VSutcfc', symObjAddr: 0x2E8, symBinAddr: 0xA5A4, symSize: 0x140 }
  - { offsetInCU: 0x5EF, offset: 0x6188, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoCACycfC', symObjAddr: 0x5C4, symBinAddr: 0xA880, symSize: 0x20 }
  - { offsetInCU: 0x608, offset: 0x61A1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoCACycfc', symObjAddr: 0x5E4, symBinAddr: 0xA8A0, symSize: 0x2C }
  - { offsetInCU: 0x65C, offset: 0x61F5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoCfD', symObjAddr: 0x63C, symBinAddr: 0xA8F8, symSize: 0x34 }
  - { offsetInCU: 0x186, offset: 0x639C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV10Foundation13CustomNSErrorAadEP11errorDomainSSvgZTW', symObjAddr: 0x214, symBinAddr: 0xACC8, symSize: 0x5C }
  - { offsetInCU: 0x1BB, offset: 0x63D1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV10Foundation13CustomNSErrorAadEP9errorCodeSivgTW', symObjAddr: 0x270, symBinAddr: 0xAD24, symSize: 0x8 }
  - { offsetInCU: 0x1DA, offset: 0x63F0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV10Foundation13CustomNSErrorAadEP9errorCodeSivgTW', symObjAddr: 0x270, symBinAddr: 0xAD24, symSize: 0x8 }
  - { offsetInCU: 0x1EB, offset: 0x6401, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV10Foundation13CustomNSErrorAadEP13errorUserInfoSDySSypGvgTW', symObjAddr: 0x278, symBinAddr: 0xAD2C, symSize: 0x8 }
  - { offsetInCU: 0x20A, offset: 0x6420, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV10Foundation13CustomNSErrorAadEP13errorUserInfoSDySSypGvgTW', symObjAddr: 0x278, symBinAddr: 0xAD2C, symSize: 0x8 }
  - { offsetInCU: 0x21B, offset: 0x6431, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVSHAASH9hashValueSivgTW', symObjAddr: 0x280, symBinAddr: 0xAD34, symSize: 0x44 }
  - { offsetInCU: 0x2F5, offset: 0x650B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0x2C4, symBinAddr: 0xAD78, symSize: 0x28 }
  - { offsetInCU: 0x363, offset: 0x6579, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x384, symBinAddr: 0xAE38, symSize: 0x34 }
  - { offsetInCU: 0x40C, offset: 0x6622, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceLoginErrorCodeOSYAASY8rawValuexSg03RawH0Qz_tcfCTW', symObjAddr: 0x47C, symBinAddr: 0xAF30, symSize: 0x30 }
  - { offsetInCU: 0x43B, offset: 0x6651, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceLoginErrorCodeOSYAASY8rawValue03RawH0QzvgTW', symObjAddr: 0x4AC, symBinAddr: 0xAF60, symSize: 0xC }
  - { offsetInCU: 0x457, offset: 0x666D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceLoginErrorCodeO8rawValueACSgSi_tcfCTf4nd_n', symObjAddr: 0x4CC, symBinAddr: 0xAF80, symSize: 0x30 }
  - { offsetInCU: 0x4BB, offset: 0x66D1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV10Foundation13CustomNSErrorAAs0E0PWb', symObjAddr: 0x4FC, symBinAddr: 0xAFB0, symSize: 0x4 }
  - { offsetInCU: 0x4CE, offset: 0x66E4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVACs0E0AAWl', symObjAddr: 0x500, symBinAddr: 0xAFB4, symSize: 0x44 }
  - { offsetInCU: 0x4E1, offset: 0x66F7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVSHAASQWb', symObjAddr: 0x544, symBinAddr: 0xAFF8, symSize: 0x4 }
  - { offsetInCU: 0x4F4, offset: 0x670A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVACSQAAWl', symObjAddr: 0x548, symBinAddr: 0xAFFC, symSize: 0x44 }
  - { offsetInCU: 0x507, offset: 0x671D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceLoginErrorCodeOSHAASQWb', symObjAddr: 0x58C, symBinAddr: 0xB040, symSize: 0x4 }
  - { offsetInCU: 0x51A, offset: 0x6730, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceLoginErrorCodeOACSQAAWl', symObjAddr: 0x590, symBinAddr: 0xB044, symSize: 0x44 }
  - { offsetInCU: 0x52D, offset: 0x6743, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVwxx', symObjAddr: 0x5D8, symBinAddr: 0xB08C, symSize: 0x28 }
  - { offsetInCU: 0x540, offset: 0x6756, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVwca', symObjAddr: 0x640, symBinAddr: 0xB0F4, symSize: 0x64 }
  - { offsetInCU: 0x553, offset: 0x6769, size: 0x8, addend: 0x0, symName: ___swift_memcpy24_8, symObjAddr: 0x6A4, symBinAddr: 0xB158, symSize: 0x14 }
  - { offsetInCU: 0x566, offset: 0x677C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVwta', symObjAddr: 0x6B8, symBinAddr: 0xB16C, symSize: 0x44 }
  - { offsetInCU: 0x579, offset: 0x678F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVwet', symObjAddr: 0x6FC, symBinAddr: 0xB1B0, symSize: 0x48 }
  - { offsetInCU: 0x58C, offset: 0x67A2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVwst', symObjAddr: 0x744, symBinAddr: 0xB1F8, symSize: 0x40 }
  - { offsetInCU: 0x59F, offset: 0x67B5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVMa', symObjAddr: 0x784, symBinAddr: 0xB238, symSize: 0x10 }
  - { offsetInCU: 0x5B2, offset: 0x67C8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceLoginErrorCodeOMa', symObjAddr: 0x794, symBinAddr: 0xB248, symSize: 0x10 }
  - { offsetInCU: 0x5C5, offset: 0x67DB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVAC10Foundation13CustomNSErrorAAWl', symObjAddr: 0x7A4, symBinAddr: 0xB258, symSize: 0x44 }
  - { offsetInCU: 0x659, offset: 0x686F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0x2EC, symBinAddr: 0xADA0, symSize: 0x40 }
  - { offsetInCU: 0x6EF, offset: 0x6905, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVs0E0AAsADP9_userInfoyXlSgvgTW', symObjAddr: 0x37C, symBinAddr: 0xAE30, symSize: 0x4 }
  - { offsetInCU: 0x70A, offset: 0x6920, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVs0E0AAsADP19_getEmbeddedNSErroryXlSgyFTW', symObjAddr: 0x380, symBinAddr: 0xAE34, symSize: 0x4 }
  - { offsetInCU: 0x78A, offset: 0x69A0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceLoginErrorCodeOSHAASH9hashValueSivgTW', symObjAddr: 0x3D0, symBinAddr: 0xAE84, symSize: 0x44 }
  - { offsetInCU: 0x831, offset: 0x6A47, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceLoginErrorCodeOSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0x414, symBinAddr: 0xAEC8, symSize: 0x28 }
  - { offsetInCU: 0x880, offset: 0x6A96, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceLoginErrorCodeOSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0x43C, symBinAddr: 0xAEF0, symSize: 0x40 }
  - { offsetInCU: 0x905, offset: 0x6B1B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceLoginErrorCodeOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x4B8, symBinAddr: 0xAF6C, symSize: 0x14 }
  - { offsetInCU: 0x998, offset: 0x6BAE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVs0E0AAsADP7_domainSSvgTW', symObjAddr: 0x32C, symBinAddr: 0xADE0, symSize: 0x28 }
  - { offsetInCU: 0x9B4, offset: 0x6BCA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVs0E0AAsADP5_codeSivgTW', symObjAddr: 0x354, symBinAddr: 0xAE08, symSize: 0x28 }
  - { offsetInCU: 0xA3C, offset: 0x6C52, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV03_nsE0So7NSErrorCSgvg', symObjAddr: 0x0, symBinAddr: 0xAAB4, symSize: 0x28 }
  - { offsetInCU: 0xA4F, offset: 0x6C65, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV9errorCodeSivg', symObjAddr: 0x28, symBinAddr: 0xAADC, symSize: 0x8 }
  - { offsetInCU: 0xA63, offset: 0x6C79, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV13errorUserInfoSDySSypGvg', symObjAddr: 0x30, symBinAddr: 0xAAE4, symSize: 0x8 }
  - { offsetInCU: 0xA82, offset: 0x6C98, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV03_nsE0ACSo7NSErrorC_tcfC', symObjAddr: 0x38, symBinAddr: 0xAAEC, symSize: 0xA0 }
  - { offsetInCU: 0xAA5, offset: 0x6CBB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV_8userInfoAcA0cdE4CodeO_SDySSypGtcfC', symObjAddr: 0xD8, symBinAddr: 0xAB8C, symSize: 0xC }
  - { offsetInCU: 0xAD5, offset: 0x6CEB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceLoginErrorCodeO8rawValueSivg', symObjAddr: 0xE4, symBinAddr: 0xAB98, symSize: 0x4 }
  - { offsetInCU: 0xAFA, offset: 0x6D10, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV11errorDomainSSvgZ', symObjAddr: 0xE8, symBinAddr: 0xAB9C, symSize: 0x5C }
  - { offsetInCU: 0xB21, offset: 0x6D37, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV16excessivePollingAA0cdE4CodeOvgZ', symObjAddr: 0x144, symBinAddr: 0xABF8, symSize: 0xC }
  - { offsetInCU: 0xB42, offset: 0x6D58, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV21authorizationDeclinedAA0cdE4CodeOvgZ', symObjAddr: 0x150, symBinAddr: 0xAC04, symSize: 0xC }
  - { offsetInCU: 0xB63, offset: 0x6D79, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV20authorizationPendingAA0cdE4CodeOvgZ', symObjAddr: 0x15C, symBinAddr: 0xAC10, symSize: 0xC }
  - { offsetInCU: 0xB84, offset: 0x6D9A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV11codeExpiredAA0cdE4CodeOvgZ', symObjAddr: 0x168, symBinAddr: 0xAC1C, symSize: 0xC }
  - { offsetInCU: 0xBA5, offset: 0x6DBB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV2eeoiySbAC_ACtFZ', symObjAddr: 0x174, symBinAddr: 0xAC28, symSize: 0x34 }
  - { offsetInCU: 0xC14, offset: 0x6E2A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV4hash4intoys6HasherVz_tF', symObjAddr: 0x1A8, symBinAddr: 0xAC5C, symSize: 0x28 }
  - { offsetInCU: 0xC86, offset: 0x6E9C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV9hashValueSivg', symObjAddr: 0x1D0, symBinAddr: 0xAC84, symSize: 0x44 }
  - { offsetInCU: 0xDBE, offset: 0x6FD4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceLoginErrorCodeO8rawValueACSgSi_tcfC', symObjAddr: 0x3B8, symBinAddr: 0xAE6C, symSize: 0x18 }
  - { offsetInCU: 0x4E, offset: 0x7067, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC05loginE9Instances33_2E1868FF91A815585B124C0140A60DCBLLSayACGvpZ', symObjAddr: 0x73D8, symBinAddr: 0x5EC50, symSize: 0x0 }
  - { offsetInCU: 0x7C, offset: 0x7095, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC8delegateAA0cdE8Delegate_pSgvgTo', symObjAddr: 0x14, symBinAddr: 0xB2B0, symSize: 0x48 }
  - { offsetInCU: 0xD0, offset: 0x70E9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC8delegateAA0cdE8Delegate_pSgvsTo', symObjAddr: 0xA4, symBinAddr: 0xB340, symSize: 0x50 }
  - { offsetInCU: 0x150, offset: 0x7169, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC11permissionsSaySSGvgTo', symObjAddr: 0x228, symBinAddr: 0xB4C4, symSize: 0x48 }
  - { offsetInCU: 0x19D, offset: 0x71B6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC11redirectURL10Foundation0G0VSgvgTo', symObjAddr: 0x280, symBinAddr: 0xB51C, symSize: 0xD4 }
  - { offsetInCU: 0x1F1, offset: 0x720A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC11redirectURL10Foundation0G0VSgvsTo', symObjAddr: 0x3EC, symBinAddr: 0xB648, symSize: 0xFC }
  - { offsetInCU: 0x26B, offset: 0x7284, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC8codeInfoAA0cd4CodeG0CSgvgTo', symObjAddr: 0x590, symBinAddr: 0xB7EC, symSize: 0x48 }
  - { offsetInCU: 0x2BF, offset: 0x72D8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC8codeInfoAA0cd4CodeG0CSgvsTo', symObjAddr: 0x628, symBinAddr: 0xB884, symSize: 0x64 }
  - { offsetInCU: 0x37D, offset: 0x7396, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC11permissions011enableSmartD0ACSaySSG_SbtcfcTo', symObjAddr: 0xB20, symBinAddr: 0xBD7C, symSize: 0x38 }
  - { offsetInCU: 0x441, offset: 0x745A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC5startyyFTo', symObjAddr: 0x2518, symBinAddr: 0xD774, symSize: 0x28 }
  - { offsetInCU: 0x471, offset: 0x748A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC6cancelyyFTo', symObjAddr: 0x2714, symBinAddr: 0xD970, symSize: 0x28 }
  - { offsetInCU: 0x4B0, offset: 0x74C9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC14notifyDelegate5token14expirationDate020dataAccessExpirationJ0ySSSg_10Foundation0J0VSgALtFTo', symObjAddr: 0x3988, symBinAddr: 0xEBE4, symSize: 0x18C }
  - { offsetInCU: 0x4CB, offset: 0x74E4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC12processErroryys0G0_pFTo', symObjAddr: 0x3B14, symBinAddr: 0xED70, symSize: 0x50 }
  - { offsetInCU: 0x4E6, offset: 0x74FF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC12schedulePoll8intervalySu_tFTo', symObjAddr: 0x4D68, symBinAddr: 0xFFC4, symSize: 0x30 }
  - { offsetInCU: 0x52B, offset: 0x7544, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerCACycfcTo', symObjAddr: 0x4DE4, symBinAddr: 0x10040, symSize: 0x2C }
  - { offsetInCU: 0x5BD, offset: 0x75D6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerCAA17DependentAsObjectA2aDP22configuredDependencies0hJ0QzSgvsTW', symObjAddr: 0x5094, symBinAddr: 0x102F0, symSize: 0x60 }
  - { offsetInCU: 0x5FF, offset: 0x7618, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerCAA17DependentAsObjectA2aDP22configuredDependencies0hJ0QzSgvMTW', symObjAddr: 0x50F4, symBinAddr: 0x10350, symSize: 0x44 }
  - { offsetInCU: 0x63A, offset: 0x7653, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC05loginE9Instances33_2E1868FF91A815585B124C0140A60DCBLL_WZ', symObjAddr: 0x0, symBinAddr: 0xB29C, symSize: 0x14 }
  - { offsetInCU: 0x830, offset: 0x7849, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13DevicePolling_pWOb', symObjAddr: 0x80C, symBinAddr: 0xBA68, symSize: 0x18 }
  - { offsetInCU: 0xBD1, offset: 0x7BEA, size: 0x8, addend: 0x0, symName: '_$sSo27FBSDKGraphRequestConnecting_pSgypSgs5Error_pSgIeggng_AByXlSgSo7NSErrorCSgIeyByyy_TR', symObjAddr: 0x245C, symBinAddr: 0xD6B8, symSize: 0xBC }
  - { offsetInCU: 0xE2F, offset: 0x7E48, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerCfETo', symObjAddr: 0x4E44, symBinAddr: 0x100A0, symSize: 0x94 }
  - { offsetInCU: 0xE7A, offset: 0x7E93, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC10netService_13didNotPublishySo05NSNetG0C_SDySSSo8NSNumberCGtFTo', symObjAddr: 0x4EDC, symBinAddr: 0x10138, symSize: 0x50 }
  - { offsetInCU: 0xF01, offset: 0x7F1A, size: 0x8, addend: 0x0, symName: '_$sSaySSGSayxGSKsWl', symObjAddr: 0x52E4, symBinAddr: 0x10540, symSize: 0x4C }
  - { offsetInCU: 0xF3D, offset: 0x7F56, size: 0x8, addend: 0x0, symName: '_$ss13_parseInteger5ascii5radixq_Sgx_SitSyRzs010FixedWidthB0R_r0_lFSS_SiTg5', symObjAddr: 0x5374, symBinAddr: 0x1058C, symSize: 0xE4 }
  - { offsetInCU: 0xFEC, offset: 0x8005, size: 0x8, addend: 0x0, symName: '_$ss13_parseInteger5ascii5radixq_Sgx_SitSyRzs010FixedWidthB0R_r0_lFADSRys5UInt8VGXEfU_SS_SiTg5', symObjAddr: 0x5458, symBinAddr: 0x10670, symSize: 0x284 }
  - { offsetInCU: 0x1185, offset: 0x819E, size: 0x8, addend: 0x0, symName: '_$ss22_ContiguousArrayBufferV19_uninitializedCount15minimumCapacityAByxGSi_SitcfCs5UInt8V_Tgmq5', symObjAddr: 0x5A08, symBinAddr: 0x10C20, symSize: 0x64 }
  - { offsetInCU: 0x12E5, offset: 0x82FE, size: 0x8, addend: 0x0, symName: '_$sSS11withCStringyxxSPys4Int8VGKXEKlFSb_Tg5024$sSdySdSgxcSyRzlufcSbSpyf6GXEfU_j5SPys4C7VGXEfU_SpySdGTf1cn_n', symObjAddr: 0x5D60, symBinAddr: 0x10F78, symSize: 0x10C }
  - { offsetInCU: 0x1441, offset: 0x845A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC5startyyFySo27FBSDKGraphRequestConnecting_pSg_ypSgs5Error_pSgtcfU_TA', symObjAddr: 0x5EA4, symBinAddr: 0x11088, symSize: 0xC }
  - { offsetInCU: 0x1454, offset: 0x846D, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x5EB0, symBinAddr: 0x11094, symSize: 0x10 }
  - { offsetInCU: 0x1467, offset: 0x8480, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x5EC0, symBinAddr: 0x110A4, symSize: 0x8 }
  - { offsetInCU: 0x147A, offset: 0x8493, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesVWOh', symObjAddr: 0x5EC8, symBinAddr: 0x110AC, symSize: 0x2C }
  - { offsetInCU: 0x148D, offset: 0x84A6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13DevicePolling_pWOc', symObjAddr: 0x5F30, symBinAddr: 0x110D8, symSize: 0x44 }
  - { offsetInCU: 0x14A0, offset: 0x84B9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC12schedulePoll8intervalySu_tFyycfU_TA', symObjAddr: 0x5FE8, symBinAddr: 0x1116C, symSize: 0xC }
  - { offsetInCU: 0x1602, offset: 0x861B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC14notifyDelegate5token14expirationDate020dataAccessExpirationJ0ySSSg_10Foundation0J0VSgALtFyAA0cdE6ResultCcfU_TA', symObjAddr: 0x6834, symBinAddr: 0x119B8, symSize: 0x8 }
  - { offsetInCU: 0x162A, offset: 0x8643, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DateVSgWOb', symObjAddr: 0x6960, symBinAddr: 0x11AE4, symSize: 0x48 }
  - { offsetInCU: 0x163D, offset: 0x8656, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC14notifyDelegate5token14expirationDate020dataAccessExpirationJ0ySSSg_10Foundation0J0VSgALtFySo27FBSDKGraphRequestConnecting_pSg_ypSgs5Error_pSgtcfU0_TA', symObjAddr: 0x69A8, symBinAddr: 0x11B2C, symSize: 0xA0 }
  - { offsetInCU: 0x166C, offset: 0x8685, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerCMU', symObjAddr: 0x6C3C, symBinAddr: 0x11DC0, symSize: 0x8 }
  - { offsetInCU: 0x167F, offset: 0x8698, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerCMa', symObjAddr: 0x6C44, symBinAddr: 0x11DC8, symSize: 0x3C }
  - { offsetInCU: 0x1692, offset: 0x86AB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerCMr', symObjAddr: 0x6C80, symBinAddr: 0x11E04, symSize: 0xA4 }
  - { offsetInCU: 0x16A5, offset: 0x86BE, size: 0x8, addend: 0x0, symName: '_$s10Foundation3URLVSgMa', symObjAddr: 0x6D38, symBinAddr: 0x11EBC, symSize: 0x54 }
  - { offsetInCU: 0x16B8, offset: 0x86D1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesVwCP', symObjAddr: 0x6D8C, symBinAddr: 0x11F10, symSize: 0x30 }
  - { offsetInCU: 0x16CB, offset: 0x86E4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesVwxx', symObjAddr: 0x6DBC, symBinAddr: 0x11F40, symSize: 0x3C }
  - { offsetInCU: 0x16DE, offset: 0x86F7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesVwcp', symObjAddr: 0x6DF8, symBinAddr: 0x11F7C, symSize: 0x6C }
  - { offsetInCU: 0x16F1, offset: 0x870A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesVwca', symObjAddr: 0x6E64, symBinAddr: 0x11FE8, symSize: 0x90 }
  - { offsetInCU: 0x1704, offset: 0x871D, size: 0x8, addend: 0x0, symName: ___swift_assign_boxed_opaque_existential_1, symObjAddr: 0x6EF4, symBinAddr: 0x12078, symSize: 0x168 }
  - { offsetInCU: 0x1717, offset: 0x8730, size: 0x8, addend: 0x0, symName: ___swift_memcpy72_8, symObjAddr: 0x705C, symBinAddr: 0x121E0, symSize: 0x24 }
  - { offsetInCU: 0x172A, offset: 0x8743, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesVwta', symObjAddr: 0x7080, symBinAddr: 0x12204, symSize: 0x70 }
  - { offsetInCU: 0x173D, offset: 0x8756, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesVwet', symObjAddr: 0x70F0, symBinAddr: 0x12274, symSize: 0x48 }
  - { offsetInCU: 0x1750, offset: 0x8769, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesVwst', symObjAddr: 0x7138, symBinAddr: 0x122BC, symSize: 0x50 }
  - { offsetInCU: 0x1763, offset: 0x877C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesVMa', symObjAddr: 0x7188, symBinAddr: 0x1230C, symSize: 0x10 }
  - { offsetInCU: 0x1776, offset: 0x878F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26DeviceLoginManagerDelegate_pSgXwWOh', symObjAddr: 0x7198, symBinAddr: 0x1231C, symSize: 0x24 }
  - { offsetInCU: 0x1789, offset: 0x87A2, size: 0x8, addend: 0x0, symName: '_$s10Foundation25NSFastEnumerationIteratorVACStAAWl', symObjAddr: 0x723C, symBinAddr: 0x123C0, symSize: 0x48 }
  - { offsetInCU: 0x179C, offset: 0x87B5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC12schedulePoll8intervalySu_tFyycfU_ySo27FBSDKGraphRequestConnecting_pSg_ypSgs5Error_pSgtcfU_TA', symObjAddr: 0x72B8, symBinAddr: 0x1242C, symSize: 0xC }
  - { offsetInCU: 0x17AF, offset: 0x87C8, size: 0x8, addend: 0x0, symName: '_$sSdySdSgxcSyRzlufcSbSpySdGXEfU_SbSPys4Int8VGXEfU_TA', symObjAddr: 0x72FC, symBinAddr: 0x12470, symSize: 0x6C }
  - { offsetInCU: 0x1AA1, offset: 0x8ABA, size: 0x8, addend: 0x0, symName: '_$ss17FixedWidthIntegerPsEyxSgSScfCSi_Tgm5', symObjAddr: 0x49B0, symBinAddr: 0xFC0C, symSize: 0x3B8 }
  - { offsetInCU: 0x216C, offset: 0x9185, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC8delegateAA0cdE8Delegate_pSgvg', symObjAddr: 0x5C, symBinAddr: 0xB2F8, symSize: 0x48 }
  - { offsetInCU: 0x21B1, offset: 0x91CA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC8delegateAA0cdE8Delegate_pSgvs', symObjAddr: 0xF4, symBinAddr: 0xB390, symSize: 0x58 }
  - { offsetInCU: 0x21D7, offset: 0x91F0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC8delegateAA0cdE8Delegate_pSgvM', symObjAddr: 0x14C, symBinAddr: 0xB3E8, symSize: 0x70 }
  - { offsetInCU: 0x21FA, offset: 0x9213, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC8delegateAA0cdE8Delegate_pSgvM.resume.0', symObjAddr: 0x1BC, symBinAddr: 0xB458, symSize: 0x6C }
  - { offsetInCU: 0x222B, offset: 0x9244, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC11permissionsSaySSGvg', symObjAddr: 0x270, symBinAddr: 0xB50C, symSize: 0x10 }
  - { offsetInCU: 0x2258, offset: 0x9271, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC11redirectURL10Foundation0G0VSgvg', symObjAddr: 0x354, symBinAddr: 0xB5F0, symSize: 0x58 }
  - { offsetInCU: 0x2297, offset: 0x92B0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC11redirectURL10Foundation0G0VSgvs', symObjAddr: 0x4E8, symBinAddr: 0xB744, symSize: 0x60 }
  - { offsetInCU: 0x22BD, offset: 0x92D6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC11redirectURL10Foundation0G0VSgvM', symObjAddr: 0x548, symBinAddr: 0xB7A4, symSize: 0x44 }
  - { offsetInCU: 0x22E0, offset: 0x92F9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC11redirectURL10Foundation0G0VSgvM.resume.0', symObjAddr: 0x58C, symBinAddr: 0xB7E8, symSize: 0x4 }
  - { offsetInCU: 0x2311, offset: 0x932A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC8codeInfoAA0cd4CodeG0CSgvg', symObjAddr: 0x5D8, symBinAddr: 0xB834, symSize: 0x50 }
  - { offsetInCU: 0x2350, offset: 0x9369, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC8codeInfoAA0cd4CodeG0CSgvs', symObjAddr: 0x68C, symBinAddr: 0xB8E8, symSize: 0x50 }
  - { offsetInCU: 0x2376, offset: 0x938F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC8codeInfoAA0cd4CodeG0CSgvM', symObjAddr: 0x6DC, symBinAddr: 0xB938, symSize: 0x44 }
  - { offsetInCU: 0x2399, offset: 0x93B2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC22configuredDependenciesAC06ObjectG0VSgvM', symObjAddr: 0x77C, symBinAddr: 0xB9D8, symSize: 0x44 }
  - { offsetInCU: 0x23BC, offset: 0x93D5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV12devicePoller12errorFactory012graphRequestK015internalUtility8settingsAeA0C7Polling_p_So18FBSDKErrorCreating_pSo010FBSDKGraphmK0_pSo013FBSDKInternalO0_p09FBSDKCoreB016SettingsProtocol_ptcfC', symObjAddr: 0x7C0, symBinAddr: 0xBA1C, symSize: 0x4C }
  - { offsetInCU: 0x23CF, offset: 0x93E8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC19defaultDependenciesAC06ObjectG0VSgvM', symObjAddr: 0x8EC, symBinAddr: 0xBB48, symSize: 0x44 }
  - { offsetInCU: 0x23F8, offset: 0x9411, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC11permissions011enableSmartD0ACSaySSG_SbtcfC', symObjAddr: 0x930, symBinAddr: 0xBB8C, symSize: 0x40 }
  - { offsetInCU: 0x242F, offset: 0x9448, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC11permissions011enableSmartD0ACSaySSG_Sbtcfc', symObjAddr: 0x970, symBinAddr: 0xBBCC, symSize: 0x1B0 }
  - { offsetInCU: 0x2566, offset: 0x957F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC5startyyF', symObjAddr: 0xB58, symBinAddr: 0xBDB4, symSize: 0x5B0 }
  - { offsetInCU: 0x27CC, offset: 0x97E5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC5startyyFySo27FBSDKGraphRequestConnecting_pSg_ypSgs5Error_pSgtcfU_', symObjAddr: 0x1108, symBinAddr: 0xC364, symSize: 0xC0C }
  - { offsetInCU: 0x2C9B, offset: 0x9CB4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC12processErroryys0G0_pF', symObjAddr: 0x1D14, symBinAddr: 0xCF70, symSize: 0x2F8 }
  - { offsetInCU: 0x2E0E, offset: 0x9E27, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC14notifyDelegate33_2E1868FF91A815585B124C0140A60DCBLL5errorys5Error_p_tF', symObjAddr: 0x2068, symBinAddr: 0xD2C4, symSize: 0x238 }
  - { offsetInCU: 0x2F79, offset: 0x9F92, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC12schedulePoll8intervalySu_tF', symObjAddr: 0x22A0, symBinAddr: 0xD4FC, symSize: 0x1BC }
  - { offsetInCU: 0x3039, offset: 0xA052, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC12schedulePoll8intervalySu_tFyycfU_', symObjAddr: 0x3B64, symBinAddr: 0xEDC0, symSize: 0x360 }
  - { offsetInCU: 0x317D, offset: 0xA196, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC12schedulePoll8intervalySu_tFyycfU_ySo27FBSDKGraphRequestConnecting_pSg_ypSgs5Error_pSgtcfU_', symObjAddr: 0x3EC4, symBinAddr: 0xF120, symSize: 0xAEC }
  - { offsetInCU: 0x363D, offset: 0xA656, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC6cancelyyF', symObjAddr: 0x2540, symBinAddr: 0xD79C, symSize: 0x1D4 }
  - { offsetInCU: 0x37DC, offset: 0xA7F5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC14notifyDelegate5token14expirationDate020dataAccessExpirationJ0ySSSg_10Foundation0J0VSgALtF', symObjAddr: 0x273C, symBinAddr: 0xD998, symSize: 0x734 }
  - { offsetInCU: 0x3BD4, offset: 0xABED, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC14notifyDelegate5token14expirationDate020dataAccessExpirationJ0ySSSg_10Foundation0J0VSgALtFySo27FBSDKGraphRequestConnecting_pSg_ypSgs5Error_pSgtcfU0_', symObjAddr: 0x2E70, symBinAddr: 0xE0CC, symSize: 0xB18 }
  - { offsetInCU: 0x4197, offset: 0xB1B0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC14notifyDelegate5token14expirationDate020dataAccessExpirationJ0ySSSg_10Foundation0J0VSgALtFyAA0cdE6ResultCcfU_Tf4nnd_n', symObjAddr: 0x62F0, symBinAddr: 0x11474, symSize: 0x158 }
  - { offsetInCU: 0x4372, offset: 0xB38B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerCACycfC', symObjAddr: 0x4D98, symBinAddr: 0xFFF4, symSize: 0x20 }
  - { offsetInCU: 0x4385, offset: 0xB39E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerCACycfc', symObjAddr: 0x4DB8, symBinAddr: 0x10014, symSize: 0x2C }
  - { offsetInCU: 0x43D9, offset: 0xB3F2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerCfD', symObjAddr: 0x4E10, symBinAddr: 0x1006C, symSize: 0x34 }
  - { offsetInCU: 0x43FA, offset: 0xB413, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC10netService_13didNotPublishySo05NSNetG0C_SDySSSo8NSNumberCGtF', symObjAddr: 0x4ED8, symBinAddr: 0x10134, symSize: 0x4 }
  - { offsetInCU: 0x4414, offset: 0xB42D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV12devicePollerAA0C7Polling_pvg', symObjAddr: 0x4F2C, symBinAddr: 0x10188, symSize: 0xC }
  - { offsetInCU: 0x4427, offset: 0xB440, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV12devicePollerAA0C7Polling_pvs', symObjAddr: 0x4F38, symBinAddr: 0x10194, symSize: 0x2C }
  - { offsetInCU: 0x443A, offset: 0xB453, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV12devicePollerAA0C7Polling_pvM', symObjAddr: 0x4F64, symBinAddr: 0x101C0, symSize: 0x10 }
  - { offsetInCU: 0x444D, offset: 0xB466, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV12devicePollerAA0C7Polling_pvM.resume.0', symObjAddr: 0x4F74, symBinAddr: 0x101D0, symSize: 0x4 }
  - { offsetInCU: 0x4460, offset: 0xB479, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV12errorFactorySo18FBSDKErrorCreating_pvg', symObjAddr: 0x4F78, symBinAddr: 0x101D4, symSize: 0x8 }
  - { offsetInCU: 0x4473, offset: 0xB48C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV12errorFactorySo18FBSDKErrorCreating_pvs', symObjAddr: 0x4F80, symBinAddr: 0x101DC, symSize: 0x28 }
  - { offsetInCU: 0x4486, offset: 0xB49F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV12errorFactorySo18FBSDKErrorCreating_pvM', symObjAddr: 0x4FA8, symBinAddr: 0x10204, symSize: 0x10 }
  - { offsetInCU: 0x4499, offset: 0xB4B2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV12errorFactorySo18FBSDKErrorCreating_pvM.resume.0', symObjAddr: 0x4FB8, symBinAddr: 0x10214, symSize: 0x4 }
  - { offsetInCU: 0x44AC, offset: 0xB4C5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV19graphRequestFactorySo010FBSDKGraphiJ0_pvg', symObjAddr: 0x4FBC, symBinAddr: 0x10218, symSize: 0x8 }
  - { offsetInCU: 0x44BF, offset: 0xB4D8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV19graphRequestFactorySo010FBSDKGraphiJ0_pvs', symObjAddr: 0x4FC4, symBinAddr: 0x10220, symSize: 0x28 }
  - { offsetInCU: 0x44D2, offset: 0xB4EB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV19graphRequestFactorySo010FBSDKGraphiJ0_pvM', symObjAddr: 0x4FEC, symBinAddr: 0x10248, symSize: 0x10 }
  - { offsetInCU: 0x44E5, offset: 0xB4FE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV19graphRequestFactorySo010FBSDKGraphiJ0_pvM.resume.0', symObjAddr: 0x4FFC, symBinAddr: 0x10258, symSize: 0x4 }
  - { offsetInCU: 0x44F8, offset: 0xB511, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV15internalUtilitySo013FBSDKInternalI0_pvg', symObjAddr: 0x5000, symBinAddr: 0x1025C, symSize: 0x8 }
  - { offsetInCU: 0x450B, offset: 0xB524, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV15internalUtilitySo013FBSDKInternalI0_pvs', symObjAddr: 0x5008, symBinAddr: 0x10264, symSize: 0x28 }
  - { offsetInCU: 0x451E, offset: 0xB537, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV15internalUtilitySo013FBSDKInternalI0_pvM', symObjAddr: 0x5030, symBinAddr: 0x1028C, symSize: 0x10 }
  - { offsetInCU: 0x4531, offset: 0xB54A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV15internalUtilitySo013FBSDKInternalI0_pvM.resume.0', symObjAddr: 0x5040, symBinAddr: 0x1029C, symSize: 0x4 }
  - { offsetInCU: 0x4544, offset: 0xB55D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV8settings09FBSDKCoreB016SettingsProtocol_pvg', symObjAddr: 0x5044, symBinAddr: 0x102A0, symSize: 0x8 }
  - { offsetInCU: 0x4557, offset: 0xB570, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV8settings09FBSDKCoreB016SettingsProtocol_pvs', symObjAddr: 0x504C, symBinAddr: 0x102A8, symSize: 0x28 }
  - { offsetInCU: 0x456A, offset: 0xB583, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV8settings09FBSDKCoreB016SettingsProtocol_pvM', symObjAddr: 0x5074, symBinAddr: 0x102D0, symSize: 0x10 }
  - { offsetInCU: 0x457D, offset: 0xB596, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV8settings09FBSDKCoreB016SettingsProtocol_pvM.resume.0', symObjAddr: 0x5084, symBinAddr: 0x102E0, symSize: 0x4 }
  - { offsetInCU: 0x45D6, offset: 0xB5EF, size: 0x8, addend: 0x0, symName: '_$sSa20_reserveCapacityImpl07minimumB013growForAppendySi_SbtF13FBSDKLoginKit18DeviceLoginManagerC_Tg5', symObjAddr: 0x5198, symBinAddr: 0x103F4, symSize: 0xBC }
  - { offsetInCU: 0x4676, offset: 0xB68F, size: 0x8, addend: 0x0, symName: '_$sSa034_makeUniqueAndReserveCapacityIfNotB0yyF13FBSDKLoginKit18DeviceLoginManagerC_Tg5', symObjAddr: 0x5254, symBinAddr: 0x104B0, symSize: 0x90 }
  - { offsetInCU: 0x4706, offset: 0xB71F, size: 0x8, addend: 0x0, symName: '_$sSS8_copyingyS2SFZ', symObjAddr: 0x56DC, symBinAddr: 0x108F4, symSize: 0x8C }
  - { offsetInCU: 0x471A, offset: 0xB733, size: 0x8, addend: 0x0, symName: '_$sSlsEy11SubSequenceQzqd__cSXRd__5BoundQyd__5IndexRtzluigSS_s16PartialRangeFromVySSAEVGTgq5', symObjAddr: 0x5768, symBinAddr: 0x10980, symSize: 0x4C }
  - { offsetInCU: 0x4748, offset: 0xB761, size: 0x8, addend: 0x0, symName: '_$sSS8_copyingySSSsFZ', symObjAddr: 0x57B4, symBinAddr: 0x109CC, symSize: 0x164 }
  - { offsetInCU: 0x479F, offset: 0xB7B8, size: 0x8, addend: 0x0, symName: '_$sSlsE5countSivgSs8UTF8ViewV_Tgq5', symObjAddr: 0x5918, symBinAddr: 0x10B30, symSize: 0xF0 }
  - { offsetInCU: 0x47C4, offset: 0xB7DD, size: 0x8, addend: 0x0, symName: '_$sSTsE21_copySequenceContents12initializing8IteratorQz_SitSry7ElementQzG_tFSs8UTF8ViewV_Tgq5', symObjAddr: 0x5A6C, symBinAddr: 0x10C84, symSize: 0x214 }
  - { offsetInCU: 0x47F6, offset: 0xB80F, size: 0x8, addend: 0x0, symName: '_$ss11_StringGutsV27_slowEnsureMatchingEncodingySS5IndexVAEF', symObjAddr: 0x5C80, symBinAddr: 0x10E98, symSize: 0x78 }
  - { offsetInCU: 0x480A, offset: 0xB823, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV20_consumeAndCreateNewAByxGyF13FBSDKLoginKit18DeviceLoginManagerC_Tg5', symObjAddr: 0x5CF8, symBinAddr: 0x10F10, symSize: 0x68 }
  - { offsetInCU: 0x48AB, offset: 0xB8C4, size: 0x8, addend: 0x0, symName: '_$ss20_ArrayBufferProtocolPsE15replaceSubrange_4with10elementsOfySnySiG_Siqd__ntSlRd__7ElementQyd__AGRtzlFs01_aB0Vy13FBSDKLoginKit18DeviceLoginManagerCG_s15EmptyCollectionVyANGTg5Tf4nndn_n', symObjAddr: 0x5FF4, symBinAddr: 0x11178, symSize: 0x1FC }
  - { offsetInCU: 0x4B46, offset: 0xBB5F, size: 0x8, addend: 0x0, symName: '_$sSa15replaceSubrange_4withySnySiG_qd__nt7ElementQyd__RszSlRd__lF13FBSDKLoginKit18DeviceLoginManagerC_s15EmptyCollectionVyAHGTg5Tf4ndn_n', symObjAddr: 0x61F0, symBinAddr: 0x11374, symSize: 0x100 }
  - { offsetInCU: 0x4C4B, offset: 0xBC64, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC10netService_13didNotPublishySo05NSNetG0C_SDySSSo8NSNumberCGtFTf4ndn_n', symObjAddr: 0x6A48, symBinAddr: 0x11BCC, symSize: 0x14C }
  - { offsetInCU: 0x27, offset: 0xBDF6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultC5token11isCancelledACSo16FBSDKAccessTokenCSg_SbtcfC', symObjAddr: 0x0, symBinAddr: 0x124FC, symSize: 0xC8 }
  - { offsetInCU: 0x9B, offset: 0xBE6A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultC11accessTokenSo011FBSDKAccessH0CSgvgTo', symObjAddr: 0xC8, symBinAddr: 0x125C4, symSize: 0x48 }
  - { offsetInCU: 0xEF, offset: 0xBEBE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultC11accessTokenSo011FBSDKAccessH0CSgvsTo', symObjAddr: 0x160, symBinAddr: 0x1265C, symSize: 0x64 }
  - { offsetInCU: 0x145, offset: 0xBF14, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultC11isCancelledSbvgTo', symObjAddr: 0x1C4, symBinAddr: 0x126C0, symSize: 0x44 }
  - { offsetInCU: 0x199, offset: 0xBF68, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultC11isCancelledSbvsTo', symObjAddr: 0x24C, symBinAddr: 0x12748, symSize: 0x48 }
  - { offsetInCU: 0x1D3, offset: 0xBFA2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultC5token11isCancelledACSo16FBSDKAccessTokenCSg_SbtcfcTo', symObjAddr: 0x35C, symBinAddr: 0x12858, symSize: 0xCC }
  - { offsetInCU: 0x239, offset: 0xC008, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultCACycfcTo', symObjAddr: 0x474, symBinAddr: 0x12970, symSize: 0x2C }
  - { offsetInCU: 0x2C6, offset: 0xC095, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultCfETo', symObjAddr: 0x4D4, symBinAddr: 0x129D0, symSize: 0x10 }
  - { offsetInCU: 0x2F3, offset: 0xC0C2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultCMa', symObjAddr: 0x4E4, symBinAddr: 0x129E0, symSize: 0x20 }
  - { offsetInCU: 0x405, offset: 0xC1D4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultC5token11isCancelledACSo16FBSDKAccessTokenCSg_SbtcfC', symObjAddr: 0x0, symBinAddr: 0x124FC, symSize: 0xC8 }
  - { offsetInCU: 0x44A, offset: 0xC219, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultC11accessTokenSo011FBSDKAccessH0CSgvg', symObjAddr: 0x110, symBinAddr: 0x1260C, symSize: 0x50 }
  - { offsetInCU: 0x49C, offset: 0xC26B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultC11isCancelledSbvg', symObjAddr: 0x208, symBinAddr: 0x12704, symSize: 0x44 }
  - { offsetInCU: 0x4D6, offset: 0xC2A5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultC5token11isCancelledACSo16FBSDKAccessTokenCSg_Sbtcfc', symObjAddr: 0x294, symBinAddr: 0x12790, symSize: 0xC8 }
  - { offsetInCU: 0x502, offset: 0xC2D1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultCACycfC', symObjAddr: 0x428, symBinAddr: 0x12924, symSize: 0x20 }
  - { offsetInCU: 0x515, offset: 0xC2E4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultCACycfc', symObjAddr: 0x448, symBinAddr: 0x12944, symSize: 0x2C }
  - { offsetInCU: 0x569, offset: 0xC338, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultCfD', symObjAddr: 0x4A0, symBinAddr: 0x1299C, symSize: 0x34 }
  - { offsetInCU: 0x27, offset: 0xC390, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12DevicePollerVACycfC', symObjAddr: 0x0, symBinAddr: 0x12A14, symSize: 0x4 }
  - { offsetInCU: 0x75, offset: 0xC3DE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12DevicePollerVAA0C7PollingA2aDP8schedule8interval5blockySu_yyctFTW', symObjAddr: 0x8, symBinAddr: 0x12A1C, symSize: 0x4 }
  - { offsetInCU: 0x94, offset: 0xC3FD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12DevicePollerVAA0C7PollingA2aDP8schedule8interval5blockySu_yyctFTW', symObjAddr: 0x8, symBinAddr: 0x12A1C, symSize: 0x4 }
  - { offsetInCU: 0xA5, offset: 0xC40E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12DevicePollerV8schedule8interval5blockySu_yyctFTf4nnd_n', symObjAddr: 0xC, symBinAddr: 0x12A20, symSize: 0x270 }
  - { offsetInCU: 0x140, offset: 0xC4A9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12DevicePollerVMa', symObjAddr: 0x27C, symBinAddr: 0x12C90, symSize: 0x10 }
  - { offsetInCU: 0x153, offset: 0xC4BC, size: 0x8, addend: 0x0, symName: '_$sSo17OS_dispatch_queueCMa', symObjAddr: 0x28C, symBinAddr: 0x12CA0, symSize: 0x3C }
  - { offsetInCU: 0x166, offset: 0xC4CF, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x2C8, symBinAddr: 0x12CDC, symSize: 0x10 }
  - { offsetInCU: 0x179, offset: 0xC4E2, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x2D8, symBinAddr: 0x12CEC, symSize: 0x8 }
  - { offsetInCU: 0x18C, offset: 0xC4F5, size: 0x8, addend: 0x0, symName: '_$s8Dispatch0A13WorkItemFlagsVACs10SetAlgebraAAWl', symObjAddr: 0x2E0, symBinAddr: 0x12CF4, symSize: 0x48 }
  - { offsetInCU: 0x19F, offset: 0xC508, size: 0x8, addend: 0x0, symName: '_$sSay8Dispatch0A13WorkItemFlagsVGSayxGSTsWl', symObjAddr: 0x368, symBinAddr: 0x12D3C, symSize: 0x4C }
  - { offsetInCU: 0x25B, offset: 0xC5C4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12DevicePollerVACycfC', symObjAddr: 0x0, symBinAddr: 0x12A14, symSize: 0x4 }
  - { offsetInCU: 0x27F, offset: 0xC5E8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12DevicePollerV8schedule8interval5blockySu_yyctF', symObjAddr: 0x4, symBinAddr: 0x12A18, symSize: 0x4 }
  - { offsetInCU: 0x2B, offset: 0xC67C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperO03getC4InfoSSyFZ', symObjAddr: 0x0, symBinAddr: 0x12D90, symSize: 0x4 }
  - { offsetInCU: 0x4D, offset: 0xC69E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperO25mdnsAdvertisementServicesSo10NSMapTableCySo20NSNetServiceDelegate_pyXlGvpZ', symObjAddr: 0x4F00, symBinAddr: 0x61168, symSize: 0x0 }
  - { offsetInCU: 0x67, offset: 0xC6B8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperO16NetServiceValues33_D51BDA1F8F5A5E4604761D8AFA9F40BBLLO10sdkVersionSSvpZ', symObjAddr: 0xA80, symBinAddr: 0x5ED88, symSize: 0x0 }
  - { offsetInCU: 0x121, offset: 0xC772, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperO16NetServiceValues33_D51BDA1F8F5A5E4604761D8AFA9F40BBLLO10sdkVersion_WZ', symObjAddr: 0x180, symBinAddr: 0x12F10, symSize: 0x144 }
  - { offsetInCU: 0x16D, offset: 0xC7BE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperO27cleanUpAdvertisementService3forySo05NSNetI8Delegate_p_tFZTf4en_n', symObjAddr: 0x304, symBinAddr: 0x13094, symSize: 0xF4 }
  - { offsetInCU: 0x1E8, offset: 0xC839, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperO25startAdvertisementService9loginCode8delegateSbSS_So05NSNetH8Delegate_ptFZTf4nen_n', symObjAddr: 0x3F8, symBinAddr: 0x13188, symSize: 0x240 }
  - { offsetInCU: 0x377, offset: 0xC9C8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperO10isDelegate_23forAdvertisementServiceSbSo05NSNetjG0_p_So0kJ0CtFZTf4enn_n', symObjAddr: 0x638, symBinAddr: 0x133C8, symSize: 0xC8 }
  - { offsetInCU: 0x3F4, offset: 0xCA45, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperO03getC4InfoSSyFZTf4d_n', symObjAddr: 0x700, symBinAddr: 0x13490, symSize: 0x2A0 }
  - { offsetInCU: 0x61F, offset: 0xCC70, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperO25mdnsAdvertisementServices_WZ', symObjAddr: 0xA0, symBinAddr: 0x12E30, symSize: 0x38 }
  - { offsetInCU: 0x639, offset: 0xCC8A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperO25mdnsAdvertisementServicesSo10NSMapTableCySo20NSNetServiceDelegate_pyXlGvau', symObjAddr: 0xD8, symBinAddr: 0x12E68, symSize: 0x40 }
  - { offsetInCU: 0x6E9, offset: 0xCD3A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperOMa', symObjAddr: 0x9A0, symBinAddr: 0x13730, symSize: 0x10 }
  - { offsetInCU: 0x6FC, offset: 0xCD4D, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataVACSTAAWl', symObjAddr: 0x9F4, symBinAddr: 0x13740, symSize: 0x44 }
  - { offsetInCU: 0x89E, offset: 0xCEEF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperO03getC4InfoSSyFZ', symObjAddr: 0x0, symBinAddr: 0x12D90, symSize: 0x4 }
  - { offsetInCU: 0x8B1, offset: 0xCF02, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperO25startAdvertisementService9loginCode8delegateSbSS_So05NSNetH8Delegate_ptFZ', symObjAddr: 0x4, symBinAddr: 0x12D94, symSize: 0x44 }
  - { offsetInCU: 0x8CA, offset: 0xCF1B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperO27cleanUpAdvertisementService3forySo05NSNetI8Delegate_p_tFZ', symObjAddr: 0x48, symBinAddr: 0x12DD8, symSize: 0x28 }
  - { offsetInCU: 0x8DD, offset: 0xCF2E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperO10isDelegate_23forAdvertisementServiceSbSo05NSNetjG0_p_So0kJ0CtFZ', symObjAddr: 0x70, symBinAddr: 0x12E00, symSize: 0x30 }
  - { offsetInCU: 0x8F6, offset: 0xCF47, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperO25mdnsAdvertisementServicesSo10NSMapTableCySo20NSNetServiceDelegate_pyXlGvgZ', symObjAddr: 0x118, symBinAddr: 0x12EA8, symSize: 0x68 }
  - { offsetInCU: 0x926, offset: 0xCF77, size: 0x8, addend: 0x0, symName: '_$sSlsE5first7ElementQzSgvgSS_Tg5', symObjAddr: 0x2C4, symBinAddr: 0x13054, symSize: 0x40 }
  - { offsetInCU: 0x62, offset: 0xD0D9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15defaultAudienceAA07DefaultF0OvgTo', symObjAddr: 0x0, symBinAddr: 0x13784, symSize: 0x9C }
  - { offsetInCU: 0xAF, offset: 0xD126, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15defaultAudienceAA07DefaultF0OvsTo', symObjAddr: 0x13C, symBinAddr: 0x1389C, symSize: 0xA8 }
  - { offsetInCU: 0x127, offset: 0xD19E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC8delegateAA05LoginD8Delegate_pSgvgTo', symObjAddr: 0x4B0, symBinAddr: 0x13C10, symSize: 0x48 }
  - { offsetInCU: 0x17B, offset: 0xD1F2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC8delegateAA05LoginD8Delegate_pSgvsTo', symObjAddr: 0x540, symBinAddr: 0x13CA0, symSize: 0x50 }
  - { offsetInCU: 0x1FB, offset: 0xD272, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC11permissionsSaySSGvgTo', symObjAddr: 0x6C4, symBinAddr: 0x13E24, symSize: 0x68 }
  - { offsetInCU: 0x24F, offset: 0xD2C6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC11permissionsSaySSGvsTo', symObjAddr: 0x740, symBinAddr: 0x13EA0, symSize: 0x64 }
  - { offsetInCU: 0x305, offset: 0xD37C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15TooltipBehaviorOSYAASY8rawValuexSg03RawH0Qz_tcfCTW', symObjAddr: 0x1C3C, symBinAddr: 0x15318, symSize: 0x20 }
  - { offsetInCU: 0x334, offset: 0xD3AB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15TooltipBehaviorOSYAASY8rawValue03RawH0QzvgTW', symObjAddr: 0x1C5C, symBinAddr: 0x15338, symSize: 0xC }
  - { offsetInCU: 0x351, offset: 0xD3C8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15tooltipBehaviorAC07TooltipF0OvgTo', symObjAddr: 0x800, symBinAddr: 0x13F60, symSize: 0x44 }
  - { offsetInCU: 0x3A6, offset: 0xD41D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15tooltipBehaviorAC07TooltipF0OvsTo', symObjAddr: 0x888, symBinAddr: 0x13FE8, symSize: 0x48 }
  - { offsetInCU: 0x411, offset: 0xD488, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC17tooltipColorStyleAA13FBTooltipViewC0fG0OvgTo', symObjAddr: 0x95C, symBinAddr: 0x140BC, symSize: 0x44 }
  - { offsetInCU: 0x465, offset: 0xD4DC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC17tooltipColorStyleAA13FBTooltipViewC0fG0OvsTo', symObjAddr: 0x9E4, symBinAddr: 0x14144, symSize: 0x48 }
  - { offsetInCU: 0x4D0, offset: 0xD547, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC13loginTrackingAA05LoginF0OvgTo', symObjAddr: 0xAB8, symBinAddr: 0x14218, symSize: 0x44 }
  - { offsetInCU: 0x524, offset: 0xD59B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC13loginTrackingAA05LoginF0OvsTo', symObjAddr: 0xB40, symBinAddr: 0x142A0, symSize: 0x48 }
  - { offsetInCU: 0x58F, offset: 0xD606, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5nonceSSSgvgTo', symObjAddr: 0xC14, symBinAddr: 0x14374, symSize: 0x5C }
  - { offsetInCU: 0x5BF, offset: 0xD636, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5nonceSSSgvsTo', symObjAddr: 0xCA8, symBinAddr: 0x14408, symSize: 0x64 }
  - { offsetInCU: 0x648, offset: 0xD6BF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC8authTypeSo0a4AuthF0aSgvgTo', symObjAddr: 0x10A8, symBinAddr: 0x14784, symSize: 0x48 }
  - { offsetInCU: 0x69C, offset: 0xD713, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC8authTypeSo0a4AuthF0aSgvsTo', symObjAddr: 0x1140, symBinAddr: 0x1481C, symSize: 0x64 }
  - { offsetInCU: 0x707, offset: 0xD77E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC12codeVerifierAA04CodeF0CvgTo', symObjAddr: 0x1238, symBinAddr: 0x14914, symSize: 0x48 }
  - { offsetInCU: 0x75C, offset: 0xD7D3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC12codeVerifierAA04CodeF0CvsTo', symObjAddr: 0x12C4, symBinAddr: 0x149A0, symSize: 0x64 }
  - { offsetInCU: 0x830, offset: 0xD8A7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC19graphRequestFactorySo010FBSDKGraphfG0_pvgTo', symObjAddr: 0x18A4, symBinAddr: 0x14F80, symSize: 0x48 }
  - { offsetInCU: 0x884, offset: 0xD8FB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC19graphRequestFactorySo010FBSDKGraphfG0_pvsTo', symObjAddr: 0x1948, symBinAddr: 0x15024, symSize: 0x64 }
  - { offsetInCU: 0x8EF, offset: 0xD966, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15isAuthenticatedSbvgTo', symObjAddr: 0x1A5C, symBinAddr: 0x15138, symSize: 0xA8 }
  - { offsetInCU: 0x9AF, offset: 0xDA26, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5frameACSo6CGRectV_tcfcTo', symObjAddr: 0x23A0, symBinAddr: 0x15A7C, symSize: 0x20 }
  - { offsetInCU: 0x9FC, offset: 0xDA73, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5coderACSgSo7NSCoderC_tcfcTo', symObjAddr: 0x2434, symBinAddr: 0x15B10, symSize: 0x3C }
  - { offsetInCU: 0xAF6, offset: 0xDB6D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15didMoveToWindowyyFTo', symObjAddr: 0x2B90, symBinAddr: 0x1626C, symSize: 0x28 }
  - { offsetInCU: 0xB2B, offset: 0xDBA2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC9imageRect010forContentF0So6CGRectVAG_tFTo', symObjAddr: 0x2BE0, symBinAddr: 0x162BC, symSize: 0x28 }
  - { offsetInCU: 0xB93, offset: 0xDC0A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC9titleRect010forContentF0So6CGRectVAG_tFTo', symObjAddr: 0x2CF8, symBinAddr: 0x163D4, symSize: 0x110 }
  - { offsetInCU: 0xC97, offset: 0xDD0E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC14layoutSubviewsyyFTo', symObjAddr: 0x31EC, symBinAddr: 0x168C8, symSize: 0x28 }
  - { offsetInCU: 0xCCC, offset: 0xDD43, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC12sizeThatFitsySo6CGSizeVAFFTo', symObjAddr: 0x3708, symBinAddr: 0x16DE4, symSize: 0x54 }
  - { offsetInCU: 0xD1A, offset: 0xDD91, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC20accessTokenDidChangeyy10Foundation12NotificationVFTo', symObjAddr: 0x3A2C, symBinAddr: 0x17108, symSize: 0x94 }
  - { offsetInCU: 0xD51, offset: 0xDDC8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC16profileDidChangeyy10Foundation12NotificationVFTo', symObjAddr: 0x3D08, symBinAddr: 0x173E4, symSize: 0x90 }
  - { offsetInCU: 0xDB6, offset: 0xDE2D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC13buttonPressedyyypFTo', symObjAddr: 0x47B0, symBinAddr: 0x17E8C, symSize: 0x64 }
  - { offsetInCU: 0xDE8, offset: 0xDE5F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC22makeLoginConfigurationAA0fG0CSgyFTo', symObjAddr: 0x4AE8, symBinAddr: 0x181C4, symSize: 0x34 }
  - { offsetInCU: 0xE03, offset: 0xDE7A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC9logInUser33_2D5546723C2E9E390359F57C16888789LLyyFyAA11LoginResultOcfU_', symObjAddr: 0x4B1C, symBinAddr: 0x181F8, symSize: 0x104 }
  - { offsetInCU: 0xE58, offset: 0xDECF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC17initializeContentyyFTo', symObjAddr: 0x4E04, symBinAddr: 0x184E0, symSize: 0x28 }
  - { offsetInCU: 0xE8A, offset: 0xDF01, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC27updateContentForAccessTokenyyFTo', symObjAddr: 0x5044, symBinAddr: 0x18720, symSize: 0x28 }
  - { offsetInCU: 0xEA5, offset: 0xDF1C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC18fetchAndSetContentyyFTo', symObjAddr: 0x52D4, symBinAddr: 0x189B0, symSize: 0x28 }
  - { offsetInCU: 0xEDC, offset: 0xDF53, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC20updateContentForUseryy09FBSDKCoreB07ProfileCSgFTo', symObjAddr: 0x543C, symBinAddr: 0x18B18, symSize: 0x54 }
  - { offsetInCU: 0xEF7, offset: 0xDF6E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC6logoutyyFTo', symObjAddr: 0x5490, symBinAddr: 0x18B6C, symSize: 0x28 }
  - { offsetInCU: 0xF42, offset: 0xDFB9, size: 0x8, addend: 0x0, symName: ___swift_mutable_project_boxed_opaque_existential_1, symObjAddr: 0x260, symBinAddr: 0x139C0, symSize: 0x28 }
  - { offsetInCU: 0xF55, offset: 0xDFCC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15defaultAudienceAA07DefaultF0OvpACTK', symObjAddr: 0x288, symBinAddr: 0x139E8, symSize: 0x84 }
  - { offsetInCU: 0xF8B, offset: 0xE002, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15defaultAudienceAA07DefaultF0OvpACTk', symObjAddr: 0x30C, symBinAddr: 0x13A6C, symSize: 0x80 }
  - { offsetInCU: 0x108F, offset: 0xE106, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit29UserInterfaceElementProviding_pWOb', symObjAddr: 0x1658, symBinAddr: 0x14D34, symSize: 0x18 }
  - { offsetInCU: 0x10A2, offset: 0xE119, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28UserInterfaceStringProviding_pWOb', symObjAddr: 0x1788, symBinAddr: 0x14E64, symSize: 0x18 }
  - { offsetInCU: 0x10B5, offset: 0xE12C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14LoginProviding_pWOb', symObjAddr: 0x1848, symBinAddr: 0x14F24, symSize: 0x18 }
  - { offsetInCU: 0x110C, offset: 0xE183, size: 0x8, addend: 0x0, symName: '_$sSo20FBSDKInternalUtilityCMa', symObjAddr: 0x1F28, symBinAddr: 0x15604, symSize: 0x3C }
  - { offsetInCU: 0x1456, offset: 0xE4CD, size: 0x8, addend: 0x0, symName: '_$sSo13UIAlertActionCIegg_ABIeyBy_TR', symObjAddr: 0x4C20, symBinAddr: 0x182FC, symSize: 0x4C }
  - { offsetInCU: 0x14DB, offset: 0xE552, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonCfETo', symObjAddr: 0x54EC, symBinAddr: 0x18BC8, symSize: 0xE8 }
  - { offsetInCU: 0x1699, offset: 0xE710, size: 0x8, addend: 0x0, symName: '_$sypSgWOb', symObjAddr: 0x5B14, symBinAddr: 0x191BC, symSize: 0x48 }
  - { offsetInCU: 0x16AC, offset: 0xE723, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC9logInUser33_2D5546723C2E9E390359F57C16888789LLyyFyAA11LoginResultOcfU_TA', symObjAddr: 0x5BFC, symBinAddr: 0x19228, symSize: 0x8 }
  - { offsetInCU: 0x170F, offset: 0xE786, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC18fetchAndSetContentyyFySo27FBSDKGraphRequestConnecting_pSg_ypSgs5Error_pSgtcfU_TA', symObjAddr: 0x5D84, symBinAddr: 0x193B0, symSize: 0x8 }
  - { offsetInCU: 0x1722, offset: 0xE799, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x5D8C, symBinAddr: 0x193B8, symSize: 0x10 }
  - { offsetInCU: 0x1735, offset: 0xE7AC, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x5D9C, symBinAddr: 0x193C8, symSize: 0x8 }
  - { offsetInCU: 0x1748, offset: 0xE7BF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15TooltipBehaviorOSHAASQWb', symObjAddr: 0x5DA4, symBinAddr: 0x193D0, symSize: 0x4 }
  - { offsetInCU: 0x175B, offset: 0xE7D2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15TooltipBehaviorOAESQAAWl', symObjAddr: 0x5DA8, symBinAddr: 0x193D4, symSize: 0x44 }
  - { offsetInCU: 0x176E, offset: 0xE7E5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonCMa', symObjAddr: 0x5EF4, symBinAddr: 0x19520, symSize: 0x20 }
  - { offsetInCU: 0x1781, offset: 0xE7F8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15TooltipBehaviorOMa', symObjAddr: 0x5F14, symBinAddr: 0x19540, symSize: 0x10 }
  - { offsetInCU: 0x1794, offset: 0xE80B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19LoginButtonDelegate_pSgXwWOh', symObjAddr: 0x5F44, symBinAddr: 0x19570, symSize: 0x24 }
  - { offsetInCU: 0x17A7, offset: 0xE81E, size: 0x8, addend: 0x0, symName: '_$sypSgWOc', symObjAddr: 0x5F68, symBinAddr: 0x19594, symSize: 0x48 }
  - { offsetInCU: 0x17BA, offset: 0xE831, size: 0x8, addend: 0x0, symName: '_$s12FBSDKCoreKit10PermissionOACSHAAWl', symObjAddr: 0x5FB0, symBinAddr: 0x195DC, symSize: 0x48 }
  - { offsetInCU: 0x17CD, offset: 0xE844, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC26presentAlertViewController33_2D5546723C2E9E390359F57C16888789LLyyFySo13UIAlertActionCcfU_TA', symObjAddr: 0x5FF8, symBinAddr: 0x19624, symSize: 0x8 }
  - { offsetInCU: 0x17E0, offset: 0xE857, size: 0x8, addend: 0x0, symName: '_$sS2Ss7CVarArg10FoundationWl', symObjAddr: 0x6000, symBinAddr: 0x1962C, symSize: 0x44 }
  - { offsetInCU: 0x17F3, offset: 0xE86A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultOWOe', symObjAddr: 0x6044, symBinAddr: 0x19670, symSize: 0x48 }
  - { offsetInCU: 0x18F5, offset: 0xE96C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15TooltipBehaviorOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x1B7C, symBinAddr: 0x15258, symSize: 0x14 }
  - { offsetInCU: 0x1997, offset: 0xEA0E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15TooltipBehaviorOSHAASH9hashValueSivgTW', symObjAddr: 0x1B90, symBinAddr: 0x1526C, symSize: 0x44 }
  - { offsetInCU: 0x1A3E, offset: 0xEAB5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15TooltipBehaviorOSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0x1BD4, symBinAddr: 0x152B0, symSize: 0x28 }
  - { offsetInCU: 0x1A8D, offset: 0xEB04, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15TooltipBehaviorOSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0x1BFC, symBinAddr: 0x152D8, symSize: 0x40 }
  - { offsetInCU: 0x1D3D, offset: 0xEDB4, size: 0x8, addend: 0x0, symName: '_$sShyShyxGqd__nc7ElementQyd__RszSTRd__lufC12FBSDKCoreKit10PermissionO_SayAFGTgm5Tf4g_n', symObjAddr: 0x5C04, symBinAddr: 0x19230, symSize: 0xEC }
  - { offsetInCU: 0x1E6F, offset: 0xEEE6, size: 0x8, addend: 0x0, symName: '_$sShyShyxGqd__nc7ElementQyd__RszSTRd__lufCSS_SaySSGTgm5Tf4g_n', symObjAddr: 0x5CF0, symBinAddr: 0x1931C, symSize: 0x94 }
  - { offsetInCU: 0x21E4, offset: 0xF25B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15defaultAudienceAA07DefaultF0Ovg', symObjAddr: 0x9C, symBinAddr: 0x13820, symSize: 0x7C }
  - { offsetInCU: 0x2229, offset: 0xF2A0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15defaultAudienceAA07DefaultF0Ovs', symObjAddr: 0x1E4, symBinAddr: 0x13944, symSize: 0x7C }
  - { offsetInCU: 0x224F, offset: 0xF2C6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15defaultAudienceAA07DefaultF0OvM', symObjAddr: 0x38C, symBinAddr: 0x13AEC, symSize: 0xA0 }
  - { offsetInCU: 0x2287, offset: 0xF2FE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15defaultAudienceAA07DefaultF0OvM.resume.0', symObjAddr: 0x42C, symBinAddr: 0x13B8C, symSize: 0x84 }
  - { offsetInCU: 0x22C3, offset: 0xF33A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC8delegateAA05LoginD8Delegate_pSgvg', symObjAddr: 0x4F8, symBinAddr: 0x13C58, symSize: 0x48 }
  - { offsetInCU: 0x2302, offset: 0xF379, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC8delegateAA05LoginD8Delegate_pSgvs', symObjAddr: 0x590, symBinAddr: 0x13CF0, symSize: 0x58 }
  - { offsetInCU: 0x2328, offset: 0xF39F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC8delegateAA05LoginD8Delegate_pSgvM', symObjAddr: 0x5E8, symBinAddr: 0x13D48, symSize: 0x70 }
  - { offsetInCU: 0x234B, offset: 0xF3C2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC8delegateAA05LoginD8Delegate_pSgvM.resume.0', symObjAddr: 0x658, symBinAddr: 0x13DB8, symSize: 0x6C }
  - { offsetInCU: 0x239E, offset: 0xF415, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC11permissionsSaySSGvM', symObjAddr: 0x7B8, symBinAddr: 0x13F18, symSize: 0x44 }
  - { offsetInCU: 0x23C1, offset: 0xF438, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC11permissionsSaySSGvM.resume.0', symObjAddr: 0x7FC, symBinAddr: 0x13F5C, symSize: 0x4 }
  - { offsetInCU: 0x23F2, offset: 0xF469, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15tooltipBehaviorAC07TooltipF0Ovg', symObjAddr: 0x844, symBinAddr: 0x13FA4, symSize: 0x44 }
  - { offsetInCU: 0x2431, offset: 0xF4A8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15tooltipBehaviorAC07TooltipF0Ovs', symObjAddr: 0x8D0, symBinAddr: 0x14030, symSize: 0x48 }
  - { offsetInCU: 0x2457, offset: 0xF4CE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15tooltipBehaviorAC07TooltipF0OvM', symObjAddr: 0x918, symBinAddr: 0x14078, symSize: 0x44 }
  - { offsetInCU: 0x248C, offset: 0xF503, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC17tooltipColorStyleAA13FBTooltipViewC0fG0Ovg', symObjAddr: 0x9A0, symBinAddr: 0x14100, symSize: 0x44 }
  - { offsetInCU: 0x24CB, offset: 0xF542, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC17tooltipColorStyleAA13FBTooltipViewC0fG0Ovs', symObjAddr: 0xA2C, symBinAddr: 0x1418C, symSize: 0x48 }
  - { offsetInCU: 0x24F1, offset: 0xF568, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC17tooltipColorStyleAA13FBTooltipViewC0fG0OvM', symObjAddr: 0xA74, symBinAddr: 0x141D4, symSize: 0x44 }
  - { offsetInCU: 0x2526, offset: 0xF59D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC13loginTrackingAA05LoginF0Ovg', symObjAddr: 0xAFC, symBinAddr: 0x1425C, symSize: 0x44 }
  - { offsetInCU: 0x2565, offset: 0xF5DC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC13loginTrackingAA05LoginF0Ovs', symObjAddr: 0xB88, symBinAddr: 0x142E8, symSize: 0x48 }
  - { offsetInCU: 0x258B, offset: 0xF602, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC13loginTrackingAA05LoginF0OvM', symObjAddr: 0xBD0, symBinAddr: 0x14330, symSize: 0x44 }
  - { offsetInCU: 0x25C0, offset: 0xF637, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5nonceSSSgvg', symObjAddr: 0xC70, symBinAddr: 0x143D0, symSize: 0x38 }
  - { offsetInCU: 0x2630, offset: 0xF6A7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5nonceSSSgvs', symObjAddr: 0xD0C, symBinAddr: 0x1446C, symSize: 0x1FC }
  - { offsetInCU: 0x276A, offset: 0xF7E1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5nonceSSSgvM', symObjAddr: 0xF8C, symBinAddr: 0x14668, symSize: 0x48 }
  - { offsetInCU: 0x27A8, offset: 0xF81F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5nonceSSSgvM.resume.0', symObjAddr: 0xFD4, symBinAddr: 0x146B0, symSize: 0x60 }
  - { offsetInCU: 0x27C7, offset: 0xF83E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15messengerPageIdSSSgvM', symObjAddr: 0x1064, symBinAddr: 0x14740, symSize: 0x44 }
  - { offsetInCU: 0x27FC, offset: 0xF873, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC8authTypeSo0a4AuthF0aSgvg', symObjAddr: 0x10F0, symBinAddr: 0x147CC, symSize: 0x50 }
  - { offsetInCU: 0x283B, offset: 0xF8B2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC8authTypeSo0a4AuthF0aSgvs', symObjAddr: 0x11A4, symBinAddr: 0x14880, symSize: 0x50 }
  - { offsetInCU: 0x2861, offset: 0xF8D8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC8authTypeSo0a4AuthF0aSgvM', symObjAddr: 0x11F4, symBinAddr: 0x148D0, symSize: 0x44 }
  - { offsetInCU: 0x2896, offset: 0xF90D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC12codeVerifierAA04CodeF0Cvg', symObjAddr: 0x1280, symBinAddr: 0x1495C, symSize: 0x44 }
  - { offsetInCU: 0x28D5, offset: 0xF94C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC12codeVerifierAA04CodeF0Cvs', symObjAddr: 0x1328, symBinAddr: 0x14A04, symSize: 0x50 }
  - { offsetInCU: 0x28FB, offset: 0xF972, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC12codeVerifierAA04CodeF0CvM', symObjAddr: 0x1378, symBinAddr: 0x14A54, symSize: 0x44 }
  - { offsetInCU: 0x291E, offset: 0xF995, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC6userIDSSSgvM', symObjAddr: 0x13EC, symBinAddr: 0x14AC8, symSize: 0x44 }
  - { offsetInCU: 0x2941, offset: 0xF9B8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC8userNameSSSgvM', symObjAddr: 0x15F4, symBinAddr: 0x14CD0, symSize: 0x44 }
  - { offsetInCU: 0x2964, offset: 0xF9DB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15elementProviderAA29UserInterfaceElementProviding_pvM', symObjAddr: 0x1670, symBinAddr: 0x14D4C, symSize: 0x44 }
  - { offsetInCU: 0x2987, offset: 0xF9FE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC14stringProviderAA28UserInterfaceStringProviding_pvM', symObjAddr: 0x17A0, symBinAddr: 0x14E7C, symSize: 0x44 }
  - { offsetInCU: 0x29AA, offset: 0xFA21, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC13loginProviderAA14LoginProviding_pvM', symObjAddr: 0x1860, symBinAddr: 0x14F3C, symSize: 0x44 }
  - { offsetInCU: 0x2A01, offset: 0xFA78, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC19graphRequestFactorySo010FBSDKGraphfG0_pvM', symObjAddr: 0x1A18, symBinAddr: 0x150F4, symSize: 0x44 }
  - { offsetInCU: 0x2A36, offset: 0xFAAD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15isAuthenticatedSbvg', symObjAddr: 0x1B04, symBinAddr: 0x151E0, symSize: 0x60 }
  - { offsetInCU: 0x2A56, offset: 0xFACD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15TooltipBehaviorO8rawValueAESgSu_tcfC', symObjAddr: 0x1B64, symBinAddr: 0x15240, symSize: 0x14 }
  - { offsetInCU: 0x2A73, offset: 0xFAEA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15TooltipBehaviorO8rawValueSuvg', symObjAddr: 0x1B78, symBinAddr: 0x15254, symSize: 0x4 }
  - { offsetInCU: 0x2AEB, offset: 0xFB62, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5frameACSo6CGRectV_tcfC', symObjAddr: 0x1C68, symBinAddr: 0x15344, symSize: 0x50 }
  - { offsetInCU: 0x2B1C, offset: 0xFB93, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5frameACSo6CGRectV_tcfc', symObjAddr: 0x1CB8, symBinAddr: 0x15394, symSize: 0x270 }
  - { offsetInCU: 0x2BE1, offset: 0xFC58, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC09configureD033_2D5546723C2E9E390359F57C16888789LLyyF', symObjAddr: 0x1F64, symBinAddr: 0x15640, symSize: 0x43C }
  - { offsetInCU: 0x2C8F, offset: 0xFD06, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5coderACSgSo7NSCoderC_tcfC', symObjAddr: 0x23C0, symBinAddr: 0x15A9C, symSize: 0x44 }
  - { offsetInCU: 0x2CA2, offset: 0xFD19, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5coderACSgSo7NSCoderC_tcfc', symObjAddr: 0x2404, symBinAddr: 0x15AE0, symSize: 0x30 }
  - { offsetInCU: 0x2CBB, offset: 0xFD32, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5frame15elementProvider06stringG005loginG019graphRequestFactoryACSo6CGRectV_AA29UserInterfaceElementProviding_pAA0no6StringQ0_pAA05LoginQ0_pSo010FBSDKGraphkL0_ptcfC', symObjAddr: 0x2470, symBinAddr: 0x15B4C, symSize: 0x16C }
  - { offsetInCU: 0x2DA0, offset: 0xFE17, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5frame11permissionsACSo6CGRectV_Say09FBSDKCoreB010PermissionOGtcfC', symObjAddr: 0x25DC, symBinAddr: 0x15CB8, symSize: 0x21C }
  - { offsetInCU: 0x2FB3, offset: 0x1002A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC17initializeContentyyF', symObjAddr: 0x27F8, symBinAddr: 0x15ED4, symSize: 0x17C }
  - { offsetInCU: 0x3005, offset: 0x1007C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15didMoveToWindowyyF', symObjAddr: 0x2974, symBinAddr: 0x16050, symSize: 0xA8 }
  - { offsetInCU: 0x308F, offset: 0x10106, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC19showTooltipIfNeeded33_2D5546723C2E9E390359F57C16888789LLyyF', symObjAddr: 0x2A1C, symBinAddr: 0x160F8, symSize: 0x174 }
  - { offsetInCU: 0x31E4, offset: 0x1025B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC9imageRect010forContentF0So6CGRectVAG_tF', symObjAddr: 0x2BB8, symBinAddr: 0x16294, symSize: 0x28 }
  - { offsetInCU: 0x3255, offset: 0x102CC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC9titleRect010forContentF0So6CGRectVAG_tF', symObjAddr: 0x2C08, symBinAddr: 0x162E4, symSize: 0xF0 }
  - { offsetInCU: 0x3399, offset: 0x10410, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC14layoutSubviewsyyF', symObjAddr: 0x2E08, symBinAddr: 0x164E4, symSize: 0x3E4 }
  - { offsetInCU: 0x3477, offset: 0x104EE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC12sizeThatFitsySo6CGSizeVAFF', symObjAddr: 0x3214, symBinAddr: 0x168F0, symSize: 0x4F4 }
  - { offsetInCU: 0x3592, offset: 0x10609, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC20accessTokenDidChangeyy10Foundation12NotificationVF', symObjAddr: 0x375C, symBinAddr: 0x16E38, symSize: 0x18C }
  - { offsetInCU: 0x365F, offset: 0x106D6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC27updateContentForAccessTokenyyF', symObjAddr: 0x38E8, symBinAddr: 0x16FC4, symSize: 0x144 }
  - { offsetInCU: 0x36C1, offset: 0x10738, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC16profileDidChangeyy10Foundation12NotificationVF', symObjAddr: 0x3AC0, symBinAddr: 0x1719C, symSize: 0x140 }
  - { offsetInCU: 0x3719, offset: 0x10790, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC20updateContentForUseryy09FBSDKCoreB07ProfileCSgF', symObjAddr: 0x3C00, symBinAddr: 0x172DC, symSize: 0x108 }
  - { offsetInCU: 0x3781, offset: 0x107F8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC13buttonPressedyyypF', symObjAddr: 0x3D98, symBinAddr: 0x17474, symSize: 0x254 }
  - { offsetInCU: 0x382E, offset: 0x108A5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC26presentAlertViewController33_2D5546723C2E9E390359F57C16888789LLyyF', symObjAddr: 0x3FEC, symBinAddr: 0x176C8, symSize: 0x7C4 }
  - { offsetInCU: 0x39A6, offset: 0x10A1D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC26presentAlertViewController33_2D5546723C2E9E390359F57C16888789LLyyFySo13UIAlertActionCcfU_', symObjAddr: 0x4C6C, symBinAddr: 0x18348, symSize: 0xCC }
  - { offsetInCU: 0x3A4B, offset: 0x10AC2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC22makeLoginConfigurationAA0fG0CSgyF', symObjAddr: 0x4814, symBinAddr: 0x17EF0, symSize: 0x2D4 }
  - { offsetInCU: 0x3CA2, offset: 0x10D19, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC6logoutyyF', symObjAddr: 0x4D38, symBinAddr: 0x18414, symSize: 0xCC }
  - { offsetInCU: 0x3CE9, offset: 0x10D60, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC18fetchAndSetContentyyF', symObjAddr: 0x4E2C, symBinAddr: 0x18508, symSize: 0x218 }
  - { offsetInCU: 0x3DC8, offset: 0x10E3F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC18fetchAndSetContentyyFySo27FBSDKGraphRequestConnecting_pSg_ypSgs5Error_pSgtcfU_', symObjAddr: 0x506C, symBinAddr: 0x18748, symSize: 0x268 }
  - { offsetInCU: 0x3EE1, offset: 0x10F58, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC27userInformationDoesNotMatch33_2D5546723C2E9E390359F57C16888789LLySb09FBSDKCoreB07ProfileCF', symObjAddr: 0x52FC, symBinAddr: 0x189D8, symSize: 0x140 }
  - { offsetInCU: 0x3F6B, offset: 0x10FE2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonCfD', symObjAddr: 0x54B8, symBinAddr: 0x18B94, symSize: 0x34 }
  - { offsetInCU: 0x3F8C, offset: 0x11003, size: 0x8, addend: 0x0, symName: '_$ss15ContiguousArrayV16_createNewBuffer14bufferIsUnique15minimumCapacity13growForAppendySb_SiSbtFSS_Tg5', symObjAddr: 0x55D4, symBinAddr: 0x18CB0, symSize: 0x1C }
  - { offsetInCU: 0x3FA0, offset: 0x11017, size: 0x8, addend: 0x0, symName: '_$ss15ContiguousArrayV16_createNewBuffer14bufferIsUnique15minimumCapacity13growForAppendySb_SiSbtF12FBSDKCoreKit10PermissionO_Tg5', symObjAddr: 0x55F0, symBinAddr: 0x18CCC, symSize: 0x1C }
  - { offsetInCU: 0x3FDE, offset: 0x11055, size: 0x8, addend: 0x0, symName: '_$ss22_ContiguousArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtFSS_Tg5', symObjAddr: 0x560C, symBinAddr: 0x18CE8, symSize: 0x104 }
  - { offsetInCU: 0x40D7, offset: 0x1114E, size: 0x8, addend: 0x0, symName: '_$ss22_ContiguousArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtF12FBSDKCoreKit10PermissionO_Tg5', symObjAddr: 0x5710, symBinAddr: 0x18DEC, symSize: 0x174 }
  - { offsetInCU: 0x41B6, offset: 0x1122D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5coderACSgSo7NSCoderC_tcfcTf4gn_n', symObjAddr: 0x5884, symBinAddr: 0x18F60, symSize: 0x25C }
  - { offsetInCU: 0x77, offset: 0x11426, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC8delegateAA05LogindE8Delegate_pSgvgTo', symObjAddr: 0x20, symBinAddr: 0x19718, symSize: 0x48 }
  - { offsetInCU: 0xCB, offset: 0x1147A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC8delegateAA05LogindE8Delegate_pSgvsTo', symObjAddr: 0xB0, symBinAddr: 0x197A8, symSize: 0x50 }
  - { offsetInCU: 0x14B, offset: 0x114FA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC12forceDisplaySbvgTo', symObjAddr: 0x234, symBinAddr: 0x1992C, symSize: 0x44 }
  - { offsetInCU: 0x19F, offset: 0x1154E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC12forceDisplaySbvsTo', symObjAddr: 0x2BC, symBinAddr: 0x199B4, symSize: 0x48 }
  - { offsetInCU: 0x2CC, offset: 0x1167B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewCACycfcTo', symObjAddr: 0x548, symBinAddr: 0x19C40, symSize: 0xA8 }
  - { offsetInCU: 0x3AA, offset: 0x11759, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC7tagline7message10colorStyleACSSSg_AgA09FBTooltipE0C05ColorI0OtcfcTo', symObjAddr: 0x83C, symBinAddr: 0x19F34, symSize: 0x84 }
  - { offsetInCU: 0x429, offset: 0x117D8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC7present2in13arrowPosition9directionySo6UIViewC_So7CGPointVAA09FBTooltipE0C14ArrowDirectionOtFTo', symObjAddr: 0xB6C, symBinAddr: 0x1A264, symSize: 0x70 }
  - { offsetInCU: 0x444, offset: 0x117F3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC05fetchD13Configuration015_3798D0DCC12906H16A17D23FC0B3F34C1LL4with13arrowPosition9directionySo6UIViewC_So7CGPointVAA09FBTooltipE0C14ArrowDirectionOtFySo0aD0CSg_s5Error_pSgtcfU_', symObjAddr: 0xBDC, symBinAddr: 0x1A2D4, symSize: 0x270 }
  - { offsetInCU: 0x599, offset: 0x11948, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC27serverConfigurationProvider06stringH0AcA06ServerG9Providing_p_AA019UserInterfaceStringK0_ptcfcTf4een_n09FBSDKCoreB00jgH0C_So20FBSDKInternalUtilityCTg5', symObjAddr: 0xF04, symBinAddr: 0x1A5FC, symSize: 0x178 }
  - { offsetInCU: 0x633, offset: 0x119E2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC27serverConfigurationProvider06stringH0AcA06ServerG9Providing_p_AA019UserInterfaceStringK0_ptcfcTf4een_n', symObjAddr: 0x10E0, symBinAddr: 0x1A774, symSize: 0x19C }
  - { offsetInCU: 0x6C1, offset: 0x11A70, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC27serverConfigurationProvider06stringH0AcA06ServerG9Providing_p_AA019UserInterfaceStringK0_ptcfCTfq4een_nTf4ngn_nTf4gnn_n', symObjAddr: 0x127C, symBinAddr: 0x1A910, symSize: 0xE4 }
  - { offsetInCU: 0x7D2, offset: 0x11B81, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewCfETo', symObjAddr: 0xEBC, symBinAddr: 0x1A5B4, symSize: 0x48 }
  - { offsetInCU: 0x800, offset: 0x11BAF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC05fetchD13Configuration015_3798D0DCC12906H16A17D23FC0B3F34C1LL4with13arrowPosition9directionySo6UIViewC_So7CGPointVAA09FBTooltipE0C14ArrowDirectionOtFySo0aD0CSg_s5Error_pSgtcfU_TA', symObjAddr: 0x13D0, symBinAddr: 0x1AA20, symSize: 0x10 }
  - { offsetInCU: 0x813, offset: 0x11BC2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewCMa', symObjAddr: 0x1524, symBinAddr: 0x1AB74, symSize: 0x20 }
  - { offsetInCU: 0x826, offset: 0x11BD5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24LoginTooltipViewDelegate_pSgXwWOh', symObjAddr: 0x1558, symBinAddr: 0x1ABA8, symSize: 0x24 }
  - { offsetInCU: 0x839, offset: 0x11BE8, size: 0x8, addend: 0x0, symName: ___swift_allocate_boxed_opaque_existential_1, symObjAddr: 0x157C, symBinAddr: 0x1ABCC, symSize: 0x3C }
  - { offsetInCU: 0x9E6, offset: 0x11D95, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewCACycfC', symObjAddr: 0x0, symBinAddr: 0x196F8, symSize: 0x20 }
  - { offsetInCU: 0xA10, offset: 0x11DBF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC8delegateAA05LogindE8Delegate_pSgvg', symObjAddr: 0x68, symBinAddr: 0x19760, symSize: 0x48 }
  - { offsetInCU: 0xA55, offset: 0x11E04, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC8delegateAA05LogindE8Delegate_pSgvs', symObjAddr: 0x100, symBinAddr: 0x197F8, symSize: 0x58 }
  - { offsetInCU: 0xA7B, offset: 0x11E2A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC8delegateAA05LogindE8Delegate_pSgvM', symObjAddr: 0x158, symBinAddr: 0x19850, symSize: 0x70 }
  - { offsetInCU: 0xA9E, offset: 0x11E4D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC8delegateAA05LogindE8Delegate_pSgvM.resume.0', symObjAddr: 0x1C8, symBinAddr: 0x198C0, symSize: 0x6C }
  - { offsetInCU: 0xACF, offset: 0x11E7E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC12forceDisplaySbvg', symObjAddr: 0x278, symBinAddr: 0x19970, symSize: 0x44 }
  - { offsetInCU: 0xB0E, offset: 0x11EBD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC12forceDisplaySbvs', symObjAddr: 0x304, symBinAddr: 0x199FC, symSize: 0x48 }
  - { offsetInCU: 0xB31, offset: 0x11EE0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC12forceDisplaySbvM', symObjAddr: 0x34C, symBinAddr: 0x19A44, symSize: 0x44 }
  - { offsetInCU: 0xB54, offset: 0x11F03, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC12forceDisplaySbvM.resume.0', symObjAddr: 0x390, symBinAddr: 0x19A88, symSize: 0x4 }
  - { offsetInCU: 0xB85, offset: 0x11F34, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC18shouldForceDisplaySbvM', symObjAddr: 0x394, symBinAddr: 0x19A8C, symSize: 0x6C }
  - { offsetInCU: 0xBBD, offset: 0x11F6C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC18shouldForceDisplaySbvM.resume.0', symObjAddr: 0x400, symBinAddr: 0x19AF8, symSize: 0x14 }
  - { offsetInCU: 0xBEF, offset: 0x11F9E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC27serverConfigurationProviderAA06ServerG9Providing_pvg', symObjAddr: 0x414, symBinAddr: 0x19B0C, symSize: 0x24 }
  - { offsetInCU: 0xC10, offset: 0x11FBF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC14stringProviderAA28UserInterfaceStringProviding_pvg', symObjAddr: 0x438, symBinAddr: 0x19B30, symSize: 0x24 }
  - { offsetInCU: 0xC49, offset: 0x11FF8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewCACycfc', symObjAddr: 0x4A0, symBinAddr: 0x19B98, symSize: 0xA8 }
  - { offsetInCU: 0xD26, offset: 0x120D5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC7tagline7message10colorStyleACSSSg_AgA09FBTooltipE0C05ColorI0OtcfC', symObjAddr: 0x5F0, symBinAddr: 0x19CE8, symSize: 0x12C }
  - { offsetInCU: 0xD75, offset: 0x12124, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC7tagline7message10colorStyleACSSSg_AgA09FBTooltipE0C05ColorI0Otcfc', symObjAddr: 0x71C, symBinAddr: 0x19E14, symSize: 0x120 }
  - { offsetInCU: 0xDC2, offset: 0x12171, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC27serverConfigurationProvider06stringH0AcA06ServerG9Providing_p_AA019UserInterfaceStringK0_ptcfC', symObjAddr: 0x8C0, symBinAddr: 0x19FB8, symSize: 0x90 }
  - { offsetInCU: 0xDFE, offset: 0x121AD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC27serverConfigurationProvider06stringH0AcA06ServerG9Providing_p_AA019UserInterfaceStringK0_ptcfc', symObjAddr: 0x950, symBinAddr: 0x1A048, symSize: 0xFC }
  - { offsetInCU: 0xE6E, offset: 0x1221D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC7present2in13arrowPosition9directionySo6UIViewC_So7CGPointVAA09FBTooltipE0C14ArrowDirectionOtF', symObjAddr: 0xA4C, symBinAddr: 0x1A144, symSize: 0x120 }
  - { offsetInCU: 0xF66, offset: 0x12315, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewCfE', symObjAddr: 0xE4C, symBinAddr: 0x1A544, symSize: 0x3C }
  - { offsetInCU: 0xF87, offset: 0x12336, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewCfD', symObjAddr: 0xE88, symBinAddr: 0x1A580, symSize: 0x34 }
  - { offsetInCU: 0x169, offset: 0x12509, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionC4hashSivgTo', symObjAddr: 0x334, symBinAddr: 0x1AF4C, symSize: 0x4C }
  - { offsetInCU: 0x1D9, offset: 0x12579, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionC6stringACSgSS_tcfcTo', symObjAddr: 0x5C0, symBinAddr: 0x1B194, symSize: 0x28 }
  - { offsetInCU: 0x20D, offset: 0x125AD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionC11permissions18fromRawPermissionsShyACGSgShySSG_tFZTo', symObjAddr: 0x5EC, symBinAddr: 0x1B1C0, symSize: 0x90 }
  - { offsetInCU: 0x23D, offset: 0x125DD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionC14rawPermissions4fromShySSGShyACG_tFZTo', symObjAddr: 0x6C4, symBinAddr: 0x1B298, symSize: 0x98 }
  - { offsetInCU: 0x2A3, offset: 0x12643, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionC7isEqualySbypSgFTo', symObjAddr: 0x850, symBinAddr: 0x1B424, symSize: 0x80 }
  - { offsetInCU: 0x2E9, offset: 0x12689, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionCACycfcTo', symObjAddr: 0x91C, symBinAddr: 0x1B4F0, symSize: 0x2C }
  - { offsetInCU: 0x361, offset: 0x12701, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionC11permissions18fromRawPermissionsShyACGSgShySSG_tFZTf4nd_n', symObjAddr: 0x990, symBinAddr: 0x1B564, symSize: 0x330 }
  - { offsetInCU: 0x535, offset: 0x128D5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionCfETo', symObjAddr: 0x97C, symBinAddr: 0x1B550, symSize: 0x14 }
  - { offsetInCU: 0x580, offset: 0x12920, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionCMa', symObjAddr: 0xD88, symBinAddr: 0x1B894, symSize: 0x20 }
  - { offsetInCU: 0x593, offset: 0x12933, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionCSo8NSObjectCSH10ObjectiveCWl', symObjAddr: 0xDBC, symBinAddr: 0x1B8C8, symSize: 0x44 }
  - { offsetInCU: 0x5A6, offset: 0x12946, size: 0x8, addend: 0x0, symName: '_$sSh5IndexV8_VariantOyx__GSHRzlWOe', symObjAddr: 0xE00, symBinAddr: 0x1B90C, symSize: 0xC }
  - { offsetInCU: 0x5F8, offset: 0x12998, size: 0x8, addend: 0x0, symName: '_$sSlsE3mapySayqd__Gqd__7ElementQzKXEKlFShy13FBSDKLoginKit12FBPermissionCG_SSTg504$s13c5Kit12E93C14rawPermissions4fromShySSGShyACG_tFZSSACcfu_32e0d58b938ad0b6cb17de1b825049cc00ACSSTf3nnpk_nTf1cn_n', symObjAddr: 0x0, symBinAddr: 0x1AC18, symSize: 0x2A8 }
  - { offsetInCU: 0x9E4, offset: 0x12D84, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionC14rawPermissions4fromShySSGShyACG_tFZ', symObjAddr: 0x67C, symBinAddr: 0x1B250, symSize: 0x48 }
  - { offsetInCU: 0xA6E, offset: 0x12E0E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionC11descriptionSSvg', symObjAddr: 0x2FC, symBinAddr: 0x1AF14, symSize: 0x38 }
  - { offsetInCU: 0xAA8, offset: 0x12E48, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionC4hashSivg', symObjAddr: 0x380, symBinAddr: 0x1AF98, symSize: 0x4C }
  - { offsetInCU: 0xAC5, offset: 0x12E65, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionC6stringACSgSS_tcfC', symObjAddr: 0x410, symBinAddr: 0x1AFE4, symSize: 0x40 }
  - { offsetInCU: 0xADE, offset: 0x12E7E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionC6stringACSgSS_tcfc', symObjAddr: 0x450, symBinAddr: 0x1B024, symSize: 0x170 }
  - { offsetInCU: 0xB27, offset: 0x12EC7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionC11permissions18fromRawPermissionsShyACGSgShySSG_tFZ', symObjAddr: 0x5E8, symBinAddr: 0x1B1BC, symSize: 0x4 }
  - { offsetInCU: 0xB74, offset: 0x12F14, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionC7isEqualySbypSgF', symObjAddr: 0x75C, symBinAddr: 0x1B330, symSize: 0xF4 }
  - { offsetInCU: 0xBCE, offset: 0x12F6E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionCACycfC', symObjAddr: 0x8D0, symBinAddr: 0x1B4A4, symSize: 0x20 }
  - { offsetInCU: 0xBE1, offset: 0x12F81, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionCACycfc', symObjAddr: 0x8F0, symBinAddr: 0x1B4C4, symSize: 0x2C }
  - { offsetInCU: 0xC35, offset: 0x12FD5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionCfD', symObjAddr: 0x948, symBinAddr: 0x1B51C, symSize: 0x34 }
  - { offsetInCU: 0x4E, offset: 0x130D5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO13kNUXRectInset12CoreGraphics7CGFloatVvpZ', symObjAddr: 0x50E0, symBinAddr: 0x5EF00, symSize: 0x0 }
  - { offsetInCU: 0x68, offset: 0x130EF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO16kNUXBubbleMargin12CoreGraphics7CGFloatVvpZ', symObjAddr: 0x50E8, symBinAddr: 0x5EF08, symSize: 0x0 }
  - { offsetInCU: 0x82, offset: 0x13109, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO15kNUXPointMargin12CoreGraphics7CGFloatVvpZ', symObjAddr: 0x50F0, symBinAddr: 0x5EF10, symSize: 0x0 }
  - { offsetInCU: 0x9C, offset: 0x13123, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO16kNUXCornerRadius12CoreGraphics7CGFloatVvpZ', symObjAddr: 0x50F8, symBinAddr: 0x5EF18, symSize: 0x0 }
  - { offsetInCU: 0xB6, offset: 0x1313D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO11kNUXSideCap12CoreGraphics7CGFloatVvpZ', symObjAddr: 0x5100, symBinAddr: 0x5EF20, symSize: 0x0 }
  - { offsetInCU: 0xD0, offset: 0x13157, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO12kNUXFontSize12CoreGraphics7CGFloatVvpZ', symObjAddr: 0x5108, symBinAddr: 0x5EF28, symSize: 0x0 }
  - { offsetInCU: 0xEA, offset: 0x13171, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO18kNUXCrossGlyphSize12CoreGraphics7CGFloatVvpZ', symObjAddr: 0x5110, symBinAddr: 0x5EF30, symSize: 0x0 }
  - { offsetInCU: 0x104, offset: 0x1318B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO27kFriendlyBlueGradientColorsSaySo10CGColorRefaGvpZ', symObjAddr: 0x5118, symBinAddr: 0x5EF38, symSize: 0x0 }
  - { offsetInCU: 0x11E, offset: 0x131A5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO12kNeutralGraySaySo10CGColorRefaGvpZ', symObjAddr: 0x5120, symBinAddr: 0x5EF40, symSize: 0x0 }
  - { offsetInCU: 0x12C, offset: 0x131B3, size: 0x8, addend: 0x0, symName: '_$sIeg_IeyB_TR', symObjAddr: 0x0, symBinAddr: 0x1B91C, symSize: 0x2C }
  - { offsetInCU: 0x1A8, offset: 0x1322F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC10ColorStyleOSYAASY8rawValuexSg03RawH0Qz_tcfCTW', symObjAddr: 0x22C4, symBinAddr: 0x1DBE0, symSize: 0x30 }
  - { offsetInCU: 0x1D7, offset: 0x1325E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC10ColorStyleOSYAASY8rawValue03RawH0QzvgTW', symObjAddr: 0x22F4, symBinAddr: 0x1DC10, symSize: 0xC }
  - { offsetInCU: 0x67C, offset: 0x13703, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO13kNUXRectInset_WZ', symObjAddr: 0x2300, symBinAddr: 0x1DC1C, symSize: 0x10 }
  - { offsetInCU: 0x695, offset: 0x1371C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO16kNUXBubbleMargin_WZ', symObjAddr: 0x2310, symBinAddr: 0x1DC2C, symSize: 0x50 }
  - { offsetInCU: 0x6BD, offset: 0x13744, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO15kNUXPointMargin_WZ', symObjAddr: 0x2360, symBinAddr: 0x1DC7C, symSize: 0x10 }
  - { offsetInCU: 0x6D6, offset: 0x1375D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO16kNUXCornerRadius_WZ', symObjAddr: 0x2370, symBinAddr: 0x1DC8C, symSize: 0x10 }
  - { offsetInCU: 0x6EF, offset: 0x13776, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO11kNUXSideCap_WZ', symObjAddr: 0x2380, symBinAddr: 0x1DC9C, symSize: 0x10 }
  - { offsetInCU: 0x708, offset: 0x1378F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO12kNUXFontSize_WZ', symObjAddr: 0x2390, symBinAddr: 0x1DCAC, symSize: 0x10 }
  - { offsetInCU: 0x721, offset: 0x137A8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO18kNUXCrossGlyphSize_WZ', symObjAddr: 0x23A0, symBinAddr: 0x1DCBC, symSize: 0x10 }
  - { offsetInCU: 0x73A, offset: 0x137C1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO27kFriendlyBlueGradientColors_WZ', symObjAddr: 0x23B0, symBinAddr: 0x1DCCC, symSize: 0x10C }
  - { offsetInCU: 0x7F5, offset: 0x1387C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO12kNeutralGray_WZ', symObjAddr: 0x24BC, symBinAddr: 0x1DDD8, symSize: 0x108 }
  - { offsetInCU: 0x8B0, offset: 0x13937, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC15displayDurationSdvgTo', symObjAddr: 0x25C4, symBinAddr: 0x1DEE0, symSize: 0x44 }
  - { offsetInCU: 0x8EB, offset: 0x13972, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC15displayDurationSdvsTo', symObjAddr: 0x2608, symBinAddr: 0x1DF24, symSize: 0x50 }
  - { offsetInCU: 0x92D, offset: 0x139B4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC10colorStyleAC05ColorF0OvgTo', symObjAddr: 0x2658, symBinAddr: 0x1DF74, symSize: 0x44 }
  - { offsetInCU: 0x968, offset: 0x139EF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC10colorStyleAC05ColorF0OvsTo', symObjAddr: 0x269C, symBinAddr: 0x1DFB8, symSize: 0x6C }
  - { offsetInCU: 0x9B4, offset: 0x13A3B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9textLabelSo7UILabelCvpfiAFyXEfU_', symObjAddr: 0x2810, symBinAddr: 0x1E12C, symSize: 0x13C }
  - { offsetInCU: 0xA2E, offset: 0x13AB5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9textLabelSo7UILabelCvgTo', symObjAddr: 0x2990, symBinAddr: 0x1E2AC, symSize: 0x10 }
  - { offsetInCU: 0xA4E, offset: 0x13AD5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9textLabelSo7UILabelCvgTo', symObjAddr: 0x2990, symBinAddr: 0x1E2AC, symSize: 0x10 }
  - { offsetInCU: 0xA93, offset: 0x13B1A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewCACycfcTo', symObjAddr: 0x29EC, symBinAddr: 0x1E308, symSize: 0x18 }
  - { offsetInCU: 0xAB3, offset: 0x13B3A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewCACycfcTo', symObjAddr: 0x29EC, symBinAddr: 0x1E308, symSize: 0x18 }
  - { offsetInCU: 0xAF1, offset: 0x13B78, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7tagline7message10colorStyleACSSSg_AgC05ColorH0OtcfcTo', symObjAddr: 0x2A64, symBinAddr: 0x1E380, symSize: 0x84 }
  - { offsetInCU: 0xB3E, offset: 0x13BC5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC5coderACSgSo7NSCoderC_tcfcTo', symObjAddr: 0x2B30, symBinAddr: 0x1E44C, symSize: 0x28 }
  - { offsetInCU: 0xB83, offset: 0x13C0A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewCfDTo', symObjAddr: 0x2BAC, symBinAddr: 0x1E4C8, symSize: 0x78 }
  - { offsetInCU: 0xBB3, offset: 0x13C3A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7present4fromySo6UIViewC_tFTo', symObjAddr: 0x2CB4, symBinAddr: 0x1E5D0, symSize: 0x50 }
  - { offsetInCU: 0xBCE, offset: 0x13C55, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7present2in13arrowPosition9directionySo6UIViewC_So7CGPointVAC14ArrowDirectionOtFTo', symObjAddr: 0x2D04, symBinAddr: 0x1E620, symSize: 0x70 }
  - { offsetInCU: 0xBE9, offset: 0x13C70, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7dismissyyFTo', symObjAddr: 0x2E38, symBinAddr: 0x1E754, symSize: 0x98 }
  - { offsetInCU: 0xC69, offset: 0x13CF0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC13animateFadeInyyFTo', symObjAddr: 0x368C, symBinAddr: 0x1EFA8, symSize: 0x28 }
  - { offsetInCU: 0xCA0, offset: 0x13D27, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC14onTapInTooltip33_1C39B2F52DDA14663AEF238AF411735ALLyySo19UIGestureRecognizerCFTo', symObjAddr: 0x36B4, symBinAddr: 0x1EFD0, symSize: 0x74 }
  - { offsetInCU: 0xCEA, offset: 0x13D71, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC4drawyySo6CGRectVFTo', symObjAddr: 0x372C, symBinAddr: 0x1F044, symSize: 0x28 }
  - { offsetInCU: 0xD33, offset: 0x13DBA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC14layoutSubviewsyyFTo', symObjAddr: 0x3788, symBinAddr: 0x1F06C, symSize: 0x5C }
  - { offsetInCU: 0xD67, offset: 0x13DEE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC47scheduleFadeoutRespectingMinimumDisplayDuration33_1C39B2F52DDA14663AEF238AF411735ALLyyFTo', symObjAddr: 0x37E4, symBinAddr: 0x1F0C8, symSize: 0x84 }
  - { offsetInCU: 0xDFB, offset: 0x13E82, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC5frameACSo6CGRectV_tcfcTo', symObjAddr: 0x38E4, symBinAddr: 0x1F1C8, symSize: 0x2C }
  - { offsetInCU: 0xE5E, offset: 0x13EE5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC35fbsdkCreateUpPointingBubbleWithRect33_1C39B2F52DDA14663AEF238AF411735ALLySo16CGMutablePathRefaSo6CGRectV_12CoreGraphics7CGFloatVA2LtFTf4nnnnd_n', symObjAddr: 0x39B8, symBinAddr: 0x1F29C, symSize: 0x2D4 }
  - { offsetInCU: 0x10A2, offset: 0x14129, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC37fbsdkCreateDownPointingBubbleWithRect33_1C39B2F52DDA14663AEF238AF411735ALLySo16CGMutablePathRefaSo6CGRectV_12CoreGraphics7CGFloatVA2LtFTf4nnnnd_n', symObjAddr: 0x3C8C, symBinAddr: 0x1F570, symSize: 0x2D4 }
  - { offsetInCU: 0x12E6, offset: 0x1436D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC29createCloseCrossGlyphWithRect33_1C39B2F52DDA14663AEF238AF411735ALLySo16CGMutablePathRefaSo6CGRectVFTf4nd_n', symObjAddr: 0x3F60, symBinAddr: 0x1F844, symSize: 0x2AC }
  - { offsetInCU: 0x164D, offset: 0x146D4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewCMa', symObjAddr: 0x294C, symBinAddr: 0x1E268, symSize: 0x20 }
  - { offsetInCU: 0x1660, offset: 0x146E7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewCfETo', symObjAddr: 0x2C24, symBinAddr: 0x1E540, symSize: 0x90 }
  - { offsetInCU: 0x168E, offset: 0x14715, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7dismissyyFyycfU_TA', symObjAddr: 0x2E30, symBinAddr: 0x1E74C, symSize: 0x8 }
  - { offsetInCU: 0x16A1, offset: 0x14728, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC13animateFadeInyyFyycfU_TA', symObjAddr: 0x3120, symBinAddr: 0x1EA3C, symSize: 0x10 }
  - { offsetInCU: 0x16B4, offset: 0x1473B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC13animateFadeInyyFyycfU0_TA', symObjAddr: 0x337C, symBinAddr: 0x1EC98, symSize: 0x8 }
  - { offsetInCU: 0x16C7, offset: 0x1474E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC13animateFadeInyyFyycfU1_TA', symObjAddr: 0x3404, symBinAddr: 0x1ED20, symSize: 0x8 }
  - { offsetInCU: 0x16DA, offset: 0x14761, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x340C, symBinAddr: 0x1ED28, symSize: 0x10 }
  - { offsetInCU: 0x16ED, offset: 0x14774, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x341C, symBinAddr: 0x1ED38, symSize: 0x8 }
  - { offsetInCU: 0x1700, offset: 0x14787, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC13animateFadeInyyFySbcfU2_TA', symObjAddr: 0x3590, symBinAddr: 0x1EEAC, symSize: 0xC }
  - { offsetInCU: 0x1713, offset: 0x1479A, size: 0x8, addend: 0x0, symName: '_$sSbIegy_SbIeyBy_TR', symObjAddr: 0x3650, symBinAddr: 0x1EF6C, symSize: 0x3C }
  - { offsetInCU: 0x1748, offset: 0x147CF, size: 0x8, addend: 0x0, symName: '_$ss29getContiguousArrayStorageType3fors01_bcD0CyxGmxm_tlF13FBSDKLoginKit18DeviceLoginManagerC_Tgm5', symObjAddr: 0x3910, symBinAddr: 0x1F1F4, symSize: 0x54 }
  - { offsetInCU: 0x1773, offset: 0x147FA, size: 0x8, addend: 0x0, symName: '_$ss29getContiguousArrayStorageType3fors01_bcD0CyxGmxm_tlFSo10CGColorRefa_Tgm5', symObjAddr: 0x3964, symBinAddr: 0x1F248, symSize: 0x54 }
  - { offsetInCU: 0x179E, offset: 0x14825, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC14ArrowDirectionOSHAASQWb', symObjAddr: 0x49FC, symBinAddr: 0x202E0, symSize: 0x4 }
  - { offsetInCU: 0x17B1, offset: 0x14838, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC14ArrowDirectionOAESQAAWl', symObjAddr: 0x4A00, symBinAddr: 0x202E4, symSize: 0x44 }
  - { offsetInCU: 0x17C4, offset: 0x1484B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC10ColorStyleOSHAASQWb', symObjAddr: 0x4A44, symBinAddr: 0x20328, symSize: 0x4 }
  - { offsetInCU: 0x17D7, offset: 0x1485E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC10ColorStyleOAESQAAWl', symObjAddr: 0x4A48, symBinAddr: 0x2032C, symSize: 0x44 }
  - { offsetInCU: 0x17EA, offset: 0x14871, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC14ArrowDirectionOMa', symObjAddr: 0x4E80, symBinAddr: 0x20764, symSize: 0x10 }
  - { offsetInCU: 0x17FD, offset: 0x14884, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC10ColorStyleOMa', symObjAddr: 0x4E90, symBinAddr: 0x20774, symSize: 0x10 }
  - { offsetInCU: 0x1810, offset: 0x14897, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7dismissyyFyycfU_TA.23', symObjAddr: 0x4EA0, symBinAddr: 0x20784, symSize: 0x8 }
  - { offsetInCU: 0x1823, offset: 0x148AA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC13animateFadeInyyFySbcfU2_ySbcfU_TA', symObjAddr: 0x4ECC, symBinAddr: 0x207B0, symSize: 0x8 }
  - { offsetInCU: 0x1836, offset: 0x148BD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC14animateFadeOut33_1C39B2F52DDA14663AEF238AF411735ALL17completionHandleryyyc_tFyycfU_TA', symObjAddr: 0x4EF8, symBinAddr: 0x207DC, symSize: 0x14 }
  - { offsetInCU: 0x1866, offset: 0x148ED, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC14animateFadeOut33_1C39B2F52DDA14663AEF238AF411735ALL17completionHandleryyyc_tFySbcfU0_TA', symObjAddr: 0x4F0C, symBinAddr: 0x207F0, symSize: 0x20 }
  - { offsetInCU: 0x1996, offset: 0x14A1D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC10ColorStyleOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x21FC, symBinAddr: 0x1DB18, symSize: 0x14 }
  - { offsetInCU: 0x19F1, offset: 0x14A78, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC10ColorStyleOSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0x2258, symBinAddr: 0x1DB74, symSize: 0x28 }
  - { offsetInCU: 0x1D75, offset: 0x14DFC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7tagline7message10colorStyleACSSSg_AgC05ColorH0Otcfc', symObjAddr: 0x2C, symBinAddr: 0x1B948, symSize: 0x504 }
  - { offsetInCU: 0x1EC1, offset: 0x14F48, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7present2in13arrowPosition9directionySo6UIViewC_So7CGPointVAC14ArrowDirectionOtF', symObjAddr: 0x530, symBinAddr: 0x1BE4C, symSize: 0xF8 }
  - { offsetInCU: 0x1FCA, offset: 0x15051, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC15displayDurationSdvg', symObjAddr: 0x628, symBinAddr: 0x1BF44, symSize: 0x44 }
  - { offsetInCU: 0x1FE7, offset: 0x1506E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC15displayDurationSdvs', symObjAddr: 0x66C, symBinAddr: 0x1BF88, symSize: 0x50 }
  - { offsetInCU: 0x200D, offset: 0x15094, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC15displayDurationSdvM', symObjAddr: 0x6BC, symBinAddr: 0x1BFD8, symSize: 0x44 }
  - { offsetInCU: 0x2030, offset: 0x150B7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC15displayDurationSdvM.resume.0', symObjAddr: 0x700, symBinAddr: 0x1C01C, symSize: 0x4 }
  - { offsetInCU: 0x204F, offset: 0x150D6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC10colorStyleAC05ColorF0Ovg', symObjAddr: 0x704, symBinAddr: 0x1C020, symSize: 0x44 }
  - { offsetInCU: 0x207E, offset: 0x15105, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC10colorStyleAC05ColorF0Ovs', symObjAddr: 0x748, symBinAddr: 0x1C064, symSize: 0x54 }
  - { offsetInCU: 0x20BF, offset: 0x15146, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC10colorStyleAC05ColorF0OvM', symObjAddr: 0x79C, symBinAddr: 0x1C0B8, symSize: 0x48 }
  - { offsetInCU: 0x20E2, offset: 0x15169, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC10colorStyleAC05ColorF0OvM.resume.0', symObjAddr: 0x7E4, symBinAddr: 0x1C100, symSize: 0x30 }
  - { offsetInCU: 0x2142, offset: 0x151C9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7messageSSSgvs', symObjAddr: 0x820, symBinAddr: 0x1C13C, symSize: 0xD8 }
  - { offsetInCU: 0x21B4, offset: 0x1523B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7messageSSSgvM', symObjAddr: 0x8F8, symBinAddr: 0x1C214, symSize: 0x74 }
  - { offsetInCU: 0x21F4, offset: 0x1527B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7messageSSSgvM.resume.0', symObjAddr: 0x96C, symBinAddr: 0x1C288, symSize: 0x170 }
  - { offsetInCU: 0x22B0, offset: 0x15337, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7taglineSSSgvs', symObjAddr: 0xB38, symBinAddr: 0x1C454, symSize: 0xD0 }
  - { offsetInCU: 0x2322, offset: 0x153A9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7taglineSSSgvM', symObjAddr: 0xC08, symBinAddr: 0x1C524, symSize: 0x74 }
  - { offsetInCU: 0x2362, offset: 0x153E9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7taglineSSSgvM.resume.0', symObjAddr: 0xC7C, symBinAddr: 0x1C598, symSize: 0x160 }
  - { offsetInCU: 0x2502, offset: 0x15589, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7present4fromySo6UIViewC_tF', symObjAddr: 0xDDC, symBinAddr: 0x1C6F8, symSize: 0x228 }
  - { offsetInCU: 0x263D, offset: 0x156C4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7dismissyyF', symObjAddr: 0x1004, symBinAddr: 0x1C920, symSize: 0x78 }
  - { offsetInCU: 0x26A5, offset: 0x1572C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7dismissyyFyycfU_', symObjAddr: 0x2D74, symBinAddr: 0x1E690, symSize: 0xBC }
  - { offsetInCU: 0x272E, offset: 0x157B5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC13animateFadeInyyF', symObjAddr: 0x107C, symBinAddr: 0x1C998, symSize: 0x434 }
  - { offsetInCU: 0x2877, offset: 0x158FE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC13animateFadeInyyFyycfU_', symObjAddr: 0x2ED0, symBinAddr: 0x1E7EC, symSize: 0x224 }
  - { offsetInCU: 0x2956, offset: 0x159DD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC13animateFadeInyyFyycfU0_', symObjAddr: 0x3130, symBinAddr: 0x1EA4C, symSize: 0x220 }
  - { offsetInCU: 0x2A1C, offset: 0x15AA3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC13animateFadeInyyFyycfU1_', symObjAddr: 0x3384, symBinAddr: 0x1ECA0, symSize: 0x80 }
  - { offsetInCU: 0x2A49, offset: 0x15AD0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC13animateFadeInyyFySbcfU2_', symObjAddr: 0x3424, symBinAddr: 0x1ED40, symSize: 0x140 }
  - { offsetInCU: 0x2A94, offset: 0x15B1B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC13animateFadeInyyFySbcfU2_ySbcfU_', symObjAddr: 0x359C, symBinAddr: 0x1EEB8, symSize: 0xB4 }
  - { offsetInCU: 0x2ADA, offset: 0x15B61, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC14animateFadeOut33_1C39B2F52DDA14663AEF238AF411735ALL17completionHandleryyyc_tF', symObjAddr: 0x14B0, symBinAddr: 0x1CDCC, symSize: 0x168 }
  - { offsetInCU: 0x2BB2, offset: 0x15C39, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC12updateColors33_1C39B2F52DDA14663AEF238AF411735ALLyyF', symObjAddr: 0x1618, symBinAddr: 0x1CF34, symSize: 0x238 }
  - { offsetInCU: 0x2DB6, offset: 0x15E3D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC31layoutSubviewsAndDetermineFrame33_1C39B2F52DDA14663AEF238AF411735ALLSo6CGRectVyF', symObjAddr: 0x1850, symBinAddr: 0x1D16C, symSize: 0x494 }
  - { offsetInCU: 0x30F9, offset: 0x16180, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC3set33_1C39B2F52DDA14663AEF238AF411735ALL7message7taglineySSSg_AHtF', symObjAddr: 0x1CE4, symBinAddr: 0x1D600, symSize: 0x338 }
  - { offsetInCU: 0x3306, offset: 0x1638D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC24scheduleAutomaticFadeout33_1C39B2F52DDA14663AEF238AF411735ALLyyF', symObjAddr: 0x201C, symBinAddr: 0x1D938, symSize: 0x14C }
  - { offsetInCU: 0x33CC, offset: 0x16453, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC32cancelAllScheduledFadeOutMethods33_1C39B2F52DDA14663AEF238AF411735ALLyyF', symObjAddr: 0x2168, symBinAddr: 0x1DA84, symSize: 0x64 }
  - { offsetInCU: 0x33F1, offset: 0x16478, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC14ArrowDirectionO8rawValueSuvg', symObjAddr: 0x21CC, symBinAddr: 0x1DAE8, symSize: 0x4 }
  - { offsetInCU: 0x3416, offset: 0x1649D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC10ColorStyleO8rawValueAESgSu_tcfC', symObjAddr: 0x21D8, symBinAddr: 0x1DAF4, symSize: 0x20 }
  - { offsetInCU: 0x3433, offset: 0x164BA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC10ColorStyleO8rawValueSuvg', symObjAddr: 0x21F8, symBinAddr: 0x1DB14, symSize: 0x4 }
  - { offsetInCU: 0x3511, offset: 0x16598, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9textLabelSo7UILabelCvg', symObjAddr: 0x29A0, symBinAddr: 0x1E2BC, symSize: 0x10 }
  - { offsetInCU: 0x3532, offset: 0x165B9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewCACycfC', symObjAddr: 0x29B0, symBinAddr: 0x1E2CC, symSize: 0x20 }
  - { offsetInCU: 0x3545, offset: 0x165CC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewCACycfc', symObjAddr: 0x29D0, symBinAddr: 0x1E2EC, symSize: 0x1C }
  - { offsetInCU: 0x3585, offset: 0x1660C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7tagline7message10colorStyleACSSSg_AgC05ColorH0OtcfC', symObjAddr: 0x2A04, symBinAddr: 0x1E320, symSize: 0x60 }
  - { offsetInCU: 0x3598, offset: 0x1661F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC5coderACSgSo7NSCoderC_tcfC', symObjAddr: 0x2AE8, symBinAddr: 0x1E404, symSize: 0x44 }
  - { offsetInCU: 0x35AB, offset: 0x16632, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC5coderACSgSo7NSCoderC_tcfc', symObjAddr: 0x2B2C, symBinAddr: 0x1E448, symSize: 0x4 }
  - { offsetInCU: 0x35C5, offset: 0x1664C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewCfD', symObjAddr: 0x2B58, symBinAddr: 0x1E474, symSize: 0x54 }
  - { offsetInCU: 0x3646, offset: 0x166CD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC5frameACSo6CGRectV_tcfC', symObjAddr: 0x3868, symBinAddr: 0x1F14C, symSize: 0x50 }
  - { offsetInCU: 0x3659, offset: 0x166E0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC5frameACSo6CGRectV_tcfc', symObjAddr: 0x38B8, symBinAddr: 0x1F19C, symSize: 0x2C }
  - { offsetInCU: 0x36D3, offset: 0x1675A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC5coderACSgSo7NSCoderC_tcfcTf4dn_n', symObjAddr: 0x420C, symBinAddr: 0x1FAF0, symSize: 0x1DC }
  - { offsetInCU: 0x374A, offset: 0x167D1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC4drawyySo6CGRectVFTf4dn_n', symObjAddr: 0x43E8, symBinAddr: 0x1FCCC, symSize: 0x614 }
  - { offsetInCU: 0x2B, offset: 0x16DB2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVyACyAA0d7ManagerdE0CSg_s5Error_pSgtccfC', symObjAddr: 0x0, symBinAddr: 0x2085C, symSize: 0x44 }
  - { offsetInCU: 0xA6, offset: 0x16E2D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0xCC, symBinAddr: 0x20928, symSize: 0x4 }
  - { offsetInCU: 0xC5, offset: 0x16E4C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0xCC, symBinAddr: 0x20928, symSize: 0x4 }
  - { offsetInCU: 0xE8, offset: 0x16E6F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVMa', symObjAddr: 0x44, symBinAddr: 0x208A0, symSize: 0x3C }
  - { offsetInCU: 0xFB, offset: 0x16E82, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVwCP', symObjAddr: 0xD0, symBinAddr: 0x2092C, symSize: 0x90 }
  - { offsetInCU: 0x10E, offset: 0x16E95, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVwxx', symObjAddr: 0x160, symBinAddr: 0x209BC, symSize: 0x48 }
  - { offsetInCU: 0x121, offset: 0x16EA8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVwcp', symObjAddr: 0x1A8, symBinAddr: 0x20A04, symSize: 0x68 }
  - { offsetInCU: 0x134, offset: 0x16EBB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVwca', symObjAddr: 0x210, symBinAddr: 0x20A6C, symSize: 0x74 }
  - { offsetInCU: 0x147, offset: 0x16ECE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVwtk', symObjAddr: 0x284, symBinAddr: 0x20AE0, symSize: 0x64 }
  - { offsetInCU: 0x15A, offset: 0x16EE1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVwta', symObjAddr: 0x2E8, symBinAddr: 0x20B44, symSize: 0x6C }
  - { offsetInCU: 0x16D, offset: 0x16EF4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVwet', symObjAddr: 0x354, symBinAddr: 0x20BB0, symSize: 0xC }
  - { offsetInCU: 0x180, offset: 0x16F07, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVwst', symObjAddr: 0x3DC, symBinAddr: 0x20C38, symSize: 0xC }
  - { offsetInCU: 0x193, offset: 0x16F1A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVMr', symObjAddr: 0x460, symBinAddr: 0x20CBC, symSize: 0x74 }
  - { offsetInCU: 0x291, offset: 0x17018, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVyACyAA0d7ManagerdE0CSg_s5Error_pSgtccfC', symObjAddr: 0x0, symBinAddr: 0x2085C, symSize: 0x44 }
  - { offsetInCU: 0x2BA, offset: 0x17041, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerV14callAsFunctionyyAA0d7ManagerdE0CSg_s5Error_pSgtF', symObjAddr: 0x80, symBinAddr: 0x208DC, symSize: 0x48 }
  - { offsetInCU: 0x2FB, offset: 0x17082, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerV2eeoiySbAC_ACtFZ', symObjAddr: 0xC8, symBinAddr: 0x20924, symSize: 0x4 }
  - { offsetInCU: 0x27, offset: 0x17115, size: 0x8, addend: 0x0, symName: '_$sSo20FBSDKInternalUtilityC13FBSDKLoginKit29UserInterfaceElementProvidingA2cDP21topMostViewControllerSo06UIViewL0CSgyFTW', symObjAddr: 0x0, symBinAddr: 0x20D30, symSize: 0x24 }
  - { offsetInCU: 0x7E, offset: 0x1716C, size: 0x8, addend: 0x0, symName: '_$sSo20FBSDKInternalUtilityC13FBSDKLoginKit29UserInterfaceElementProvidingA2cDP21topMostViewControllerSo06UIViewL0CSgyFTW', symObjAddr: 0x0, symBinAddr: 0x20D30, symSize: 0x24 }
  - { offsetInCU: 0xC5, offset: 0x171B3, size: 0x8, addend: 0x0, symName: '_$sSo20FBSDKInternalUtilityC13FBSDKLoginKit29UserInterfaceElementProvidingA2cDP14viewController3forSo06UIViewJ0CSgSo0L0C_tFTW', symObjAddr: 0x24, symBinAddr: 0x20D54, symSize: 0x28 }
  - { offsetInCU: 0x107, offset: 0x171F5, size: 0x8, addend: 0x0, symName: '_$sSo20FBSDKInternalUtilityC13FBSDKLoginKit28UserInterfaceStringProvidingA2cDP16bundleForStringsSo8NSBundleCvgTW', symObjAddr: 0x4C, symBinAddr: 0x20D7C, symSize: 0x24 }
  - { offsetInCU: 0x49, offset: 0x17315, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE17loginButtonDidTapABvpZ', symObjAddr: 0x25A0, symBinAddr: 0x61170, symSize: 0x0 }
  - { offsetInCU: 0x63, offset: 0x1732F, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE17smartLoginServiceABvpZ', symObjAddr: 0x25A8, symBinAddr: 0x61178, symSize: 0x0 }
  - { offsetInCU: 0x7D, offset: 0x17349, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE16sessionAuthStartABvpZ', symObjAddr: 0x25B0, symBinAddr: 0x61180, symSize: 0x0 }
  - { offsetInCU: 0x97, offset: 0x17363, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE14sessionAuthEndABvpZ', symObjAddr: 0x25B8, symBinAddr: 0x61188, symSize: 0x0 }
  - { offsetInCU: 0xB1, offset: 0x1737D, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE22sessionAuthMethodStartABvpZ', symObjAddr: 0x25C0, symBinAddr: 0x61190, symSize: 0x0 }
  - { offsetInCU: 0xCB, offset: 0x17397, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE20sessionAuthMethodEndABvpZ', symObjAddr: 0x25C8, symBinAddr: 0x61198, symSize: 0x0 }
  - { offsetInCU: 0xE5, offset: 0x173B1, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE20sessionAuthHeartbeatABvpZ', symObjAddr: 0x25D0, symBinAddr: 0x611A0, symSize: 0x0 }
  - { offsetInCU: 0xF3, offset: 0x173BF, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE17loginButtonDidTap_WZ', symObjAddr: 0x0, symBinAddr: 0x20DA0, symSize: 0x34 }
  - { offsetInCU: 0x10D, offset: 0x173D9, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE17loginButtonDidTapABvau', symObjAddr: 0x34, symBinAddr: 0x20DD4, symSize: 0x40 }
  - { offsetInCU: 0x12B, offset: 0x173F7, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE17smartLoginService_WZ', symObjAddr: 0x90, symBinAddr: 0x20E30, symSize: 0x34 }
  - { offsetInCU: 0x145, offset: 0x17411, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE17smartLoginServiceABvau', symObjAddr: 0xC4, symBinAddr: 0x20E64, symSize: 0x40 }
  - { offsetInCU: 0x163, offset: 0x1742F, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE16sessionAuthStart_WZ', symObjAddr: 0x120, symBinAddr: 0x20EC0, symSize: 0x34 }
  - { offsetInCU: 0x17D, offset: 0x17449, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE16sessionAuthStartABvau', symObjAddr: 0x154, symBinAddr: 0x20EF4, symSize: 0x40 }
  - { offsetInCU: 0x19B, offset: 0x17467, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE14sessionAuthEnd_WZ', symObjAddr: 0x1B0, symBinAddr: 0x20F50, symSize: 0x34 }
  - { offsetInCU: 0x1B5, offset: 0x17481, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE14sessionAuthEndABvau', symObjAddr: 0x1E4, symBinAddr: 0x20F84, symSize: 0x40 }
  - { offsetInCU: 0x1D3, offset: 0x1749F, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE22sessionAuthMethodStart_WZ', symObjAddr: 0x240, symBinAddr: 0x20FE0, symSize: 0x34 }
  - { offsetInCU: 0x1ED, offset: 0x174B9, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE22sessionAuthMethodStartABvau', symObjAddr: 0x274, symBinAddr: 0x21014, symSize: 0x40 }
  - { offsetInCU: 0x20B, offset: 0x174D7, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE20sessionAuthMethodEnd_WZ', symObjAddr: 0x2D0, symBinAddr: 0x21070, symSize: 0x34 }
  - { offsetInCU: 0x225, offset: 0x174F1, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE20sessionAuthMethodEndABvau', symObjAddr: 0x304, symBinAddr: 0x210A4, symSize: 0x40 }
  - { offsetInCU: 0x243, offset: 0x1750F, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE20sessionAuthHeartbeat_WZ', symObjAddr: 0x360, symBinAddr: 0x21100, symSize: 0x34 }
  - { offsetInCU: 0x25D, offset: 0x17529, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE20sessionAuthHeartbeatABvau', symObjAddr: 0x394, symBinAddr: 0x21134, symSize: 0x40 }
  - { offsetInCU: 0x27, offset: 0x1761E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit21LoginCompleterFactoryV06createcD013urlParameters5appIDAA0C10Completing_pSDySSypG_SStF', symObjAddr: 0x0, symBinAddr: 0x211C8, symSize: 0x64 }
  - { offsetInCU: 0xA0, offset: 0x17697, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit21LoginCompleterFactoryVAA0cdE8ProtocolA2aDP06createcD013urlParameters5appIDAA0C10Completing_pSDySSypG_SStFTW', symObjAddr: 0x68, symBinAddr: 0x21230, symSize: 0x64 }
  - { offsetInCU: 0xFC, offset: 0x176F3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit21LoginCompleterFactoryVMa', symObjAddr: 0xCC, symBinAddr: 0x21294, symSize: 0x10 }
  - { offsetInCU: 0x1D1, offset: 0x177C8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit21LoginCompleterFactoryV06createcD013urlParameters5appIDAA0C10Completing_pSDySSypG_SStF', symObjAddr: 0x0, symBinAddr: 0x211C8, symSize: 0x64 }
  - { offsetInCU: 0x218, offset: 0x1780F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit21LoginCompleterFactoryVACycfC', symObjAddr: 0x64, symBinAddr: 0x2122C, symSize: 0x4 }
  - { offsetInCU: 0x8E, offset: 0x178ED, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC5nonceSSvgTo', symObjAddr: 0x4, symBinAddr: 0x212C0, symSize: 0x4C }
  - { offsetInCU: 0xDB, offset: 0x1793A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC8trackingAA0C8TrackingOvgTo', symObjAddr: 0x88, symBinAddr: 0x21344, symSize: 0x10 }
  - { offsetInCU: 0xFB, offset: 0x1795A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC8trackingAA0C8TrackingOvgTo', symObjAddr: 0x88, symBinAddr: 0x21344, symSize: 0x10 }
  - { offsetInCU: 0x129, offset: 0x17988, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC20requestedPermissionsShyAA12FBPermissionCGvgTo', symObjAddr: 0xA8, symBinAddr: 0x21364, symSize: 0x64 }
  - { offsetInCU: 0x176, offset: 0x179D5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC15messengerPageIdSSSgvgTo', symObjAddr: 0x11C, symBinAddr: 0x213D8, symSize: 0x5C }
  - { offsetInCU: 0x1BB, offset: 0x17A1A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC8authTypeSo0a4AuthF0aSgvgTo', symObjAddr: 0x1B0, symBinAddr: 0x2146C, symSize: 0x10 }
  - { offsetInCU: 0x1DA, offset: 0x17A39, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC8authTypeSo0a4AuthF0aSgvgTo', symObjAddr: 0x1B0, symBinAddr: 0x2146C, symSize: 0x10 }
  - { offsetInCU: 0x20A, offset: 0x17A69, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC12codeVerifierAA04CodeF0CvgTo', symObjAddr: 0x1F0, symBinAddr: 0x214AC, symSize: 0x10 }
  - { offsetInCU: 0x22A, offset: 0x17A89, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC12codeVerifierAA04CodeF0CvgTo', symObjAddr: 0x1F0, symBinAddr: 0x214AC, symSize: 0x10 }
  - { offsetInCU: 0x297, offset: 0x17AF6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking5nonce15messengerPageIdACSgSaySSG_AA0C8TrackingOS2SSgtcfcTo', symObjAddr: 0x3F0, symBinAddr: 0x216AC, symSize: 0x128 }
  - { offsetInCU: 0x349, offset: 0x17BA8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking5nonce15messengerPageId8authTypeACSgSaySSG_AA0C8TrackingOS2SSgSo0a4AuthL0aSgtcfcTo', symObjAddr: 0x6AC, symBinAddr: 0x21968, symSize: 0xAC }
  - { offsetInCU: 0x3AC, offset: 0x17C0B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking5nonceACSgSaySSG_AA0C8TrackingOSStcfcTo', symObjAddr: 0x8A8, symBinAddr: 0x21B64, symSize: 0xC4 }
  - { offsetInCU: 0x43D, offset: 0x17C9C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking15messengerPageIdACSgSaySSG_AA0C8TrackingOSSSgtcfcTo', symObjAddr: 0xAFC, symBinAddr: 0x21DB8, symSize: 0x6C }
  - { offsetInCU: 0x4AA, offset: 0x17D09, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking15messengerPageId8authTypeACSgSaySSG_AA0C8TrackingOSSSgSo0a4AuthK0aSgtcfcTo', symObjAddr: 0xD14, symBinAddr: 0x21FD0, symSize: 0x88 }
  - { offsetInCU: 0x52B, offset: 0x17D8A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking5nonce15messengerPageId8authType12codeVerifierACSgSaySSG_AA0C8TrackingOS2SSgSo0a4AuthL0aSgAA04CodeN0CtcfcTo', symObjAddr: 0x153C, symBinAddr: 0x227F8, symSize: 0xC4 }
  - { offsetInCU: 0x584, offset: 0x17DE3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8trackingACSgSaySSG_AA0C8TrackingOtcfcTo', symObjAddr: 0x17F8, symBinAddr: 0x22AB4, symSize: 0x108 }
  - { offsetInCU: 0x5FC, offset: 0x17E5B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC8trackingACSgAA0C8TrackingO_tcfcTo', symObjAddr: 0x19D4, symBinAddr: 0x22C90, symSize: 0x68 }
  - { offsetInCU: 0x669, offset: 0x17EC8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationCACycfcTo', symObjAddr: 0x1A88, symBinAddr: 0x22D44, symSize: 0x2C }
  - { offsetInCU: 0x702, offset: 0x17F61, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking5nonce15messengerPageId8authType12codeVerifierACSgShy09FBSDKCoreB010PermissionOG_AA0C8TrackingOS2SSgSo0a4AuthL0aSgAA04CodeN0CtcfCTf4nnnnnnd_n', symObjAddr: 0x1B58, symBinAddr: 0x22E14, symSize: 0x27C }
  - { offsetInCU: 0xBFC, offset: 0x1845B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationCfETo', symObjAddr: 0x1AE8, symBinAddr: 0x22DA4, symSize: 0x70 }
  - { offsetInCU: 0xDAB, offset: 0x1860A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationCMa', symObjAddr: 0x1E58, symBinAddr: 0x23090, symSize: 0x20 }
  - { offsetInCU: 0x10E8, offset: 0x18947, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking5nonce15messengerPageId8authType12codeVerifierACSgShy09FBSDKCoreB010PermissionOG_AA0C8TrackingOS2SSgSo0a4AuthL0aSgAA04CodeN0CtcfC', symObjAddr: 0x0, symBinAddr: 0x212BC, symSize: 0x4 }
  - { offsetInCU: 0x1112, offset: 0x18971, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC5nonceSSvg', symObjAddr: 0x50, symBinAddr: 0x2130C, symSize: 0x38 }
  - { offsetInCU: 0x1141, offset: 0x189A0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC8trackingAA0C8TrackingOvg', symObjAddr: 0x98, symBinAddr: 0x21354, symSize: 0x10 }
  - { offsetInCU: 0x116E, offset: 0x189CD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC20requestedPermissionsShyAA12FBPermissionCGvg', symObjAddr: 0x10C, symBinAddr: 0x213C8, symSize: 0x10 }
  - { offsetInCU: 0x119B, offset: 0x189FA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC15messengerPageIdSSSgvg', symObjAddr: 0x178, symBinAddr: 0x21434, symSize: 0x38 }
  - { offsetInCU: 0x11CA, offset: 0x18A29, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC8authTypeSo0a4AuthF0aSgvg', symObjAddr: 0x1C0, symBinAddr: 0x2147C, symSize: 0x30 }
  - { offsetInCU: 0x11F9, offset: 0x18A58, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC12codeVerifierAA04CodeF0Cvg', symObjAddr: 0x200, symBinAddr: 0x214BC, symSize: 0x10 }
  - { offsetInCU: 0x1266, offset: 0x18AC5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking5nonce15messengerPageIdACSgSaySSG_AA0C8TrackingOS2SSgtcfC', symObjAddr: 0x210, symBinAddr: 0x214CC, symSize: 0xF8 }
  - { offsetInCU: 0x12AB, offset: 0x18B0A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking5nonce15messengerPageIdACSgSaySSG_AA0C8TrackingOS2SSgtcfc', symObjAddr: 0x308, symBinAddr: 0x215C4, symSize: 0xE8 }
  - { offsetInCU: 0x12EC, offset: 0x18B4B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking5nonce15messengerPageId8authTypeACSgSaySSG_AA0C8TrackingOS2SSgSo0a4AuthL0aSgtcfC', symObjAddr: 0x518, symBinAddr: 0x217D4, symSize: 0x78 }
  - { offsetInCU: 0x1305, offset: 0x18B64, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking5nonce15messengerPageId8authTypeACSgSaySSG_AA0C8TrackingOS2SSgSo0a4AuthL0aSgtcfc', symObjAddr: 0x590, symBinAddr: 0x2184C, symSize: 0x11C }
  - { offsetInCU: 0x13BA, offset: 0x18C19, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking5nonceACSgSaySSG_AA0C8TrackingOSStcfC', symObjAddr: 0x758, symBinAddr: 0x21A14, symSize: 0xB0 }
  - { offsetInCU: 0x13FA, offset: 0x18C59, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking5nonceACSgSaySSG_AA0C8TrackingOSStcfc', symObjAddr: 0x808, symBinAddr: 0x21AC4, symSize: 0xA0 }
  - { offsetInCU: 0x1432, offset: 0x18C91, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking15messengerPageIdACSgSaySSG_AA0C8TrackingOSSSgtcfC', symObjAddr: 0x96C, symBinAddr: 0x21C28, symSize: 0x58 }
  - { offsetInCU: 0x1445, offset: 0x18CA4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking15messengerPageIdACSgSaySSG_AA0C8TrackingOSSSgtcfc', symObjAddr: 0x9C4, symBinAddr: 0x21C80, symSize: 0x138 }
  - { offsetInCU: 0x1495, offset: 0x18CF4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking15messengerPageId8authTypeACSgSaySSG_AA0C8TrackingOSSSgSo0a4AuthK0aSgtcfC', symObjAddr: 0xB68, symBinAddr: 0x21E24, symSize: 0x60 }
  - { offsetInCU: 0x14A8, offset: 0x18D07, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking15messengerPageId8authTypeACSgSaySSG_AA0C8TrackingOSSSgSo0a4AuthK0aSgtcfc', symObjAddr: 0xBC8, symBinAddr: 0x21E84, symSize: 0x14C }
  - { offsetInCU: 0x1507, offset: 0x18D66, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking5nonce15messengerPageId8authType12codeVerifierACSgSaySSG_AA0C8TrackingOS2SSgSo0a4AuthL0aSgAA04CodeN0CtcfC', symObjAddr: 0xD9C, symBinAddr: 0x22058, symSize: 0x88 }
  - { offsetInCU: 0x1598, offset: 0x18DF7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking5nonce15messengerPageId8authType12codeVerifierACSgSaySSG_AA0C8TrackingOS2SSgSo0a4AuthL0aSgAA04CodeN0Ctcfc', symObjAddr: 0xE24, symBinAddr: 0x220E0, symSize: 0x500 }
  - { offsetInCU: 0x187E, offset: 0x190DD, size: 0x8, addend: 0x0, symName: '_$sSTsSQ7ElementRpzrlE8containsySbABFSaySo18FBSDKLoginAuthTypeaG_Tg5', symObjAddr: 0x1324, symBinAddr: 0x225E0, symSize: 0x154 }
  - { offsetInCU: 0x1A3E, offset: 0x1929D, size: 0x8, addend: 0x0, symName: '_$sSTsSQ7ElementRpzrlE8containsySbABFSaySSG_Tg5', symObjAddr: 0x1478, symBinAddr: 0x22734, symSize: 0xC4 }
  - { offsetInCU: 0x1BCD, offset: 0x1942C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8trackingACSgSaySSG_AA0C8TrackingOtcfC', symObjAddr: 0x1600, symBinAddr: 0x228BC, symSize: 0x100 }
  - { offsetInCU: 0x1C09, offset: 0x19468, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8trackingACSgSaySSG_AA0C8TrackingOtcfc', symObjAddr: 0x1700, symBinAddr: 0x229BC, symSize: 0xF8 }
  - { offsetInCU: 0x1C55, offset: 0x194B4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC8trackingACSgAA0C8TrackingO_tcfC', symObjAddr: 0x1900, symBinAddr: 0x22BBC, symSize: 0x70 }
  - { offsetInCU: 0x1C8D, offset: 0x194EC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC8trackingACSgAA0C8TrackingO_tcfc', symObjAddr: 0x1970, symBinAddr: 0x22C2C, symSize: 0x64 }
  - { offsetInCU: 0x1CB3, offset: 0x19512, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationCACycfC', symObjAddr: 0x1A3C, symBinAddr: 0x22CF8, symSize: 0x20 }
  - { offsetInCU: 0x1CC6, offset: 0x19525, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationCACycfc', symObjAddr: 0x1A5C, symBinAddr: 0x22D18, symSize: 0x2C }
  - { offsetInCU: 0x1D1A, offset: 0x19579, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationCfD', symObjAddr: 0x1AB4, symBinAddr: 0x22D70, symSize: 0x34 }
  - { offsetInCU: 0x220, offset: 0x19862, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV10Foundation13CustomNSErrorAadEP11errorDomainSSvgZTW', symObjAddr: 0x23C, symBinAddr: 0x23300, symSize: 0x5C }
  - { offsetInCU: 0x255, offset: 0x19897, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV10Foundation13CustomNSErrorAadEP9errorCodeSivgTW', symObjAddr: 0x298, symBinAddr: 0x2335C, symSize: 0x8 }
  - { offsetInCU: 0x274, offset: 0x198B6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV10Foundation13CustomNSErrorAadEP9errorCodeSivgTW', symObjAddr: 0x298, symBinAddr: 0x2335C, symSize: 0x8 }
  - { offsetInCU: 0x285, offset: 0x198C7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV10Foundation13CustomNSErrorAadEP13errorUserInfoSDySSypGvgTW', symObjAddr: 0x2A0, symBinAddr: 0x23364, symSize: 0x8 }
  - { offsetInCU: 0x2A4, offset: 0x198E6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV10Foundation13CustomNSErrorAadEP13errorUserInfoSDySSypGvgTW', symObjAddr: 0x2A0, symBinAddr: 0x23364, symSize: 0x8 }
  - { offsetInCU: 0x2B5, offset: 0x198F7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVSHAASH9hashValueSivgTW', symObjAddr: 0x2A8, symBinAddr: 0x2336C, symSize: 0x44 }
  - { offsetInCU: 0x38F, offset: 0x199D1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0x2EC, symBinAddr: 0x233B0, symSize: 0x28 }
  - { offsetInCU: 0x3FD, offset: 0x19A3F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x3AC, symBinAddr: 0x23470, symSize: 0x34 }
  - { offsetInCU: 0x4A6, offset: 0x19AE8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14LoginErrorCodeOSYAASY8rawValuexSg03RawG0Qz_tcfCTW', symObjAddr: 0x4A4, symBinAddr: 0x23568, symSize: 0x30 }
  - { offsetInCU: 0x4D5, offset: 0x19B17, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14LoginErrorCodeOSYAASY8rawValue03RawG0QzvgTW', symObjAddr: 0x4D4, symBinAddr: 0x23598, symSize: 0xC }
  - { offsetInCU: 0x4F1, offset: 0x19B33, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14LoginErrorCodeO8rawValueACSgSi_tcfCTf4nd_n', symObjAddr: 0x4F4, symBinAddr: 0x235B8, symSize: 0x18 }
  - { offsetInCU: 0x555, offset: 0x19B97, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV10Foundation13CustomNSErrorAAs0D0PWb', symObjAddr: 0x50C, symBinAddr: 0x235D0, symSize: 0x4 }
  - { offsetInCU: 0x568, offset: 0x19BAA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVACs0D0AAWl', symObjAddr: 0x510, symBinAddr: 0x235D4, symSize: 0x44 }
  - { offsetInCU: 0x57B, offset: 0x19BBD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVSHAASQWb', symObjAddr: 0x554, symBinAddr: 0x23618, symSize: 0x4 }
  - { offsetInCU: 0x58E, offset: 0x19BD0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVACSQAAWl', symObjAddr: 0x558, symBinAddr: 0x2361C, symSize: 0x44 }
  - { offsetInCU: 0x5A1, offset: 0x19BE3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14LoginErrorCodeOSHAASQWb', symObjAddr: 0x59C, symBinAddr: 0x23660, symSize: 0x4 }
  - { offsetInCU: 0x5B4, offset: 0x19BF6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14LoginErrorCodeOACSQAAWl', symObjAddr: 0x5A0, symBinAddr: 0x23664, symSize: 0x44 }
  - { offsetInCU: 0x5C7, offset: 0x19C09, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVwxx', symObjAddr: 0x5E8, symBinAddr: 0x236AC, symSize: 0x28 }
  - { offsetInCU: 0x5DA, offset: 0x19C1C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVwca', symObjAddr: 0x650, symBinAddr: 0x23714, symSize: 0x64 }
  - { offsetInCU: 0x5ED, offset: 0x19C2F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVwta', symObjAddr: 0x6C8, symBinAddr: 0x23778, symSize: 0x44 }
  - { offsetInCU: 0x600, offset: 0x19C42, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVwet', symObjAddr: 0x70C, symBinAddr: 0x237BC, symSize: 0x48 }
  - { offsetInCU: 0x613, offset: 0x19C55, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVwst', symObjAddr: 0x754, symBinAddr: 0x23804, symSize: 0x40 }
  - { offsetInCU: 0x626, offset: 0x19C68, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVMa', symObjAddr: 0x794, symBinAddr: 0x23844, symSize: 0x10 }
  - { offsetInCU: 0x639, offset: 0x19C7B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14LoginErrorCodeOMa', symObjAddr: 0x7A4, symBinAddr: 0x23854, symSize: 0x10 }
  - { offsetInCU: 0x64C, offset: 0x19C8E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVAC10Foundation13CustomNSErrorAAWl', symObjAddr: 0x7B4, symBinAddr: 0x23864, symSize: 0x44 }
  - { offsetInCU: 0x6BC, offset: 0x19CFE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0x314, symBinAddr: 0x233D8, symSize: 0x40 }
  - { offsetInCU: 0x752, offset: 0x19D94, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVs0D0AAsADP9_userInfoyXlSgvgTW', symObjAddr: 0x3A4, symBinAddr: 0x23468, symSize: 0x4 }
  - { offsetInCU: 0x76D, offset: 0x19DAF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVs0D0AAsADP19_getEmbeddedNSErroryXlSgyFTW', symObjAddr: 0x3A8, symBinAddr: 0x2346C, symSize: 0x4 }
  - { offsetInCU: 0x7ED, offset: 0x19E2F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14LoginErrorCodeOSHAASH9hashValueSivgTW', symObjAddr: 0x3F8, symBinAddr: 0x234BC, symSize: 0x44 }
  - { offsetInCU: 0x894, offset: 0x19ED6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14LoginErrorCodeOSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0x43C, symBinAddr: 0x23500, symSize: 0x28 }
  - { offsetInCU: 0x8E3, offset: 0x19F25, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14LoginErrorCodeOSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0x464, symBinAddr: 0x23528, symSize: 0x40 }
  - { offsetInCU: 0x968, offset: 0x19FAA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14LoginErrorCodeOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x4E0, symBinAddr: 0x235A4, symSize: 0x14 }
  - { offsetInCU: 0x9FB, offset: 0x1A03D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVs0D0AAsADP7_domainSSvgTW', symObjAddr: 0x354, symBinAddr: 0x23418, symSize: 0x28 }
  - { offsetInCU: 0xA17, offset: 0x1A059, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVs0D0AAsADP5_codeSivgTW', symObjAddr: 0x37C, symBinAddr: 0x23440, symSize: 0x28 }
  - { offsetInCU: 0xA9F, offset: 0x1A0E1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV03_nsD0So7NSErrorCSgvg', symObjAddr: 0x0, symBinAddr: 0x230C4, symSize: 0x28 }
  - { offsetInCU: 0xAB2, offset: 0x1A0F4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV9errorCodeSivg', symObjAddr: 0x28, symBinAddr: 0x230EC, symSize: 0x8 }
  - { offsetInCU: 0xAC6, offset: 0x1A108, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV13errorUserInfoSDySSypGvg', symObjAddr: 0x30, symBinAddr: 0x230F4, symSize: 0x8 }
  - { offsetInCU: 0xAE5, offset: 0x1A127, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV03_nsD0ACSo7NSErrorC_tcfC', symObjAddr: 0x38, symBinAddr: 0x230FC, symSize: 0xA0 }
  - { offsetInCU: 0xB08, offset: 0x1A14A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV_8userInfoAcA0cD4CodeO_SDySSypGtcfC', symObjAddr: 0xD8, symBinAddr: 0x2319C, symSize: 0xC }
  - { offsetInCU: 0xB38, offset: 0x1A17A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14LoginErrorCodeO8rawValueSivg', symObjAddr: 0xE4, symBinAddr: 0x231A8, symSize: 0x4 }
  - { offsetInCU: 0xB5D, offset: 0x1A19F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV11errorDomainSSvgZ', symObjAddr: 0xE8, symBinAddr: 0x231AC, symSize: 0x5C }
  - { offsetInCU: 0xB84, offset: 0x1A1C6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV8reservedAA0cD4CodeOvgZ', symObjAddr: 0x144, symBinAddr: 0x23208, symSize: 0x8 }
  - { offsetInCU: 0xBA5, offset: 0x1A1E7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV7unknownAA0cD4CodeOvgZ', symObjAddr: 0x14C, symBinAddr: 0x23210, symSize: 0x8 }
  - { offsetInCU: 0xBC6, offset: 0x1A208, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV15passwordChangedAA0cD4CodeOvgZ', symObjAddr: 0x154, symBinAddr: 0x23218, symSize: 0x8 }
  - { offsetInCU: 0xBE7, offset: 0x1A229, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV16userCheckpointedAA0cD4CodeOvgZ', symObjAddr: 0x15C, symBinAddr: 0x23220, symSize: 0x8 }
  - { offsetInCU: 0xC08, offset: 0x1A24A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV12userMismatchAA0cD4CodeOvgZ', symObjAddr: 0x164, symBinAddr: 0x23228, symSize: 0x8 }
  - { offsetInCU: 0xC29, offset: 0x1A26B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV15unconfirmedUserAA0cD4CodeOvgZ', symObjAddr: 0x16C, symBinAddr: 0x23230, symSize: 0x8 }
  - { offsetInCU: 0xC4A, offset: 0x1A28C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV24systemAccountAppDisabledAA0cD4CodeOvgZ', symObjAddr: 0x174, symBinAddr: 0x23238, symSize: 0x8 }
  - { offsetInCU: 0xC6B, offset: 0x1A2AD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV24systemAccountUnavailableAA0cD4CodeOvgZ', symObjAddr: 0x17C, symBinAddr: 0x23240, symSize: 0x8 }
  - { offsetInCU: 0xC8C, offset: 0x1A2CE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV18badChallengeStringAA0cD4CodeOvgZ', symObjAddr: 0x184, symBinAddr: 0x23248, symSize: 0x8 }
  - { offsetInCU: 0xCAD, offset: 0x1A2EF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV14invalidIDTokenAA0cD4CodeOvgZ', symObjAddr: 0x18C, symBinAddr: 0x23250, symSize: 0x8 }
  - { offsetInCU: 0xCCE, offset: 0x1A310, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV18missingAccessTokenAA0cD4CodeOvgZ', symObjAddr: 0x194, symBinAddr: 0x23258, symSize: 0x8 }
  - { offsetInCU: 0xCEF, offset: 0x1A331, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV2eeoiySbAC_ACtFZ', symObjAddr: 0x19C, symBinAddr: 0x23260, symSize: 0x34 }
  - { offsetInCU: 0xD5E, offset: 0x1A3A0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV4hash4intoys6HasherVz_tF', symObjAddr: 0x1D0, symBinAddr: 0x23294, symSize: 0x28 }
  - { offsetInCU: 0xDD0, offset: 0x1A412, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV9hashValueSivg', symObjAddr: 0x1F8, symBinAddr: 0x232BC, symSize: 0x44 }
  - { offsetInCU: 0xF08, offset: 0x1A54A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14LoginErrorCodeO8rawValueACSgSi_tcfC', symObjAddr: 0x3E0, symBinAddr: 0x234A4, symSize: 0x18 }
  - { offsetInCU: 0x27, offset: 0x1A5B6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16LoginErrorDomain_WZ', symObjAddr: 0x0, symBinAddr: 0x238A8, symSize: 0x2C }
  - { offsetInCU: 0x49, offset: 0x1A5D8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16LoginErrorDomainSSvp', symObjAddr: 0x88, symBinAddr: 0x5F158, symSize: 0x0 }
  - { offsetInCU: 0x57, offset: 0x1A5E6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16LoginErrorDomain_WZ', symObjAddr: 0x0, symBinAddr: 0x238A8, symSize: 0x2C }
  - { offsetInCU: 0x93, offset: 0x1A622, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16LoginErrorDomainSSvg', symObjAddr: 0x2C, symBinAddr: 0x238D4, symSize: 0x5C }
  - { offsetInCU: 0xC2, offset: 0x1A7B3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC15defaultAudienceAA07DefaultF0OvgTo', symObjAddr: 0x4C4, symBinAddr: 0x23E0C, symSize: 0x44 }
  - { offsetInCU: 0x117, offset: 0x1A808, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC15defaultAudienceAA07DefaultF0OvsTo', symObjAddr: 0x54C, symBinAddr: 0x23E94, symSize: 0x48 }
  - { offsetInCU: 0x1DB, offset: 0x1A8CC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC13configurationAA0C13ConfigurationCSgvgTo', symObjAddr: 0x760, symBinAddr: 0x24068, symSize: 0x48 }
  - { offsetInCU: 0x22F, offset: 0x1A920, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC13configurationAA0C13ConfigurationCSgvsTo', symObjAddr: 0x7F8, symBinAddr: 0x24100, symSize: 0x64 }
  - { offsetInCU: 0x285, offset: 0x1A976, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC20requestedPermissionsShyAA12FBPermissionCGSgvgTo', symObjAddr: 0x85C, symBinAddr: 0x24164, symSize: 0xA8 }
  - { offsetInCU: 0x2D9, offset: 0x1A9CA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC20requestedPermissionsShyAA12FBPermissionCGSgvsTo', symObjAddr: 0x94C, symBinAddr: 0x24254, symSize: 0x9C }
  - { offsetInCU: 0x3B2, offset: 0x1AAA3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC17usedSafariSessionSbvgTo', symObjAddr: 0xC08, symBinAddr: 0x24510, symSize: 0x44 }
  - { offsetInCU: 0x406, offset: 0x1AAF7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC17usedSafariSessionSbvsTo', symObjAddr: 0xC90, symBinAddr: 0x24598, symSize: 0x48 }
  - { offsetInCU: 0x46A, offset: 0x1AB5B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC012isPerformingC0SbvgTo', symObjAddr: 0xD64, symBinAddr: 0x2466C, symSize: 0x4C }
  - { offsetInCU: 0x59E, offset: 0x1AC8F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC15defaultAudienceAcA07DefaultF0O_tcfcTo', symObjAddr: 0x15C8, symBinAddr: 0x24ED0, symSize: 0x64 }
  - { offsetInCU: 0x665, offset: 0x1AD56, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5logIn4from13configuration10completionySo16UIViewControllerCSg_AA0C13ConfigurationCSgyAA0cdC6ResultCSg_s5Error_pSgtctFTo', symObjAddr: 0x1A70, symBinAddr: 0x25378, symSize: 0xB8 }
  - { offsetInCU: 0x6E9, offset: 0x1ADDA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5logIn11permissions4from7handlerySaySSG_So16UIViewControllerCSgyAA0cdC6ResultCSg_s5Error_pSgtcSgtFTo', symObjAddr: 0x2278, symBinAddr: 0x25B80, symSize: 0xD4 }
  - { offsetInCU: 0x72C, offset: 0x1AE1D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5logIn33_C218275A97333B874EDDFE627110566CLLyyFySb_s5Error_pSgtcfU_', symObjAddr: 0x245C, symBinAddr: 0x25D64, symSize: 0x1EC }
  - { offsetInCU: 0x8D0, offset: 0x1AFC1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC21reauthorizeDataAccess4from7handlerySo16UIViewControllerC_yAA0cdC6ResultCSg_s5Error_pSgtctFTo', symObjAddr: 0x3990, symBinAddr: 0x27298, symSize: 0x74 }
  - { offsetInCU: 0x935, offset: 0x1B026, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC6logOutyyFTo', symObjAddr: 0x3BC8, symBinAddr: 0x274D0, symSize: 0x28 }
  - { offsetInCU: 0xA44, offset: 0x1B135, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC22completeAuthentication10parameters15expectChallengeyAA01_C20CompletionParametersC_SbtFTo', symObjAddr: 0x50F0, symBinAddr: 0x289F8, symSize: 0x58 }
  - { offsetInCU: 0xAD4, offset: 0x1B1C5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC15logInParameters13configuration12loggingToken20authenticationMethodSDyS2SGSgAA0C13ConfigurationCSg_SSSgSStFTo', symObjAddr: 0x6D10, symBinAddr: 0x2A618, symSize: 0xFC }
  - { offsetInCU: 0xB0B, offset: 0x1B1FC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC24validateReauthentication11accessToken11loginResult04userH5NonceySo011FBSDKAccessH0C_AA0cdcJ0CSgSSSgtFTo', symObjAddr: 0x7240, symBinAddr: 0x2AB48, symSize: 0xAC }
  - { offsetInCU: 0xB42, offset: 0x1B233, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18storeExpectedNonceyySSSgFTo', symObjAddr: 0x7638, symBinAddr: 0x2AF40, symSize: 0x68 }
  - { offsetInCU: 0xB72, offset: 0x1B263, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCACycfcTo', symObjAddr: 0x7888, symBinAddr: 0x2B190, symSize: 0x20 }
  - { offsetInCU: 0xBA2, offset: 0x1B293, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCAA0C9ProvidingA2aDP15defaultAudienceAA07DefaultG0OvgTW', symObjAddr: 0x80A0, symBinAddr: 0x2B9A8, symSize: 0x48 }
  - { offsetInCU: 0xBDC, offset: 0x1B2CD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCAA0C9ProvidingA2aDP15defaultAudienceAA07DefaultG0OvsTW', symObjAddr: 0x80E8, symBinAddr: 0x2B9F0, symSize: 0x4C }
  - { offsetInCU: 0xC1D, offset: 0x1B30E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCAA0C9ProvidingA2aDP15defaultAudienceAA07DefaultG0OvMTW', symObjAddr: 0x8134, symBinAddr: 0x2BA3C, symSize: 0x48 }
  - { offsetInCU: 0xC57, offset: 0x1B348, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCAA0C9ProvidingA2aDP5logIn14viewController13configuration10completionySo06UIViewI0CSg_AA0C13ConfigurationCSgyAA0C6ResultOctFTW', symObjAddr: 0x817C, symBinAddr: 0x2BA84, symSize: 0x7C }
  - { offsetInCU: 0xCBD, offset: 0x1B3AE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCAA0C9ProvidingA2aDP5logIn11permissions4from7handlerySaySSG_So16UIViewControllerCSgyAA0cdC6ResultCSg_s5Error_pSgtcSgtFTW', symObjAddr: 0x81F8, symBinAddr: 0x2BB00, symSize: 0x20 }
  - { offsetInCU: 0xCD8, offset: 0x1B3C9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCAA0C9ProvidingA2aDP6logOutyyFTW', symObjAddr: 0x8218, symBinAddr: 0x2BB20, symSize: 0x20 }
  - { offsetInCU: 0xCF3, offset: 0x1B3E4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCAA17DependentAsObjectA2aDP22configuredDependencies0gI0QzSgvgTW', symObjAddr: 0x848C, symBinAddr: 0x2BD94, symSize: 0x58 }
  - { offsetInCU: 0xD25, offset: 0x1B416, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCAA17DependentAsObjectA2aDP22configuredDependencies0gI0QzSgvsTW', symObjAddr: 0x84E4, symBinAddr: 0x2BDEC, symSize: 0x60 }
  - { offsetInCU: 0xD67, offset: 0x1B458, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCAA17DependentAsObjectA2aDP22configuredDependencies0gI0QzSgvMTW', symObjAddr: 0x8544, symBinAddr: 0x2BE4C, symSize: 0x44 }
  - { offsetInCU: 0xDA1, offset: 0x1B492, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCAA17DependentAsObjectA2aDP19defaultDependencies0gI0QzSgvgTW', symObjAddr: 0x8588, symBinAddr: 0x2BE90, symSize: 0x4 }
  - { offsetInCU: 0xE00, offset: 0x1B4F1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC21reauthorizeDataAccess4from7handlerySo16UIViewControllerC_yAA0cdC6ResultCSg_s5Error_pSgtctF04$s13a6Kit012cd2C6l13CSgSo7NSErrorq11IeyByy_ADs5M12_pSgIeggg_TRAKSo0S0CSgIeyByy_Tf1ncn_nTf4dng_n', symObjAddr: 0xDAB4, symBinAddr: 0x312A4, symSize: 0x4B4 }
  - { offsetInCU: 0x14FF, offset: 0x1BBF0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC19defaultDependenciesAC06ObjectF0VSgvpACTk', symObjAddr: 0xFD8, symBinAddr: 0x248E0, symSize: 0x90 }
  - { offsetInCU: 0x15B4, offset: 0x1BCA5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultCSgSo7NSErrorCSgIeyByy_ADs5Error_pSgIeggg_TR', symObjAddr: 0x1B28, symBinAddr: 0x25430, symSize: 0x58 }
  - { offsetInCU: 0x15F0, offset: 0x1BCE1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultCSgs5Error_pSgytIegnnr_AdFIeggg_TR', symObjAddr: 0x1C6C, symBinAddr: 0x25574, symSize: 0x60 }
  - { offsetInCU: 0x1A72, offset: 0x1C163, size: 0x8, addend: 0x0, symName: '_$sSbs5Error_pSgIegyg_SbSo7NSErrorCSgIeyByy_TR', symObjAddr: 0x72F4, symBinAddr: 0x2ABFC, symSize: 0x64 }
  - { offsetInCU: 0x1A98, offset: 0x1C189, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCfETo', symObjAddr: 0x78DC, symBinAddr: 0x2B1E4, symSize: 0xA4 }
  - { offsetInCU: 0x1ADE, offset: 0x1C1CF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC10makeOpenerACyFZTo', symObjAddr: 0x79A0, symBinAddr: 0x2B2A8, symSize: 0x24 }
  - { offsetInCU: 0x1B76, offset: 0x1C267, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC11application_4open17sourceApplication10annotationSbSo13UIApplicationCSg_10Foundation3URLVSgSSSgypSgtFTo', symObjAddr: 0x79D8, symBinAddr: 0x2B2E0, symSize: 0x164 }
  - { offsetInCU: 0x1BA8, offset: 0x1C299, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC7canOpen_3for17sourceApplication10annotationSb10Foundation3URLV_So13UIApplicationCSgSSSgypSgtFTo', symObjAddr: 0x7B3C, symBinAddr: 0x2B444, symSize: 0x128 }
  - { offsetInCU: 0x1BF6, offset: 0x1C2E7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC26applicationDidBecomeActiveyySo13UIApplicationCFTo', symObjAddr: 0x7CB4, symBinAddr: 0x2B5BC, symSize: 0x88 }
  - { offsetInCU: 0x1C62, offset: 0x1C353, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC19isAuthenticationURLySb10Foundation0G0VFTo', symObjAddr: 0x7D9C, symBinAddr: 0x2B6A4, symSize: 0xD4 }
  - { offsetInCU: 0x1CC3, offset: 0x1C3B4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC21shouldStopPropagation2ofSb10Foundation3URLV_tFTo', symObjAddr: 0x8004, symBinAddr: 0x2B90C, symSize: 0x9C }
  - { offsetInCU: 0x1CDE, offset: 0x1C3CF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit29LoginCompleterFactoryProtocol_pWOb', symObjAddr: 0x85D0, symBinAddr: 0x2BED8, symSize: 0x18 }
  - { offsetInCU: 0x1CF1, offset: 0x1C3E2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5logIn14viewController13configuration10completionySo06UIViewH0CSg_AA0C13ConfigurationCSgyAA0C6ResultOctFyAA0cdcM0CSg_s5Error_pSgtcfU_TA', symObjAddr: 0x860C, symBinAddr: 0x2BF14, symSize: 0x8 }
  - { offsetInCU: 0x1D04, offset: 0x1C3F5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5logIn11permissions14viewController10completionySay09FBSDKCoreB010PermissionOG_So06UIViewI0CSgyAA0C6ResultOcSgtFyAA0cdcN0CSg_s5Error_pSgtcfU_TA', symObjAddr: 0x8640, symBinAddr: 0x2BF48, symSize: 0x8 }
  - { offsetInCU: 0x1D17, offset: 0x1C408, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesVWOh', symObjAddr: 0x8648, symBinAddr: 0x2BF50, symSize: 0x2C }
  - { offsetInCU: 0x1DD4, offset: 0x1C4C5, size: 0x8, addend: 0x0, symName: '_$ss22_ContiguousArrayBufferV19_uninitializedCount15minimumCapacityAByxGSi_SitcfC13FBSDKLoginKit18DeviceLoginManagerC_Tgm5', symObjAddr: 0x8C28, symBinAddr: 0x2C4EC, symSize: 0x80 }
  - { offsetInCU: 0x1E33, offset: 0x1C524, size: 0x8, addend: 0x0, symName: '_$ss22_ContiguousArrayBufferV19_uninitializedCount15minimumCapacityAByxGSi_SitcfCSS_Tgm5', symObjAddr: 0x8CA8, symBinAddr: 0x2C56C, symSize: 0x78 }
  - { offsetInCU: 0x1FC8, offset: 0x1C6B9, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV_8capacityAByxGs07__CocoaB0Vn_SitcfC13FBSDKLoginKit12FBPermissionC_Tgm5', symObjAddr: 0x93C4, symBinAddr: 0x2CC88, symSize: 0x1F8 }
  - { offsetInCU: 0x23FB, offset: 0x1CAEC, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV19genericIntersectionyAByxGqd__7ElementQyd__RszSTRd__lFADs13_UnsafeBitsetVXEfU_13FBSDKLoginKit12FBPermissionC_ShyAKGTg5', symObjAddr: 0xC124, symBinAddr: 0x2F9E8, symSize: 0x3E4 }
  - { offsetInCU: 0x2541, offset: 0x1CC32, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV12intersectionyAByxGADFADs13_UnsafeBitsetVXEfU_13FBSDKLoginKit12FBPermissionC_Tg5', symObjAddr: 0xC508, symBinAddr: 0x2FDCC, symSize: 0x4F4 }
  - { offsetInCU: 0x2655, offset: 0x1CD46, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC24validateReauthentication11accessToken11loginResult04userH5NonceySo011FBSDKAccessH0C_AA0cdcJ0CSgSSSgtFySo27FBSDKGraphRequestConnecting_pSg_ypSgs5Error_pSgtcfU_TA', symObjAddr: 0xD150, symBinAddr: 0x309E0, symSize: 0x30 }
  - { offsetInCU: 0x2668, offset: 0x1CD59, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0xD180, symBinAddr: 0x30A10, symSize: 0x10 }
  - { offsetInCU: 0x267B, offset: 0x1CD6C, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0xD190, symBinAddr: 0x30A20, symSize: 0x8 }
  - { offsetInCU: 0x274E, offset: 0x1CE3F, size: 0x8, addend: 0x0, symName: '_$sSTsE6reduce4into_qd__qd__n_yqd__z_7ElementQztKXEtKlFSDyS2SSgG_s17_NativeDictionaryVyS2SGTg5051$sSD16compactMapValuesySDyxqd__Gqd__Sgq_KXEKlFys17_dE44Vyxqd__Gz_x3key_q_5valuettKXEfU_SS_SSSgSSTG5xq_Sgs5Error_pr0_lyAESSIsgnrzo_Tf1ncn_nTf4nng_n', symObjAddr: 0xD198, symBinAddr: 0x30A28, symSize: 0x344 }
  - { offsetInCU: 0x29A6, offset: 0x1D097, size: 0x8, addend: 0x0, symName: '_$sSh21_nonEmptyArrayLiteralShyxGSayxG_tcfC13FBSDKLoginKit12FBPermissionC_Tgm5Tf4g_n', symObjAddr: 0xD798, symBinAddr: 0x30F88, symSize: 0x31C }
  - { offsetInCU: 0x2B8B, offset: 0x1D27C, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV12intersectionyAByxGADF13FBSDKLoginKit12FBPermissionC_Tg5Tf4ng_n', symObjAddr: 0xDF68, symBinAddr: 0x31758, symSize: 0x14C }
  - { offsetInCU: 0x2CAA, offset: 0x1D39B, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV19genericIntersectionyAByxGqd__7ElementQyd__RszSTRd__lF13FBSDKLoginKit12FBPermissionC_ShyAIGTg5Tf4ng_n', symObjAddr: 0xE0B4, symBinAddr: 0x318A4, symSize: 0x14C }
  - { offsetInCU: 0x2DFB, offset: 0x1D4EC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit29LoginCompleterFactoryProtocol_pWOc', symObjAddr: 0xE89C, symBinAddr: 0x3208C, symSize: 0x44 }
  - { offsetInCU: 0x2E0E, offset: 0x1D4FF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCMU', symObjAddr: 0xE904, symBinAddr: 0x320F4, symSize: 0x8 }
  - { offsetInCU: 0x2E21, offset: 0x1D512, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCMa', symObjAddr: 0xE90C, symBinAddr: 0x320FC, symSize: 0x3C }
  - { offsetInCU: 0x2E34, offset: 0x1D525, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCMr', symObjAddr: 0xE948, symBinAddr: 0x32138, symSize: 0xBC }
  - { offsetInCU: 0x2E47, offset: 0x1D538, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVSgMa', symObjAddr: 0xEA04, symBinAddr: 0x321F4, symSize: 0x54 }
  - { offsetInCU: 0x2E5A, offset: 0x1D54B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesVwCP', symObjAddr: 0xEA58, symBinAddr: 0x32248, symSize: 0x30 }
  - { offsetInCU: 0x2E6D, offset: 0x1D55E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesVwxx', symObjAddr: 0xEA88, symBinAddr: 0x32278, symSize: 0x50 }
  - { offsetInCU: 0x2E80, offset: 0x1D571, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesVwcp', symObjAddr: 0xEAD8, symBinAddr: 0x322C8, symSize: 0xBC }
  - { offsetInCU: 0x2E93, offset: 0x1D584, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesVwca', symObjAddr: 0xEB94, symBinAddr: 0x32384, symSize: 0xE0 }
  - { offsetInCU: 0x2EA6, offset: 0x1D597, size: 0x8, addend: 0x0, symName: ___swift_memcpy112_8, symObjAddr: 0xEDDC, symBinAddr: 0x32464, symSize: 0x24 }
  - { offsetInCU: 0x2EB9, offset: 0x1D5AA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesVwta', symObjAddr: 0xEE00, symBinAddr: 0x32488, symSize: 0xA4 }
  - { offsetInCU: 0x2ECC, offset: 0x1D5BD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesVwet', symObjAddr: 0xEEA4, symBinAddr: 0x3252C, symSize: 0x48 }
  - { offsetInCU: 0x2EDF, offset: 0x1D5D0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesVwst', symObjAddr: 0xEEEC, symBinAddr: 0x32574, symSize: 0x5C }
  - { offsetInCU: 0x2EF2, offset: 0x1D5E3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesVMa', symObjAddr: 0xEF48, symBinAddr: 0x325D0, symSize: 0x10 }
  - { offsetInCU: 0x2F05, offset: 0x1D5F6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultCSgSo7NSErrorCSgIeyByy_ADs5Error_pSgIeggg_TRTA', symObjAddr: 0xEF7C, symBinAddr: 0x32604, symSize: 0x8 }
  - { offsetInCU: 0x2F18, offset: 0x1D609, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultCSgs5Error_pSgIeggg_SgWOe', symObjAddr: 0xEF84, symBinAddr: 0x3260C, symSize: 0x10 }
  - { offsetInCU: 0x2F2B, offset: 0x1D61C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC11application_4open17sourceApplication10annotationSbSo13UIApplicationCSg_10Foundation3URLVSgSSSgypSgtFyAA01_C20CompletionParametersCcfU_TA', symObjAddr: 0xF054, symBinAddr: 0x3267C, symSize: 0x24 }
  - { offsetInCU: 0x2F62, offset: 0x1D653, size: 0x8, addend: 0x0, symName: '_$sSh5IndexV8_VariantOyx__GSHRzlWOy', symObjAddr: 0xF084, symBinAddr: 0x326A0, symSize: 0xC }
  - { offsetInCU: 0x2F75, offset: 0x1D666, size: 0x8, addend: 0x0, symName: '_$sSD8IteratorV8_VariantOyxq___GSHRzr0_lWOe', symObjAddr: 0xF090, symBinAddr: 0x326AC, symSize: 0x8 }
  - { offsetInCU: 0x2F88, offset: 0x1D679, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVWOb', symObjAddr: 0xF098, symBinAddr: 0x326B4, symSize: 0x44 }
  - { offsetInCU: 0x2F9B, offset: 0x1D68C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVWOh', symObjAddr: 0xF0DC, symBinAddr: 0x326F8, symSize: 0x3C }
  - { offsetInCU: 0x2FB8, offset: 0x1D6A9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultCSgs5Error_pSgIeggg_AdFytIegnnr_TRTA', symObjAddr: 0xF1C4, symBinAddr: 0x32798, symSize: 0x28 }
  - { offsetInCU: 0x2FE0, offset: 0x1D6D1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultCSgs5Error_pSgytIegnnr_AdFIeggg_TRTA', symObjAddr: 0xF1EC, symBinAddr: 0x327C0, symSize: 0x8 }
  - { offsetInCU: 0x2FF3, offset: 0x1D6E4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultCSgs5Error_pSgIeggg_SgWOy', symObjAddr: 0xF1F4, symBinAddr: 0x327C8, symSize: 0x10 }
  - { offsetInCU: 0x3006, offset: 0x1D6F7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5logIn33_C218275A97333B874EDDFE627110566CLLyyFySb_s5Error_pSgtcfU_TA', symObjAddr: 0xF204, symBinAddr: 0x327D8, symSize: 0x8 }
  - { offsetInCU: 0x3023, offset: 0x1D714, size: 0x8, addend: 0x0, symName: '_$sSbs5Error_pSgytIegnnr_SbABIegyg_TRTA', symObjAddr: 0xF20C, symBinAddr: 0x327E0, symSize: 0x50 }
  - { offsetInCU: 0x3055, offset: 0x1D746, size: 0x8, addend: 0x0, symName: '_$sSbs5Error_pSgIegyg_SbABytIegnnr_TRTA', symObjAddr: 0xF25C, symBinAddr: 0x32830, symSize: 0x28 }
  - { offsetInCU: 0x30F2, offset: 0x1D7E3, size: 0x8, addend: 0x0, symName: '_$sSlsE3mapySayqd__Gqd__7ElementQzKXEKlFSay12FBSDKCoreKit10PermissionOG_SSTg5085$s13FBSDKLoginKit12LoginManagerC5logIn11permissions14viewController10completionySay09c4B010E91OG_So06UIViewI0CSgyAA0C6ResultOcSgtFSSAJcfu_32f5ef5a0b9ade21eb65ffea7b618f60adAJSSTf3nnpk_nTf1cn_n', symObjAddr: 0x11C, symBinAddr: 0x23A64, symSize: 0xFC }
  - { offsetInCU: 0x32C1, offset: 0x1D9B2, size: 0x8, addend: 0x0, symName: '_$sSlsE3mapySayqd__Gqd__7ElementQzKXEKlFShy13FBSDKLoginKit12FBPermissionCG_SSTg504$s13c132Kit12LoginManagerC15logInParameters13configuration12loggingToken20authenticationMethodSDyS2SGSgAA0C13ConfigurationCSg_SSSgSStFSSAA12E52Ccfu_32e0d58b938ad0b6cb17de1b825049cc00AOSSTf3nnpk_nTf1cn_n', symObjAddr: 0x218, symBinAddr: 0x23B60, symSize: 0x2AC }
  - { offsetInCU: 0x3EC1, offset: 0x1E5B2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCACycfC', symObjAddr: 0xFC, symBinAddr: 0x23A44, symSize: 0x20 }
  - { offsetInCU: 0x3F16, offset: 0x1E607, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5logIn11permissions14viewController10completionySay09FBSDKCoreB010PermissionOG_So06UIViewI0CSgyAA0C6ResultOcSgtF', symObjAddr: 0x234C, symBinAddr: 0x25C54, symSize: 0x94 }
  - { offsetInCU: 0x3F76, offset: 0x1E667, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5logIn11permissions14viewController10completionySay09FBSDKCoreB010PermissionOG_So06UIViewI0CSgyAA0C6ResultOcSgtFyAA0cdcN0CSg_s5Error_pSgtcfU_', symObjAddr: 0x23E0, symBinAddr: 0x25CE8, symSize: 0x7C }
  - { offsetInCU: 0x4028, offset: 0x1E719, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC15logInParameters13configuration12loggingToken20authenticationMethodSDyS2SGSgAA0C13ConfigurationCSg_SSSgSStF', symObjAddr: 0x5148, symBinAddr: 0x28A50, symSize: 0x1970 }
  - { offsetInCU: 0x4EB4, offset: 0x1F5A5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC15logInParameters13configuration12loggingToken20authenticationMethodSDyS2SGSgAA0C13ConfigurationCSg_SSSgSStFA2MXEfU_', symObjAddr: 0x6AB8, symBinAddr: 0x2A3C0, symSize: 0x1C }
  - { offsetInCU: 0x4F1C, offset: 0x1F60D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC15defaultAudienceAA07DefaultF0Ovg', symObjAddr: 0x508, symBinAddr: 0x23E50, symSize: 0x44 }
  - { offsetInCU: 0x4F5B, offset: 0x1F64C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC15defaultAudienceAA07DefaultF0Ovs', symObjAddr: 0x594, symBinAddr: 0x23EDC, symSize: 0x48 }
  - { offsetInCU: 0x4F81, offset: 0x1F672, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC15defaultAudienceAA07DefaultF0OvM', symObjAddr: 0x5DC, symBinAddr: 0x23F24, symSize: 0x44 }
  - { offsetInCU: 0x4F9E, offset: 0x1F68F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC15defaultAudienceAA07DefaultF0OvM.resume.0', symObjAddr: 0x620, symBinAddr: 0x23F68, symSize: 0x4 }
  - { offsetInCU: 0x4FBD, offset: 0x1F6AE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC7handlerAA010IdentifiedC13ResultHandlerVSgvg', symObjAddr: 0x624, symBinAddr: 0x23F6C, symSize: 0x58 }
  - { offsetInCU: 0x4FE0, offset: 0x1F6D1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC7handlerAA010IdentifiedC13ResultHandlerVSgvs', symObjAddr: 0x6BC, symBinAddr: 0x23FC4, symSize: 0x60 }
  - { offsetInCU: 0x5012, offset: 0x1F703, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC7handlerAA010IdentifiedC13ResultHandlerVSgvM', symObjAddr: 0x71C, symBinAddr: 0x24024, symSize: 0x44 }
  - { offsetInCU: 0x5047, offset: 0x1F738, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC13configurationAA0C13ConfigurationCSgvg', symObjAddr: 0x7A8, symBinAddr: 0x240B0, symSize: 0x50 }
  - { offsetInCU: 0x5098, offset: 0x1F789, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC20requestedPermissionsShyAA12FBPermissionCGSgvg', symObjAddr: 0x904, symBinAddr: 0x2420C, symSize: 0x48 }
  - { offsetInCU: 0x50D7, offset: 0x1F7C8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC20requestedPermissionsShyAA12FBPermissionCGSgvM', symObjAddr: 0x9FC, symBinAddr: 0x24304, symSize: 0x44 }
  - { offsetInCU: 0x50FA, offset: 0x1F7EB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC6loggerAA0cD6LoggerCSgvg', symObjAddr: 0xA40, symBinAddr: 0x24348, symSize: 0x48 }
  - { offsetInCU: 0x511D, offset: 0x1F80E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC6loggerAA0cD6LoggerCSgvM', symObjAddr: 0xAF4, symBinAddr: 0x243FC, symSize: 0x44 }
  - { offsetInCU: 0x5140, offset: 0x1F831, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5stateAA0cD5StateOvg', symObjAddr: 0xB38, symBinAddr: 0x24440, symSize: 0x44 }
  - { offsetInCU: 0x5163, offset: 0x1F854, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5stateAA0cD5StateOvs', symObjAddr: 0xB7C, symBinAddr: 0x24484, symSize: 0x48 }
  - { offsetInCU: 0x5195, offset: 0x1F886, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5stateAA0cD5StateOvM', symObjAddr: 0xBC4, symBinAddr: 0x244CC, symSize: 0x44 }
  - { offsetInCU: 0x51CA, offset: 0x1F8BB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC17usedSafariSessionSbvg', symObjAddr: 0xC4C, symBinAddr: 0x24554, symSize: 0x44 }
  - { offsetInCU: 0x5209, offset: 0x1F8FA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC17usedSafariSessionSbvs', symObjAddr: 0xCD8, symBinAddr: 0x245E0, symSize: 0x48 }
  - { offsetInCU: 0x522C, offset: 0x1F91D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC17usedSafariSessionSbvM', symObjAddr: 0xD20, symBinAddr: 0x24628, symSize: 0x44 }
  - { offsetInCU: 0x5261, offset: 0x1F952, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC012isPerformingC0Sbvg', symObjAddr: 0xDB0, symBinAddr: 0x246B8, symSize: 0x4C }
  - { offsetInCU: 0x52A6, offset: 0x1F997, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC22configuredDependenciesAC06ObjectF0VSgvg', symObjAddr: 0xDFC, symBinAddr: 0x24704, symSize: 0x58 }
  - { offsetInCU: 0x52C3, offset: 0x1F9B4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC22configuredDependenciesAC06ObjectF0VSgvs', symObjAddr: 0xE54, symBinAddr: 0x2475C, symSize: 0x60 }
  - { offsetInCU: 0x52E9, offset: 0x1F9DA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC22configuredDependenciesAC06ObjectF0VSgvM', symObjAddr: 0xEB4, symBinAddr: 0x247BC, symSize: 0x44 }
  - { offsetInCU: 0x5306, offset: 0x1F9F7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC19defaultDependenciesAC06ObjectF0VSgvg', symObjAddr: 0xEF8, symBinAddr: 0x24800, symSize: 0xE0 }
  - { offsetInCU: 0x5328, offset: 0x1FA19, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC19defaultDependenciesAC06ObjectF0VSgvgAGyXEfU_', symObjAddr: 0x10D4, symBinAddr: 0x249DC, symSize: 0x27C }
  - { offsetInCU: 0x547F, offset: 0x1FB70, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC19defaultDependenciesAC06ObjectF0VSgvs', symObjAddr: 0x1068, symBinAddr: 0x24970, symSize: 0x6C }
  - { offsetInCU: 0x551D, offset: 0x1FC0E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV17accessTokenWallet014authenticationhI012errorFactory012graphRequestL015internalUtility13keychainStore014loginCompleterL015profileProvider8settings9urlOpenerAESo011FBSDKAccessH9Providing_pXp_So019FBSDKAuthenticationH9Providing_pXpSo18FBSDKErrorCreating_pSo010FBSDKGraphnL0_pSo27FBSDKAppAvailabilityChecker_So26FBSDKAppURLSchemeProvidingSo15FBSDKURLHostingpSo013FBSDKKeychainR0_pAA0ctL8Protocol_p09FBSDKCoreB016ProfileProviding_pXpAY16SettingsProtocol_pSo14FBSDKURLOpener_ptcfC', symObjAddr: 0x1350, symBinAddr: 0x24C58, symSize: 0x4C }
  - { offsetInCU: 0x5530, offset: 0x1FC21, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC19defaultDependenciesAC06ObjectF0VSgvM', symObjAddr: 0x139C, symBinAddr: 0x24CA4, symSize: 0x4C }
  - { offsetInCU: 0x5553, offset: 0x1FC44, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC19defaultDependenciesAC06ObjectF0VSgvM.resume.0', symObjAddr: 0x13E8, symBinAddr: 0x24CF0, symSize: 0x10C }
  - { offsetInCU: 0x55CA, offset: 0x1FCBB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC15defaultAudienceAcA07DefaultF0O_tcfC', symObjAddr: 0x14F4, symBinAddr: 0x24DFC, symSize: 0x6C }
  - { offsetInCU: 0x560C, offset: 0x1FCFD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC15defaultAudienceAcA07DefaultF0O_tcfc', symObjAddr: 0x1560, symBinAddr: 0x24E68, symSize: 0x68 }
  - { offsetInCU: 0x569B, offset: 0x1FD8C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5logIn4from13configuration10completionySo16UIViewControllerCSg_AA0C13ConfigurationCSgyAA0cdC6ResultCSg_s5Error_pSgtctF', symObjAddr: 0x162C, symBinAddr: 0x24F34, symSize: 0x124 }
  - { offsetInCU: 0x5741, offset: 0x1FE32, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC11commonLogIn33_C218275A97333B874EDDFE627110566CLL4from13configuration10completionySo16UIViewControllerCSg_AA0C13ConfigurationCSgyAA0cdC6ResultCSg_s5Error_pSgtcSgtF', symObjAddr: 0x1750, symBinAddr: 0x25058, symSize: 0x320 }
  - { offsetInCU: 0x586E, offset: 0x1FF5F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5logIn14viewController13configuration10completionySo06UIViewH0CSg_AA0C13ConfigurationCSgyAA0C6ResultOctF', symObjAddr: 0x1B80, symBinAddr: 0x25488, symSize: 0x78 }
  - { offsetInCU: 0x58BC, offset: 0x1FFAD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5logIn14viewController13configuration10completionySo06UIViewH0CSg_AA0C13ConfigurationCSgyAA0C6ResultOctFyAA0cdcM0CSg_s5Error_pSgtcfU_', symObjAddr: 0x1BF8, symBinAddr: 0x25500, symSize: 0x74 }
  - { offsetInCU: 0x59AC, offset: 0x2009D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC13invokeHandler33_C218275A97333B874EDDFE627110566CLL6result5erroryAA0cdC6ResultCSg_s5Error_pSgtF', symObjAddr: 0x1CCC, symBinAddr: 0x255D4, symSize: 0x3AC }
  - { offsetInCU: 0x5AC8, offset: 0x201B9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5logIn11permissions4from7handlerySaySSG_So16UIViewControllerCSgyAA0cdC6ResultCSg_s5Error_pSgtcSgtF', symObjAddr: 0x2078, symBinAddr: 0x25980, symSize: 0x200 }
  - { offsetInCU: 0x5C92, offset: 0x20383, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC27handleImplicitCancelOfLogIn33_C218275A97333B874EDDFE627110566CLLyyF', symObjAddr: 0x2648, symBinAddr: 0x25F50, symSize: 0x16C }
  - { offsetInCU: 0x5DCC, offset: 0x204BD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC19performBrowserLogIn33_C218275A97333B874EDDFE627110566CLL7handleryySb_s5Error_pSgtcSg_tF', symObjAddr: 0x27B4, symBinAddr: 0x260BC, symSize: 0xD94 }
  - { offsetInCU: 0x60FD, offset: 0x207EE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC19performBrowserLogIn33_C218275A97333B874EDDFE627110566CLL7handleryySb_s5Error_pSgtcSg_tFySb_AHtcyKXEfu1_ySb_AHtcfU_', symObjAddr: 0x72EC, symBinAddr: 0x2ABF4, symSize: 0x4 }
  - { offsetInCU: 0x6118, offset: 0x20809, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC19performBrowserLogIn33_C218275A97333B874EDDFE627110566CLL7handleryySb_s5Error_pSgtcSg_tFySb_AHtcyKXEfu2_ySb_AHtcfU0_', symObjAddr: 0x72F0, symBinAddr: 0x2ABF8, symSize: 0x4 }
  - { offsetInCU: 0x6172, offset: 0x20863, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC21reauthorizeDataAccess4from7handlerySo16UIViewControllerC_yAA0cdC6ResultCSg_s5Error_pSgtctF', symObjAddr: 0x3548, symBinAddr: 0x26E50, symSize: 0x448 }
  - { offsetInCU: 0x6354, offset: 0x20A45, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC6logOutyyF', symObjAddr: 0x3A04, symBinAddr: 0x2730C, symSize: 0x1C4 }
  - { offsetInCU: 0x64B0, offset: 0x20BA1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC22completeAuthentication10parameters15expectChallengeyAA01_C20CompletionParametersC_SbtF', symObjAddr: 0x3BF0, symBinAddr: 0x274F8, symSize: 0x538 }
  - { offsetInCU: 0x661C, offset: 0x20D0D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC22storeExpectedChallenge33_C218275A97333B874EDDFE627110566CLLyySSSgF', symObjAddr: 0x4128, symBinAddr: 0x27A30, symSize: 0xFC }
  - { offsetInCU: 0x666C, offset: 0x20D5D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC16getSuccessResult33_C218275A97333B874EDDFE627110566CLL4fromAA0cdcG0CAA01_C20CompletionParametersC_tF', symObjAddr: 0x4224, symBinAddr: 0x27B2C, symSize: 0x770 }
  - { offsetInCU: 0x6CE6, offset: 0x213D7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC24validateReauthentication11accessToken11loginResult04userH5NonceySo011FBSDKAccessH0C_AA0cdcJ0CSgSSSgtF', symObjAddr: 0x4994, symBinAddr: 0x2829C, symSize: 0x3A8 }
  - { offsetInCU: 0x6E6C, offset: 0x2155D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC24validateReauthentication11accessToken11loginResult04userH5NonceySo011FBSDKAccessH0C_AA0cdcJ0CSgSSSgtFySo27FBSDKGraphRequestConnecting_pSg_ypSgs5Error_pSgtcfU_', symObjAddr: 0x6F0C, symBinAddr: 0x2A814, symSize: 0x334 }
  - { offsetInCU: 0x6F7A, offset: 0x2166B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18getCancelledResult33_C218275A97333B874EDDFE627110566CLL4fromAA0cdcG0CAA01_C20CompletionParametersC_tF', symObjAddr: 0x4D3C, symBinAddr: 0x28644, symSize: 0x1C0 }
  - { offsetInCU: 0x70C6, offset: 0x217B7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC19setGlobalProperties33_C218275A97333B874EDDFE627110566CLL10parameters11loginResultyAA01_C20CompletionParametersC_AA0cdcN0CSgtF', symObjAddr: 0x4EFC, symBinAddr: 0x28804, symSize: 0x1F4 }
  - { offsetInCU: 0x730A, offset: 0x219FB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC010addLimitedC14ShimParameters33_C218275A97333B874EDDFE627110566CLL10parameters13configurationySDyS2SGz_AA0C13ConfigurationCtF', symObjAddr: 0x6AD4, symBinAddr: 0x2A3DC, symSize: 0x138 }
  - { offsetInCU: 0x73EE, offset: 0x21ADF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC25storeExpectedCodeVerifier33_C218275A97333B874EDDFE627110566CLLyyAA0gH0CSgF', symObjAddr: 0x6E0C, symBinAddr: 0x2A714, symSize: 0x100 }
  - { offsetInCU: 0x743A, offset: 0x21B2B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC29getRecentlyGrantedPermissions4fromShyAA12FBPermissionCGAH_tF', symObjAddr: 0x7358, symBinAddr: 0x2AC60, symSize: 0x148 }
  - { offsetInCU: 0x750C, offset: 0x21BFD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC30getRecentlyDeclinedPermissions4fromShyAA12FBPermissionCGAH_tF', symObjAddr: 0x74A0, symBinAddr: 0x2ADA8, symSize: 0xC4 }
  - { offsetInCU: 0x75A5, offset: 0x21C96, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCACycfc', symObjAddr: 0x7764, symBinAddr: 0x2B06C, symSize: 0x124 }
  - { offsetInCU: 0x75C8, offset: 0x21CB9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCfD', symObjAddr: 0x78A8, symBinAddr: 0x2B1B0, symSize: 0x34 }
  - { offsetInCU: 0x75EF, offset: 0x21CE0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC10makeOpenerACyFZ', symObjAddr: 0x7980, symBinAddr: 0x2B288, symSize: 0x20 }
  - { offsetInCU: 0x763C, offset: 0x21D2D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC11application_4open17sourceApplication10annotationSbSo13UIApplicationCSg_10Foundation3URLVSgSSSgypSgtF', symObjAddr: 0x79C4, symBinAddr: 0x2B2CC, symSize: 0xC }
  - { offsetInCU: 0x7679, offset: 0x21D6A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC7canOpen_3for17sourceApplication10annotationSb10Foundation3URLV_So13UIApplicationCSgSSSgypSgtF', symObjAddr: 0x79D0, symBinAddr: 0x2B2D8, symSize: 0x8 }
  - { offsetInCU: 0x7699, offset: 0x21D8A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC26applicationDidBecomeActiveyySo13UIApplicationCF', symObjAddr: 0x7C64, symBinAddr: 0x2B56C, symSize: 0x50 }
  - { offsetInCU: 0x7701, offset: 0x21DF2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC19isAuthenticationURLySb10Foundation0G0VF', symObjAddr: 0x7D3C, symBinAddr: 0x2B644, symSize: 0x60 }
  - { offsetInCU: 0x774B, offset: 0x21E3C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC21shouldStopPropagation2ofSb10Foundation3URLV_tF', symObjAddr: 0x7E70, symBinAddr: 0x2B778, symSize: 0x194 }
  - { offsetInCU: 0x78A8, offset: 0x21F99, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV17accessTokenWalletSo011FBSDKAccessH9Providing_pXpvg', symObjAddr: 0x8238, symBinAddr: 0x2BB40, symSize: 0x8 }
  - { offsetInCU: 0x78BB, offset: 0x21FAC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV17accessTokenWalletSo011FBSDKAccessH9Providing_pXpvs', symObjAddr: 0x8240, symBinAddr: 0x2BB48, symSize: 0x8 }
  - { offsetInCU: 0x78CE, offset: 0x21FBF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV17accessTokenWalletSo011FBSDKAccessH9Providing_pXpvM', symObjAddr: 0x8248, symBinAddr: 0x2BB50, symSize: 0x10 }
  - { offsetInCU: 0x78E1, offset: 0x21FD2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV17accessTokenWalletSo011FBSDKAccessH9Providing_pXpvM.resume.0', symObjAddr: 0x8258, symBinAddr: 0x2BB60, symSize: 0x4 }
  - { offsetInCU: 0x78F4, offset: 0x21FE5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV25authenticationTokenWalletSo019FBSDKAuthenticationH9Providing_pXpvg', symObjAddr: 0x825C, symBinAddr: 0x2BB64, symSize: 0x8 }
  - { offsetInCU: 0x7907, offset: 0x21FF8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV25authenticationTokenWalletSo019FBSDKAuthenticationH9Providing_pXpvs', symObjAddr: 0x8264, symBinAddr: 0x2BB6C, symSize: 0x8 }
  - { offsetInCU: 0x791A, offset: 0x2200B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV25authenticationTokenWalletSo019FBSDKAuthenticationH9Providing_pXpvM', symObjAddr: 0x826C, symBinAddr: 0x2BB74, symSize: 0x10 }
  - { offsetInCU: 0x792D, offset: 0x2201E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV25authenticationTokenWalletSo019FBSDKAuthenticationH9Providing_pXpvM.resume.0', symObjAddr: 0x827C, symBinAddr: 0x2BB84, symSize: 0x4 }
  - { offsetInCU: 0x7940, offset: 0x22031, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV12errorFactorySo18FBSDKErrorCreating_pvg', symObjAddr: 0x8280, symBinAddr: 0x2BB88, symSize: 0x8 }
  - { offsetInCU: 0x7953, offset: 0x22044, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV12errorFactorySo18FBSDKErrorCreating_pvs', symObjAddr: 0x8288, symBinAddr: 0x2BB90, symSize: 0x28 }
  - { offsetInCU: 0x7966, offset: 0x22057, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV12errorFactorySo18FBSDKErrorCreating_pvM', symObjAddr: 0x82B0, symBinAddr: 0x2BBB8, symSize: 0x10 }
  - { offsetInCU: 0x7979, offset: 0x2206A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV12errorFactorySo18FBSDKErrorCreating_pvM.resume.0', symObjAddr: 0x82C0, symBinAddr: 0x2BBC8, symSize: 0x4 }
  - { offsetInCU: 0x798C, offset: 0x2207D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV19graphRequestFactorySo010FBSDKGraphhI0_pvg', symObjAddr: 0x82C4, symBinAddr: 0x2BBCC, symSize: 0x8 }
  - { offsetInCU: 0x799F, offset: 0x22090, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV19graphRequestFactorySo010FBSDKGraphhI0_pvs', symObjAddr: 0x82CC, symBinAddr: 0x2BBD4, symSize: 0x28 }
  - { offsetInCU: 0x79B2, offset: 0x220A3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV19graphRequestFactorySo010FBSDKGraphhI0_pvM', symObjAddr: 0x82F4, symBinAddr: 0x2BBFC, symSize: 0x10 }
  - { offsetInCU: 0x79C5, offset: 0x220B6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV19graphRequestFactorySo010FBSDKGraphhI0_pvM.resume.0', symObjAddr: 0x8304, symBinAddr: 0x2BC0C, symSize: 0x4 }
  - { offsetInCU: 0x79D8, offset: 0x220C9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV15internalUtilitySo27FBSDKAppAvailabilityChecker_So0I18URLSchemeProvidingSo15FBSDKURLHostingpvg', symObjAddr: 0x8308, symBinAddr: 0x2BC10, symSize: 0x8 }
  - { offsetInCU: 0x79EB, offset: 0x220DC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV15internalUtilitySo27FBSDKAppAvailabilityChecker_So0I18URLSchemeProvidingSo15FBSDKURLHostingpvs', symObjAddr: 0x8310, symBinAddr: 0x2BC18, symSize: 0x28 }
  - { offsetInCU: 0x79FE, offset: 0x220EF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV15internalUtilitySo27FBSDKAppAvailabilityChecker_So0I18URLSchemeProvidingSo15FBSDKURLHostingpvM', symObjAddr: 0x8338, symBinAddr: 0x2BC40, symSize: 0x10 }
  - { offsetInCU: 0x7A11, offset: 0x22102, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV15internalUtilitySo27FBSDKAppAvailabilityChecker_So0I18URLSchemeProvidingSo15FBSDKURLHostingpvM.resume.0', symObjAddr: 0x8348, symBinAddr: 0x2BC50, symSize: 0x4 }
  - { offsetInCU: 0x7A24, offset: 0x22115, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV13keychainStoreSo013FBSDKKeychainH0_pvg', symObjAddr: 0x834C, symBinAddr: 0x2BC54, symSize: 0x8 }
  - { offsetInCU: 0x7A37, offset: 0x22128, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV13keychainStoreSo013FBSDKKeychainH0_pvs', symObjAddr: 0x8354, symBinAddr: 0x2BC5C, symSize: 0x28 }
  - { offsetInCU: 0x7A4A, offset: 0x2213B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV13keychainStoreSo013FBSDKKeychainH0_pvM', symObjAddr: 0x837C, symBinAddr: 0x2BC84, symSize: 0x10 }
  - { offsetInCU: 0x7A5D, offset: 0x2214E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV13keychainStoreSo013FBSDKKeychainH0_pvM.resume.0', symObjAddr: 0x838C, symBinAddr: 0x2BC94, symSize: 0x4 }
  - { offsetInCU: 0x7A70, offset: 0x22161, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV21loginCompleterFactoryAA0chI8Protocol_pvg', symObjAddr: 0x8390, symBinAddr: 0x2BC98, symSize: 0xC }
  - { offsetInCU: 0x7A83, offset: 0x22174, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV21loginCompleterFactoryAA0chI8Protocol_pvs', symObjAddr: 0x839C, symBinAddr: 0x2BCA4, symSize: 0x30 }
  - { offsetInCU: 0x7A96, offset: 0x22187, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV21loginCompleterFactoryAA0chI8Protocol_pvM', symObjAddr: 0x83CC, symBinAddr: 0x2BCD4, symSize: 0x10 }
  - { offsetInCU: 0x7AA9, offset: 0x2219A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV21loginCompleterFactoryAA0chI8Protocol_pvM.resume.0', symObjAddr: 0x83DC, symBinAddr: 0x2BCE4, symSize: 0x4 }
  - { offsetInCU: 0x7ABC, offset: 0x221AD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV15profileProvider09FBSDKCoreB016ProfileProviding_pXpvg', symObjAddr: 0x83E0, symBinAddr: 0x2BCE8, symSize: 0x8 }
  - { offsetInCU: 0x7ACF, offset: 0x221C0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV15profileProvider09FBSDKCoreB016ProfileProviding_pXpvs', symObjAddr: 0x83E8, symBinAddr: 0x2BCF0, symSize: 0x8 }
  - { offsetInCU: 0x7AE2, offset: 0x221D3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV15profileProvider09FBSDKCoreB016ProfileProviding_pXpvM', symObjAddr: 0x83F0, symBinAddr: 0x2BCF8, symSize: 0x10 }
  - { offsetInCU: 0x7AF5, offset: 0x221E6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV15profileProvider09FBSDKCoreB016ProfileProviding_pXpvM.resume.0', symObjAddr: 0x8400, symBinAddr: 0x2BD08, symSize: 0x4 }
  - { offsetInCU: 0x7B08, offset: 0x221F9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV8settings09FBSDKCoreB016SettingsProtocol_pvg', symObjAddr: 0x8404, symBinAddr: 0x2BD0C, symSize: 0x8 }
  - { offsetInCU: 0x7B1B, offset: 0x2220C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV8settings09FBSDKCoreB016SettingsProtocol_pvs', symObjAddr: 0x840C, symBinAddr: 0x2BD14, symSize: 0x28 }
  - { offsetInCU: 0x7B2E, offset: 0x2221F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV8settings09FBSDKCoreB016SettingsProtocol_pvM', symObjAddr: 0x8434, symBinAddr: 0x2BD3C, symSize: 0x10 }
  - { offsetInCU: 0x7B41, offset: 0x22232, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV8settings09FBSDKCoreB016SettingsProtocol_pvM.resume.0', symObjAddr: 0x8444, symBinAddr: 0x2BD4C, symSize: 0x4 }
  - { offsetInCU: 0x7B54, offset: 0x22245, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV9urlOpenerSo14FBSDKURLOpener_pvg', symObjAddr: 0x8448, symBinAddr: 0x2BD50, symSize: 0x8 }
  - { offsetInCU: 0x7B67, offset: 0x22258, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV9urlOpenerSo14FBSDKURLOpener_pvs', symObjAddr: 0x8450, symBinAddr: 0x2BD58, symSize: 0x28 }
  - { offsetInCU: 0x7B7A, offset: 0x2226B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV9urlOpenerSo14FBSDKURLOpener_pvM', symObjAddr: 0x8478, symBinAddr: 0x2BD80, symSize: 0x10 }
  - { offsetInCU: 0x7B8D, offset: 0x2227E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV9urlOpenerSo14FBSDKURLOpener_pvM.resume.0', symObjAddr: 0x8488, symBinAddr: 0x2BD90, symSize: 0x4 }
  - { offsetInCU: 0x7BCF, offset: 0x222C0, size: 0x8, addend: 0x0, symName: '_$sSo16FBSDKAccessTokenC11tokenString11permissions19declinedPermissions07expiredG05appID04userJ014expirationDate07refreshM0020dataAccessExpirationM0ABSS_SaySSGA2LS2S10Foundation0M0VSgA2PtcfcTO', symObjAddr: 0x86B8, symBinAddr: 0x2BF7C, symSize: 0x248 }
  - { offsetInCU: 0x7BEE, offset: 0x222DF, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV7_insert2at3key5valueys10_HashTableV6BucketV_xnq_ntFSS_ypTg5', symObjAddr: 0x8900, symBinAddr: 0x2C1C4, symSize: 0x6C }
  - { offsetInCU: 0x7C73, offset: 0x22364, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV7_insert2at3key5valueys10_HashTableV6BucketV_xnq_ntFSo26FBSDKAppEventParameterNamea_ypTg5', symObjAddr: 0x896C, symBinAddr: 0x2C230, symSize: 0x68 }
  - { offsetInCU: 0x7CE6, offset: 0x223D7, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV7_insert2at3key5valueys10_HashTableV6BucketV_xnq_ntFSS_SSTg5', symObjAddr: 0x89D4, symBinAddr: 0x2C298, symSize: 0x54 }
  - { offsetInCU: 0x7D77, offset: 0x22468, size: 0x8, addend: 0x0, symName: '_$sSh8_VariantV6insertySb8inserted_x17memberAfterInserttxnF13FBSDKLoginKit12FBPermissionC_Tg5', symObjAddr: 0x8D20, symBinAddr: 0x2C5E4, symSize: 0x2B4 }
  - { offsetInCU: 0x7E49, offset: 0x2253A, size: 0x8, addend: 0x0, symName: '_$sSh8_VariantV6insertySb8inserted_x17memberAfterInserttxnFSS_Tg5', symObjAddr: 0x8FD4, symBinAddr: 0x2C898, symSize: 0x1AC }
  - { offsetInCU: 0x7F2C, offset: 0x2261D, size: 0x8, addend: 0x0, symName: '_$sSh8_VariantV6insertySb8inserted_x17memberAfterInserttxnF12FBSDKCoreKit10PermissionO_Tg5', symObjAddr: 0x9180, symBinAddr: 0x2CA44, symSize: 0x244 }
  - { offsetInCU: 0x7FAA, offset: 0x2269B, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV9insertNew_2at8isUniqueyxn_s10_HashTableV6BucketVSbtF13FBSDKLoginKit12FBPermissionC_Tg5', symObjAddr: 0x95BC, symBinAddr: 0x2CE80, symSize: 0x19C }
  - { offsetInCU: 0x8032, offset: 0x22723, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV9insertNew_2at8isUniqueyxn_s10_HashTableV6BucketVSbtFSS_Tg5', symObjAddr: 0x9758, symBinAddr: 0x2D01C, symSize: 0x1B4 }
  - { offsetInCU: 0x811B, offset: 0x2280C, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV9insertNew_2at8isUniqueyxn_s10_HashTableV6BucketVSbtF12FBSDKCoreKit10PermissionO_Tg5', symObjAddr: 0x990C, symBinAddr: 0x2D1D0, symSize: 0x22C }
  - { offsetInCU: 0x818D, offset: 0x2287E, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV4copyyyF13FBSDKLoginKit12FBPermissionC_Tg5', symObjAddr: 0x9B38, symBinAddr: 0x2D3FC, symSize: 0x1A4 }
  - { offsetInCU: 0x81F8, offset: 0x228E9, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV4copyyyFSS_Tg5', symObjAddr: 0x9CDC, symBinAddr: 0x2D5A0, symSize: 0x1AC }
  - { offsetInCU: 0x8263, offset: 0x22954, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV4copyyyF12FBSDKCoreKit10PermissionO_Tg5', symObjAddr: 0x9E88, symBinAddr: 0x2D74C, symSize: 0x210 }
  - { offsetInCU: 0x82CE, offset: 0x229BF, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV13copyAndResize8capacityySi_tF13FBSDKLoginKit12FBPermissionC_Tg5', symObjAddr: 0xA098, symBinAddr: 0x2D95C, symSize: 0x268 }
  - { offsetInCU: 0x836A, offset: 0x22A5B, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV13copyAndResize8capacityySi_tFSS_Tg5', symObjAddr: 0xA300, symBinAddr: 0x2DBC4, symSize: 0x29C }
  - { offsetInCU: 0x840A, offset: 0x22AFB, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV13copyAndResize8capacityySi_tF12FBSDKCoreKit10PermissionO_Tg5', symObjAddr: 0xA59C, symBinAddr: 0x2DE60, symSize: 0x308 }
  - { offsetInCU: 0x8499, offset: 0x22B8A, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV6resize8capacityySi_tF13FBSDKLoginKit12FBPermissionC_Tg5', symObjAddr: 0xA8A4, symBinAddr: 0x2E168, symSize: 0x288 }
  - { offsetInCU: 0x855B, offset: 0x22C4C, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV6resize8capacityySi_tFSS_Tg5', symObjAddr: 0xAB2C, symBinAddr: 0x2E3F0, symSize: 0x2C8 }
  - { offsetInCU: 0x8631, offset: 0x22D22, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV6resize8capacityySi_tF12FBSDKCoreKit10PermissionO_Tg5', symObjAddr: 0xADF4, symBinAddr: 0x2E6B8, symSize: 0x348 }
  - { offsetInCU: 0x86DA, offset: 0x22DCB, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV16_unsafeInsertNewyyxnF13FBSDKLoginKit12FBPermissionC_Tg5', symObjAddr: 0xB13C, symBinAddr: 0x2EA00, symSize: 0x80 }
  - { offsetInCU: 0x8733, offset: 0x22E24, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV12mutatingFind_8isUniques10_HashTableV6BucketV6bucket_Sb5foundtx_SbtFSo26FBSDKAppEventParameterNamea_ypTg5', symObjAddr: 0xB1BC, symBinAddr: 0x2EA80, symSize: 0xC8 }
  - { offsetInCU: 0x877F, offset: 0x22E70, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV20_copyOrMoveAndResize8capacity12moveElementsySi_SbtFSo26FBSDKAppEventParameterNamea_ypTg5', symObjAddr: 0xB394, symBinAddr: 0x2EC58, symSize: 0x368 }
  - { offsetInCU: 0x8868, offset: 0x22F59, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV20_copyOrMoveAndResize8capacity12moveElementsySi_SbtFSS_ypTg5', symObjAddr: 0xB6FC, symBinAddr: 0x2EFC0, symSize: 0x340 }
  - { offsetInCU: 0x8951, offset: 0x23042, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV20_copyOrMoveAndResize8capacity12moveElementsySi_SbtFSS_SSTg5', symObjAddr: 0xBA3C, symBinAddr: 0x2F300, symSize: 0x354 }
  - { offsetInCU: 0x8A2D, offset: 0x2311E, size: 0x8, addend: 0x0, symName: '_$sSh8_VariantV12intersectionys10_NativeSetVyxGShyxGF13FBSDKLoginKit12FBPermissionC_Tg5', symObjAddr: 0xBD90, symBinAddr: 0x2F654, symSize: 0x394 }
  - { offsetInCU: 0x8B8F, offset: 0x23280, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV13extractSubset5using5countAByxGs13_UnsafeBitsetV_SitF13FBSDKLoginKit12FBPermissionC_Tg5', symObjAddr: 0xC9FC, symBinAddr: 0x302C0, symSize: 0x220 }
  - { offsetInCU: 0x8C38, offset: 0x23329, size: 0x8, addend: 0x0, symName: '_$sShyxSh5IndexVyx_Gcig13FBSDKLoginKit12FBPermissionC_Tg5', symObjAddr: 0xCC1C, symBinAddr: 0x304E0, symSize: 0x2DC }
  - { offsetInCU: 0x8CCA, offset: 0x233BB, size: 0x8, addend: 0x0, symName: '_$sSTsE21_copySequenceContents12initializing8IteratorQz_SitSry7ElementQzG_tFShySSG_Tg5', symObjAddr: 0xCEF8, symBinAddr: 0x307BC, symSize: 0x1B0 }
  - { offsetInCU: 0x8DEE, offset: 0x234DF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5logIn33_C218275A97333B874EDDFE627110566CLL11permissions7handleryShyAA12FBPermissionCG_yAA0cdC6ResultCSg_s5Error_pSgtcSgtFTf4dnn_n', symObjAddr: 0xD57C, symBinAddr: 0x30D6C, symSize: 0x21C }
  - { offsetInCU: 0x8EF6, offset: 0x235E7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC7canOpen_3for17sourceApplication10annotationSb10Foundation3URLV_So13UIApplicationCSgSSSgypSgtFTf4nddnn_n', symObjAddr: 0xE200, symBinAddr: 0x319F0, symSize: 0x1A4 }
  - { offsetInCU: 0x9045, offset: 0x23736, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC11application_4open17sourceApplication10annotationSbSo13UIApplicationCSg_10Foundation3URLVSgSSSgypSgtFTf4dndnn_n', symObjAddr: 0xE3A4, symBinAddr: 0x31B94, symSize: 0x4F8 }
  - { offsetInCU: 0x4E, offset: 0x23A64, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO10identifierSo013FBSDKAppEventF4NameavpZ', symObjAddr: 0x5EC0, symBinAddr: 0x5F260, symSize: 0x0 }
  - { offsetInCU: 0x68, offset: 0x23A7E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO9timestampSo013FBSDKAppEventF4NameavpZ', symObjAddr: 0x5EC8, symBinAddr: 0x5F268, symSize: 0x0 }
  - { offsetInCU: 0x82, offset: 0x23A98, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO6resultSo013FBSDKAppEventF4NameavpZ', symObjAddr: 0x5ED0, symBinAddr: 0x5F270, symSize: 0x0 }
  - { offsetInCU: 0x9C, offset: 0x23AB2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO10authMethodSo013FBSDKAppEventF4NameavpZ', symObjAddr: 0x5ED8, symBinAddr: 0x5F278, symSize: 0x0 }
  - { offsetInCU: 0xB6, offset: 0x23ACC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO9errorCodeSo013FBSDKAppEventF4NameavpZ', symObjAddr: 0x5EE0, symBinAddr: 0x5F280, symSize: 0x0 }
  - { offsetInCU: 0xD0, offset: 0x23AE6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO12errorMessageSo013FBSDKAppEventF4NameavpZ', symObjAddr: 0x5EE8, symBinAddr: 0x5F288, symSize: 0x0 }
  - { offsetInCU: 0xEA, offset: 0x23B00, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO6extrasSo013FBSDKAppEventF4NameavpZ', symObjAddr: 0x5EF0, symBinAddr: 0x5F290, symSize: 0x0 }
  - { offsetInCU: 0x104, offset: 0x23B1A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO12loggingTokenSo013FBSDKAppEventF4NameavpZ', symObjAddr: 0x5EF8, symBinAddr: 0x5F298, symSize: 0x0 }
  - { offsetInCU: 0x11E, offset: 0x23B34, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO19declinedPermissionsSo013FBSDKAppEventF4NameavpZ', symObjAddr: 0x5F00, symBinAddr: 0x5F2A0, symSize: 0x0 }
  - { offsetInCU: 0x139, offset: 0x23B4F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC19defaultDependenciesAC04TypeG0VSgvpZ', symObjAddr: 0x186A0, symBinAddr: 0x611E0, symSize: 0x0 }
  - { offsetInCU: 0x1C8, offset: 0x23BDE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC22configuredDependenciesAC04TypeG0VSgvpZ', symObjAddr: 0x186C8, symBinAddr: 0x61208, symSize: 0x0 }
  - { offsetInCU: 0x4A2, offset: 0x23EB8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO10identifier_WZ', symObjAddr: 0x120C, symBinAddr: 0x33B50, symSize: 0x34 }
  - { offsetInCU: 0x4BC, offset: 0x23ED2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO9timestamp_WZ', symObjAddr: 0x1240, symBinAddr: 0x33B84, symSize: 0x3C }
  - { offsetInCU: 0x4D6, offset: 0x23EEC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO6result_WZ', symObjAddr: 0x127C, symBinAddr: 0x33BC0, symSize: 0x30 }
  - { offsetInCU: 0x4F0, offset: 0x23F06, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO10authMethod_WZ', symObjAddr: 0x12AC, symBinAddr: 0x33BF0, symSize: 0x30 }
  - { offsetInCU: 0x50A, offset: 0x23F20, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO9errorCode_WZ', symObjAddr: 0x12DC, symBinAddr: 0x33C20, symSize: 0x38 }
  - { offsetInCU: 0x524, offset: 0x23F3A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO12errorMessage_WZ', symObjAddr: 0x1314, symBinAddr: 0x33C58, symSize: 0x3C }
  - { offsetInCU: 0x53E, offset: 0x23F54, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO6extras_WZ', symObjAddr: 0x1350, symBinAddr: 0x33C94, symSize: 0x30 }
  - { offsetInCU: 0x558, offset: 0x23F6E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO12loggingToken_WZ', symObjAddr: 0x1380, symBinAddr: 0x33CC4, symSize: 0x3C }
  - { offsetInCU: 0x572, offset: 0x23F88, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO19declinedPermissions_WZ', symObjAddr: 0x13BC, symBinAddr: 0x33D00, symSize: 0x34 }
  - { offsetInCU: 0x6DC, offset: 0x240F2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC21heartbeatTimerDidFireyyFTo', symObjAddr: 0x3AF8, symBinAddr: 0x3643C, symSize: 0xDC }
  - { offsetInCU: 0x744, offset: 0x2415A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC14getClientState20authenticationMethod08existingH06loggerSSSgAH_SDySSypGSgACSgtFZTf4nnnd_n', symObjAddr: 0x5380, symBinAddr: 0x37C90, symSize: 0x390 }
  - { offsetInCU: 0xC65, offset: 0x2467B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC19defaultDependencies_WZ', symObjAddr: 0x4024, symBinAddr: 0x36968, symSize: 0x6C }
  - { offsetInCU: 0xC80, offset: 0x24696, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC19defaultDependenciesAC04TypeG0VSgvau', symObjAddr: 0x4090, symBinAddr: 0x369D4, symSize: 0x40 }
  - { offsetInCU: 0xCB8, offset: 0x246CE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC22configuredDependencies_WZ', symObjAddr: 0x4174, symBinAddr: 0x36AB8, symSize: 0x18 }
  - { offsetInCU: 0xCD3, offset: 0x246E9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC22configuredDependenciesAC04TypeG0VSgvau', symObjAddr: 0x418C, symBinAddr: 0x36AD0, symSize: 0x40 }
  - { offsetInCU: 0xD23, offset: 0x24739, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerCAA15DependentAsTypeA2aDP22configuredDependencies0hJ0QzSgvsZTW', symObjAddr: 0x4370, symBinAddr: 0x36CB4, symSize: 0x8C }
  - { offsetInCU: 0xD59, offset: 0x2476F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerCAA15DependentAsTypeA2aDP22configuredDependencies0hJ0QzSgvMZTW', symObjAddr: 0x43FC, symBinAddr: 0x36D40, symSize: 0x6C }
  - { offsetInCU: 0xDAB, offset: 0x247C1, size: 0x8, addend: 0x0, symName: '_$sxq_xq_Iegnnrr_x3key_q_5valuetx_q_tIegnr_SHRzr0_lTRSS_ypTg575$sSD5merge_16uniquingKeysWithySDyxq_Gn_q_q__q_tKXEtKFx_q_tx_q_tcfU_SS_ypTG5Tf3nnpf_n', symObjAddr: 0x44F0, symBinAddr: 0x36E34, symSize: 0x40 }
  - { offsetInCU: 0xF84, offset: 0x2499A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginEventLogging_pWOb', symObjAddr: 0x5344, symBinAddr: 0x37C78, symSize: 0x18 }
  - { offsetInCU: 0xF97, offset: 0x249AD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginEventLogging_pWOc', symObjAddr: 0x59AC, symBinAddr: 0x3819C, symSize: 0x44 }
  - { offsetInCU: 0xFAA, offset: 0x249C0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC16TypeDependenciesVSgWOf', symObjAddr: 0x59F0, symBinAddr: 0x381E0, symSize: 0x48 }
  - { offsetInCU: 0xFBD, offset: 0x249D3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerCMa', symObjAddr: 0x5A38, symBinAddr: 0x38228, symSize: 0x20 }
  - { offsetInCU: 0xFD0, offset: 0x249E6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC16TypeDependenciesVwCP', symObjAddr: 0x5A6C, symBinAddr: 0x3825C, symSize: 0x30 }
  - { offsetInCU: 0xFE3, offset: 0x249F9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC16TypeDependenciesVwxx', symObjAddr: 0x5A9C, symBinAddr: 0x3828C, symSize: 0x14 }
  - { offsetInCU: 0xFF6, offset: 0x24A0C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC16TypeDependenciesVwcp', symObjAddr: 0x5AB0, symBinAddr: 0x382A0, symSize: 0x34 }
  - { offsetInCU: 0x1009, offset: 0x24A1F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC16TypeDependenciesVwca', symObjAddr: 0x5AE4, symBinAddr: 0x382D4, symSize: 0x24 }
  - { offsetInCU: 0x101C, offset: 0x24A32, size: 0x8, addend: 0x0, symName: ___swift_memcpy40_8, symObjAddr: 0x5C70, symBinAddr: 0x382F8, symSize: 0x14 }
  - { offsetInCU: 0x102F, offset: 0x24A45, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC16TypeDependenciesVwta', symObjAddr: 0x5C84, symBinAddr: 0x3830C, symSize: 0x38 }
  - { offsetInCU: 0x1042, offset: 0x24A58, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC16TypeDependenciesVwet', symObjAddr: 0x5CBC, symBinAddr: 0x38344, symSize: 0x48 }
  - { offsetInCU: 0x1055, offset: 0x24A6B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC16TypeDependenciesVwst', symObjAddr: 0x5D04, symBinAddr: 0x3838C, symSize: 0x48 }
  - { offsetInCU: 0x1068, offset: 0x24A7E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC16TypeDependenciesVMa', symObjAddr: 0x5D4C, symBinAddr: 0x383D4, symSize: 0x10 }
  - { offsetInCU: 0x1289, offset: 0x24C9F, size: 0x8, addend: 0x0, symName: '_$sSTsE6reduceyqd__qd___qd__qd___7ElementQztKXEtKlFShy13FBSDKLoginKit12FBPermissionCG_SSTg504$s13c61Kit18LoginManagerLoggerC12startSession3foryAA0cD0C_tFS2S_AA12E7CtXEfU_Tf1ncn_n', symObjAddr: 0x1B98, symBinAddr: 0x344DC, symSize: 0x308 }
  - { offsetInCU: 0x1488, offset: 0x24E9E, size: 0x8, addend: 0x0, symName: '_$ss17_dictionaryUpCastySDyq0_q1_GSDyxq_GSHRzSHR0_r2_lFSS_SbSSypTg5', symObjAddr: 0x3BD4, symBinAddr: 0x36518, symSize: 0x36C }
  - { offsetInCU: 0x17C3, offset: 0x251D9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC12startSession3foryAA0cD0C_tF', symObjAddr: 0x0, symBinAddr: 0x32984, symSize: 0x398 }
  - { offsetInCU: 0x19F1, offset: 0x25407, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC03endC06result5erroryAA0cdC6ResultCSg_So7NSErrorCSgtF', symObjAddr: 0x398, symBinAddr: 0x32D1C, symSize: 0x48C }
  - { offsetInCU: 0x1C56, offset: 0x2566C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC10endSessionyyF', symObjAddr: 0x824, symBinAddr: 0x331A8, symSize: 0x188 }
  - { offsetInCU: 0x1C99, offset: 0x256AF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC04postC9HeartbeatyyF', symObjAddr: 0x9AC, symBinAddr: 0x33330, symSize: 0x48 }
  - { offsetInCU: 0x1CC2, offset: 0x256D8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC14getClientState20authenticationMethod08existingH06loggerSSSgAH_SDySSypGSgACSgtFZ', symObjAddr: 0xA34, symBinAddr: 0x33378, symSize: 0x4 }
  - { offsetInCU: 0x1CFF, offset: 0x25715, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC31willAttemptAppSwitchingBehavior9urlSchemeySS_tF', symObjAddr: 0xA38, symBinAddr: 0x3337C, symSize: 0x260 }
  - { offsetInCU: 0x1E41, offset: 0x25857, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC5start20authenticationMethodySS_tF', symObjAddr: 0xC98, symBinAddr: 0x335DC, symSize: 0xA8 }
  - { offsetInCU: 0x1E7F, offset: 0x25895, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC10identifierSSSgvg', symObjAddr: 0xD40, symBinAddr: 0x33684, symSize: 0x48 }
  - { offsetInCU: 0x1E92, offset: 0x258A8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC10identifierSSSgvs', symObjAddr: 0xD88, symBinAddr: 0x336CC, symSize: 0x50 }
  - { offsetInCU: 0x1EA5, offset: 0x258BB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC10identifierSSSgvM', symObjAddr: 0xDD8, symBinAddr: 0x3371C, symSize: 0x3C }
  - { offsetInCU: 0x1EB8, offset: 0x258CE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC6extrasSDySSypGvg', symObjAddr: 0xE14, symBinAddr: 0x33758, symSize: 0x34 }
  - { offsetInCU: 0x1ECB, offset: 0x258E1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC6extrasSDySSypGvs', symObjAddr: 0xE48, symBinAddr: 0x3378C, symSize: 0x44 }
  - { offsetInCU: 0x1EDE, offset: 0x258F4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC6extrasSDySSypGvM', symObjAddr: 0xE8C, symBinAddr: 0x337D0, symSize: 0x3C }
  - { offsetInCU: 0x1EF1, offset: 0x25907, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC10lastResultSSvg', symObjAddr: 0xEC8, symBinAddr: 0x3380C, symSize: 0x48 }
  - { offsetInCU: 0x1F04, offset: 0x2591A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC10lastResultSSvs', symObjAddr: 0xF10, symBinAddr: 0x33854, symSize: 0x50 }
  - { offsetInCU: 0x1F17, offset: 0x2592D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC10lastResultSSvM', symObjAddr: 0xF60, symBinAddr: 0x338A4, symSize: 0x3C }
  - { offsetInCU: 0x1F2A, offset: 0x25940, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC9lastErrorSo7NSErrorCSgvg', symObjAddr: 0xF9C, symBinAddr: 0x338E0, symSize: 0x44 }
  - { offsetInCU: 0x1F3D, offset: 0x25953, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC9lastErrorSo7NSErrorCSgvs', symObjAddr: 0xFE0, symBinAddr: 0x33924, symSize: 0x44 }
  - { offsetInCU: 0x1F50, offset: 0x25966, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC9lastErrorSo7NSErrorCSgvM', symObjAddr: 0x1024, symBinAddr: 0x33968, symSize: 0x3C }
  - { offsetInCU: 0x1F63, offset: 0x25979, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC10authMethodSSSgvg', symObjAddr: 0x1060, symBinAddr: 0x339A4, symSize: 0x48 }
  - { offsetInCU: 0x1F76, offset: 0x2598C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC10authMethodSSSgvs', symObjAddr: 0x10A8, symBinAddr: 0x339EC, symSize: 0x50 }
  - { offsetInCU: 0x1F89, offset: 0x2599F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC10authMethodSSSgvM', symObjAddr: 0x10F8, symBinAddr: 0x33A3C, symSize: 0x3C }
  - { offsetInCU: 0x1F9C, offset: 0x259B2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC10authMethodSSSgvM.resume.0', symObjAddr: 0x1134, symBinAddr: 0x33A78, symSize: 0x4 }
  - { offsetInCU: 0x1FAF, offset: 0x259C5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC12loggingTokenSSSgvg', symObjAddr: 0x1138, symBinAddr: 0x33A7C, symSize: 0x48 }
  - { offsetInCU: 0x1FC2, offset: 0x259D8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC12loggingTokenSSSgvs', symObjAddr: 0x1180, symBinAddr: 0x33AC4, symSize: 0x50 }
  - { offsetInCU: 0x1FD5, offset: 0x259EB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC12loggingTokenSSSgvM', symObjAddr: 0x11D0, symBinAddr: 0x33B14, symSize: 0x3C }
  - { offsetInCU: 0x2000, offset: 0x25A16, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC10parameters8trackingACSgSDySSypGSg_AA0C8TrackingOtcfC', symObjAddr: 0x13F0, symBinAddr: 0x33D34, symSize: 0x718 }
  - { offsetInCU: 0x21AC, offset: 0x25BC2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC12loggingToken8trackingACSgSSSg_AA0C8TrackingOtcfC', symObjAddr: 0x1B08, symBinAddr: 0x3444C, symSize: 0x60 }
  - { offsetInCU: 0x21D4, offset: 0x25BEA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC12loggingToken8trackingACSgSSSg_AA0C8TrackingOtcfc', symObjAddr: 0x1B68, symBinAddr: 0x344AC, symSize: 0x30 }
  - { offsetInCU: 0x225D, offset: 0x25C73, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC21parametersForNewEventSDySo08FBSDKAppI13ParameterNameaypGyF', symObjAddr: 0x1F1C, symBinAddr: 0x34860, symSize: 0x7E0 }
  - { offsetInCU: 0x263F, offset: 0x26055, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC8logEvent_6paramsySo08FBSDKAppG4Namea_SDySo0ig9ParameterJ0aypGSgtF', symObjAddr: 0x26FC, symBinAddr: 0x35040, symSize: 0x344 }
  - { offsetInCU: 0x2756, offset: 0x2616C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC8logEvent_6result5errorySo08FBSDKAppG4Namea_SSSo7NSErrorCSgtF', symObjAddr: 0x2A40, symBinAddr: 0x35384, symSize: 0xFEC }
  - { offsetInCU: 0x2C68, offset: 0x2667E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC21heartbeatTimerDidFireyyF', symObjAddr: 0x3A2C, symBinAddr: 0x36370, symSize: 0xCC }
  - { offsetInCU: 0x2CDE, offset: 0x266F4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerCfd', symObjAddr: 0x3F40, symBinAddr: 0x36884, symSize: 0x44 }
  - { offsetInCU: 0x2D0B, offset: 0x26721, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerCfD', symObjAddr: 0x3F84, symBinAddr: 0x368C8, symSize: 0x4C }
  - { offsetInCU: 0x2D40, offset: 0x26756, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC16TypeDependenciesV05eventE0AA0C12EventLogging_pvg', symObjAddr: 0x3FD0, symBinAddr: 0x36914, symSize: 0xC }
  - { offsetInCU: 0x2D53, offset: 0x26769, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC16TypeDependenciesV05eventE0AA0C12EventLogging_pvs', symObjAddr: 0x3FDC, symBinAddr: 0x36920, symSize: 0x2C }
  - { offsetInCU: 0x2D66, offset: 0x2677C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC16TypeDependenciesV05eventE0AA0C12EventLogging_pvM', symObjAddr: 0x4008, symBinAddr: 0x3694C, symSize: 0x10 }
  - { offsetInCU: 0x2D79, offset: 0x2678F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC16TypeDependenciesV05eventE0AA0C12EventLogging_pvM.resume.0', symObjAddr: 0x4018, symBinAddr: 0x3695C, symSize: 0x4 }
  - { offsetInCU: 0x2D92, offset: 0x267A8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC16TypeDependenciesV05eventE0AeA0C12EventLogging_p_tcfC', symObjAddr: 0x401C, symBinAddr: 0x36960, symSize: 0x8 }
  - { offsetInCU: 0x2DA5, offset: 0x267BB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC19defaultDependenciesAC04TypeG0VSgvMZ', symObjAddr: 0x4108, symBinAddr: 0x36A4C, symSize: 0x6C }
  - { offsetInCU: 0x2DC4, offset: 0x267DA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC22configuredDependenciesAC04TypeG0VSgvMZ', symObjAddr: 0x42E8, symBinAddr: 0x36C2C, symSize: 0x6C }
  - { offsetInCU: 0x2E01, offset: 0x26817, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV4copyyyFSo26FBSDKAppEventParameterNamea_ypTg5', symObjAddr: 0x48D4, symBinAddr: 0x37218, symSize: 0x1D8 }
  - { offsetInCU: 0x2E92, offset: 0x268A8, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV4copyyyFSS_ypTg5', symObjAddr: 0x4AAC, symBinAddr: 0x373F0, symSize: 0x1F4 }
  - { offsetInCU: 0x2F2F, offset: 0x26945, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV4copyyyFSS_SSTg5', symObjAddr: 0x4CA0, symBinAddr: 0x375E4, symSize: 0x1C8 }
  - { offsetInCU: 0x2FDE, offset: 0x269F4, size: 0x8, addend: 0x0, symName: '_$sSD11removeValue6forKeyq_Sgx_tFSS_ypTg5', symObjAddr: 0x4E68, symBinAddr: 0x377AC, symSize: 0xE8 }
  - { offsetInCU: 0x3086, offset: 0x26A9C, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV7_delete2atys10_HashTableV6BucketV_tFSo26FBSDKAppEventParameterNamea_ypTg5', symObjAddr: 0x4F50, symBinAddr: 0x37894, symSize: 0x208 }
  - { offsetInCU: 0x3127, offset: 0x26B3D, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV7_delete2atys10_HashTableV6BucketV_tFSS_ypTg5', symObjAddr: 0x5158, symBinAddr: 0x37A9C, symSize: 0x1DC }
  - { offsetInCU: 0x31BC, offset: 0x26BD2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC12loggingToken8trackingACSgSSSg_AA0C8TrackingOtcfcTf4gnn_n', symObjAddr: 0x57E8, symBinAddr: 0x38020, symSize: 0x15C }
  - { offsetInCU: 0xD8, offset: 0x26D3B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC5tokenSo16FBSDKAccessTokenCSgvgTo', symObjAddr: 0x138, symBinAddr: 0x385F4, symSize: 0x10 }
  - { offsetInCU: 0xF7, offset: 0x26D5A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC5tokenSo16FBSDKAccessTokenCSgvgTo', symObjAddr: 0x138, symBinAddr: 0x385F4, symSize: 0x10 }
  - { offsetInCU: 0x127, offset: 0x26D8A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC19authenticationTokenSo019FBSDKAuthenticationG0CSgvgTo', symObjAddr: 0x178, symBinAddr: 0x38634, symSize: 0x10 }
  - { offsetInCU: 0x146, offset: 0x26DA9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC19authenticationTokenSo019FBSDKAuthenticationG0CSgvgTo', symObjAddr: 0x178, symBinAddr: 0x38634, symSize: 0x10 }
  - { offsetInCU: 0x176, offset: 0x26DD9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC11isCancelledSbvgTo', symObjAddr: 0x1B8, symBinAddr: 0x38674, symSize: 0x10 }
  - { offsetInCU: 0x195, offset: 0x26DF8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC11isCancelledSbvgTo', symObjAddr: 0x1B8, symBinAddr: 0x38674, symSize: 0x10 }
  - { offsetInCU: 0x1ED, offset: 0x26E50, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC13loggingExtrasSDySSypGvgTo', symObjAddr: 0x25C, symBinAddr: 0x38718, symSize: 0x7C }
  - { offsetInCU: 0x241, offset: 0x26EA4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC13loggingExtrasSDySSypGvsTo', symObjAddr: 0x320, symBinAddr: 0x387DC, symSize: 0x7C }
  - { offsetInCU: 0x282, offset: 0x26EE5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC5token19authenticationToken11isCancelled18grantedPermissions08declinedL0ACSo011FBSDKAccessH0CSg_So019FBSDKAuthenticationH0CSgSbShySSGAOtcfcTo', symObjAddr: 0x488, symBinAddr: 0x38908, symSize: 0x10C }
  - { offsetInCU: 0x2D9, offset: 0x26F3C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC15addLoggingExtra_6forKeyyyp_SStFTo', symObjAddr: 0x594, symBinAddr: 0x38A14, symSize: 0xF0 }
  - { offsetInCU: 0x34B, offset: 0x26FAE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultCACycfcTo', symObjAddr: 0x6D0, symBinAddr: 0x38B50, symSize: 0x2C }
  - { offsetInCU: 0x3D8, offset: 0x2703B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultCfETo', symObjAddr: 0x730, symBinAddr: 0x38BB0, symSize: 0x68 }
  - { offsetInCU: 0x4C4, offset: 0x27127, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultCMa', symObjAddr: 0x908, symBinAddr: 0x38D88, symSize: 0x20 }
  - { offsetInCU: 0x4D7, offset: 0x2713A, size: 0x8, addend: 0x0, symName: ___swift_destroy_boxed_opaque_existential_0, symObjAddr: 0x93C, symBinAddr: 0x38DBC, symSize: 0x20 }
  - { offsetInCU: 0x6D4, offset: 0x27337, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC5token19authenticationToken11isCancelled18grantedPermissions08declinedL0ACSo011FBSDKAccessH0CSg_So019FBSDKAuthenticationH0CSgSbShySSGAOtcfC', symObjAddr: 0x0, symBinAddr: 0x384BC, symSize: 0xB0 }
  - { offsetInCU: 0x739, offset: 0x2739C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC15addLoggingExtra_6forKeyyyp_SStF', symObjAddr: 0xB0, symBinAddr: 0x3856C, symSize: 0x88 }
  - { offsetInCU: 0x786, offset: 0x273E9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC5tokenSo16FBSDKAccessTokenCSgvg', symObjAddr: 0x148, symBinAddr: 0x38604, symSize: 0x30 }
  - { offsetInCU: 0x7B5, offset: 0x27418, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC19authenticationTokenSo019FBSDKAuthenticationG0CSgvg', symObjAddr: 0x188, symBinAddr: 0x38644, symSize: 0x30 }
  - { offsetInCU: 0x7E4, offset: 0x27447, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC11isCancelledSbvg', symObjAddr: 0x1C8, symBinAddr: 0x38684, symSize: 0x10 }
  - { offsetInCU: 0x7FF, offset: 0x27462, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC18grantedPermissionsShySSGvg', symObjAddr: 0x1E4, symBinAddr: 0x386A0, symSize: 0x10 }
  - { offsetInCU: 0x820, offset: 0x27483, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC19declinedPermissionsShySSGvg', symObjAddr: 0x24C, symBinAddr: 0x38708, symSize: 0x10 }
  - { offsetInCU: 0x853, offset: 0x274B6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC13loggingExtrasSDySSypGvg', symObjAddr: 0x2D8, symBinAddr: 0x38794, symSize: 0x48 }
  - { offsetInCU: 0x892, offset: 0x274F5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC5token19authenticationToken11isCancelled18grantedPermissions08declinedL0ACSo011FBSDKAccessH0CSg_So019FBSDKAuthenticationH0CSgSbShySSGAOtcfc', symObjAddr: 0x39C, symBinAddr: 0x38858, symSize: 0xB0 }
  - { offsetInCU: 0x90B, offset: 0x2756E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultCACycfC', symObjAddr: 0x684, symBinAddr: 0x38B04, symSize: 0x20 }
  - { offsetInCU: 0x91E, offset: 0x27581, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultCACycfc', symObjAddr: 0x6A4, symBinAddr: 0x38B24, symSize: 0x2C }
  - { offsetInCU: 0x972, offset: 0x275D5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultCfD', symObjAddr: 0x6FC, symBinAddr: 0x38B7C, symSize: 0x34 }
  - { offsetInCU: 0x9A5, offset: 0x27608, size: 0x8, addend: 0x0, symName: '_$sSD8_VariantV8setValue_6forKeyyq_n_xtFSS_SSTg5', symObjAddr: 0x798, symBinAddr: 0x38C18, symSize: 0xB8 }
  - { offsetInCU: 0xA10, offset: 0x27673, size: 0x8, addend: 0x0, symName: '_$sSD8_VariantV8setValue_6forKeyyq_n_xtFSS_ypTg5', symObjAddr: 0x850, symBinAddr: 0x38CD0, symSize: 0xB8 }
  - { offsetInCU: 0x27, offset: 0x2770B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateO8rawValueACSgSi_tcfC', symObjAddr: 0x0, symBinAddr: 0x38DDC, symSize: 0x10 }
  - { offsetInCU: 0x73, offset: 0x27757, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateOSYAASY8rawValuexSg03RawG0Qz_tcfCTW', symObjAddr: 0x120, symBinAddr: 0x38EFC, symSize: 0x18 }
  - { offsetInCU: 0xA2, offset: 0x27786, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateOSYAASY8rawValue03RawG0QzvgTW', symObjAddr: 0x138, symBinAddr: 0x38F14, symSize: 0xC }
  - { offsetInCU: 0xC9, offset: 0x277AD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateOSHAASQWb', symObjAddr: 0x2C, symBinAddr: 0x38E08, symSize: 0x4 }
  - { offsetInCU: 0xDC, offset: 0x277C0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateOACSQAAWl', symObjAddr: 0x30, symBinAddr: 0x38E0C, symSize: 0x44 }
  - { offsetInCU: 0x10D, offset: 0x277F1, size: 0x8, addend: 0x0, symName: ___swift_memcpy1_1, symObjAddr: 0x144, symBinAddr: 0x38F20, symSize: 0xC }
  - { offsetInCU: 0x120, offset: 0x27804, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateOwet', symObjAddr: 0x154, symBinAddr: 0x38F2C, symSize: 0x90 }
  - { offsetInCU: 0x133, offset: 0x27817, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateOwst', symObjAddr: 0x1E4, symBinAddr: 0x38FBC, symSize: 0xBC }
  - { offsetInCU: 0x146, offset: 0x2782A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateOwug', symObjAddr: 0x2A0, symBinAddr: 0x39078, symSize: 0x8 }
  - { offsetInCU: 0x159, offset: 0x2783D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateOwup', symObjAddr: 0x2A8, symBinAddr: 0x39080, symSize: 0x4 }
  - { offsetInCU: 0x16C, offset: 0x27850, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateOwui', symObjAddr: 0x2AC, symBinAddr: 0x39084, symSize: 0x8 }
  - { offsetInCU: 0x17F, offset: 0x27863, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateOMa', symObjAddr: 0x2B4, symBinAddr: 0x3908C, symSize: 0x10 }
  - { offsetInCU: 0x1B5, offset: 0x27899, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x18, symBinAddr: 0x38DF4, symSize: 0x14 }
  - { offsetInCU: 0x257, offset: 0x2793B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateOSHAASH9hashValueSivgTW', symObjAddr: 0x74, symBinAddr: 0x38E50, symSize: 0x44 }
  - { offsetInCU: 0x2FE, offset: 0x279E2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateOSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0xB8, symBinAddr: 0x38E94, symSize: 0x28 }
  - { offsetInCU: 0x34D, offset: 0x27A31, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateOSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0xE0, symBinAddr: 0x38EBC, symSize: 0x40 }
  - { offsetInCU: 0x450, offset: 0x27B34, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateO8rawValueACSgSi_tcfC', symObjAddr: 0x0, symBinAddr: 0x38DDC, symSize: 0x10 }
  - { offsetInCU: 0x46D, offset: 0x27B51, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateO8rawValueSivg', symObjAddr: 0x10, symBinAddr: 0x38DEC, symSize: 0x8 }
  - { offsetInCU: 0x49, offset: 0x27BF4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC19defaultDependenciesAC04TypeG0VSgvpZ', symObjAddr: 0x75D8, symBinAddr: 0x61230, symSize: 0x0 }
  - { offsetInCU: 0x130, offset: 0x27CDB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC22configuredDependenciesAC04TypeG0VSgvpZ', symObjAddr: 0x7608, symBinAddr: 0x61260, symSize: 0x0 }
  - { offsetInCU: 0x22E, offset: 0x27DD9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC07attemptD04from10completionys5Error_p_ySbctFTo', symObjAddr: 0x22C, symBinAddr: 0x392F8, symSize: 0x88 }
  - { offsetInCU: 0x390, offset: 0x27F3B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC19defaultDependencies_WZ', symObjAddr: 0x380, symBinAddr: 0x3944C, symSize: 0x58 }
  - { offsetInCU: 0x3D3, offset: 0x27F7E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC19defaultDependenciesAC04TypeG0VSgvau', symObjAddr: 0x3D8, symBinAddr: 0x394A4, symSize: 0x40 }
  - { offsetInCU: 0x41D, offset: 0x27FC8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC22configuredDependencies_WZ', symObjAddr: 0x4C0, symBinAddr: 0x3958C, symSize: 0x18 }
  - { offsetInCU: 0x436, offset: 0x27FE1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC22configuredDependenciesAC04TypeG0VSgvau', symObjAddr: 0x4D8, symBinAddr: 0x395A4, symSize: 0x40 }
  - { offsetInCU: 0x485, offset: 0x28030, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterCAA15DependentAsTypeA2aDP22configuredDependencies0hJ0QzSgvsZTW', symObjAddr: 0x6AC, symBinAddr: 0x39778, symSize: 0x84 }
  - { offsetInCU: 0x4B9, offset: 0x28064, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterCAA15DependentAsTypeA2aDP22configuredDependencies0hJ0QzSgvMZTW', symObjAddr: 0x730, symBinAddr: 0x397FC, symSize: 0x6C }
  - { offsetInCU: 0x52C, offset: 0x280D7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14LoginProviding_pWOc', symObjAddr: 0xB5C, symBinAddr: 0x39C28, symSize: 0x44 }
  - { offsetInCU: 0x53F, offset: 0x280EA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesVSgWOc', symObjAddr: 0xBD8, symBinAddr: 0x39C6C, symSize: 0x48 }
  - { offsetInCU: 0x552, offset: 0x280FD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesVSgWOf', symObjAddr: 0xC60, symBinAddr: 0x39CB4, symSize: 0x48 }
  - { offsetInCU: 0x565, offset: 0x28110, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesVSgWOh', symObjAddr: 0xCA8, symBinAddr: 0x39CFC, symSize: 0x40 }
  - { offsetInCU: 0x578, offset: 0x28123, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterCMa', symObjAddr: 0xCE8, symBinAddr: 0x39D3C, symSize: 0x20 }
  - { offsetInCU: 0x58B, offset: 0x28136, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesVwCP', symObjAddr: 0xD1C, symBinAddr: 0x39D70, symSize: 0x30 }
  - { offsetInCU: 0x59E, offset: 0x28149, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesVwxx', symObjAddr: 0xD4C, symBinAddr: 0x39DA0, symSize: 0x4 }
  - { offsetInCU: 0x5B2, offset: 0x2815D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesVwcp', symObjAddr: 0xD50, symBinAddr: 0x39DA4, symSize: 0x40 }
  - { offsetInCU: 0x5C5, offset: 0x28170, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesVwca', symObjAddr: 0xD90, symBinAddr: 0x39DE4, symSize: 0x30 }
  - { offsetInCU: 0x5D8, offset: 0x28183, size: 0x8, addend: 0x0, symName: ___swift_memcpy48_8, symObjAddr: 0xF28, symBinAddr: 0x39E14, symSize: 0x14 }
  - { offsetInCU: 0x5EB, offset: 0x28196, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesVwta', symObjAddr: 0xF3C, symBinAddr: 0x39E28, symSize: 0x38 }
  - { offsetInCU: 0x5FE, offset: 0x281A9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesVwet', symObjAddr: 0xF74, symBinAddr: 0x39E60, symSize: 0x48 }
  - { offsetInCU: 0x611, offset: 0x281BC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesVwst', symObjAddr: 0xFBC, symBinAddr: 0x39EA8, symSize: 0x4C }
  - { offsetInCU: 0x624, offset: 0x281CF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesVMa', symObjAddr: 0x1008, symBinAddr: 0x39EF4, symSize: 0x10 }
  - { offsetInCU: 0x641, offset: 0x281EC, size: 0x8, addend: 0x0, symName: '_$s10ObjectiveC8ObjCBoolVIeyBy_SbIegy_TRTA', symObjAddr: 0x103C, symBinAddr: 0x39F28, symSize: 0x14 }
  - { offsetInCU: 0x669, offset: 0x28214, size: 0x8, addend: 0x0, symName: '_$sSo16FBSDKAccessTokenCMa', symObjAddr: 0x1050, symBinAddr: 0x39F3C, symSize: 0x3C }
  - { offsetInCU: 0x68B, offset: 0x28236, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC07attemptD04from10completionys5Error_p_ySbctFyAA0c7ManagerC6ResultCSg_sAG_pSgtcfU_TA', symObjAddr: 0x10D4, symBinAddr: 0x39F9C, symSize: 0x68 }
  - { offsetInCU: 0x70A, offset: 0x282B5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesVWOh', symObjAddr: 0x113C, symBinAddr: 0x3A004, symSize: 0x24 }
  - { offsetInCU: 0x782, offset: 0x2832D, size: 0x8, addend: 0x0, symName: '_$sSlsE3mapySayqd__Gqd__7ElementQzKXEKlFShy12FBSDKCoreKit10PermissionOG_SSTg5091$s13FBSDKLoginKit22LoginRecoveryAttempterC07attemptD04from10completionys5Error_p_ySbctFSS09c4B010E52Ocfu_32f5ef5a0b9ade21eb65ffea7b618f60adAJSSTf3nnpk_nTf1cn_n', symObjAddr: 0x0, symBinAddr: 0x390CC, symSize: 0x220 }
  - { offsetInCU: 0xAD9, offset: 0x28684, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC07attemptD04from10completionys5Error_p_ySbctF', symObjAddr: 0x220, symBinAddr: 0x392EC, symSize: 0xC }
  - { offsetInCU: 0xB87, offset: 0x28732, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterCfd', symObjAddr: 0x2B4, symBinAddr: 0x39380, symSize: 0x8 }
  - { offsetInCU: 0xBAA, offset: 0x28755, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterCfD', symObjAddr: 0x2BC, symBinAddr: 0x39388, symSize: 0x10 }
  - { offsetInCU: 0xBD3, offset: 0x2877E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterCACycfC', symObjAddr: 0x2CC, symBinAddr: 0x39398, symSize: 0x10 }
  - { offsetInCU: 0xBE6, offset: 0x28791, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterCACycfc', symObjAddr: 0x2DC, symBinAddr: 0x393A8, symSize: 0x8 }
  - { offsetInCU: 0xC09, offset: 0x287B4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesV13loginProviderAA0C9Providing_pvg', symObjAddr: 0x2E4, symBinAddr: 0x393B0, symSize: 0xC }
  - { offsetInCU: 0xC1C, offset: 0x287C7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesV13loginProviderAA0C9Providing_pvs', symObjAddr: 0x2F0, symBinAddr: 0x393BC, symSize: 0x2C }
  - { offsetInCU: 0xC2F, offset: 0x287DA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesV13loginProviderAA0C9Providing_pvM', symObjAddr: 0x31C, symBinAddr: 0x393E8, symSize: 0x10 }
  - { offsetInCU: 0xC42, offset: 0x287ED, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesV13loginProviderAA0C9Providing_pvM.resume.0', symObjAddr: 0x32C, symBinAddr: 0x393F8, symSize: 0x4 }
  - { offsetInCU: 0xC5B, offset: 0x28806, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesV19accessTokenProviderSo011FBSDKAccessI9Providing_pXpvg', symObjAddr: 0x330, symBinAddr: 0x393FC, symSize: 0x8 }
  - { offsetInCU: 0xC6E, offset: 0x28819, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesV19accessTokenProviderSo011FBSDKAccessI9Providing_pXpvs', symObjAddr: 0x338, symBinAddr: 0x39404, symSize: 0x8 }
  - { offsetInCU: 0xC81, offset: 0x2882C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesV19accessTokenProviderSo011FBSDKAccessI9Providing_pXpvM', symObjAddr: 0x340, symBinAddr: 0x3940C, symSize: 0x10 }
  - { offsetInCU: 0xC94, offset: 0x2883F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesV19accessTokenProviderSo011FBSDKAccessI9Providing_pXpvM.resume.0', symObjAddr: 0x350, symBinAddr: 0x3941C, symSize: 0x4 }
  - { offsetInCU: 0xCAD, offset: 0x28858, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesV13loginProvider011accessTokenI0AeA0C9Providing_p_So011FBSDKAccesskL0_pXptcfC', symObjAddr: 0x354, symBinAddr: 0x39420, symSize: 0x2C }
  - { offsetInCU: 0xCD2, offset: 0x2887D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC19defaultDependenciesAC04TypeG0VSgvMZ', symObjAddr: 0x450, symBinAddr: 0x3951C, symSize: 0x6C }
  - { offsetInCU: 0xCF1, offset: 0x2889C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC19defaultDependenciesAC04TypeG0VSgvMZ.resume.0', symObjAddr: 0x4BC, symBinAddr: 0x39588, symSize: 0x4 }
  - { offsetInCU: 0xD05, offset: 0x288B0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC22configuredDependenciesAC04TypeG0VSgvMZ', symObjAddr: 0x624, symBinAddr: 0x396F0, symSize: 0x6C }
  - { offsetInCU: 0xD48, offset: 0x288F3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC07attemptD04from10completionys5Error_p_ySbctFTf4dnn_n', symObjAddr: 0x9C8, symBinAddr: 0x39A94, symSize: 0x194 }
  - { offsetInCU: 0x2B, offset: 0x28A0D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultO012loginManagerD0AA0cfcD0CSgvg', symObjAddr: 0x220, symBinAddr: 0x3A250, symSize: 0x144 }
  - { offsetInCU: 0x10A, offset: 0x28AEC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultO6result5errorAcA0c7ManagercD0CSg_s5Error_pSgtcfCTf4nnd_n', symObjAddr: 0x5E0, symBinAddr: 0x3A610, symSize: 0x498 }
  - { offsetInCU: 0x5D4, offset: 0x28FB6, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_ypTgmq5Tf4g_n', symObjAddr: 0x4E4, symBinAddr: 0x3A514, symSize: 0xFC }
  - { offsetInCU: 0x7C1, offset: 0x291A3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultOwCP', symObjAddr: 0xA78, symBinAddr: 0x3AAA8, symSize: 0x30 }
  - { offsetInCU: 0x7D4, offset: 0x291B6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultOWOy', symObjAddr: 0xAA8, symBinAddr: 0x3AAD8, symSize: 0x60 }
  - { offsetInCU: 0x7E7, offset: 0x291C9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultOwxx', symObjAddr: 0xB08, symBinAddr: 0x3AB38, symSize: 0x14 }
  - { offsetInCU: 0x7FA, offset: 0x291DC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultOwcp', symObjAddr: 0xB64, symBinAddr: 0x3AB4C, symSize: 0x5C }
  - { offsetInCU: 0x80D, offset: 0x291EF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultOwca', symObjAddr: 0xBC0, symBinAddr: 0x3ABA8, symSize: 0x6C }
  - { offsetInCU: 0x820, offset: 0x29202, size: 0x8, addend: 0x0, symName: ___swift_memcpy25_8, symObjAddr: 0xC2C, symBinAddr: 0x3AC14, symSize: 0x14 }
  - { offsetInCU: 0x833, offset: 0x29215, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultOwta', symObjAddr: 0xC40, symBinAddr: 0x3AC28, symSize: 0x4C }
  - { offsetInCU: 0x846, offset: 0x29228, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultOwet', symObjAddr: 0xC8C, symBinAddr: 0x3AC74, symSize: 0x48 }
  - { offsetInCU: 0x859, offset: 0x2923B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultOwst', symObjAddr: 0xCD4, symBinAddr: 0x3ACBC, symSize: 0x48 }
  - { offsetInCU: 0x86C, offset: 0x2924E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultOwug', symObjAddr: 0xD1C, symBinAddr: 0x3AD04, symSize: 0x18 }
  - { offsetInCU: 0x87F, offset: 0x29261, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultOwup', symObjAddr: 0xD34, symBinAddr: 0x3AD1C, symSize: 0x4 }
  - { offsetInCU: 0x892, offset: 0x29274, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultOwui', symObjAddr: 0xD38, symBinAddr: 0x3AD20, symSize: 0x1C }
  - { offsetInCU: 0x8A5, offset: 0x29287, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultOMa', symObjAddr: 0xD54, symBinAddr: 0x3AD3C, symSize: 0x10 }
  - { offsetInCU: 0xB6B, offset: 0x2954D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultO012loginManagerD0AA0cfcD0CSgvg', symObjAddr: 0x220, symBinAddr: 0x3A250, symSize: 0x144 }
  - { offsetInCU: 0xC95, offset: 0x29677, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultO5errors5Error_pSgvg', symObjAddr: 0x364, symBinAddr: 0x3A394, symSize: 0x38 }
  - { offsetInCU: 0xCBE, offset: 0x296A0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultO6result5errorAcA0c7ManagercD0CSg_s5Error_pSgtcfC', symObjAddr: 0x39C, symBinAddr: 0x3A3CC, symSize: 0x4 }
  - { offsetInCU: 0xCD7, offset: 0x296B9, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4findys10_HashTableV6BucketV6bucket_Sb5foundtxSHRzlFSS_Tgq5', symObjAddr: 0x3A0, symBinAddr: 0x3A3D0, symSize: 0x64 }
  - { offsetInCU: 0xCF7, offset: 0x296D9, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4find_9hashValues10_HashTableV6BucketV6bucket_Sb5foundtx_SitSHRzlFSS_Tgq5', symObjAddr: 0x404, symBinAddr: 0x3A434, symSize: 0xE0 }
  - { offsetInCU: 0x27, offset: 0x29813, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13LoginTrackingO8rawValueACSgSu_tcfC', symObjAddr: 0x0, symBinAddr: 0x3AD4C, symSize: 0x20 }
  - { offsetInCU: 0x73, offset: 0x2985F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13LoginTrackingOSYAASY8rawValuexSg03RawF0Qz_tcfCTW', symObjAddr: 0x12C, symBinAddr: 0x3AE78, symSize: 0x30 }
  - { offsetInCU: 0xA2, offset: 0x2988E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13LoginTrackingOSYAASY8rawValue03RawF0QzvgTW', symObjAddr: 0x15C, symBinAddr: 0x3AEA8, symSize: 0xC }
  - { offsetInCU: 0xC9, offset: 0x298B5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13LoginTrackingOSHAASQWb', symObjAddr: 0x38, symBinAddr: 0x3AD84, symSize: 0x4 }
  - { offsetInCU: 0xDC, offset: 0x298C8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13LoginTrackingOACSQAAWl', symObjAddr: 0x3C, symBinAddr: 0x3AD88, symSize: 0x44 }
  - { offsetInCU: 0x10D, offset: 0x298F9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13LoginTrackingOMa', symObjAddr: 0x168, symBinAddr: 0x3AEB4, symSize: 0x10 }
  - { offsetInCU: 0x143, offset: 0x2992F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13LoginTrackingOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x24, symBinAddr: 0x3AD70, symSize: 0x14 }
  - { offsetInCU: 0x1E5, offset: 0x299D1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13LoginTrackingOSHAASH9hashValueSivgTW', symObjAddr: 0x80, symBinAddr: 0x3ADCC, symSize: 0x44 }
  - { offsetInCU: 0x28C, offset: 0x29A78, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13LoginTrackingOSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0xC4, symBinAddr: 0x3AE10, symSize: 0x28 }
  - { offsetInCU: 0x2DB, offset: 0x29AC7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13LoginTrackingOSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0xEC, symBinAddr: 0x3AE38, symSize: 0x40 }
  - { offsetInCU: 0x3DE, offset: 0x29BCA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13LoginTrackingO8rawValueACSgSu_tcfC', symObjAddr: 0x0, symBinAddr: 0x3AD4C, symSize: 0x20 }
  - { offsetInCU: 0x3FB, offset: 0x29BE7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13LoginTrackingO8rawValueSuvg', symObjAddr: 0x20, symBinAddr: 0x3AD6C, symSize: 0x4 }
  - { offsetInCU: 0x4A, offset: 0x29C88, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV22configuredDependenciesAC04TypeF0VSgvpZ', symObjAddr: 0x16AA8, symBinAddr: 0x61290, symSize: 0x0 }
  - { offsetInCU: 0x236, offset: 0x29E74, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV19defaultDependenciesAC04TypeF0VSgvpZ', symObjAddr: 0x16B10, symBinAddr: 0x612F8, symSize: 0x0 }
  - { offsetInCU: 0x3C8, offset: 0x2A006, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterVAA0C10CompletingA2aDP08completeC07handleryyAA01_C20CompletionParametersCc_tFTW', symObjAddr: 0x3174, symBinAddr: 0x3E038, symSize: 0x20 }
  - { offsetInCU: 0x409, offset: 0x2A047, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterVAA0C10CompletingA2aDP08completeC05nonce12codeVerifier7handlerySSSg_AJyAA01_C20CompletionParametersCctFTW', symObjAddr: 0x3194, symBinAddr: 0x3E058, symSize: 0x8 }
  - { offsetInCU: 0x424, offset: 0x2A062, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV9challenge4fromSSSgSDySSypG_tFTf4nd_n', symObjAddr: 0x3978, symBinAddr: 0x3E83C, symSize: 0x348 }
  - { offsetInCU: 0x4DD, offset: 0x2A11B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV5error4froms5Error_pSgSDySSypG_tFTf4nd_n', symObjAddr: 0x3CC0, symBinAddr: 0x3EB84, symSize: 0x774 }
  - { offsetInCU: 0x7CF, offset: 0x2A40D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV13urlParameters5appIDACSDySSypG_SStcfCTf4nnd_n', symObjAddr: 0x4434, symBinAddr: 0x3F2F8, symSize: 0x7B0 }
  - { offsetInCU: 0x9D4, offset: 0x2A612, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV7profile4with11permissions09FBSDKCoreB07ProfileCSgAG25AuthenticationTokenClaimsC_ShySSGSgtFTf4nnd_n', symObjAddr: 0x4E94, symBinAddr: 0x3FC40, symSize: 0x7AC }
  - { offsetInCU: 0xD27, offset: 0x2A965, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV22configuredDependencies_WZ', symObjAddr: 0x337C, symBinAddr: 0x3E240, symSize: 0x20 }
  - { offsetInCU: 0xD42, offset: 0x2A980, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV22configuredDependenciesAC04TypeF0VSgvau', symObjAddr: 0x339C, symBinAddr: 0x3E260, symSize: 0x40 }
  - { offsetInCU: 0xD84, offset: 0x2A9C2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV19defaultDependencies_WZ', symObjAddr: 0x3480, symBinAddr: 0x3E344, symSize: 0x190 }
  - { offsetInCU: 0xE43, offset: 0x2AA81, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV19defaultDependenciesAC04TypeF0VSgvau', symObjAddr: 0x3610, symBinAddr: 0x3E4D4, symSize: 0x40 }
  - { offsetInCU: 0xE94, offset: 0x2AAD2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterVAA15DependentAsTypeA2aDP22configuredDependencies0gI0QzSgvsZTW', symObjAddr: 0x37F8, symBinAddr: 0x3E6BC, symSize: 0x8C }
  - { offsetInCU: 0xECA, offset: 0x2AB08, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterVAA15DependentAsTypeA2aDP22configuredDependencies0gI0QzSgvMZTW', symObjAddr: 0x3884, symBinAddr: 0x3E748, symSize: 0x6C }
  - { offsetInCU: 0xF4E, offset: 0x2AB8C, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DateVSgWOd', symObjAddr: 0x4C68, symBinAddr: 0x3FAA8, symSize: 0x48 }
  - { offsetInCU: 0xF61, offset: 0x2AB9F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesVWOh', symObjAddr: 0x4CEC, symBinAddr: 0x3FAF0, symSize: 0x2C }
  - { offsetInCU: 0xF74, offset: 0x2ABB2, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x4D38, symBinAddr: 0x3FB3C, symSize: 0x10 }
  - { offsetInCU: 0xF87, offset: 0x2ABC5, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x4D48, symBinAddr: 0x3FB4C, symSize: 0x8 }
  - { offsetInCU: 0xF9A, offset: 0x2ABD8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV24fetchAndSetPropertiesFor10parameters5nonce7handleryAA01_C20CompletionParametersC_SSyAIctFySo24FBSDKAuthenticationTokenCSgcfU_TA', symObjAddr: 0x4E84, symBinAddr: 0x3FC30, symSize: 0x10 }
  - { offsetInCU: 0xFAD, offset: 0x2ABEB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15ProfileCreating_pWOb', symObjAddr: 0x5684, symBinAddr: 0x40430, symSize: 0x18 }
  - { offsetInCU: 0xFC0, offset: 0x2ABFE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesVSgWOf', symObjAddr: 0x569C, symBinAddr: 0x40448, symSize: 0x48 }
  - { offsetInCU: 0xFD3, offset: 0x2AC11, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterVMa', symObjAddr: 0x56E4, symBinAddr: 0x40490, symSize: 0x10 }
  - { offsetInCU: 0xFE6, offset: 0x2AC24, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesVwCP', symObjAddr: 0x56F4, symBinAddr: 0x404A0, symSize: 0x30 }
  - { offsetInCU: 0xFF9, offset: 0x2AC37, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesVwxx', symObjAddr: 0x5724, symBinAddr: 0x404D0, symSize: 0x3C }
  - { offsetInCU: 0x100C, offset: 0x2AC4A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesVwcp', symObjAddr: 0x5760, symBinAddr: 0x4050C, symSize: 0x80 }
  - { offsetInCU: 0x101F, offset: 0x2AC5D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesVwca', symObjAddr: 0x57E0, symBinAddr: 0x4058C, symSize: 0x84 }
  - { offsetInCU: 0x1032, offset: 0x2AC70, size: 0x8, addend: 0x0, symName: ___swift_memcpy104_8, symObjAddr: 0x59CC, symBinAddr: 0x40610, symSize: 0x2C }
  - { offsetInCU: 0x1045, offset: 0x2AC83, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesVwta', symObjAddr: 0x59F8, symBinAddr: 0x4063C, symSize: 0x80 }
  - { offsetInCU: 0x1058, offset: 0x2AC96, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesVwet', symObjAddr: 0x5A78, symBinAddr: 0x406BC, symSize: 0x48 }
  - { offsetInCU: 0x106B, offset: 0x2ACA9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesVwst', symObjAddr: 0x5AC0, symBinAddr: 0x40704, symSize: 0x58 }
  - { offsetInCU: 0x107E, offset: 0x2ACBC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesVMa', symObjAddr: 0x5B18, symBinAddr: 0x4075C, symSize: 0x10 }
  - { offsetInCU: 0x1091, offset: 0x2ACCF, size: 0x8, addend: 0x0, symName: '_$sSo8NSNumberCMa', symObjAddr: 0x5B28, symBinAddr: 0x4076C, symSize: 0x3C }
  - { offsetInCU: 0x147D, offset: 0x2B0BB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV13urlParameters5appIDACSDySSypG_SStcfC', symObjAddr: 0x0, symBinAddr: 0x3AEC4, symSize: 0x4 }
  - { offsetInCU: 0x1490, offset: 0x2B0CE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV10parametersAA01_C20CompletionParametersCvg', symObjAddr: 0x4, symBinAddr: 0x3AEC8, symSize: 0x4 }
  - { offsetInCU: 0x14EB, offset: 0x2B129, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV13setParameters6values5appIDySDySSypG_SStF', symObjAddr: 0x8, symBinAddr: 0x3AECC, symSize: 0xD38 }
  - { offsetInCU: 0x1883, offset: 0x2B4C1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV5error4froms5Error_pSgSDySSypG_tF', symObjAddr: 0xD40, symBinAddr: 0x3BC04, symSize: 0x4 }
  - { offsetInCU: 0x18A2, offset: 0x2B4E0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV08completeC07handleryyAA01_C20CompletionParametersCc_tF', symObjAddr: 0xD44, symBinAddr: 0x3BC08, symSize: 0x20 }
  - { offsetInCU: 0x18DA, offset: 0x2B518, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV08completeC05nonce12codeVerifier7handlerySSSg_AHyAA01_C20CompletionParametersCctF', symObjAddr: 0xD64, symBinAddr: 0x3BC28, symSize: 0x340 }
  - { offsetInCU: 0x19DF, offset: 0x2B61D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV25exchangeCodeForTokensWith5nonce12codeVerifier7handlerySSSg_AHyAA01_C20CompletionParametersCctF', symObjAddr: 0x10A4, symBinAddr: 0x3BF68, symSize: 0x854 }
  - { offsetInCU: 0x1C01, offset: 0x2B83F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV25exchangeCodeForTokensWith5nonce12codeVerifier7handlerySSSg_AHyAA01_C20CompletionParametersCctFySo27FBSDKGraphRequestConnecting_pSg_ypSgs5Error_pSgtcfU_', symObjAddr: 0x2B34, symBinAddr: 0x3D9F8, symSize: 0x640 }
  - { offsetInCU: 0x1D71, offset: 0x2B9AF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV25exchangeNonceForTokenWith7handler014authenticationF0yyAA01_C20CompletionParametersCc_SStF', symObjAddr: 0x18F8, symBinAddr: 0x3C7BC, symSize: 0x55C }
  - { offsetInCU: 0x1F28, offset: 0x2BB66, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV25exchangeNonceForTokenWith7handler014authenticationF0yyAA01_C20CompletionParametersCc_SStFySo27FBSDKGraphRequestConnecting_pSg_ypSgs5Error_pSgtcfU_', symObjAddr: 0x270C, symBinAddr: 0x3D5D0, symSize: 0x428 }
  - { offsetInCU: 0x1FF2, offset: 0x2BC30, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV24fetchAndSetPropertiesFor10parameters5nonce7handleryAA01_C20CompletionParametersC_SSyAIctF', symObjAddr: 0x1E54, symBinAddr: 0x3CD18, symSize: 0x308 }
  - { offsetInCU: 0x20B1, offset: 0x2BCEF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV24fetchAndSetPropertiesFor10parameters5nonce7handleryAA01_C20CompletionParametersC_SSyAIctFySo24FBSDKAuthenticationTokenCSgcfU_', symObjAddr: 0x215C, symBinAddr: 0x3D020, symSize: 0x1F4 }
  - { offsetInCU: 0x21B5, offset: 0x2BDF3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV7profile4with11permissions09FBSDKCoreB07ProfileCSgAG25AuthenticationTokenClaimsC_ShySSGSgtF', symObjAddr: 0x2350, symBinAddr: 0x3D214, symSize: 0x4 }
  - { offsetInCU: 0x21C9, offset: 0x2BE07, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV18expirationDateFrom10parameters10Foundation0F0VSDySSypG_tF', symObjAddr: 0x2354, symBinAddr: 0x3D218, symSize: 0x2CC }
  - { offsetInCU: 0x22E7, offset: 0x2BF25, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV28dataAccessExpirationDateFrom10parameters10Foundation0H0VSDySSypG_tF', symObjAddr: 0x2620, symBinAddr: 0x3D4E4, symSize: 0xE8 }
  - { offsetInCU: 0x2352, offset: 0x2BF90, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV9challenge4fromSSSgSDySSypG_tF', symObjAddr: 0x2708, symBinAddr: 0x3D5CC, symSize: 0x4 }
  - { offsetInCU: 0x238D, offset: 0x2BFCB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV14profileFactoryAA15ProfileCreating_pvg', symObjAddr: 0x319C, symBinAddr: 0x3E060, symSize: 0x1C }
  - { offsetInCU: 0x23A0, offset: 0x2BFDE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV14profileFactoryAA15ProfileCreating_pvs', symObjAddr: 0x31B8, symBinAddr: 0x3E07C, symSize: 0x2C }
  - { offsetInCU: 0x23B3, offset: 0x2BFF1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV14profileFactoryAA15ProfileCreating_pvM', symObjAddr: 0x31E4, symBinAddr: 0x3E0A8, symSize: 0x10 }
  - { offsetInCU: 0x23C6, offset: 0x2C004, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV14profileFactoryAA15ProfileCreating_pvM.resume.0', symObjAddr: 0x31F4, symBinAddr: 0x3E0B8, symSize: 0x4 }
  - { offsetInCU: 0x23D9, offset: 0x2C017, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV26authenticationTokenCreatorAA014AuthenticationH8Creating_pvg', symObjAddr: 0x31F8, symBinAddr: 0x3E0BC, symSize: 0x1C }
  - { offsetInCU: 0x23EC, offset: 0x2C02A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV26authenticationTokenCreatorAA014AuthenticationH8Creating_pvs', symObjAddr: 0x3214, symBinAddr: 0x3E0D8, symSize: 0x30 }
  - { offsetInCU: 0x23FF, offset: 0x2C03D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV26authenticationTokenCreatorAA014AuthenticationH8Creating_pvM', symObjAddr: 0x3244, symBinAddr: 0x3E108, symSize: 0x10 }
  - { offsetInCU: 0x2412, offset: 0x2C050, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV26authenticationTokenCreatorAA014AuthenticationH8Creating_pvM.resume.0', symObjAddr: 0x3254, symBinAddr: 0x3E118, symSize: 0x4 }
  - { offsetInCU: 0x2425, offset: 0x2C063, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV19graphRequestFactorySo010FBSDKGraphhI0_pvg', symObjAddr: 0x3258, symBinAddr: 0x3E11C, symSize: 0x8 }
  - { offsetInCU: 0x2438, offset: 0x2C076, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV19graphRequestFactorySo010FBSDKGraphhI0_pvs', symObjAddr: 0x3260, symBinAddr: 0x3E124, symSize: 0x28 }
  - { offsetInCU: 0x244B, offset: 0x2C089, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV19graphRequestFactorySo010FBSDKGraphhI0_pvM', symObjAddr: 0x3288, symBinAddr: 0x3E14C, symSize: 0x10 }
  - { offsetInCU: 0x245E, offset: 0x2C09C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV19graphRequestFactorySo010FBSDKGraphhI0_pvM.resume.0', symObjAddr: 0x3298, symBinAddr: 0x3E15C, symSize: 0x4 }
  - { offsetInCU: 0x2471, offset: 0x2C0AF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV15internalUtilitySo15FBSDKURLHosting_pvg', symObjAddr: 0x329C, symBinAddr: 0x3E160, symSize: 0x8 }
  - { offsetInCU: 0x2484, offset: 0x2C0C2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV15internalUtilitySo15FBSDKURLHosting_pvs', symObjAddr: 0x32A4, symBinAddr: 0x3E168, symSize: 0x28 }
  - { offsetInCU: 0x2497, offset: 0x2C0D5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV15internalUtilitySo15FBSDKURLHosting_pvM', symObjAddr: 0x32CC, symBinAddr: 0x3E190, symSize: 0x10 }
  - { offsetInCU: 0x24AA, offset: 0x2C0E8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV15internalUtilitySo15FBSDKURLHosting_pvM.resume.0', symObjAddr: 0x32DC, symBinAddr: 0x3E1A0, symSize: 0x4 }
  - { offsetInCU: 0x24BD, offset: 0x2C0FB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV12errorFactorySo18FBSDKErrorCreating_pvg', symObjAddr: 0x32E0, symBinAddr: 0x3E1A4, symSize: 0x8 }
  - { offsetInCU: 0x24D0, offset: 0x2C10E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV12errorFactorySo18FBSDKErrorCreating_pvs', symObjAddr: 0x32E8, symBinAddr: 0x3E1AC, symSize: 0x28 }
  - { offsetInCU: 0x24E3, offset: 0x2C121, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV12errorFactorySo18FBSDKErrorCreating_pvM', symObjAddr: 0x3310, symBinAddr: 0x3E1D4, symSize: 0x10 }
  - { offsetInCU: 0x24F6, offset: 0x2C134, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV12errorFactorySo18FBSDKErrorCreating_pvM.resume.0', symObjAddr: 0x3320, symBinAddr: 0x3E1E4, symSize: 0x4 }
  - { offsetInCU: 0x250F, offset: 0x2C14D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV14profileFactory26authenticationTokenCreator012graphRequestH015internalUtility05errorH0AeA15ProfileCreating_p_AA014AuthenticationjR0_pSo010FBSDKGraphmH0_pSo15FBSDKURLHosting_pSo010FBSDKErrorR0_ptcfC', symObjAddr: 0x3324, symBinAddr: 0x3E1E8, symSize: 0x58 }
  - { offsetInCU: 0x2522, offset: 0x2C160, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV22configuredDependenciesAC04TypeF0VSgvMZ', symObjAddr: 0x3414, symBinAddr: 0x3E2D8, symSize: 0x6C }
  - { offsetInCU: 0x2588, offset: 0x2C1C6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV19defaultDependenciesAC04TypeF0VSgvMZ', symObjAddr: 0x376C, symBinAddr: 0x3E630, symSize: 0x6C }
  - { offsetInCU: 0x25A7, offset: 0x2C1E5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV19defaultDependenciesAC04TypeF0VSgvMZ.resume.0', symObjAddr: 0x37D8, symBinAddr: 0x3E69C, symSize: 0x4 }
  - { offsetInCU: 0x90, offset: 0x2C36E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginUtilityO9getUserID4fromSSSgAF_tFZTf4nd_n', symObjAddr: 0x70, symBinAddr: 0x4090C, symSize: 0x304 }
  - { offsetInCU: 0x1AB, offset: 0x2C489, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginUtilityO18getQueryParameters4fromSDySSypGSg10Foundation3URLV_tFZTf4nd_n', symObjAddr: 0x374, symBinAddr: 0x40C10, symSize: 0x594 }
  - { offsetInCU: 0x35F, offset: 0x2C63D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginUtilityOMa', symObjAddr: 0x908, symBinAddr: 0x411A4, symSize: 0x10 }
  - { offsetInCU: 0x372, offset: 0x2C650, size: 0x8, addend: 0x0, symName: '_$s10Foundation3URLVSgWOc', symObjAddr: 0xA1C, symBinAddr: 0x411B4, symSize: 0x48 }
  - { offsetInCU: 0x54A, offset: 0x2C828, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginUtilityO17stringForAudienceySSAA07DefaultG0OFZ', symObjAddr: 0x0, symBinAddr: 0x4089C, symSize: 0x68 }
  - { offsetInCU: 0x57A, offset: 0x2C858, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginUtilityO18getQueryParameters4fromSDySSypGSg10Foundation3URLV_tFZ', symObjAddr: 0x68, symBinAddr: 0x40904, symSize: 0x4 }
  - { offsetInCU: 0x58D, offset: 0x2C86B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginUtilityO9getUserID4fromSSSgAF_tFZ', symObjAddr: 0x6C, symBinAddr: 0x40908, symSize: 0x4 }
  - { offsetInCU: 0x27, offset: 0x2C92E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24MissingDependenciesErrorV3forACyxGxm_tcfC', symObjAddr: 0x0, symBinAddr: 0x41238, symSize: 0x4 }
  - { offsetInCU: 0x73, offset: 0x2C97A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24MissingDependenciesErrorVyxGs23CustomStringConvertibleAAsAEP11descriptionSSvgTW', symObjAddr: 0xC4, symBinAddr: 0x412FC, symSize: 0x8 }
  - { offsetInCU: 0xA4, offset: 0x2C9AB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24MissingDependenciesErrorVMi', symObjAddr: 0xCC, symBinAddr: 0x41304, symSize: 0x8 }
  - { offsetInCU: 0xB7, offset: 0x2C9BE, size: 0x8, addend: 0x0, symName: ___swift_memcpy8_8, symObjAddr: 0xD4, symBinAddr: 0x4130C, symSize: 0xC }
  - { offsetInCU: 0xCA, offset: 0x2C9D1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24MissingDependenciesErrorVwet', symObjAddr: 0xE4, symBinAddr: 0x41318, symSize: 0x48 }
  - { offsetInCU: 0xDD, offset: 0x2C9E4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24MissingDependenciesErrorVwst', symObjAddr: 0x12C, symBinAddr: 0x41360, symSize: 0x3C }
  - { offsetInCU: 0xF0, offset: 0x2C9F7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24MissingDependenciesErrorVMa', symObjAddr: 0x168, symBinAddr: 0x4139C, symSize: 0xC }
  - { offsetInCU: 0x103, offset: 0x2CA0A, size: 0x8, addend: 0x0, symName: ___swift_instantiateGenericMetadata, symObjAddr: 0x174, symBinAddr: 0x413A8, symSize: 0x2C }
  - { offsetInCU: 0x18F, offset: 0x2CA96, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24MissingDependenciesErrorVyxGs0E0AAsAEP7_domainSSvgTW', symObjAddr: 0xB4, symBinAddr: 0x412EC, symSize: 0x4 }
  - { offsetInCU: 0x1AB, offset: 0x2CAB2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24MissingDependenciesErrorVyxGs0E0AAsAEP5_codeSivgTW', symObjAddr: 0xB8, symBinAddr: 0x412F0, symSize: 0x4 }
  - { offsetInCU: 0x1C7, offset: 0x2CACE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24MissingDependenciesErrorVyxGs0E0AAsAEP9_userInfoyXlSgvgTW', symObjAddr: 0xBC, symBinAddr: 0x412F4, symSize: 0x4 }
  - { offsetInCU: 0x1E2, offset: 0x2CAE9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24MissingDependenciesErrorVyxGs0E0AAsAEP19_getEmbeddedNSErroryXlSgyFTW', symObjAddr: 0xC0, symBinAddr: 0x412F8, symSize: 0x4 }
  - { offsetInCU: 0x29A, offset: 0x2CBA1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24MissingDependenciesErrorV3forACyxGxm_tcfC', symObjAddr: 0x0, symBinAddr: 0x41238, symSize: 0x4 }
  - { offsetInCU: 0x2F6, offset: 0x2CBFD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24MissingDependenciesErrorV11descriptionSSvg', symObjAddr: 0x4, symBinAddr: 0x4123C, symSize: 0xB0 }
  - { offsetInCU: 0x27, offset: 0x2CCDA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14NonceValidatorO7isValid5nonceSbSS_tFZ', symObjAddr: 0x0, symBinAddr: 0x413D4, symSize: 0xCC }
  - { offsetInCU: 0x3D, offset: 0x2CCF0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14NonceValidatorOMa', symObjAddr: 0x110, symBinAddr: 0x414A0, symSize: 0x10 }
  - { offsetInCU: 0x10B, offset: 0x2CDBE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14NonceValidatorO7isValid5nonceSbSS_tFZ', symObjAddr: 0x0, symBinAddr: 0x413D4, symSize: 0xCC }
  - { offsetInCU: 0x27, offset: 0x2CE35, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14ProfileFactoryCACycfC', symObjAddr: 0x0, symBinAddr: 0x41518, symSize: 0x20 }
  - { offsetInCU: 0xDA, offset: 0x2CEE8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14ProfileFactoryCACycfcTo', symObjAddr: 0xA0, symBinAddr: 0x415B8, symSize: 0x3C }
  - { offsetInCU: 0x12A, offset: 0x2CF38, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14ProfileFactoryCAA0C8CreatingA2aDP06createC06userID9firstName06middleJ004lastJ04name7linkURL11refreshDate05imageO05email9friendIDs8birthday8ageRange8hometown8location6gender11permissions9isLimited09FBSDKCoreB00C0CSS_SSSgA_A_A_10Foundation0O0VSgA0_0Q0VSgA3_A_SaySSGSgA6_So012FBSDKUserAgeX0CSgSo13FBSDKLocationCSgA14_A_ShySSGSgSbtFTW', symObjAddr: 0x110, symBinAddr: 0x41628, symSize: 0x4C }
  - { offsetInCU: 0x15A, offset: 0x2CF68, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14ProfileFactoryC06createC06userID9firstName06middleI004lastI04name7linkURL11refreshDate05imageN05email9friendIDs8birthday8ageRange8hometown8location6gender11permissions9isLimited09FBSDKCoreB00C0CSS_SSSgA3Y10Foundation0N0VSgAZ0P0VSgA1_AYSaySSGSgA4_So012FBSDKUserAgeW0CSgSo13FBSDKLocationCSgA12_AYShySSGSgSbtFTf4nnnnnnnnnnnnnnnnnd_n', symObjAddr: 0x15C, symBinAddr: 0x41674, symSize: 0x294 }
  - { offsetInCU: 0x28F, offset: 0x2D09D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14ProfileFactoryCMa', symObjAddr: 0x3F0, symBinAddr: 0x41908, symSize: 0x20 }
  - { offsetInCU: 0x4B4, offset: 0x2D2C2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14ProfileFactoryCACycfC', symObjAddr: 0x0, symBinAddr: 0x41518, symSize: 0x20 }
  - { offsetInCU: 0x4C7, offset: 0x2D2D5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14ProfileFactoryC06createC06userID9firstName06middleI004lastI04name7linkURL11refreshDate05imageN05email9friendIDs8birthday8ageRange8hometown8location6gender11permissions9isLimited09FBSDKCoreB00C0CSS_SSSgA3Y10Foundation0N0VSgAZ0P0VSgA1_AYSaySSGSgA4_So012FBSDKUserAgeW0CSgSo13FBSDKLocationCSgA12_AYShySSGSgSbtF', symObjAddr: 0x20, symBinAddr: 0x41538, symSize: 0x4C }
  - { offsetInCU: 0x4DB, offset: 0x2D2E9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14ProfileFactoryCACycfc', symObjAddr: 0x6C, symBinAddr: 0x41584, symSize: 0x34 }
  - { offsetInCU: 0x515, offset: 0x2D323, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14ProfileFactoryCfD', symObjAddr: 0xDC, symBinAddr: 0x415F4, symSize: 0x34 }
  - { offsetInCU: 0x27, offset: 0x2D3AA, size: 0x8, addend: 0x0, symName: '_$s12FBSDKCoreKit27ServerConfigurationProviderC010FBSDKLoginB00cD9ProvidingA2dEP04loadcD010completionyySo0F7TooltipCSg_s5Error_pSgtcSg_tFTW', symObjAddr: 0x0, symBinAddr: 0x4196C, symSize: 0x20 }
  - { offsetInCU: 0x64, offset: 0x2D3E7, size: 0x8, addend: 0x0, symName: '_$s12FBSDKCoreKit27ServerConfigurationProviderC010FBSDKLoginB00cD9ProvidingA2dEP04loadcD010completionyySo0F7TooltipCSg_s5Error_pSgtcSg_tFTW', symObjAddr: 0x0, symBinAddr: 0x4196C, symSize: 0x20 }
  - { offsetInCU: 0x183, offset: 0x2D5F8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC5errors5Error_pSgvgTo', symObjAddr: 0x800, symBinAddr: 0x421AC, symSize: 0x70 }
  - { offsetInCU: 0x1D7, offset: 0x2D64C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC5errors5Error_pSgvsTo', symObjAddr: 0x8C0, symBinAddr: 0x4226C, symSize: 0x64 }
  - { offsetInCU: 0x2C0, offset: 0x2D735, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersCACycfcTo', symObjAddr: 0x1258, symBinAddr: 0x42B7C, symSize: 0x20 }
  - { offsetInCU: 0x2F1, offset: 0x2D766, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DateVSgWOc', symObjAddr: 0x9EC, symBinAddr: 0x42398, symSize: 0x48 }
  - { offsetInCU: 0x304, offset: 0x2D779, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersCfETo', symObjAddr: 0x12AC, symBinAddr: 0x42BD0, symSize: 0x14C }
  - { offsetInCU: 0x332, offset: 0x2D7A7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersCMU', symObjAddr: 0x13F8, symBinAddr: 0x42D1C, symSize: 0x8 }
  - { offsetInCU: 0x345, offset: 0x2D7BA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersCMa', symObjAddr: 0x1400, symBinAddr: 0x42D24, symSize: 0x3C }
  - { offsetInCU: 0x358, offset: 0x2D7CD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersCMr', symObjAddr: 0x143C, symBinAddr: 0x42D60, symSize: 0x98 }
  - { offsetInCU: 0x36B, offset: 0x2D7E0, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DateVSgMa', symObjAddr: 0x14D4, symBinAddr: 0x42DF8, symSize: 0x54 }
  - { offsetInCU: 0x37E, offset: 0x2D7F3, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DateVSgWOh', symObjAddr: 0x1570, symBinAddr: 0x42E4C, symSize: 0x40 }
  - { offsetInCU: 0x45E, offset: 0x2D8D3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersCACycfC', symObjAddr: 0x0, symBinAddr: 0x419AC, symSize: 0x20 }
  - { offsetInCU: 0x471, offset: 0x2D8E6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC19authenticationTokenSo019FBSDKAuthenticationG0CSgvg', symObjAddr: 0x2C, symBinAddr: 0x419D8, symSize: 0x50 }
  - { offsetInCU: 0x49A, offset: 0x2D90F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC19authenticationTokenSo019FBSDKAuthenticationG0CSgvM', symObjAddr: 0x94, symBinAddr: 0x41A40, symSize: 0x44 }
  - { offsetInCU: 0x4BD, offset: 0x2D932, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC7profile09FBSDKCoreB07ProfileCSgvg', symObjAddr: 0x128, symBinAddr: 0x41AD4, symSize: 0x50 }
  - { offsetInCU: 0x4E0, offset: 0x2D955, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC7profile09FBSDKCoreB07ProfileCSgvM', symObjAddr: 0x23C, symBinAddr: 0x41BE8, symSize: 0x44 }
  - { offsetInCU: 0x503, offset: 0x2D978, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC17accessTokenStringSSSgvM', symObjAddr: 0x2B0, symBinAddr: 0x41C5C, symSize: 0x44 }
  - { offsetInCU: 0x526, offset: 0x2D99B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC11nonceStringSSSgvM', symObjAddr: 0x324, symBinAddr: 0x41CD0, symSize: 0x44 }
  - { offsetInCU: 0x549, offset: 0x2D9BE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC25authenticationTokenStringSSSgvM', symObjAddr: 0x398, symBinAddr: 0x41D44, symSize: 0x44 }
  - { offsetInCU: 0x56C, offset: 0x2D9E1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC4codeSSSgvM', symObjAddr: 0x40C, symBinAddr: 0x41DB8, symSize: 0x44 }
  - { offsetInCU: 0x58F, offset: 0x2DA04, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC11permissionsShyAA12FBPermissionCGSgvM', symObjAddr: 0x488, symBinAddr: 0x41E34, symSize: 0x44 }
  - { offsetInCU: 0x5B2, offset: 0x2DA27, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC19declinedPermissionsShyAA12FBPermissionCGSgvM', symObjAddr: 0x504, symBinAddr: 0x41EB0, symSize: 0x44 }
  - { offsetInCU: 0x5D5, offset: 0x2DA4A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC18expiredPermissionsShyAA12FBPermissionCGSgvM', symObjAddr: 0x6D4, symBinAddr: 0x42080, symSize: 0x44 }
  - { offsetInCU: 0x5F8, offset: 0x2DA6D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC5appIDSSSgvM', symObjAddr: 0x748, symBinAddr: 0x420F4, symSize: 0x44 }
  - { offsetInCU: 0x61B, offset: 0x2DA90, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC6userIDSSSgvM', symObjAddr: 0x7BC, symBinAddr: 0x42168, symSize: 0x44 }
  - { offsetInCU: 0x655, offset: 0x2DACA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC5errors5Error_pSgvg', symObjAddr: 0x870, symBinAddr: 0x4221C, symSize: 0x50 }
  - { offsetInCU: 0x694, offset: 0x2DB09, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC5errors5Error_pSgvM', symObjAddr: 0x990, symBinAddr: 0x4233C, symSize: 0x44 }
  - { offsetInCU: 0x6B7, offset: 0x2DB2C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC14expirationDate10Foundation0G0VSgvM', symObjAddr: 0xAD4, symBinAddr: 0x423F8, symSize: 0x44 }
  - { offsetInCU: 0x6DA, offset: 0x2DB4F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC24dataAccessExpirationDate10Foundation0I0VSgvM', symObjAddr: 0xDA8, symBinAddr: 0x426CC, symSize: 0x44 }
  - { offsetInCU: 0x6FD, offset: 0x2DB72, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC9challengeSSSgvM', symObjAddr: 0xE1C, symBinAddr: 0x42740, symSize: 0x44 }
  - { offsetInCU: 0x720, offset: 0x2DB95, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC11graphDomainSSSgvM', symObjAddr: 0xE90, symBinAddr: 0x427B4, symSize: 0x44 }
  - { offsetInCU: 0x743, offset: 0x2DBB8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC11graphDomainSSSgvM.resume.0', symObjAddr: 0xED4, symBinAddr: 0x427F8, symSize: 0x4 }
  - { offsetInCU: 0x762, offset: 0x2DBD7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC14userTokenNonceSSSgvM', symObjAddr: 0x109C, symBinAddr: 0x429C0, symSize: 0x44 }
  - { offsetInCU: 0x785, offset: 0x2DBFA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersCACycfc', symObjAddr: 0x10E0, symBinAddr: 0x42A04, symSize: 0x178 }
  - { offsetInCU: 0x7A8, offset: 0x2DC1D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersCfD', symObjAddr: 0x1278, symBinAddr: 0x42B9C, symSize: 0x34 }
...
