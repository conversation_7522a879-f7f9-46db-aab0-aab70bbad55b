<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/FBSDKLoginAuthType.h</key>
		<data>
		dbPjG4QiKnnxeSs+knOQIJW0JVg=
		</data>
		<key>Headers/FBSDKLoginCompletionParametersBlock.h</key>
		<data>
		fPr6aC3BILzRqRBn1j6oocs3jMc=
		</data>
		<key>Headers/FBSDKLoginErrorDomain.h</key>
		<data>
		cneem1mczyhnLYxbDjbbR5mEmzM=
		</data>
		<key>Headers/FBSDKLoginKit-Swift.h</key>
		<data>
		xjD7iydkJ0Thh2ruGVwGJndMUDA=
		</data>
		<key>Headers/FBSDKLoginKit.h</key>
		<data>
		OUJgU2YPcKajMNRVhOXvWNOomHs=
		</data>
		<key>Headers/FBSDKLoginManagerLoginResultBlock.h</key>
		<data>
		hdGFhcZwp8w9OhQTkCmMyUDevXU=
		</data>
		<key>Info.plist</key>
		<data>
		5Iismi9QMj5R3rmLK7d0RMjCo9Q=
		</data>
		<key>Modules/FBSDKLoginKit.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<data>
		54b2zs1bfpk6hiYuhiW6naCu+h0=
		</data>
		<key>Modules/FBSDKLoginKit.swiftmodule/arm64-apple-ios-simulator.private.swiftinterface</key>
		<data>
		ouBVeevyUeuZ7++seMzrJM8Pw5E=
		</data>
		<key>Modules/FBSDKLoginKit.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<data>
		xmmHYSkmYmTTcUwGspxqqjfVtag=
		</data>
		<key>Modules/FBSDKLoginKit.swiftmodule/arm64-apple-ios-simulator.swiftinterface</key>
		<data>
		ouBVeevyUeuZ7++seMzrJM8Pw5E=
		</data>
		<key>Modules/FBSDKLoginKit.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<data>
		54b2zs1bfpk6hiYuhiW6naCu+h0=
		</data>
		<key>Modules/FBSDKLoginKit.swiftmodule/x86_64-apple-ios-simulator.private.swiftinterface</key>
		<data>
		9xLvEcb57VLJ95OxV9NpjwodpQM=
		</data>
		<key>Modules/FBSDKLoginKit.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<data>
		hUkBOw6N/o3v/hxeaTy/ysIFoCs=
		</data>
		<key>Modules/FBSDKLoginKit.swiftmodule/x86_64-apple-ios-simulator.swiftinterface</key>
		<data>
		9xLvEcb57VLJ95OxV9NpjwodpQM=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		KrDKcg24trXz/SqZuZkQjJYkUsc=
		</data>
		<key>PrivacyInfo.xcprivacy</key>
		<data>
		c/GhFel+DoHL4HradxiX7nIkOl4=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/FBSDKLoginAuthType.h</key>
		<dict>
			<key>hash2</key>
			<data>
			En8JspBXmCZrSWkWaxJV5tKzr8At6tqf53zIGNa2VYY=
			</data>
		</dict>
		<key>Headers/FBSDKLoginCompletionParametersBlock.h</key>
		<dict>
			<key>hash2</key>
			<data>
			7WGMXXull6LrlXHwbqyalo/ZMN0JSBtHbC6cWu8k2eI=
			</data>
		</dict>
		<key>Headers/FBSDKLoginErrorDomain.h</key>
		<dict>
			<key>hash2</key>
			<data>
			zFgW2vVnY7X9MEoilZ5/3iQAYiab+N4zlq9kMvgkl/4=
			</data>
		</dict>
		<key>Headers/FBSDKLoginKit-Swift.h</key>
		<dict>
			<key>hash2</key>
			<data>
			jMaPKo/8VzW30tDEgaa7AFlzku9Zp1zdjoGZik1KZjQ=
			</data>
		</dict>
		<key>Headers/FBSDKLoginKit.h</key>
		<dict>
			<key>hash2</key>
			<data>
			8b6xB2UCDMQ82B5SNTD1CpaH+pyHg+K3Yy0MxzTdgTE=
			</data>
		</dict>
		<key>Headers/FBSDKLoginManagerLoginResultBlock.h</key>
		<dict>
			<key>hash2</key>
			<data>
			Zb9GWGgaSc6xOtE0UsSlBLPOxWMfbTeNdSsr0fvIp74=
			</data>
		</dict>
		<key>Modules/FBSDKLoginKit.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			otLvCaFFvvsH2p2rr92zyti2Xrh/LUYkV/dlnLl/VDw=
			</data>
		</dict>
		<key>Modules/FBSDKLoginKit.swiftmodule/arm64-apple-ios-simulator.private.swiftinterface</key>
		<dict>
			<key>hash2</key>
			<data>
			wkOUEumaMOZRIE73EpGFHRFOE/E6POi8Xpd+VVevC8U=
			</data>
		</dict>
		<key>Modules/FBSDKLoginKit.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash2</key>
			<data>
			/zjaVRoFwEGicRWJ7mMqKDEe0Dj/mDVDluWz9IwvmoE=
			</data>
		</dict>
		<key>Modules/FBSDKLoginKit.swiftmodule/arm64-apple-ios-simulator.swiftinterface</key>
		<dict>
			<key>hash2</key>
			<data>
			wkOUEumaMOZRIE73EpGFHRFOE/E6POi8Xpd+VVevC8U=
			</data>
		</dict>
		<key>Modules/FBSDKLoginKit.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			otLvCaFFvvsH2p2rr92zyti2Xrh/LUYkV/dlnLl/VDw=
			</data>
		</dict>
		<key>Modules/FBSDKLoginKit.swiftmodule/x86_64-apple-ios-simulator.private.swiftinterface</key>
		<dict>
			<key>hash2</key>
			<data>
			1bezunNhsNtcxBcqIgXu0PggzGasID2rtN8n5ewKUW8=
			</data>
		</dict>
		<key>Modules/FBSDKLoginKit.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash2</key>
			<data>
			Edh3hLALLHPiPEpSTtcBB+9WoUkRCrs6Iimr+lPa+gM=
			</data>
		</dict>
		<key>Modules/FBSDKLoginKit.swiftmodule/x86_64-apple-ios-simulator.swiftinterface</key>
		<dict>
			<key>hash2</key>
			<data>
			1bezunNhsNtcxBcqIgXu0PggzGasID2rtN8n5ewKUW8=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash2</key>
			<data>
			/LNPo6mK3Ap58ptMqxKbx/hlGBOSkSGDoDN3+CL3VlA=
			</data>
		</dict>
		<key>PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			V+yTPiE3CaHxIWdHy5KWEryxwgIcGAfRgIV8XZH0Qpc=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
