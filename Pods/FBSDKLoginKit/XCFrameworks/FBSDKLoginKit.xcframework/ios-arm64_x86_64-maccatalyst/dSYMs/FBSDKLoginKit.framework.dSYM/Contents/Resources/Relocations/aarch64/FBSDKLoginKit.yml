---
triple:          'arm64-apple-darwin'
binary-path:     '/data/sandcastle/boxes/trunk-git-facebook-ios-sdk/build/Build/Intermediates.noindex/ArchiveIntermediates/FBSDKLoginKit-Dynamic/InstallationBuildProductsLocation/@rpath/FBSDKLoginKit.framework/Versions/A/FBSDKLoginKit'
relocations:
  - { offsetInCU: 0x33, offset: 0x33, size: 0x8, addend: 0x0, symName: _FBSDKLoginKitVersionString, symObjAddr: 0x0, symBinAddr: 0x46DC0, symSize: 0x0 }
  - { offsetInCU: 0x67, offset: 0x67, size: 0x8, addend: 0x0, symName: _FBSDKLoginKitVersionNumber, symObjAddr: 0x40, symBinAddr: 0x46E00, symSize: 0x0 }
  - { offsetInCU: 0x34, offset: 0xB0, size: 0x8, addend: 0x0, symName: _FBSDKLoginAuthTypeRerequest, symObjAddr: 0x58, symBinAddr: 0x54E10, symSize: 0x0 }
  - { offsetInCU: 0xB7, offset: 0x133, size: 0x8, addend: 0x0, symName: _FBSDKLoginAuthTypeReauthorize, symObjAddr: 0x60, symBinAddr: 0x54E18, symSize: 0x0 }
  - { offsetInCU: 0x34, offset: 0x184, size: 0x8, addend: 0x0, symName: _FBSDKLoginErrorDomain, symObjAddr: 0x38, symBinAddr: 0x54E20, symSize: 0x0 }
  - { offsetInCU: 0x3D, offset: 0x23F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5frame15elementProvider06stringG005loginG019graphRequestFactoryACSo6CGRectV_AA29UserInterfaceElementProviding_pAA0no6StringQ0_pAA05LoginQ0_pSo010FBSDKGraphkL0_ptcfcfA_', symObjAddr: 0xC4, symBinAddr: 0x5608, symSize: 0x14 }
  - { offsetInCU: 0x56, offset: 0x258, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5frame11permissionsACSo6CGRectV_Say09FBSDKCoreB010PermissionOGtcfcfA_', symObjAddr: 0xD8, symBinAddr: 0x561C, symSize: 0x14 }
  - { offsetInCU: 0x6F, offset: 0x271, size: 0x8, addend: 0x0, symName: ___swift_instantiateConcreteTypeFromMangledName, symObjAddr: 0xF0, symBinAddr: 0x5634, symSize: 0x40 }
  - { offsetInCU: 0x82, offset: 0x284, size: 0x8, addend: 0x0, symName: ___swift_memcpy16_8, symObjAddr: 0x1EC, symBinAddr: 0x5730, symSize: 0xC }
  - { offsetInCU: 0x95, offset: 0x297, size: 0x8, addend: 0x0, symName: ___swift_noop_void_return, symObjAddr: 0x1F8, symBinAddr: 0x573C, symSize: 0x4 }
  - { offsetInCU: 0xA8, offset: 0x2AA, size: 0x8, addend: 0x0, symName: '_$sSo7CGPointVwet', symObjAddr: 0x1FC, symBinAddr: 0x5740, symSize: 0x20 }
  - { offsetInCU: 0xBB, offset: 0x2BD, size: 0x8, addend: 0x0, symName: '_$sSo7CGPointVwst', symObjAddr: 0x21C, symBinAddr: 0x5760, symSize: 0x28 }
  - { offsetInCU: 0xCE, offset: 0x2D0, size: 0x8, addend: 0x0, symName: '_$sSo18FBSDKLoginAuthTypeas20_SwiftNewtypeWrapperSCSYWb', symObjAddr: 0x5D4, symBinAddr: 0x5A90, symSize: 0x2C }
  - { offsetInCU: 0xE1, offset: 0x2E3, size: 0x8, addend: 0x0, symName: '_$sSo18FBSDKLoginAuthTypeas20_SwiftNewtypeWrapperSCs35_HasCustomAnyHashableRepresentationPWb', symObjAddr: 0x600, symBinAddr: 0x5ABC, symSize: 0x2C }
  - { offsetInCU: 0xF4, offset: 0x2F6, size: 0x8, addend: 0x0, symName: '_$sSo18FBSDKLoginAuthTypeaSHSCSQWb', symObjAddr: 0x66C, symBinAddr: 0x5B28, symSize: 0x2C }
  - { offsetInCU: 0x107, offset: 0x309, size: 0x8, addend: 0x0, symName: '_$sSo26FBSDKAppEventParameterNameas20_SwiftNewtypeWrapperSCSYWb', symObjAddr: 0x804, symBinAddr: 0x5CC0, symSize: 0x2C }
  - { offsetInCU: 0x11A, offset: 0x31C, size: 0x8, addend: 0x0, symName: '_$sSo26FBSDKAppEventParameterNameas20_SwiftNewtypeWrapperSCs35_HasCustomAnyHashableRepresentationPWb', symObjAddr: 0x830, symBinAddr: 0x5CEC, symSize: 0x2C }
  - { offsetInCU: 0x12D, offset: 0x32F, size: 0x8, addend: 0x0, symName: '_$sSo26FBSDKAppEventParameterNameaSHSCSQWb', symObjAddr: 0x85C, symBinAddr: 0x5D18, symSize: 0x2C }
  - { offsetInCU: 0x15B, offset: 0x35D, size: 0x8, addend: 0x0, symName: '_$sSo26FBSDKAppEventParameterNameas21_ObjectiveCBridgeableSCsACP016_forceBridgeFromE1C_6resulty01_E5CTypeQz_xSgztFZTW', symObjAddr: 0x294, symBinAddr: 0x57D8, symSize: 0x14 }
  - { offsetInCU: 0x176, offset: 0x378, size: 0x8, addend: 0x0, symName: '_$sSo26FBSDKAppEventParameterNameas21_ObjectiveCBridgeableSCsACP024_conditionallyBridgeFromE1C_6resultSb01_E5CTypeQz_xSgztFZTW', symObjAddr: 0x2A8, symBinAddr: 0x57EC, symSize: 0x18 }
  - { offsetInCU: 0x197, offset: 0x399, size: 0x8, addend: 0x0, symName: '_$sSo18FBSDKLoginAuthTypeas21_ObjectiveCBridgeableSCsACP016_forceBridgeFromD1C_6resulty01_D5CTypeQz_xSgztFZTW', symObjAddr: 0x324, symBinAddr: 0x5814, symSize: 0x14 }
  - { offsetInCU: 0x1B2, offset: 0x3B4, size: 0x8, addend: 0x0, symName: '_$sSo18FBSDKLoginAuthTypeas21_ObjectiveCBridgeableSCsACP024_conditionallyBridgeFromD1C_6resultSb01_D5CTypeQz_xSgztFZTW', symObjAddr: 0x338, symBinAddr: 0x5828, symSize: 0x18 }
  - { offsetInCU: 0x1D3, offset: 0x3D5, size: 0x8, addend: 0x0, symName: '_$sSo26FBSDKAppEventParameterNameas35_HasCustomAnyHashableRepresentationSCsACP03_tofgH0s0gH0VSgyFTW', symObjAddr: 0x4CC, symBinAddr: 0x5988, symSize: 0x84 }
  - { offsetInCU: 0x1EE, offset: 0x3F0, size: 0x8, addend: 0x0, symName: '_$sSo18FBSDKLoginAuthTypeas35_HasCustomAnyHashableRepresentationSCsACP03_toefG0s0fG0VSgyFTW', symObjAddr: 0x550, symBinAddr: 0x5A0C, symSize: 0x84 }
  - { offsetInCU: 0x242, offset: 0x444, size: 0x8, addend: 0x0, symName: '_$sSo14FBSDKAppEventsC13FBSDKLoginKit17LoginEventLoggingA2cDP13flushBehaviorSo0ab5FlushI0VvgTW', symObjAddr: 0x0, symBinAddr: 0x5544, symSize: 0x10 }
  - { offsetInCU: 0x293, offset: 0x495, size: 0x8, addend: 0x0, symName: '_$sSo14FBSDKAppEventsC13FBSDKLoginKit17LoginEventLoggingA2cDP011logInternalF0_10parameters18isImplicitlyLoggedySo0aF4Namea_SDySo0af9ParameterN0aypGSgSbtFTW', symObjAddr: 0x10, symBinAddr: 0x5554, symSize: 0xA4 }
  - { offsetInCU: 0x2D5, offset: 0x4D7, size: 0x8, addend: 0x0, symName: '_$sSo14FBSDKAppEventsC13FBSDKLoginKit17LoginEventLoggingA2cDP5flushyyFTW', symObjAddr: 0xB4, symBinAddr: 0x55F8, symSize: 0x10 }
  - { offsetInCU: 0x360, offset: 0x562, size: 0x8, addend: 0x0, symName: '_$sSo18FBSDKLoginAuthTypeaSYSCSY8rawValue03RawE0QzvgTW', symObjAddr: 0x45C, symBinAddr: 0x5918, symSize: 0x28 }
  - { offsetInCU: 0x1D7, offset: 0x891, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryCAA0cD8CreatingA2aDP06createD011tokenString5nonce11graphDomain10completionySS_S2SySo019FBSDKAuthenticationD0CSgctFTW', symObjAddr: 0x1894, symBinAddr: 0x7700, symSize: 0x20 }
  - { offsetInCU: 0x41E, offset: 0xAD8, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataVSgSo13NSURLResponseCSgs5Error_pSgIegggg_So6NSDataCSgAGSo7NSErrorCSgIeyByyy_TR', symObjAddr: 0x1688, symBinAddr: 0x74F4, symSize: 0xC8 }
  - { offsetInCU: 0x435, offset: 0xAEF, size: 0x8, addend: 0x0, symName: '_$s10Foundation13__DataStorageC15withUnsafeBytes2in5applyxSnySiG_xSWKXEtKlFyt_Tg5153$s13FBSDKLoginKit26AuthenticationTokenFactoryC15verifySignature_6header6claims14certificateKey10completionySS_S3SySbctFySo03SecK3RefaSgcfU_yycfU_ySWXEfU_AA0B0VSays5UInt8VGTf1nncn_n', symObjAddr: 0x18B4, symBinAddr: 0x7720, symSize: 0xCC }
  - { offsetInCU: 0x507, offset: 0xBC1, size: 0x8, addend: 0x0, symName: '_$sS2SSysWl', symObjAddr: 0x1A7C, symBinAddr: 0x78E8, symSize: 0x44 }
  - { offsetInCU: 0x51A, offset: 0xBD4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC06createD011tokenString5nonce11graphDomain10completionySS_S2SySo019FBSDKAuthenticationD0CSgctFySbcfU_TA', symObjAddr: 0x1AFC, symBinAddr: 0x7968, symSize: 0x30 }
  - { offsetInCU: 0x52D, offset: 0xBE7, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV15_RepresentationOWOe', symObjAddr: 0x1B80, symBinAddr: 0x79EC, symSize: 0x44 }
  - { offsetInCU: 0x540, offset: 0xBFA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC15verifySignature_6header6claims14certificateKey10completionySS_S3SySbctFySo03SecK3RefaSgcfU_TA', symObjAddr: 0x1BC4, symBinAddr: 0x7A30, symSize: 0x10 }
  - { offsetInCU: 0x553, offset: 0xC0D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC16getPublicKeyWith011certificateH010completionySS_ySo03SecH3RefaSgctFySo0l11CertificateM0aSgcfU_TA', symObjAddr: 0x1BF8, symBinAddr: 0x7A64, symSize: 0x8 }
  - { offsetInCU: 0x566, offset: 0xC20, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataVSgWOy', symObjAddr: 0x1C00, symBinAddr: 0x7A6C, symSize: 0x14 }
  - { offsetInCU: 0x579, offset: 0xC33, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV15_RepresentationOWOy', symObjAddr: 0x1C14, symBinAddr: 0x7A80, symSize: 0x44 }
  - { offsetInCU: 0x58C, offset: 0xC46, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataVSgWOe', symObjAddr: 0x1C58, symBinAddr: 0x7AC4, symSize: 0x14 }
  - { offsetInCU: 0x59F, offset: 0xC59, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC18getCertificateWith14certificateKey10completionySS_ySo03SecG3RefaSgctFy10Foundation4DataVSg_So13NSURLResponseCSgs5Error_pSgtcfU_TA', symObjAddr: 0x1CA0, symBinAddr: 0x7B0C, symSize: 0x2C }
  - { offsetInCU: 0x5B2, offset: 0xC6C, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x1CCC, symBinAddr: 0x7B38, symSize: 0x10 }
  - { offsetInCU: 0x5C5, offset: 0xC7F, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x1CDC, symBinAddr: 0x7B48, symSize: 0x8 }
  - { offsetInCU: 0x5D8, offset: 0xC92, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryCMa', symObjAddr: 0x1CE4, symBinAddr: 0x7B50, symSize: 0x20 }
  - { offsetInCU: 0x8B6, offset: 0xF70, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC15verifySignature_6header6claims14certificateKey10completionySS_S3SySbctFySo03SecK3RefaSgcfU_yycfU_TA', symObjAddr: 0x226C, symBinAddr: 0x8098, symSize: 0x14 }
  - { offsetInCU: 0x8C9, offset: 0xF83, size: 0x8, addend: 0x0, symName: '_$sSS_yptWOc', symObjAddr: 0x2280, symBinAddr: 0x80AC, symSize: 0x48 }
  - { offsetInCU: 0x8DC, offset: 0xF96, size: 0x8, addend: 0x0, symName: '_$sypWOb', symObjAddr: 0x22C8, symBinAddr: 0x80F4, symSize: 0x10 }
  - { offsetInCU: 0xAC4, offset: 0x117E, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_ypTgm5Tf4g_n', symObjAddr: 0x1980, symBinAddr: 0x77EC, symSize: 0xFC }
  - { offsetInCU: 0xC16, offset: 0x12D0, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_SbTgm5Tf4g_n', symObjAddr: 0x1D18, symBinAddr: 0x7B84, symSize: 0xE4 }
  - { offsetInCU: 0xD68, offset: 0x1422, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_SSSgTgm5Tf4g_n', symObjAddr: 0x1DFC, symBinAddr: 0x7C68, symSize: 0xFC }
  - { offsetInCU: 0xEAE, offset: 0x1568, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_SSTgm5Tf4g_n', symObjAddr: 0x1EF8, symBinAddr: 0x7D64, symSize: 0xFC }
  - { offsetInCU: 0x1006, offset: 0x16C0, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_SdTgm5Tf4g_n', symObjAddr: 0x1FF4, symBinAddr: 0x7E60, symSize: 0xEC }
  - { offsetInCU: 0x1158, offset: 0x1812, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_So8NSNumberCTgm5Tf4g_n', symObjAddr: 0x20E0, symBinAddr: 0x7F4C, symSize: 0xF0 }
  - { offsetInCU: 0x15B5, offset: 0x1C6F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC16beginCertificateSSvg', symObjAddr: 0x0, symBinAddr: 0x5E6C, symSize: 0x2C }
  - { offsetInCU: 0x15C8, offset: 0x1C82, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC14endCertificateSSvg', symObjAddr: 0x2C, symBinAddr: 0x5E98, symSize: 0x2C }
  - { offsetInCU: 0x15DB, offset: 0x1C95, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC15sessionProviderSo24FBSDKURLSessionProviding_pvg', symObjAddr: 0x58, symBinAddr: 0x5EC4, symSize: 0x34 }
  - { offsetInCU: 0x15F4, offset: 0x1CAE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC15sessionProviderSo24FBSDKURLSessionProviding_pvs', symObjAddr: 0x8C, symBinAddr: 0x5EF8, symSize: 0x44 }
  - { offsetInCU: 0x1607, offset: 0x1CC1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC15sessionProviderSo24FBSDKURLSessionProviding_pvM', symObjAddr: 0xD0, symBinAddr: 0x5F3C, symSize: 0x3C }
  - { offsetInCU: 0x161A, offset: 0x1CD4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC15sessionProviderSo24FBSDKURLSessionProviding_pvM.resume.0', symObjAddr: 0x10C, symBinAddr: 0x5F78, symSize: 0x4 }
  - { offsetInCU: 0x162D, offset: 0x1CE7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC19certificateEndpoint10Foundation3URLVvg', symObjAddr: 0x110, symBinAddr: 0x5F7C, symSize: 0x144 }
  - { offsetInCU: 0x168F, offset: 0x1D49, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC15sessionProviderACSo24FBSDKURLSessionProviding_p_tcfC', symObjAddr: 0x254, symBinAddr: 0x60C0, symSize: 0xF4 }
  - { offsetInCU: 0x1731, offset: 0x1DEB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC06createD011tokenString5nonce11graphDomain10completionySS_S2SySo019FBSDKAuthenticationD0CSgctF', symObjAddr: 0x348, symBinAddr: 0x61B4, symSize: 0x300 }
  - { offsetInCU: 0x1912, offset: 0x1FCC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC06createD011tokenString5nonce11graphDomain10completionySS_S2SySo019FBSDKAuthenticationD0CSgctFySbcfU_', symObjAddr: 0x648, symBinAddr: 0x64B4, symSize: 0x134 }
  - { offsetInCU: 0x1A1D, offset: 0x20D7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC15verifySignature_6header6claims14certificateKey10completionySS_S3SySbctF', symObjAddr: 0x77C, symBinAddr: 0x65E8, symSize: 0x288 }
  - { offsetInCU: 0x1B69, offset: 0x2223, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC15verifySignature_6header6claims14certificateKey10completionySS_S3SySbctFySo03SecK3RefaSgcfU_', symObjAddr: 0xA04, symBinAddr: 0x6870, symSize: 0x100 }
  - { offsetInCU: 0x1BF2, offset: 0x22AC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC15verifySignature_6header6claims14certificateKey10completionySS_S3SySbctFySo03SecK3RefaSgcfU_yycfU_', symObjAddr: 0xB04, symBinAddr: 0x6970, symSize: 0x324 }
  - { offsetInCU: 0x1F28, offset: 0x25E2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC15verifySignature_6header6claims14certificateKey10completionySS_S3SySbctFySo03SecK3RefaSgcfU_yycfU_ySWXEfU_', symObjAddr: 0xE38, symBinAddr: 0x6CA4, symSize: 0xDC }
  - { offsetInCU: 0x2120, offset: 0x27DA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC16getPublicKeyWith011certificateH010completionySS_ySo03SecH3RefaSgctF', symObjAddr: 0xF24, symBinAddr: 0x6D90, symSize: 0x78 }
  - { offsetInCU: 0x214E, offset: 0x2808, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC16getPublicKeyWith011certificateH010completionySS_ySo03SecH3RefaSgctFySo0l11CertificateM0aSgcfU_', symObjAddr: 0xF9C, symBinAddr: 0x6E08, symSize: 0xCC }
  - { offsetInCU: 0x2211, offset: 0x28CB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC18getCertificateWith14certificateKey10completionySS_ySo03SecG3RefaSgctF', symObjAddr: 0x1068, symBinAddr: 0x6ED4, symSize: 0x1F4 }
  - { offsetInCU: 0x2266, offset: 0x2920, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC18getCertificateWith14certificateKey10completionySS_ySo03SecG3RefaSgctFy10Foundation4DataVSg_So13NSURLResponseCSgs5Error_pSgtcfU_', symObjAddr: 0x125C, symBinAddr: 0x70C8, symSize: 0x42C }
  - { offsetInCU: 0x2374, offset: 0x2A2E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryCfd', symObjAddr: 0x1750, symBinAddr: 0x75BC, symSize: 0x2C }
  - { offsetInCU: 0x23A1, offset: 0x2A5B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryCfD', symObjAddr: 0x177C, symBinAddr: 0x75E8, symSize: 0x34 }
  - { offsetInCU: 0x23D6, offset: 0x2A90, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryCACycfC', symObjAddr: 0x17B0, symBinAddr: 0x761C, symSize: 0x34 }
  - { offsetInCU: 0x23E9, offset: 0x2AA3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryCACycfc', symObjAddr: 0x17E4, symBinAddr: 0x7650, symSize: 0xB0 }
  - { offsetInCU: 0x105, offset: 0x2DDC, size: 0x8, addend: 0x0, symName: '_$sypSgWOh', symObjAddr: 0x6A0, symBinAddr: 0x8730, symSize: 0x40 }
  - { offsetInCU: 0x118, offset: 0x2DEF, size: 0x8, addend: 0x0, symName: '_$sypWOc', symObjAddr: 0x6E0, symBinAddr: 0x8770, symSize: 0x3C }
  - { offsetInCU: 0x12B, offset: 0x2E02, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit25AuthenticationTokenHeaderCMa', symObjAddr: 0x71C, symBinAddr: 0x87AC, symSize: 0x20 }
  - { offsetInCU: 0x1ED, offset: 0x2EC4, size: 0x8, addend: 0x0, symName: '_$ss11AnyHashableVWOc', symObjAddr: 0xB18, symBinAddr: 0x8BA8, symSize: 0x3C }
  - { offsetInCU: 0x200, offset: 0x2ED7, size: 0x8, addend: 0x0, symName: '_$ss11AnyHashableVWOh', symObjAddr: 0xB54, symBinAddr: 0x8BE4, symSize: 0x34 }
  - { offsetInCU: 0x35C, offset: 0x3033, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit25AuthenticationTokenHeaderC17fromEncodedStringACSgSS_tcfC', symObjAddr: 0x0, symBinAddr: 0x8114, symSize: 0x48 }
  - { offsetInCU: 0x36F, offset: 0x3046, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit25AuthenticationTokenHeaderC3kidSSvg', symObjAddr: 0x48, symBinAddr: 0x815C, symSize: 0x2C }
  - { offsetInCU: 0x3A0, offset: 0x3077, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit25AuthenticationTokenHeaderC17fromEncodedStringACSgSS_tcfc', symObjAddr: 0x74, symBinAddr: 0x8188, symSize: 0x504 }
  - { offsetInCU: 0x4C6, offset: 0x319D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit25AuthenticationTokenHeaderCfd', symObjAddr: 0x5FC, symBinAddr: 0x868C, symSize: 0x1C }
  - { offsetInCU: 0x4FE, offset: 0x31D5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit25AuthenticationTokenHeaderCfD', symObjAddr: 0x618, symBinAddr: 0x86A8, symSize: 0x24 }
  - { offsetInCU: 0x547, offset: 0x321E, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4findys10_HashTableV6BucketV6bucket_Sb5foundtxSHRzlFSS_Tg5', symObjAddr: 0x63C, symBinAddr: 0x86CC, symSize: 0x64 }
  - { offsetInCU: 0x582, offset: 0x3259, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4findys10_HashTableV6BucketV6bucket_Sb5foundtxSHRzlFs11AnyHashableV_Tg5', symObjAddr: 0x750, symBinAddr: 0x87E0, symSize: 0x30 }
  - { offsetInCU: 0x5AF, offset: 0x3286, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4findys10_HashTableV6BucketV6bucket_Sb5foundtxSHRzlFSo26FBSDKAppEventParameterNamea_Tg5', symObjAddr: 0x780, symBinAddr: 0x8810, symSize: 0x80 }
  - { offsetInCU: 0x5EA, offset: 0x32C1, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4find_9hashValues10_HashTableV6BucketV6bucket_Sb5foundtx_SitSHRzlFSS_Tg5', symObjAddr: 0x800, symBinAddr: 0x8890, symSize: 0xE0 }
  - { offsetInCU: 0x658, offset: 0x332F, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4find_9hashValues10_HashTableV6BucketV6bucket_Sb5foundtx_SitSHRzlFs11AnyHashableV_Tg5', symObjAddr: 0x8E0, symBinAddr: 0x8970, symSize: 0xC4 }
  - { offsetInCU: 0x67F, offset: 0x3356, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4find_9hashValues10_HashTableV6BucketV6bucket_Sb5foundtx_SitSHRzlFSo26FBSDKAppEventParameterNamea_Tg5', symObjAddr: 0x9A4, symBinAddr: 0x8A34, symSize: 0x174 }
  - { offsetInCU: 0x2B, offset: 0x33E5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12CodeVerifierC5valueSSvgTo', symObjAddr: 0x0, symBinAddr: 0x8C18, symSize: 0x4C }
  - { offsetInCU: 0x61, offset: 0x341B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12CodeVerifierC5valueSSvgTo', symObjAddr: 0x0, symBinAddr: 0x8C18, symSize: 0x4C }
  - { offsetInCU: 0x99, offset: 0x3453, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12CodeVerifierC9challengeSSvgTo', symObjAddr: 0x84, symBinAddr: 0x8C9C, symSize: 0x58 }
  - { offsetInCU: 0x14B, offset: 0x3505, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12CodeVerifierC6stringACSgSS_tcfcTo', symObjAddr: 0xAEC, symBinAddr: 0x96C0, symSize: 0x28 }
  - { offsetInCU: 0x190, offset: 0x354A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12CodeVerifierCACycfcTo', symObjAddr: 0xDD0, symBinAddr: 0x99A4, symSize: 0x20 }
  - { offsetInCU: 0x400, offset: 0x37BA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12CodeVerifierCfETo', symObjAddr: 0xE24, symBinAddr: 0x99F8, symSize: 0x14 }
  - { offsetInCU: 0x42D, offset: 0x37E7, size: 0x8, addend: 0x0, symName: '_$s10Foundation13__DataStorageC15withUnsafeBytes2in5applyxSnySiG_xSWKXEtKlFyt_Tg554$s13FBSDKLoginKit12CodeVerifierC9challengeSSvgySWXEfU_AA0B0VSays5UInt8VGTf1nncn_n', symObjAddr: 0xE38, symBinAddr: 0x9A0C, symSize: 0xCC }
  - { offsetInCU: 0x455, offset: 0x380F, size: 0x8, addend: 0x0, symName: '_$sSays5UInt8VGSayxG10Foundation15ContiguousBytesAeBRszlWl', symObjAddr: 0xFF4, symBinAddr: 0x9AD8, symSize: 0x4C }
  - { offsetInCU: 0x468, offset: 0x3822, size: 0x8, addend: 0x0, symName: ___swift_instantiateConcreteTypeFromMangledNameAbstract, symObjAddr: 0x1040, symBinAddr: 0x9B24, symSize: 0x44 }
  - { offsetInCU: 0x47B, offset: 0x3835, size: 0x8, addend: 0x0, symName: ___swift_project_boxed_opaque_existential_1, symObjAddr: 0x1084, symBinAddr: 0x9B68, symSize: 0x24 }
  - { offsetInCU: 0x78B, offset: 0x3B45, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV06InlineB0VyAESWcfCTf4nd_n', symObjAddr: 0x14F0, symBinAddr: 0x9FD4, symSize: 0xC4 }
  - { offsetInCU: 0x7FB, offset: 0x3BB5, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV10LargeSliceVyAESWcfCTf4nd_n', symObjAddr: 0x15B4, symBinAddr: 0xA098, symSize: 0x78 }
  - { offsetInCU: 0x826, offset: 0x3BE0, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV11InlineSliceVyAESWcfCTf4nd_n', symObjAddr: 0x162C, symBinAddr: 0xA110, symSize: 0x80 }
  - { offsetInCU: 0x8B3, offset: 0x3C6D, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV15_RepresentationOyAESWcfCTf4nd_n', symObjAddr: 0x16AC, symBinAddr: 0xA190, symSize: 0x68 }
  - { offsetInCU: 0x904, offset: 0x3CBE, size: 0x8, addend: 0x0, symName: ___swift_destroy_boxed_opaque_existential_1, symObjAddr: 0x1714, symBinAddr: 0xA1F8, symSize: 0x20 }
  - { offsetInCU: 0x917, offset: 0x3CD1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12CodeVerifierCMa', symObjAddr: 0x1734, symBinAddr: 0xA218, symSize: 0x20 }
  - { offsetInCU: 0xB89, offset: 0x3F43, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12CodeVerifierC5valueSSvg', symObjAddr: 0x4C, symBinAddr: 0x8C64, symSize: 0x38 }
  - { offsetInCU: 0xBF9, offset: 0x3FB3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12CodeVerifierC9challengeSSvg', symObjAddr: 0xDC, symBinAddr: 0x8CF4, symSize: 0x458 }
  - { offsetInCU: 0xED3, offset: 0x428D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12CodeVerifierC9challengeSSvgySWXEfU_', symObjAddr: 0x544, symBinAddr: 0x915C, symSize: 0xDC }
  - { offsetInCU: 0x111C, offset: 0x44D6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12CodeVerifierC6stringACSgSS_tcfC', symObjAddr: 0x674, symBinAddr: 0x9248, symSize: 0x238 }
  - { offsetInCU: 0x11AE, offset: 0x4568, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12CodeVerifierC6stringACSgSS_tcfc', symObjAddr: 0x8AC, symBinAddr: 0x9480, symSize: 0x240 }
  - { offsetInCU: 0x123C, offset: 0x45F6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12CodeVerifierCACycfC', symObjAddr: 0xB14, symBinAddr: 0x96E8, symSize: 0x20 }
  - { offsetInCU: 0x1279, offset: 0x4633, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12CodeVerifierCACycfc', symObjAddr: 0xB34, symBinAddr: 0x9708, symSize: 0x29C }
  - { offsetInCU: 0x13CD, offset: 0x4787, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12CodeVerifierCfD', symObjAddr: 0xDF0, symBinAddr: 0x99C4, symSize: 0x34 }
  - { offsetInCU: 0x1418, offset: 0x47D2, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtFs5UInt8V_Tg5', symObjAddr: 0x10A8, symBinAddr: 0x9B8C, symSize: 0xE8 }
  - { offsetInCU: 0x153F, offset: 0x48F9, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtF13FBSDKLoginKit18DeviceLoginManagerC_Tg5', symObjAddr: 0x1190, symBinAddr: 0x9C74, symSize: 0x160 }
  - { offsetInCU: 0x169A, offset: 0x4A54, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtFSS_Tg5', symObjAddr: 0x12F0, symBinAddr: 0x9DD4, symSize: 0x104 }
  - { offsetInCU: 0x17AF, offset: 0x4B69, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV13_copyContents8subRange12initializingSpyxGSnySiG_AFtF13FBSDKLoginKit18DeviceLoginManagerC_Tg5', symObjAddr: 0x13F4, symBinAddr: 0x9ED8, symSize: 0xFC }
  - { offsetInCU: 0x27, offset: 0x4D00, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15DefaultAudienceO8rawValueACSgSu_tcfC', symObjAddr: 0x0, symBinAddr: 0xA244, symSize: 0x14 }
  - { offsetInCU: 0x73, offset: 0x4D4C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15DefaultAudienceOSYAASY8rawValuexSg03RawF0Qz_tcfCTW', symObjAddr: 0x120, symBinAddr: 0xA364, symSize: 0x20 }
  - { offsetInCU: 0xA2, offset: 0x4D7B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15DefaultAudienceOSYAASY8rawValue03RawF0QzvgTW', symObjAddr: 0x140, symBinAddr: 0xA384, symSize: 0xC }
  - { offsetInCU: 0xC8, offset: 0x4DA1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15DefaultAudienceOSHAASQWb', symObjAddr: 0x2C, symBinAddr: 0xA270, symSize: 0x4 }
  - { offsetInCU: 0xDB, offset: 0x4DB4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15DefaultAudienceOACSQAAWl', symObjAddr: 0x30, symBinAddr: 0xA274, symSize: 0x44 }
  - { offsetInCU: 0x10C, offset: 0x4DE5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15DefaultAudienceOMa', symObjAddr: 0x14C, symBinAddr: 0xA390, symSize: 0x10 }
  - { offsetInCU: 0x14D, offset: 0x4E26, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15DefaultAudienceOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x18, symBinAddr: 0xA25C, symSize: 0x14 }
  - { offsetInCU: 0x1F5, offset: 0x4ECE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15DefaultAudienceOSHAASH9hashValueSivgTW', symObjAddr: 0x74, symBinAddr: 0xA2B8, symSize: 0x44 }
  - { offsetInCU: 0x29C, offset: 0x4F75, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15DefaultAudienceOSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0xB8, symBinAddr: 0xA2FC, symSize: 0x28 }
  - { offsetInCU: 0x2EB, offset: 0x4FC4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15DefaultAudienceOSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0xE0, symBinAddr: 0xA324, symSize: 0x40 }
  - { offsetInCU: 0x3F0, offset: 0x50C9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15DefaultAudienceO8rawValueACSgSu_tcfC', symObjAddr: 0x0, symBinAddr: 0xA244, symSize: 0x14 }
  - { offsetInCU: 0x40D, offset: 0x50E6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15DefaultAudienceO8rawValueSuvg', symObjAddr: 0x14, symBinAddr: 0xA258, symSize: 0x4 }
  - { offsetInCU: 0xE4, offset: 0x5221, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerCAA17DependentAsObjectA2aDP15setDependenciesyy0hJ0QzFTW', symObjAddr: 0x47C, symBinAddr: 0xA81C, symSize: 0x68 }
  - { offsetInCU: 0x1B9, offset: 0x52F6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCAA17DependentAsObjectA2aDP15setDependenciesyy0gI0QzFTW', symObjAddr: 0x4E4, symBinAddr: 0xA884, symSize: 0x68 }
  - { offsetInCU: 0x235, offset: 0x5372, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesVWOc', symObjAddr: 0x54C, symBinAddr: 0xA8EC, symSize: 0x3C }
  - { offsetInCU: 0x248, offset: 0x5385, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesVWOc', symObjAddr: 0x5C8, symBinAddr: 0xA928, symSize: 0x3C }
  - { offsetInCU: 0x34B, offset: 0x5488, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17DependentAsObjectPAAE15setDependenciesyy0eG0QzF', symObjAddr: 0x0, symBinAddr: 0xA3A0, symSize: 0xE8 }
  - { offsetInCU: 0x38B, offset: 0x54C8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17DependentAsObjectPAAE15getDependencies0eG0QzyKF', symObjAddr: 0xE8, symBinAddr: 0xA488, symSize: 0x244 }
  - { offsetInCU: 0x3BC, offset: 0x54F9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17DependentAsObjectPAAE13dynamicMemberqd__Sgs7KeyPathCy0E12DependenciesQzqd__G_tcluig', symObjAddr: 0x32C, symBinAddr: 0xA6CC, symSize: 0x128 }
  - { offsetInCU: 0xED, offset: 0x573F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerCAA15DependentAsTypeA2aDP15setDependenciesyy0hJ0QzFZTW', symObjAddr: 0x47C, symBinAddr: 0xAE24, symSize: 0x98 }
  - { offsetInCU: 0x1A4, offset: 0x57F6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterCAA15DependentAsTypeA2aDP15setDependenciesyy0hJ0QzFZTW', symObjAddr: 0x514, symBinAddr: 0xAEBC, symSize: 0x98 }
  - { offsetInCU: 0x259, offset: 0x58AB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterVAA15DependentAsTypeA2aDP15setDependenciesyy0gI0QzFZTW', symObjAddr: 0x5AC, symBinAddr: 0xAF54, symSize: 0x98 }
  - { offsetInCU: 0x2B2, offset: 0x5904, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesVWOc', symObjAddr: 0x644, symBinAddr: 0xAFEC, symSize: 0x3C }
  - { offsetInCU: 0x2C5, offset: 0x5917, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesVWOc', symObjAddr: 0x6C0, symBinAddr: 0xB028, symSize: 0x3C }
  - { offsetInCU: 0x2D8, offset: 0x592A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC16TypeDependenciesVWOc', symObjAddr: 0x6FC, symBinAddr: 0xB064, symSize: 0x3C }
  - { offsetInCU: 0x3BA, offset: 0x5A0C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15DependentAsTypePAAE15setDependenciesyy0eG0QzFZ', symObjAddr: 0x0, symBinAddr: 0xA9A8, symSize: 0xE8 }
  - { offsetInCU: 0x3FA, offset: 0x5A4C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15DependentAsTypePAAE15getDependencies0eG0QzyKFZ', symObjAddr: 0xE8, symBinAddr: 0xAA90, symSize: 0x244 }
  - { offsetInCU: 0x42B, offset: 0x5A7D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15DependentAsTypePAAE13dynamicMemberqd__Sgs7KeyPathCy0E12DependenciesQzqd__G_tcluigZ', symObjAddr: 0x32C, symBinAddr: 0xACD4, symSize: 0x128 }
  - { offsetInCU: 0x8C, offset: 0x5C25, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoC15verificationURL10Foundation0H0VvgTo', symObjAddr: 0xD0, symBinAddr: 0xB1F0, symSize: 0x28 }
  - { offsetInCU: 0xD9, offset: 0x5C72, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoC14expirationDate10Foundation0H0VvgTo', symObjAddr: 0x10C, symBinAddr: 0xB22C, symSize: 0x28 }
  - { offsetInCU: 0x126, offset: 0x5CBF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoC15pollingIntervalSuvgTo', symObjAddr: 0x188, symBinAddr: 0xB2A8, symSize: 0x10 }
  - { offsetInCU: 0x146, offset: 0x5CDF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoC15pollingIntervalSuvgTo', symObjAddr: 0x188, symBinAddr: 0xB2A8, symSize: 0x10 }
  - { offsetInCU: 0x1BB, offset: 0x5D54, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoC10identifier05loginE015verificationURL14expirationDate15pollingIntervalACSS_SS10Foundation0J0VAI0L0VSutcfcTo', symObjAddr: 0x428, symBinAddr: 0xB548, symSize: 0x1BC }
  - { offsetInCU: 0x259, offset: 0x5DF2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoCACycfcTo', symObjAddr: 0x630, symBinAddr: 0xB750, symSize: 0x2C }
  - { offsetInCU: 0x2E6, offset: 0x5E7F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoCfETo', symObjAddr: 0x690, symBinAddr: 0xB7B0, symSize: 0x90 }
  - { offsetInCU: 0x314, offset: 0x5EAD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoCMU', symObjAddr: 0x720, symBinAddr: 0xB840, symSize: 0x8 }
  - { offsetInCU: 0x327, offset: 0x5EC0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoCMa', symObjAddr: 0x728, symBinAddr: 0xB848, symSize: 0x3C }
  - { offsetInCU: 0x33A, offset: 0x5ED3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoCMr', symObjAddr: 0x764, symBinAddr: 0xB884, symSize: 0xA0 }
  - { offsetInCU: 0x424, offset: 0x5FBD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoC10identifierSSvg', symObjAddr: 0xC, symBinAddr: 0xB12C, symSize: 0x38 }
  - { offsetInCU: 0x447, offset: 0x5FE0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoC05loginE0SSvg', symObjAddr: 0x98, symBinAddr: 0xB1B8, symSize: 0x38 }
  - { offsetInCU: 0x4A5, offset: 0x603E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoC15pollingIntervalSuvg', symObjAddr: 0x198, symBinAddr: 0xB2B8, symSize: 0x10 }
  - { offsetInCU: 0x523, offset: 0x60BC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoC10identifier05loginE015verificationURL14expirationDate15pollingIntervalACSS_SS10Foundation0J0VAI0L0VSutcfC', symObjAddr: 0x1A8, symBinAddr: 0xB2C8, symSize: 0x140 }
  - { offsetInCU: 0x591, offset: 0x612A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoC10identifier05loginE015verificationURL14expirationDate15pollingIntervalACSS_SS10Foundation0J0VAI0L0VSutcfc', symObjAddr: 0x2E8, symBinAddr: 0xB408, symSize: 0x140 }
  - { offsetInCU: 0x5EF, offset: 0x6188, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoCACycfC', symObjAddr: 0x5E4, symBinAddr: 0xB704, symSize: 0x20 }
  - { offsetInCU: 0x608, offset: 0x61A1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoCACycfc', symObjAddr: 0x604, symBinAddr: 0xB724, symSize: 0x2C }
  - { offsetInCU: 0x65C, offset: 0x61F5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoCfD', symObjAddr: 0x65C, symBinAddr: 0xB77C, symSize: 0x34 }
  - { offsetInCU: 0x186, offset: 0x639C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV10Foundation13CustomNSErrorAadEP11errorDomainSSvgZTW', symObjAddr: 0x214, symBinAddr: 0xBB4C, symSize: 0x5C }
  - { offsetInCU: 0x1BB, offset: 0x63D1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV10Foundation13CustomNSErrorAadEP9errorCodeSivgTW', symObjAddr: 0x270, symBinAddr: 0xBBA8, symSize: 0x8 }
  - { offsetInCU: 0x1DA, offset: 0x63F0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV10Foundation13CustomNSErrorAadEP9errorCodeSivgTW', symObjAddr: 0x270, symBinAddr: 0xBBA8, symSize: 0x8 }
  - { offsetInCU: 0x1EB, offset: 0x6401, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV10Foundation13CustomNSErrorAadEP13errorUserInfoSDySSypGvgTW', symObjAddr: 0x278, symBinAddr: 0xBBB0, symSize: 0x8 }
  - { offsetInCU: 0x20A, offset: 0x6420, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV10Foundation13CustomNSErrorAadEP13errorUserInfoSDySSypGvgTW', symObjAddr: 0x278, symBinAddr: 0xBBB0, symSize: 0x8 }
  - { offsetInCU: 0x21B, offset: 0x6431, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVSHAASH9hashValueSivgTW', symObjAddr: 0x280, symBinAddr: 0xBBB8, symSize: 0x44 }
  - { offsetInCU: 0x2F5, offset: 0x650B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0x2C4, symBinAddr: 0xBBFC, symSize: 0x28 }
  - { offsetInCU: 0x363, offset: 0x6579, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x384, symBinAddr: 0xBCBC, symSize: 0x34 }
  - { offsetInCU: 0x40C, offset: 0x6622, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceLoginErrorCodeOSYAASY8rawValuexSg03RawH0Qz_tcfCTW', symObjAddr: 0x47C, symBinAddr: 0xBDB4, symSize: 0x30 }
  - { offsetInCU: 0x43B, offset: 0x6651, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceLoginErrorCodeOSYAASY8rawValue03RawH0QzvgTW', symObjAddr: 0x4AC, symBinAddr: 0xBDE4, symSize: 0xC }
  - { offsetInCU: 0x457, offset: 0x666D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceLoginErrorCodeO8rawValueACSgSi_tcfCTf4nd_n', symObjAddr: 0x4CC, symBinAddr: 0xBE04, symSize: 0x30 }
  - { offsetInCU: 0x4BB, offset: 0x66D1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV10Foundation13CustomNSErrorAAs0E0PWb', symObjAddr: 0x4FC, symBinAddr: 0xBE34, symSize: 0x4 }
  - { offsetInCU: 0x4CE, offset: 0x66E4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVACs0E0AAWl', symObjAddr: 0x500, symBinAddr: 0xBE38, symSize: 0x44 }
  - { offsetInCU: 0x4E1, offset: 0x66F7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVSHAASQWb', symObjAddr: 0x544, symBinAddr: 0xBE7C, symSize: 0x4 }
  - { offsetInCU: 0x4F4, offset: 0x670A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVACSQAAWl', symObjAddr: 0x548, symBinAddr: 0xBE80, symSize: 0x44 }
  - { offsetInCU: 0x507, offset: 0x671D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceLoginErrorCodeOSHAASQWb', symObjAddr: 0x58C, symBinAddr: 0xBEC4, symSize: 0x4 }
  - { offsetInCU: 0x51A, offset: 0x6730, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceLoginErrorCodeOACSQAAWl', symObjAddr: 0x590, symBinAddr: 0xBEC8, symSize: 0x44 }
  - { offsetInCU: 0x52D, offset: 0x6743, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVwxx', symObjAddr: 0x5D8, symBinAddr: 0xBF10, symSize: 0x28 }
  - { offsetInCU: 0x540, offset: 0x6756, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVwca', symObjAddr: 0x640, symBinAddr: 0xBF78, symSize: 0x64 }
  - { offsetInCU: 0x553, offset: 0x6769, size: 0x8, addend: 0x0, symName: ___swift_memcpy24_8, symObjAddr: 0x6A4, symBinAddr: 0xBFDC, symSize: 0x14 }
  - { offsetInCU: 0x566, offset: 0x677C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVwta', symObjAddr: 0x6B8, symBinAddr: 0xBFF0, symSize: 0x44 }
  - { offsetInCU: 0x579, offset: 0x678F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVwet', symObjAddr: 0x6FC, symBinAddr: 0xC034, symSize: 0x48 }
  - { offsetInCU: 0x58C, offset: 0x67A2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVwst', symObjAddr: 0x744, symBinAddr: 0xC07C, symSize: 0x40 }
  - { offsetInCU: 0x59F, offset: 0x67B5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVMa', symObjAddr: 0x784, symBinAddr: 0xC0BC, symSize: 0x10 }
  - { offsetInCU: 0x5B2, offset: 0x67C8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceLoginErrorCodeOMa', symObjAddr: 0x794, symBinAddr: 0xC0CC, symSize: 0x10 }
  - { offsetInCU: 0x5C5, offset: 0x67DB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVAC10Foundation13CustomNSErrorAAWl', symObjAddr: 0x7A4, symBinAddr: 0xC0DC, symSize: 0x44 }
  - { offsetInCU: 0x659, offset: 0x686F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0x2EC, symBinAddr: 0xBC24, symSize: 0x40 }
  - { offsetInCU: 0x6EF, offset: 0x6905, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVs0E0AAsADP9_userInfoyXlSgvgTW', symObjAddr: 0x37C, symBinAddr: 0xBCB4, symSize: 0x4 }
  - { offsetInCU: 0x70A, offset: 0x6920, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVs0E0AAsADP19_getEmbeddedNSErroryXlSgyFTW', symObjAddr: 0x380, symBinAddr: 0xBCB8, symSize: 0x4 }
  - { offsetInCU: 0x78A, offset: 0x69A0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceLoginErrorCodeOSHAASH9hashValueSivgTW', symObjAddr: 0x3D0, symBinAddr: 0xBD08, symSize: 0x44 }
  - { offsetInCU: 0x831, offset: 0x6A47, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceLoginErrorCodeOSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0x414, symBinAddr: 0xBD4C, symSize: 0x28 }
  - { offsetInCU: 0x880, offset: 0x6A96, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceLoginErrorCodeOSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0x43C, symBinAddr: 0xBD74, symSize: 0x40 }
  - { offsetInCU: 0x905, offset: 0x6B1B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceLoginErrorCodeOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x4B8, symBinAddr: 0xBDF0, symSize: 0x14 }
  - { offsetInCU: 0x998, offset: 0x6BAE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVs0E0AAsADP7_domainSSvgTW', symObjAddr: 0x32C, symBinAddr: 0xBC64, symSize: 0x28 }
  - { offsetInCU: 0x9B4, offset: 0x6BCA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVs0E0AAsADP5_codeSivgTW', symObjAddr: 0x354, symBinAddr: 0xBC8C, symSize: 0x28 }
  - { offsetInCU: 0xA3C, offset: 0x6C52, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV03_nsE0So7NSErrorCSgvg', symObjAddr: 0x0, symBinAddr: 0xB938, symSize: 0x28 }
  - { offsetInCU: 0xA4F, offset: 0x6C65, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV9errorCodeSivg', symObjAddr: 0x28, symBinAddr: 0xB960, symSize: 0x8 }
  - { offsetInCU: 0xA63, offset: 0x6C79, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV13errorUserInfoSDySSypGvg', symObjAddr: 0x30, symBinAddr: 0xB968, symSize: 0x8 }
  - { offsetInCU: 0xA82, offset: 0x6C98, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV03_nsE0ACSo7NSErrorC_tcfC', symObjAddr: 0x38, symBinAddr: 0xB970, symSize: 0xA0 }
  - { offsetInCU: 0xAA5, offset: 0x6CBB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV_8userInfoAcA0cdE4CodeO_SDySSypGtcfC', symObjAddr: 0xD8, symBinAddr: 0xBA10, symSize: 0xC }
  - { offsetInCU: 0xAD5, offset: 0x6CEB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceLoginErrorCodeO8rawValueSivg', symObjAddr: 0xE4, symBinAddr: 0xBA1C, symSize: 0x4 }
  - { offsetInCU: 0xAFA, offset: 0x6D10, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV11errorDomainSSvgZ', symObjAddr: 0xE8, symBinAddr: 0xBA20, symSize: 0x5C }
  - { offsetInCU: 0xB21, offset: 0x6D37, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV16excessivePollingAA0cdE4CodeOvgZ', symObjAddr: 0x144, symBinAddr: 0xBA7C, symSize: 0xC }
  - { offsetInCU: 0xB42, offset: 0x6D58, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV21authorizationDeclinedAA0cdE4CodeOvgZ', symObjAddr: 0x150, symBinAddr: 0xBA88, symSize: 0xC }
  - { offsetInCU: 0xB63, offset: 0x6D79, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV20authorizationPendingAA0cdE4CodeOvgZ', symObjAddr: 0x15C, symBinAddr: 0xBA94, symSize: 0xC }
  - { offsetInCU: 0xB84, offset: 0x6D9A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV11codeExpiredAA0cdE4CodeOvgZ', symObjAddr: 0x168, symBinAddr: 0xBAA0, symSize: 0xC }
  - { offsetInCU: 0xBA5, offset: 0x6DBB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV2eeoiySbAC_ACtFZ', symObjAddr: 0x174, symBinAddr: 0xBAAC, symSize: 0x34 }
  - { offsetInCU: 0xC14, offset: 0x6E2A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV4hash4intoys6HasherVz_tF', symObjAddr: 0x1A8, symBinAddr: 0xBAE0, symSize: 0x28 }
  - { offsetInCU: 0xC86, offset: 0x6E9C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV9hashValueSivg', symObjAddr: 0x1D0, symBinAddr: 0xBB08, symSize: 0x44 }
  - { offsetInCU: 0xDBE, offset: 0x6FD4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceLoginErrorCodeO8rawValueACSgSi_tcfC', symObjAddr: 0x3B8, symBinAddr: 0xBCF0, symSize: 0x18 }
  - { offsetInCU: 0x4E, offset: 0x7067, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC05loginE9Instances33_2E1868FF91A815585B124C0140A60DCBLLSayACGvpZ', symObjAddr: 0x75A8, symBinAddr: 0x5E180, symSize: 0x0 }
  - { offsetInCU: 0x7C, offset: 0x7095, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC8delegateAA0cdE8Delegate_pSgvgTo', symObjAddr: 0x14, symBinAddr: 0xC134, symSize: 0x48 }
  - { offsetInCU: 0xD0, offset: 0x70E9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC8delegateAA0cdE8Delegate_pSgvsTo', symObjAddr: 0xA4, symBinAddr: 0xC1C4, symSize: 0x50 }
  - { offsetInCU: 0x150, offset: 0x7169, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC11permissionsSaySSGvgTo', symObjAddr: 0x228, symBinAddr: 0xC348, symSize: 0x48 }
  - { offsetInCU: 0x19D, offset: 0x71B6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC11redirectURL10Foundation0G0VSgvgTo', symObjAddr: 0x280, symBinAddr: 0xC3A0, symSize: 0xE4 }
  - { offsetInCU: 0x1F1, offset: 0x720A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC11redirectURL10Foundation0G0VSgvsTo', symObjAddr: 0x3FC, symBinAddr: 0xC4DC, symSize: 0x10C }
  - { offsetInCU: 0x26B, offset: 0x7284, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC8codeInfoAA0cd4CodeG0CSgvgTo', symObjAddr: 0x5B0, symBinAddr: 0xC690, symSize: 0x48 }
  - { offsetInCU: 0x2BF, offset: 0x72D8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC8codeInfoAA0cd4CodeG0CSgvsTo', symObjAddr: 0x648, symBinAddr: 0xC728, symSize: 0x64 }
  - { offsetInCU: 0x37D, offset: 0x7396, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC11permissions011enableSmartD0ACSaySSG_SbtcfcTo', symObjAddr: 0xB40, symBinAddr: 0xCC20, symSize: 0x44 }
  - { offsetInCU: 0x457, offset: 0x7470, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC5startyyFTo', symObjAddr: 0x25D4, symBinAddr: 0xE6B4, symSize: 0x28 }
  - { offsetInCU: 0x487, offset: 0x74A0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC6cancelyyFTo', symObjAddr: 0x27D0, symBinAddr: 0xE8B0, symSize: 0x28 }
  - { offsetInCU: 0x4C6, offset: 0x74DF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC14notifyDelegate5token14expirationDate020dataAccessExpirationJ0ySSSg_10Foundation0J0VSgALtFTo', symObjAddr: 0x3AE4, symBinAddr: 0xFBC4, symSize: 0x1B0 }
  - { offsetInCU: 0x4E1, offset: 0x74FA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC12processErroryys0G0_pFTo', symObjAddr: 0x3C94, symBinAddr: 0xFD74, symSize: 0x50 }
  - { offsetInCU: 0x4FC, offset: 0x7515, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC12schedulePoll8intervalySu_tFTo', symObjAddr: 0x4F40, symBinAddr: 0x11020, symSize: 0x30 }
  - { offsetInCU: 0x541, offset: 0x755A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerCACycfcTo', symObjAddr: 0x4FBC, symBinAddr: 0x1109C, symSize: 0x2C }
  - { offsetInCU: 0x5D3, offset: 0x75EC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerCAA17DependentAsObjectA2aDP22configuredDependencies0hJ0QzSgvsTW', symObjAddr: 0x526C, symBinAddr: 0x1134C, symSize: 0x60 }
  - { offsetInCU: 0x615, offset: 0x762E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerCAA17DependentAsObjectA2aDP22configuredDependencies0hJ0QzSgvMTW', symObjAddr: 0x52CC, symBinAddr: 0x113AC, symSize: 0x44 }
  - { offsetInCU: 0x650, offset: 0x7669, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC05loginE9Instances33_2E1868FF91A815585B124C0140A60DCBLL_WZ', symObjAddr: 0x0, symBinAddr: 0xC120, symSize: 0x14 }
  - { offsetInCU: 0x846, offset: 0x785F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13DevicePolling_pWOb', symObjAddr: 0x82C, symBinAddr: 0xC90C, symSize: 0x18 }
  - { offsetInCU: 0xBE7, offset: 0x7C00, size: 0x8, addend: 0x0, symName: '_$sSo27FBSDKGraphRequestConnecting_pSgypSgs5Error_pSgIeggng_AByXlSgSo7NSErrorCSgIeyByyy_TR', symObjAddr: 0x2518, symBinAddr: 0xE5F8, symSize: 0xBC }
  - { offsetInCU: 0xE45, offset: 0x7E5E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerCfETo', symObjAddr: 0x501C, symBinAddr: 0x110FC, symSize: 0x94 }
  - { offsetInCU: 0xE90, offset: 0x7EA9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC10netService_13didNotPublishySo05NSNetG0C_SDySSSo8NSNumberCGtFTo', symObjAddr: 0x50B4, symBinAddr: 0x11194, symSize: 0x50 }
  - { offsetInCU: 0xF17, offset: 0x7F30, size: 0x8, addend: 0x0, symName: '_$sSaySSGSayxGSKsWl', symObjAddr: 0x54BC, symBinAddr: 0x1159C, symSize: 0x4C }
  - { offsetInCU: 0xF53, offset: 0x7F6C, size: 0x8, addend: 0x0, symName: '_$ss13_parseInteger5ascii5radixq_Sgx_SitSyRzs010FixedWidthB0R_r0_lFSS_SiTg5', symObjAddr: 0x554C, symBinAddr: 0x115E8, symSize: 0xE4 }
  - { offsetInCU: 0x1002, offset: 0x801B, size: 0x8, addend: 0x0, symName: '_$ss13_parseInteger5ascii5radixq_Sgx_SitSyRzs010FixedWidthB0R_r0_lFADSRys5UInt8VGXEfU_SS_SiTg5', symObjAddr: 0x5630, symBinAddr: 0x116CC, symSize: 0x284 }
  - { offsetInCU: 0x119B, offset: 0x81B4, size: 0x8, addend: 0x0, symName: '_$ss22_ContiguousArrayBufferV19_uninitializedCount15minimumCapacityAByxGSi_SitcfCs5UInt8V_Tgmq5', symObjAddr: 0x5BE0, symBinAddr: 0x11C7C, symSize: 0x64 }
  - { offsetInCU: 0x130F, offset: 0x8328, size: 0x8, addend: 0x0, symName: '_$sSS11withCStringyxxSPys4Int8VGKXEKlFSb_Tg5024$sSdySdSgxcSyRzlufcSbSpyf6GXEfU_j5SPys4C7VGXEfU_SpySdGTf1cn_n', symObjAddr: 0x5F38, symBinAddr: 0x11FD4, symSize: 0x10C }
  - { offsetInCU: 0x146B, offset: 0x8484, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC5startyyFySo27FBSDKGraphRequestConnecting_pSg_ypSgs5Error_pSgtcfU_TA', symObjAddr: 0x607C, symBinAddr: 0x120E4, symSize: 0xC }
  - { offsetInCU: 0x147E, offset: 0x8497, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x6088, symBinAddr: 0x120F0, symSize: 0x10 }
  - { offsetInCU: 0x1491, offset: 0x84AA, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x6098, symBinAddr: 0x12100, symSize: 0x8 }
  - { offsetInCU: 0x14A4, offset: 0x84BD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesVWOh', symObjAddr: 0x60A0, symBinAddr: 0x12108, symSize: 0x2C }
  - { offsetInCU: 0x14B7, offset: 0x84D0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13DevicePolling_pWOc', symObjAddr: 0x6108, symBinAddr: 0x12134, symSize: 0x44 }
  - { offsetInCU: 0x14CA, offset: 0x84E3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC12schedulePoll8intervalySu_tFyycfU_TA', symObjAddr: 0x61C0, symBinAddr: 0x121C8, symSize: 0xC }
  - { offsetInCU: 0x162C, offset: 0x8645, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC14notifyDelegate5token14expirationDate020dataAccessExpirationJ0ySSSg_10Foundation0J0VSgALtFyAA0cdE6ResultCcfU_TA', symObjAddr: 0x6A0C, symBinAddr: 0x12A14, symSize: 0x8 }
  - { offsetInCU: 0x1654, offset: 0x866D, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DateVSgWOb', symObjAddr: 0x6B38, symBinAddr: 0x12B40, symSize: 0x48 }
  - { offsetInCU: 0x1667, offset: 0x8680, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC14notifyDelegate5token14expirationDate020dataAccessExpirationJ0ySSSg_10Foundation0J0VSgALtFySo27FBSDKGraphRequestConnecting_pSg_ypSgs5Error_pSgtcfU0_TA', symObjAddr: 0x6B80, symBinAddr: 0x12B88, symSize: 0xA0 }
  - { offsetInCU: 0x1696, offset: 0x86AF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerCMU', symObjAddr: 0x6E14, symBinAddr: 0x12E1C, symSize: 0x8 }
  - { offsetInCU: 0x16A9, offset: 0x86C2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerCMa', symObjAddr: 0x6E1C, symBinAddr: 0x12E24, symSize: 0x3C }
  - { offsetInCU: 0x16BC, offset: 0x86D5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerCMr', symObjAddr: 0x6E58, symBinAddr: 0x12E60, symSize: 0xA4 }
  - { offsetInCU: 0x16CF, offset: 0x86E8, size: 0x8, addend: 0x0, symName: '_$s10Foundation3URLVSgMa', symObjAddr: 0x6F10, symBinAddr: 0x12F18, symSize: 0x54 }
  - { offsetInCU: 0x16E2, offset: 0x86FB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesVwCP', symObjAddr: 0x6F64, symBinAddr: 0x12F6C, symSize: 0x30 }
  - { offsetInCU: 0x16F5, offset: 0x870E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesVwxx', symObjAddr: 0x6F94, symBinAddr: 0x12F9C, symSize: 0x3C }
  - { offsetInCU: 0x1708, offset: 0x8721, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesVwcp', symObjAddr: 0x6FD0, symBinAddr: 0x12FD8, symSize: 0x6C }
  - { offsetInCU: 0x171B, offset: 0x8734, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesVwca', symObjAddr: 0x703C, symBinAddr: 0x13044, symSize: 0x90 }
  - { offsetInCU: 0x172E, offset: 0x8747, size: 0x8, addend: 0x0, symName: ___swift_assign_boxed_opaque_existential_1, symObjAddr: 0x70CC, symBinAddr: 0x130D4, symSize: 0x168 }
  - { offsetInCU: 0x1741, offset: 0x875A, size: 0x8, addend: 0x0, symName: ___swift_memcpy72_8, symObjAddr: 0x7234, symBinAddr: 0x1323C, symSize: 0x24 }
  - { offsetInCU: 0x1754, offset: 0x876D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesVwta', symObjAddr: 0x7258, symBinAddr: 0x13260, symSize: 0x70 }
  - { offsetInCU: 0x1767, offset: 0x8780, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesVwet', symObjAddr: 0x72C8, symBinAddr: 0x132D0, symSize: 0x48 }
  - { offsetInCU: 0x177A, offset: 0x8793, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesVwst', symObjAddr: 0x7310, symBinAddr: 0x13318, symSize: 0x50 }
  - { offsetInCU: 0x178D, offset: 0x87A6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesVMa', symObjAddr: 0x7360, symBinAddr: 0x13368, symSize: 0x10 }
  - { offsetInCU: 0x17A0, offset: 0x87B9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26DeviceLoginManagerDelegate_pSgXwWOh', symObjAddr: 0x7370, symBinAddr: 0x13378, symSize: 0x24 }
  - { offsetInCU: 0x17B3, offset: 0x87CC, size: 0x8, addend: 0x0, symName: '_$s10Foundation25NSFastEnumerationIteratorVACStAAWl', symObjAddr: 0x7414, symBinAddr: 0x1341C, symSize: 0x48 }
  - { offsetInCU: 0x17C6, offset: 0x87DF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC12schedulePoll8intervalySu_tFyycfU_ySo27FBSDKGraphRequestConnecting_pSg_ypSgs5Error_pSgtcfU_TA', symObjAddr: 0x7490, symBinAddr: 0x13488, symSize: 0xC }
  - { offsetInCU: 0x17D9, offset: 0x87F2, size: 0x8, addend: 0x0, symName: '_$sSdySdSgxcSyRzlufcSbSpySdGXEfU_SbSPys4Int8VGXEfU_TA', symObjAddr: 0x74D4, symBinAddr: 0x134CC, symSize: 0x6C }
  - { offsetInCU: 0x1ACB, offset: 0x8AE4, size: 0x8, addend: 0x0, symName: '_$ss17FixedWidthIntegerPsEyxSgSScfCSi_Tgm5', symObjAddr: 0x4B88, symBinAddr: 0x10C68, symSize: 0x3B8 }
  - { offsetInCU: 0x219C, offset: 0x91B5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC8delegateAA0cdE8Delegate_pSgvg', symObjAddr: 0x5C, symBinAddr: 0xC17C, symSize: 0x48 }
  - { offsetInCU: 0x21E1, offset: 0x91FA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC8delegateAA0cdE8Delegate_pSgvs', symObjAddr: 0xF4, symBinAddr: 0xC214, symSize: 0x58 }
  - { offsetInCU: 0x2207, offset: 0x9220, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC8delegateAA0cdE8Delegate_pSgvM', symObjAddr: 0x14C, symBinAddr: 0xC26C, symSize: 0x70 }
  - { offsetInCU: 0x222A, offset: 0x9243, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC8delegateAA0cdE8Delegate_pSgvM.resume.0', symObjAddr: 0x1BC, symBinAddr: 0xC2DC, symSize: 0x6C }
  - { offsetInCU: 0x225B, offset: 0x9274, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC11permissionsSaySSGvg', symObjAddr: 0x270, symBinAddr: 0xC390, symSize: 0x10 }
  - { offsetInCU: 0x2288, offset: 0x92A1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC11redirectURL10Foundation0G0VSgvg', symObjAddr: 0x364, symBinAddr: 0xC484, symSize: 0x58 }
  - { offsetInCU: 0x22C7, offset: 0x92E0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC11redirectURL10Foundation0G0VSgvs', symObjAddr: 0x508, symBinAddr: 0xC5E8, symSize: 0x60 }
  - { offsetInCU: 0x22ED, offset: 0x9306, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC11redirectURL10Foundation0G0VSgvM', symObjAddr: 0x568, symBinAddr: 0xC648, symSize: 0x44 }
  - { offsetInCU: 0x2310, offset: 0x9329, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC11redirectURL10Foundation0G0VSgvM.resume.0', symObjAddr: 0x5AC, symBinAddr: 0xC68C, symSize: 0x4 }
  - { offsetInCU: 0x2341, offset: 0x935A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC8codeInfoAA0cd4CodeG0CSgvg', symObjAddr: 0x5F8, symBinAddr: 0xC6D8, symSize: 0x50 }
  - { offsetInCU: 0x2380, offset: 0x9399, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC8codeInfoAA0cd4CodeG0CSgvs', symObjAddr: 0x6AC, symBinAddr: 0xC78C, symSize: 0x50 }
  - { offsetInCU: 0x23A6, offset: 0x93BF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC8codeInfoAA0cd4CodeG0CSgvM', symObjAddr: 0x6FC, symBinAddr: 0xC7DC, symSize: 0x44 }
  - { offsetInCU: 0x23C9, offset: 0x93E2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC22configuredDependenciesAC06ObjectG0VSgvM', symObjAddr: 0x79C, symBinAddr: 0xC87C, symSize: 0x44 }
  - { offsetInCU: 0x23EC, offset: 0x9405, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV12devicePoller12errorFactory012graphRequestK015internalUtility8settingsAeA0C7Polling_p_So18FBSDKErrorCreating_pSo010FBSDKGraphmK0_pSo013FBSDKInternalO0_p09FBSDKCoreB016SettingsProtocol_ptcfC', symObjAddr: 0x7E0, symBinAddr: 0xC8C0, symSize: 0x4C }
  - { offsetInCU: 0x23FF, offset: 0x9418, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC19defaultDependenciesAC06ObjectG0VSgvM', symObjAddr: 0x90C, symBinAddr: 0xC9EC, symSize: 0x44 }
  - { offsetInCU: 0x2428, offset: 0x9441, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC11permissions011enableSmartD0ACSaySSG_SbtcfC', symObjAddr: 0x950, symBinAddr: 0xCA30, symSize: 0x40 }
  - { offsetInCU: 0x245F, offset: 0x9478, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC11permissions011enableSmartD0ACSaySSG_Sbtcfc', symObjAddr: 0x990, symBinAddr: 0xCA70, symSize: 0x1B0 }
  - { offsetInCU: 0x25AE, offset: 0x95C7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC5startyyF', symObjAddr: 0xB84, symBinAddr: 0xCC64, symSize: 0x5C0 }
  - { offsetInCU: 0x2814, offset: 0x982D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC5startyyFySo27FBSDKGraphRequestConnecting_pSg_ypSgs5Error_pSgtcfU_', symObjAddr: 0x1144, symBinAddr: 0xD224, symSize: 0xC6C }
  - { offsetInCU: 0x2CE3, offset: 0x9CFC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC12processErroryys0G0_pF', symObjAddr: 0x1DB0, symBinAddr: 0xDE90, symSize: 0x318 }
  - { offsetInCU: 0x2E56, offset: 0x9E6F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC14notifyDelegate33_2E1868FF91A815585B124C0140A60DCBLL5errorys5Error_p_tF', symObjAddr: 0x2124, symBinAddr: 0xE204, symSize: 0x238 }
  - { offsetInCU: 0x2FC1, offset: 0x9FDA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC12schedulePoll8intervalySu_tF', symObjAddr: 0x235C, symBinAddr: 0xE43C, symSize: 0x1BC }
  - { offsetInCU: 0x3081, offset: 0xA09A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC12schedulePoll8intervalySu_tFyycfU_', symObjAddr: 0x3CE4, symBinAddr: 0xFDC4, symSize: 0x360 }
  - { offsetInCU: 0x31C5, offset: 0xA1DE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC12schedulePoll8intervalySu_tFyycfU_ySo27FBSDKGraphRequestConnecting_pSg_ypSgs5Error_pSgtcfU_', symObjAddr: 0x4044, symBinAddr: 0x10124, symSize: 0xB44 }
  - { offsetInCU: 0x3685, offset: 0xA69E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC6cancelyyF', symObjAddr: 0x25FC, symBinAddr: 0xE6DC, symSize: 0x1D4 }
  - { offsetInCU: 0x3824, offset: 0xA83D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC14notifyDelegate5token14expirationDate020dataAccessExpirationJ0ySSSg_10Foundation0J0VSgALtF', symObjAddr: 0x27F8, symBinAddr: 0xE8D8, symSize: 0x758 }
  - { offsetInCU: 0x3C1C, offset: 0xAC35, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC14notifyDelegate5token14expirationDate020dataAccessExpirationJ0ySSSg_10Foundation0J0VSgALtFySo27FBSDKGraphRequestConnecting_pSg_ypSgs5Error_pSgtcfU0_', symObjAddr: 0x2F50, symBinAddr: 0xF030, symSize: 0xB94 }
  - { offsetInCU: 0x41DF, offset: 0xB1F8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC14notifyDelegate5token14expirationDate020dataAccessExpirationJ0ySSSg_10Foundation0J0VSgALtFyAA0cdE6ResultCcfU_Tf4nnd_n', symObjAddr: 0x64C8, symBinAddr: 0x124D0, symSize: 0x158 }
  - { offsetInCU: 0x43BA, offset: 0xB3D3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerCACycfC', symObjAddr: 0x4F70, symBinAddr: 0x11050, symSize: 0x20 }
  - { offsetInCU: 0x43CD, offset: 0xB3E6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerCACycfc', symObjAddr: 0x4F90, symBinAddr: 0x11070, symSize: 0x2C }
  - { offsetInCU: 0x4421, offset: 0xB43A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerCfD', symObjAddr: 0x4FE8, symBinAddr: 0x110C8, symSize: 0x34 }
  - { offsetInCU: 0x4442, offset: 0xB45B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC10netService_13didNotPublishySo05NSNetG0C_SDySSSo8NSNumberCGtF', symObjAddr: 0x50B0, symBinAddr: 0x11190, symSize: 0x4 }
  - { offsetInCU: 0x445C, offset: 0xB475, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV12devicePollerAA0C7Polling_pvg', symObjAddr: 0x5104, symBinAddr: 0x111E4, symSize: 0xC }
  - { offsetInCU: 0x446F, offset: 0xB488, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV12devicePollerAA0C7Polling_pvs', symObjAddr: 0x5110, symBinAddr: 0x111F0, symSize: 0x2C }
  - { offsetInCU: 0x4482, offset: 0xB49B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV12devicePollerAA0C7Polling_pvM', symObjAddr: 0x513C, symBinAddr: 0x1121C, symSize: 0x10 }
  - { offsetInCU: 0x4495, offset: 0xB4AE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV12devicePollerAA0C7Polling_pvM.resume.0', symObjAddr: 0x514C, symBinAddr: 0x1122C, symSize: 0x4 }
  - { offsetInCU: 0x44A8, offset: 0xB4C1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV12errorFactorySo18FBSDKErrorCreating_pvg', symObjAddr: 0x5150, symBinAddr: 0x11230, symSize: 0x8 }
  - { offsetInCU: 0x44BB, offset: 0xB4D4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV12errorFactorySo18FBSDKErrorCreating_pvs', symObjAddr: 0x5158, symBinAddr: 0x11238, symSize: 0x28 }
  - { offsetInCU: 0x44CE, offset: 0xB4E7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV12errorFactorySo18FBSDKErrorCreating_pvM', symObjAddr: 0x5180, symBinAddr: 0x11260, symSize: 0x10 }
  - { offsetInCU: 0x44E1, offset: 0xB4FA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV12errorFactorySo18FBSDKErrorCreating_pvM.resume.0', symObjAddr: 0x5190, symBinAddr: 0x11270, symSize: 0x4 }
  - { offsetInCU: 0x44F4, offset: 0xB50D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV19graphRequestFactorySo010FBSDKGraphiJ0_pvg', symObjAddr: 0x5194, symBinAddr: 0x11274, symSize: 0x8 }
  - { offsetInCU: 0x4507, offset: 0xB520, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV19graphRequestFactorySo010FBSDKGraphiJ0_pvs', symObjAddr: 0x519C, symBinAddr: 0x1127C, symSize: 0x28 }
  - { offsetInCU: 0x451A, offset: 0xB533, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV19graphRequestFactorySo010FBSDKGraphiJ0_pvM', symObjAddr: 0x51C4, symBinAddr: 0x112A4, symSize: 0x10 }
  - { offsetInCU: 0x452D, offset: 0xB546, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV19graphRequestFactorySo010FBSDKGraphiJ0_pvM.resume.0', symObjAddr: 0x51D4, symBinAddr: 0x112B4, symSize: 0x4 }
  - { offsetInCU: 0x4540, offset: 0xB559, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV15internalUtilitySo013FBSDKInternalI0_pvg', symObjAddr: 0x51D8, symBinAddr: 0x112B8, symSize: 0x8 }
  - { offsetInCU: 0x4553, offset: 0xB56C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV15internalUtilitySo013FBSDKInternalI0_pvs', symObjAddr: 0x51E0, symBinAddr: 0x112C0, symSize: 0x28 }
  - { offsetInCU: 0x4566, offset: 0xB57F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV15internalUtilitySo013FBSDKInternalI0_pvM', symObjAddr: 0x5208, symBinAddr: 0x112E8, symSize: 0x10 }
  - { offsetInCU: 0x4579, offset: 0xB592, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV15internalUtilitySo013FBSDKInternalI0_pvM.resume.0', symObjAddr: 0x5218, symBinAddr: 0x112F8, symSize: 0x4 }
  - { offsetInCU: 0x458C, offset: 0xB5A5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV8settings09FBSDKCoreB016SettingsProtocol_pvg', symObjAddr: 0x521C, symBinAddr: 0x112FC, symSize: 0x8 }
  - { offsetInCU: 0x459F, offset: 0xB5B8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV8settings09FBSDKCoreB016SettingsProtocol_pvs', symObjAddr: 0x5224, symBinAddr: 0x11304, symSize: 0x28 }
  - { offsetInCU: 0x45B2, offset: 0xB5CB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV8settings09FBSDKCoreB016SettingsProtocol_pvM', symObjAddr: 0x524C, symBinAddr: 0x1132C, symSize: 0x10 }
  - { offsetInCU: 0x45C5, offset: 0xB5DE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV8settings09FBSDKCoreB016SettingsProtocol_pvM.resume.0', symObjAddr: 0x525C, symBinAddr: 0x1133C, symSize: 0x4 }
  - { offsetInCU: 0x461E, offset: 0xB637, size: 0x8, addend: 0x0, symName: '_$sSa20_reserveCapacityImpl07minimumB013growForAppendySi_SbtF13FBSDKLoginKit18DeviceLoginManagerC_Tg5', symObjAddr: 0x5370, symBinAddr: 0x11450, symSize: 0xBC }
  - { offsetInCU: 0x46BE, offset: 0xB6D7, size: 0x8, addend: 0x0, symName: '_$sSa034_makeUniqueAndReserveCapacityIfNotB0yyF13FBSDKLoginKit18DeviceLoginManagerC_Tg5', symObjAddr: 0x542C, symBinAddr: 0x1150C, symSize: 0x90 }
  - { offsetInCU: 0x474E, offset: 0xB767, size: 0x8, addend: 0x0, symName: '_$sSS8_copyingyS2SFZ', symObjAddr: 0x58B4, symBinAddr: 0x11950, symSize: 0x8C }
  - { offsetInCU: 0x4762, offset: 0xB77B, size: 0x8, addend: 0x0, symName: '_$sSlsEy11SubSequenceQzqd__cSXRd__5BoundQyd__5IndexRtzluigSS_s16PartialRangeFromVySSAEVGTgq5', symObjAddr: 0x5940, symBinAddr: 0x119DC, symSize: 0x4C }
  - { offsetInCU: 0x4790, offset: 0xB7A9, size: 0x8, addend: 0x0, symName: '_$sSS8_copyingySSSsFZ', symObjAddr: 0x598C, symBinAddr: 0x11A28, symSize: 0x164 }
  - { offsetInCU: 0x47E7, offset: 0xB800, size: 0x8, addend: 0x0, symName: '_$sSlsE5countSivgSs8UTF8ViewV_Tgq5', symObjAddr: 0x5AF0, symBinAddr: 0x11B8C, symSize: 0xF0 }
  - { offsetInCU: 0x4812, offset: 0xB82B, size: 0x8, addend: 0x0, symName: '_$sSTsE21_copySequenceContents12initializing8IteratorQz_SitSry7ElementQzG_tFSs8UTF8ViewV_Tgq5', symObjAddr: 0x5C44, symBinAddr: 0x11CE0, symSize: 0x214 }
  - { offsetInCU: 0x486B, offset: 0xB884, size: 0x8, addend: 0x0, symName: '_$ss11_StringGutsV27_slowEnsureMatchingEncodingySS5IndexVAEF', symObjAddr: 0x5E58, symBinAddr: 0x11EF4, symSize: 0x78 }
  - { offsetInCU: 0x487F, offset: 0xB898, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV20_consumeAndCreateNewAByxGyF13FBSDKLoginKit18DeviceLoginManagerC_Tg5', symObjAddr: 0x5ED0, symBinAddr: 0x11F6C, symSize: 0x68 }
  - { offsetInCU: 0x4920, offset: 0xB939, size: 0x8, addend: 0x0, symName: '_$ss20_ArrayBufferProtocolPsE15replaceSubrange_4with10elementsOfySnySiG_Siqd__ntSlRd__7ElementQyd__AGRtzlFs01_aB0Vy13FBSDKLoginKit18DeviceLoginManagerCG_s15EmptyCollectionVyANGTg5Tf4nndn_n', symObjAddr: 0x61CC, symBinAddr: 0x121D4, symSize: 0x1FC }
  - { offsetInCU: 0x4BBB, offset: 0xBBD4, size: 0x8, addend: 0x0, symName: '_$sSa15replaceSubrange_4withySnySiG_qd__nt7ElementQyd__RszSlRd__lF13FBSDKLoginKit18DeviceLoginManagerC_s15EmptyCollectionVyAHGTg5Tf4ndn_n', symObjAddr: 0x63C8, symBinAddr: 0x123D0, symSize: 0x100 }
  - { offsetInCU: 0x4CC0, offset: 0xBCD9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC10netService_13didNotPublishySo05NSNetG0C_SDySSSo8NSNumberCGtFTf4ndn_n', symObjAddr: 0x6C20, symBinAddr: 0x12C28, symSize: 0x14C }
  - { offsetInCU: 0x27, offset: 0xBE6B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultC5token11isCancelledACSo16FBSDKAccessTokenCSg_SbtcfC', symObjAddr: 0x0, symBinAddr: 0x13558, symSize: 0xC8 }
  - { offsetInCU: 0x9B, offset: 0xBEDF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultC11accessTokenSo011FBSDKAccessH0CSgvgTo', symObjAddr: 0xC8, symBinAddr: 0x13620, symSize: 0x48 }
  - { offsetInCU: 0xEF, offset: 0xBF33, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultC11accessTokenSo011FBSDKAccessH0CSgvsTo', symObjAddr: 0x160, symBinAddr: 0x136B8, symSize: 0x64 }
  - { offsetInCU: 0x145, offset: 0xBF89, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultC11isCancelledSbvgTo', symObjAddr: 0x1C4, symBinAddr: 0x1371C, symSize: 0x44 }
  - { offsetInCU: 0x199, offset: 0xBFDD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultC11isCancelledSbvsTo', symObjAddr: 0x24C, symBinAddr: 0x137A4, symSize: 0x4C }
  - { offsetInCU: 0x1EE, offset: 0xC032, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultC5token11isCancelledACSo16FBSDKAccessTokenCSg_SbtcfcTo', symObjAddr: 0x360, symBinAddr: 0x138B8, symSize: 0xD4 }
  - { offsetInCU: 0x271, offset: 0xC0B5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultCACycfcTo', symObjAddr: 0x480, symBinAddr: 0x139D8, symSize: 0x2C }
  - { offsetInCU: 0x2FE, offset: 0xC142, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultCfETo', symObjAddr: 0x4E0, symBinAddr: 0x13A38, symSize: 0x10 }
  - { offsetInCU: 0x32B, offset: 0xC16F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultCMa', symObjAddr: 0x4F0, symBinAddr: 0x13A48, symSize: 0x20 }
  - { offsetInCU: 0x43D, offset: 0xC281, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultC5token11isCancelledACSo16FBSDKAccessTokenCSg_SbtcfC', symObjAddr: 0x0, symBinAddr: 0x13558, symSize: 0xC8 }
  - { offsetInCU: 0x482, offset: 0xC2C6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultC11accessTokenSo011FBSDKAccessH0CSgvg', symObjAddr: 0x110, symBinAddr: 0x13668, symSize: 0x50 }
  - { offsetInCU: 0x4D4, offset: 0xC318, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultC11isCancelledSbvg', symObjAddr: 0x208, symBinAddr: 0x13760, symSize: 0x44 }
  - { offsetInCU: 0x52C, offset: 0xC370, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultC5token11isCancelledACSo16FBSDKAccessTokenCSg_Sbtcfc', symObjAddr: 0x298, symBinAddr: 0x137F0, symSize: 0xC8 }
  - { offsetInCU: 0x558, offset: 0xC39C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultCACycfC', symObjAddr: 0x434, symBinAddr: 0x1398C, symSize: 0x20 }
  - { offsetInCU: 0x56B, offset: 0xC3AF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultCACycfc', symObjAddr: 0x454, symBinAddr: 0x139AC, symSize: 0x2C }
  - { offsetInCU: 0x5BF, offset: 0xC403, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultCfD', symObjAddr: 0x4AC, symBinAddr: 0x13A04, symSize: 0x34 }
  - { offsetInCU: 0x27, offset: 0xC44B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12DevicePollerVACycfC', symObjAddr: 0x0, symBinAddr: 0x13A7C, symSize: 0x4 }
  - { offsetInCU: 0x75, offset: 0xC499, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12DevicePollerVAA0C7PollingA2aDP8schedule8interval5blockySu_yyctFTW', symObjAddr: 0x8, symBinAddr: 0x13A84, symSize: 0x4 }
  - { offsetInCU: 0x94, offset: 0xC4B8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12DevicePollerVAA0C7PollingA2aDP8schedule8interval5blockySu_yyctFTW', symObjAddr: 0x8, symBinAddr: 0x13A84, symSize: 0x4 }
  - { offsetInCU: 0xA5, offset: 0xC4C9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12DevicePollerV8schedule8interval5blockySu_yyctFTf4nnd_n', symObjAddr: 0xC, symBinAddr: 0x13A88, symSize: 0x2C0 }
  - { offsetInCU: 0x140, offset: 0xC564, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12DevicePollerVMa', symObjAddr: 0x2CC, symBinAddr: 0x13D48, symSize: 0x10 }
  - { offsetInCU: 0x153, offset: 0xC577, size: 0x8, addend: 0x0, symName: '_$sSo17OS_dispatch_queueCMa', symObjAddr: 0x2DC, symBinAddr: 0x13D58, symSize: 0x3C }
  - { offsetInCU: 0x166, offset: 0xC58A, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x318, symBinAddr: 0x13D94, symSize: 0x10 }
  - { offsetInCU: 0x179, offset: 0xC59D, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x328, symBinAddr: 0x13DA4, symSize: 0x8 }
  - { offsetInCU: 0x18C, offset: 0xC5B0, size: 0x8, addend: 0x0, symName: '_$s8Dispatch0A13WorkItemFlagsVACs10SetAlgebraAAWl', symObjAddr: 0x330, symBinAddr: 0x13DAC, symSize: 0x48 }
  - { offsetInCU: 0x19F, offset: 0xC5C3, size: 0x8, addend: 0x0, symName: '_$sSay8Dispatch0A13WorkItemFlagsVGSayxGSTsWl', symObjAddr: 0x3B8, symBinAddr: 0x13DF4, symSize: 0x4C }
  - { offsetInCU: 0x25B, offset: 0xC67F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12DevicePollerVACycfC', symObjAddr: 0x0, symBinAddr: 0x13A7C, symSize: 0x4 }
  - { offsetInCU: 0x27F, offset: 0xC6A3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12DevicePollerV8schedule8interval5blockySu_yyctF', symObjAddr: 0x4, symBinAddr: 0x13A80, symSize: 0x4 }
  - { offsetInCU: 0x2B, offset: 0xC737, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperO03getC4InfoSSyFZ', symObjAddr: 0x0, symBinAddr: 0x13E48, symSize: 0x4 }
  - { offsetInCU: 0x4D, offset: 0xC759, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperO25mdnsAdvertisementServicesSo10NSMapTableCySo20NSNetServiceDelegate_pyXlGvpZ', symObjAddr: 0x4E88, symBinAddr: 0x60670, symSize: 0x0 }
  - { offsetInCU: 0x67, offset: 0xC773, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperO16NetServiceValues33_D51BDA1F8F5A5E4604761D8AFA9F40BBLLO10sdkVersionSSvpZ', symObjAddr: 0xAA0, symBinAddr: 0x5E2B8, symSize: 0x0 }
  - { offsetInCU: 0x121, offset: 0xC82D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperO16NetServiceValues33_D51BDA1F8F5A5E4604761D8AFA9F40BBLLO10sdkVersion_WZ', symObjAddr: 0x180, symBinAddr: 0x13FC8, symSize: 0x144 }
  - { offsetInCU: 0x16D, offset: 0xC879, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperO27cleanUpAdvertisementService3forySo05NSNetI8Delegate_p_tFZTf4en_n', symObjAddr: 0x304, symBinAddr: 0x1414C, symSize: 0xF4 }
  - { offsetInCU: 0x1E8, offset: 0xC8F4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperO25startAdvertisementService9loginCode8delegateSbSS_So05NSNetH8Delegate_ptFZTf4nen_n', symObjAddr: 0x3F8, symBinAddr: 0x14240, symSize: 0x240 }
  - { offsetInCU: 0x377, offset: 0xCA83, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperO10isDelegate_23forAdvertisementServiceSbSo05NSNetjG0_p_So0kJ0CtFZTf4enn_n', symObjAddr: 0x638, symBinAddr: 0x14480, symSize: 0xC8 }
  - { offsetInCU: 0x3F4, offset: 0xCB00, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperO03getC4InfoSSyFZTf4d_n', symObjAddr: 0x700, symBinAddr: 0x14548, symSize: 0x2C0 }
  - { offsetInCU: 0x61F, offset: 0xCD2B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperO25mdnsAdvertisementServices_WZ', symObjAddr: 0xA0, symBinAddr: 0x13EE8, symSize: 0x38 }
  - { offsetInCU: 0x639, offset: 0xCD45, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperO25mdnsAdvertisementServicesSo10NSMapTableCySo20NSNetServiceDelegate_pyXlGvau', symObjAddr: 0xD8, symBinAddr: 0x13F20, symSize: 0x40 }
  - { offsetInCU: 0x6E9, offset: 0xCDF5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperOMa', symObjAddr: 0x9C0, symBinAddr: 0x14808, symSize: 0x10 }
  - { offsetInCU: 0x6FC, offset: 0xCE08, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataVACSTAAWl', symObjAddr: 0xA14, symBinAddr: 0x14818, symSize: 0x44 }
  - { offsetInCU: 0x89E, offset: 0xCFAA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperO03getC4InfoSSyFZ', symObjAddr: 0x0, symBinAddr: 0x13E48, symSize: 0x4 }
  - { offsetInCU: 0x8B1, offset: 0xCFBD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperO25startAdvertisementService9loginCode8delegateSbSS_So05NSNetH8Delegate_ptFZ', symObjAddr: 0x4, symBinAddr: 0x13E4C, symSize: 0x44 }
  - { offsetInCU: 0x8CA, offset: 0xCFD6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperO27cleanUpAdvertisementService3forySo05NSNetI8Delegate_p_tFZ', symObjAddr: 0x48, symBinAddr: 0x13E90, symSize: 0x28 }
  - { offsetInCU: 0x8DD, offset: 0xCFE9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperO10isDelegate_23forAdvertisementServiceSbSo05NSNetjG0_p_So0kJ0CtFZ', symObjAddr: 0x70, symBinAddr: 0x13EB8, symSize: 0x30 }
  - { offsetInCU: 0x8F6, offset: 0xD002, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperO25mdnsAdvertisementServicesSo10NSMapTableCySo20NSNetServiceDelegate_pyXlGvgZ', symObjAddr: 0x118, symBinAddr: 0x13F60, symSize: 0x68 }
  - { offsetInCU: 0x926, offset: 0xD032, size: 0x8, addend: 0x0, symName: '_$sSlsE5first7ElementQzSgvgSS_Tg5', symObjAddr: 0x2C4, symBinAddr: 0x1410C, symSize: 0x40 }
  - { offsetInCU: 0x62, offset: 0xD194, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15defaultAudienceAA07DefaultF0OvgTo', symObjAddr: 0x0, symBinAddr: 0x1485C, symSize: 0x9C }
  - { offsetInCU: 0xAF, offset: 0xD1E1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15defaultAudienceAA07DefaultF0OvsTo', symObjAddr: 0x13C, symBinAddr: 0x14974, symSize: 0xA8 }
  - { offsetInCU: 0x127, offset: 0xD259, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC8delegateAA05LoginD8Delegate_pSgvgTo', symObjAddr: 0x4B0, symBinAddr: 0x14CE8, symSize: 0x48 }
  - { offsetInCU: 0x17B, offset: 0xD2AD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC8delegateAA05LoginD8Delegate_pSgvsTo', symObjAddr: 0x540, symBinAddr: 0x14D78, symSize: 0x50 }
  - { offsetInCU: 0x1FB, offset: 0xD32D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC11permissionsSaySSGvgTo', symObjAddr: 0x6C4, symBinAddr: 0x14EFC, symSize: 0x68 }
  - { offsetInCU: 0x24F, offset: 0xD381, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC11permissionsSaySSGvsTo', symObjAddr: 0x740, symBinAddr: 0x14F78, symSize: 0x64 }
  - { offsetInCU: 0x305, offset: 0xD437, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15TooltipBehaviorOSYAASY8rawValuexSg03RawH0Qz_tcfCTW', symObjAddr: 0x1C4C, symBinAddr: 0x16400, symSize: 0x20 }
  - { offsetInCU: 0x334, offset: 0xD466, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15TooltipBehaviorOSYAASY8rawValue03RawH0QzvgTW', symObjAddr: 0x1C6C, symBinAddr: 0x16420, symSize: 0xC }
  - { offsetInCU: 0x351, offset: 0xD483, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15tooltipBehaviorAC07TooltipF0OvgTo', symObjAddr: 0x800, symBinAddr: 0x15038, symSize: 0x44 }
  - { offsetInCU: 0x3A6, offset: 0xD4D8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15tooltipBehaviorAC07TooltipF0OvsTo', symObjAddr: 0x888, symBinAddr: 0x150C0, symSize: 0x48 }
  - { offsetInCU: 0x411, offset: 0xD543, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC17tooltipColorStyleAA13FBTooltipViewC0fG0OvgTo', symObjAddr: 0x95C, symBinAddr: 0x15194, symSize: 0x44 }
  - { offsetInCU: 0x465, offset: 0xD597, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC17tooltipColorStyleAA13FBTooltipViewC0fG0OvsTo', symObjAddr: 0x9E4, symBinAddr: 0x1521C, symSize: 0x48 }
  - { offsetInCU: 0x4D0, offset: 0xD602, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC13loginTrackingAA05LoginF0OvgTo', symObjAddr: 0xAB8, symBinAddr: 0x152F0, symSize: 0x44 }
  - { offsetInCU: 0x524, offset: 0xD656, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC13loginTrackingAA05LoginF0OvsTo', symObjAddr: 0xB40, symBinAddr: 0x15378, symSize: 0x48 }
  - { offsetInCU: 0x58F, offset: 0xD6C1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5nonceSSSgvgTo', symObjAddr: 0xC14, symBinAddr: 0x1544C, symSize: 0x5C }
  - { offsetInCU: 0x5BF, offset: 0xD6F1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5nonceSSSgvsTo', symObjAddr: 0xCA8, symBinAddr: 0x154E0, symSize: 0x64 }
  - { offsetInCU: 0x648, offset: 0xD77A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC8authTypeSo0a4AuthF0aSgvgTo', symObjAddr: 0x10B8, symBinAddr: 0x1586C, symSize: 0x48 }
  - { offsetInCU: 0x69C, offset: 0xD7CE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC8authTypeSo0a4AuthF0aSgvsTo', symObjAddr: 0x1150, symBinAddr: 0x15904, symSize: 0x64 }
  - { offsetInCU: 0x707, offset: 0xD839, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC12codeVerifierAA04CodeF0CvgTo', symObjAddr: 0x1248, symBinAddr: 0x159FC, symSize: 0x48 }
  - { offsetInCU: 0x75C, offset: 0xD88E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC12codeVerifierAA04CodeF0CvsTo', symObjAddr: 0x12D4, symBinAddr: 0x15A88, symSize: 0x64 }
  - { offsetInCU: 0x830, offset: 0xD962, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC19graphRequestFactorySo010FBSDKGraphfG0_pvgTo', symObjAddr: 0x18B4, symBinAddr: 0x16068, symSize: 0x48 }
  - { offsetInCU: 0x884, offset: 0xD9B6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC19graphRequestFactorySo010FBSDKGraphfG0_pvsTo', symObjAddr: 0x1958, symBinAddr: 0x1610C, symSize: 0x64 }
  - { offsetInCU: 0x8EF, offset: 0xDA21, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15isAuthenticatedSbvgTo', symObjAddr: 0x1A6C, symBinAddr: 0x16220, symSize: 0xA8 }
  - { offsetInCU: 0x9AF, offset: 0xDAE1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5frameACSo6CGRectV_tcfcTo', symObjAddr: 0x23C8, symBinAddr: 0x16B7C, symSize: 0x20 }
  - { offsetInCU: 0x9FC, offset: 0xDB2E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5coderACSgSo7NSCoderC_tcfcTo', symObjAddr: 0x245C, symBinAddr: 0x16C10, symSize: 0x3C }
  - { offsetInCU: 0xAF6, offset: 0xDC28, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15didMoveToWindowyyFTo', symObjAddr: 0x2BC4, symBinAddr: 0x17378, symSize: 0x28 }
  - { offsetInCU: 0xB2B, offset: 0xDC5D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC9imageRect010forContentF0So6CGRectVAG_tFTo', symObjAddr: 0x2C14, symBinAddr: 0x173C8, symSize: 0x28 }
  - { offsetInCU: 0xB93, offset: 0xDCC5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC9titleRect010forContentF0So6CGRectVAG_tFTo', symObjAddr: 0x2D2C, symBinAddr: 0x174E0, symSize: 0x110 }
  - { offsetInCU: 0xC97, offset: 0xDDC9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC14layoutSubviewsyyFTo', symObjAddr: 0x3238, symBinAddr: 0x179EC, symSize: 0x28 }
  - { offsetInCU: 0xCCC, offset: 0xDDFE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC12sizeThatFitsySo6CGSizeVAFFTo', symObjAddr: 0x377C, symBinAddr: 0x17F30, symSize: 0x54 }
  - { offsetInCU: 0xD1A, offset: 0xDE4C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC20accessTokenDidChangeyy10Foundation12NotificationVFTo', symObjAddr: 0x3AA0, symBinAddr: 0x18254, symSize: 0xA4 }
  - { offsetInCU: 0xD51, offset: 0xDE83, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC16profileDidChangeyy10Foundation12NotificationVFTo', symObjAddr: 0x3D8C, symBinAddr: 0x18540, symSize: 0xA0 }
  - { offsetInCU: 0xDB6, offset: 0xDEE8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC13buttonPressedyyypFTo', symObjAddr: 0x4874, symBinAddr: 0x19028, symSize: 0x64 }
  - { offsetInCU: 0xDE8, offset: 0xDF1A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC22makeLoginConfigurationAA0fG0CSgyFTo', symObjAddr: 0x4BCC, symBinAddr: 0x19380, symSize: 0x34 }
  - { offsetInCU: 0xE03, offset: 0xDF35, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC9logInUser33_2D5546723C2E9E390359F57C16888789LLyyFyAA11LoginResultOcfU_', symObjAddr: 0x4C00, symBinAddr: 0x193B4, symSize: 0x104 }
  - { offsetInCU: 0xE58, offset: 0xDF8A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC17initializeContentyyFTo', symObjAddr: 0x4EE8, symBinAddr: 0x1969C, symSize: 0x28 }
  - { offsetInCU: 0xE8A, offset: 0xDFBC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC27updateContentForAccessTokenyyFTo', symObjAddr: 0x5128, symBinAddr: 0x198DC, symSize: 0x28 }
  - { offsetInCU: 0xEA5, offset: 0xDFD7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC18fetchAndSetContentyyFTo', symObjAddr: 0x53B8, symBinAddr: 0x19B6C, symSize: 0x28 }
  - { offsetInCU: 0xEDC, offset: 0xE00E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC20updateContentForUseryy09FBSDKCoreB07ProfileCSgFTo', symObjAddr: 0x5520, symBinAddr: 0x19CD4, symSize: 0x54 }
  - { offsetInCU: 0xEF7, offset: 0xE029, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC6logoutyyFTo', symObjAddr: 0x5574, symBinAddr: 0x19D28, symSize: 0x28 }
  - { offsetInCU: 0xF42, offset: 0xE074, size: 0x8, addend: 0x0, symName: ___swift_mutable_project_boxed_opaque_existential_1, symObjAddr: 0x260, symBinAddr: 0x14A98, symSize: 0x28 }
  - { offsetInCU: 0xF55, offset: 0xE087, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15defaultAudienceAA07DefaultF0OvpACTK', symObjAddr: 0x288, symBinAddr: 0x14AC0, symSize: 0x84 }
  - { offsetInCU: 0xF8B, offset: 0xE0BD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15defaultAudienceAA07DefaultF0OvpACTk', symObjAddr: 0x30C, symBinAddr: 0x14B44, symSize: 0x80 }
  - { offsetInCU: 0x108F, offset: 0xE1C1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit29UserInterfaceElementProviding_pWOb', symObjAddr: 0x1668, symBinAddr: 0x15E1C, symSize: 0x18 }
  - { offsetInCU: 0x10A2, offset: 0xE1D4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28UserInterfaceStringProviding_pWOb', symObjAddr: 0x1798, symBinAddr: 0x15F4C, symSize: 0x18 }
  - { offsetInCU: 0x10B5, offset: 0xE1E7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14LoginProviding_pWOb', symObjAddr: 0x1858, symBinAddr: 0x1600C, symSize: 0x18 }
  - { offsetInCU: 0x110C, offset: 0xE23E, size: 0x8, addend: 0x0, symName: '_$sSo20FBSDKInternalUtilityCMa', symObjAddr: 0x1F38, symBinAddr: 0x166EC, symSize: 0x3C }
  - { offsetInCU: 0x1456, offset: 0xE588, size: 0x8, addend: 0x0, symName: '_$sSo13UIAlertActionCIegg_ABIeyBy_TR', symObjAddr: 0x4D04, symBinAddr: 0x194B8, symSize: 0x4C }
  - { offsetInCU: 0x14DB, offset: 0xE60D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonCfETo', symObjAddr: 0x55D0, symBinAddr: 0x19D84, symSize: 0xE8 }
  - { offsetInCU: 0x1699, offset: 0xE7CB, size: 0x8, addend: 0x0, symName: '_$sypSgWOb', symObjAddr: 0x5BF8, symBinAddr: 0x1A378, symSize: 0x48 }
  - { offsetInCU: 0x16AC, offset: 0xE7DE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC9logInUser33_2D5546723C2E9E390359F57C16888789LLyyFyAA11LoginResultOcfU_TA', symObjAddr: 0x5CE0, symBinAddr: 0x1A3E4, symSize: 0x8 }
  - { offsetInCU: 0x170F, offset: 0xE841, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC18fetchAndSetContentyyFySo27FBSDKGraphRequestConnecting_pSg_ypSgs5Error_pSgtcfU_TA', symObjAddr: 0x5E88, symBinAddr: 0x1A58C, symSize: 0x8 }
  - { offsetInCU: 0x1722, offset: 0xE854, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x5E90, symBinAddr: 0x1A594, symSize: 0x10 }
  - { offsetInCU: 0x1735, offset: 0xE867, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x5EA0, symBinAddr: 0x1A5A4, symSize: 0x8 }
  - { offsetInCU: 0x1748, offset: 0xE87A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15TooltipBehaviorOSHAASQWb', symObjAddr: 0x5EA8, symBinAddr: 0x1A5AC, symSize: 0x4 }
  - { offsetInCU: 0x175B, offset: 0xE88D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15TooltipBehaviorOAESQAAWl', symObjAddr: 0x5EAC, symBinAddr: 0x1A5B0, symSize: 0x44 }
  - { offsetInCU: 0x176E, offset: 0xE8A0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonCMa', symObjAddr: 0x5FF8, symBinAddr: 0x1A6FC, symSize: 0x20 }
  - { offsetInCU: 0x1781, offset: 0xE8B3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15TooltipBehaviorOMa', symObjAddr: 0x6018, symBinAddr: 0x1A71C, symSize: 0x10 }
  - { offsetInCU: 0x1794, offset: 0xE8C6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19LoginButtonDelegate_pSgXwWOh', symObjAddr: 0x6048, symBinAddr: 0x1A74C, symSize: 0x24 }
  - { offsetInCU: 0x17A7, offset: 0xE8D9, size: 0x8, addend: 0x0, symName: '_$sypSgWOc', symObjAddr: 0x606C, symBinAddr: 0x1A770, symSize: 0x48 }
  - { offsetInCU: 0x17BA, offset: 0xE8EC, size: 0x8, addend: 0x0, symName: '_$s12FBSDKCoreKit10PermissionOACSHAAWl', symObjAddr: 0x60B4, symBinAddr: 0x1A7B8, symSize: 0x48 }
  - { offsetInCU: 0x17CD, offset: 0xE8FF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC26presentAlertViewController33_2D5546723C2E9E390359F57C16888789LLyyFySo13UIAlertActionCcfU_TA', symObjAddr: 0x60FC, symBinAddr: 0x1A800, symSize: 0x8 }
  - { offsetInCU: 0x17E0, offset: 0xE912, size: 0x8, addend: 0x0, symName: '_$sS2Ss7CVarArg10FoundationWl', symObjAddr: 0x6104, symBinAddr: 0x1A808, symSize: 0x44 }
  - { offsetInCU: 0x17F3, offset: 0xE925, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultOWOe', symObjAddr: 0x6148, symBinAddr: 0x1A84C, symSize: 0x48 }
  - { offsetInCU: 0x18F5, offset: 0xEA27, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15TooltipBehaviorOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x1B8C, symBinAddr: 0x16340, symSize: 0x14 }
  - { offsetInCU: 0x1997, offset: 0xEAC9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15TooltipBehaviorOSHAASH9hashValueSivgTW', symObjAddr: 0x1BA0, symBinAddr: 0x16354, symSize: 0x44 }
  - { offsetInCU: 0x1A3E, offset: 0xEB70, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15TooltipBehaviorOSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0x1BE4, symBinAddr: 0x16398, symSize: 0x28 }
  - { offsetInCU: 0x1A8D, offset: 0xEBBF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15TooltipBehaviorOSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0x1C0C, symBinAddr: 0x163C0, symSize: 0x40 }
  - { offsetInCU: 0x1D3D, offset: 0xEE6F, size: 0x8, addend: 0x0, symName: '_$sShyShyxGqd__nc7ElementQyd__RszSTRd__lufC12FBSDKCoreKit10PermissionO_SayAFGTgm5Tf4g_n', symObjAddr: 0x5CE8, symBinAddr: 0x1A3EC, symSize: 0x10C }
  - { offsetInCU: 0x1E6F, offset: 0xEFA1, size: 0x8, addend: 0x0, symName: '_$sShyShyxGqd__nc7ElementQyd__RszSTRd__lufCSS_SaySSGTgm5Tf4g_n', symObjAddr: 0x5DF4, symBinAddr: 0x1A4F8, symSize: 0x94 }
  - { offsetInCU: 0x21E4, offset: 0xF316, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15defaultAudienceAA07DefaultF0Ovg', symObjAddr: 0x9C, symBinAddr: 0x148F8, symSize: 0x7C }
  - { offsetInCU: 0x2229, offset: 0xF35B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15defaultAudienceAA07DefaultF0Ovs', symObjAddr: 0x1E4, symBinAddr: 0x14A1C, symSize: 0x7C }
  - { offsetInCU: 0x224F, offset: 0xF381, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15defaultAudienceAA07DefaultF0OvM', symObjAddr: 0x38C, symBinAddr: 0x14BC4, symSize: 0xA0 }
  - { offsetInCU: 0x2287, offset: 0xF3B9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15defaultAudienceAA07DefaultF0OvM.resume.0', symObjAddr: 0x42C, symBinAddr: 0x14C64, symSize: 0x84 }
  - { offsetInCU: 0x22C3, offset: 0xF3F5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC8delegateAA05LoginD8Delegate_pSgvg', symObjAddr: 0x4F8, symBinAddr: 0x14D30, symSize: 0x48 }
  - { offsetInCU: 0x2302, offset: 0xF434, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC8delegateAA05LoginD8Delegate_pSgvs', symObjAddr: 0x590, symBinAddr: 0x14DC8, symSize: 0x58 }
  - { offsetInCU: 0x2328, offset: 0xF45A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC8delegateAA05LoginD8Delegate_pSgvM', symObjAddr: 0x5E8, symBinAddr: 0x14E20, symSize: 0x70 }
  - { offsetInCU: 0x234B, offset: 0xF47D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC8delegateAA05LoginD8Delegate_pSgvM.resume.0', symObjAddr: 0x658, symBinAddr: 0x14E90, symSize: 0x6C }
  - { offsetInCU: 0x239E, offset: 0xF4D0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC11permissionsSaySSGvM', symObjAddr: 0x7B8, symBinAddr: 0x14FF0, symSize: 0x44 }
  - { offsetInCU: 0x23C1, offset: 0xF4F3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC11permissionsSaySSGvM.resume.0', symObjAddr: 0x7FC, symBinAddr: 0x15034, symSize: 0x4 }
  - { offsetInCU: 0x23F2, offset: 0xF524, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15tooltipBehaviorAC07TooltipF0Ovg', symObjAddr: 0x844, symBinAddr: 0x1507C, symSize: 0x44 }
  - { offsetInCU: 0x2431, offset: 0xF563, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15tooltipBehaviorAC07TooltipF0Ovs', symObjAddr: 0x8D0, symBinAddr: 0x15108, symSize: 0x48 }
  - { offsetInCU: 0x2457, offset: 0xF589, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15tooltipBehaviorAC07TooltipF0OvM', symObjAddr: 0x918, symBinAddr: 0x15150, symSize: 0x44 }
  - { offsetInCU: 0x248C, offset: 0xF5BE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC17tooltipColorStyleAA13FBTooltipViewC0fG0Ovg', symObjAddr: 0x9A0, symBinAddr: 0x151D8, symSize: 0x44 }
  - { offsetInCU: 0x24CB, offset: 0xF5FD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC17tooltipColorStyleAA13FBTooltipViewC0fG0Ovs', symObjAddr: 0xA2C, symBinAddr: 0x15264, symSize: 0x48 }
  - { offsetInCU: 0x24F1, offset: 0xF623, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC17tooltipColorStyleAA13FBTooltipViewC0fG0OvM', symObjAddr: 0xA74, symBinAddr: 0x152AC, symSize: 0x44 }
  - { offsetInCU: 0x2526, offset: 0xF658, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC13loginTrackingAA05LoginF0Ovg', symObjAddr: 0xAFC, symBinAddr: 0x15334, symSize: 0x44 }
  - { offsetInCU: 0x2565, offset: 0xF697, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC13loginTrackingAA05LoginF0Ovs', symObjAddr: 0xB88, symBinAddr: 0x153C0, symSize: 0x48 }
  - { offsetInCU: 0x258B, offset: 0xF6BD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC13loginTrackingAA05LoginF0OvM', symObjAddr: 0xBD0, symBinAddr: 0x15408, symSize: 0x44 }
  - { offsetInCU: 0x25C0, offset: 0xF6F2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5nonceSSSgvg', symObjAddr: 0xC70, symBinAddr: 0x154A8, symSize: 0x38 }
  - { offsetInCU: 0x2630, offset: 0xF762, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5nonceSSSgvs', symObjAddr: 0xD0C, symBinAddr: 0x15544, symSize: 0x20C }
  - { offsetInCU: 0x276A, offset: 0xF89C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5nonceSSSgvM', symObjAddr: 0xF9C, symBinAddr: 0x15750, symSize: 0x48 }
  - { offsetInCU: 0x27A8, offset: 0xF8DA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5nonceSSSgvM.resume.0', symObjAddr: 0xFE4, symBinAddr: 0x15798, symSize: 0x60 }
  - { offsetInCU: 0x27C7, offset: 0xF8F9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15messengerPageIdSSSgvM', symObjAddr: 0x1074, symBinAddr: 0x15828, symSize: 0x44 }
  - { offsetInCU: 0x27FC, offset: 0xF92E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC8authTypeSo0a4AuthF0aSgvg', symObjAddr: 0x1100, symBinAddr: 0x158B4, symSize: 0x50 }
  - { offsetInCU: 0x283B, offset: 0xF96D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC8authTypeSo0a4AuthF0aSgvs', symObjAddr: 0x11B4, symBinAddr: 0x15968, symSize: 0x50 }
  - { offsetInCU: 0x2861, offset: 0xF993, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC8authTypeSo0a4AuthF0aSgvM', symObjAddr: 0x1204, symBinAddr: 0x159B8, symSize: 0x44 }
  - { offsetInCU: 0x2896, offset: 0xF9C8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC12codeVerifierAA04CodeF0Cvg', symObjAddr: 0x1290, symBinAddr: 0x15A44, symSize: 0x44 }
  - { offsetInCU: 0x28D5, offset: 0xFA07, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC12codeVerifierAA04CodeF0Cvs', symObjAddr: 0x1338, symBinAddr: 0x15AEC, symSize: 0x50 }
  - { offsetInCU: 0x28FB, offset: 0xFA2D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC12codeVerifierAA04CodeF0CvM', symObjAddr: 0x1388, symBinAddr: 0x15B3C, symSize: 0x44 }
  - { offsetInCU: 0x291E, offset: 0xFA50, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC6userIDSSSgvM', symObjAddr: 0x13FC, symBinAddr: 0x15BB0, symSize: 0x44 }
  - { offsetInCU: 0x2941, offset: 0xFA73, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC8userNameSSSgvM', symObjAddr: 0x1604, symBinAddr: 0x15DB8, symSize: 0x44 }
  - { offsetInCU: 0x2964, offset: 0xFA96, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15elementProviderAA29UserInterfaceElementProviding_pvM', symObjAddr: 0x1680, symBinAddr: 0x15E34, symSize: 0x44 }
  - { offsetInCU: 0x2987, offset: 0xFAB9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC14stringProviderAA28UserInterfaceStringProviding_pvM', symObjAddr: 0x17B0, symBinAddr: 0x15F64, symSize: 0x44 }
  - { offsetInCU: 0x29AA, offset: 0xFADC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC13loginProviderAA14LoginProviding_pvM', symObjAddr: 0x1870, symBinAddr: 0x16024, symSize: 0x44 }
  - { offsetInCU: 0x2A01, offset: 0xFB33, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC19graphRequestFactorySo010FBSDKGraphfG0_pvM', symObjAddr: 0x1A28, symBinAddr: 0x161DC, symSize: 0x44 }
  - { offsetInCU: 0x2A36, offset: 0xFB68, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15isAuthenticatedSbvg', symObjAddr: 0x1B14, symBinAddr: 0x162C8, symSize: 0x60 }
  - { offsetInCU: 0x2A56, offset: 0xFB88, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15TooltipBehaviorO8rawValueAESgSu_tcfC', symObjAddr: 0x1B74, symBinAddr: 0x16328, symSize: 0x14 }
  - { offsetInCU: 0x2A73, offset: 0xFBA5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15TooltipBehaviorO8rawValueSuvg', symObjAddr: 0x1B88, symBinAddr: 0x1633C, symSize: 0x4 }
  - { offsetInCU: 0x2AEB, offset: 0xFC1D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5frameACSo6CGRectV_tcfC', symObjAddr: 0x1C78, symBinAddr: 0x1642C, symSize: 0x50 }
  - { offsetInCU: 0x2B1C, offset: 0xFC4E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5frameACSo6CGRectV_tcfc', symObjAddr: 0x1CC8, symBinAddr: 0x1647C, symSize: 0x270 }
  - { offsetInCU: 0x2BE1, offset: 0xFD13, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC09configureD033_2D5546723C2E9E390359F57C16888789LLyyF', symObjAddr: 0x1F74, symBinAddr: 0x16728, symSize: 0x454 }
  - { offsetInCU: 0x2C8F, offset: 0xFDC1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5coderACSgSo7NSCoderC_tcfC', symObjAddr: 0x23E8, symBinAddr: 0x16B9C, symSize: 0x44 }
  - { offsetInCU: 0x2CA2, offset: 0xFDD4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5coderACSgSo7NSCoderC_tcfc', symObjAddr: 0x242C, symBinAddr: 0x16BE0, symSize: 0x30 }
  - { offsetInCU: 0x2CBB, offset: 0xFDED, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5frame15elementProvider06stringG005loginG019graphRequestFactoryACSo6CGRectV_AA29UserInterfaceElementProviding_pAA0no6StringQ0_pAA05LoginQ0_pSo010FBSDKGraphkL0_ptcfC', symObjAddr: 0x2498, symBinAddr: 0x16C4C, symSize: 0x16C }
  - { offsetInCU: 0x2DA0, offset: 0xFED2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5frame11permissionsACSo6CGRectV_Say09FBSDKCoreB010PermissionOGtcfC', symObjAddr: 0x2604, symBinAddr: 0x16DB8, symSize: 0x228 }
  - { offsetInCU: 0x2FB3, offset: 0x100E5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC17initializeContentyyF', symObjAddr: 0x282C, symBinAddr: 0x16FE0, symSize: 0x17C }
  - { offsetInCU: 0x3005, offset: 0x10137, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15didMoveToWindowyyF', symObjAddr: 0x29A8, symBinAddr: 0x1715C, symSize: 0xA8 }
  - { offsetInCU: 0x308F, offset: 0x101C1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC19showTooltipIfNeeded33_2D5546723C2E9E390359F57C16888789LLyyF', symObjAddr: 0x2A50, symBinAddr: 0x17204, symSize: 0x174 }
  - { offsetInCU: 0x31E4, offset: 0x10316, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC9imageRect010forContentF0So6CGRectVAG_tF', symObjAddr: 0x2BEC, symBinAddr: 0x173A0, symSize: 0x28 }
  - { offsetInCU: 0x3255, offset: 0x10387, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC9titleRect010forContentF0So6CGRectVAG_tF', symObjAddr: 0x2C3C, symBinAddr: 0x173F0, symSize: 0xF0 }
  - { offsetInCU: 0x3399, offset: 0x104CB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC14layoutSubviewsyyF', symObjAddr: 0x2E3C, symBinAddr: 0x175F0, symSize: 0x3FC }
  - { offsetInCU: 0x3477, offset: 0x105A9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC12sizeThatFitsySo6CGSizeVAFF', symObjAddr: 0x3260, symBinAddr: 0x17A14, symSize: 0x51C }
  - { offsetInCU: 0x3592, offset: 0x106C4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC20accessTokenDidChangeyy10Foundation12NotificationVF', symObjAddr: 0x37D0, symBinAddr: 0x17F84, symSize: 0x18C }
  - { offsetInCU: 0x365F, offset: 0x10791, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC27updateContentForAccessTokenyyF', symObjAddr: 0x395C, symBinAddr: 0x18110, symSize: 0x144 }
  - { offsetInCU: 0x36C1, offset: 0x107F3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC16profileDidChangeyy10Foundation12NotificationVF', symObjAddr: 0x3B44, symBinAddr: 0x182F8, symSize: 0x140 }
  - { offsetInCU: 0x3719, offset: 0x1084B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC20updateContentForUseryy09FBSDKCoreB07ProfileCSgF', symObjAddr: 0x3C84, symBinAddr: 0x18438, symSize: 0x108 }
  - { offsetInCU: 0x3781, offset: 0x108B3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC13buttonPressedyyypF', symObjAddr: 0x3E2C, symBinAddr: 0x185E0, symSize: 0x254 }
  - { offsetInCU: 0x382E, offset: 0x10960, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC26presentAlertViewController33_2D5546723C2E9E390359F57C16888789LLyyF', symObjAddr: 0x4080, symBinAddr: 0x18834, symSize: 0x7F4 }
  - { offsetInCU: 0x39A6, offset: 0x10AD8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC26presentAlertViewController33_2D5546723C2E9E390359F57C16888789LLyyFySo13UIAlertActionCcfU_', symObjAddr: 0x4D50, symBinAddr: 0x19504, symSize: 0xCC }
  - { offsetInCU: 0x3A4B, offset: 0x10B7D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC22makeLoginConfigurationAA0fG0CSgyF', symObjAddr: 0x48D8, symBinAddr: 0x1908C, symSize: 0x2F4 }
  - { offsetInCU: 0x3CA2, offset: 0x10DD4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC6logoutyyF', symObjAddr: 0x4E1C, symBinAddr: 0x195D0, symSize: 0xCC }
  - { offsetInCU: 0x3CE9, offset: 0x10E1B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC18fetchAndSetContentyyF', symObjAddr: 0x4F10, symBinAddr: 0x196C4, symSize: 0x218 }
  - { offsetInCU: 0x3DC8, offset: 0x10EFA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC18fetchAndSetContentyyFySo27FBSDKGraphRequestConnecting_pSg_ypSgs5Error_pSgtcfU_', symObjAddr: 0x5150, symBinAddr: 0x19904, symSize: 0x268 }
  - { offsetInCU: 0x3EE1, offset: 0x11013, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC27userInformationDoesNotMatch33_2D5546723C2E9E390359F57C16888789LLySb09FBSDKCoreB07ProfileCF', symObjAddr: 0x53E0, symBinAddr: 0x19B94, symSize: 0x140 }
  - { offsetInCU: 0x3F6B, offset: 0x1109D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonCfD', symObjAddr: 0x559C, symBinAddr: 0x19D50, symSize: 0x34 }
  - { offsetInCU: 0x3F8C, offset: 0x110BE, size: 0x8, addend: 0x0, symName: '_$ss15ContiguousArrayV16_createNewBuffer14bufferIsUnique15minimumCapacity13growForAppendySb_SiSbtFSS_Tg5', symObjAddr: 0x56B8, symBinAddr: 0x19E6C, symSize: 0x1C }
  - { offsetInCU: 0x3FA0, offset: 0x110D2, size: 0x8, addend: 0x0, symName: '_$ss15ContiguousArrayV16_createNewBuffer14bufferIsUnique15minimumCapacity13growForAppendySb_SiSbtF12FBSDKCoreKit10PermissionO_Tg5', symObjAddr: 0x56D4, symBinAddr: 0x19E88, symSize: 0x1C }
  - { offsetInCU: 0x3FDE, offset: 0x11110, size: 0x8, addend: 0x0, symName: '_$ss22_ContiguousArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtFSS_Tg5', symObjAddr: 0x56F0, symBinAddr: 0x19EA4, symSize: 0x104 }
  - { offsetInCU: 0x40D7, offset: 0x11209, size: 0x8, addend: 0x0, symName: '_$ss22_ContiguousArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtF12FBSDKCoreKit10PermissionO_Tg5', symObjAddr: 0x57F4, symBinAddr: 0x19FA8, symSize: 0x174 }
  - { offsetInCU: 0x41B6, offset: 0x112E8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5coderACSgSo7NSCoderC_tcfcTf4gn_n', symObjAddr: 0x5968, symBinAddr: 0x1A11C, symSize: 0x25C }
  - { offsetInCU: 0x77, offset: 0x114E1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC8delegateAA05LogindE8Delegate_pSgvgTo', symObjAddr: 0x20, symBinAddr: 0x1A8F4, symSize: 0x48 }
  - { offsetInCU: 0xCB, offset: 0x11535, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC8delegateAA05LogindE8Delegate_pSgvsTo', symObjAddr: 0xB0, symBinAddr: 0x1A984, symSize: 0x50 }
  - { offsetInCU: 0x14B, offset: 0x115B5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC12forceDisplaySbvgTo', symObjAddr: 0x234, symBinAddr: 0x1AB08, symSize: 0x44 }
  - { offsetInCU: 0x292, offset: 0x116FC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewCACycfcTo', symObjAddr: 0x554, symBinAddr: 0x1AE28, symSize: 0xA8 }
  - { offsetInCU: 0x370, offset: 0x117DA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC7tagline7message10colorStyleACSSSg_AgA09FBTooltipE0C05ColorI0OtcfcTo', symObjAddr: 0x848, symBinAddr: 0x1B11C, symSize: 0x84 }
  - { offsetInCU: 0x3EF, offset: 0x11859, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC7present2in13arrowPosition9directionySo6UIViewC_So7CGPointVAA09FBTooltipE0C14ArrowDirectionOtFTo', symObjAddr: 0xB98, symBinAddr: 0x1B46C, symSize: 0x70 }
  - { offsetInCU: 0x40A, offset: 0x11874, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC05fetchD13Configuration015_3798D0DCC12906H16A17D23FC0B3F34C1LL4with13arrowPosition9directionySo6UIViewC_So7CGPointVAA09FBTooltipE0C14ArrowDirectionOtFySo0aD0CSg_s5Error_pSgtcfU_', symObjAddr: 0xC08, symBinAddr: 0x1B4DC, symSize: 0x278 }
  - { offsetInCU: 0x55F, offset: 0x119C9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC27serverConfigurationProvider06stringH0AcA06ServerG9Providing_p_AA019UserInterfaceStringK0_ptcfcTf4een_n09FBSDKCoreB00jgH0C_So20FBSDKInternalUtilityCTg5', symObjAddr: 0xF38, symBinAddr: 0x1B80C, symSize: 0x178 }
  - { offsetInCU: 0x5F9, offset: 0x11A63, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC27serverConfigurationProvider06stringH0AcA06ServerG9Providing_p_AA019UserInterfaceStringK0_ptcfcTf4een_n', symObjAddr: 0x1114, symBinAddr: 0x1B984, symSize: 0x19C }
  - { offsetInCU: 0x687, offset: 0x11AF1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC27serverConfigurationProvider06stringH0AcA06ServerG9Providing_p_AA019UserInterfaceStringK0_ptcfCTfq4een_nTf4ngn_nTf4gnn_n', symObjAddr: 0x12B0, symBinAddr: 0x1BB20, symSize: 0x104 }
  - { offsetInCU: 0x798, offset: 0x11C02, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewCfETo', symObjAddr: 0xEF0, symBinAddr: 0x1B7C4, symSize: 0x48 }
  - { offsetInCU: 0x7C6, offset: 0x11C30, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC05fetchD13Configuration015_3798D0DCC12906H16A17D23FC0B3F34C1LL4with13arrowPosition9directionySo6UIViewC_So7CGPointVAA09FBTooltipE0C14ArrowDirectionOtFySo0aD0CSg_s5Error_pSgtcfU_TA', symObjAddr: 0x1424, symBinAddr: 0x1BC50, symSize: 0x10 }
  - { offsetInCU: 0x7D9, offset: 0x11C43, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewCMa', symObjAddr: 0x1578, symBinAddr: 0x1BDA4, symSize: 0x20 }
  - { offsetInCU: 0x7EC, offset: 0x11C56, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24LoginTooltipViewDelegate_pSgXwWOh', symObjAddr: 0x15AC, symBinAddr: 0x1BDD8, symSize: 0x24 }
  - { offsetInCU: 0x7FF, offset: 0x11C69, size: 0x8, addend: 0x0, symName: ___swift_allocate_boxed_opaque_existential_1, symObjAddr: 0x15D0, symBinAddr: 0x1BDFC, symSize: 0x3C }
  - { offsetInCU: 0x9AC, offset: 0x11E16, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewCACycfC', symObjAddr: 0x0, symBinAddr: 0x1A8D4, symSize: 0x20 }
  - { offsetInCU: 0x9D6, offset: 0x11E40, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC8delegateAA05LogindE8Delegate_pSgvg', symObjAddr: 0x68, symBinAddr: 0x1A93C, symSize: 0x48 }
  - { offsetInCU: 0xA1B, offset: 0x11E85, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC8delegateAA05LogindE8Delegate_pSgvs', symObjAddr: 0x100, symBinAddr: 0x1A9D4, symSize: 0x58 }
  - { offsetInCU: 0xA41, offset: 0x11EAB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC8delegateAA05LogindE8Delegate_pSgvM', symObjAddr: 0x158, symBinAddr: 0x1AA2C, symSize: 0x70 }
  - { offsetInCU: 0xA64, offset: 0x11ECE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC8delegateAA05LogindE8Delegate_pSgvM.resume.0', symObjAddr: 0x1C8, symBinAddr: 0x1AA9C, symSize: 0x6C }
  - { offsetInCU: 0xA95, offset: 0x11EFF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC12forceDisplaySbvg', symObjAddr: 0x278, symBinAddr: 0x1AB4C, symSize: 0x44 }
  - { offsetInCU: 0xAB2, offset: 0x11F1C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC12forceDisplaySbvs', symObjAddr: 0x2C0, symBinAddr: 0x1AB94, symSize: 0x48 }
  - { offsetInCU: 0xAE0, offset: 0x11F4A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC12forceDisplaySbvM', symObjAddr: 0x308, symBinAddr: 0x1ABDC, symSize: 0x44 }
  - { offsetInCU: 0xB03, offset: 0x11F6D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC12forceDisplaySbvM.resume.0', symObjAddr: 0x34C, symBinAddr: 0x1AC20, symSize: 0x4 }
  - { offsetInCU: 0xB34, offset: 0x11F9E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC18shouldForceDisplaySbvM', symObjAddr: 0x3A0, symBinAddr: 0x1AC74, symSize: 0x6C }
  - { offsetInCU: 0xB6C, offset: 0x11FD6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC18shouldForceDisplaySbvM.resume.0', symObjAddr: 0x40C, symBinAddr: 0x1ACE0, symSize: 0x14 }
  - { offsetInCU: 0xB9E, offset: 0x12008, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC27serverConfigurationProviderAA06ServerG9Providing_pvg', symObjAddr: 0x420, symBinAddr: 0x1ACF4, symSize: 0x24 }
  - { offsetInCU: 0xBBF, offset: 0x12029, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC14stringProviderAA28UserInterfaceStringProviding_pvg', symObjAddr: 0x444, symBinAddr: 0x1AD18, symSize: 0x24 }
  - { offsetInCU: 0xBF8, offset: 0x12062, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewCACycfc', symObjAddr: 0x4AC, symBinAddr: 0x1AD80, symSize: 0xA8 }
  - { offsetInCU: 0xCD5, offset: 0x1213F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC7tagline7message10colorStyleACSSSg_AgA09FBTooltipE0C05ColorI0OtcfC', symObjAddr: 0x5FC, symBinAddr: 0x1AED0, symSize: 0x12C }
  - { offsetInCU: 0xD24, offset: 0x1218E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC7tagline7message10colorStyleACSSSg_AgA09FBTooltipE0C05ColorI0Otcfc', symObjAddr: 0x728, symBinAddr: 0x1AFFC, symSize: 0x120 }
  - { offsetInCU: 0xD71, offset: 0x121DB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC27serverConfigurationProvider06stringH0AcA06ServerG9Providing_p_AA019UserInterfaceStringK0_ptcfC', symObjAddr: 0x8CC, symBinAddr: 0x1B1A0, symSize: 0x90 }
  - { offsetInCU: 0xDAD, offset: 0x12217, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC27serverConfigurationProvider06stringH0AcA06ServerG9Providing_p_AA019UserInterfaceStringK0_ptcfc', symObjAddr: 0x95C, symBinAddr: 0x1B230, symSize: 0x11C }
  - { offsetInCU: 0xE1D, offset: 0x12287, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC7present2in13arrowPosition9directionySo6UIViewC_So7CGPointVAA09FBTooltipE0C14ArrowDirectionOtF', symObjAddr: 0xA78, symBinAddr: 0x1B34C, symSize: 0x120 }
  - { offsetInCU: 0xF15, offset: 0x1237F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewCfE', symObjAddr: 0xE80, symBinAddr: 0x1B754, symSize: 0x3C }
  - { offsetInCU: 0xF36, offset: 0x123A0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewCfD', symObjAddr: 0xEBC, symBinAddr: 0x1B790, symSize: 0x34 }
  - { offsetInCU: 0x169, offset: 0x12578, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionC4hashSivgTo', symObjAddr: 0x334, symBinAddr: 0x1C178, symSize: 0x4C }
  - { offsetInCU: 0x1D9, offset: 0x125E8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionC6stringACSgSS_tcfcTo', symObjAddr: 0x5E0, symBinAddr: 0x1C3E0, symSize: 0x28 }
  - { offsetInCU: 0x20D, offset: 0x1261C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionC11permissions18fromRawPermissionsShyACGSgShySSG_tFZTo', symObjAddr: 0x60C, symBinAddr: 0x1C40C, symSize: 0x90 }
  - { offsetInCU: 0x23D, offset: 0x1264C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionC14rawPermissions4fromShySSGShyACG_tFZTo', symObjAddr: 0x6E4, symBinAddr: 0x1C4E4, symSize: 0x98 }
  - { offsetInCU: 0x2A3, offset: 0x126B2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionC7isEqualySbypSgFTo', symObjAddr: 0x870, symBinAddr: 0x1C670, symSize: 0x80 }
  - { offsetInCU: 0x2E9, offset: 0x126F8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionCACycfcTo', symObjAddr: 0x93C, symBinAddr: 0x1C73C, symSize: 0x2C }
  - { offsetInCU: 0x361, offset: 0x12770, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionC11permissions18fromRawPermissionsShyACGSgShySSG_tFZTf4nd_n', symObjAddr: 0x9B0, symBinAddr: 0x1C7B0, symSize: 0x350 }
  - { offsetInCU: 0x535, offset: 0x12944, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionCfETo', symObjAddr: 0x99C, symBinAddr: 0x1C79C, symSize: 0x14 }
  - { offsetInCU: 0x580, offset: 0x1298F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionCMa', symObjAddr: 0xDC8, symBinAddr: 0x1CB00, symSize: 0x20 }
  - { offsetInCU: 0x593, offset: 0x129A2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionCSo8NSObjectCSH10ObjectiveCWl', symObjAddr: 0xDFC, symBinAddr: 0x1CB34, symSize: 0x44 }
  - { offsetInCU: 0x5A6, offset: 0x129B5, size: 0x8, addend: 0x0, symName: '_$sSh5IndexV8_VariantOyx__GSHRzlWOe', symObjAddr: 0xE40, symBinAddr: 0x1CB78, symSize: 0xC }
  - { offsetInCU: 0x5F8, offset: 0x12A07, size: 0x8, addend: 0x0, symName: '_$sSlsE3mapySayqd__Gqd__7ElementQzKXEKlFShy13FBSDKLoginKit12FBPermissionCG_SSTg504$s13c5Kit12E93C14rawPermissions4fromShySSGShyACG_tFZSSACcfu_32e0d58b938ad0b6cb17de1b825049cc00ACSSTf3nnpk_nTf1cn_n', symObjAddr: 0x0, symBinAddr: 0x1BE44, symSize: 0x2A8 }
  - { offsetInCU: 0x9E4, offset: 0x12DF3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionC14rawPermissions4fromShySSGShyACG_tFZ', symObjAddr: 0x69C, symBinAddr: 0x1C49C, symSize: 0x48 }
  - { offsetInCU: 0xA6E, offset: 0x12E7D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionC11descriptionSSvg', symObjAddr: 0x2FC, symBinAddr: 0x1C140, symSize: 0x38 }
  - { offsetInCU: 0xAA8, offset: 0x12EB7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionC4hashSivg', symObjAddr: 0x380, symBinAddr: 0x1C1C4, symSize: 0x4C }
  - { offsetInCU: 0xAC5, offset: 0x12ED4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionC6stringACSgSS_tcfC', symObjAddr: 0x410, symBinAddr: 0x1C210, symSize: 0x40 }
  - { offsetInCU: 0xADE, offset: 0x12EED, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionC6stringACSgSS_tcfc', symObjAddr: 0x450, symBinAddr: 0x1C250, symSize: 0x190 }
  - { offsetInCU: 0xB27, offset: 0x12F36, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionC11permissions18fromRawPermissionsShyACGSgShySSG_tFZ', symObjAddr: 0x608, symBinAddr: 0x1C408, symSize: 0x4 }
  - { offsetInCU: 0xB74, offset: 0x12F83, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionC7isEqualySbypSgF', symObjAddr: 0x77C, symBinAddr: 0x1C57C, symSize: 0xF4 }
  - { offsetInCU: 0xBCE, offset: 0x12FDD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionCACycfC', symObjAddr: 0x8F0, symBinAddr: 0x1C6F0, symSize: 0x20 }
  - { offsetInCU: 0xBE1, offset: 0x12FF0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionCACycfc', symObjAddr: 0x910, symBinAddr: 0x1C710, symSize: 0x2C }
  - { offsetInCU: 0xC35, offset: 0x13044, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionCfD', symObjAddr: 0x968, symBinAddr: 0x1C768, symSize: 0x34 }
  - { offsetInCU: 0x4E, offset: 0x13144, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO13kNUXRectInset12CoreGraphics7CGFloatVvpZ', symObjAddr: 0x50F0, symBinAddr: 0x5E430, symSize: 0x0 }
  - { offsetInCU: 0x68, offset: 0x1315E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO16kNUXBubbleMargin12CoreGraphics7CGFloatVvpZ', symObjAddr: 0x50F8, symBinAddr: 0x5E438, symSize: 0x0 }
  - { offsetInCU: 0x82, offset: 0x13178, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO15kNUXPointMargin12CoreGraphics7CGFloatVvpZ', symObjAddr: 0x5100, symBinAddr: 0x5E440, symSize: 0x0 }
  - { offsetInCU: 0x9C, offset: 0x13192, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO16kNUXCornerRadius12CoreGraphics7CGFloatVvpZ', symObjAddr: 0x5108, symBinAddr: 0x5E448, symSize: 0x0 }
  - { offsetInCU: 0xB6, offset: 0x131AC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO11kNUXSideCap12CoreGraphics7CGFloatVvpZ', symObjAddr: 0x5110, symBinAddr: 0x5E450, symSize: 0x0 }
  - { offsetInCU: 0xD0, offset: 0x131C6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO12kNUXFontSize12CoreGraphics7CGFloatVvpZ', symObjAddr: 0x5118, symBinAddr: 0x5E458, symSize: 0x0 }
  - { offsetInCU: 0xEA, offset: 0x131E0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO18kNUXCrossGlyphSize12CoreGraphics7CGFloatVvpZ', symObjAddr: 0x5120, symBinAddr: 0x5E460, symSize: 0x0 }
  - { offsetInCU: 0x104, offset: 0x131FA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO27kFriendlyBlueGradientColorsSaySo10CGColorRefaGvpZ', symObjAddr: 0x5128, symBinAddr: 0x5E468, symSize: 0x0 }
  - { offsetInCU: 0x11E, offset: 0x13214, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO12kNeutralGraySaySo10CGColorRefaGvpZ', symObjAddr: 0x5130, symBinAddr: 0x5E470, symSize: 0x0 }
  - { offsetInCU: 0x12C, offset: 0x13222, size: 0x8, addend: 0x0, symName: '_$sIeg_IeyB_TR', symObjAddr: 0x0, symBinAddr: 0x1CB88, symSize: 0x2C }
  - { offsetInCU: 0x1A8, offset: 0x1329E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC10ColorStyleOSYAASY8rawValuexSg03RawH0Qz_tcfCTW', symObjAddr: 0x22C4, symBinAddr: 0x1EE4C, symSize: 0x30 }
  - { offsetInCU: 0x1D7, offset: 0x132CD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC10ColorStyleOSYAASY8rawValue03RawH0QzvgTW', symObjAddr: 0x22F4, symBinAddr: 0x1EE7C, symSize: 0xC }
  - { offsetInCU: 0x67C, offset: 0x13772, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO13kNUXRectInset_WZ', symObjAddr: 0x2300, symBinAddr: 0x1EE88, symSize: 0x10 }
  - { offsetInCU: 0x695, offset: 0x1378B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO16kNUXBubbleMargin_WZ', symObjAddr: 0x2310, symBinAddr: 0x1EE98, symSize: 0x50 }
  - { offsetInCU: 0x6BD, offset: 0x137B3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO15kNUXPointMargin_WZ', symObjAddr: 0x2360, symBinAddr: 0x1EEE8, symSize: 0x10 }
  - { offsetInCU: 0x6D6, offset: 0x137CC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO16kNUXCornerRadius_WZ', symObjAddr: 0x2370, symBinAddr: 0x1EEF8, symSize: 0x10 }
  - { offsetInCU: 0x6EF, offset: 0x137E5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO11kNUXSideCap_WZ', symObjAddr: 0x2380, symBinAddr: 0x1EF08, symSize: 0x10 }
  - { offsetInCU: 0x708, offset: 0x137FE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO12kNUXFontSize_WZ', symObjAddr: 0x2390, symBinAddr: 0x1EF18, symSize: 0x10 }
  - { offsetInCU: 0x721, offset: 0x13817, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO18kNUXCrossGlyphSize_WZ', symObjAddr: 0x23A0, symBinAddr: 0x1EF28, symSize: 0x10 }
  - { offsetInCU: 0x73A, offset: 0x13830, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO27kFriendlyBlueGradientColors_WZ', symObjAddr: 0x23B0, symBinAddr: 0x1EF38, symSize: 0x10C }
  - { offsetInCU: 0x7F5, offset: 0x138EB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO12kNeutralGray_WZ', symObjAddr: 0x24BC, symBinAddr: 0x1F044, symSize: 0x108 }
  - { offsetInCU: 0x8B0, offset: 0x139A6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC15displayDurationSdvgTo', symObjAddr: 0x25C4, symBinAddr: 0x1F14C, symSize: 0x44 }
  - { offsetInCU: 0x8EB, offset: 0x139E1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC15displayDurationSdvsTo', symObjAddr: 0x2608, symBinAddr: 0x1F190, symSize: 0x50 }
  - { offsetInCU: 0x92D, offset: 0x13A23, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC10colorStyleAC05ColorF0OvgTo', symObjAddr: 0x2658, symBinAddr: 0x1F1E0, symSize: 0x44 }
  - { offsetInCU: 0x968, offset: 0x13A5E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC10colorStyleAC05ColorF0OvsTo', symObjAddr: 0x269C, symBinAddr: 0x1F224, symSize: 0x6C }
  - { offsetInCU: 0x9B4, offset: 0x13AAA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9textLabelSo7UILabelCvpfiAFyXEfU_', symObjAddr: 0x2810, symBinAddr: 0x1F398, symSize: 0x13C }
  - { offsetInCU: 0xA2E, offset: 0x13B24, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9textLabelSo7UILabelCvgTo', symObjAddr: 0x2990, symBinAddr: 0x1F518, symSize: 0x10 }
  - { offsetInCU: 0xA4E, offset: 0x13B44, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9textLabelSo7UILabelCvgTo', symObjAddr: 0x2990, symBinAddr: 0x1F518, symSize: 0x10 }
  - { offsetInCU: 0xA93, offset: 0x13B89, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewCACycfcTo', symObjAddr: 0x29EC, symBinAddr: 0x1F574, symSize: 0x18 }
  - { offsetInCU: 0xAB3, offset: 0x13BA9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewCACycfcTo', symObjAddr: 0x29EC, symBinAddr: 0x1F574, symSize: 0x18 }
  - { offsetInCU: 0xAF1, offset: 0x13BE7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7tagline7message10colorStyleACSSSg_AgC05ColorH0OtcfcTo', symObjAddr: 0x2A64, symBinAddr: 0x1F5EC, symSize: 0x84 }
  - { offsetInCU: 0xB3E, offset: 0x13C34, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC5coderACSgSo7NSCoderC_tcfcTo', symObjAddr: 0x2B30, symBinAddr: 0x1F6B8, symSize: 0x28 }
  - { offsetInCU: 0xB83, offset: 0x13C79, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewCfDTo', symObjAddr: 0x2BAC, symBinAddr: 0x1F734, symSize: 0x78 }
  - { offsetInCU: 0xBB3, offset: 0x13CA9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7present4fromySo6UIViewC_tFTo', symObjAddr: 0x2CB4, symBinAddr: 0x1F83C, symSize: 0x50 }
  - { offsetInCU: 0xBCE, offset: 0x13CC4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7present2in13arrowPosition9directionySo6UIViewC_So7CGPointVAC14ArrowDirectionOtFTo', symObjAddr: 0x2D04, symBinAddr: 0x1F88C, symSize: 0x70 }
  - { offsetInCU: 0xBE9, offset: 0x13CDF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7dismissyyFTo', symObjAddr: 0x2E38, symBinAddr: 0x1F9C0, symSize: 0x98 }
  - { offsetInCU: 0xC69, offset: 0x13D5F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC13animateFadeInyyFTo', symObjAddr: 0x368C, symBinAddr: 0x20214, symSize: 0x28 }
  - { offsetInCU: 0xCA0, offset: 0x13D96, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC14onTapInTooltip33_1C39B2F52DDA14663AEF238AF411735ALLyySo19UIGestureRecognizerCFTo', symObjAddr: 0x36B4, symBinAddr: 0x2023C, symSize: 0x74 }
  - { offsetInCU: 0xCEA, offset: 0x13DE0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC4drawyySo6CGRectVFTo', symObjAddr: 0x372C, symBinAddr: 0x202B0, symSize: 0x28 }
  - { offsetInCU: 0xD33, offset: 0x13E29, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC14layoutSubviewsyyFTo', symObjAddr: 0x3788, symBinAddr: 0x202D8, symSize: 0x5C }
  - { offsetInCU: 0xD67, offset: 0x13E5D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC47scheduleFadeoutRespectingMinimumDisplayDuration33_1C39B2F52DDA14663AEF238AF411735ALLyyFTo', symObjAddr: 0x37E4, symBinAddr: 0x20334, symSize: 0x84 }
  - { offsetInCU: 0xDFB, offset: 0x13EF1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC5frameACSo6CGRectV_tcfcTo', symObjAddr: 0x38E4, symBinAddr: 0x20434, symSize: 0x2C }
  - { offsetInCU: 0xE5E, offset: 0x13F54, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC35fbsdkCreateUpPointingBubbleWithRect33_1C39B2F52DDA14663AEF238AF411735ALLySo16CGMutablePathRefaSo6CGRectV_12CoreGraphics7CGFloatVA2LtFTf4nnnnd_n', symObjAddr: 0x39B0, symBinAddr: 0x20500, symSize: 0x2D4 }
  - { offsetInCU: 0x10A2, offset: 0x14198, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC37fbsdkCreateDownPointingBubbleWithRect33_1C39B2F52DDA14663AEF238AF411735ALLySo16CGMutablePathRefaSo6CGRectV_12CoreGraphics7CGFloatVA2LtFTf4nnnnd_n', symObjAddr: 0x3C84, symBinAddr: 0x207D4, symSize: 0x2D4 }
  - { offsetInCU: 0x12E6, offset: 0x143DC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC29createCloseCrossGlyphWithRect33_1C39B2F52DDA14663AEF238AF411735ALLySo16CGMutablePathRefaSo6CGRectVFTf4nd_n', symObjAddr: 0x3F58, symBinAddr: 0x20AA8, symSize: 0x2AC }
  - { offsetInCU: 0x164D, offset: 0x14743, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewCMa', symObjAddr: 0x294C, symBinAddr: 0x1F4D4, symSize: 0x20 }
  - { offsetInCU: 0x1660, offset: 0x14756, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewCfETo', symObjAddr: 0x2C24, symBinAddr: 0x1F7AC, symSize: 0x90 }
  - { offsetInCU: 0x168E, offset: 0x14784, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7dismissyyFyycfU_TA', symObjAddr: 0x2E30, symBinAddr: 0x1F9B8, symSize: 0x8 }
  - { offsetInCU: 0x16A1, offset: 0x14797, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC13animateFadeInyyFyycfU_TA', symObjAddr: 0x3120, symBinAddr: 0x1FCA8, symSize: 0x10 }
  - { offsetInCU: 0x16B4, offset: 0x147AA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC13animateFadeInyyFyycfU0_TA', symObjAddr: 0x337C, symBinAddr: 0x1FF04, symSize: 0x8 }
  - { offsetInCU: 0x16C7, offset: 0x147BD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC13animateFadeInyyFyycfU1_TA', symObjAddr: 0x3404, symBinAddr: 0x1FF8C, symSize: 0x8 }
  - { offsetInCU: 0x16DA, offset: 0x147D0, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x340C, symBinAddr: 0x1FF94, symSize: 0x10 }
  - { offsetInCU: 0x16ED, offset: 0x147E3, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x341C, symBinAddr: 0x1FFA4, symSize: 0x8 }
  - { offsetInCU: 0x1700, offset: 0x147F6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC13animateFadeInyyFySbcfU2_TA', symObjAddr: 0x3590, symBinAddr: 0x20118, symSize: 0xC }
  - { offsetInCU: 0x1713, offset: 0x14809, size: 0x8, addend: 0x0, symName: '_$sSbIegy_SbIeyBy_TR', symObjAddr: 0x3650, symBinAddr: 0x201D8, symSize: 0x3C }
  - { offsetInCU: 0x173E, offset: 0x14834, size: 0x8, addend: 0x0, symName: '_$ss29getContiguousArrayStorageType3fors01_bcD0CyxGmxm_tlF13FBSDKLoginKit18DeviceLoginManagerC_Tgm5', symObjAddr: 0x3910, symBinAddr: 0x20460, symSize: 0x50 }
  - { offsetInCU: 0x1755, offset: 0x1484B, size: 0x8, addend: 0x0, symName: '_$ss29getContiguousArrayStorageType3fors01_bcD0CyxGmxm_tlFSo10CGColorRefa_Tgm5', symObjAddr: 0x3960, symBinAddr: 0x204B0, symSize: 0x50 }
  - { offsetInCU: 0x176C, offset: 0x14862, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC14ArrowDirectionOSHAASQWb', symObjAddr: 0x4A04, symBinAddr: 0x21554, symSize: 0x4 }
  - { offsetInCU: 0x177F, offset: 0x14875, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC14ArrowDirectionOAESQAAWl', symObjAddr: 0x4A08, symBinAddr: 0x21558, symSize: 0x44 }
  - { offsetInCU: 0x1792, offset: 0x14888, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC10ColorStyleOSHAASQWb', symObjAddr: 0x4A4C, symBinAddr: 0x2159C, symSize: 0x4 }
  - { offsetInCU: 0x17A5, offset: 0x1489B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC10ColorStyleOAESQAAWl', symObjAddr: 0x4A50, symBinAddr: 0x215A0, symSize: 0x44 }
  - { offsetInCU: 0x17B8, offset: 0x148AE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC14ArrowDirectionOMa', symObjAddr: 0x4E88, symBinAddr: 0x219D8, symSize: 0x10 }
  - { offsetInCU: 0x17CB, offset: 0x148C1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC10ColorStyleOMa', symObjAddr: 0x4E98, symBinAddr: 0x219E8, symSize: 0x10 }
  - { offsetInCU: 0x17DE, offset: 0x148D4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7dismissyyFyycfU_TA.23', symObjAddr: 0x4EA8, symBinAddr: 0x219F8, symSize: 0x8 }
  - { offsetInCU: 0x17F1, offset: 0x148E7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC13animateFadeInyyFySbcfU2_ySbcfU_TA', symObjAddr: 0x4ED4, symBinAddr: 0x21A24, symSize: 0x8 }
  - { offsetInCU: 0x1804, offset: 0x148FA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC14animateFadeOut33_1C39B2F52DDA14663AEF238AF411735ALL17completionHandleryyyc_tFyycfU_TA', symObjAddr: 0x4F00, symBinAddr: 0x21A50, symSize: 0x14 }
  - { offsetInCU: 0x1834, offset: 0x1492A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC14animateFadeOut33_1C39B2F52DDA14663AEF238AF411735ALL17completionHandleryyyc_tFySbcfU0_TA', symObjAddr: 0x4F14, symBinAddr: 0x21A64, symSize: 0x20 }
  - { offsetInCU: 0x1964, offset: 0x14A5A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC10ColorStyleOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x21FC, symBinAddr: 0x1ED84, symSize: 0x14 }
  - { offsetInCU: 0x19BF, offset: 0x14AB5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC10ColorStyleOSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0x2258, symBinAddr: 0x1EDE0, symSize: 0x28 }
  - { offsetInCU: 0x1D43, offset: 0x14E39, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7tagline7message10colorStyleACSSSg_AgC05ColorH0Otcfc', symObjAddr: 0x2C, symBinAddr: 0x1CBB4, symSize: 0x504 }
  - { offsetInCU: 0x1E8F, offset: 0x14F85, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7present2in13arrowPosition9directionySo6UIViewC_So7CGPointVAC14ArrowDirectionOtF', symObjAddr: 0x530, symBinAddr: 0x1D0B8, symSize: 0xF8 }
  - { offsetInCU: 0x1F98, offset: 0x1508E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC15displayDurationSdvg', symObjAddr: 0x628, symBinAddr: 0x1D1B0, symSize: 0x44 }
  - { offsetInCU: 0x1FB5, offset: 0x150AB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC15displayDurationSdvs', symObjAddr: 0x66C, symBinAddr: 0x1D1F4, symSize: 0x50 }
  - { offsetInCU: 0x1FDB, offset: 0x150D1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC15displayDurationSdvM', symObjAddr: 0x6BC, symBinAddr: 0x1D244, symSize: 0x44 }
  - { offsetInCU: 0x1FFE, offset: 0x150F4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC15displayDurationSdvM.resume.0', symObjAddr: 0x700, symBinAddr: 0x1D288, symSize: 0x4 }
  - { offsetInCU: 0x201D, offset: 0x15113, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC10colorStyleAC05ColorF0Ovg', symObjAddr: 0x704, symBinAddr: 0x1D28C, symSize: 0x44 }
  - { offsetInCU: 0x204C, offset: 0x15142, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC10colorStyleAC05ColorF0Ovs', symObjAddr: 0x748, symBinAddr: 0x1D2D0, symSize: 0x54 }
  - { offsetInCU: 0x208D, offset: 0x15183, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC10colorStyleAC05ColorF0OvM', symObjAddr: 0x79C, symBinAddr: 0x1D324, symSize: 0x48 }
  - { offsetInCU: 0x20B0, offset: 0x151A6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC10colorStyleAC05ColorF0OvM.resume.0', symObjAddr: 0x7E4, symBinAddr: 0x1D36C, symSize: 0x30 }
  - { offsetInCU: 0x2110, offset: 0x15206, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7messageSSSgvs', symObjAddr: 0x820, symBinAddr: 0x1D3A8, symSize: 0xD8 }
  - { offsetInCU: 0x2182, offset: 0x15278, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7messageSSSgvM', symObjAddr: 0x8F8, symBinAddr: 0x1D480, symSize: 0x74 }
  - { offsetInCU: 0x21C2, offset: 0x152B8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7messageSSSgvM.resume.0', symObjAddr: 0x96C, symBinAddr: 0x1D4F4, symSize: 0x170 }
  - { offsetInCU: 0x227E, offset: 0x15374, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7taglineSSSgvs', symObjAddr: 0xB38, symBinAddr: 0x1D6C0, symSize: 0xD0 }
  - { offsetInCU: 0x22F0, offset: 0x153E6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7taglineSSSgvM', symObjAddr: 0xC08, symBinAddr: 0x1D790, symSize: 0x74 }
  - { offsetInCU: 0x2330, offset: 0x15426, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7taglineSSSgvM.resume.0', symObjAddr: 0xC7C, symBinAddr: 0x1D804, symSize: 0x160 }
  - { offsetInCU: 0x24D0, offset: 0x155C6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7present4fromySo6UIViewC_tF', symObjAddr: 0xDDC, symBinAddr: 0x1D964, symSize: 0x228 }
  - { offsetInCU: 0x260B, offset: 0x15701, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7dismissyyF', symObjAddr: 0x1004, symBinAddr: 0x1DB8C, symSize: 0x78 }
  - { offsetInCU: 0x2673, offset: 0x15769, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7dismissyyFyycfU_', symObjAddr: 0x2D74, symBinAddr: 0x1F8FC, symSize: 0xBC }
  - { offsetInCU: 0x26FC, offset: 0x157F2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC13animateFadeInyyF', symObjAddr: 0x107C, symBinAddr: 0x1DC04, symSize: 0x434 }
  - { offsetInCU: 0x2845, offset: 0x1593B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC13animateFadeInyyFyycfU_', symObjAddr: 0x2ED0, symBinAddr: 0x1FA58, symSize: 0x224 }
  - { offsetInCU: 0x2924, offset: 0x15A1A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC13animateFadeInyyFyycfU0_', symObjAddr: 0x3130, symBinAddr: 0x1FCB8, symSize: 0x220 }
  - { offsetInCU: 0x29EA, offset: 0x15AE0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC13animateFadeInyyFyycfU1_', symObjAddr: 0x3384, symBinAddr: 0x1FF0C, symSize: 0x80 }
  - { offsetInCU: 0x2A17, offset: 0x15B0D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC13animateFadeInyyFySbcfU2_', symObjAddr: 0x3424, symBinAddr: 0x1FFAC, symSize: 0x140 }
  - { offsetInCU: 0x2A62, offset: 0x15B58, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC13animateFadeInyyFySbcfU2_ySbcfU_', symObjAddr: 0x359C, symBinAddr: 0x20124, symSize: 0xB4 }
  - { offsetInCU: 0x2AA8, offset: 0x15B9E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC14animateFadeOut33_1C39B2F52DDA14663AEF238AF411735ALL17completionHandleryyyc_tF', symObjAddr: 0x14B0, symBinAddr: 0x1E038, symSize: 0x168 }
  - { offsetInCU: 0x2B80, offset: 0x15C76, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC12updateColors33_1C39B2F52DDA14663AEF238AF411735ALLyyF', symObjAddr: 0x1618, symBinAddr: 0x1E1A0, symSize: 0x238 }
  - { offsetInCU: 0x2D84, offset: 0x15E7A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC31layoutSubviewsAndDetermineFrame33_1C39B2F52DDA14663AEF238AF411735ALLSo6CGRectVyF', symObjAddr: 0x1850, symBinAddr: 0x1E3D8, symSize: 0x494 }
  - { offsetInCU: 0x30C7, offset: 0x161BD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC3set33_1C39B2F52DDA14663AEF238AF411735ALL7message7taglineySSSg_AHtF', symObjAddr: 0x1CE4, symBinAddr: 0x1E86C, symSize: 0x338 }
  - { offsetInCU: 0x32D4, offset: 0x163CA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC24scheduleAutomaticFadeout33_1C39B2F52DDA14663AEF238AF411735ALLyyF', symObjAddr: 0x201C, symBinAddr: 0x1EBA4, symSize: 0x14C }
  - { offsetInCU: 0x339A, offset: 0x16490, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC32cancelAllScheduledFadeOutMethods33_1C39B2F52DDA14663AEF238AF411735ALLyyF', symObjAddr: 0x2168, symBinAddr: 0x1ECF0, symSize: 0x64 }
  - { offsetInCU: 0x33BF, offset: 0x164B5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC14ArrowDirectionO8rawValueSuvg', symObjAddr: 0x21CC, symBinAddr: 0x1ED54, symSize: 0x4 }
  - { offsetInCU: 0x33E4, offset: 0x164DA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC10ColorStyleO8rawValueAESgSu_tcfC', symObjAddr: 0x21D8, symBinAddr: 0x1ED60, symSize: 0x20 }
  - { offsetInCU: 0x3401, offset: 0x164F7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC10ColorStyleO8rawValueSuvg', symObjAddr: 0x21F8, symBinAddr: 0x1ED80, symSize: 0x4 }
  - { offsetInCU: 0x34DF, offset: 0x165D5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9textLabelSo7UILabelCvg', symObjAddr: 0x29A0, symBinAddr: 0x1F528, symSize: 0x10 }
  - { offsetInCU: 0x3500, offset: 0x165F6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewCACycfC', symObjAddr: 0x29B0, symBinAddr: 0x1F538, symSize: 0x20 }
  - { offsetInCU: 0x3513, offset: 0x16609, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewCACycfc', symObjAddr: 0x29D0, symBinAddr: 0x1F558, symSize: 0x1C }
  - { offsetInCU: 0x3553, offset: 0x16649, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7tagline7message10colorStyleACSSSg_AgC05ColorH0OtcfC', symObjAddr: 0x2A04, symBinAddr: 0x1F58C, symSize: 0x60 }
  - { offsetInCU: 0x3566, offset: 0x1665C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC5coderACSgSo7NSCoderC_tcfC', symObjAddr: 0x2AE8, symBinAddr: 0x1F670, symSize: 0x44 }
  - { offsetInCU: 0x3579, offset: 0x1666F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC5coderACSgSo7NSCoderC_tcfc', symObjAddr: 0x2B2C, symBinAddr: 0x1F6B4, symSize: 0x4 }
  - { offsetInCU: 0x3593, offset: 0x16689, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewCfD', symObjAddr: 0x2B58, symBinAddr: 0x1F6E0, symSize: 0x54 }
  - { offsetInCU: 0x3614, offset: 0x1670A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC5frameACSo6CGRectV_tcfC', symObjAddr: 0x3868, symBinAddr: 0x203B8, symSize: 0x50 }
  - { offsetInCU: 0x3627, offset: 0x1671D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC5frameACSo6CGRectV_tcfc', symObjAddr: 0x38B8, symBinAddr: 0x20408, symSize: 0x2C }
  - { offsetInCU: 0x369B, offset: 0x16791, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC5coderACSgSo7NSCoderC_tcfcTf4dn_n', symObjAddr: 0x4204, symBinAddr: 0x20D54, symSize: 0x1DC }
  - { offsetInCU: 0x3712, offset: 0x16808, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC4drawyySo6CGRectVFTf4dn_n', symObjAddr: 0x43E0, symBinAddr: 0x20F30, symSize: 0x624 }
  - { offsetInCU: 0x2B, offset: 0x16DE9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVyACyAA0d7ManagerdE0CSg_s5Error_pSgtccfC', symObjAddr: 0x0, symBinAddr: 0x21AD0, symSize: 0x44 }
  - { offsetInCU: 0xA6, offset: 0x16E64, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0xCC, symBinAddr: 0x21B9C, symSize: 0x4 }
  - { offsetInCU: 0xC5, offset: 0x16E83, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0xCC, symBinAddr: 0x21B9C, symSize: 0x4 }
  - { offsetInCU: 0xE8, offset: 0x16EA6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVMa', symObjAddr: 0x44, symBinAddr: 0x21B14, symSize: 0x3C }
  - { offsetInCU: 0xFB, offset: 0x16EB9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVwCP', symObjAddr: 0xD0, symBinAddr: 0x21BA0, symSize: 0x90 }
  - { offsetInCU: 0x10E, offset: 0x16ECC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVwxx', symObjAddr: 0x160, symBinAddr: 0x21C30, symSize: 0x48 }
  - { offsetInCU: 0x121, offset: 0x16EDF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVwcp', symObjAddr: 0x1A8, symBinAddr: 0x21C78, symSize: 0x68 }
  - { offsetInCU: 0x134, offset: 0x16EF2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVwca', symObjAddr: 0x210, symBinAddr: 0x21CE0, symSize: 0x74 }
  - { offsetInCU: 0x147, offset: 0x16F05, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVwtk', symObjAddr: 0x284, symBinAddr: 0x21D54, symSize: 0x64 }
  - { offsetInCU: 0x15A, offset: 0x16F18, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVwta', symObjAddr: 0x2E8, symBinAddr: 0x21DB8, symSize: 0x6C }
  - { offsetInCU: 0x16D, offset: 0x16F2B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVwet', symObjAddr: 0x354, symBinAddr: 0x21E24, symSize: 0xC }
  - { offsetInCU: 0x180, offset: 0x16F3E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVwst', symObjAddr: 0x3DC, symBinAddr: 0x21EAC, symSize: 0xC }
  - { offsetInCU: 0x193, offset: 0x16F51, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVMr', symObjAddr: 0x460, symBinAddr: 0x21F30, symSize: 0x74 }
  - { offsetInCU: 0x291, offset: 0x1704F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVyACyAA0d7ManagerdE0CSg_s5Error_pSgtccfC', symObjAddr: 0x0, symBinAddr: 0x21AD0, symSize: 0x44 }
  - { offsetInCU: 0x2BA, offset: 0x17078, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerV14callAsFunctionyyAA0d7ManagerdE0CSg_s5Error_pSgtF', symObjAddr: 0x80, symBinAddr: 0x21B50, symSize: 0x48 }
  - { offsetInCU: 0x2FB, offset: 0x170B9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerV2eeoiySbAC_ACtFZ', symObjAddr: 0xC8, symBinAddr: 0x21B98, symSize: 0x4 }
  - { offsetInCU: 0x27, offset: 0x1714C, size: 0x8, addend: 0x0, symName: '_$sSo20FBSDKInternalUtilityC13FBSDKLoginKit29UserInterfaceElementProvidingA2cDP21topMostViewControllerSo06UIViewL0CSgyFTW', symObjAddr: 0x0, symBinAddr: 0x21FA4, symSize: 0x24 }
  - { offsetInCU: 0x7E, offset: 0x171A3, size: 0x8, addend: 0x0, symName: '_$sSo20FBSDKInternalUtilityC13FBSDKLoginKit29UserInterfaceElementProvidingA2cDP21topMostViewControllerSo06UIViewL0CSgyFTW', symObjAddr: 0x0, symBinAddr: 0x21FA4, symSize: 0x24 }
  - { offsetInCU: 0xC5, offset: 0x171EA, size: 0x8, addend: 0x0, symName: '_$sSo20FBSDKInternalUtilityC13FBSDKLoginKit29UserInterfaceElementProvidingA2cDP14viewController3forSo06UIViewJ0CSgSo0L0C_tFTW', symObjAddr: 0x24, symBinAddr: 0x21FC8, symSize: 0x28 }
  - { offsetInCU: 0x107, offset: 0x1722C, size: 0x8, addend: 0x0, symName: '_$sSo20FBSDKInternalUtilityC13FBSDKLoginKit28UserInterfaceStringProvidingA2cDP16bundleForStringsSo8NSBundleCvgTW', symObjAddr: 0x4C, symBinAddr: 0x21FF0, symSize: 0x24 }
  - { offsetInCU: 0x49, offset: 0x1734C, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE17loginButtonDidTapABvpZ', symObjAddr: 0x2520, symBinAddr: 0x60678, symSize: 0x0 }
  - { offsetInCU: 0x63, offset: 0x17366, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE17smartLoginServiceABvpZ', symObjAddr: 0x2528, symBinAddr: 0x60680, symSize: 0x0 }
  - { offsetInCU: 0x7D, offset: 0x17380, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE16sessionAuthStartABvpZ', symObjAddr: 0x2530, symBinAddr: 0x60688, symSize: 0x0 }
  - { offsetInCU: 0x97, offset: 0x1739A, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE14sessionAuthEndABvpZ', symObjAddr: 0x2538, symBinAddr: 0x60690, symSize: 0x0 }
  - { offsetInCU: 0xB1, offset: 0x173B4, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE22sessionAuthMethodStartABvpZ', symObjAddr: 0x2540, symBinAddr: 0x60698, symSize: 0x0 }
  - { offsetInCU: 0xCB, offset: 0x173CE, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE20sessionAuthMethodEndABvpZ', symObjAddr: 0x2548, symBinAddr: 0x606A0, symSize: 0x0 }
  - { offsetInCU: 0xE5, offset: 0x173E8, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE20sessionAuthHeartbeatABvpZ', symObjAddr: 0x2550, symBinAddr: 0x606A8, symSize: 0x0 }
  - { offsetInCU: 0xF3, offset: 0x173F6, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE17loginButtonDidTap_WZ', symObjAddr: 0x0, symBinAddr: 0x22014, symSize: 0x34 }
  - { offsetInCU: 0x10D, offset: 0x17410, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE17loginButtonDidTapABvau', symObjAddr: 0x34, symBinAddr: 0x22048, symSize: 0x40 }
  - { offsetInCU: 0x12B, offset: 0x1742E, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE17smartLoginService_WZ', symObjAddr: 0x90, symBinAddr: 0x220A4, symSize: 0x34 }
  - { offsetInCU: 0x145, offset: 0x17448, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE17smartLoginServiceABvau', symObjAddr: 0xC4, symBinAddr: 0x220D8, symSize: 0x40 }
  - { offsetInCU: 0x163, offset: 0x17466, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE16sessionAuthStart_WZ', symObjAddr: 0x120, symBinAddr: 0x22134, symSize: 0x34 }
  - { offsetInCU: 0x17D, offset: 0x17480, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE16sessionAuthStartABvau', symObjAddr: 0x154, symBinAddr: 0x22168, symSize: 0x40 }
  - { offsetInCU: 0x19B, offset: 0x1749E, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE14sessionAuthEnd_WZ', symObjAddr: 0x1B0, symBinAddr: 0x221C4, symSize: 0x34 }
  - { offsetInCU: 0x1B5, offset: 0x174B8, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE14sessionAuthEndABvau', symObjAddr: 0x1E4, symBinAddr: 0x221F8, symSize: 0x40 }
  - { offsetInCU: 0x1D3, offset: 0x174D6, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE22sessionAuthMethodStart_WZ', symObjAddr: 0x240, symBinAddr: 0x22254, symSize: 0x34 }
  - { offsetInCU: 0x1ED, offset: 0x174F0, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE22sessionAuthMethodStartABvau', symObjAddr: 0x274, symBinAddr: 0x22288, symSize: 0x40 }
  - { offsetInCU: 0x20B, offset: 0x1750E, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE20sessionAuthMethodEnd_WZ', symObjAddr: 0x2D0, symBinAddr: 0x222E4, symSize: 0x34 }
  - { offsetInCU: 0x225, offset: 0x17528, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE20sessionAuthMethodEndABvau', symObjAddr: 0x304, symBinAddr: 0x22318, symSize: 0x40 }
  - { offsetInCU: 0x243, offset: 0x17546, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE20sessionAuthHeartbeat_WZ', symObjAddr: 0x360, symBinAddr: 0x22374, symSize: 0x34 }
  - { offsetInCU: 0x25D, offset: 0x17560, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE20sessionAuthHeartbeatABvau', symObjAddr: 0x394, symBinAddr: 0x223A8, symSize: 0x40 }
  - { offsetInCU: 0x27, offset: 0x17655, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit21LoginCompleterFactoryV06createcD013urlParameters5appIDAA0C10Completing_pSDySSypG_SStF', symObjAddr: 0x0, symBinAddr: 0x2243C, symSize: 0x64 }
  - { offsetInCU: 0xA0, offset: 0x176CE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit21LoginCompleterFactoryVAA0cdE8ProtocolA2aDP06createcD013urlParameters5appIDAA0C10Completing_pSDySSypG_SStFTW', symObjAddr: 0x68, symBinAddr: 0x224A4, symSize: 0x64 }
  - { offsetInCU: 0xFC, offset: 0x1772A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit21LoginCompleterFactoryVMa', symObjAddr: 0xCC, symBinAddr: 0x22508, symSize: 0x10 }
  - { offsetInCU: 0x1D1, offset: 0x177FF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit21LoginCompleterFactoryV06createcD013urlParameters5appIDAA0C10Completing_pSDySSypG_SStF', symObjAddr: 0x0, symBinAddr: 0x2243C, symSize: 0x64 }
  - { offsetInCU: 0x218, offset: 0x17846, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit21LoginCompleterFactoryVACycfC', symObjAddr: 0x64, symBinAddr: 0x224A0, symSize: 0x4 }
  - { offsetInCU: 0x8E, offset: 0x17924, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC5nonceSSvgTo', symObjAddr: 0x4, symBinAddr: 0x22534, symSize: 0x4C }
  - { offsetInCU: 0xDB, offset: 0x17971, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC8trackingAA0C8TrackingOvgTo', symObjAddr: 0x88, symBinAddr: 0x225B8, symSize: 0x10 }
  - { offsetInCU: 0xFB, offset: 0x17991, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC8trackingAA0C8TrackingOvgTo', symObjAddr: 0x88, symBinAddr: 0x225B8, symSize: 0x10 }
  - { offsetInCU: 0x129, offset: 0x179BF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC20requestedPermissionsShyAA12FBPermissionCGvgTo', symObjAddr: 0xA8, symBinAddr: 0x225D8, symSize: 0x64 }
  - { offsetInCU: 0x176, offset: 0x17A0C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC15messengerPageIdSSSgvgTo', symObjAddr: 0x11C, symBinAddr: 0x2264C, symSize: 0x5C }
  - { offsetInCU: 0x1BB, offset: 0x17A51, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC8authTypeSo0a4AuthF0aSgvgTo', symObjAddr: 0x1B0, symBinAddr: 0x226E0, symSize: 0x10 }
  - { offsetInCU: 0x1DA, offset: 0x17A70, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC8authTypeSo0a4AuthF0aSgvgTo', symObjAddr: 0x1B0, symBinAddr: 0x226E0, symSize: 0x10 }
  - { offsetInCU: 0x20A, offset: 0x17AA0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC12codeVerifierAA04CodeF0CvgTo', symObjAddr: 0x1F0, symBinAddr: 0x22720, symSize: 0x10 }
  - { offsetInCU: 0x22A, offset: 0x17AC0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC12codeVerifierAA04CodeF0CvgTo', symObjAddr: 0x1F0, symBinAddr: 0x22720, symSize: 0x10 }
  - { offsetInCU: 0x297, offset: 0x17B2D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking5nonce15messengerPageIdACSgSaySSG_AA0C8TrackingOS2SSgtcfcTo', symObjAddr: 0x3F0, symBinAddr: 0x22920, symSize: 0x128 }
  - { offsetInCU: 0x349, offset: 0x17BDF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking5nonce15messengerPageId8authTypeACSgSaySSG_AA0C8TrackingOS2SSgSo0a4AuthL0aSgtcfcTo', symObjAddr: 0x6AC, symBinAddr: 0x22BDC, symSize: 0xAC }
  - { offsetInCU: 0x3AC, offset: 0x17C42, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking5nonceACSgSaySSG_AA0C8TrackingOSStcfcTo', symObjAddr: 0x8A8, symBinAddr: 0x22DD8, symSize: 0xC4 }
  - { offsetInCU: 0x43D, offset: 0x17CD3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking15messengerPageIdACSgSaySSG_AA0C8TrackingOSSSgtcfcTo', symObjAddr: 0xB0C, symBinAddr: 0x2303C, symSize: 0x6C }
  - { offsetInCU: 0x4AA, offset: 0x17D40, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking15messengerPageId8authTypeACSgSaySSG_AA0C8TrackingOSSSgSo0a4AuthK0aSgtcfcTo', symObjAddr: 0xD34, symBinAddr: 0x23264, symSize: 0x88 }
  - { offsetInCU: 0x52B, offset: 0x17DC1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking5nonce15messengerPageId8authType12codeVerifierACSgSaySSG_AA0C8TrackingOS2SSgSo0a4AuthL0aSgAA04CodeN0CtcfcTo', symObjAddr: 0x1550, symBinAddr: 0x23A80, symSize: 0xC4 }
  - { offsetInCU: 0x584, offset: 0x17E1A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8trackingACSgSaySSG_AA0C8TrackingOtcfcTo', symObjAddr: 0x182C, symBinAddr: 0x23D5C, symSize: 0x118 }
  - { offsetInCU: 0x5FC, offset: 0x17E92, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC8trackingACSgAA0C8TrackingO_tcfcTo', symObjAddr: 0x1A18, symBinAddr: 0x23F48, symSize: 0x68 }
  - { offsetInCU: 0x669, offset: 0x17EFF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationCACycfcTo', symObjAddr: 0x1ACC, symBinAddr: 0x23FFC, symSize: 0x2C }
  - { offsetInCU: 0x702, offset: 0x17F98, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking5nonce15messengerPageId8authType12codeVerifierACSgShy09FBSDKCoreB010PermissionOG_AA0C8TrackingOS2SSgSo0a4AuthL0aSgAA04CodeN0CtcfCTf4nnnnnnd_n', symObjAddr: 0x1B9C, symBinAddr: 0x240CC, symSize: 0x2A0 }
  - { offsetInCU: 0xBFC, offset: 0x18492, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationCfETo', symObjAddr: 0x1B2C, symBinAddr: 0x2405C, symSize: 0x70 }
  - { offsetInCU: 0xDAB, offset: 0x18641, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationCMa', symObjAddr: 0x1EC0, symBinAddr: 0x2436C, symSize: 0x20 }
  - { offsetInCU: 0x10E8, offset: 0x1897E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking5nonce15messengerPageId8authType12codeVerifierACSgShy09FBSDKCoreB010PermissionOG_AA0C8TrackingOS2SSgSo0a4AuthL0aSgAA04CodeN0CtcfC', symObjAddr: 0x0, symBinAddr: 0x22530, symSize: 0x4 }
  - { offsetInCU: 0x1112, offset: 0x189A8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC5nonceSSvg', symObjAddr: 0x50, symBinAddr: 0x22580, symSize: 0x38 }
  - { offsetInCU: 0x1141, offset: 0x189D7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC8trackingAA0C8TrackingOvg', symObjAddr: 0x98, symBinAddr: 0x225C8, symSize: 0x10 }
  - { offsetInCU: 0x116E, offset: 0x18A04, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC20requestedPermissionsShyAA12FBPermissionCGvg', symObjAddr: 0x10C, symBinAddr: 0x2263C, symSize: 0x10 }
  - { offsetInCU: 0x119B, offset: 0x18A31, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC15messengerPageIdSSSgvg', symObjAddr: 0x178, symBinAddr: 0x226A8, symSize: 0x38 }
  - { offsetInCU: 0x11CA, offset: 0x18A60, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC8authTypeSo0a4AuthF0aSgvg', symObjAddr: 0x1C0, symBinAddr: 0x226F0, symSize: 0x30 }
  - { offsetInCU: 0x11F9, offset: 0x18A8F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC12codeVerifierAA04CodeF0Cvg', symObjAddr: 0x200, symBinAddr: 0x22730, symSize: 0x10 }
  - { offsetInCU: 0x1266, offset: 0x18AFC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking5nonce15messengerPageIdACSgSaySSG_AA0C8TrackingOS2SSgtcfC', symObjAddr: 0x210, symBinAddr: 0x22740, symSize: 0xF8 }
  - { offsetInCU: 0x12AB, offset: 0x18B41, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking5nonce15messengerPageIdACSgSaySSG_AA0C8TrackingOS2SSgtcfc', symObjAddr: 0x308, symBinAddr: 0x22838, symSize: 0xE8 }
  - { offsetInCU: 0x12EC, offset: 0x18B82, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking5nonce15messengerPageId8authTypeACSgSaySSG_AA0C8TrackingOS2SSgSo0a4AuthL0aSgtcfC', symObjAddr: 0x518, symBinAddr: 0x22A48, symSize: 0x78 }
  - { offsetInCU: 0x1305, offset: 0x18B9B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking5nonce15messengerPageId8authTypeACSgSaySSG_AA0C8TrackingOS2SSgSo0a4AuthL0aSgtcfc', symObjAddr: 0x590, symBinAddr: 0x22AC0, symSize: 0x11C }
  - { offsetInCU: 0x13BA, offset: 0x18C50, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking5nonceACSgSaySSG_AA0C8TrackingOSStcfC', symObjAddr: 0x758, symBinAddr: 0x22C88, symSize: 0xB0 }
  - { offsetInCU: 0x13FA, offset: 0x18C90, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking5nonceACSgSaySSG_AA0C8TrackingOSStcfc', symObjAddr: 0x808, symBinAddr: 0x22D38, symSize: 0xA0 }
  - { offsetInCU: 0x1432, offset: 0x18CC8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking15messengerPageIdACSgSaySSG_AA0C8TrackingOSSSgtcfC', symObjAddr: 0x96C, symBinAddr: 0x22E9C, symSize: 0x58 }
  - { offsetInCU: 0x1445, offset: 0x18CDB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking15messengerPageIdACSgSaySSG_AA0C8TrackingOSSSgtcfc', symObjAddr: 0x9C4, symBinAddr: 0x22EF4, symSize: 0x148 }
  - { offsetInCU: 0x1495, offset: 0x18D2B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking15messengerPageId8authTypeACSgSaySSG_AA0C8TrackingOSSSgSo0a4AuthK0aSgtcfC', symObjAddr: 0xB78, symBinAddr: 0x230A8, symSize: 0x60 }
  - { offsetInCU: 0x14A8, offset: 0x18D3E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking15messengerPageId8authTypeACSgSaySSG_AA0C8TrackingOSSSgSo0a4AuthK0aSgtcfc', symObjAddr: 0xBD8, symBinAddr: 0x23108, symSize: 0x15C }
  - { offsetInCU: 0x1507, offset: 0x18D9D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking5nonce15messengerPageId8authType12codeVerifierACSgSaySSG_AA0C8TrackingOS2SSgSo0a4AuthL0aSgAA04CodeN0CtcfC', symObjAddr: 0xDBC, symBinAddr: 0x232EC, symSize: 0x88 }
  - { offsetInCU: 0x1598, offset: 0x18E2E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking5nonce15messengerPageId8authType12codeVerifierACSgSaySSG_AA0C8TrackingOS2SSgSo0a4AuthL0aSgAA04CodeN0Ctcfc', symObjAddr: 0xE44, symBinAddr: 0x23374, symSize: 0x4F4 }
  - { offsetInCU: 0x187E, offset: 0x19114, size: 0x8, addend: 0x0, symName: '_$sSTsSQ7ElementRpzrlE8containsySbABFSaySo18FBSDKLoginAuthTypeaG_Tg5', symObjAddr: 0x1338, symBinAddr: 0x23868, symSize: 0x154 }
  - { offsetInCU: 0x1A3E, offset: 0x192D4, size: 0x8, addend: 0x0, symName: '_$sSTsSQ7ElementRpzrlE8containsySbABFSaySSG_Tg5', symObjAddr: 0x148C, symBinAddr: 0x239BC, symSize: 0xC4 }
  - { offsetInCU: 0x1BCD, offset: 0x19463, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8trackingACSgSaySSG_AA0C8TrackingOtcfC', symObjAddr: 0x1614, symBinAddr: 0x23B44, symSize: 0x110 }
  - { offsetInCU: 0x1C09, offset: 0x1949F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8trackingACSgSaySSG_AA0C8TrackingOtcfc', symObjAddr: 0x1724, symBinAddr: 0x23C54, symSize: 0x108 }
  - { offsetInCU: 0x1C55, offset: 0x194EB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC8trackingACSgAA0C8TrackingO_tcfC', symObjAddr: 0x1944, symBinAddr: 0x23E74, symSize: 0x70 }
  - { offsetInCU: 0x1C8D, offset: 0x19523, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC8trackingACSgAA0C8TrackingO_tcfc', symObjAddr: 0x19B4, symBinAddr: 0x23EE4, symSize: 0x64 }
  - { offsetInCU: 0x1CB3, offset: 0x19549, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationCACycfC', symObjAddr: 0x1A80, symBinAddr: 0x23FB0, symSize: 0x20 }
  - { offsetInCU: 0x1CC6, offset: 0x1955C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationCACycfc', symObjAddr: 0x1AA0, symBinAddr: 0x23FD0, symSize: 0x2C }
  - { offsetInCU: 0x1D1A, offset: 0x195B0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationCfD', symObjAddr: 0x1AF8, symBinAddr: 0x24028, symSize: 0x34 }
  - { offsetInCU: 0x220, offset: 0x19899, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV10Foundation13CustomNSErrorAadEP11errorDomainSSvgZTW', symObjAddr: 0x23C, symBinAddr: 0x245DC, symSize: 0x5C }
  - { offsetInCU: 0x255, offset: 0x198CE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV10Foundation13CustomNSErrorAadEP9errorCodeSivgTW', symObjAddr: 0x298, symBinAddr: 0x24638, symSize: 0x8 }
  - { offsetInCU: 0x274, offset: 0x198ED, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV10Foundation13CustomNSErrorAadEP9errorCodeSivgTW', symObjAddr: 0x298, symBinAddr: 0x24638, symSize: 0x8 }
  - { offsetInCU: 0x285, offset: 0x198FE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV10Foundation13CustomNSErrorAadEP13errorUserInfoSDySSypGvgTW', symObjAddr: 0x2A0, symBinAddr: 0x24640, symSize: 0x8 }
  - { offsetInCU: 0x2A4, offset: 0x1991D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV10Foundation13CustomNSErrorAadEP13errorUserInfoSDySSypGvgTW', symObjAddr: 0x2A0, symBinAddr: 0x24640, symSize: 0x8 }
  - { offsetInCU: 0x2B5, offset: 0x1992E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVSHAASH9hashValueSivgTW', symObjAddr: 0x2A8, symBinAddr: 0x24648, symSize: 0x44 }
  - { offsetInCU: 0x38F, offset: 0x19A08, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0x2EC, symBinAddr: 0x2468C, symSize: 0x28 }
  - { offsetInCU: 0x3FD, offset: 0x19A76, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x3AC, symBinAddr: 0x2474C, symSize: 0x34 }
  - { offsetInCU: 0x4A6, offset: 0x19B1F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14LoginErrorCodeOSYAASY8rawValuexSg03RawG0Qz_tcfCTW', symObjAddr: 0x4A4, symBinAddr: 0x24844, symSize: 0x30 }
  - { offsetInCU: 0x4D5, offset: 0x19B4E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14LoginErrorCodeOSYAASY8rawValue03RawG0QzvgTW', symObjAddr: 0x4D4, symBinAddr: 0x24874, symSize: 0xC }
  - { offsetInCU: 0x4F1, offset: 0x19B6A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14LoginErrorCodeO8rawValueACSgSi_tcfCTf4nd_n', symObjAddr: 0x4F4, symBinAddr: 0x24894, symSize: 0x18 }
  - { offsetInCU: 0x555, offset: 0x19BCE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV10Foundation13CustomNSErrorAAs0D0PWb', symObjAddr: 0x50C, symBinAddr: 0x248AC, symSize: 0x4 }
  - { offsetInCU: 0x568, offset: 0x19BE1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVACs0D0AAWl', symObjAddr: 0x510, symBinAddr: 0x248B0, symSize: 0x44 }
  - { offsetInCU: 0x57B, offset: 0x19BF4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVSHAASQWb', symObjAddr: 0x554, symBinAddr: 0x248F4, symSize: 0x4 }
  - { offsetInCU: 0x58E, offset: 0x19C07, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVACSQAAWl', symObjAddr: 0x558, symBinAddr: 0x248F8, symSize: 0x44 }
  - { offsetInCU: 0x5A1, offset: 0x19C1A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14LoginErrorCodeOSHAASQWb', symObjAddr: 0x59C, symBinAddr: 0x2493C, symSize: 0x4 }
  - { offsetInCU: 0x5B4, offset: 0x19C2D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14LoginErrorCodeOACSQAAWl', symObjAddr: 0x5A0, symBinAddr: 0x24940, symSize: 0x44 }
  - { offsetInCU: 0x5C7, offset: 0x19C40, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVwxx', symObjAddr: 0x5E8, symBinAddr: 0x24988, symSize: 0x28 }
  - { offsetInCU: 0x5DA, offset: 0x19C53, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVwca', symObjAddr: 0x650, symBinAddr: 0x249F0, symSize: 0x64 }
  - { offsetInCU: 0x5ED, offset: 0x19C66, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVwta', symObjAddr: 0x6C8, symBinAddr: 0x24A54, symSize: 0x44 }
  - { offsetInCU: 0x600, offset: 0x19C79, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVwet', symObjAddr: 0x70C, symBinAddr: 0x24A98, symSize: 0x48 }
  - { offsetInCU: 0x613, offset: 0x19C8C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVwst', symObjAddr: 0x754, symBinAddr: 0x24AE0, symSize: 0x40 }
  - { offsetInCU: 0x626, offset: 0x19C9F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVMa', symObjAddr: 0x794, symBinAddr: 0x24B20, symSize: 0x10 }
  - { offsetInCU: 0x639, offset: 0x19CB2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14LoginErrorCodeOMa', symObjAddr: 0x7A4, symBinAddr: 0x24B30, symSize: 0x10 }
  - { offsetInCU: 0x64C, offset: 0x19CC5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVAC10Foundation13CustomNSErrorAAWl', symObjAddr: 0x7B4, symBinAddr: 0x24B40, symSize: 0x44 }
  - { offsetInCU: 0x6BC, offset: 0x19D35, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0x314, symBinAddr: 0x246B4, symSize: 0x40 }
  - { offsetInCU: 0x752, offset: 0x19DCB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVs0D0AAsADP9_userInfoyXlSgvgTW', symObjAddr: 0x3A4, symBinAddr: 0x24744, symSize: 0x4 }
  - { offsetInCU: 0x76D, offset: 0x19DE6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVs0D0AAsADP19_getEmbeddedNSErroryXlSgyFTW', symObjAddr: 0x3A8, symBinAddr: 0x24748, symSize: 0x4 }
  - { offsetInCU: 0x7ED, offset: 0x19E66, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14LoginErrorCodeOSHAASH9hashValueSivgTW', symObjAddr: 0x3F8, symBinAddr: 0x24798, symSize: 0x44 }
  - { offsetInCU: 0x894, offset: 0x19F0D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14LoginErrorCodeOSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0x43C, symBinAddr: 0x247DC, symSize: 0x28 }
  - { offsetInCU: 0x8E3, offset: 0x19F5C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14LoginErrorCodeOSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0x464, symBinAddr: 0x24804, symSize: 0x40 }
  - { offsetInCU: 0x968, offset: 0x19FE1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14LoginErrorCodeOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x4E0, symBinAddr: 0x24880, symSize: 0x14 }
  - { offsetInCU: 0x9FB, offset: 0x1A074, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVs0D0AAsADP7_domainSSvgTW', symObjAddr: 0x354, symBinAddr: 0x246F4, symSize: 0x28 }
  - { offsetInCU: 0xA17, offset: 0x1A090, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVs0D0AAsADP5_codeSivgTW', symObjAddr: 0x37C, symBinAddr: 0x2471C, symSize: 0x28 }
  - { offsetInCU: 0xA9F, offset: 0x1A118, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV03_nsD0So7NSErrorCSgvg', symObjAddr: 0x0, symBinAddr: 0x243A0, symSize: 0x28 }
  - { offsetInCU: 0xAB2, offset: 0x1A12B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV9errorCodeSivg', symObjAddr: 0x28, symBinAddr: 0x243C8, symSize: 0x8 }
  - { offsetInCU: 0xAC6, offset: 0x1A13F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV13errorUserInfoSDySSypGvg', symObjAddr: 0x30, symBinAddr: 0x243D0, symSize: 0x8 }
  - { offsetInCU: 0xAE5, offset: 0x1A15E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV03_nsD0ACSo7NSErrorC_tcfC', symObjAddr: 0x38, symBinAddr: 0x243D8, symSize: 0xA0 }
  - { offsetInCU: 0xB08, offset: 0x1A181, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV_8userInfoAcA0cD4CodeO_SDySSypGtcfC', symObjAddr: 0xD8, symBinAddr: 0x24478, symSize: 0xC }
  - { offsetInCU: 0xB38, offset: 0x1A1B1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14LoginErrorCodeO8rawValueSivg', symObjAddr: 0xE4, symBinAddr: 0x24484, symSize: 0x4 }
  - { offsetInCU: 0xB5D, offset: 0x1A1D6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV11errorDomainSSvgZ', symObjAddr: 0xE8, symBinAddr: 0x24488, symSize: 0x5C }
  - { offsetInCU: 0xB84, offset: 0x1A1FD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV8reservedAA0cD4CodeOvgZ', symObjAddr: 0x144, symBinAddr: 0x244E4, symSize: 0x8 }
  - { offsetInCU: 0xBA5, offset: 0x1A21E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV7unknownAA0cD4CodeOvgZ', symObjAddr: 0x14C, symBinAddr: 0x244EC, symSize: 0x8 }
  - { offsetInCU: 0xBC6, offset: 0x1A23F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV15passwordChangedAA0cD4CodeOvgZ', symObjAddr: 0x154, symBinAddr: 0x244F4, symSize: 0x8 }
  - { offsetInCU: 0xBE7, offset: 0x1A260, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV16userCheckpointedAA0cD4CodeOvgZ', symObjAddr: 0x15C, symBinAddr: 0x244FC, symSize: 0x8 }
  - { offsetInCU: 0xC08, offset: 0x1A281, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV12userMismatchAA0cD4CodeOvgZ', symObjAddr: 0x164, symBinAddr: 0x24504, symSize: 0x8 }
  - { offsetInCU: 0xC29, offset: 0x1A2A2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV15unconfirmedUserAA0cD4CodeOvgZ', symObjAddr: 0x16C, symBinAddr: 0x2450C, symSize: 0x8 }
  - { offsetInCU: 0xC4A, offset: 0x1A2C3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV24systemAccountAppDisabledAA0cD4CodeOvgZ', symObjAddr: 0x174, symBinAddr: 0x24514, symSize: 0x8 }
  - { offsetInCU: 0xC6B, offset: 0x1A2E4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV24systemAccountUnavailableAA0cD4CodeOvgZ', symObjAddr: 0x17C, symBinAddr: 0x2451C, symSize: 0x8 }
  - { offsetInCU: 0xC8C, offset: 0x1A305, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV18badChallengeStringAA0cD4CodeOvgZ', symObjAddr: 0x184, symBinAddr: 0x24524, symSize: 0x8 }
  - { offsetInCU: 0xCAD, offset: 0x1A326, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV14invalidIDTokenAA0cD4CodeOvgZ', symObjAddr: 0x18C, symBinAddr: 0x2452C, symSize: 0x8 }
  - { offsetInCU: 0xCCE, offset: 0x1A347, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV18missingAccessTokenAA0cD4CodeOvgZ', symObjAddr: 0x194, symBinAddr: 0x24534, symSize: 0x8 }
  - { offsetInCU: 0xCEF, offset: 0x1A368, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV2eeoiySbAC_ACtFZ', symObjAddr: 0x19C, symBinAddr: 0x2453C, symSize: 0x34 }
  - { offsetInCU: 0xD5E, offset: 0x1A3D7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV4hash4intoys6HasherVz_tF', symObjAddr: 0x1D0, symBinAddr: 0x24570, symSize: 0x28 }
  - { offsetInCU: 0xDD0, offset: 0x1A449, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV9hashValueSivg', symObjAddr: 0x1F8, symBinAddr: 0x24598, symSize: 0x44 }
  - { offsetInCU: 0xF08, offset: 0x1A581, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14LoginErrorCodeO8rawValueACSgSi_tcfC', symObjAddr: 0x3E0, symBinAddr: 0x24780, symSize: 0x18 }
  - { offsetInCU: 0x27, offset: 0x1A5ED, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16LoginErrorDomain_WZ', symObjAddr: 0x0, symBinAddr: 0x24B84, symSize: 0x2C }
  - { offsetInCU: 0x49, offset: 0x1A60F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16LoginErrorDomainSSvp', symObjAddr: 0x88, symBinAddr: 0x5E688, symSize: 0x0 }
  - { offsetInCU: 0x57, offset: 0x1A61D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16LoginErrorDomain_WZ', symObjAddr: 0x0, symBinAddr: 0x24B84, symSize: 0x2C }
  - { offsetInCU: 0x93, offset: 0x1A659, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16LoginErrorDomainSSvg', symObjAddr: 0x2C, symBinAddr: 0x24BB0, symSize: 0x5C }
  - { offsetInCU: 0xC2, offset: 0x1A7EA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC15defaultAudienceAA07DefaultF0OvgTo', symObjAddr: 0x4C4, symBinAddr: 0x250E8, symSize: 0x44 }
  - { offsetInCU: 0x117, offset: 0x1A83F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC15defaultAudienceAA07DefaultF0OvsTo', symObjAddr: 0x54C, symBinAddr: 0x25170, symSize: 0x48 }
  - { offsetInCU: 0x1DB, offset: 0x1A903, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC13configurationAA0C13ConfigurationCSgvgTo', symObjAddr: 0x760, symBinAddr: 0x25344, symSize: 0x48 }
  - { offsetInCU: 0x22F, offset: 0x1A957, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC13configurationAA0C13ConfigurationCSgvsTo', symObjAddr: 0x7F8, symBinAddr: 0x253DC, symSize: 0x64 }
  - { offsetInCU: 0x285, offset: 0x1A9AD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC20requestedPermissionsShyAA12FBPermissionCGSgvgTo', symObjAddr: 0x85C, symBinAddr: 0x25440, symSize: 0xA8 }
  - { offsetInCU: 0x2D9, offset: 0x1AA01, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC20requestedPermissionsShyAA12FBPermissionCGSgvsTo', symObjAddr: 0x94C, symBinAddr: 0x25530, symSize: 0x9C }
  - { offsetInCU: 0x3B2, offset: 0x1AADA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC17usedSafariSessionSbvgTo', symObjAddr: 0xC08, symBinAddr: 0x257EC, symSize: 0x44 }
  - { offsetInCU: 0x406, offset: 0x1AB2E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC17usedSafariSessionSbvsTo', symObjAddr: 0xC90, symBinAddr: 0x25874, symSize: 0x4C }
  - { offsetInCU: 0x485, offset: 0x1ABAD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC012isPerformingC0SbvgTo', symObjAddr: 0xD68, symBinAddr: 0x2594C, symSize: 0x4C }
  - { offsetInCU: 0x5B9, offset: 0x1ACE1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC15defaultAudienceAcA07DefaultF0O_tcfcTo', symObjAddr: 0x15CC, symBinAddr: 0x261B0, symSize: 0x64 }
  - { offsetInCU: 0x680, offset: 0x1ADA8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5logIn4from13configuration10completionySo16UIViewControllerCSg_AA0C13ConfigurationCSgyAA0cdC6ResultCSg_s5Error_pSgtctFTo', symObjAddr: 0x1A84, symBinAddr: 0x26668, symSize: 0xB8 }
  - { offsetInCU: 0x704, offset: 0x1AE2C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5logIn11permissions4from7handlerySaySSG_So16UIViewControllerCSgyAA0cdC6ResultCSg_s5Error_pSgtcSgtFTo', symObjAddr: 0x22EC, symBinAddr: 0x26ED0, symSize: 0xD4 }
  - { offsetInCU: 0x747, offset: 0x1AE6F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5logIn33_C218275A97333B874EDDFE627110566CLLyyFySb_s5Error_pSgtcfU_', symObjAddr: 0x24D0, symBinAddr: 0x270B4, symSize: 0x1EC }
  - { offsetInCU: 0x8EB, offset: 0x1B013, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC21reauthorizeDataAccess4from7handlerySo16UIViewControllerC_yAA0cdC6ResultCSg_s5Error_pSgtctFTo', symObjAddr: 0x3A10, symBinAddr: 0x285F4, symSize: 0x74 }
  - { offsetInCU: 0x950, offset: 0x1B078, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC6logOutyyFTo', symObjAddr: 0x3C48, symBinAddr: 0x2882C, symSize: 0x28 }
  - { offsetInCU: 0xA5F, offset: 0x1B187, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC22completeAuthentication10parameters15expectChallengeyAA01_C20CompletionParametersC_SbtFTo', symObjAddr: 0x51A4, symBinAddr: 0x29D88, symSize: 0x68 }
  - { offsetInCU: 0xB06, offset: 0x1B22E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC15logInParameters13configuration12loggingToken20authenticationMethodSDyS2SGSgAA0C13ConfigurationCSg_SSSgSStFTo', symObjAddr: 0x6E4C, symBinAddr: 0x2BA30, symSize: 0xFC }
  - { offsetInCU: 0xB3D, offset: 0x1B265, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC24validateReauthentication11accessToken11loginResult04userH5NonceySo011FBSDKAccessH0C_AA0cdcJ0CSgSSSgtFTo', symObjAddr: 0x737C, symBinAddr: 0x2BF60, symSize: 0xAC }
  - { offsetInCU: 0xB74, offset: 0x1B29C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18storeExpectedNonceyySSSgFTo', symObjAddr: 0x7774, symBinAddr: 0x2C358, symSize: 0x68 }
  - { offsetInCU: 0xBA4, offset: 0x1B2CC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCACycfcTo', symObjAddr: 0x79C4, symBinAddr: 0x2C5A8, symSize: 0x20 }
  - { offsetInCU: 0xBD4, offset: 0x1B2FC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCAA0C9ProvidingA2aDP15defaultAudienceAA07DefaultG0OvgTW', symObjAddr: 0x821C, symBinAddr: 0x2CE00, symSize: 0x48 }
  - { offsetInCU: 0xC0E, offset: 0x1B336, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCAA0C9ProvidingA2aDP15defaultAudienceAA07DefaultG0OvsTW', symObjAddr: 0x8264, symBinAddr: 0x2CE48, symSize: 0x4C }
  - { offsetInCU: 0xC4F, offset: 0x1B377, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCAA0C9ProvidingA2aDP15defaultAudienceAA07DefaultG0OvMTW', symObjAddr: 0x82B0, symBinAddr: 0x2CE94, symSize: 0x48 }
  - { offsetInCU: 0xC89, offset: 0x1B3B1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCAA0C9ProvidingA2aDP5logIn14viewController13configuration10completionySo06UIViewI0CSg_AA0C13ConfigurationCSgyAA0C6ResultOctFTW', symObjAddr: 0x82F8, symBinAddr: 0x2CEDC, symSize: 0x7C }
  - { offsetInCU: 0xCEF, offset: 0x1B417, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCAA0C9ProvidingA2aDP5logIn11permissions4from7handlerySaySSG_So16UIViewControllerCSgyAA0cdC6ResultCSg_s5Error_pSgtcSgtFTW', symObjAddr: 0x8374, symBinAddr: 0x2CF58, symSize: 0x20 }
  - { offsetInCU: 0xD0A, offset: 0x1B432, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCAA0C9ProvidingA2aDP6logOutyyFTW', symObjAddr: 0x8394, symBinAddr: 0x2CF78, symSize: 0x20 }
  - { offsetInCU: 0xD25, offset: 0x1B44D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCAA17DependentAsObjectA2aDP22configuredDependencies0gI0QzSgvgTW', symObjAddr: 0x8608, symBinAddr: 0x2D1EC, symSize: 0x58 }
  - { offsetInCU: 0xD57, offset: 0x1B47F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCAA17DependentAsObjectA2aDP22configuredDependencies0gI0QzSgvsTW', symObjAddr: 0x8660, symBinAddr: 0x2D244, symSize: 0x60 }
  - { offsetInCU: 0xD99, offset: 0x1B4C1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCAA17DependentAsObjectA2aDP22configuredDependencies0gI0QzSgvMTW', symObjAddr: 0x86C0, symBinAddr: 0x2D2A4, symSize: 0x44 }
  - { offsetInCU: 0xDD3, offset: 0x1B4FB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCAA17DependentAsObjectA2aDP19defaultDependencies0gI0QzSgvgTW', symObjAddr: 0x8704, symBinAddr: 0x2D2E8, symSize: 0x4 }
  - { offsetInCU: 0xE32, offset: 0x1B55A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC21reauthorizeDataAccess4from7handlerySo16UIViewControllerC_yAA0cdC6ResultCSg_s5Error_pSgtctF04$s13a6Kit012cd2C6l13CSgSo7NSErrorq11IeyByy_ADs5M12_pSgIeggg_TRAKSo0S0CSgIeyByy_Tf1ncn_nTf4dng_n', symObjAddr: 0xDC90, symBinAddr: 0x3275C, symSize: 0x4C4 }
  - { offsetInCU: 0x1531, offset: 0x1BC59, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC19defaultDependenciesAC06ObjectF0VSgvpACTk', symObjAddr: 0xFDC, symBinAddr: 0x25BC0, symSize: 0x90 }
  - { offsetInCU: 0x15E6, offset: 0x1BD0E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultCSgSo7NSErrorCSgIeyByy_ADs5Error_pSgIeggg_TR', symObjAddr: 0x1B3C, symBinAddr: 0x26720, symSize: 0x58 }
  - { offsetInCU: 0x1622, offset: 0x1BD4A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultCSgs5Error_pSgytIegnnr_AdFIeggg_TR', symObjAddr: 0x1C80, symBinAddr: 0x26864, symSize: 0x60 }
  - { offsetInCU: 0x1AA4, offset: 0x1C1CC, size: 0x8, addend: 0x0, symName: '_$sSbs5Error_pSgIegyg_SbSo7NSErrorCSgIeyByy_TR', symObjAddr: 0x7430, symBinAddr: 0x2C014, symSize: 0x64 }
  - { offsetInCU: 0x1ACA, offset: 0x1C1F2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCfETo', symObjAddr: 0x7A18, symBinAddr: 0x2C5FC, symSize: 0xA4 }
  - { offsetInCU: 0x1B10, offset: 0x1C238, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC10makeOpenerACyFZTo', symObjAddr: 0x7ADC, symBinAddr: 0x2C6C0, symSize: 0x24 }
  - { offsetInCU: 0x1BA8, offset: 0x1C2D0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC11application_4open17sourceApplication10annotationSbSo13UIApplicationCSg_10Foundation3URLVSgSSSgypSgtFTo', symObjAddr: 0x7B14, symBinAddr: 0x2C6F8, symSize: 0x174 }
  - { offsetInCU: 0x1BDA, offset: 0x1C302, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC7canOpen_3for17sourceApplication10annotationSb10Foundation3URLV_So13UIApplicationCSgSSSgypSgtFTo', symObjAddr: 0x7C88, symBinAddr: 0x2C86C, symSize: 0x138 }
  - { offsetInCU: 0x1C28, offset: 0x1C350, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC26applicationDidBecomeActiveyySo13UIApplicationCFTo', symObjAddr: 0x7E10, symBinAddr: 0x2C9F4, symSize: 0x88 }
  - { offsetInCU: 0x1C94, offset: 0x1C3BC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC19isAuthenticationURLySb10Foundation0G0VFTo', symObjAddr: 0x7EF8, symBinAddr: 0x2CADC, symSize: 0xE4 }
  - { offsetInCU: 0x1CF5, offset: 0x1C41D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC21shouldStopPropagation2ofSb10Foundation3URLV_tFTo', symObjAddr: 0x8170, symBinAddr: 0x2CD54, symSize: 0xAC }
  - { offsetInCU: 0x1D10, offset: 0x1C438, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit29LoginCompleterFactoryProtocol_pWOb', symObjAddr: 0x874C, symBinAddr: 0x2D330, symSize: 0x18 }
  - { offsetInCU: 0x1D23, offset: 0x1C44B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5logIn14viewController13configuration10completionySo06UIViewH0CSg_AA0C13ConfigurationCSgyAA0C6ResultOctFyAA0cdcM0CSg_s5Error_pSgtcfU_TA', symObjAddr: 0x8788, symBinAddr: 0x2D36C, symSize: 0x8 }
  - { offsetInCU: 0x1D36, offset: 0x1C45E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5logIn11permissions14viewController10completionySay09FBSDKCoreB010PermissionOG_So06UIViewI0CSgyAA0C6ResultOcSgtFyAA0cdcN0CSg_s5Error_pSgtcfU_TA', symObjAddr: 0x87BC, symBinAddr: 0x2D3A0, symSize: 0x8 }
  - { offsetInCU: 0x1D49, offset: 0x1C471, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesVWOh', symObjAddr: 0x87C4, symBinAddr: 0x2D3A8, symSize: 0x2C }
  - { offsetInCU: 0x1E06, offset: 0x1C52E, size: 0x8, addend: 0x0, symName: '_$ss22_ContiguousArrayBufferV19_uninitializedCount15minimumCapacityAByxGSi_SitcfC13FBSDKLoginKit18DeviceLoginManagerC_Tgm5', symObjAddr: 0x8DA4, symBinAddr: 0x2D944, symSize: 0x80 }
  - { offsetInCU: 0x1E65, offset: 0x1C58D, size: 0x8, addend: 0x0, symName: '_$ss22_ContiguousArrayBufferV19_uninitializedCount15minimumCapacityAByxGSi_SitcfCSS_Tgm5', symObjAddr: 0x8E24, symBinAddr: 0x2D9C4, symSize: 0x78 }
  - { offsetInCU: 0x1FFA, offset: 0x1C722, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV_8capacityAByxGs07__CocoaB0Vn_SitcfC13FBSDKLoginKit12FBPermissionC_Tgm5', symObjAddr: 0x9550, symBinAddr: 0x2E0F0, symSize: 0x1F8 }
  - { offsetInCU: 0x242D, offset: 0x1CB55, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV19genericIntersectionyAByxGqd__7ElementQyd__RszSTRd__lFADs13_UnsafeBitsetVXEfU_13FBSDKLoginKit12FBPermissionC_ShyAKGTg5', symObjAddr: 0xC2F0, symBinAddr: 0x30E90, symSize: 0x3E4 }
  - { offsetInCU: 0x2573, offset: 0x1CC9B, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV12intersectionyAByxGADFADs13_UnsafeBitsetVXEfU_13FBSDKLoginKit12FBPermissionC_Tg5', symObjAddr: 0xC6D4, symBinAddr: 0x31274, symSize: 0x4F4 }
  - { offsetInCU: 0x2687, offset: 0x1CDAF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC24validateReauthentication11accessToken11loginResult04userH5NonceySo011FBSDKAccessH0C_AA0cdcJ0CSgSSSgtFySo27FBSDKGraphRequestConnecting_pSg_ypSgs5Error_pSgtcfU_TA', symObjAddr: 0xD31C, symBinAddr: 0x31E88, symSize: 0x30 }
  - { offsetInCU: 0x269A, offset: 0x1CDC2, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0xD34C, symBinAddr: 0x31EB8, symSize: 0x10 }
  - { offsetInCU: 0x26AD, offset: 0x1CDD5, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0xD35C, symBinAddr: 0x31EC8, symSize: 0x8 }
  - { offsetInCU: 0x2780, offset: 0x1CEA8, size: 0x8, addend: 0x0, symName: '_$sSTsE6reduce4into_qd__qd__n_yqd__z_7ElementQztKXEtKlFSDyS2SSgG_s17_NativeDictionaryVyS2SGTg5051$sSD16compactMapValuesySDyxqd__Gqd__Sgq_KXEKlFys17_dE44Vyxqd__Gz_x3key_q_5valuettKXEfU_SS_SSSgSSTG5xq_Sgs5Error_pr0_lyAESSIsgnrzo_Tf1ncn_nTf4nng_n', symObjAddr: 0xD364, symBinAddr: 0x31ED0, symSize: 0x344 }
  - { offsetInCU: 0x29D8, offset: 0x1D100, size: 0x8, addend: 0x0, symName: '_$sSh21_nonEmptyArrayLiteralShyxGSayxG_tcfC13FBSDKLoginKit12FBPermissionC_Tgm5Tf4g_n', symObjAddr: 0xD974, symBinAddr: 0x32440, symSize: 0x31C }
  - { offsetInCU: 0x2BB3, offset: 0x1D2DB, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV12intersectionyAByxGADF13FBSDKLoginKit12FBPermissionC_Tg5Tf4ng_n', symObjAddr: 0xE154, symBinAddr: 0x32C20, symSize: 0x158 }
  - { offsetInCU: 0x2CBF, offset: 0x1D3E7, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV19genericIntersectionyAByxGqd__7ElementQyd__RszSTRd__lF13FBSDKLoginKit12FBPermissionC_ShyAIGTg5Tf4ng_n', symObjAddr: 0xE2AC, symBinAddr: 0x32D78, symSize: 0x158 }
  - { offsetInCU: 0x2DFD, offset: 0x1D525, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit29LoginCompleterFactoryProtocol_pWOc', symObjAddr: 0xEAC0, symBinAddr: 0x3358C, symSize: 0x44 }
  - { offsetInCU: 0x2E10, offset: 0x1D538, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCMU', symObjAddr: 0xEB28, symBinAddr: 0x335F4, symSize: 0x8 }
  - { offsetInCU: 0x2E23, offset: 0x1D54B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCMa', symObjAddr: 0xEB30, symBinAddr: 0x335FC, symSize: 0x3C }
  - { offsetInCU: 0x2E36, offset: 0x1D55E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCMr', symObjAddr: 0xEB6C, symBinAddr: 0x33638, symSize: 0xBC }
  - { offsetInCU: 0x2E49, offset: 0x1D571, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVSgMa', symObjAddr: 0xEC28, symBinAddr: 0x336F4, symSize: 0x54 }
  - { offsetInCU: 0x2E5C, offset: 0x1D584, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesVwCP', symObjAddr: 0xEC7C, symBinAddr: 0x33748, symSize: 0x30 }
  - { offsetInCU: 0x2E6F, offset: 0x1D597, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesVwxx', symObjAddr: 0xECAC, symBinAddr: 0x33778, symSize: 0x50 }
  - { offsetInCU: 0x2E82, offset: 0x1D5AA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesVwcp', symObjAddr: 0xECFC, symBinAddr: 0x337C8, symSize: 0xBC }
  - { offsetInCU: 0x2E95, offset: 0x1D5BD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesVwca', symObjAddr: 0xEDB8, symBinAddr: 0x33884, symSize: 0xE0 }
  - { offsetInCU: 0x2EA8, offset: 0x1D5D0, size: 0x8, addend: 0x0, symName: ___swift_memcpy112_8, symObjAddr: 0xF000, symBinAddr: 0x33964, symSize: 0x24 }
  - { offsetInCU: 0x2EBB, offset: 0x1D5E3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesVwta', symObjAddr: 0xF024, symBinAddr: 0x33988, symSize: 0xA4 }
  - { offsetInCU: 0x2ECE, offset: 0x1D5F6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesVwet', symObjAddr: 0xF0C8, symBinAddr: 0x33A2C, symSize: 0x48 }
  - { offsetInCU: 0x2EE1, offset: 0x1D609, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesVwst', symObjAddr: 0xF110, symBinAddr: 0x33A74, symSize: 0x5C }
  - { offsetInCU: 0x2EF4, offset: 0x1D61C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesVMa', symObjAddr: 0xF16C, symBinAddr: 0x33AD0, symSize: 0x10 }
  - { offsetInCU: 0x2F07, offset: 0x1D62F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultCSgSo7NSErrorCSgIeyByy_ADs5Error_pSgIeggg_TRTA', symObjAddr: 0xF1A0, symBinAddr: 0x33B04, symSize: 0x8 }
  - { offsetInCU: 0x2F1A, offset: 0x1D642, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultCSgs5Error_pSgIeggg_SgWOe', symObjAddr: 0xF1A8, symBinAddr: 0x33B0C, symSize: 0x10 }
  - { offsetInCU: 0x2F2D, offset: 0x1D655, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC11application_4open17sourceApplication10annotationSbSo13UIApplicationCSg_10Foundation3URLVSgSSSgypSgtFyAA01_C20CompletionParametersCcfU_TA', symObjAddr: 0xF278, symBinAddr: 0x33B7C, symSize: 0x24 }
  - { offsetInCU: 0x2F64, offset: 0x1D68C, size: 0x8, addend: 0x0, symName: '_$sSh5IndexV8_VariantOyx__GSHRzlWOy', symObjAddr: 0xF2A8, symBinAddr: 0x33BA0, symSize: 0xC }
  - { offsetInCU: 0x2F77, offset: 0x1D69F, size: 0x8, addend: 0x0, symName: '_$sSD8IteratorV8_VariantOyxq___GSHRzr0_lWOe', symObjAddr: 0xF2B4, symBinAddr: 0x33BAC, symSize: 0x8 }
  - { offsetInCU: 0x2F8A, offset: 0x1D6B2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVWOb', symObjAddr: 0xF2BC, symBinAddr: 0x33BB4, symSize: 0x44 }
  - { offsetInCU: 0x2F9D, offset: 0x1D6C5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVWOh', symObjAddr: 0xF300, symBinAddr: 0x33BF8, symSize: 0x3C }
  - { offsetInCU: 0x2FBA, offset: 0x1D6E2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultCSgs5Error_pSgIeggg_AdFytIegnnr_TRTA', symObjAddr: 0xF3E8, symBinAddr: 0x33C98, symSize: 0x28 }
  - { offsetInCU: 0x2FE2, offset: 0x1D70A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultCSgs5Error_pSgytIegnnr_AdFIeggg_TRTA', symObjAddr: 0xF410, symBinAddr: 0x33CC0, symSize: 0x8 }
  - { offsetInCU: 0x2FF5, offset: 0x1D71D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultCSgs5Error_pSgIeggg_SgWOy', symObjAddr: 0xF418, symBinAddr: 0x33CC8, symSize: 0x10 }
  - { offsetInCU: 0x3008, offset: 0x1D730, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5logIn33_C218275A97333B874EDDFE627110566CLLyyFySb_s5Error_pSgtcfU_TA', symObjAddr: 0xF428, symBinAddr: 0x33CD8, symSize: 0x8 }
  - { offsetInCU: 0x3025, offset: 0x1D74D, size: 0x8, addend: 0x0, symName: '_$sSbs5Error_pSgytIegnnr_SbABIegyg_TRTA', symObjAddr: 0xF430, symBinAddr: 0x33CE0, symSize: 0x50 }
  - { offsetInCU: 0x3057, offset: 0x1D77F, size: 0x8, addend: 0x0, symName: '_$sSbs5Error_pSgIegyg_SbABytIegnnr_TRTA', symObjAddr: 0xF480, symBinAddr: 0x33D30, symSize: 0x28 }
  - { offsetInCU: 0x30F4, offset: 0x1D81C, size: 0x8, addend: 0x0, symName: '_$sSlsE3mapySayqd__Gqd__7ElementQzKXEKlFSay12FBSDKCoreKit10PermissionOG_SSTg5085$s13FBSDKLoginKit12LoginManagerC5logIn11permissions14viewController10completionySay09c4B010E91OG_So06UIViewI0CSgyAA0C6ResultOcSgtFSSAJcfu_32f5ef5a0b9ade21eb65ffea7b618f60adAJSSTf3nnpk_nTf1cn_n', symObjAddr: 0x11C, symBinAddr: 0x24D40, symSize: 0xFC }
  - { offsetInCU: 0x32C3, offset: 0x1D9EB, size: 0x8, addend: 0x0, symName: '_$sSlsE3mapySayqd__Gqd__7ElementQzKXEKlFShy13FBSDKLoginKit12FBPermissionCG_SSTg504$s13c132Kit12LoginManagerC15logInParameters13configuration12loggingToken20authenticationMethodSDyS2SGSgAA0C13ConfigurationCSg_SSSgSStFSSAA12E52Ccfu_32e0d58b938ad0b6cb17de1b825049cc00AOSSTf3nnpk_nTf1cn_n', symObjAddr: 0x218, symBinAddr: 0x24E3C, symSize: 0x2AC }
  - { offsetInCU: 0x3EC3, offset: 0x1E5EB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCACycfC', symObjAddr: 0xFC, symBinAddr: 0x24D20, symSize: 0x20 }
  - { offsetInCU: 0x3F18, offset: 0x1E640, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5logIn11permissions14viewController10completionySay09FBSDKCoreB010PermissionOG_So06UIViewI0CSgyAA0C6ResultOcSgtF', symObjAddr: 0x23C0, symBinAddr: 0x26FA4, symSize: 0x94 }
  - { offsetInCU: 0x3F78, offset: 0x1E6A0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5logIn11permissions14viewController10completionySay09FBSDKCoreB010PermissionOG_So06UIViewI0CSgyAA0C6ResultOcSgtFyAA0cdcN0CSg_s5Error_pSgtcfU_', symObjAddr: 0x2454, symBinAddr: 0x27038, symSize: 0x7C }
  - { offsetInCU: 0x402A, offset: 0x1E752, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC15logInParameters13configuration12loggingToken20authenticationMethodSDyS2SGSgAA0C13ConfigurationCSg_SSSgSStF', symObjAddr: 0x520C, symBinAddr: 0x29DF0, symSize: 0x19E8 }
  - { offsetInCU: 0x4EB6, offset: 0x1F5DE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC15logInParameters13configuration12loggingToken20authenticationMethodSDyS2SGSgAA0C13ConfigurationCSg_SSSgSStFA2MXEfU_', symObjAddr: 0x6BF4, symBinAddr: 0x2B7D8, symSize: 0x1C }
  - { offsetInCU: 0x4F1E, offset: 0x1F646, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC15defaultAudienceAA07DefaultF0Ovg', symObjAddr: 0x508, symBinAddr: 0x2512C, symSize: 0x44 }
  - { offsetInCU: 0x4F5D, offset: 0x1F685, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC15defaultAudienceAA07DefaultF0Ovs', symObjAddr: 0x594, symBinAddr: 0x251B8, symSize: 0x48 }
  - { offsetInCU: 0x4F83, offset: 0x1F6AB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC15defaultAudienceAA07DefaultF0OvM', symObjAddr: 0x5DC, symBinAddr: 0x25200, symSize: 0x44 }
  - { offsetInCU: 0x4FA0, offset: 0x1F6C8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC15defaultAudienceAA07DefaultF0OvM.resume.0', symObjAddr: 0x620, symBinAddr: 0x25244, symSize: 0x4 }
  - { offsetInCU: 0x4FBF, offset: 0x1F6E7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC7handlerAA010IdentifiedC13ResultHandlerVSgvg', symObjAddr: 0x624, symBinAddr: 0x25248, symSize: 0x58 }
  - { offsetInCU: 0x4FE2, offset: 0x1F70A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC7handlerAA010IdentifiedC13ResultHandlerVSgvs', symObjAddr: 0x6BC, symBinAddr: 0x252A0, symSize: 0x60 }
  - { offsetInCU: 0x5014, offset: 0x1F73C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC7handlerAA010IdentifiedC13ResultHandlerVSgvM', symObjAddr: 0x71C, symBinAddr: 0x25300, symSize: 0x44 }
  - { offsetInCU: 0x5049, offset: 0x1F771, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC13configurationAA0C13ConfigurationCSgvg', symObjAddr: 0x7A8, symBinAddr: 0x2538C, symSize: 0x50 }
  - { offsetInCU: 0x509A, offset: 0x1F7C2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC20requestedPermissionsShyAA12FBPermissionCGSgvg', symObjAddr: 0x904, symBinAddr: 0x254E8, symSize: 0x48 }
  - { offsetInCU: 0x50D9, offset: 0x1F801, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC20requestedPermissionsShyAA12FBPermissionCGSgvM', symObjAddr: 0x9FC, symBinAddr: 0x255E0, symSize: 0x44 }
  - { offsetInCU: 0x50FC, offset: 0x1F824, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC6loggerAA0cD6LoggerCSgvg', symObjAddr: 0xA40, symBinAddr: 0x25624, symSize: 0x48 }
  - { offsetInCU: 0x511F, offset: 0x1F847, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC6loggerAA0cD6LoggerCSgvM', symObjAddr: 0xAF4, symBinAddr: 0x256D8, symSize: 0x44 }
  - { offsetInCU: 0x5142, offset: 0x1F86A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5stateAA0cD5StateOvg', symObjAddr: 0xB38, symBinAddr: 0x2571C, symSize: 0x44 }
  - { offsetInCU: 0x5165, offset: 0x1F88D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5stateAA0cD5StateOvs', symObjAddr: 0xB7C, symBinAddr: 0x25760, symSize: 0x48 }
  - { offsetInCU: 0x5197, offset: 0x1F8BF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5stateAA0cD5StateOvM', symObjAddr: 0xBC4, symBinAddr: 0x257A8, symSize: 0x44 }
  - { offsetInCU: 0x51CC, offset: 0x1F8F4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC17usedSafariSessionSbvg', symObjAddr: 0xC4C, symBinAddr: 0x25830, symSize: 0x44 }
  - { offsetInCU: 0x5229, offset: 0x1F951, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC17usedSafariSessionSbvs', symObjAddr: 0xCDC, symBinAddr: 0x258C0, symSize: 0x48 }
  - { offsetInCU: 0x524C, offset: 0x1F974, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC17usedSafariSessionSbvM', symObjAddr: 0xD24, symBinAddr: 0x25908, symSize: 0x44 }
  - { offsetInCU: 0x5281, offset: 0x1F9A9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC012isPerformingC0Sbvg', symObjAddr: 0xDB4, symBinAddr: 0x25998, symSize: 0x4C }
  - { offsetInCU: 0x52C6, offset: 0x1F9EE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC22configuredDependenciesAC06ObjectF0VSgvg', symObjAddr: 0xE00, symBinAddr: 0x259E4, symSize: 0x58 }
  - { offsetInCU: 0x52E3, offset: 0x1FA0B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC22configuredDependenciesAC06ObjectF0VSgvs', symObjAddr: 0xE58, symBinAddr: 0x25A3C, symSize: 0x60 }
  - { offsetInCU: 0x5309, offset: 0x1FA31, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC22configuredDependenciesAC06ObjectF0VSgvM', symObjAddr: 0xEB8, symBinAddr: 0x25A9C, symSize: 0x44 }
  - { offsetInCU: 0x5326, offset: 0x1FA4E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC19defaultDependenciesAC06ObjectF0VSgvg', symObjAddr: 0xEFC, symBinAddr: 0x25AE0, symSize: 0xE0 }
  - { offsetInCU: 0x5348, offset: 0x1FA70, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC19defaultDependenciesAC06ObjectF0VSgvgAGyXEfU_', symObjAddr: 0x10D8, symBinAddr: 0x25CBC, symSize: 0x27C }
  - { offsetInCU: 0x549F, offset: 0x1FBC7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC19defaultDependenciesAC06ObjectF0VSgvs', symObjAddr: 0x106C, symBinAddr: 0x25C50, symSize: 0x6C }
  - { offsetInCU: 0x553D, offset: 0x1FC65, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV17accessTokenWallet014authenticationhI012errorFactory012graphRequestL015internalUtility13keychainStore014loginCompleterL015profileProvider8settings9urlOpenerAESo011FBSDKAccessH9Providing_pXp_So019FBSDKAuthenticationH9Providing_pXpSo18FBSDKErrorCreating_pSo010FBSDKGraphnL0_pSo27FBSDKAppAvailabilityChecker_So26FBSDKAppURLSchemeProvidingSo15FBSDKURLHostingpSo013FBSDKKeychainR0_pAA0ctL8Protocol_p09FBSDKCoreB016ProfileProviding_pXpAY16SettingsProtocol_pSo14FBSDKURLOpener_ptcfC', symObjAddr: 0x1354, symBinAddr: 0x25F38, symSize: 0x4C }
  - { offsetInCU: 0x5550, offset: 0x1FC78, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC19defaultDependenciesAC06ObjectF0VSgvM', symObjAddr: 0x13A0, symBinAddr: 0x25F84, symSize: 0x4C }
  - { offsetInCU: 0x5573, offset: 0x1FC9B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC19defaultDependenciesAC06ObjectF0VSgvM.resume.0', symObjAddr: 0x13EC, symBinAddr: 0x25FD0, symSize: 0x10C }
  - { offsetInCU: 0x55EA, offset: 0x1FD12, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC15defaultAudienceAcA07DefaultF0O_tcfC', symObjAddr: 0x14F8, symBinAddr: 0x260DC, symSize: 0x6C }
  - { offsetInCU: 0x562C, offset: 0x1FD54, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC15defaultAudienceAcA07DefaultF0O_tcfc', symObjAddr: 0x1564, symBinAddr: 0x26148, symSize: 0x68 }
  - { offsetInCU: 0x56BB, offset: 0x1FDE3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5logIn4from13configuration10completionySo16UIViewControllerCSg_AA0C13ConfigurationCSgyAA0cdC6ResultCSg_s5Error_pSgtctF', symObjAddr: 0x1630, symBinAddr: 0x26214, symSize: 0x124 }
  - { offsetInCU: 0x5761, offset: 0x1FE89, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC11commonLogIn33_C218275A97333B874EDDFE627110566CLL4from13configuration10completionySo16UIViewControllerCSg_AA0C13ConfigurationCSgyAA0cdC6ResultCSg_s5Error_pSgtcSgtF', symObjAddr: 0x1754, symBinAddr: 0x26338, symSize: 0x330 }
  - { offsetInCU: 0x588E, offset: 0x1FFB6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5logIn14viewController13configuration10completionySo06UIViewH0CSg_AA0C13ConfigurationCSgyAA0C6ResultOctF', symObjAddr: 0x1B94, symBinAddr: 0x26778, symSize: 0x78 }
  - { offsetInCU: 0x58DC, offset: 0x20004, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5logIn14viewController13configuration10completionySo06UIViewH0CSg_AA0C13ConfigurationCSgyAA0C6ResultOctFyAA0cdcM0CSg_s5Error_pSgtcfU_', symObjAddr: 0x1C0C, symBinAddr: 0x267F0, symSize: 0x74 }
  - { offsetInCU: 0x59CC, offset: 0x200F4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC13invokeHandler33_C218275A97333B874EDDFE627110566CLL6result5erroryAA0cdC6ResultCSg_s5Error_pSgtF', symObjAddr: 0x1CE0, symBinAddr: 0x268C4, symSize: 0x3FC }
  - { offsetInCU: 0x5AE8, offset: 0x20210, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5logIn11permissions4from7handlerySaySSG_So16UIViewControllerCSgyAA0cdC6ResultCSg_s5Error_pSgtcSgtF', symObjAddr: 0x20DC, symBinAddr: 0x26CC0, symSize: 0x210 }
  - { offsetInCU: 0x5CB2, offset: 0x203DA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC27handleImplicitCancelOfLogIn33_C218275A97333B874EDDFE627110566CLLyyF', symObjAddr: 0x26BC, symBinAddr: 0x272A0, symSize: 0x16C }
  - { offsetInCU: 0x5DEC, offset: 0x20514, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC19performBrowserLogIn33_C218275A97333B874EDDFE627110566CLL7handleryySb_s5Error_pSgtcSg_tF', symObjAddr: 0x2828, symBinAddr: 0x2740C, symSize: 0xD90 }
  - { offsetInCU: 0x611D, offset: 0x20845, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC19performBrowserLogIn33_C218275A97333B874EDDFE627110566CLL7handleryySb_s5Error_pSgtcSg_tFySb_AHtcyKXEfu1_ySb_AHtcfU_', symObjAddr: 0x7428, symBinAddr: 0x2C00C, symSize: 0x4 }
  - { offsetInCU: 0x6138, offset: 0x20860, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC19performBrowserLogIn33_C218275A97333B874EDDFE627110566CLL7handleryySb_s5Error_pSgtcSg_tFySb_AHtcyKXEfu2_ySb_AHtcfU0_', symObjAddr: 0x742C, symBinAddr: 0x2C010, symSize: 0x4 }
  - { offsetInCU: 0x6192, offset: 0x208BA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC21reauthorizeDataAccess4from7handlerySo16UIViewControllerC_yAA0cdC6ResultCSg_s5Error_pSgtctF', symObjAddr: 0x35B8, symBinAddr: 0x2819C, symSize: 0x458 }
  - { offsetInCU: 0x6374, offset: 0x20A9C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC6logOutyyF', symObjAddr: 0x3A84, symBinAddr: 0x28668, symSize: 0x1C4 }
  - { offsetInCU: 0x64D0, offset: 0x20BF8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC22completeAuthentication10parameters15expectChallengeyAA01_C20CompletionParametersC_SbtF', symObjAddr: 0x3C70, symBinAddr: 0x28854, symSize: 0x538 }
  - { offsetInCU: 0x663C, offset: 0x20D64, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC22storeExpectedChallenge33_C218275A97333B874EDDFE627110566CLLyySSSgF', symObjAddr: 0x41A8, symBinAddr: 0x28D8C, symSize: 0xFC }
  - { offsetInCU: 0x668C, offset: 0x20DB4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC16getSuccessResult33_C218275A97333B874EDDFE627110566CLL4fromAA0cdcG0CAA01_C20CompletionParametersC_tF', symObjAddr: 0x42A4, symBinAddr: 0x28E88, symSize: 0x7A4 }
  - { offsetInCU: 0x6D06, offset: 0x2142E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC24validateReauthentication11accessToken11loginResult04userH5NonceySo011FBSDKAccessH0C_AA0cdcJ0CSgSSSgtF', symObjAddr: 0x4A48, symBinAddr: 0x2962C, symSize: 0x3A8 }
  - { offsetInCU: 0x6E8C, offset: 0x215B4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC24validateReauthentication11accessToken11loginResult04userH5NonceySo011FBSDKAccessH0C_AA0cdcJ0CSgSSSgtFySo27FBSDKGraphRequestConnecting_pSg_ypSgs5Error_pSgtcfU_', symObjAddr: 0x7048, symBinAddr: 0x2BC2C, symSize: 0x334 }
  - { offsetInCU: 0x6F9A, offset: 0x216C2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18getCancelledResult33_C218275A97333B874EDDFE627110566CLL4fromAA0cdcG0CAA01_C20CompletionParametersC_tF', symObjAddr: 0x4DF0, symBinAddr: 0x299D4, symSize: 0x1C0 }
  - { offsetInCU: 0x70E6, offset: 0x2180E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC19setGlobalProperties33_C218275A97333B874EDDFE627110566CLL10parameters11loginResultyAA01_C20CompletionParametersC_AA0cdcN0CSgtF', symObjAddr: 0x4FB0, symBinAddr: 0x29B94, symSize: 0x1F4 }
  - { offsetInCU: 0x732A, offset: 0x21A52, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC010addLimitedC14ShimParameters33_C218275A97333B874EDDFE627110566CLL10parameters13configurationySDyS2SGz_AA0C13ConfigurationCtF', symObjAddr: 0x6C10, symBinAddr: 0x2B7F4, symSize: 0x138 }
  - { offsetInCU: 0x740E, offset: 0x21B36, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC25storeExpectedCodeVerifier33_C218275A97333B874EDDFE627110566CLLyyAA0gH0CSgF', symObjAddr: 0x6F48, symBinAddr: 0x2BB2C, symSize: 0x100 }
  - { offsetInCU: 0x745A, offset: 0x21B82, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC29getRecentlyGrantedPermissions4fromShyAA12FBPermissionCGAH_tF', symObjAddr: 0x7494, symBinAddr: 0x2C078, symSize: 0x148 }
  - { offsetInCU: 0x752C, offset: 0x21C54, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC30getRecentlyDeclinedPermissions4fromShyAA12FBPermissionCGAH_tF', symObjAddr: 0x75DC, symBinAddr: 0x2C1C0, symSize: 0xC4 }
  - { offsetInCU: 0x75C5, offset: 0x21CED, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCACycfc', symObjAddr: 0x78A0, symBinAddr: 0x2C484, symSize: 0x124 }
  - { offsetInCU: 0x75E8, offset: 0x21D10, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCfD', symObjAddr: 0x79E4, symBinAddr: 0x2C5C8, symSize: 0x34 }
  - { offsetInCU: 0x760F, offset: 0x21D37, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC10makeOpenerACyFZ', symObjAddr: 0x7ABC, symBinAddr: 0x2C6A0, symSize: 0x20 }
  - { offsetInCU: 0x765C, offset: 0x21D84, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC11application_4open17sourceApplication10annotationSbSo13UIApplicationCSg_10Foundation3URLVSgSSSgypSgtF', symObjAddr: 0x7B00, symBinAddr: 0x2C6E4, symSize: 0xC }
  - { offsetInCU: 0x7699, offset: 0x21DC1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC7canOpen_3for17sourceApplication10annotationSb10Foundation3URLV_So13UIApplicationCSgSSSgypSgtF', symObjAddr: 0x7B0C, symBinAddr: 0x2C6F0, symSize: 0x8 }
  - { offsetInCU: 0x76B9, offset: 0x21DE1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC26applicationDidBecomeActiveyySo13UIApplicationCF', symObjAddr: 0x7DC0, symBinAddr: 0x2C9A4, symSize: 0x50 }
  - { offsetInCU: 0x7721, offset: 0x21E49, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC19isAuthenticationURLySb10Foundation0G0VF', symObjAddr: 0x7E98, symBinAddr: 0x2CA7C, symSize: 0x60 }
  - { offsetInCU: 0x776B, offset: 0x21E93, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC21shouldStopPropagation2ofSb10Foundation3URLV_tF', symObjAddr: 0x7FDC, symBinAddr: 0x2CBC0, symSize: 0x194 }
  - { offsetInCU: 0x78C8, offset: 0x21FF0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV17accessTokenWalletSo011FBSDKAccessH9Providing_pXpvg', symObjAddr: 0x83B4, symBinAddr: 0x2CF98, symSize: 0x8 }
  - { offsetInCU: 0x78DB, offset: 0x22003, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV17accessTokenWalletSo011FBSDKAccessH9Providing_pXpvs', symObjAddr: 0x83BC, symBinAddr: 0x2CFA0, symSize: 0x8 }
  - { offsetInCU: 0x78EE, offset: 0x22016, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV17accessTokenWalletSo011FBSDKAccessH9Providing_pXpvM', symObjAddr: 0x83C4, symBinAddr: 0x2CFA8, symSize: 0x10 }
  - { offsetInCU: 0x7901, offset: 0x22029, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV17accessTokenWalletSo011FBSDKAccessH9Providing_pXpvM.resume.0', symObjAddr: 0x83D4, symBinAddr: 0x2CFB8, symSize: 0x4 }
  - { offsetInCU: 0x7914, offset: 0x2203C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV25authenticationTokenWalletSo019FBSDKAuthenticationH9Providing_pXpvg', symObjAddr: 0x83D8, symBinAddr: 0x2CFBC, symSize: 0x8 }
  - { offsetInCU: 0x7927, offset: 0x2204F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV25authenticationTokenWalletSo019FBSDKAuthenticationH9Providing_pXpvs', symObjAddr: 0x83E0, symBinAddr: 0x2CFC4, symSize: 0x8 }
  - { offsetInCU: 0x793A, offset: 0x22062, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV25authenticationTokenWalletSo019FBSDKAuthenticationH9Providing_pXpvM', symObjAddr: 0x83E8, symBinAddr: 0x2CFCC, symSize: 0x10 }
  - { offsetInCU: 0x794D, offset: 0x22075, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV25authenticationTokenWalletSo019FBSDKAuthenticationH9Providing_pXpvM.resume.0', symObjAddr: 0x83F8, symBinAddr: 0x2CFDC, symSize: 0x4 }
  - { offsetInCU: 0x7960, offset: 0x22088, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV12errorFactorySo18FBSDKErrorCreating_pvg', symObjAddr: 0x83FC, symBinAddr: 0x2CFE0, symSize: 0x8 }
  - { offsetInCU: 0x7973, offset: 0x2209B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV12errorFactorySo18FBSDKErrorCreating_pvs', symObjAddr: 0x8404, symBinAddr: 0x2CFE8, symSize: 0x28 }
  - { offsetInCU: 0x7986, offset: 0x220AE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV12errorFactorySo18FBSDKErrorCreating_pvM', symObjAddr: 0x842C, symBinAddr: 0x2D010, symSize: 0x10 }
  - { offsetInCU: 0x7999, offset: 0x220C1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV12errorFactorySo18FBSDKErrorCreating_pvM.resume.0', symObjAddr: 0x843C, symBinAddr: 0x2D020, symSize: 0x4 }
  - { offsetInCU: 0x79AC, offset: 0x220D4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV19graphRequestFactorySo010FBSDKGraphhI0_pvg', symObjAddr: 0x8440, symBinAddr: 0x2D024, symSize: 0x8 }
  - { offsetInCU: 0x79BF, offset: 0x220E7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV19graphRequestFactorySo010FBSDKGraphhI0_pvs', symObjAddr: 0x8448, symBinAddr: 0x2D02C, symSize: 0x28 }
  - { offsetInCU: 0x79D2, offset: 0x220FA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV19graphRequestFactorySo010FBSDKGraphhI0_pvM', symObjAddr: 0x8470, symBinAddr: 0x2D054, symSize: 0x10 }
  - { offsetInCU: 0x79E5, offset: 0x2210D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV19graphRequestFactorySo010FBSDKGraphhI0_pvM.resume.0', symObjAddr: 0x8480, symBinAddr: 0x2D064, symSize: 0x4 }
  - { offsetInCU: 0x79F8, offset: 0x22120, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV15internalUtilitySo27FBSDKAppAvailabilityChecker_So0I18URLSchemeProvidingSo15FBSDKURLHostingpvg', symObjAddr: 0x8484, symBinAddr: 0x2D068, symSize: 0x8 }
  - { offsetInCU: 0x7A0B, offset: 0x22133, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV15internalUtilitySo27FBSDKAppAvailabilityChecker_So0I18URLSchemeProvidingSo15FBSDKURLHostingpvs', symObjAddr: 0x848C, symBinAddr: 0x2D070, symSize: 0x28 }
  - { offsetInCU: 0x7A1E, offset: 0x22146, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV15internalUtilitySo27FBSDKAppAvailabilityChecker_So0I18URLSchemeProvidingSo15FBSDKURLHostingpvM', symObjAddr: 0x84B4, symBinAddr: 0x2D098, symSize: 0x10 }
  - { offsetInCU: 0x7A31, offset: 0x22159, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV15internalUtilitySo27FBSDKAppAvailabilityChecker_So0I18URLSchemeProvidingSo15FBSDKURLHostingpvM.resume.0', symObjAddr: 0x84C4, symBinAddr: 0x2D0A8, symSize: 0x4 }
  - { offsetInCU: 0x7A44, offset: 0x2216C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV13keychainStoreSo013FBSDKKeychainH0_pvg', symObjAddr: 0x84C8, symBinAddr: 0x2D0AC, symSize: 0x8 }
  - { offsetInCU: 0x7A57, offset: 0x2217F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV13keychainStoreSo013FBSDKKeychainH0_pvs', symObjAddr: 0x84D0, symBinAddr: 0x2D0B4, symSize: 0x28 }
  - { offsetInCU: 0x7A6A, offset: 0x22192, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV13keychainStoreSo013FBSDKKeychainH0_pvM', symObjAddr: 0x84F8, symBinAddr: 0x2D0DC, symSize: 0x10 }
  - { offsetInCU: 0x7A7D, offset: 0x221A5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV13keychainStoreSo013FBSDKKeychainH0_pvM.resume.0', symObjAddr: 0x8508, symBinAddr: 0x2D0EC, symSize: 0x4 }
  - { offsetInCU: 0x7A90, offset: 0x221B8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV21loginCompleterFactoryAA0chI8Protocol_pvg', symObjAddr: 0x850C, symBinAddr: 0x2D0F0, symSize: 0xC }
  - { offsetInCU: 0x7AA3, offset: 0x221CB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV21loginCompleterFactoryAA0chI8Protocol_pvs', symObjAddr: 0x8518, symBinAddr: 0x2D0FC, symSize: 0x30 }
  - { offsetInCU: 0x7AB6, offset: 0x221DE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV21loginCompleterFactoryAA0chI8Protocol_pvM', symObjAddr: 0x8548, symBinAddr: 0x2D12C, symSize: 0x10 }
  - { offsetInCU: 0x7AC9, offset: 0x221F1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV21loginCompleterFactoryAA0chI8Protocol_pvM.resume.0', symObjAddr: 0x8558, symBinAddr: 0x2D13C, symSize: 0x4 }
  - { offsetInCU: 0x7ADC, offset: 0x22204, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV15profileProvider09FBSDKCoreB016ProfileProviding_pXpvg', symObjAddr: 0x855C, symBinAddr: 0x2D140, symSize: 0x8 }
  - { offsetInCU: 0x7AEF, offset: 0x22217, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV15profileProvider09FBSDKCoreB016ProfileProviding_pXpvs', symObjAddr: 0x8564, symBinAddr: 0x2D148, symSize: 0x8 }
  - { offsetInCU: 0x7B02, offset: 0x2222A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV15profileProvider09FBSDKCoreB016ProfileProviding_pXpvM', symObjAddr: 0x856C, symBinAddr: 0x2D150, symSize: 0x10 }
  - { offsetInCU: 0x7B15, offset: 0x2223D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV15profileProvider09FBSDKCoreB016ProfileProviding_pXpvM.resume.0', symObjAddr: 0x857C, symBinAddr: 0x2D160, symSize: 0x4 }
  - { offsetInCU: 0x7B28, offset: 0x22250, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV8settings09FBSDKCoreB016SettingsProtocol_pvg', symObjAddr: 0x8580, symBinAddr: 0x2D164, symSize: 0x8 }
  - { offsetInCU: 0x7B3B, offset: 0x22263, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV8settings09FBSDKCoreB016SettingsProtocol_pvs', symObjAddr: 0x8588, symBinAddr: 0x2D16C, symSize: 0x28 }
  - { offsetInCU: 0x7B4E, offset: 0x22276, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV8settings09FBSDKCoreB016SettingsProtocol_pvM', symObjAddr: 0x85B0, symBinAddr: 0x2D194, symSize: 0x10 }
  - { offsetInCU: 0x7B61, offset: 0x22289, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV8settings09FBSDKCoreB016SettingsProtocol_pvM.resume.0', symObjAddr: 0x85C0, symBinAddr: 0x2D1A4, symSize: 0x4 }
  - { offsetInCU: 0x7B74, offset: 0x2229C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV9urlOpenerSo14FBSDKURLOpener_pvg', symObjAddr: 0x85C4, symBinAddr: 0x2D1A8, symSize: 0x8 }
  - { offsetInCU: 0x7B87, offset: 0x222AF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV9urlOpenerSo14FBSDKURLOpener_pvs', symObjAddr: 0x85CC, symBinAddr: 0x2D1B0, symSize: 0x28 }
  - { offsetInCU: 0x7B9A, offset: 0x222C2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV9urlOpenerSo14FBSDKURLOpener_pvM', symObjAddr: 0x85F4, symBinAddr: 0x2D1D8, symSize: 0x10 }
  - { offsetInCU: 0x7BAD, offset: 0x222D5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV9urlOpenerSo14FBSDKURLOpener_pvM.resume.0', symObjAddr: 0x8604, symBinAddr: 0x2D1E8, symSize: 0x4 }
  - { offsetInCU: 0x7BEF, offset: 0x22317, size: 0x8, addend: 0x0, symName: '_$sSo16FBSDKAccessTokenC11tokenString11permissions19declinedPermissions07expiredG05appID04userJ014expirationDate07refreshM0020dataAccessExpirationM0ABSS_SaySSGA2LS2S10Foundation0M0VSgA2PtcfcTO', symObjAddr: 0x8834, symBinAddr: 0x2D3D4, symSize: 0x248 }
  - { offsetInCU: 0x7C0E, offset: 0x22336, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV7_insert2at3key5valueys10_HashTableV6BucketV_xnq_ntFSS_ypTg5', symObjAddr: 0x8A7C, symBinAddr: 0x2D61C, symSize: 0x6C }
  - { offsetInCU: 0x7C93, offset: 0x223BB, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV7_insert2at3key5valueys10_HashTableV6BucketV_xnq_ntFSo26FBSDKAppEventParameterNamea_ypTg5', symObjAddr: 0x8AE8, symBinAddr: 0x2D688, symSize: 0x68 }
  - { offsetInCU: 0x7D06, offset: 0x2242E, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV7_insert2at3key5valueys10_HashTableV6BucketV_xnq_ntFSS_SSTg5', symObjAddr: 0x8B50, symBinAddr: 0x2D6F0, symSize: 0x54 }
  - { offsetInCU: 0x7D97, offset: 0x224BF, size: 0x8, addend: 0x0, symName: '_$sSh8_VariantV6insertySb8inserted_x17memberAfterInserttxnF13FBSDKLoginKit12FBPermissionC_Tg5', symObjAddr: 0x8E9C, symBinAddr: 0x2DA3C, symSize: 0x2B4 }
  - { offsetInCU: 0x7E69, offset: 0x22591, size: 0x8, addend: 0x0, symName: '_$sSh8_VariantV6insertySb8inserted_x17memberAfterInserttxnFSS_Tg5', symObjAddr: 0x9150, symBinAddr: 0x2DCF0, symSize: 0x1AC }
  - { offsetInCU: 0x7F4C, offset: 0x22674, size: 0x8, addend: 0x0, symName: '_$sSh8_VariantV6insertySb8inserted_x17memberAfterInserttxnF12FBSDKCoreKit10PermissionO_Tg5', symObjAddr: 0x92FC, symBinAddr: 0x2DE9C, symSize: 0x254 }
  - { offsetInCU: 0x7FCA, offset: 0x226F2, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV9insertNew_2at8isUniqueyxn_s10_HashTableV6BucketVSbtF13FBSDKLoginKit12FBPermissionC_Tg5', symObjAddr: 0x9748, symBinAddr: 0x2E2E8, symSize: 0x19C }
  - { offsetInCU: 0x8052, offset: 0x2277A, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV9insertNew_2at8isUniqueyxn_s10_HashTableV6BucketVSbtFSS_Tg5', symObjAddr: 0x98E4, symBinAddr: 0x2E484, symSize: 0x1B4 }
  - { offsetInCU: 0x813B, offset: 0x22863, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV9insertNew_2at8isUniqueyxn_s10_HashTableV6BucketVSbtF12FBSDKCoreKit10PermissionO_Tg5', symObjAddr: 0x9A98, symBinAddr: 0x2E638, symSize: 0x23C }
  - { offsetInCU: 0x81AD, offset: 0x228D5, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV4copyyyF13FBSDKLoginKit12FBPermissionC_Tg5', symObjAddr: 0x9CD4, symBinAddr: 0x2E874, symSize: 0x1A4 }
  - { offsetInCU: 0x8218, offset: 0x22940, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV4copyyyFSS_Tg5', symObjAddr: 0x9E78, symBinAddr: 0x2EA18, symSize: 0x1AC }
  - { offsetInCU: 0x8283, offset: 0x229AB, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV4copyyyF12FBSDKCoreKit10PermissionO_Tg5', symObjAddr: 0xA024, symBinAddr: 0x2EBC4, symSize: 0x220 }
  - { offsetInCU: 0x82EE, offset: 0x22A16, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV13copyAndResize8capacityySi_tF13FBSDKLoginKit12FBPermissionC_Tg5', symObjAddr: 0xA244, symBinAddr: 0x2EDE4, symSize: 0x268 }
  - { offsetInCU: 0x838A, offset: 0x22AB2, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV13copyAndResize8capacityySi_tFSS_Tg5', symObjAddr: 0xA4AC, symBinAddr: 0x2F04C, symSize: 0x29C }
  - { offsetInCU: 0x842A, offset: 0x22B52, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV13copyAndResize8capacityySi_tF12FBSDKCoreKit10PermissionO_Tg5', symObjAddr: 0xA748, symBinAddr: 0x2F2E8, symSize: 0x318 }
  - { offsetInCU: 0x84B9, offset: 0x22BE1, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV6resize8capacityySi_tF13FBSDKLoginKit12FBPermissionC_Tg5', symObjAddr: 0xAA60, symBinAddr: 0x2F600, symSize: 0x288 }
  - { offsetInCU: 0x857B, offset: 0x22CA3, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV6resize8capacityySi_tFSS_Tg5', symObjAddr: 0xACE8, symBinAddr: 0x2F888, symSize: 0x2C8 }
  - { offsetInCU: 0x8651, offset: 0x22D79, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV6resize8capacityySi_tF12FBSDKCoreKit10PermissionO_Tg5', symObjAddr: 0xAFB0, symBinAddr: 0x2FB50, symSize: 0x358 }
  - { offsetInCU: 0x86FA, offset: 0x22E22, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV16_unsafeInsertNewyyxnF13FBSDKLoginKit12FBPermissionC_Tg5', symObjAddr: 0xB308, symBinAddr: 0x2FEA8, symSize: 0x80 }
  - { offsetInCU: 0x8753, offset: 0x22E7B, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV12mutatingFind_8isUniques10_HashTableV6BucketV6bucket_Sb5foundtx_SbtFSo26FBSDKAppEventParameterNamea_ypTg5', symObjAddr: 0xB388, symBinAddr: 0x2FF28, symSize: 0xC8 }
  - { offsetInCU: 0x879F, offset: 0x22EC7, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV20_copyOrMoveAndResize8capacity12moveElementsySi_SbtFSo26FBSDKAppEventParameterNamea_ypTg5', symObjAddr: 0xB560, symBinAddr: 0x30100, symSize: 0x368 }
  - { offsetInCU: 0x8888, offset: 0x22FB0, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV20_copyOrMoveAndResize8capacity12moveElementsySi_SbtFSS_ypTg5', symObjAddr: 0xB8C8, symBinAddr: 0x30468, symSize: 0x340 }
  - { offsetInCU: 0x8971, offset: 0x23099, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV20_copyOrMoveAndResize8capacity12moveElementsySi_SbtFSS_SSTg5', symObjAddr: 0xBC08, symBinAddr: 0x307A8, symSize: 0x354 }
  - { offsetInCU: 0x8A4D, offset: 0x23175, size: 0x8, addend: 0x0, symName: '_$sSh8_VariantV12intersectionys10_NativeSetVyxGShyxGF13FBSDKLoginKit12FBPermissionC_Tg5', symObjAddr: 0xBF5C, symBinAddr: 0x30AFC, symSize: 0x394 }
  - { offsetInCU: 0x8BAF, offset: 0x232D7, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV13extractSubset5using5countAByxGs13_UnsafeBitsetV_SitF13FBSDKLoginKit12FBPermissionC_Tg5', symObjAddr: 0xCBC8, symBinAddr: 0x31768, symSize: 0x220 }
  - { offsetInCU: 0x8C58, offset: 0x23380, size: 0x8, addend: 0x0, symName: '_$sShyxSh5IndexVyx_Gcig13FBSDKLoginKit12FBPermissionC_Tg5', symObjAddr: 0xCDE8, symBinAddr: 0x31988, symSize: 0x2DC }
  - { offsetInCU: 0x8CEA, offset: 0x23412, size: 0x8, addend: 0x0, symName: '_$sSTsE21_copySequenceContents12initializing8IteratorQz_SitSry7ElementQzG_tFShySSG_Tg5', symObjAddr: 0xD0C4, symBinAddr: 0x31C64, symSize: 0x1B0 }
  - { offsetInCU: 0x8E0E, offset: 0x23536, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5logIn33_C218275A97333B874EDDFE627110566CLL11permissions7handleryShyAA12FBPermissionCG_yAA0cdC6ResultCSg_s5Error_pSgtcSgtFTf4dnn_n', symObjAddr: 0xD748, symBinAddr: 0x32214, symSize: 0x22C }
  - { offsetInCU: 0x8F10, offset: 0x23638, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC7canOpen_3for17sourceApplication10annotationSb10Foundation3URLV_So13UIApplicationCSgSSSgypSgtFTf4nddnn_n', symObjAddr: 0xE404, symBinAddr: 0x32ED0, symSize: 0x1A4 }
  - { offsetInCU: 0x905F, offset: 0x23787, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC11application_4open17sourceApplication10annotationSbSo13UIApplicationCSg_10Foundation3URLVSgSSSgypSgtFTf4dndnn_n', symObjAddr: 0xE5A8, symBinAddr: 0x33074, symSize: 0x518 }
  - { offsetInCU: 0x4E, offset: 0x23AA5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO10identifierSo013FBSDKAppEventF4NameavpZ', symObjAddr: 0x5F20, symBinAddr: 0x5E790, symSize: 0x0 }
  - { offsetInCU: 0x68, offset: 0x23ABF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO9timestampSo013FBSDKAppEventF4NameavpZ', symObjAddr: 0x5F28, symBinAddr: 0x5E798, symSize: 0x0 }
  - { offsetInCU: 0x82, offset: 0x23AD9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO6resultSo013FBSDKAppEventF4NameavpZ', symObjAddr: 0x5F30, symBinAddr: 0x5E7A0, symSize: 0x0 }
  - { offsetInCU: 0x9C, offset: 0x23AF3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO10authMethodSo013FBSDKAppEventF4NameavpZ', symObjAddr: 0x5F38, symBinAddr: 0x5E7A8, symSize: 0x0 }
  - { offsetInCU: 0xB6, offset: 0x23B0D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO9errorCodeSo013FBSDKAppEventF4NameavpZ', symObjAddr: 0x5F40, symBinAddr: 0x5E7B0, symSize: 0x0 }
  - { offsetInCU: 0xD0, offset: 0x23B27, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO12errorMessageSo013FBSDKAppEventF4NameavpZ', symObjAddr: 0x5F48, symBinAddr: 0x5E7B8, symSize: 0x0 }
  - { offsetInCU: 0xEA, offset: 0x23B41, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO6extrasSo013FBSDKAppEventF4NameavpZ', symObjAddr: 0x5F50, symBinAddr: 0x5E7C0, symSize: 0x0 }
  - { offsetInCU: 0x104, offset: 0x23B5B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO12loggingTokenSo013FBSDKAppEventF4NameavpZ', symObjAddr: 0x5F58, symBinAddr: 0x5E7C8, symSize: 0x0 }
  - { offsetInCU: 0x11E, offset: 0x23B75, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO19declinedPermissionsSo013FBSDKAppEventF4NameavpZ', symObjAddr: 0x5F60, symBinAddr: 0x5E7D0, symSize: 0x0 }
  - { offsetInCU: 0x139, offset: 0x23B90, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC19defaultDependenciesAC04TypeG0VSgvpZ', symObjAddr: 0x18650, symBinAddr: 0x606E8, symSize: 0x0 }
  - { offsetInCU: 0x1C8, offset: 0x23C1F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC22configuredDependenciesAC04TypeG0VSgvpZ', symObjAddr: 0x18678, symBinAddr: 0x60710, symSize: 0x0 }
  - { offsetInCU: 0x4A2, offset: 0x23EF9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO10identifier_WZ', symObjAddr: 0x120C, symBinAddr: 0x35050, symSize: 0x34 }
  - { offsetInCU: 0x4BC, offset: 0x23F13, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO9timestamp_WZ', symObjAddr: 0x1240, symBinAddr: 0x35084, symSize: 0x3C }
  - { offsetInCU: 0x4D6, offset: 0x23F2D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO6result_WZ', symObjAddr: 0x127C, symBinAddr: 0x350C0, symSize: 0x30 }
  - { offsetInCU: 0x4F0, offset: 0x23F47, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO10authMethod_WZ', symObjAddr: 0x12AC, symBinAddr: 0x350F0, symSize: 0x30 }
  - { offsetInCU: 0x50A, offset: 0x23F61, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO9errorCode_WZ', symObjAddr: 0x12DC, symBinAddr: 0x35120, symSize: 0x38 }
  - { offsetInCU: 0x524, offset: 0x23F7B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO12errorMessage_WZ', symObjAddr: 0x1314, symBinAddr: 0x35158, symSize: 0x3C }
  - { offsetInCU: 0x53E, offset: 0x23F95, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO6extras_WZ', symObjAddr: 0x1350, symBinAddr: 0x35194, symSize: 0x30 }
  - { offsetInCU: 0x558, offset: 0x23FAF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO12loggingToken_WZ', symObjAddr: 0x1380, symBinAddr: 0x351C4, symSize: 0x3C }
  - { offsetInCU: 0x572, offset: 0x23FC9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO19declinedPermissions_WZ', symObjAddr: 0x13BC, symBinAddr: 0x35200, symSize: 0x34 }
  - { offsetInCU: 0x6DC, offset: 0x24133, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC21heartbeatTimerDidFireyyFTo', symObjAddr: 0x3B28, symBinAddr: 0x3796C, symSize: 0xDC }
  - { offsetInCU: 0x744, offset: 0x2419B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC14getClientState20authenticationMethod08existingH06loggerSSSgAH_SDySSypGSgACSgtFZTf4nnnd_n', symObjAddr: 0x53B0, symBinAddr: 0x391C0, symSize: 0x3B4 }
  - { offsetInCU: 0xC65, offset: 0x246BC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC19defaultDependencies_WZ', symObjAddr: 0x4054, symBinAddr: 0x37E98, symSize: 0x6C }
  - { offsetInCU: 0xC80, offset: 0x246D7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC19defaultDependenciesAC04TypeG0VSgvau', symObjAddr: 0x40C0, symBinAddr: 0x37F04, symSize: 0x40 }
  - { offsetInCU: 0xCB8, offset: 0x2470F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC22configuredDependencies_WZ', symObjAddr: 0x41A4, symBinAddr: 0x37FE8, symSize: 0x18 }
  - { offsetInCU: 0xCD3, offset: 0x2472A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC22configuredDependenciesAC04TypeG0VSgvau', symObjAddr: 0x41BC, symBinAddr: 0x38000, symSize: 0x40 }
  - { offsetInCU: 0xD23, offset: 0x2477A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerCAA15DependentAsTypeA2aDP22configuredDependencies0hJ0QzSgvsZTW', symObjAddr: 0x43A0, symBinAddr: 0x381E4, symSize: 0x8C }
  - { offsetInCU: 0xD59, offset: 0x247B0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerCAA15DependentAsTypeA2aDP22configuredDependencies0hJ0QzSgvMZTW', symObjAddr: 0x442C, symBinAddr: 0x38270, symSize: 0x6C }
  - { offsetInCU: 0xDAB, offset: 0x24802, size: 0x8, addend: 0x0, symName: '_$sxq_xq_Iegnnrr_x3key_q_5valuetx_q_tIegnr_SHRzr0_lTRSS_ypTg575$sSD5merge_16uniquingKeysWithySDyxq_Gn_q_q__q_tKXEtKFx_q_tx_q_tcfU_SS_ypTG5Tf3nnpf_n', symObjAddr: 0x4520, symBinAddr: 0x38364, symSize: 0x40 }
  - { offsetInCU: 0xF84, offset: 0x249DB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginEventLogging_pWOb', symObjAddr: 0x5374, symBinAddr: 0x391A8, symSize: 0x18 }
  - { offsetInCU: 0xF97, offset: 0x249EE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginEventLogging_pWOc', symObjAddr: 0x5A14, symBinAddr: 0x39704, symSize: 0x44 }
  - { offsetInCU: 0xFAA, offset: 0x24A01, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC16TypeDependenciesVSgWOf', symObjAddr: 0x5A58, symBinAddr: 0x39748, symSize: 0x48 }
  - { offsetInCU: 0xFBD, offset: 0x24A14, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerCMa', symObjAddr: 0x5AA0, symBinAddr: 0x39790, symSize: 0x20 }
  - { offsetInCU: 0xFD0, offset: 0x24A27, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC16TypeDependenciesVwCP', symObjAddr: 0x5AD4, symBinAddr: 0x397C4, symSize: 0x30 }
  - { offsetInCU: 0xFE3, offset: 0x24A3A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC16TypeDependenciesVwxx', symObjAddr: 0x5B04, symBinAddr: 0x397F4, symSize: 0x14 }
  - { offsetInCU: 0xFF6, offset: 0x24A4D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC16TypeDependenciesVwcp', symObjAddr: 0x5B18, symBinAddr: 0x39808, symSize: 0x34 }
  - { offsetInCU: 0x1009, offset: 0x24A60, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC16TypeDependenciesVwca', symObjAddr: 0x5B4C, symBinAddr: 0x3983C, symSize: 0x24 }
  - { offsetInCU: 0x101C, offset: 0x24A73, size: 0x8, addend: 0x0, symName: ___swift_memcpy40_8, symObjAddr: 0x5CD8, symBinAddr: 0x39860, symSize: 0x14 }
  - { offsetInCU: 0x102F, offset: 0x24A86, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC16TypeDependenciesVwta', symObjAddr: 0x5CEC, symBinAddr: 0x39874, symSize: 0x38 }
  - { offsetInCU: 0x1042, offset: 0x24A99, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC16TypeDependenciesVwet', symObjAddr: 0x5D24, symBinAddr: 0x398AC, symSize: 0x48 }
  - { offsetInCU: 0x1055, offset: 0x24AAC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC16TypeDependenciesVwst', symObjAddr: 0x5D6C, symBinAddr: 0x398F4, symSize: 0x48 }
  - { offsetInCU: 0x1068, offset: 0x24ABF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC16TypeDependenciesVMa', symObjAddr: 0x5DB4, symBinAddr: 0x3993C, symSize: 0x10 }
  - { offsetInCU: 0x1289, offset: 0x24CE0, size: 0x8, addend: 0x0, symName: '_$sSTsE6reduceyqd__qd___qd__qd___7ElementQztKXEtKlFShy13FBSDKLoginKit12FBPermissionCG_SSTg504$s13c61Kit18LoginManagerLoggerC12startSession3foryAA0cD0C_tFS2S_AA12E7CtXEfU_Tf1ncn_n', symObjAddr: 0x1BA8, symBinAddr: 0x359EC, symSize: 0x308 }
  - { offsetInCU: 0x1488, offset: 0x24EDF, size: 0x8, addend: 0x0, symName: '_$ss17_dictionaryUpCastySDyq0_q1_GSDyxq_GSHRzSHR0_r2_lFSS_SbSSypTg5', symObjAddr: 0x3C04, symBinAddr: 0x37A48, symSize: 0x36C }
  - { offsetInCU: 0x17C3, offset: 0x2521A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC12startSession3foryAA0cD0C_tF', symObjAddr: 0x0, symBinAddr: 0x33E84, symSize: 0x398 }
  - { offsetInCU: 0x19F1, offset: 0x25448, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC03endC06result5erroryAA0cdC6ResultCSg_So7NSErrorCSgtF', symObjAddr: 0x398, symBinAddr: 0x3421C, symSize: 0x48C }
  - { offsetInCU: 0x1C56, offset: 0x256AD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC10endSessionyyF', symObjAddr: 0x824, symBinAddr: 0x346A8, symSize: 0x188 }
  - { offsetInCU: 0x1C99, offset: 0x256F0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC04postC9HeartbeatyyF', symObjAddr: 0x9AC, symBinAddr: 0x34830, symSize: 0x48 }
  - { offsetInCU: 0x1CC2, offset: 0x25719, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC14getClientState20authenticationMethod08existingH06loggerSSSgAH_SDySSypGSgACSgtFZ', symObjAddr: 0xA34, symBinAddr: 0x34878, symSize: 0x4 }
  - { offsetInCU: 0x1CFF, offset: 0x25756, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC31willAttemptAppSwitchingBehavior9urlSchemeySS_tF', symObjAddr: 0xA38, symBinAddr: 0x3487C, symSize: 0x260 }
  - { offsetInCU: 0x1E41, offset: 0x25898, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC5start20authenticationMethodySS_tF', symObjAddr: 0xC98, symBinAddr: 0x34ADC, symSize: 0xA8 }
  - { offsetInCU: 0x1E7F, offset: 0x258D6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC10identifierSSSgvg', symObjAddr: 0xD40, symBinAddr: 0x34B84, symSize: 0x48 }
  - { offsetInCU: 0x1E92, offset: 0x258E9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC10identifierSSSgvs', symObjAddr: 0xD88, symBinAddr: 0x34BCC, symSize: 0x50 }
  - { offsetInCU: 0x1EA5, offset: 0x258FC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC10identifierSSSgvM', symObjAddr: 0xDD8, symBinAddr: 0x34C1C, symSize: 0x3C }
  - { offsetInCU: 0x1EB8, offset: 0x2590F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC6extrasSDySSypGvg', symObjAddr: 0xE14, symBinAddr: 0x34C58, symSize: 0x34 }
  - { offsetInCU: 0x1ECB, offset: 0x25922, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC6extrasSDySSypGvs', symObjAddr: 0xE48, symBinAddr: 0x34C8C, symSize: 0x44 }
  - { offsetInCU: 0x1EDE, offset: 0x25935, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC6extrasSDySSypGvM', symObjAddr: 0xE8C, symBinAddr: 0x34CD0, symSize: 0x3C }
  - { offsetInCU: 0x1EF1, offset: 0x25948, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC10lastResultSSvg', symObjAddr: 0xEC8, symBinAddr: 0x34D0C, symSize: 0x48 }
  - { offsetInCU: 0x1F04, offset: 0x2595B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC10lastResultSSvs', symObjAddr: 0xF10, symBinAddr: 0x34D54, symSize: 0x50 }
  - { offsetInCU: 0x1F17, offset: 0x2596E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC10lastResultSSvM', symObjAddr: 0xF60, symBinAddr: 0x34DA4, symSize: 0x3C }
  - { offsetInCU: 0x1F2A, offset: 0x25981, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC9lastErrorSo7NSErrorCSgvg', symObjAddr: 0xF9C, symBinAddr: 0x34DE0, symSize: 0x44 }
  - { offsetInCU: 0x1F3D, offset: 0x25994, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC9lastErrorSo7NSErrorCSgvs', symObjAddr: 0xFE0, symBinAddr: 0x34E24, symSize: 0x44 }
  - { offsetInCU: 0x1F50, offset: 0x259A7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC9lastErrorSo7NSErrorCSgvM', symObjAddr: 0x1024, symBinAddr: 0x34E68, symSize: 0x3C }
  - { offsetInCU: 0x1F63, offset: 0x259BA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC10authMethodSSSgvg', symObjAddr: 0x1060, symBinAddr: 0x34EA4, symSize: 0x48 }
  - { offsetInCU: 0x1F76, offset: 0x259CD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC10authMethodSSSgvs', symObjAddr: 0x10A8, symBinAddr: 0x34EEC, symSize: 0x50 }
  - { offsetInCU: 0x1F89, offset: 0x259E0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC10authMethodSSSgvM', symObjAddr: 0x10F8, symBinAddr: 0x34F3C, symSize: 0x3C }
  - { offsetInCU: 0x1F9C, offset: 0x259F3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC10authMethodSSSgvM.resume.0', symObjAddr: 0x1134, symBinAddr: 0x34F78, symSize: 0x4 }
  - { offsetInCU: 0x1FAF, offset: 0x25A06, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC12loggingTokenSSSgvg', symObjAddr: 0x1138, symBinAddr: 0x34F7C, symSize: 0x48 }
  - { offsetInCU: 0x1FC2, offset: 0x25A19, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC12loggingTokenSSSgvs', symObjAddr: 0x1180, symBinAddr: 0x34FC4, symSize: 0x50 }
  - { offsetInCU: 0x1FD5, offset: 0x25A2C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC12loggingTokenSSSgvM', symObjAddr: 0x11D0, symBinAddr: 0x35014, symSize: 0x3C }
  - { offsetInCU: 0x2000, offset: 0x25A57, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC10parameters8trackingACSgSDySSypGSg_AA0C8TrackingOtcfC', symObjAddr: 0x13F0, symBinAddr: 0x35234, symSize: 0x728 }
  - { offsetInCU: 0x21B4, offset: 0x25C0B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC12loggingToken8trackingACSgSSSg_AA0C8TrackingOtcfC', symObjAddr: 0x1B18, symBinAddr: 0x3595C, symSize: 0x60 }
  - { offsetInCU: 0x21DC, offset: 0x25C33, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC12loggingToken8trackingACSgSSSg_AA0C8TrackingOtcfc', symObjAddr: 0x1B78, symBinAddr: 0x359BC, symSize: 0x30 }
  - { offsetInCU: 0x2265, offset: 0x25CBC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC21parametersForNewEventSDySo08FBSDKAppI13ParameterNameaypGyF', symObjAddr: 0x1F2C, symBinAddr: 0x35D70, symSize: 0x7F0 }
  - { offsetInCU: 0x2647, offset: 0x2609E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC8logEvent_6paramsySo08FBSDKAppG4Namea_SDySo0ig9ParameterJ0aypGSgtF', symObjAddr: 0x271C, symBinAddr: 0x36560, symSize: 0x354 }
  - { offsetInCU: 0x275E, offset: 0x261B5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC8logEvent_6result5errorySo08FBSDKAppG4Namea_SSSo7NSErrorCSgtF', symObjAddr: 0x2A70, symBinAddr: 0x368B4, symSize: 0xFEC }
  - { offsetInCU: 0x2C70, offset: 0x266C7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC21heartbeatTimerDidFireyyF', symObjAddr: 0x3A5C, symBinAddr: 0x378A0, symSize: 0xCC }
  - { offsetInCU: 0x2CE6, offset: 0x2673D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerCfd', symObjAddr: 0x3F70, symBinAddr: 0x37DB4, symSize: 0x44 }
  - { offsetInCU: 0x2D13, offset: 0x2676A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerCfD', symObjAddr: 0x3FB4, symBinAddr: 0x37DF8, symSize: 0x4C }
  - { offsetInCU: 0x2D48, offset: 0x2679F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC16TypeDependenciesV05eventE0AA0C12EventLogging_pvg', symObjAddr: 0x4000, symBinAddr: 0x37E44, symSize: 0xC }
  - { offsetInCU: 0x2D5B, offset: 0x267B2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC16TypeDependenciesV05eventE0AA0C12EventLogging_pvs', symObjAddr: 0x400C, symBinAddr: 0x37E50, symSize: 0x2C }
  - { offsetInCU: 0x2D6E, offset: 0x267C5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC16TypeDependenciesV05eventE0AA0C12EventLogging_pvM', symObjAddr: 0x4038, symBinAddr: 0x37E7C, symSize: 0x10 }
  - { offsetInCU: 0x2D81, offset: 0x267D8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC16TypeDependenciesV05eventE0AA0C12EventLogging_pvM.resume.0', symObjAddr: 0x4048, symBinAddr: 0x37E8C, symSize: 0x4 }
  - { offsetInCU: 0x2D9A, offset: 0x267F1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC16TypeDependenciesV05eventE0AeA0C12EventLogging_p_tcfC', symObjAddr: 0x404C, symBinAddr: 0x37E90, symSize: 0x8 }
  - { offsetInCU: 0x2DAD, offset: 0x26804, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC19defaultDependenciesAC04TypeG0VSgvMZ', symObjAddr: 0x4138, symBinAddr: 0x37F7C, symSize: 0x6C }
  - { offsetInCU: 0x2DCC, offset: 0x26823, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC22configuredDependenciesAC04TypeG0VSgvMZ', symObjAddr: 0x4318, symBinAddr: 0x3815C, symSize: 0x6C }
  - { offsetInCU: 0x2E09, offset: 0x26860, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV4copyyyFSo26FBSDKAppEventParameterNamea_ypTg5', symObjAddr: 0x4904, symBinAddr: 0x38748, symSize: 0x1D8 }
  - { offsetInCU: 0x2E9A, offset: 0x268F1, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV4copyyyFSS_ypTg5', symObjAddr: 0x4ADC, symBinAddr: 0x38920, symSize: 0x1F4 }
  - { offsetInCU: 0x2F37, offset: 0x2698E, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV4copyyyFSS_SSTg5', symObjAddr: 0x4CD0, symBinAddr: 0x38B14, symSize: 0x1C8 }
  - { offsetInCU: 0x2FE6, offset: 0x26A3D, size: 0x8, addend: 0x0, symName: '_$sSD11removeValue6forKeyq_Sgx_tFSS_ypTg5', symObjAddr: 0x4E98, symBinAddr: 0x38CDC, symSize: 0xE8 }
  - { offsetInCU: 0x308E, offset: 0x26AE5, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV7_delete2atys10_HashTableV6BucketV_tFSo26FBSDKAppEventParameterNamea_ypTg5', symObjAddr: 0x4F80, symBinAddr: 0x38DC4, symSize: 0x208 }
  - { offsetInCU: 0x312F, offset: 0x26B86, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV7_delete2atys10_HashTableV6BucketV_tFSS_ypTg5', symObjAddr: 0x5188, symBinAddr: 0x38FCC, symSize: 0x1DC }
  - { offsetInCU: 0x31C4, offset: 0x26C1B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC12loggingToken8trackingACSgSSSg_AA0C8TrackingOtcfcTf4gnn_n', symObjAddr: 0x583C, symBinAddr: 0x39574, symSize: 0x170 }
  - { offsetInCU: 0xD8, offset: 0x26D84, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC5tokenSo16FBSDKAccessTokenCSgvgTo', symObjAddr: 0x138, symBinAddr: 0x39B5C, symSize: 0x10 }
  - { offsetInCU: 0xF7, offset: 0x26DA3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC5tokenSo16FBSDKAccessTokenCSgvgTo', symObjAddr: 0x138, symBinAddr: 0x39B5C, symSize: 0x10 }
  - { offsetInCU: 0x127, offset: 0x26DD3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC19authenticationTokenSo019FBSDKAuthenticationG0CSgvgTo', symObjAddr: 0x178, symBinAddr: 0x39B9C, symSize: 0x10 }
  - { offsetInCU: 0x146, offset: 0x26DF2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC19authenticationTokenSo019FBSDKAuthenticationG0CSgvgTo', symObjAddr: 0x178, symBinAddr: 0x39B9C, symSize: 0x10 }
  - { offsetInCU: 0x176, offset: 0x26E22, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC11isCancelledSbvgTo', symObjAddr: 0x1B8, symBinAddr: 0x39BDC, symSize: 0x10 }
  - { offsetInCU: 0x195, offset: 0x26E41, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC11isCancelledSbvgTo', symObjAddr: 0x1B8, symBinAddr: 0x39BDC, symSize: 0x10 }
  - { offsetInCU: 0x1ED, offset: 0x26E99, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC13loggingExtrasSDySSypGvgTo', symObjAddr: 0x25C, symBinAddr: 0x39C80, symSize: 0x7C }
  - { offsetInCU: 0x241, offset: 0x26EED, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC13loggingExtrasSDySSypGvsTo', symObjAddr: 0x320, symBinAddr: 0x39D44, symSize: 0x7C }
  - { offsetInCU: 0x282, offset: 0x26F2E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC5token19authenticationToken11isCancelled18grantedPermissions08declinedL0ACSo011FBSDKAccessH0CSg_So019FBSDKAuthenticationH0CSgSbShySSGAOtcfcTo', symObjAddr: 0x488, symBinAddr: 0x39E70, symSize: 0x114 }
  - { offsetInCU: 0x2F6, offset: 0x26FA2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC15addLoggingExtra_6forKeyyyp_SStFTo', symObjAddr: 0x59C, symBinAddr: 0x39F84, symSize: 0xF0 }
  - { offsetInCU: 0x368, offset: 0x27014, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultCACycfcTo', symObjAddr: 0x6D8, symBinAddr: 0x3A0C0, symSize: 0x2C }
  - { offsetInCU: 0x3F5, offset: 0x270A1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultCfETo', symObjAddr: 0x738, symBinAddr: 0x3A120, symSize: 0x68 }
  - { offsetInCU: 0x4E1, offset: 0x2718D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultCMa', symObjAddr: 0x910, symBinAddr: 0x3A2F8, symSize: 0x20 }
  - { offsetInCU: 0x4F4, offset: 0x271A0, size: 0x8, addend: 0x0, symName: ___swift_destroy_boxed_opaque_existential_0, symObjAddr: 0x944, symBinAddr: 0x3A32C, symSize: 0x20 }
  - { offsetInCU: 0x6F1, offset: 0x2739D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC5token19authenticationToken11isCancelled18grantedPermissions08declinedL0ACSo011FBSDKAccessH0CSg_So019FBSDKAuthenticationH0CSgSbShySSGAOtcfC', symObjAddr: 0x0, symBinAddr: 0x39A24, symSize: 0xB0 }
  - { offsetInCU: 0x756, offset: 0x27402, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC15addLoggingExtra_6forKeyyyp_SStF', symObjAddr: 0xB0, symBinAddr: 0x39AD4, symSize: 0x88 }
  - { offsetInCU: 0x7A3, offset: 0x2744F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC5tokenSo16FBSDKAccessTokenCSgvg', symObjAddr: 0x148, symBinAddr: 0x39B6C, symSize: 0x30 }
  - { offsetInCU: 0x7D2, offset: 0x2747E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC19authenticationTokenSo019FBSDKAuthenticationG0CSgvg', symObjAddr: 0x188, symBinAddr: 0x39BAC, symSize: 0x30 }
  - { offsetInCU: 0x801, offset: 0x274AD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC11isCancelledSbvg', symObjAddr: 0x1C8, symBinAddr: 0x39BEC, symSize: 0x10 }
  - { offsetInCU: 0x81C, offset: 0x274C8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC18grantedPermissionsShySSGvg', symObjAddr: 0x1E4, symBinAddr: 0x39C08, symSize: 0x10 }
  - { offsetInCU: 0x83D, offset: 0x274E9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC19declinedPermissionsShySSGvg', symObjAddr: 0x24C, symBinAddr: 0x39C70, symSize: 0x10 }
  - { offsetInCU: 0x870, offset: 0x2751C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC13loggingExtrasSDySSypGvg', symObjAddr: 0x2D8, symBinAddr: 0x39CFC, symSize: 0x48 }
  - { offsetInCU: 0x8AF, offset: 0x2755B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC5token19authenticationToken11isCancelled18grantedPermissions08declinedL0ACSo011FBSDKAccessH0CSg_So019FBSDKAuthenticationH0CSgSbShySSGAOtcfc', symObjAddr: 0x39C, symBinAddr: 0x39DC0, symSize: 0xB0 }
  - { offsetInCU: 0x946, offset: 0x275F2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultCACycfC', symObjAddr: 0x68C, symBinAddr: 0x3A074, symSize: 0x20 }
  - { offsetInCU: 0x959, offset: 0x27605, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultCACycfc', symObjAddr: 0x6AC, symBinAddr: 0x3A094, symSize: 0x2C }
  - { offsetInCU: 0x9AD, offset: 0x27659, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultCfD', symObjAddr: 0x704, symBinAddr: 0x3A0EC, symSize: 0x34 }
  - { offsetInCU: 0x9E0, offset: 0x2768C, size: 0x8, addend: 0x0, symName: '_$sSD8_VariantV8setValue_6forKeyyq_n_xtFSS_SSTg5', symObjAddr: 0x7A0, symBinAddr: 0x3A188, symSize: 0xB8 }
  - { offsetInCU: 0xA4B, offset: 0x276F7, size: 0x8, addend: 0x0, symName: '_$sSD8_VariantV8setValue_6forKeyyq_n_xtFSS_ypTg5', symObjAddr: 0x858, symBinAddr: 0x3A240, symSize: 0xB8 }
  - { offsetInCU: 0x27, offset: 0x2777F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateO8rawValueACSgSi_tcfC', symObjAddr: 0x0, symBinAddr: 0x3A34C, symSize: 0x10 }
  - { offsetInCU: 0x73, offset: 0x277CB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateOSYAASY8rawValuexSg03RawG0Qz_tcfCTW', symObjAddr: 0x120, symBinAddr: 0x3A46C, symSize: 0x18 }
  - { offsetInCU: 0xA2, offset: 0x277FA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateOSYAASY8rawValue03RawG0QzvgTW', symObjAddr: 0x138, symBinAddr: 0x3A484, symSize: 0xC }
  - { offsetInCU: 0xC9, offset: 0x27821, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateOSHAASQWb', symObjAddr: 0x2C, symBinAddr: 0x3A378, symSize: 0x4 }
  - { offsetInCU: 0xDC, offset: 0x27834, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateOACSQAAWl', symObjAddr: 0x30, symBinAddr: 0x3A37C, symSize: 0x44 }
  - { offsetInCU: 0x10D, offset: 0x27865, size: 0x8, addend: 0x0, symName: ___swift_memcpy1_1, symObjAddr: 0x144, symBinAddr: 0x3A490, symSize: 0xC }
  - { offsetInCU: 0x120, offset: 0x27878, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateOwet', symObjAddr: 0x154, symBinAddr: 0x3A49C, symSize: 0x90 }
  - { offsetInCU: 0x133, offset: 0x2788B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateOwst', symObjAddr: 0x1E4, symBinAddr: 0x3A52C, symSize: 0xBC }
  - { offsetInCU: 0x146, offset: 0x2789E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateOwug', symObjAddr: 0x2A0, symBinAddr: 0x3A5E8, symSize: 0x8 }
  - { offsetInCU: 0x159, offset: 0x278B1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateOwup', symObjAddr: 0x2A8, symBinAddr: 0x3A5F0, symSize: 0x4 }
  - { offsetInCU: 0x16C, offset: 0x278C4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateOwui', symObjAddr: 0x2AC, symBinAddr: 0x3A5F4, symSize: 0x8 }
  - { offsetInCU: 0x17F, offset: 0x278D7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateOMa', symObjAddr: 0x2B4, symBinAddr: 0x3A5FC, symSize: 0x10 }
  - { offsetInCU: 0x1B5, offset: 0x2790D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x18, symBinAddr: 0x3A364, symSize: 0x14 }
  - { offsetInCU: 0x257, offset: 0x279AF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateOSHAASH9hashValueSivgTW', symObjAddr: 0x74, symBinAddr: 0x3A3C0, symSize: 0x44 }
  - { offsetInCU: 0x2FE, offset: 0x27A56, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateOSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0xB8, symBinAddr: 0x3A404, symSize: 0x28 }
  - { offsetInCU: 0x34D, offset: 0x27AA5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateOSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0xE0, symBinAddr: 0x3A42C, symSize: 0x40 }
  - { offsetInCU: 0x450, offset: 0x27BA8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateO8rawValueACSgSi_tcfC', symObjAddr: 0x0, symBinAddr: 0x3A34C, symSize: 0x10 }
  - { offsetInCU: 0x46D, offset: 0x27BC5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateO8rawValueSivg', symObjAddr: 0x10, symBinAddr: 0x3A35C, symSize: 0x8 }
  - { offsetInCU: 0x49, offset: 0x27C68, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC19defaultDependenciesAC04TypeG0VSgvpZ', symObjAddr: 0x7578, symBinAddr: 0x60738, symSize: 0x0 }
  - { offsetInCU: 0x130, offset: 0x27D4F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC22configuredDependenciesAC04TypeG0VSgvpZ', symObjAddr: 0x75A8, symBinAddr: 0x60768, symSize: 0x0 }
  - { offsetInCU: 0x22E, offset: 0x27E4D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC07attemptD04from10completionys5Error_p_ySbctFTo', symObjAddr: 0x24C, symBinAddr: 0x3A888, symSize: 0x88 }
  - { offsetInCU: 0x390, offset: 0x27FAF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC19defaultDependencies_WZ', symObjAddr: 0x3A0, symBinAddr: 0x3A9DC, symSize: 0x58 }
  - { offsetInCU: 0x3D3, offset: 0x27FF2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC19defaultDependenciesAC04TypeG0VSgvau', symObjAddr: 0x3F8, symBinAddr: 0x3AA34, symSize: 0x40 }
  - { offsetInCU: 0x41D, offset: 0x2803C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC22configuredDependencies_WZ', symObjAddr: 0x4E0, symBinAddr: 0x3AB1C, symSize: 0x18 }
  - { offsetInCU: 0x436, offset: 0x28055, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC22configuredDependenciesAC04TypeG0VSgvau', symObjAddr: 0x4F8, symBinAddr: 0x3AB34, symSize: 0x40 }
  - { offsetInCU: 0x485, offset: 0x280A4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterCAA15DependentAsTypeA2aDP22configuredDependencies0hJ0QzSgvsZTW', symObjAddr: 0x6CC, symBinAddr: 0x3AD08, symSize: 0x84 }
  - { offsetInCU: 0x4B9, offset: 0x280D8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterCAA15DependentAsTypeA2aDP22configuredDependencies0hJ0QzSgvMZTW', symObjAddr: 0x750, symBinAddr: 0x3AD8C, symSize: 0x6C }
  - { offsetInCU: 0x52C, offset: 0x2814B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14LoginProviding_pWOc', symObjAddr: 0xB7C, symBinAddr: 0x3B1B8, symSize: 0x44 }
  - { offsetInCU: 0x53F, offset: 0x2815E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesVSgWOc', symObjAddr: 0xBF8, symBinAddr: 0x3B1FC, symSize: 0x48 }
  - { offsetInCU: 0x552, offset: 0x28171, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesVSgWOf', symObjAddr: 0xC80, symBinAddr: 0x3B244, symSize: 0x48 }
  - { offsetInCU: 0x565, offset: 0x28184, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesVSgWOh', symObjAddr: 0xCC8, symBinAddr: 0x3B28C, symSize: 0x40 }
  - { offsetInCU: 0x578, offset: 0x28197, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterCMa', symObjAddr: 0xD08, symBinAddr: 0x3B2CC, symSize: 0x20 }
  - { offsetInCU: 0x58B, offset: 0x281AA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesVwCP', symObjAddr: 0xD3C, symBinAddr: 0x3B300, symSize: 0x30 }
  - { offsetInCU: 0x59E, offset: 0x281BD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesVwxx', symObjAddr: 0xD6C, symBinAddr: 0x3B330, symSize: 0x4 }
  - { offsetInCU: 0x5B2, offset: 0x281D1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesVwcp', symObjAddr: 0xD70, symBinAddr: 0x3B334, symSize: 0x40 }
  - { offsetInCU: 0x5C5, offset: 0x281E4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesVwca', symObjAddr: 0xDB0, symBinAddr: 0x3B374, symSize: 0x30 }
  - { offsetInCU: 0x5D8, offset: 0x281F7, size: 0x8, addend: 0x0, symName: ___swift_memcpy48_8, symObjAddr: 0xF48, symBinAddr: 0x3B3A4, symSize: 0x14 }
  - { offsetInCU: 0x5EB, offset: 0x2820A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesVwta', symObjAddr: 0xF5C, symBinAddr: 0x3B3B8, symSize: 0x38 }
  - { offsetInCU: 0x5FE, offset: 0x2821D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesVwet', symObjAddr: 0xF94, symBinAddr: 0x3B3F0, symSize: 0x48 }
  - { offsetInCU: 0x611, offset: 0x28230, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesVwst', symObjAddr: 0xFDC, symBinAddr: 0x3B438, symSize: 0x4C }
  - { offsetInCU: 0x624, offset: 0x28243, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesVMa', symObjAddr: 0x1028, symBinAddr: 0x3B484, symSize: 0x10 }
  - { offsetInCU: 0x641, offset: 0x28260, size: 0x8, addend: 0x0, symName: '_$s10ObjectiveC8ObjCBoolVIeyBy_SbIegy_TRTA', symObjAddr: 0x105C, symBinAddr: 0x3B4B8, symSize: 0x14 }
  - { offsetInCU: 0x669, offset: 0x28288, size: 0x8, addend: 0x0, symName: '_$sSo16FBSDKAccessTokenCMa', symObjAddr: 0x1070, symBinAddr: 0x3B4CC, symSize: 0x3C }
  - { offsetInCU: 0x68B, offset: 0x282AA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC07attemptD04from10completionys5Error_p_ySbctFyAA0c7ManagerC6ResultCSg_sAG_pSgtcfU_TA', symObjAddr: 0x10F4, symBinAddr: 0x3B52C, symSize: 0x68 }
  - { offsetInCU: 0x70A, offset: 0x28329, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesVWOh', symObjAddr: 0x115C, symBinAddr: 0x3B594, symSize: 0x24 }
  - { offsetInCU: 0x782, offset: 0x283A1, size: 0x8, addend: 0x0, symName: '_$sSlsE3mapySayqd__Gqd__7ElementQzKXEKlFShy12FBSDKCoreKit10PermissionOG_SSTg5091$s13FBSDKLoginKit22LoginRecoveryAttempterC07attemptD04from10completionys5Error_p_ySbctFSS09c4B010E52Ocfu_32f5ef5a0b9ade21eb65ffea7b618f60adAJSSTf3nnpk_nTf1cn_n', symObjAddr: 0x0, symBinAddr: 0x3A63C, symSize: 0x240 }
  - { offsetInCU: 0xAD9, offset: 0x286F8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC07attemptD04from10completionys5Error_p_ySbctF', symObjAddr: 0x240, symBinAddr: 0x3A87C, symSize: 0xC }
  - { offsetInCU: 0xB87, offset: 0x287A6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterCfd', symObjAddr: 0x2D4, symBinAddr: 0x3A910, symSize: 0x8 }
  - { offsetInCU: 0xBAA, offset: 0x287C9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterCfD', symObjAddr: 0x2DC, symBinAddr: 0x3A918, symSize: 0x10 }
  - { offsetInCU: 0xBD3, offset: 0x287F2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterCACycfC', symObjAddr: 0x2EC, symBinAddr: 0x3A928, symSize: 0x10 }
  - { offsetInCU: 0xBE6, offset: 0x28805, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterCACycfc', symObjAddr: 0x2FC, symBinAddr: 0x3A938, symSize: 0x8 }
  - { offsetInCU: 0xC09, offset: 0x28828, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesV13loginProviderAA0C9Providing_pvg', symObjAddr: 0x304, symBinAddr: 0x3A940, symSize: 0xC }
  - { offsetInCU: 0xC1C, offset: 0x2883B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesV13loginProviderAA0C9Providing_pvs', symObjAddr: 0x310, symBinAddr: 0x3A94C, symSize: 0x2C }
  - { offsetInCU: 0xC2F, offset: 0x2884E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesV13loginProviderAA0C9Providing_pvM', symObjAddr: 0x33C, symBinAddr: 0x3A978, symSize: 0x10 }
  - { offsetInCU: 0xC42, offset: 0x28861, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesV13loginProviderAA0C9Providing_pvM.resume.0', symObjAddr: 0x34C, symBinAddr: 0x3A988, symSize: 0x4 }
  - { offsetInCU: 0xC5B, offset: 0x2887A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesV19accessTokenProviderSo011FBSDKAccessI9Providing_pXpvg', symObjAddr: 0x350, symBinAddr: 0x3A98C, symSize: 0x8 }
  - { offsetInCU: 0xC6E, offset: 0x2888D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesV19accessTokenProviderSo011FBSDKAccessI9Providing_pXpvs', symObjAddr: 0x358, symBinAddr: 0x3A994, symSize: 0x8 }
  - { offsetInCU: 0xC81, offset: 0x288A0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesV19accessTokenProviderSo011FBSDKAccessI9Providing_pXpvM', symObjAddr: 0x360, symBinAddr: 0x3A99C, symSize: 0x10 }
  - { offsetInCU: 0xC94, offset: 0x288B3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesV19accessTokenProviderSo011FBSDKAccessI9Providing_pXpvM.resume.0', symObjAddr: 0x370, symBinAddr: 0x3A9AC, symSize: 0x4 }
  - { offsetInCU: 0xCAD, offset: 0x288CC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesV13loginProvider011accessTokenI0AeA0C9Providing_p_So011FBSDKAccesskL0_pXptcfC', symObjAddr: 0x374, symBinAddr: 0x3A9B0, symSize: 0x2C }
  - { offsetInCU: 0xCD2, offset: 0x288F1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC19defaultDependenciesAC04TypeG0VSgvMZ', symObjAddr: 0x470, symBinAddr: 0x3AAAC, symSize: 0x6C }
  - { offsetInCU: 0xCF1, offset: 0x28910, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC19defaultDependenciesAC04TypeG0VSgvMZ.resume.0', symObjAddr: 0x4DC, symBinAddr: 0x3AB18, symSize: 0x4 }
  - { offsetInCU: 0xD05, offset: 0x28924, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC22configuredDependenciesAC04TypeG0VSgvMZ', symObjAddr: 0x644, symBinAddr: 0x3AC80, symSize: 0x6C }
  - { offsetInCU: 0xD48, offset: 0x28967, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC07attemptD04from10completionys5Error_p_ySbctFTf4dnn_n', symObjAddr: 0x9E8, symBinAddr: 0x3B024, symSize: 0x194 }
  - { offsetInCU: 0x2B, offset: 0x28A81, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultO012loginManagerD0AA0cfcD0CSgvg', symObjAddr: 0x240, symBinAddr: 0x3B800, symSize: 0x144 }
  - { offsetInCU: 0x10A, offset: 0x28B60, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultO6result5errorAcA0c7ManagercD0CSg_s5Error_pSgtcfCTf4nnd_n', symObjAddr: 0x600, symBinAddr: 0x3BBC0, symSize: 0x4B8 }
  - { offsetInCU: 0x5D4, offset: 0x2902A, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_ypTgmq5Tf4g_n', symObjAddr: 0x504, symBinAddr: 0x3BAC4, symSize: 0xFC }
  - { offsetInCU: 0x7C1, offset: 0x29217, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultOwCP', symObjAddr: 0xAB8, symBinAddr: 0x3C078, symSize: 0x30 }
  - { offsetInCU: 0x7D4, offset: 0x2922A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultOWOy', symObjAddr: 0xAE8, symBinAddr: 0x3C0A8, symSize: 0x60 }
  - { offsetInCU: 0x7E7, offset: 0x2923D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultOwxx', symObjAddr: 0xB48, symBinAddr: 0x3C108, symSize: 0x14 }
  - { offsetInCU: 0x7FA, offset: 0x29250, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultOwcp', symObjAddr: 0xBA4, symBinAddr: 0x3C11C, symSize: 0x5C }
  - { offsetInCU: 0x80D, offset: 0x29263, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultOwca', symObjAddr: 0xC00, symBinAddr: 0x3C178, symSize: 0x6C }
  - { offsetInCU: 0x820, offset: 0x29276, size: 0x8, addend: 0x0, symName: ___swift_memcpy25_8, symObjAddr: 0xC6C, symBinAddr: 0x3C1E4, symSize: 0x14 }
  - { offsetInCU: 0x833, offset: 0x29289, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultOwta', symObjAddr: 0xC80, symBinAddr: 0x3C1F8, symSize: 0x4C }
  - { offsetInCU: 0x846, offset: 0x2929C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultOwet', symObjAddr: 0xCCC, symBinAddr: 0x3C244, symSize: 0x48 }
  - { offsetInCU: 0x859, offset: 0x292AF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultOwst', symObjAddr: 0xD14, symBinAddr: 0x3C28C, symSize: 0x48 }
  - { offsetInCU: 0x86C, offset: 0x292C2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultOwug', symObjAddr: 0xD5C, symBinAddr: 0x3C2D4, symSize: 0x18 }
  - { offsetInCU: 0x87F, offset: 0x292D5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultOwup', symObjAddr: 0xD74, symBinAddr: 0x3C2EC, symSize: 0x4 }
  - { offsetInCU: 0x892, offset: 0x292E8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultOwui', symObjAddr: 0xD78, symBinAddr: 0x3C2F0, symSize: 0x1C }
  - { offsetInCU: 0x8A5, offset: 0x292FB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultOMa', symObjAddr: 0xD94, symBinAddr: 0x3C30C, symSize: 0x10 }
  - { offsetInCU: 0xB6B, offset: 0x295C1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultO012loginManagerD0AA0cfcD0CSgvg', symObjAddr: 0x240, symBinAddr: 0x3B800, symSize: 0x144 }
  - { offsetInCU: 0xC95, offset: 0x296EB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultO5errors5Error_pSgvg', symObjAddr: 0x384, symBinAddr: 0x3B944, symSize: 0x38 }
  - { offsetInCU: 0xCBE, offset: 0x29714, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultO6result5errorAcA0c7ManagercD0CSg_s5Error_pSgtcfC', symObjAddr: 0x3BC, symBinAddr: 0x3B97C, symSize: 0x4 }
  - { offsetInCU: 0xCD7, offset: 0x2972D, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4findys10_HashTableV6BucketV6bucket_Sb5foundtxSHRzlFSS_Tgq5', symObjAddr: 0x3C0, symBinAddr: 0x3B980, symSize: 0x64 }
  - { offsetInCU: 0xCF7, offset: 0x2974D, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4find_9hashValues10_HashTableV6BucketV6bucket_Sb5foundtx_SitSHRzlFSS_Tgq5', symObjAddr: 0x424, symBinAddr: 0x3B9E4, symSize: 0xE0 }
  - { offsetInCU: 0x27, offset: 0x29887, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13LoginTrackingO8rawValueACSgSu_tcfC', symObjAddr: 0x0, symBinAddr: 0x3C31C, symSize: 0x20 }
  - { offsetInCU: 0x73, offset: 0x298D3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13LoginTrackingOSYAASY8rawValuexSg03RawF0Qz_tcfCTW', symObjAddr: 0x12C, symBinAddr: 0x3C448, symSize: 0x30 }
  - { offsetInCU: 0xA2, offset: 0x29902, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13LoginTrackingOSYAASY8rawValue03RawF0QzvgTW', symObjAddr: 0x15C, symBinAddr: 0x3C478, symSize: 0xC }
  - { offsetInCU: 0xC9, offset: 0x29929, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13LoginTrackingOSHAASQWb', symObjAddr: 0x38, symBinAddr: 0x3C354, symSize: 0x4 }
  - { offsetInCU: 0xDC, offset: 0x2993C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13LoginTrackingOACSQAAWl', symObjAddr: 0x3C, symBinAddr: 0x3C358, symSize: 0x44 }
  - { offsetInCU: 0x10D, offset: 0x2996D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13LoginTrackingOMa', symObjAddr: 0x168, symBinAddr: 0x3C484, symSize: 0x10 }
  - { offsetInCU: 0x143, offset: 0x299A3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13LoginTrackingOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x24, symBinAddr: 0x3C340, symSize: 0x14 }
  - { offsetInCU: 0x1E5, offset: 0x29A45, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13LoginTrackingOSHAASH9hashValueSivgTW', symObjAddr: 0x80, symBinAddr: 0x3C39C, symSize: 0x44 }
  - { offsetInCU: 0x28C, offset: 0x29AEC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13LoginTrackingOSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0xC4, symBinAddr: 0x3C3E0, symSize: 0x28 }
  - { offsetInCU: 0x2DB, offset: 0x29B3B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13LoginTrackingOSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0xEC, symBinAddr: 0x3C408, symSize: 0x40 }
  - { offsetInCU: 0x3DE, offset: 0x29C3E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13LoginTrackingO8rawValueACSgSu_tcfC', symObjAddr: 0x0, symBinAddr: 0x3C31C, symSize: 0x20 }
  - { offsetInCU: 0x3FB, offset: 0x29C5B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13LoginTrackingO8rawValueSuvg', symObjAddr: 0x20, symBinAddr: 0x3C33C, symSize: 0x4 }
  - { offsetInCU: 0x4A, offset: 0x29CFC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV22configuredDependenciesAC04TypeF0VSgvpZ', symObjAddr: 0x16AA8, symBinAddr: 0x60798, symSize: 0x0 }
  - { offsetInCU: 0x236, offset: 0x29EE8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV19defaultDependenciesAC04TypeF0VSgvpZ', symObjAddr: 0x16B10, symBinAddr: 0x60800, symSize: 0x0 }
  - { offsetInCU: 0x3C8, offset: 0x2A07A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterVAA0C10CompletingA2aDP08completeC07handleryyAA01_C20CompletionParametersCc_tFTW', symObjAddr: 0x31D4, symBinAddr: 0x3F668, symSize: 0x20 }
  - { offsetInCU: 0x409, offset: 0x2A0BB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterVAA0C10CompletingA2aDP08completeC05nonce12codeVerifier7handlerySSSg_AJyAA01_C20CompletionParametersCctFTW', symObjAddr: 0x31F4, symBinAddr: 0x3F688, symSize: 0x8 }
  - { offsetInCU: 0x424, offset: 0x2A0D6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV9challenge4fromSSSgSDySSypG_tFTf4nd_n', symObjAddr: 0x39D8, symBinAddr: 0x3FE6C, symSize: 0x348 }
  - { offsetInCU: 0x4DD, offset: 0x2A18F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV5error4froms5Error_pSgSDySSypG_tFTf4nd_n', symObjAddr: 0x3D20, symBinAddr: 0x401B4, symSize: 0x774 }
  - { offsetInCU: 0x7CF, offset: 0x2A481, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV13urlParameters5appIDACSDySSypG_SStcfCTf4nnd_n', symObjAddr: 0x4494, symBinAddr: 0x40928, symSize: 0x7B0 }
  - { offsetInCU: 0x9D4, offset: 0x2A686, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV7profile4with11permissions09FBSDKCoreB07ProfileCSgAG25AuthenticationTokenClaimsC_ShySSGSgtFTf4nnd_n', symObjAddr: 0x4EF4, symBinAddr: 0x41270, symSize: 0x824 }
  - { offsetInCU: 0xD27, offset: 0x2A9D9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV22configuredDependencies_WZ', symObjAddr: 0x33DC, symBinAddr: 0x3F870, symSize: 0x20 }
  - { offsetInCU: 0xD42, offset: 0x2A9F4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV22configuredDependenciesAC04TypeF0VSgvau', symObjAddr: 0x33FC, symBinAddr: 0x3F890, symSize: 0x40 }
  - { offsetInCU: 0xD84, offset: 0x2AA36, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV19defaultDependencies_WZ', symObjAddr: 0x34E0, symBinAddr: 0x3F974, symSize: 0x190 }
  - { offsetInCU: 0xE43, offset: 0x2AAF5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV19defaultDependenciesAC04TypeF0VSgvau', symObjAddr: 0x3670, symBinAddr: 0x3FB04, symSize: 0x40 }
  - { offsetInCU: 0xE94, offset: 0x2AB46, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterVAA15DependentAsTypeA2aDP22configuredDependencies0gI0QzSgvsZTW', symObjAddr: 0x3858, symBinAddr: 0x3FCEC, symSize: 0x8C }
  - { offsetInCU: 0xECA, offset: 0x2AB7C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterVAA15DependentAsTypeA2aDP22configuredDependencies0gI0QzSgvMZTW', symObjAddr: 0x38E4, symBinAddr: 0x3FD78, symSize: 0x6C }
  - { offsetInCU: 0xF4E, offset: 0x2AC00, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DateVSgWOd', symObjAddr: 0x4CC8, symBinAddr: 0x410D8, symSize: 0x48 }
  - { offsetInCU: 0xF61, offset: 0x2AC13, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesVWOh', symObjAddr: 0x4D4C, symBinAddr: 0x41120, symSize: 0x2C }
  - { offsetInCU: 0xF74, offset: 0x2AC26, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x4D98, symBinAddr: 0x4116C, symSize: 0x10 }
  - { offsetInCU: 0xF87, offset: 0x2AC39, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x4DA8, symBinAddr: 0x4117C, symSize: 0x8 }
  - { offsetInCU: 0xF9A, offset: 0x2AC4C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV24fetchAndSetPropertiesFor10parameters5nonce7handleryAA01_C20CompletionParametersC_SSyAIctFySo24FBSDKAuthenticationTokenCSgcfU_TA', symObjAddr: 0x4EE4, symBinAddr: 0x41260, symSize: 0x10 }
  - { offsetInCU: 0xFAD, offset: 0x2AC5F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15ProfileCreating_pWOb', symObjAddr: 0x575C, symBinAddr: 0x41AD8, symSize: 0x18 }
  - { offsetInCU: 0xFC0, offset: 0x2AC72, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesVSgWOf', symObjAddr: 0x5774, symBinAddr: 0x41AF0, symSize: 0x48 }
  - { offsetInCU: 0xFD3, offset: 0x2AC85, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterVMa', symObjAddr: 0x57BC, symBinAddr: 0x41B38, symSize: 0x10 }
  - { offsetInCU: 0xFE6, offset: 0x2AC98, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesVwCP', symObjAddr: 0x57CC, symBinAddr: 0x41B48, symSize: 0x30 }
  - { offsetInCU: 0xFF9, offset: 0x2ACAB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesVwxx', symObjAddr: 0x57FC, symBinAddr: 0x41B78, symSize: 0x3C }
  - { offsetInCU: 0x100C, offset: 0x2ACBE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesVwcp', symObjAddr: 0x5838, symBinAddr: 0x41BB4, symSize: 0x80 }
  - { offsetInCU: 0x101F, offset: 0x2ACD1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesVwca', symObjAddr: 0x58B8, symBinAddr: 0x41C34, symSize: 0x84 }
  - { offsetInCU: 0x1032, offset: 0x2ACE4, size: 0x8, addend: 0x0, symName: ___swift_memcpy104_8, symObjAddr: 0x5AA4, symBinAddr: 0x41CB8, symSize: 0x2C }
  - { offsetInCU: 0x1045, offset: 0x2ACF7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesVwta', symObjAddr: 0x5AD0, symBinAddr: 0x41CE4, symSize: 0x80 }
  - { offsetInCU: 0x1058, offset: 0x2AD0A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesVwet', symObjAddr: 0x5B50, symBinAddr: 0x41D64, symSize: 0x48 }
  - { offsetInCU: 0x106B, offset: 0x2AD1D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesVwst', symObjAddr: 0x5B98, symBinAddr: 0x41DAC, symSize: 0x58 }
  - { offsetInCU: 0x107E, offset: 0x2AD30, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesVMa', symObjAddr: 0x5BF0, symBinAddr: 0x41E04, symSize: 0x10 }
  - { offsetInCU: 0x1091, offset: 0x2AD43, size: 0x8, addend: 0x0, symName: '_$sSo8NSNumberCMa', symObjAddr: 0x5C00, symBinAddr: 0x41E14, symSize: 0x3C }
  - { offsetInCU: 0x147D, offset: 0x2B12F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV13urlParameters5appIDACSDySSypG_SStcfC', symObjAddr: 0x0, symBinAddr: 0x3C494, symSize: 0x4 }
  - { offsetInCU: 0x1490, offset: 0x2B142, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV10parametersAA01_C20CompletionParametersCvg', symObjAddr: 0x4, symBinAddr: 0x3C498, symSize: 0x4 }
  - { offsetInCU: 0x14EB, offset: 0x2B19D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV13setParameters6values5appIDySDySSypG_SStF', symObjAddr: 0x8, symBinAddr: 0x3C49C, symSize: 0xD48 }
  - { offsetInCU: 0x1883, offset: 0x2B535, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV5error4froms5Error_pSgSDySSypG_tF', symObjAddr: 0xD50, symBinAddr: 0x3D1E4, symSize: 0x4 }
  - { offsetInCU: 0x18A2, offset: 0x2B554, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV08completeC07handleryyAA01_C20CompletionParametersCc_tF', symObjAddr: 0xD54, symBinAddr: 0x3D1E8, symSize: 0x20 }
  - { offsetInCU: 0x18DA, offset: 0x2B58C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV08completeC05nonce12codeVerifier7handlerySSSg_AHyAA01_C20CompletionParametersCctF', symObjAddr: 0xD74, symBinAddr: 0x3D208, symSize: 0x340 }
  - { offsetInCU: 0x19DF, offset: 0x2B691, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV25exchangeCodeForTokensWith5nonce12codeVerifier7handlerySSSg_AHyAA01_C20CompletionParametersCctF', symObjAddr: 0x10B4, symBinAddr: 0x3D548, symSize: 0x884 }
  - { offsetInCU: 0x1C01, offset: 0x2B8B3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV25exchangeCodeForTokensWith5nonce12codeVerifier7handlerySSSg_AHyAA01_C20CompletionParametersCctFySo27FBSDKGraphRequestConnecting_pSg_ypSgs5Error_pSgtcfU_', symObjAddr: 0x2B84, symBinAddr: 0x3F018, symSize: 0x650 }
  - { offsetInCU: 0x1D71, offset: 0x2BA23, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV25exchangeNonceForTokenWith7handler014authenticationF0yyAA01_C20CompletionParametersCc_SStF', symObjAddr: 0x1938, symBinAddr: 0x3DDCC, symSize: 0x55C }
  - { offsetInCU: 0x1F28, offset: 0x2BBDA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV25exchangeNonceForTokenWith7handler014authenticationF0yyAA01_C20CompletionParametersCc_SStFySo27FBSDKGraphRequestConnecting_pSg_ypSgs5Error_pSgtcfU_', symObjAddr: 0x274C, symBinAddr: 0x3EBE0, symSize: 0x438 }
  - { offsetInCU: 0x1FF2, offset: 0x2BCA4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV24fetchAndSetPropertiesFor10parameters5nonce7handleryAA01_C20CompletionParametersC_SSyAIctF', symObjAddr: 0x1E94, symBinAddr: 0x3E328, symSize: 0x308 }
  - { offsetInCU: 0x20B1, offset: 0x2BD63, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV24fetchAndSetPropertiesFor10parameters5nonce7handleryAA01_C20CompletionParametersC_SSyAIctFySo24FBSDKAuthenticationTokenCSgcfU_', symObjAddr: 0x219C, symBinAddr: 0x3E630, symSize: 0x1F4 }
  - { offsetInCU: 0x21B5, offset: 0x2BE67, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV7profile4with11permissions09FBSDKCoreB07ProfileCSgAG25AuthenticationTokenClaimsC_ShySSGSgtF', symObjAddr: 0x2390, symBinAddr: 0x3E824, symSize: 0x4 }
  - { offsetInCU: 0x21C9, offset: 0x2BE7B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV18expirationDateFrom10parameters10Foundation0F0VSDySSypG_tF', symObjAddr: 0x2394, symBinAddr: 0x3E828, symSize: 0x2CC }
  - { offsetInCU: 0x22E7, offset: 0x2BF99, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV28dataAccessExpirationDateFrom10parameters10Foundation0H0VSDySSypG_tF', symObjAddr: 0x2660, symBinAddr: 0x3EAF4, symSize: 0xE8 }
  - { offsetInCU: 0x2352, offset: 0x2C004, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV9challenge4fromSSSgSDySSypG_tF', symObjAddr: 0x2748, symBinAddr: 0x3EBDC, symSize: 0x4 }
  - { offsetInCU: 0x238D, offset: 0x2C03F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV14profileFactoryAA15ProfileCreating_pvg', symObjAddr: 0x31FC, symBinAddr: 0x3F690, symSize: 0x1C }
  - { offsetInCU: 0x23A0, offset: 0x2C052, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV14profileFactoryAA15ProfileCreating_pvs', symObjAddr: 0x3218, symBinAddr: 0x3F6AC, symSize: 0x2C }
  - { offsetInCU: 0x23B3, offset: 0x2C065, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV14profileFactoryAA15ProfileCreating_pvM', symObjAddr: 0x3244, symBinAddr: 0x3F6D8, symSize: 0x10 }
  - { offsetInCU: 0x23C6, offset: 0x2C078, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV14profileFactoryAA15ProfileCreating_pvM.resume.0', symObjAddr: 0x3254, symBinAddr: 0x3F6E8, symSize: 0x4 }
  - { offsetInCU: 0x23D9, offset: 0x2C08B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV26authenticationTokenCreatorAA014AuthenticationH8Creating_pvg', symObjAddr: 0x3258, symBinAddr: 0x3F6EC, symSize: 0x1C }
  - { offsetInCU: 0x23EC, offset: 0x2C09E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV26authenticationTokenCreatorAA014AuthenticationH8Creating_pvs', symObjAddr: 0x3274, symBinAddr: 0x3F708, symSize: 0x30 }
  - { offsetInCU: 0x23FF, offset: 0x2C0B1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV26authenticationTokenCreatorAA014AuthenticationH8Creating_pvM', symObjAddr: 0x32A4, symBinAddr: 0x3F738, symSize: 0x10 }
  - { offsetInCU: 0x2412, offset: 0x2C0C4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV26authenticationTokenCreatorAA014AuthenticationH8Creating_pvM.resume.0', symObjAddr: 0x32B4, symBinAddr: 0x3F748, symSize: 0x4 }
  - { offsetInCU: 0x2425, offset: 0x2C0D7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV19graphRequestFactorySo010FBSDKGraphhI0_pvg', symObjAddr: 0x32B8, symBinAddr: 0x3F74C, symSize: 0x8 }
  - { offsetInCU: 0x2438, offset: 0x2C0EA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV19graphRequestFactorySo010FBSDKGraphhI0_pvs', symObjAddr: 0x32C0, symBinAddr: 0x3F754, symSize: 0x28 }
  - { offsetInCU: 0x244B, offset: 0x2C0FD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV19graphRequestFactorySo010FBSDKGraphhI0_pvM', symObjAddr: 0x32E8, symBinAddr: 0x3F77C, symSize: 0x10 }
  - { offsetInCU: 0x245E, offset: 0x2C110, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV19graphRequestFactorySo010FBSDKGraphhI0_pvM.resume.0', symObjAddr: 0x32F8, symBinAddr: 0x3F78C, symSize: 0x4 }
  - { offsetInCU: 0x2471, offset: 0x2C123, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV15internalUtilitySo15FBSDKURLHosting_pvg', symObjAddr: 0x32FC, symBinAddr: 0x3F790, symSize: 0x8 }
  - { offsetInCU: 0x2484, offset: 0x2C136, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV15internalUtilitySo15FBSDKURLHosting_pvs', symObjAddr: 0x3304, symBinAddr: 0x3F798, symSize: 0x28 }
  - { offsetInCU: 0x2497, offset: 0x2C149, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV15internalUtilitySo15FBSDKURLHosting_pvM', symObjAddr: 0x332C, symBinAddr: 0x3F7C0, symSize: 0x10 }
  - { offsetInCU: 0x24AA, offset: 0x2C15C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV15internalUtilitySo15FBSDKURLHosting_pvM.resume.0', symObjAddr: 0x333C, symBinAddr: 0x3F7D0, symSize: 0x4 }
  - { offsetInCU: 0x24BD, offset: 0x2C16F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV12errorFactorySo18FBSDKErrorCreating_pvg', symObjAddr: 0x3340, symBinAddr: 0x3F7D4, symSize: 0x8 }
  - { offsetInCU: 0x24D0, offset: 0x2C182, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV12errorFactorySo18FBSDKErrorCreating_pvs', symObjAddr: 0x3348, symBinAddr: 0x3F7DC, symSize: 0x28 }
  - { offsetInCU: 0x24E3, offset: 0x2C195, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV12errorFactorySo18FBSDKErrorCreating_pvM', symObjAddr: 0x3370, symBinAddr: 0x3F804, symSize: 0x10 }
  - { offsetInCU: 0x24F6, offset: 0x2C1A8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV12errorFactorySo18FBSDKErrorCreating_pvM.resume.0', symObjAddr: 0x3380, symBinAddr: 0x3F814, symSize: 0x4 }
  - { offsetInCU: 0x250F, offset: 0x2C1C1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV14profileFactory26authenticationTokenCreator012graphRequestH015internalUtility05errorH0AeA15ProfileCreating_p_AA014AuthenticationjR0_pSo010FBSDKGraphmH0_pSo15FBSDKURLHosting_pSo010FBSDKErrorR0_ptcfC', symObjAddr: 0x3384, symBinAddr: 0x3F818, symSize: 0x58 }
  - { offsetInCU: 0x2522, offset: 0x2C1D4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV22configuredDependenciesAC04TypeF0VSgvMZ', symObjAddr: 0x3474, symBinAddr: 0x3F908, symSize: 0x6C }
  - { offsetInCU: 0x2588, offset: 0x2C23A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV19defaultDependenciesAC04TypeF0VSgvMZ', symObjAddr: 0x37CC, symBinAddr: 0x3FC60, symSize: 0x6C }
  - { offsetInCU: 0x25A7, offset: 0x2C259, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV19defaultDependenciesAC04TypeF0VSgvMZ.resume.0', symObjAddr: 0x3838, symBinAddr: 0x3FCCC, symSize: 0x4 }
  - { offsetInCU: 0x90, offset: 0x2C3E2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginUtilityO9getUserID4fromSSSgAF_tFZTf4nd_n', symObjAddr: 0x70, symBinAddr: 0x41FB4, symSize: 0x304 }
  - { offsetInCU: 0x1AB, offset: 0x2C4FD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginUtilityO18getQueryParameters4fromSDySSypGSg10Foundation3URLV_tFZTf4nd_n', symObjAddr: 0x374, symBinAddr: 0x422B8, symSize: 0x5C4 }
  - { offsetInCU: 0x35F, offset: 0x2C6B1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginUtilityOMa', symObjAddr: 0x938, symBinAddr: 0x4287C, symSize: 0x10 }
  - { offsetInCU: 0x372, offset: 0x2C6C4, size: 0x8, addend: 0x0, symName: '_$s10Foundation3URLVSgWOc', symObjAddr: 0xA4C, symBinAddr: 0x4288C, symSize: 0x48 }
  - { offsetInCU: 0x54A, offset: 0x2C89C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginUtilityO17stringForAudienceySSAA07DefaultG0OFZ', symObjAddr: 0x0, symBinAddr: 0x41F44, symSize: 0x68 }
  - { offsetInCU: 0x57A, offset: 0x2C8CC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginUtilityO18getQueryParameters4fromSDySSypGSg10Foundation3URLV_tFZ', symObjAddr: 0x68, symBinAddr: 0x41FAC, symSize: 0x4 }
  - { offsetInCU: 0x58D, offset: 0x2C8DF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginUtilityO9getUserID4fromSSSgAF_tFZ', symObjAddr: 0x6C, symBinAddr: 0x41FB0, symSize: 0x4 }
  - { offsetInCU: 0x27, offset: 0x2C9A2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24MissingDependenciesErrorV3forACyxGxm_tcfC', symObjAddr: 0x0, symBinAddr: 0x42910, symSize: 0x4 }
  - { offsetInCU: 0x73, offset: 0x2C9EE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24MissingDependenciesErrorVyxGs23CustomStringConvertibleAAsAEP11descriptionSSvgTW', symObjAddr: 0xC4, symBinAddr: 0x429D4, symSize: 0x8 }
  - { offsetInCU: 0xA4, offset: 0x2CA1F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24MissingDependenciesErrorVMi', symObjAddr: 0xCC, symBinAddr: 0x429DC, symSize: 0x8 }
  - { offsetInCU: 0xB7, offset: 0x2CA32, size: 0x8, addend: 0x0, symName: ___swift_memcpy8_8, symObjAddr: 0xD4, symBinAddr: 0x429E4, symSize: 0xC }
  - { offsetInCU: 0xCA, offset: 0x2CA45, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24MissingDependenciesErrorVwet', symObjAddr: 0xE4, symBinAddr: 0x429F0, symSize: 0x48 }
  - { offsetInCU: 0xDD, offset: 0x2CA58, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24MissingDependenciesErrorVwst', symObjAddr: 0x12C, symBinAddr: 0x42A38, symSize: 0x3C }
  - { offsetInCU: 0xF0, offset: 0x2CA6B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24MissingDependenciesErrorVMa', symObjAddr: 0x168, symBinAddr: 0x42A74, symSize: 0xC }
  - { offsetInCU: 0x103, offset: 0x2CA7E, size: 0x8, addend: 0x0, symName: ___swift_instantiateGenericMetadata, symObjAddr: 0x174, symBinAddr: 0x42A80, symSize: 0x2C }
  - { offsetInCU: 0x18F, offset: 0x2CB0A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24MissingDependenciesErrorVyxGs0E0AAsAEP7_domainSSvgTW', symObjAddr: 0xB4, symBinAddr: 0x429C4, symSize: 0x4 }
  - { offsetInCU: 0x1AB, offset: 0x2CB26, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24MissingDependenciesErrorVyxGs0E0AAsAEP5_codeSivgTW', symObjAddr: 0xB8, symBinAddr: 0x429C8, symSize: 0x4 }
  - { offsetInCU: 0x1C7, offset: 0x2CB42, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24MissingDependenciesErrorVyxGs0E0AAsAEP9_userInfoyXlSgvgTW', symObjAddr: 0xBC, symBinAddr: 0x429CC, symSize: 0x4 }
  - { offsetInCU: 0x1E2, offset: 0x2CB5D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24MissingDependenciesErrorVyxGs0E0AAsAEP19_getEmbeddedNSErroryXlSgyFTW', symObjAddr: 0xC0, symBinAddr: 0x429D0, symSize: 0x4 }
  - { offsetInCU: 0x29A, offset: 0x2CC15, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24MissingDependenciesErrorV3forACyxGxm_tcfC', symObjAddr: 0x0, symBinAddr: 0x42910, symSize: 0x4 }
  - { offsetInCU: 0x2F6, offset: 0x2CC71, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24MissingDependenciesErrorV11descriptionSSvg', symObjAddr: 0x4, symBinAddr: 0x42914, symSize: 0xB0 }
  - { offsetInCU: 0x27, offset: 0x2CD4E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14NonceValidatorO7isValid5nonceSbSS_tFZ', symObjAddr: 0x0, symBinAddr: 0x42AAC, symSize: 0xDC }
  - { offsetInCU: 0x3D, offset: 0x2CD64, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14NonceValidatorOMa', symObjAddr: 0x120, symBinAddr: 0x42B88, symSize: 0x10 }
  - { offsetInCU: 0x10B, offset: 0x2CE32, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14NonceValidatorO7isValid5nonceSbSS_tFZ', symObjAddr: 0x0, symBinAddr: 0x42AAC, symSize: 0xDC }
  - { offsetInCU: 0x27, offset: 0x2CEA9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14ProfileFactoryCACycfC', symObjAddr: 0x0, symBinAddr: 0x42C00, symSize: 0x20 }
  - { offsetInCU: 0xDA, offset: 0x2CF5C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14ProfileFactoryCACycfcTo', symObjAddr: 0xA0, symBinAddr: 0x42CA0, symSize: 0x3C }
  - { offsetInCU: 0x12A, offset: 0x2CFAC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14ProfileFactoryCAA0C8CreatingA2aDP06createC06userID9firstName06middleJ004lastJ04name7linkURL11refreshDate05imageO05email9friendIDs8birthday8ageRange8hometown8location6gender11permissions9isLimited09FBSDKCoreB00C0CSS_SSSgA_A_A_10Foundation0O0VSgA0_0Q0VSgA3_A_SaySSGSgA6_So012FBSDKUserAgeX0CSgSo13FBSDKLocationCSgA14_A_ShySSGSgSbtFTW', symObjAddr: 0x110, symBinAddr: 0x42D10, symSize: 0x4C }
  - { offsetInCU: 0x15A, offset: 0x2CFDC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14ProfileFactoryC06createC06userID9firstName06middleI004lastI04name7linkURL11refreshDate05imageN05email9friendIDs8birthday8ageRange8hometown8location6gender11permissions9isLimited09FBSDKCoreB00C0CSS_SSSgA3Y10Foundation0N0VSgAZ0P0VSgA1_AYSaySSGSgA4_So012FBSDKUserAgeW0CSgSo13FBSDKLocationCSgA12_AYShySSGSgSbtFTf4nnnnnnnnnnnnnnnnnd_n', symObjAddr: 0x15C, symBinAddr: 0x42D5C, symSize: 0x2D4 }
  - { offsetInCU: 0x28F, offset: 0x2D111, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14ProfileFactoryCMa', symObjAddr: 0x430, symBinAddr: 0x43030, symSize: 0x20 }
  - { offsetInCU: 0x4B4, offset: 0x2D336, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14ProfileFactoryCACycfC', symObjAddr: 0x0, symBinAddr: 0x42C00, symSize: 0x20 }
  - { offsetInCU: 0x4C7, offset: 0x2D349, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14ProfileFactoryC06createC06userID9firstName06middleI004lastI04name7linkURL11refreshDate05imageN05email9friendIDs8birthday8ageRange8hometown8location6gender11permissions9isLimited09FBSDKCoreB00C0CSS_SSSgA3Y10Foundation0N0VSgAZ0P0VSgA1_AYSaySSGSgA4_So012FBSDKUserAgeW0CSgSo13FBSDKLocationCSgA12_AYShySSGSgSbtF', symObjAddr: 0x20, symBinAddr: 0x42C20, symSize: 0x4C }
  - { offsetInCU: 0x4DB, offset: 0x2D35D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14ProfileFactoryCACycfc', symObjAddr: 0x6C, symBinAddr: 0x42C6C, symSize: 0x34 }
  - { offsetInCU: 0x515, offset: 0x2D397, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14ProfileFactoryCfD', symObjAddr: 0xDC, symBinAddr: 0x42CDC, symSize: 0x34 }
  - { offsetInCU: 0x27, offset: 0x2D41E, size: 0x8, addend: 0x0, symName: '_$s12FBSDKCoreKit27ServerConfigurationProviderC010FBSDKLoginB00cD9ProvidingA2dEP04loadcD010completionyySo0F7TooltipCSg_s5Error_pSgtcSg_tFTW', symObjAddr: 0x0, symBinAddr: 0x43094, symSize: 0x20 }
  - { offsetInCU: 0x64, offset: 0x2D45B, size: 0x8, addend: 0x0, symName: '_$s12FBSDKCoreKit27ServerConfigurationProviderC010FBSDKLoginB00cD9ProvidingA2dEP04loadcD010completionyySo0F7TooltipCSg_s5Error_pSgtcSg_tFTW', symObjAddr: 0x0, symBinAddr: 0x43094, symSize: 0x20 }
  - { offsetInCU: 0x183, offset: 0x2D66C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC5errors5Error_pSgvgTo', symObjAddr: 0x800, symBinAddr: 0x438D4, symSize: 0x70 }
  - { offsetInCU: 0x1D7, offset: 0x2D6C0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC5errors5Error_pSgvsTo', symObjAddr: 0x8C0, symBinAddr: 0x43994, symSize: 0x64 }
  - { offsetInCU: 0x2C0, offset: 0x2D7A9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersCACycfcTo', symObjAddr: 0x1278, symBinAddr: 0x442C4, symSize: 0x20 }
  - { offsetInCU: 0x2F1, offset: 0x2D7DA, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DateVSgWOc', symObjAddr: 0x9EC, symBinAddr: 0x43AC0, symSize: 0x48 }
  - { offsetInCU: 0x304, offset: 0x2D7ED, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersCfETo', symObjAddr: 0x12CC, symBinAddr: 0x44318, symSize: 0x14C }
  - { offsetInCU: 0x332, offset: 0x2D81B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersCMU', symObjAddr: 0x1418, symBinAddr: 0x44464, symSize: 0x8 }
  - { offsetInCU: 0x345, offset: 0x2D82E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersCMa', symObjAddr: 0x1420, symBinAddr: 0x4446C, symSize: 0x3C }
  - { offsetInCU: 0x358, offset: 0x2D841, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersCMr', symObjAddr: 0x145C, symBinAddr: 0x444A8, symSize: 0x98 }
  - { offsetInCU: 0x36B, offset: 0x2D854, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DateVSgMa', symObjAddr: 0x14F4, symBinAddr: 0x44540, symSize: 0x54 }
  - { offsetInCU: 0x37E, offset: 0x2D867, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DateVSgWOh', symObjAddr: 0x1590, symBinAddr: 0x44594, symSize: 0x40 }
  - { offsetInCU: 0x45E, offset: 0x2D947, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersCACycfC', symObjAddr: 0x0, symBinAddr: 0x430D4, symSize: 0x20 }
  - { offsetInCU: 0x471, offset: 0x2D95A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC19authenticationTokenSo019FBSDKAuthenticationG0CSgvg', symObjAddr: 0x2C, symBinAddr: 0x43100, symSize: 0x50 }
  - { offsetInCU: 0x49A, offset: 0x2D983, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC19authenticationTokenSo019FBSDKAuthenticationG0CSgvM', symObjAddr: 0x94, symBinAddr: 0x43168, symSize: 0x44 }
  - { offsetInCU: 0x4BD, offset: 0x2D9A6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC7profile09FBSDKCoreB07ProfileCSgvg', symObjAddr: 0x128, symBinAddr: 0x431FC, symSize: 0x50 }
  - { offsetInCU: 0x4E0, offset: 0x2D9C9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC7profile09FBSDKCoreB07ProfileCSgvM', symObjAddr: 0x23C, symBinAddr: 0x43310, symSize: 0x44 }
  - { offsetInCU: 0x503, offset: 0x2D9EC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC17accessTokenStringSSSgvM', symObjAddr: 0x2B0, symBinAddr: 0x43384, symSize: 0x44 }
  - { offsetInCU: 0x526, offset: 0x2DA0F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC11nonceStringSSSgvM', symObjAddr: 0x324, symBinAddr: 0x433F8, symSize: 0x44 }
  - { offsetInCU: 0x549, offset: 0x2DA32, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC25authenticationTokenStringSSSgvM', symObjAddr: 0x398, symBinAddr: 0x4346C, symSize: 0x44 }
  - { offsetInCU: 0x56C, offset: 0x2DA55, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC4codeSSSgvM', symObjAddr: 0x40C, symBinAddr: 0x434E0, symSize: 0x44 }
  - { offsetInCU: 0x58F, offset: 0x2DA78, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC11permissionsShyAA12FBPermissionCGSgvM', symObjAddr: 0x488, symBinAddr: 0x4355C, symSize: 0x44 }
  - { offsetInCU: 0x5B2, offset: 0x2DA9B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC19declinedPermissionsShyAA12FBPermissionCGSgvM', symObjAddr: 0x504, symBinAddr: 0x435D8, symSize: 0x44 }
  - { offsetInCU: 0x5D5, offset: 0x2DABE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC18expiredPermissionsShyAA12FBPermissionCGSgvM', symObjAddr: 0x6D4, symBinAddr: 0x437A8, symSize: 0x44 }
  - { offsetInCU: 0x5F8, offset: 0x2DAE1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC5appIDSSSgvM', symObjAddr: 0x748, symBinAddr: 0x4381C, symSize: 0x44 }
  - { offsetInCU: 0x61B, offset: 0x2DB04, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC6userIDSSSgvM', symObjAddr: 0x7BC, symBinAddr: 0x43890, symSize: 0x44 }
  - { offsetInCU: 0x655, offset: 0x2DB3E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC5errors5Error_pSgvg', symObjAddr: 0x870, symBinAddr: 0x43944, symSize: 0x50 }
  - { offsetInCU: 0x694, offset: 0x2DB7D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC5errors5Error_pSgvM', symObjAddr: 0x990, symBinAddr: 0x43A64, symSize: 0x44 }
  - { offsetInCU: 0x6B7, offset: 0x2DBA0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC14expirationDate10Foundation0G0VSgvM', symObjAddr: 0xAD4, symBinAddr: 0x43B20, symSize: 0x44 }
  - { offsetInCU: 0x6DA, offset: 0x2DBC3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC24dataAccessExpirationDate10Foundation0I0VSgvM', symObjAddr: 0xDC8, symBinAddr: 0x43E14, symSize: 0x44 }
  - { offsetInCU: 0x6FD, offset: 0x2DBE6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC9challengeSSSgvM', symObjAddr: 0xE3C, symBinAddr: 0x43E88, symSize: 0x44 }
  - { offsetInCU: 0x720, offset: 0x2DC09, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC11graphDomainSSSgvM', symObjAddr: 0xEB0, symBinAddr: 0x43EFC, symSize: 0x44 }
  - { offsetInCU: 0x743, offset: 0x2DC2C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC11graphDomainSSSgvM.resume.0', symObjAddr: 0xEF4, symBinAddr: 0x43F40, symSize: 0x4 }
  - { offsetInCU: 0x762, offset: 0x2DC4B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC14userTokenNonceSSSgvM', symObjAddr: 0x10BC, symBinAddr: 0x44108, symSize: 0x44 }
  - { offsetInCU: 0x785, offset: 0x2DC6E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersCACycfc', symObjAddr: 0x1100, symBinAddr: 0x4414C, symSize: 0x178 }
  - { offsetInCU: 0x7A8, offset: 0x2DC91, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersCfD', symObjAddr: 0x1298, symBinAddr: 0x442E4, symSize: 0x34 }
...
