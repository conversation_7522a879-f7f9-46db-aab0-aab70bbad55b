---
triple:          'x86_64-apple-darwin'
binary-path:     '/data/sandcastle/boxes/trunk-git-facebook-ios-sdk/build/Build/Intermediates.noindex/ArchiveIntermediates/FBSDKLoginKit-Dynamic/InstallationBuildProductsLocation/@rpath/FBSDKLoginKit.framework/Versions/A/FBSDKLoginKit'
relocations:
  - { offsetInCU: 0x33, offset: 0x33, size: 0x8, addend: 0x0, symName: _FBSDKLoginKitVersionString, symObjAddr: 0x0, symBinAddr: 0x47CE0, symSize: 0x0 }
  - { offsetInCU: 0x67, offset: 0x67, size: 0x8, addend: 0x0, symName: _FBSDKLoginKitVersionNumber, symObjAddr: 0x40, symBinAddr: 0x47D20, symSize: 0x0 }
  - { offsetInCU: 0x34, offset: 0xB0, size: 0x8, addend: 0x0, symName: _FBSDKLoginAuthTypeRerequest, symObjAddr: 0x58, symBinAddr: 0x55540, symSize: 0x0 }
  - { offsetInCU: 0xB7, offset: 0x133, size: 0x8, addend: 0x0, symName: _FBSDKLoginAuthTypeReauthorize, symObjAddr: 0x60, symBinAddr: 0x55548, symSize: 0x0 }
  - { offsetInCU: 0x34, offset: 0x184, size: 0x8, addend: 0x0, symName: _FBSDKLoginErrorDomain, symObjAddr: 0x38, symBinAddr: 0x55550, symSize: 0x0 }
  - { offsetInCU: 0x3D, offset: 0x23F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5frame15elementProvider06stringG005loginG019graphRequestFactoryACSo6CGRectV_AA29UserInterfaceElementProviding_pAA0no6StringQ0_pAA05LoginQ0_pSo010FBSDKGraphkL0_ptcfcfA_', symObjAddr: 0xE0, symBinAddr: 0x1970, symSize: 0x20 }
  - { offsetInCU: 0x56, offset: 0x258, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5frame11permissionsACSo6CGRectV_Say09FBSDKCoreB010PermissionOGtcfcfA_', symObjAddr: 0x100, symBinAddr: 0x1990, symSize: 0x20 }
  - { offsetInCU: 0x6F, offset: 0x271, size: 0x8, addend: 0x0, symName: ___swift_instantiateConcreteTypeFromMangledName, symObjAddr: 0x130, symBinAddr: 0x19C0, symSize: 0x40 }
  - { offsetInCU: 0x82, offset: 0x284, size: 0x8, addend: 0x0, symName: ___swift_memcpy16_8, symObjAddr: 0x250, symBinAddr: 0x1AE0, symSize: 0x10 }
  - { offsetInCU: 0x95, offset: 0x297, size: 0x8, addend: 0x0, symName: ___swift_noop_void_return, symObjAddr: 0x260, symBinAddr: 0x1AF0, symSize: 0x10 }
  - { offsetInCU: 0xA8, offset: 0x2AA, size: 0x8, addend: 0x0, symName: '_$sSo7CGPointVwet', symObjAddr: 0x270, symBinAddr: 0x1B00, symSize: 0x20 }
  - { offsetInCU: 0xBB, offset: 0x2BD, size: 0x8, addend: 0x0, symName: '_$sSo7CGPointVwst', symObjAddr: 0x290, symBinAddr: 0x1B20, symSize: 0x30 }
  - { offsetInCU: 0xCE, offset: 0x2D0, size: 0x8, addend: 0x0, symName: '_$sSo18FBSDKLoginAuthTypeas20_SwiftNewtypeWrapperSCSYWb', symObjAddr: 0x6C0, symBinAddr: 0x1EB0, symSize: 0x20 }
  - { offsetInCU: 0xE1, offset: 0x2E3, size: 0x8, addend: 0x0, symName: '_$sSo18FBSDKLoginAuthTypeas20_SwiftNewtypeWrapperSCs35_HasCustomAnyHashableRepresentationPWb', symObjAddr: 0x6E0, symBinAddr: 0x1ED0, symSize: 0x20 }
  - { offsetInCU: 0xF4, offset: 0x2F6, size: 0x8, addend: 0x0, symName: '_$sSo18FBSDKLoginAuthTypeaSHSCSQWb', symObjAddr: 0x730, symBinAddr: 0x1F20, symSize: 0x20 }
  - { offsetInCU: 0x107, offset: 0x309, size: 0x8, addend: 0x0, symName: '_$sSo26FBSDKAppEventParameterNameas20_SwiftNewtypeWrapperSCSYWb', symObjAddr: 0x8D0, symBinAddr: 0x20C0, symSize: 0x20 }
  - { offsetInCU: 0x11A, offset: 0x31C, size: 0x8, addend: 0x0, symName: '_$sSo26FBSDKAppEventParameterNameas20_SwiftNewtypeWrapperSCs35_HasCustomAnyHashableRepresentationPWb', symObjAddr: 0x8F0, symBinAddr: 0x20E0, symSize: 0x20 }
  - { offsetInCU: 0x12D, offset: 0x32F, size: 0x8, addend: 0x0, symName: '_$sSo26FBSDKAppEventParameterNameaSHSCSQWb', symObjAddr: 0x910, symBinAddr: 0x2100, symSize: 0x20 }
  - { offsetInCU: 0x15B, offset: 0x35D, size: 0x8, addend: 0x0, symName: '_$sSo26FBSDKAppEventParameterNameas21_ObjectiveCBridgeableSCsACP016_forceBridgeFromE1C_6resulty01_E5CTypeQz_xSgztFZTW', symObjAddr: 0x360, symBinAddr: 0x1BF0, symSize: 0x10 }
  - { offsetInCU: 0x176, offset: 0x378, size: 0x8, addend: 0x0, symName: '_$sSo26FBSDKAppEventParameterNameas21_ObjectiveCBridgeableSCsACP024_conditionallyBridgeFromE1C_6resultSb01_E5CTypeQz_xSgztFZTW', symObjAddr: 0x370, symBinAddr: 0x1C00, symSize: 0x10 }
  - { offsetInCU: 0x191, offset: 0x393, size: 0x8, addend: 0x0, symName: '_$sSo18FBSDKLoginAuthTypeas21_ObjectiveCBridgeableSCsACP016_forceBridgeFromD1C_6resulty01_D5CTypeQz_xSgztFZTW', symObjAddr: 0x410, symBinAddr: 0x1C40, symSize: 0x10 }
  - { offsetInCU: 0x1AC, offset: 0x3AE, size: 0x8, addend: 0x0, symName: '_$sSo18FBSDKLoginAuthTypeas21_ObjectiveCBridgeableSCsACP024_conditionallyBridgeFromD1C_6resultSb01_D5CTypeQz_xSgztFZTW', symObjAddr: 0x420, symBinAddr: 0x1C50, symSize: 0x10 }
  - { offsetInCU: 0x1CD, offset: 0x3CF, size: 0x8, addend: 0x0, symName: '_$sSo26FBSDKAppEventParameterNameas35_HasCustomAnyHashableRepresentationSCsACP03_tofgH0s0gH0VSgyFTW', symObjAddr: 0x5E0, symBinAddr: 0x1DD0, symSize: 0x70 }
  - { offsetInCU: 0x1E8, offset: 0x3EA, size: 0x8, addend: 0x0, symName: '_$sSo18FBSDKLoginAuthTypeas35_HasCustomAnyHashableRepresentationSCsACP03_toefG0s0fG0VSgyFTW', symObjAddr: 0x650, symBinAddr: 0x1E40, symSize: 0x70 }
  - { offsetInCU: 0x23C, offset: 0x43E, size: 0x8, addend: 0x0, symName: '_$sSo14FBSDKAppEventsC13FBSDKLoginKit17LoginEventLoggingA2cDP13flushBehaviorSo0ab5FlushI0VvgTW', symObjAddr: 0x0, symBinAddr: 0x1890, symSize: 0x20 }
  - { offsetInCU: 0x28D, offset: 0x48F, size: 0x8, addend: 0x0, symName: '_$sSo14FBSDKAppEventsC13FBSDKLoginKit17LoginEventLoggingA2cDP011logInternalF0_10parameters18isImplicitlyLoggedySo0aF4Namea_SDySo0af9ParameterN0aypGSgSbtFTW', symObjAddr: 0x20, symBinAddr: 0x18B0, symSize: 0xA0 }
  - { offsetInCU: 0x2CF, offset: 0x4D1, size: 0x8, addend: 0x0, symName: '_$sSo14FBSDKAppEventsC13FBSDKLoginKit17LoginEventLoggingA2cDP5flushyyFTW', symObjAddr: 0xC0, symBinAddr: 0x1950, symSize: 0x20 }
  - { offsetInCU: 0x35A, offset: 0x55C, size: 0x8, addend: 0x0, symName: '_$sSo18FBSDKLoginAuthTypeaSYSCSY8rawValue03RawE0QzvgTW', symObjAddr: 0x570, symBinAddr: 0x1D60, symSize: 0x20 }
  - { offsetInCU: 0x1D7, offset: 0x87D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryCAA0cD8CreatingA2aDP06createD011tokenString5nonce11graphDomain10completionySS_S2SySo019FBSDKAuthenticationD0CSgctFTW', symObjAddr: 0x1980, symBinAddr: 0x3BD0, symSize: 0x30 }
  - { offsetInCU: 0x414, offset: 0xABA, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataVSgSo13NSURLResponseCSgs5Error_pSgIegggg_So6NSDataCSgAGSo7NSErrorCSgIeyByyy_TR', symObjAddr: 0x1740, symBinAddr: 0x3990, symSize: 0xF0 }
  - { offsetInCU: 0x42B, offset: 0xAD1, size: 0x8, addend: 0x0, symName: '_$s10Foundation13__DataStorageC15withUnsafeBytes2in5applyxSnySiG_xSWKXEtKlFyt_Tg5153$s13FBSDKLoginKit26AuthenticationTokenFactoryC15verifySignature_6header6claims14certificateKey10completionySS_S3SySbctFySo03SecK3RefaSgcfU_yycfU_ySWXEfU_AA0B0VSays5UInt8VGTf1nncn_n', symObjAddr: 0x19B0, symBinAddr: 0x3C00, symSize: 0xC0 }
  - { offsetInCU: 0x4EE, offset: 0xB94, size: 0x8, addend: 0x0, symName: '_$sS2SSysWl', symObjAddr: 0x1B50, symBinAddr: 0x3DA0, symSize: 0x30 }
  - { offsetInCU: 0x501, offset: 0xBA7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC06createD011tokenString5nonce11graphDomain10completionySS_S2SySo019FBSDKAuthenticationD0CSgctFySbcfU_TA', symObjAddr: 0x1BC0, symBinAddr: 0x3E10, symSize: 0x40 }
  - { offsetInCU: 0x514, offset: 0xBBA, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV15_RepresentationOWOe', symObjAddr: 0x1C60, symBinAddr: 0x3EB0, symSize: 0x40 }
  - { offsetInCU: 0x527, offset: 0xBCD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC15verifySignature_6header6claims14certificateKey10completionySS_S3SySbctFySo03SecK3RefaSgcfU_TA', symObjAddr: 0x1CA0, symBinAddr: 0x3EF0, symSize: 0x30 }
  - { offsetInCU: 0x53A, offset: 0xBE0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC16getPublicKeyWith011certificateH010completionySS_ySo03SecH3RefaSgctFySo0l11CertificateM0aSgcfU_TA', symObjAddr: 0x1CF0, symBinAddr: 0x3F40, symSize: 0x20 }
  - { offsetInCU: 0x54D, offset: 0xBF3, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataVSgWOy', symObjAddr: 0x1D10, symBinAddr: 0x3F60, symSize: 0x20 }
  - { offsetInCU: 0x560, offset: 0xC06, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV15_RepresentationOWOy', symObjAddr: 0x1D30, symBinAddr: 0x3F80, symSize: 0x40 }
  - { offsetInCU: 0x573, offset: 0xC19, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataVSgWOe', symObjAddr: 0x1D70, symBinAddr: 0x3FC0, symSize: 0x20 }
  - { offsetInCU: 0x586, offset: 0xC2C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC18getCertificateWith14certificateKey10completionySS_ySo03SecG3RefaSgctFy10Foundation4DataVSg_So13NSURLResponseCSgs5Error_pSgtcfU_TA', symObjAddr: 0x1DD0, symBinAddr: 0x4020, symSize: 0x30 }
  - { offsetInCU: 0x599, offset: 0xC3F, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x1E00, symBinAddr: 0x4050, symSize: 0x20 }
  - { offsetInCU: 0x5AC, offset: 0xC52, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x1E20, symBinAddr: 0x4070, symSize: 0x10 }
  - { offsetInCU: 0x5BF, offset: 0xC65, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryCMa', symObjAddr: 0x1E30, symBinAddr: 0x4080, symSize: 0x20 }
  - { offsetInCU: 0x89D, offset: 0xF43, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC15verifySignature_6header6claims14certificateKey10completionySS_S3SySbctFySo03SecK3RefaSgcfU_yycfU_TA', symObjAddr: 0x23B0, symBinAddr: 0x45C0, symSize: 0x40 }
  - { offsetInCU: 0x8B0, offset: 0xF56, size: 0x8, addend: 0x0, symName: '_$sSS_yptWOc', symObjAddr: 0x23F0, symBinAddr: 0x4600, symSize: 0x40 }
  - { offsetInCU: 0x8C3, offset: 0xF69, size: 0x8, addend: 0x0, symName: '_$sypWOb', symObjAddr: 0x2430, symBinAddr: 0x4640, symSize: 0x20 }
  - { offsetInCU: 0xA92, offset: 0x1138, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_ypTgm5Tf4g_n', symObjAddr: 0x1A70, symBinAddr: 0x3CC0, symSize: 0xE0 }
  - { offsetInCU: 0xBD1, offset: 0x1277, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_SbTgm5Tf4g_n', symObjAddr: 0x1E80, symBinAddr: 0x40D0, symSize: 0xE0 }
  - { offsetInCU: 0xD23, offset: 0x13C9, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_SSSgTgm5Tf4g_n', symObjAddr: 0x1F60, symBinAddr: 0x41B0, symSize: 0xF0 }
  - { offsetInCU: 0xE69, offset: 0x150F, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_SSTgm5Tf4g_n', symObjAddr: 0x2050, symBinAddr: 0x42A0, symSize: 0xF0 }
  - { offsetInCU: 0xFC1, offset: 0x1667, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_SdTgm5Tf4g_n', symObjAddr: 0x2140, symBinAddr: 0x4390, symSize: 0xE0 }
  - { offsetInCU: 0x1113, offset: 0x17B9, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_So8NSNumberCTgm5Tf4g_n', symObjAddr: 0x2220, symBinAddr: 0x4470, symSize: 0xE0 }
  - { offsetInCU: 0x1570, offset: 0x1C16, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC16beginCertificateSSvg', symObjAddr: 0x0, symBinAddr: 0x2250, symSize: 0x30 }
  - { offsetInCU: 0x1583, offset: 0x1C29, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC14endCertificateSSvg', symObjAddr: 0x30, symBinAddr: 0x2280, symSize: 0x30 }
  - { offsetInCU: 0x1596, offset: 0x1C3C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC15sessionProviderSo24FBSDKURLSessionProviding_pvg', symObjAddr: 0x60, symBinAddr: 0x22B0, symSize: 0x30 }
  - { offsetInCU: 0x15AF, offset: 0x1C55, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC15sessionProviderSo24FBSDKURLSessionProviding_pvs', symObjAddr: 0x90, symBinAddr: 0x22E0, symSize: 0x40 }
  - { offsetInCU: 0x15C2, offset: 0x1C68, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC15sessionProviderSo24FBSDKURLSessionProviding_pvM', symObjAddr: 0xD0, symBinAddr: 0x2320, symSize: 0x30 }
  - { offsetInCU: 0x15D5, offset: 0x1C7B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC15sessionProviderSo24FBSDKURLSessionProviding_pvM.resume.0', symObjAddr: 0x100, symBinAddr: 0x2350, symSize: 0x10 }
  - { offsetInCU: 0x15E8, offset: 0x1C8E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC19certificateEndpoint10Foundation3URLVvg', symObjAddr: 0x110, symBinAddr: 0x2360, symSize: 0x140 }
  - { offsetInCU: 0x164A, offset: 0x1CF0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC15sessionProviderACSo24FBSDKURLSessionProviding_p_tcfC', symObjAddr: 0x250, symBinAddr: 0x24A0, symSize: 0x100 }
  - { offsetInCU: 0x16DA, offset: 0x1D80, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC06createD011tokenString5nonce11graphDomain10completionySS_S2SySo019FBSDKAuthenticationD0CSgctF', symObjAddr: 0x350, symBinAddr: 0x25A0, symSize: 0x390 }
  - { offsetInCU: 0x187F, offset: 0x1F25, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC06createD011tokenString5nonce11graphDomain10completionySS_S2SySo019FBSDKAuthenticationD0CSgctFySbcfU_', symObjAddr: 0x6E0, symBinAddr: 0x2930, symSize: 0x120 }
  - { offsetInCU: 0x198A, offset: 0x2030, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC15verifySignature_6header6claims14certificateKey10completionySS_S3SySbctF', symObjAddr: 0x800, symBinAddr: 0x2A50, symSize: 0x2A0 }
  - { offsetInCU: 0x1ACD, offset: 0x2173, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC15verifySignature_6header6claims14certificateKey10completionySS_S3SySbctFySo03SecK3RefaSgcfU_', symObjAddr: 0xAA0, symBinAddr: 0x2CF0, symSize: 0x100 }
  - { offsetInCU: 0x1B56, offset: 0x21FC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC15verifySignature_6header6claims14certificateKey10completionySS_S3SySbctFySo03SecK3RefaSgcfU_yycfU_', symObjAddr: 0xBA0, symBinAddr: 0x2DF0, symSize: 0x310 }
  - { offsetInCU: 0x1EB5, offset: 0x255B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC15verifySignature_6header6claims14certificateKey10completionySS_S3SySbctFySo03SecK3RefaSgcfU_yycfU_ySWXEfU_', symObjAddr: 0xEB0, symBinAddr: 0x3100, symSize: 0xD0 }
  - { offsetInCU: 0x20D3, offset: 0x2779, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC16getPublicKeyWith011certificateH010completionySS_ySo03SecH3RefaSgctF', symObjAddr: 0xF80, symBinAddr: 0x31D0, symSize: 0x80 }
  - { offsetInCU: 0x2101, offset: 0x27A7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC16getPublicKeyWith011certificateH010completionySS_ySo03SecH3RefaSgctFySo0l11CertificateM0aSgcfU_', symObjAddr: 0x1000, symBinAddr: 0x3250, symSize: 0xB0 }
  - { offsetInCU: 0x21C4, offset: 0x286A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC18getCertificateWith14certificateKey10completionySS_ySo03SecG3RefaSgctF', symObjAddr: 0x10B0, symBinAddr: 0x3300, symSize: 0x1F0 }
  - { offsetInCU: 0x2219, offset: 0x28BF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC18getCertificateWith14certificateKey10completionySS_ySo03SecG3RefaSgctFy10Foundation4DataVSg_So13NSURLResponseCSgs5Error_pSgtcfU_', symObjAddr: 0x12A0, symBinAddr: 0x34F0, symSize: 0x4A0 }
  - { offsetInCU: 0x2327, offset: 0x29CD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryCfd', symObjAddr: 0x1830, symBinAddr: 0x3A80, symSize: 0x30 }
  - { offsetInCU: 0x2354, offset: 0x29FA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryCfD', symObjAddr: 0x1860, symBinAddr: 0x3AB0, symSize: 0x40 }
  - { offsetInCU: 0x2389, offset: 0x2A2F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryCACycfC', symObjAddr: 0x18A0, symBinAddr: 0x3AF0, symSize: 0x30 }
  - { offsetInCU: 0x239C, offset: 0x2A42, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryCACycfc', symObjAddr: 0x18D0, symBinAddr: 0x3B20, symSize: 0xB0 }
  - { offsetInCU: 0xF6, offset: 0x2D64, size: 0x8, addend: 0x0, symName: '_$sypSgWOh', symObjAddr: 0x770, symBinAddr: 0x4DE0, symSize: 0x30 }
  - { offsetInCU: 0x109, offset: 0x2D77, size: 0x8, addend: 0x0, symName: '_$sypWOc', symObjAddr: 0x7A0, symBinAddr: 0x4E10, symSize: 0x30 }
  - { offsetInCU: 0x11C, offset: 0x2D8A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit25AuthenticationTokenHeaderCMa', symObjAddr: 0x7D0, symBinAddr: 0x4E40, symSize: 0x20 }
  - { offsetInCU: 0x1DE, offset: 0x2E4C, size: 0x8, addend: 0x0, symName: '_$ss11AnyHashableVWOc', symObjAddr: 0xBF0, symBinAddr: 0x5260, symSize: 0x30 }
  - { offsetInCU: 0x1F1, offset: 0x2E5F, size: 0x8, addend: 0x0, symName: '_$ss11AnyHashableVWOh', symObjAddr: 0xC20, symBinAddr: 0x5290, symSize: 0x21 }
  - { offsetInCU: 0x341, offset: 0x2FAF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit25AuthenticationTokenHeaderC17fromEncodedStringACSgSS_tcfC', symObjAddr: 0x0, symBinAddr: 0x46F0, symSize: 0x40 }
  - { offsetInCU: 0x354, offset: 0x2FC2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit25AuthenticationTokenHeaderC3kidSSvg', symObjAddr: 0x40, symBinAddr: 0x4730, symSize: 0x30 }
  - { offsetInCU: 0x37F, offset: 0x2FED, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit25AuthenticationTokenHeaderC17fromEncodedStringACSgSS_tcfc', symObjAddr: 0x70, symBinAddr: 0x4760, symSize: 0x5E0 }
  - { offsetInCU: 0x469, offset: 0x30D7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit25AuthenticationTokenHeaderCfd', symObjAddr: 0x6D0, symBinAddr: 0x4D40, symSize: 0x20 }
  - { offsetInCU: 0x4A1, offset: 0x310F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit25AuthenticationTokenHeaderCfD', symObjAddr: 0x6F0, symBinAddr: 0x4D60, symSize: 0x20 }
  - { offsetInCU: 0x4EA, offset: 0x3158, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4findys10_HashTableV6BucketV6bucket_Sb5foundtxSHRzlFSS_Tg5', symObjAddr: 0x710, symBinAddr: 0x4D80, symSize: 0x60 }
  - { offsetInCU: 0x525, offset: 0x3193, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4findys10_HashTableV6BucketV6bucket_Sb5foundtxSHRzlFs11AnyHashableV_Tg5', symObjAddr: 0x820, symBinAddr: 0x4E90, symSize: 0x30 }
  - { offsetInCU: 0x552, offset: 0x31C0, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4findys10_HashTableV6BucketV6bucket_Sb5foundtxSHRzlFSo26FBSDKAppEventParameterNamea_Tg5', symObjAddr: 0x850, symBinAddr: 0x4EC0, symSize: 0x80 }
  - { offsetInCU: 0x58D, offset: 0x31FB, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4find_9hashValues10_HashTableV6BucketV6bucket_Sb5foundtx_SitSHRzlFSS_Tg5', symObjAddr: 0x8D0, symBinAddr: 0x4F40, symSize: 0xE0 }
  - { offsetInCU: 0x5EB, offset: 0x3259, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4find_9hashValues10_HashTableV6BucketV6bucket_Sb5foundtx_SitSHRzlFs11AnyHashableV_Tg5', symObjAddr: 0x9B0, symBinAddr: 0x5020, symSize: 0xC0 }
  - { offsetInCU: 0x612, offset: 0x3280, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4find_9hashValues10_HashTableV6BucketV6bucket_Sb5foundtx_SitSHRzlFSo26FBSDKAppEventParameterNamea_Tg5', symObjAddr: 0xA70, symBinAddr: 0x50E0, symSize: 0x180 }
  - { offsetInCU: 0x2B, offset: 0x331F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12CodeVerifierC5valueSSvgTo', symObjAddr: 0x0, symBinAddr: 0x52C0, symSize: 0x50 }
  - { offsetInCU: 0x61, offset: 0x3355, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12CodeVerifierC5valueSSvgTo', symObjAddr: 0x0, symBinAddr: 0x52C0, symSize: 0x50 }
  - { offsetInCU: 0x99, offset: 0x338D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12CodeVerifierC9challengeSSvgTo', symObjAddr: 0x80, symBinAddr: 0x5340, symSize: 0x50 }
  - { offsetInCU: 0x14B, offset: 0x343F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12CodeVerifierC6stringACSgSS_tcfcTo', symObjAddr: 0xB80, symBinAddr: 0x5E10, symSize: 0x30 }
  - { offsetInCU: 0x190, offset: 0x3484, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12CodeVerifierCACycfcTo', symObjAddr: 0xEA0, symBinAddr: 0x6130, symSize: 0x20 }
  - { offsetInCU: 0x41E, offset: 0x3712, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12CodeVerifierCfETo', symObjAddr: 0xEF0, symBinAddr: 0x6180, symSize: 0x20 }
  - { offsetInCU: 0x44B, offset: 0x373F, size: 0x8, addend: 0x0, symName: '_$s10Foundation13__DataStorageC15withUnsafeBytes2in5applyxSnySiG_xSWKXEtKlFyt_Tg554$s13FBSDKLoginKit12CodeVerifierC9challengeSSvgySWXEfU_AA0B0VSays5UInt8VGTf1nncn_n', symObjAddr: 0xF10, symBinAddr: 0x61A0, symSize: 0xC0 }
  - { offsetInCU: 0x473, offset: 0x3767, size: 0x8, addend: 0x0, symName: '_$sSays5UInt8VGSayxG10Foundation15ContiguousBytesAeBRszlWl', symObjAddr: 0x10D0, symBinAddr: 0x6260, symSize: 0x40 }
  - { offsetInCU: 0x486, offset: 0x377A, size: 0x8, addend: 0x0, symName: '_$sSays5UInt8VGMa', symObjAddr: 0x1110, symBinAddr: 0x62A0, symSize: 0x30 }
  - { offsetInCU: 0x499, offset: 0x378D, size: 0x8, addend: 0x0, symName: ___swift_project_boxed_opaque_existential_1, symObjAddr: 0x1140, symBinAddr: 0x62D0, symSize: 0x30 }
  - { offsetInCU: 0x7B8, offset: 0x3AAC, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV06InlineB0VyAESWcfCTf4nd_n', symObjAddr: 0x15F0, symBinAddr: 0x6780, symSize: 0xC0 }
  - { offsetInCU: 0x828, offset: 0x3B1C, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV10LargeSliceVyAESWcfCTf4nd_n', symObjAddr: 0x16B0, symBinAddr: 0x6840, symSize: 0x80 }
  - { offsetInCU: 0x853, offset: 0x3B47, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV11InlineSliceVyAESWcfCTf4nd_n', symObjAddr: 0x1730, symBinAddr: 0x68C0, symSize: 0x80 }
  - { offsetInCU: 0x8E0, offset: 0x3BD4, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV15_RepresentationOyAESWcfCTf4nd_n', symObjAddr: 0x17B0, symBinAddr: 0x6940, symSize: 0x70 }
  - { offsetInCU: 0x931, offset: 0x3C25, size: 0x8, addend: 0x0, symName: ___swift_destroy_boxed_opaque_existential_1, symObjAddr: 0x1820, symBinAddr: 0x69B0, symSize: 0x30 }
  - { offsetInCU: 0x944, offset: 0x3C38, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12CodeVerifierCMa', symObjAddr: 0x1850, symBinAddr: 0x69E0, symSize: 0x20 }
  - { offsetInCU: 0xBA9, offset: 0x3E9D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12CodeVerifierC5valueSSvg', symObjAddr: 0x50, symBinAddr: 0x5310, symSize: 0x30 }
  - { offsetInCU: 0xC06, offset: 0x3EFA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12CodeVerifierC9challengeSSvg', symObjAddr: 0xD0, symBinAddr: 0x5390, symSize: 0x500 }
  - { offsetInCU: 0xF1C, offset: 0x4210, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12CodeVerifierC9challengeSSvgySWXEfU_', symObjAddr: 0x5D0, symBinAddr: 0x5890, symSize: 0xD0 }
  - { offsetInCU: 0x119C, offset: 0x4490, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12CodeVerifierC6stringACSgSS_tcfC', symObjAddr: 0x6D0, symBinAddr: 0x5960, symSize: 0x250 }
  - { offsetInCU: 0x1255, offset: 0x4549, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12CodeVerifierC6stringACSgSS_tcfc', symObjAddr: 0x920, symBinAddr: 0x5BB0, symSize: 0x260 }
  - { offsetInCU: 0x130B, offset: 0x45FF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12CodeVerifierCACycfC', symObjAddr: 0xBB0, symBinAddr: 0x5E40, symSize: 0x20 }
  - { offsetInCU: 0x1342, offset: 0x4636, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12CodeVerifierCACycfc', symObjAddr: 0xBD0, symBinAddr: 0x5E60, symSize: 0x2D0 }
  - { offsetInCU: 0x1481, offset: 0x4775, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12CodeVerifierCfD', symObjAddr: 0xEC0, symBinAddr: 0x6150, symSize: 0x30 }
  - { offsetInCU: 0x14CC, offset: 0x47C0, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtFs5UInt8V_Tg5', symObjAddr: 0x1170, symBinAddr: 0x6300, symSize: 0xF0 }
  - { offsetInCU: 0x15F9, offset: 0x48ED, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtF13FBSDKLoginKit18DeviceLoginManagerC_Tg5', symObjAddr: 0x1260, symBinAddr: 0x63F0, symSize: 0x180 }
  - { offsetInCU: 0x1744, offset: 0x4A38, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtFSS_Tg5', symObjAddr: 0x13E0, symBinAddr: 0x6570, symSize: 0x110 }
  - { offsetInCU: 0x1859, offset: 0x4B4D, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV13_copyContents8subRange12initializingSpyxGSnySiG_AFtF13FBSDKLoginKit18DeviceLoginManagerC_Tg5', symObjAddr: 0x14F0, symBinAddr: 0x6680, symSize: 0x100 }
  - { offsetInCU: 0x27, offset: 0x4CE4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15DefaultAudienceO8rawValueACSgSu_tcfC', symObjAddr: 0x0, symBinAddr: 0x6A20, symSize: 0x20 }
  - { offsetInCU: 0x73, offset: 0x4D30, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15DefaultAudienceOSYAASY8rawValuexSg03RawF0Qz_tcfCTW', symObjAddr: 0x120, symBinAddr: 0x6B40, symSize: 0x20 }
  - { offsetInCU: 0xA2, offset: 0x4D5F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15DefaultAudienceOSYAASY8rawValue03RawF0QzvgTW', symObjAddr: 0x140, symBinAddr: 0x6B60, symSize: 0x10 }
  - { offsetInCU: 0xC8, offset: 0x4D85, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15DefaultAudienceOSHAASQWb', symObjAddr: 0x40, symBinAddr: 0x6A60, symSize: 0x10 }
  - { offsetInCU: 0xDB, offset: 0x4D98, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15DefaultAudienceOACSQAAWl', symObjAddr: 0x50, symBinAddr: 0x6A70, symSize: 0x30 }
  - { offsetInCU: 0x10C, offset: 0x4DC9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15DefaultAudienceOMa', symObjAddr: 0x150, symBinAddr: 0x6B70, symSize: 0xA }
  - { offsetInCU: 0x14D, offset: 0x4E0A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15DefaultAudienceOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x30, symBinAddr: 0x6A50, symSize: 0x10 }
  - { offsetInCU: 0x1F5, offset: 0x4EB2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15DefaultAudienceOSHAASH9hashValueSivgTW', symObjAddr: 0x80, symBinAddr: 0x6AA0, symSize: 0x40 }
  - { offsetInCU: 0x29C, offset: 0x4F59, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15DefaultAudienceOSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0xC0, symBinAddr: 0x6AE0, symSize: 0x20 }
  - { offsetInCU: 0x2EB, offset: 0x4FA8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15DefaultAudienceOSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0xE0, symBinAddr: 0x6B00, symSize: 0x40 }
  - { offsetInCU: 0x3F0, offset: 0x50AD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15DefaultAudienceO8rawValueACSgSu_tcfC', symObjAddr: 0x0, symBinAddr: 0x6A20, symSize: 0x20 }
  - { offsetInCU: 0x40B, offset: 0x50C8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15DefaultAudienceO8rawValueSuvg', symObjAddr: 0x20, symBinAddr: 0x6A40, symSize: 0x10 }
  - { offsetInCU: 0xE4, offset: 0x5203, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerCAA17DependentAsObjectA2aDP15setDependenciesyy0hJ0QzFTW', symObjAddr: 0x400, symBinAddr: 0x6F80, symSize: 0x60 }
  - { offsetInCU: 0x1BD, offset: 0x52DC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCAA17DependentAsObjectA2aDP15setDependenciesyy0gI0QzFTW', symObjAddr: 0x460, symBinAddr: 0x6FE0, symSize: 0x70 }
  - { offsetInCU: 0x23D, offset: 0x535C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesVWOc', symObjAddr: 0x4D0, symBinAddr: 0x7050, symSize: 0x30 }
  - { offsetInCU: 0x250, offset: 0x536F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesVWOc', symObjAddr: 0x540, symBinAddr: 0x7080, symSize: 0x30 }
  - { offsetInCU: 0x353, offset: 0x5472, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17DependentAsObjectPAAE15setDependenciesyy0eG0QzF', symObjAddr: 0x0, symBinAddr: 0x6B80, symSize: 0xC0 }
  - { offsetInCU: 0x392, offset: 0x54B1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17DependentAsObjectPAAE15getDependencies0eG0QzyKF', symObjAddr: 0xC0, symBinAddr: 0x6C40, symSize: 0x200 }
  - { offsetInCU: 0x3C2, offset: 0x54E1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17DependentAsObjectPAAE13dynamicMemberqd__Sgs7KeyPathCy0E12DependenciesQzqd__G_tcluig', symObjAddr: 0x2C0, symBinAddr: 0x6E40, symSize: 0xF0 }
  - { offsetInCU: 0xE7, offset: 0x5716, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerCAA15DependentAsTypeA2aDP15setDependenciesyy0hJ0QzFZTW', symObjAddr: 0x400, symBinAddr: 0x74E0, symSize: 0x90 }
  - { offsetInCU: 0x19C, offset: 0x57CB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterCAA15DependentAsTypeA2aDP15setDependenciesyy0hJ0QzFZTW', symObjAddr: 0x490, symBinAddr: 0x7570, symSize: 0x90 }
  - { offsetInCU: 0x24F, offset: 0x587E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterVAA15DependentAsTypeA2aDP15setDependenciesyy0gI0QzFZTW', symObjAddr: 0x520, symBinAddr: 0x7600, symSize: 0xA0 }
  - { offsetInCU: 0x2A8, offset: 0x58D7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesVWOc', symObjAddr: 0x5C0, symBinAddr: 0x76A0, symSize: 0x30 }
  - { offsetInCU: 0x2BB, offset: 0x58EA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesVWOc', symObjAddr: 0x630, symBinAddr: 0x76D0, symSize: 0x30 }
  - { offsetInCU: 0x2CE, offset: 0x58FD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC16TypeDependenciesVWOc', symObjAddr: 0x660, symBinAddr: 0x7700, symSize: 0x30 }
  - { offsetInCU: 0x3B0, offset: 0x59DF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15DependentAsTypePAAE15setDependenciesyy0eG0QzFZ', symObjAddr: 0x0, symBinAddr: 0x70E0, symSize: 0xC0 }
  - { offsetInCU: 0x3F0, offset: 0x5A1F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15DependentAsTypePAAE15getDependencies0eG0QzyKFZ', symObjAddr: 0xC0, symBinAddr: 0x71A0, symSize: 0x200 }
  - { offsetInCU: 0x421, offset: 0x5A50, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15DependentAsTypePAAE13dynamicMemberqd__Sgs7KeyPathCy0E12DependenciesQzqd__G_tcluigZ', symObjAddr: 0x2C0, symBinAddr: 0x73A0, symSize: 0xF0 }
  - { offsetInCU: 0x8C, offset: 0x5BF1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoC15verificationURL10Foundation0H0VvgTo', symObjAddr: 0xE0, symBinAddr: 0x7870, symSize: 0x30 }
  - { offsetInCU: 0xD9, offset: 0x5C3E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoC14expirationDate10Foundation0H0VvgTo', symObjAddr: 0x130, symBinAddr: 0x78C0, symSize: 0x30 }
  - { offsetInCU: 0x126, offset: 0x5C8B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoC15pollingIntervalSuvgTo', symObjAddr: 0x1B0, symBinAddr: 0x7940, symSize: 0x20 }
  - { offsetInCU: 0x1BB, offset: 0x5D20, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoC10identifier05loginE015verificationURL14expirationDate15pollingIntervalACSS_SS10Foundation0J0VAI0L0VSutcfcTo', symObjAddr: 0x410, symBinAddr: 0x7BA0, symSize: 0x190 }
  - { offsetInCU: 0x250, offset: 0x5DB5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoCACycfcTo', symObjAddr: 0x5F0, symBinAddr: 0x7D80, symSize: 0x30 }
  - { offsetInCU: 0x2DD, offset: 0x5E42, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoCfETo', symObjAddr: 0x650, symBinAddr: 0x7DE0, symSize: 0x70 }
  - { offsetInCU: 0x30A, offset: 0x5E6F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoCMU', symObjAddr: 0x6C0, symBinAddr: 0x7E50, symSize: 0x10 }
  - { offsetInCU: 0x31D, offset: 0x5E82, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoCMa', symObjAddr: 0x6D0, symBinAddr: 0x7E60, symSize: 0x30 }
  - { offsetInCU: 0x330, offset: 0x5E95, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoCMr', symObjAddr: 0x700, symBinAddr: 0x7E90, symSize: 0xA0 }
  - { offsetInCU: 0x41A, offset: 0x5F7F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoC10identifierSSvg', symObjAddr: 0x20, symBinAddr: 0x77B0, symSize: 0x30 }
  - { offsetInCU: 0x43B, offset: 0x5FA0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoC05loginE0SSvg', symObjAddr: 0xB0, symBinAddr: 0x7840, symSize: 0x30 }
  - { offsetInCU: 0x497, offset: 0x5FFC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoC15pollingIntervalSuvg', symObjAddr: 0x1D0, symBinAddr: 0x7960, symSize: 0x20 }
  - { offsetInCU: 0x515, offset: 0x607A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoC10identifier05loginE015verificationURL14expirationDate15pollingIntervalACSS_SS10Foundation0J0VAI0L0VSutcfC', symObjAddr: 0x1F0, symBinAddr: 0x7980, symSize: 0x110 }
  - { offsetInCU: 0x57F, offset: 0x60E4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoC10identifier05loginE015verificationURL14expirationDate15pollingIntervalACSS_SS10Foundation0J0VAI0L0VSutcfc', symObjAddr: 0x300, symBinAddr: 0x7A90, symSize: 0x110 }
  - { offsetInCU: 0x5DC, offset: 0x6141, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoCACycfC', symObjAddr: 0x5A0, symBinAddr: 0x7D30, symSize: 0x20 }
  - { offsetInCU: 0x5F5, offset: 0x615A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoCACycfc', symObjAddr: 0x5C0, symBinAddr: 0x7D50, symSize: 0x30 }
  - { offsetInCU: 0x649, offset: 0x61AE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoCfD', symObjAddr: 0x620, symBinAddr: 0x7DB0, symSize: 0x30 }
  - { offsetInCU: 0x17E, offset: 0x634D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV10Foundation13CustomNSErrorAadEP11errorDomainSSvgZTW', symObjAddr: 0x240, symBinAddr: 0x81A0, symSize: 0x50 }
  - { offsetInCU: 0x1B3, offset: 0x6382, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV10Foundation13CustomNSErrorAadEP9errorCodeSivgTW', symObjAddr: 0x290, symBinAddr: 0x81F0, symSize: 0x10 }
  - { offsetInCU: 0x1E3, offset: 0x63B2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV10Foundation13CustomNSErrorAadEP13errorUserInfoSDySSypGvgTW', symObjAddr: 0x2A0, symBinAddr: 0x8200, symSize: 0x10 }
  - { offsetInCU: 0x213, offset: 0x63E2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVSHAASH9hashValueSivgTW', symObjAddr: 0x2B0, symBinAddr: 0x8210, symSize: 0x40 }
  - { offsetInCU: 0x2EE, offset: 0x64BD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0x2F0, symBinAddr: 0x8250, symSize: 0x20 }
  - { offsetInCU: 0x35C, offset: 0x652B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x3B0, symBinAddr: 0x8310, symSize: 0x30 }
  - { offsetInCU: 0x40D, offset: 0x65DC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceLoginErrorCodeOSYAASY8rawValuexSg03RawH0Qz_tcfCTW', symObjAddr: 0x490, symBinAddr: 0x83F0, symSize: 0x30 }
  - { offsetInCU: 0x43C, offset: 0x660B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceLoginErrorCodeOSYAASY8rawValue03RawH0QzvgTW', symObjAddr: 0x4C0, symBinAddr: 0x8420, symSize: 0x10 }
  - { offsetInCU: 0x458, offset: 0x6627, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceLoginErrorCodeO8rawValueACSgSi_tcfCTf4nd_n', symObjAddr: 0x4E0, symBinAddr: 0x8440, symSize: 0x30 }
  - { offsetInCU: 0x4B9, offset: 0x6688, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV10Foundation13CustomNSErrorAAs0E0PWb', symObjAddr: 0x510, symBinAddr: 0x8470, symSize: 0x10 }
  - { offsetInCU: 0x4CC, offset: 0x669B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVACs0E0AAWl', symObjAddr: 0x520, symBinAddr: 0x8480, symSize: 0x30 }
  - { offsetInCU: 0x4DF, offset: 0x66AE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVSHAASQWb', symObjAddr: 0x550, symBinAddr: 0x84B0, symSize: 0x10 }
  - { offsetInCU: 0x4F2, offset: 0x66C1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVACSQAAWl', symObjAddr: 0x560, symBinAddr: 0x84C0, symSize: 0x30 }
  - { offsetInCU: 0x505, offset: 0x66D4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceLoginErrorCodeOSHAASQWb', symObjAddr: 0x590, symBinAddr: 0x84F0, symSize: 0x10 }
  - { offsetInCU: 0x518, offset: 0x66E7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceLoginErrorCodeOACSQAAWl', symObjAddr: 0x5A0, symBinAddr: 0x8500, symSize: 0x30 }
  - { offsetInCU: 0x52B, offset: 0x66FA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVwxx', symObjAddr: 0x5E0, symBinAddr: 0x8540, symSize: 0x30 }
  - { offsetInCU: 0x53E, offset: 0x670D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVwca', symObjAddr: 0x660, symBinAddr: 0x85C0, symSize: 0x60 }
  - { offsetInCU: 0x551, offset: 0x6720, size: 0x8, addend: 0x0, symName: ___swift_memcpy24_8, symObjAddr: 0x6C0, symBinAddr: 0x8620, symSize: 0x20 }
  - { offsetInCU: 0x564, offset: 0x6733, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVwta', symObjAddr: 0x6E0, symBinAddr: 0x8640, symSize: 0x40 }
  - { offsetInCU: 0x577, offset: 0x6746, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVwet', symObjAddr: 0x720, symBinAddr: 0x8680, symSize: 0x50 }
  - { offsetInCU: 0x58A, offset: 0x6759, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVwst', symObjAddr: 0x770, symBinAddr: 0x86D0, symSize: 0x40 }
  - { offsetInCU: 0x59D, offset: 0x676C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVMa', symObjAddr: 0x7B0, symBinAddr: 0x8710, symSize: 0x10 }
  - { offsetInCU: 0x5B0, offset: 0x677F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceLoginErrorCodeOMa', symObjAddr: 0x7C0, symBinAddr: 0x8720, symSize: 0x10 }
  - { offsetInCU: 0x5C3, offset: 0x6792, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVAC10Foundation13CustomNSErrorAAWl', symObjAddr: 0x7D0, symBinAddr: 0x8730, symSize: 0x2E }
  - { offsetInCU: 0x657, offset: 0x6826, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0x310, symBinAddr: 0x8270, symSize: 0x40 }
  - { offsetInCU: 0x6EE, offset: 0x68BD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVs0E0AAsADP9_userInfoyXlSgvgTW', symObjAddr: 0x390, symBinAddr: 0x82F0, symSize: 0x10 }
  - { offsetInCU: 0x709, offset: 0x68D8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVs0E0AAsADP19_getEmbeddedNSErroryXlSgyFTW', symObjAddr: 0x3A0, symBinAddr: 0x8300, symSize: 0x10 }
  - { offsetInCU: 0x789, offset: 0x6958, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceLoginErrorCodeOSHAASH9hashValueSivgTW', symObjAddr: 0x3F0, symBinAddr: 0x8350, symSize: 0x40 }
  - { offsetInCU: 0x830, offset: 0x69FF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceLoginErrorCodeOSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0x430, symBinAddr: 0x8390, symSize: 0x20 }
  - { offsetInCU: 0x87F, offset: 0x6A4E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceLoginErrorCodeOSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0x450, symBinAddr: 0x83B0, symSize: 0x40 }
  - { offsetInCU: 0x904, offset: 0x6AD3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceLoginErrorCodeOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x4D0, symBinAddr: 0x8430, symSize: 0x10 }
  - { offsetInCU: 0x997, offset: 0x6B66, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVs0E0AAsADP7_domainSSvgTW', symObjAddr: 0x350, symBinAddr: 0x82B0, symSize: 0x20 }
  - { offsetInCU: 0x9B3, offset: 0x6B82, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVs0E0AAsADP5_codeSivgTW', symObjAddr: 0x370, symBinAddr: 0x82D0, symSize: 0x20 }
  - { offsetInCU: 0xA3B, offset: 0x6C0A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV03_nsE0So7NSErrorCSgvg', symObjAddr: 0x0, symBinAddr: 0x7F60, symSize: 0x20 }
  - { offsetInCU: 0xA4E, offset: 0x6C1D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV9errorCodeSivg', symObjAddr: 0x20, symBinAddr: 0x7F80, symSize: 0x10 }
  - { offsetInCU: 0xA61, offset: 0x6C30, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV13errorUserInfoSDySSypGvg', symObjAddr: 0x30, symBinAddr: 0x7F90, symSize: 0x10 }
  - { offsetInCU: 0xA7F, offset: 0x6C4E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV03_nsE0ACSo7NSErrorC_tcfC', symObjAddr: 0x40, symBinAddr: 0x7FA0, symSize: 0xB0 }
  - { offsetInCU: 0xAA2, offset: 0x6C71, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV_8userInfoAcA0cdE4CodeO_SDySSypGtcfC', symObjAddr: 0xF0, symBinAddr: 0x8050, symSize: 0x20 }
  - { offsetInCU: 0xAD2, offset: 0x6CA1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceLoginErrorCodeO8rawValueSivg', symObjAddr: 0x110, symBinAddr: 0x8070, symSize: 0x10 }
  - { offsetInCU: 0xAF7, offset: 0x6CC6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV11errorDomainSSvgZ', symObjAddr: 0x120, symBinAddr: 0x8080, symSize: 0x50 }
  - { offsetInCU: 0xB1E, offset: 0x6CED, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV16excessivePollingAA0cdE4CodeOvgZ', symObjAddr: 0x170, symBinAddr: 0x80D0, symSize: 0x10 }
  - { offsetInCU: 0xB3F, offset: 0x6D0E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV21authorizationDeclinedAA0cdE4CodeOvgZ', symObjAddr: 0x180, symBinAddr: 0x80E0, symSize: 0x10 }
  - { offsetInCU: 0xB60, offset: 0x6D2F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV20authorizationPendingAA0cdE4CodeOvgZ', symObjAddr: 0x190, symBinAddr: 0x80F0, symSize: 0x10 }
  - { offsetInCU: 0xB81, offset: 0x6D50, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV11codeExpiredAA0cdE4CodeOvgZ', symObjAddr: 0x1A0, symBinAddr: 0x8100, symSize: 0x10 }
  - { offsetInCU: 0xBA2, offset: 0x6D71, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV2eeoiySbAC_ACtFZ', symObjAddr: 0x1B0, symBinAddr: 0x8110, symSize: 0x30 }
  - { offsetInCU: 0xC11, offset: 0x6DE0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV4hash4intoys6HasherVz_tF', symObjAddr: 0x1E0, symBinAddr: 0x8140, symSize: 0x20 }
  - { offsetInCU: 0xC83, offset: 0x6E52, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV9hashValueSivg', symObjAddr: 0x200, symBinAddr: 0x8160, symSize: 0x40 }
  - { offsetInCU: 0xDBC, offset: 0x6F8B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceLoginErrorCodeO8rawValueACSgSi_tcfC', symObjAddr: 0x3E0, symBinAddr: 0x8340, symSize: 0x10 }
  - { offsetInCU: 0x4E, offset: 0x701E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC05loginE9Instances33_2E1868FF91A815585B124C0140A60DCBLLSayACGvpZ', symObjAddr: 0x7B30, symBinAddr: 0x5EC78, symSize: 0x0 }
  - { offsetInCU: 0x7C, offset: 0x704C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC8delegateAA0cdE8Delegate_pSgvgTo', symObjAddr: 0x20, symBinAddr: 0x8780, symSize: 0x40 }
  - { offsetInCU: 0xD0, offset: 0x70A0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC8delegateAA0cdE8Delegate_pSgvsTo', symObjAddr: 0xA0, symBinAddr: 0x8800, symSize: 0x40 }
  - { offsetInCU: 0x150, offset: 0x7120, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC11permissionsSaySSGvgTo', symObjAddr: 0x200, symBinAddr: 0x8960, symSize: 0x40 }
  - { offsetInCU: 0x19D, offset: 0x716D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC11redirectURL10Foundation0G0VSgvgTo', symObjAddr: 0x260, symBinAddr: 0x89C0, symSize: 0xB0 }
  - { offsetInCU: 0x1F1, offset: 0x71C1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC11redirectURL10Foundation0G0VSgvsTo', symObjAddr: 0x390, symBinAddr: 0x8AB0, symSize: 0xE0 }
  - { offsetInCU: 0x26B, offset: 0x723B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC8codeInfoAA0cd4CodeG0CSgvgTo', symObjAddr: 0x520, symBinAddr: 0x8C40, symSize: 0x40 }
  - { offsetInCU: 0x2BD, offset: 0x728D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC8codeInfoAA0cd4CodeG0CSgvsTo', symObjAddr: 0x5A0, symBinAddr: 0x8CC0, symSize: 0x60 }
  - { offsetInCU: 0x379, offset: 0x7349, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC11permissions011enableSmartD0ACSaySSG_SbtcfcTo', symObjAddr: 0xAD0, symBinAddr: 0x91F0, symSize: 0x30 }
  - { offsetInCU: 0x453, offset: 0x7423, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC5startyyFTo', symObjAddr: 0x26B0, symBinAddr: 0xADD0, symSize: 0x30 }
  - { offsetInCU: 0x483, offset: 0x7453, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC6cancelyyFTo', symObjAddr: 0x28B0, symBinAddr: 0xAFD0, symSize: 0x30 }
  - { offsetInCU: 0x4C2, offset: 0x7492, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC14notifyDelegate5token14expirationDate020dataAccessExpirationJ0ySSSg_10Foundation0J0VSgALtFTo', symObjAddr: 0x3D40, symBinAddr: 0xC460, symSize: 0x160 }
  - { offsetInCU: 0x4DD, offset: 0x74AD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC12processErroryys0G0_pFTo', symObjAddr: 0x3EA0, symBinAddr: 0xC5C0, symSize: 0x50 }
  - { offsetInCU: 0x4F8, offset: 0x74C8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC12schedulePoll8intervalySu_tFTo', symObjAddr: 0x5180, symBinAddr: 0xD8A0, symSize: 0x30 }
  - { offsetInCU: 0x53D, offset: 0x750D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerCACycfcTo', symObjAddr: 0x5200, symBinAddr: 0xD920, symSize: 0x30 }
  - { offsetInCU: 0x5CF, offset: 0x759F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerCAA17DependentAsObjectA2aDP22configuredDependencies0hJ0QzSgvsTW', symObjAddr: 0x5540, symBinAddr: 0xDC60, symSize: 0x60 }
  - { offsetInCU: 0x611, offset: 0x75E1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerCAA17DependentAsObjectA2aDP22configuredDependencies0hJ0QzSgvMTW', symObjAddr: 0x55A0, symBinAddr: 0xDCC0, symSize: 0x40 }
  - { offsetInCU: 0x64C, offset: 0x761C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC05loginE9Instances33_2E1868FF91A815585B124C0140A60DCBLL_WZ', symObjAddr: 0x0, symBinAddr: 0x8760, symSize: 0x20 }
  - { offsetInCU: 0x842, offset: 0x7812, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13DevicePolling_pWOb', symObjAddr: 0x790, symBinAddr: 0x8EB0, symSize: 0x20 }
  - { offsetInCU: 0xBED, offset: 0x7BBD, size: 0x8, addend: 0x0, symName: '_$sSo27FBSDKGraphRequestConnecting_pSgypSgs5Error_pSgIeggng_AByXlSgSo7NSErrorCSgIeyByyy_TR', symObjAddr: 0x25F0, symBinAddr: 0xAD10, symSize: 0xC0 }
  - { offsetInCU: 0xE3C, offset: 0x7E0C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerCfETo', symObjAddr: 0x5260, symBinAddr: 0xD980, symSize: 0x80 }
  - { offsetInCU: 0xE86, offset: 0x7E56, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC10netService_13didNotPublishySo05NSNetG0C_SDySSSo8NSNumberCGtFTo', symObjAddr: 0x52F0, symBinAddr: 0xDA10, symSize: 0x50 }
  - { offsetInCU: 0xEFE, offset: 0x7ECE, size: 0x8, addend: 0x0, symName: '_$sSaySSGMa', symObjAddr: 0x5780, symBinAddr: 0xDEA0, symSize: 0x30 }
  - { offsetInCU: 0xF3A, offset: 0x7F0A, size: 0x8, addend: 0x0, symName: '_$ss13_parseInteger5ascii5radixq_Sgx_SitSyRzs010FixedWidthB0R_r0_lFSS_SiTg5', symObjAddr: 0x57B0, symBinAddr: 0xDED0, symSize: 0x120 }
  - { offsetInCU: 0xF71, offset: 0x7F41, size: 0x8, addend: 0x0, symName: '_$ss13_parseInteger5ascii5radixq_Sgx_SitSyRzs010FixedWidthB0R_r0_lFADSRys5UInt8VGXEfU_SS_SiTg5', symObjAddr: 0x58D0, symBinAddr: 0xDFF0, symSize: 0x290 }
  - { offsetInCU: 0x10D1, offset: 0x80A1, size: 0x8, addend: 0x0, symName: '_$ss22_ContiguousArrayBufferV19_uninitializedCount15minimumCapacityAByxGSi_SitcfCs5UInt8V_Tgmq5', symObjAddr: 0x5EC0, symBinAddr: 0xE5E0, symSize: 0x70 }
  - { offsetInCU: 0x1245, offset: 0x8215, size: 0x8, addend: 0x0, symName: '_$sSS11withCStringyxxSPys4Int8VGKXEKlFSb_Tg5024$sSdySdSgxcSyRzlufcSbSpyf6GXEfU_j5SPys4C7VGXEfU_SpySdGTf1cn_n', symObjAddr: 0x6310, symBinAddr: 0xEA30, symSize: 0x110 }
  - { offsetInCU: 0x1381, offset: 0x8351, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC5startyyFySo27FBSDKGraphRequestConnecting_pSg_ypSgs5Error_pSgtcfU_TA', symObjAddr: 0x6460, symBinAddr: 0xEB50, symSize: 0x20 }
  - { offsetInCU: 0x1394, offset: 0x8364, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x6480, symBinAddr: 0xEB70, symSize: 0x20 }
  - { offsetInCU: 0x13A7, offset: 0x8377, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x64A0, symBinAddr: 0xEB90, symSize: 0x10 }
  - { offsetInCU: 0x13BA, offset: 0x838A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesVWOh', symObjAddr: 0x64B0, symBinAddr: 0xEBA0, symSize: 0x20 }
  - { offsetInCU: 0x13CD, offset: 0x839D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13DevicePolling_pWOc', symObjAddr: 0x6500, symBinAddr: 0xEBC0, symSize: 0x30 }
  - { offsetInCU: 0x13E0, offset: 0x83B0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC12schedulePoll8intervalySu_tFyycfU_TA', symObjAddr: 0x65C0, symBinAddr: 0xEC50, symSize: 0x20 }
  - { offsetInCU: 0x1542, offset: 0x8512, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC14notifyDelegate5token14expirationDate020dataAccessExpirationJ0ySSSg_10Foundation0J0VSgALtFyAA0cdE6ResultCcfU_TA', symObjAddr: 0x6F70, symBinAddr: 0xF600, symSize: 0x10 }
  - { offsetInCU: 0x156A, offset: 0x853A, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DateVSgWOb', symObjAddr: 0x70B0, symBinAddr: 0xF740, symSize: 0x40 }
  - { offsetInCU: 0x157D, offset: 0x854D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC14notifyDelegate5token14expirationDate020dataAccessExpirationJ0ySSSg_10Foundation0J0VSgALtFySo27FBSDKGraphRequestConnecting_pSg_ypSgs5Error_pSgtcfU0_TA', symObjAddr: 0x70F0, symBinAddr: 0xF780, symSize: 0xA0 }
  - { offsetInCU: 0x15AC, offset: 0x857C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerCMU', symObjAddr: 0x7340, symBinAddr: 0xF9D0, symSize: 0x10 }
  - { offsetInCU: 0x15BF, offset: 0x858F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerCMa', symObjAddr: 0x7350, symBinAddr: 0xF9E0, symSize: 0x30 }
  - { offsetInCU: 0x15D2, offset: 0x85A2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerCMr', symObjAddr: 0x7380, symBinAddr: 0xFA10, symSize: 0xA0 }
  - { offsetInCU: 0x15E5, offset: 0x85B5, size: 0x8, addend: 0x0, symName: '_$s10Foundation3URLVSgMa', symObjAddr: 0x7450, symBinAddr: 0xFAE0, symSize: 0x50 }
  - { offsetInCU: 0x15F8, offset: 0x85C8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesVwCP', symObjAddr: 0x74A0, symBinAddr: 0xFB30, symSize: 0x30 }
  - { offsetInCU: 0x160B, offset: 0x85DB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesVwxx', symObjAddr: 0x74D0, symBinAddr: 0xFB60, symSize: 0x40 }
  - { offsetInCU: 0x161E, offset: 0x85EE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesVwcp', symObjAddr: 0x7510, symBinAddr: 0xFBA0, symSize: 0x70 }
  - { offsetInCU: 0x1631, offset: 0x8601, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesVwca', symObjAddr: 0x7580, symBinAddr: 0xFC10, symSize: 0x90 }
  - { offsetInCU: 0x1644, offset: 0x8614, size: 0x8, addend: 0x0, symName: ___swift_assign_boxed_opaque_existential_1, symObjAddr: 0x7610, symBinAddr: 0xFCA0, symSize: 0x130 }
  - { offsetInCU: 0x1657, offset: 0x8627, size: 0x8, addend: 0x0, symName: ___swift_memcpy72_8, symObjAddr: 0x7740, symBinAddr: 0xFDD0, symSize: 0x30 }
  - { offsetInCU: 0x166A, offset: 0x863A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesVwta', symObjAddr: 0x7770, symBinAddr: 0xFE00, symSize: 0x80 }
  - { offsetInCU: 0x167D, offset: 0x864D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesVwet', symObjAddr: 0x77F0, symBinAddr: 0xFE80, symSize: 0x50 }
  - { offsetInCU: 0x1690, offset: 0x8660, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesVwst', symObjAddr: 0x7840, symBinAddr: 0xFED0, symSize: 0x50 }
  - { offsetInCU: 0x16A3, offset: 0x8673, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesVMa', symObjAddr: 0x7890, symBinAddr: 0xFF20, symSize: 0x10 }
  - { offsetInCU: 0x16B6, offset: 0x8686, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26DeviceLoginManagerDelegate_pSgXwWOh', symObjAddr: 0x78A0, symBinAddr: 0xFF30, symSize: 0x20 }
  - { offsetInCU: 0x16C9, offset: 0x8699, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC12schedulePoll8intervalySu_tFyycfU_ySo27FBSDKGraphRequestConnecting_pSg_ypSgs5Error_pSgtcfU_TA', symObjAddr: 0x79B0, symBinAddr: 0x10020, symSize: 0x20 }
  - { offsetInCU: 0x16DC, offset: 0x86AC, size: 0x8, addend: 0x0, symName: '_$sSdySdSgxcSyRzlufcSbSpySdGXEfU_SbSPys4Int8VGXEfU_TA', symObjAddr: 0x7A00, symBinAddr: 0x10070, symSize: 0x50 }
  - { offsetInCU: 0x19BC, offset: 0x898C, size: 0x8, addend: 0x0, symName: '_$ss17FixedWidthIntegerPsEyxSgSScfCSi_Tgm5', symObjAddr: 0x4DF0, symBinAddr: 0xD510, symSize: 0x390 }
  - { offsetInCU: 0x2093, offset: 0x9063, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC8delegateAA0cdE8Delegate_pSgvg', symObjAddr: 0x60, symBinAddr: 0x87C0, symSize: 0x40 }
  - { offsetInCU: 0x20D8, offset: 0x90A8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC8delegateAA0cdE8Delegate_pSgvs', symObjAddr: 0xE0, symBinAddr: 0x8840, symSize: 0x50 }
  - { offsetInCU: 0x20FE, offset: 0x90CE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC8delegateAA0cdE8Delegate_pSgvM', symObjAddr: 0x130, symBinAddr: 0x8890, symSize: 0x70 }
  - { offsetInCU: 0x211F, offset: 0x90EF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC8delegateAA0cdE8Delegate_pSgvM.resume.0', symObjAddr: 0x1A0, symBinAddr: 0x8900, symSize: 0x60 }
  - { offsetInCU: 0x2150, offset: 0x9120, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC11permissionsSaySSGvg', symObjAddr: 0x240, symBinAddr: 0x89A0, symSize: 0x20 }
  - { offsetInCU: 0x217D, offset: 0x914D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC11redirectURL10Foundation0G0VSgvg', symObjAddr: 0x310, symBinAddr: 0x8A70, symSize: 0x40 }
  - { offsetInCU: 0x21BC, offset: 0x918C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC11redirectURL10Foundation0G0VSgvs', symObjAddr: 0x470, symBinAddr: 0x8B90, symSize: 0x60 }
  - { offsetInCU: 0x21E2, offset: 0x91B2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC11redirectURL10Foundation0G0VSgvM', symObjAddr: 0x4D0, symBinAddr: 0x8BF0, symSize: 0x40 }
  - { offsetInCU: 0x2205, offset: 0x91D5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC11redirectURL10Foundation0G0VSgvM.resume.0', symObjAddr: 0x510, symBinAddr: 0x8C30, symSize: 0x10 }
  - { offsetInCU: 0x2236, offset: 0x9206, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC8codeInfoAA0cd4CodeG0CSgvg', symObjAddr: 0x560, symBinAddr: 0x8C80, symSize: 0x40 }
  - { offsetInCU: 0x2273, offset: 0x9243, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC8codeInfoAA0cd4CodeG0CSgvs', symObjAddr: 0x600, symBinAddr: 0x8D20, symSize: 0x50 }
  - { offsetInCU: 0x2297, offset: 0x9267, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC8codeInfoAA0cd4CodeG0CSgvM', symObjAddr: 0x650, symBinAddr: 0x8D70, symSize: 0x40 }
  - { offsetInCU: 0x22BA, offset: 0x928A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC22configuredDependenciesAC06ObjectG0VSgvM', symObjAddr: 0x700, symBinAddr: 0x8E20, symSize: 0x40 }
  - { offsetInCU: 0x22DD, offset: 0x92AD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV12devicePoller12errorFactory012graphRequestK015internalUtility8settingsAeA0C7Polling_p_So18FBSDKErrorCreating_pSo010FBSDKGraphmK0_pSo013FBSDKInternalO0_p09FBSDKCoreB016SettingsProtocol_ptcfC', symObjAddr: 0x740, symBinAddr: 0x8E60, symSize: 0x50 }
  - { offsetInCU: 0x22F0, offset: 0x92C0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC19defaultDependenciesAC06ObjectG0VSgvM', symObjAddr: 0x880, symBinAddr: 0x8FA0, symSize: 0x40 }
  - { offsetInCU: 0x2319, offset: 0x92E9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC11permissions011enableSmartD0ACSaySSG_SbtcfC', symObjAddr: 0x8C0, symBinAddr: 0x8FE0, symSize: 0x30 }
  - { offsetInCU: 0x2350, offset: 0x9320, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC11permissions011enableSmartD0ACSaySSG_Sbtcfc', symObjAddr: 0x8F0, symBinAddr: 0x9010, symSize: 0x1E0 }
  - { offsetInCU: 0x24A2, offset: 0x9472, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC5startyyF', symObjAddr: 0xB00, symBinAddr: 0x9220, symSize: 0x690 }
  - { offsetInCU: 0x2705, offset: 0x96D5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC5startyyFySo27FBSDKGraphRequestConnecting_pSg_ypSgs5Error_pSgtcfU_', symObjAddr: 0x1190, symBinAddr: 0x98B0, symSize: 0xCF0 }
  - { offsetInCU: 0x2B7D, offset: 0x9B4D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC12processErroryys0G0_pF', symObjAddr: 0x1E80, symBinAddr: 0xA5A0, symSize: 0x350 }
  - { offsetInCU: 0x2CE2, offset: 0x9CB2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC14notifyDelegate33_2E1868FF91A815585B124C0140A60DCBLL5errorys5Error_p_tF', symObjAddr: 0x21D0, symBinAddr: 0xA8F0, symSize: 0x220 }
  - { offsetInCU: 0x2E6B, offset: 0x9E3B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC12schedulePoll8intervalySu_tF', symObjAddr: 0x23F0, symBinAddr: 0xAB10, symSize: 0x200 }
  - { offsetInCU: 0x2F29, offset: 0x9EF9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC12schedulePoll8intervalySu_tFyycfU_', symObjAddr: 0x3EF0, symBinAddr: 0xC610, symSize: 0x3B0 }
  - { offsetInCU: 0x308D, offset: 0xA05D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC12schedulePoll8intervalySu_tFyycfU_ySo27FBSDKGraphRequestConnecting_pSg_ypSgs5Error_pSgtcfU_', symObjAddr: 0x42A0, symBinAddr: 0xC9C0, symSize: 0xB50 }
  - { offsetInCU: 0x34B7, offset: 0xA487, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC6cancelyyF', symObjAddr: 0x26E0, symBinAddr: 0xAE00, symSize: 0x1D0 }
  - { offsetInCU: 0x3674, offset: 0xA644, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC14notifyDelegate5token14expirationDate020dataAccessExpirationJ0ySSSg_10Foundation0J0VSgALtF', symObjAddr: 0x28E0, symBinAddr: 0xB000, symSize: 0x830 }
  - { offsetInCU: 0x3A9B, offset: 0xAA6B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC14notifyDelegate5token14expirationDate020dataAccessExpirationJ0ySSSg_10Foundation0J0VSgALtFySo27FBSDKGraphRequestConnecting_pSg_ypSgs5Error_pSgtcfU0_', symObjAddr: 0x3110, symBinAddr: 0xB830, symSize: 0xC30 }
  - { offsetInCU: 0x3FFC, offset: 0xAFCC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC14notifyDelegate5token14expirationDate020dataAccessExpirationJ0ySSSg_10Foundation0J0VSgALtFyAA0cdE6ResultCcfU_Tf4nnd_n', symObjAddr: 0x68F0, symBinAddr: 0xEF80, symSize: 0x150 }
  - { offsetInCU: 0x41EF, offset: 0xB1BF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerCACycfC', symObjAddr: 0x51B0, symBinAddr: 0xD8D0, symSize: 0x20 }
  - { offsetInCU: 0x4202, offset: 0xB1D2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerCACycfc', symObjAddr: 0x51D0, symBinAddr: 0xD8F0, symSize: 0x30 }
  - { offsetInCU: 0x4256, offset: 0xB226, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerCfD', symObjAddr: 0x5230, symBinAddr: 0xD950, symSize: 0x30 }
  - { offsetInCU: 0x4277, offset: 0xB247, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC10netService_13didNotPublishySo05NSNetG0C_SDySSSo8NSNumberCGtF', symObjAddr: 0x52E0, symBinAddr: 0xDA00, symSize: 0x10 }
  - { offsetInCU: 0x4290, offset: 0xB260, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV12devicePollerAA0C7Polling_pvg', symObjAddr: 0x5340, symBinAddr: 0xDA60, symSize: 0x10 }
  - { offsetInCU: 0x42A3, offset: 0xB273, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV12devicePollerAA0C7Polling_pvs', symObjAddr: 0x5350, symBinAddr: 0xDA70, symSize: 0x30 }
  - { offsetInCU: 0x42B6, offset: 0xB286, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV12devicePollerAA0C7Polling_pvM', symObjAddr: 0x5380, symBinAddr: 0xDAA0, symSize: 0x10 }
  - { offsetInCU: 0x42C9, offset: 0xB299, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV12devicePollerAA0C7Polling_pvM.resume.0', symObjAddr: 0x5390, symBinAddr: 0xDAB0, symSize: 0x10 }
  - { offsetInCU: 0x42DC, offset: 0xB2AC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV12errorFactorySo18FBSDKErrorCreating_pvg', symObjAddr: 0x53A0, symBinAddr: 0xDAC0, symSize: 0x10 }
  - { offsetInCU: 0x42EF, offset: 0xB2BF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV12errorFactorySo18FBSDKErrorCreating_pvs', symObjAddr: 0x53B0, symBinAddr: 0xDAD0, symSize: 0x20 }
  - { offsetInCU: 0x4302, offset: 0xB2D2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV12errorFactorySo18FBSDKErrorCreating_pvM', symObjAddr: 0x53D0, symBinAddr: 0xDAF0, symSize: 0x20 }
  - { offsetInCU: 0x4315, offset: 0xB2E5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV12errorFactorySo18FBSDKErrorCreating_pvM.resume.0', symObjAddr: 0x53F0, symBinAddr: 0xDB10, symSize: 0x10 }
  - { offsetInCU: 0x4328, offset: 0xB2F8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV19graphRequestFactorySo010FBSDKGraphiJ0_pvg', symObjAddr: 0x5400, symBinAddr: 0xDB20, symSize: 0x10 }
  - { offsetInCU: 0x433B, offset: 0xB30B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV19graphRequestFactorySo010FBSDKGraphiJ0_pvs', symObjAddr: 0x5410, symBinAddr: 0xDB30, symSize: 0x20 }
  - { offsetInCU: 0x434E, offset: 0xB31E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV19graphRequestFactorySo010FBSDKGraphiJ0_pvM', symObjAddr: 0x5430, symBinAddr: 0xDB50, symSize: 0x20 }
  - { offsetInCU: 0x4361, offset: 0xB331, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV19graphRequestFactorySo010FBSDKGraphiJ0_pvM.resume.0', symObjAddr: 0x5450, symBinAddr: 0xDB70, symSize: 0x10 }
  - { offsetInCU: 0x4374, offset: 0xB344, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV15internalUtilitySo013FBSDKInternalI0_pvg', symObjAddr: 0x5460, symBinAddr: 0xDB80, symSize: 0x10 }
  - { offsetInCU: 0x4387, offset: 0xB357, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV15internalUtilitySo013FBSDKInternalI0_pvs', symObjAddr: 0x5470, symBinAddr: 0xDB90, symSize: 0x20 }
  - { offsetInCU: 0x439A, offset: 0xB36A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV15internalUtilitySo013FBSDKInternalI0_pvM', symObjAddr: 0x5490, symBinAddr: 0xDBB0, symSize: 0x20 }
  - { offsetInCU: 0x43AD, offset: 0xB37D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV15internalUtilitySo013FBSDKInternalI0_pvM.resume.0', symObjAddr: 0x54B0, symBinAddr: 0xDBD0, symSize: 0x10 }
  - { offsetInCU: 0x43C0, offset: 0xB390, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV8settings09FBSDKCoreB016SettingsProtocol_pvg', symObjAddr: 0x54C0, symBinAddr: 0xDBE0, symSize: 0x10 }
  - { offsetInCU: 0x43D3, offset: 0xB3A3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV8settings09FBSDKCoreB016SettingsProtocol_pvs', symObjAddr: 0x54D0, symBinAddr: 0xDBF0, symSize: 0x20 }
  - { offsetInCU: 0x43E6, offset: 0xB3B6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV8settings09FBSDKCoreB016SettingsProtocol_pvM', symObjAddr: 0x54F0, symBinAddr: 0xDC10, symSize: 0x20 }
  - { offsetInCU: 0x43F9, offset: 0xB3C9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV8settings09FBSDKCoreB016SettingsProtocol_pvM.resume.0', symObjAddr: 0x5510, symBinAddr: 0xDC30, symSize: 0x10 }
  - { offsetInCU: 0x444C, offset: 0xB41C, size: 0x8, addend: 0x0, symName: '_$sSa20_reserveCapacityImpl07minimumB013growForAppendySi_SbtF13FBSDKLoginKit18DeviceLoginManagerC_Tg5', symObjAddr: 0x5640, symBinAddr: 0xDD60, symSize: 0xB0 }
  - { offsetInCU: 0x44EC, offset: 0xB4BC, size: 0x8, addend: 0x0, symName: '_$sSa034_makeUniqueAndReserveCapacityIfNotB0yyF13FBSDKLoginKit18DeviceLoginManagerC_Tg5', symObjAddr: 0x56F0, symBinAddr: 0xDE10, symSize: 0x90 }
  - { offsetInCU: 0x457C, offset: 0xB54C, size: 0x8, addend: 0x0, symName: '_$sSS8_copyingyS2SFZ', symObjAddr: 0x5B60, symBinAddr: 0xE280, symSize: 0x80 }
  - { offsetInCU: 0x4590, offset: 0xB560, size: 0x8, addend: 0x0, symName: '_$sSlsEy11SubSequenceQzqd__cSXRd__5BoundQyd__5IndexRtzluigSS_s16PartialRangeFromVySSAEVGTgq5', symObjAddr: 0x5BE0, symBinAddr: 0xE300, symSize: 0x60 }
  - { offsetInCU: 0x45BD, offset: 0xB58D, size: 0x8, addend: 0x0, symName: '_$sSS8_copyingySSSsFZ', symObjAddr: 0x5C40, symBinAddr: 0xE360, symSize: 0x180 }
  - { offsetInCU: 0x4614, offset: 0xB5E4, size: 0x8, addend: 0x0, symName: '_$sSlsE5countSivgSs8UTF8ViewV_Tgq5', symObjAddr: 0x5DC0, symBinAddr: 0xE4E0, symSize: 0x100 }
  - { offsetInCU: 0x463F, offset: 0xB60F, size: 0x8, addend: 0x0, symName: '_$sSTsE21_copySequenceContents12initializing8IteratorQz_SitSry7ElementQzG_tFSs8UTF8ViewV_Tgq5', symObjAddr: 0x5F30, symBinAddr: 0xE650, symSize: 0x2D0 }
  - { offsetInCU: 0x4698, offset: 0xB668, size: 0x8, addend: 0x0, symName: '_$ss11_StringGutsV27_slowEnsureMatchingEncodingySS5IndexVAEF', symObjAddr: 0x6200, symBinAddr: 0xE920, symSize: 0xA0 }
  - { offsetInCU: 0x46AC, offset: 0xB67C, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV20_consumeAndCreateNewAByxGyF13FBSDKLoginKit18DeviceLoginManagerC_Tg5', symObjAddr: 0x62A0, symBinAddr: 0xE9C0, symSize: 0x70 }
  - { offsetInCU: 0x4745, offset: 0xB715, size: 0x8, addend: 0x0, symName: '_$ss20_ArrayBufferProtocolPsE15replaceSubrange_4with10elementsOfySnySiG_Siqd__ntSlRd__7ElementQyd__AGRtzlFs01_aB0Vy13FBSDKLoginKit18DeviceLoginManagerCG_s15EmptyCollectionVyANGTg5Tf4nndn_n', symObjAddr: 0x65E0, symBinAddr: 0xEC70, symSize: 0x1F0 }
  - { offsetInCU: 0x49F3, offset: 0xB9C3, size: 0x8, addend: 0x0, symName: '_$sSa15replaceSubrange_4withySnySiG_qd__nt7ElementQyd__RszSlRd__lF13FBSDKLoginKit18DeviceLoginManagerC_s15EmptyCollectionVyAHGTg5Tf4ndn_n', symObjAddr: 0x67D0, symBinAddr: 0xEE60, symSize: 0x120 }
  - { offsetInCU: 0x4AEB, offset: 0xBABB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC10netService_13didNotPublishySo05NSNetG0C_SDySSSo8NSNumberCGtFTf4ndn_n', symObjAddr: 0x7190, symBinAddr: 0xF820, symSize: 0x130 }
  - { offsetInCU: 0x27, offset: 0xBC4A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultC5token11isCancelledACSo16FBSDKAccessTokenCSg_SbtcfC', symObjAddr: 0x0, symBinAddr: 0x10140, symSize: 0xD0 }
  - { offsetInCU: 0x9B, offset: 0xBCBE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultC11accessTokenSo011FBSDKAccessH0CSgvgTo', symObjAddr: 0xD0, symBinAddr: 0x10210, symSize: 0x40 }
  - { offsetInCU: 0xED, offset: 0xBD10, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultC11accessTokenSo011FBSDKAccessH0CSgvsTo', symObjAddr: 0x150, symBinAddr: 0x10290, symSize: 0x60 }
  - { offsetInCU: 0x141, offset: 0xBD64, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultC11isCancelledSbvgTo', symObjAddr: 0x1B0, symBinAddr: 0x102F0, symSize: 0x40 }
  - { offsetInCU: 0x193, offset: 0xBDB6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultC11isCancelledSbvsTo', symObjAddr: 0x220, symBinAddr: 0x10360, symSize: 0x40 }
  - { offsetInCU: 0x1D7, offset: 0xBDFA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultC5token11isCancelledACSo16FBSDKAccessTokenCSg_SbtcfcTo', symObjAddr: 0x330, symBinAddr: 0x10470, symSize: 0xD0 }
  - { offsetInCU: 0x251, offset: 0xBE74, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultCACycfcTo', symObjAddr: 0x450, symBinAddr: 0x10590, symSize: 0x30 }
  - { offsetInCU: 0x2DE, offset: 0xBF01, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultCfETo', symObjAddr: 0x4B0, symBinAddr: 0x105F0, symSize: 0x20 }
  - { offsetInCU: 0x30B, offset: 0xBF2E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultCMa', symObjAddr: 0x4D0, symBinAddr: 0x10610, symSize: 0x20 }
  - { offsetInCU: 0x41D, offset: 0xC040, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultC5token11isCancelledACSo16FBSDKAccessTokenCSg_SbtcfC', symObjAddr: 0x0, symBinAddr: 0x10140, symSize: 0xD0 }
  - { offsetInCU: 0x46B, offset: 0xC08E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultC11accessTokenSo011FBSDKAccessH0CSgvg', symObjAddr: 0x110, symBinAddr: 0x10250, symSize: 0x40 }
  - { offsetInCU: 0x4BB, offset: 0xC0DE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultC11isCancelledSbvg', symObjAddr: 0x1F0, symBinAddr: 0x10330, symSize: 0x30 }
  - { offsetInCU: 0x511, offset: 0xC134, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultC5token11isCancelledACSo16FBSDKAccessTokenCSg_Sbtcfc', symObjAddr: 0x260, symBinAddr: 0x103A0, symSize: 0xD0 }
  - { offsetInCU: 0x540, offset: 0xC163, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultCACycfC', symObjAddr: 0x400, symBinAddr: 0x10540, symSize: 0x20 }
  - { offsetInCU: 0x553, offset: 0xC176, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultCACycfc', symObjAddr: 0x420, symBinAddr: 0x10560, symSize: 0x30 }
  - { offsetInCU: 0x5A7, offset: 0xC1CA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultCfD', symObjAddr: 0x480, symBinAddr: 0x105C0, symSize: 0x30 }
  - { offsetInCU: 0x75, offset: 0xC260, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12DevicePollerVAA0C7PollingA2aDP8schedule8interval5blockySu_yyctFTW', symObjAddr: 0x20, symBinAddr: 0x10680, symSize: 0x10 }
  - { offsetInCU: 0xA5, offset: 0xC290, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12DevicePollerV8schedule8interval5blockySu_yyctFTf4nnd_n', symObjAddr: 0x30, symBinAddr: 0x10690, symSize: 0x2C0 }
  - { offsetInCU: 0x15D, offset: 0xC348, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12DevicePollerVMa', symObjAddr: 0x2F0, symBinAddr: 0x10950, symSize: 0x10 }
  - { offsetInCU: 0x170, offset: 0xC35B, size: 0x8, addend: 0x0, symName: '_$sSo17OS_dispatch_queueCMa', symObjAddr: 0x300, symBinAddr: 0x10960, symSize: 0x30 }
  - { offsetInCU: 0x183, offset: 0xC36E, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x330, symBinAddr: 0x10990, symSize: 0x20 }
  - { offsetInCU: 0x196, offset: 0xC381, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x350, symBinAddr: 0x109B0, symSize: 0x10 }
  - { offsetInCU: 0x1A9, offset: 0xC394, size: 0x8, addend: 0x0, symName: '_$sSay8Dispatch0A13WorkItemFlagsVGMa', symObjAddr: 0x3D0, symBinAddr: 0x109F0, symSize: 0x50 }
  - { offsetInCU: 0x265, offset: 0xC450, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12DevicePollerVACycfC', symObjAddr: 0x0, symBinAddr: 0x10660, symSize: 0x10 }
  - { offsetInCU: 0x289, offset: 0xC474, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12DevicePollerV8schedule8interval5blockySu_yyctF', symObjAddr: 0x10, symBinAddr: 0x10670, symSize: 0x10 }
  - { offsetInCU: 0x2B, offset: 0xC507, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperO03getC4InfoSSyFZ', symObjAddr: 0x0, symBinAddr: 0x10A50, symSize: 0x10 }
  - { offsetInCU: 0x4D, offset: 0xC529, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperO25mdnsAdvertisementServicesSo10NSMapTableCySo20NSNetServiceDelegate_pyXlGvpZ', symObjAddr: 0x51E0, symBinAddr: 0x61230, symSize: 0x0 }
  - { offsetInCU: 0x67, offset: 0xC543, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperO16NetServiceValues33_D51BDA1F8F5A5E4604761D8AFA9F40BBLLO10sdkVersionSSvpZ', symObjAddr: 0xA70, symBinAddr: 0x5EDC0, symSize: 0x0 }
  - { offsetInCU: 0x11C, offset: 0xC5F8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperO16NetServiceValues33_D51BDA1F8F5A5E4604761D8AFA9F40BBLLO10sdkVersion_WZ', symObjAddr: 0x150, symBinAddr: 0x10BA0, symSize: 0x150 }
  - { offsetInCU: 0x168, offset: 0xC644, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperO27cleanUpAdvertisementService3forySo05NSNetI8Delegate_p_tFZTf4en_n', symObjAddr: 0x2E0, symBinAddr: 0x10D30, symSize: 0xE0 }
  - { offsetInCU: 0x1E2, offset: 0xC6BE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperO25startAdvertisementService9loginCode8delegateSbSS_So05NSNetH8Delegate_ptFZTf4nen_n', symObjAddr: 0x3C0, symBinAddr: 0x10E10, symSize: 0x230 }
  - { offsetInCU: 0x370, offset: 0xC84C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperO10isDelegate_23forAdvertisementServiceSbSo05NSNetjG0_p_So0kJ0CtFZTf4enn_n', symObjAddr: 0x5F0, symBinAddr: 0x11040, symSize: 0xC0 }
  - { offsetInCU: 0x3EC, offset: 0xC8C8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperO03getC4InfoSSyFZTf4d_n', symObjAddr: 0x6B0, symBinAddr: 0x11100, symSize: 0x2C0 }
  - { offsetInCU: 0x618, offset: 0xCAF4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperO25mdnsAdvertisementServices_WZ', symObjAddr: 0xA0, symBinAddr: 0x10AF0, symSize: 0x30 }
  - { offsetInCU: 0x631, offset: 0xCB0D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperO25mdnsAdvertisementServicesSo10NSMapTableCySo20NSNetServiceDelegate_pyXlGvau', symObjAddr: 0xD0, symBinAddr: 0x10B20, symSize: 0x30 }
  - { offsetInCU: 0x6E1, offset: 0xCBBD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperOMa', symObjAddr: 0x970, symBinAddr: 0x113C0, symSize: 0x10 }
  - { offsetInCU: 0x6F4, offset: 0xCBD0, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataVACSTAAWl', symObjAddr: 0x9B0, symBinAddr: 0x113D0, symSize: 0x30 }
  - { offsetInCU: 0x896, offset: 0xCD72, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperO03getC4InfoSSyFZ', symObjAddr: 0x0, symBinAddr: 0x10A50, symSize: 0x10 }
  - { offsetInCU: 0x8A9, offset: 0xCD85, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperO25startAdvertisementService9loginCode8delegateSbSS_So05NSNetH8Delegate_ptFZ', symObjAddr: 0x10, symBinAddr: 0x10A60, symSize: 0x40 }
  - { offsetInCU: 0x8C2, offset: 0xCD9E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperO27cleanUpAdvertisementService3forySo05NSNetI8Delegate_p_tFZ', symObjAddr: 0x50, symBinAddr: 0x10AA0, symSize: 0x20 }
  - { offsetInCU: 0x8D5, offset: 0xCDB1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperO10isDelegate_23forAdvertisementServiceSbSo05NSNetjG0_p_So0kJ0CtFZ', symObjAddr: 0x70, symBinAddr: 0x10AC0, symSize: 0x30 }
  - { offsetInCU: 0x8EE, offset: 0xCDCA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperO25mdnsAdvertisementServicesSo10NSMapTableCySo20NSNetServiceDelegate_pyXlGvgZ', symObjAddr: 0x100, symBinAddr: 0x10B50, symSize: 0x50 }
  - { offsetInCU: 0x91E, offset: 0xCDFA, size: 0x8, addend: 0x0, symName: '_$sSlsE5first7ElementQzSgvgSS_Tg5', symObjAddr: 0x2A0, symBinAddr: 0x10CF0, symSize: 0x40 }
  - { offsetInCU: 0x62, offset: 0xCF5B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15defaultAudienceAA07DefaultF0OvgTo', symObjAddr: 0x0, symBinAddr: 0x11400, symSize: 0x90 }
  - { offsetInCU: 0xAF, offset: 0xCFA8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15defaultAudienceAA07DefaultF0OvsTo', symObjAddr: 0x140, symBinAddr: 0x11510, symSize: 0xA0 }
  - { offsetInCU: 0x129, offset: 0xD022, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC8delegateAA05LoginD8Delegate_pSgvgTo', symObjAddr: 0x4A0, symBinAddr: 0x11870, symSize: 0x40 }
  - { offsetInCU: 0x17D, offset: 0xD076, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC8delegateAA05LoginD8Delegate_pSgvsTo', symObjAddr: 0x520, symBinAddr: 0x118F0, symSize: 0x40 }
  - { offsetInCU: 0x1FD, offset: 0xD0F6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC11permissionsSaySSGvgTo', symObjAddr: 0x680, symBinAddr: 0x11A50, symSize: 0x60 }
  - { offsetInCU: 0x24F, offset: 0xD148, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC11permissionsSaySSGvsTo', symObjAddr: 0x700, symBinAddr: 0x11AD0, symSize: 0x60 }
  - { offsetInCU: 0x303, offset: 0xD1FC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15TooltipBehaviorOSYAASY8rawValuexSg03RawH0Qz_tcfCTW', symObjAddr: 0x1C70, symBinAddr: 0x12FD0, symSize: 0x20 }
  - { offsetInCU: 0x332, offset: 0xD22B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15TooltipBehaviorOSYAASY8rawValue03RawH0QzvgTW', symObjAddr: 0x1C90, symBinAddr: 0x12FF0, symSize: 0x10 }
  - { offsetInCU: 0x34F, offset: 0xD248, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15tooltipBehaviorAC07TooltipF0OvgTo', symObjAddr: 0x7D0, symBinAddr: 0x11BA0, symSize: 0x40 }
  - { offsetInCU: 0x3A2, offset: 0xD29B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15tooltipBehaviorAC07TooltipF0OvsTo', symObjAddr: 0x840, symBinAddr: 0x11C10, symSize: 0x40 }
  - { offsetInCU: 0x40B, offset: 0xD304, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC17tooltipColorStyleAA13FBTooltipViewC0fG0OvgTo', symObjAddr: 0x900, symBinAddr: 0x11CD0, symSize: 0x40 }
  - { offsetInCU: 0x45D, offset: 0xD356, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC17tooltipColorStyleAA13FBTooltipViewC0fG0OvsTo', symObjAddr: 0x970, symBinAddr: 0x11D40, symSize: 0x40 }
  - { offsetInCU: 0x4C6, offset: 0xD3BF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC13loginTrackingAA05LoginF0OvgTo', symObjAddr: 0xA30, symBinAddr: 0x11E00, symSize: 0x40 }
  - { offsetInCU: 0x518, offset: 0xD411, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC13loginTrackingAA05LoginF0OvsTo', symObjAddr: 0xAA0, symBinAddr: 0x11E70, symSize: 0x40 }
  - { offsetInCU: 0x581, offset: 0xD47A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5nonceSSSgvgTo', symObjAddr: 0xB60, symBinAddr: 0x11F30, symSize: 0x50 }
  - { offsetInCU: 0x5B1, offset: 0xD4AA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5nonceSSSgvsTo', symObjAddr: 0xBE0, symBinAddr: 0x11FB0, symSize: 0x60 }
  - { offsetInCU: 0x63A, offset: 0xD533, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC8authTypeSo0a4AuthF0aSgvgTo', symObjAddr: 0x1040, symBinAddr: 0x123A0, symSize: 0x40 }
  - { offsetInCU: 0x68C, offset: 0xD585, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC8authTypeSo0a4AuthF0aSgvsTo', symObjAddr: 0x10C0, symBinAddr: 0x12420, symSize: 0x60 }
  - { offsetInCU: 0x6F5, offset: 0xD5EE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC12codeVerifierAA04CodeF0CvgTo', symObjAddr: 0x11B0, symBinAddr: 0x12510, symSize: 0x40 }
  - { offsetInCU: 0x748, offset: 0xD641, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC12codeVerifierAA04CodeF0CvsTo', symObjAddr: 0x1230, symBinAddr: 0x12590, symSize: 0x60 }
  - { offsetInCU: 0x81A, offset: 0xD713, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC19graphRequestFactorySo010FBSDKGraphfG0_pvgTo', symObjAddr: 0x18C0, symBinAddr: 0x12C20, symSize: 0x50 }
  - { offsetInCU: 0x86E, offset: 0xD767, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC19graphRequestFactorySo010FBSDKGraphfG0_pvsTo', symObjAddr: 0x1970, symBinAddr: 0x12CD0, symSize: 0x60 }
  - { offsetInCU: 0x8D7, offset: 0xD7D0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15isAuthenticatedSbvgTo', symObjAddr: 0x1A80, symBinAddr: 0x12DE0, symSize: 0xA0 }
  - { offsetInCU: 0x997, offset: 0xD890, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5frameACSo6CGRectV_tcfcTo', symObjAddr: 0x2400, symBinAddr: 0x13760, symSize: 0x30 }
  - { offsetInCU: 0x9E4, offset: 0xD8DD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5coderACSgSo7NSCoderC_tcfcTo', symObjAddr: 0x24A0, symBinAddr: 0x13800, symSize: 0x40 }
  - { offsetInCU: 0xADE, offset: 0xD9D7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15didMoveToWindowyyFTo', symObjAddr: 0x2C40, symBinAddr: 0x13FA0, symSize: 0x30 }
  - { offsetInCU: 0xB13, offset: 0xDA0C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC9imageRect010forContentF0So6CGRectVAG_tFTo', symObjAddr: 0x2CD0, symBinAddr: 0x14030, symSize: 0x70 }
  - { offsetInCU: 0xB72, offset: 0xDA6B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC9titleRect010forContentF0So6CGRectVAG_tFTo', symObjAddr: 0x2F00, symBinAddr: 0x14260, symSize: 0xB0 }
  - { offsetInCU: 0xBB9, offset: 0xDAB2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC14layoutSubviewsyyFTo', symObjAddr: 0x3380, symBinAddr: 0x146E0, symSize: 0x30 }
  - { offsetInCU: 0xBEE, offset: 0xDAE7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC12sizeThatFitsySo6CGSizeVAFFTo', symObjAddr: 0x3890, symBinAddr: 0x14BF0, symSize: 0x60 }
  - { offsetInCU: 0xC3C, offset: 0xDB35, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC20accessTokenDidChangeyy10Foundation12NotificationVFTo', symObjAddr: 0x3BE0, symBinAddr: 0x14F40, symSize: 0x80 }
  - { offsetInCU: 0xC73, offset: 0xDB6C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC16profileDidChangeyy10Foundation12NotificationVFTo', symObjAddr: 0x3EA0, symBinAddr: 0x15200, symSize: 0x80 }
  - { offsetInCU: 0xCD8, offset: 0xDBD1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC13buttonPressedyyypFTo', symObjAddr: 0x4980, symBinAddr: 0x15CE0, symSize: 0x60 }
  - { offsetInCU: 0xD0A, offset: 0xDC03, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC22makeLoginConfigurationAA0fG0CSgyFTo', symObjAddr: 0x4CD0, symBinAddr: 0x16030, symSize: 0x30 }
  - { offsetInCU: 0xD25, offset: 0xDC1E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC9logInUser33_2D5546723C2E9E390359F57C16888789LLyyFyAA11LoginResultOcfU_', symObjAddr: 0x4D00, symBinAddr: 0x16060, symSize: 0xE0 }
  - { offsetInCU: 0xD7A, offset: 0xDC73, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC17initializeContentyyFTo', symObjAddr: 0x4FB0, symBinAddr: 0x16310, symSize: 0x30 }
  - { offsetInCU: 0xDAC, offset: 0xDCA5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC27updateContentForAccessTokenyyFTo', symObjAddr: 0x5200, symBinAddr: 0x16560, symSize: 0x30 }
  - { offsetInCU: 0xDC7, offset: 0xDCC0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC18fetchAndSetContentyyFTo', symObjAddr: 0x54C0, symBinAddr: 0x16820, symSize: 0x30 }
  - { offsetInCU: 0xDFE, offset: 0xDCF7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC20updateContentForUseryy09FBSDKCoreB07ProfileCSgFTo', symObjAddr: 0x5600, symBinAddr: 0x16960, symSize: 0x60 }
  - { offsetInCU: 0xE19, offset: 0xDD12, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC6logoutyyFTo', symObjAddr: 0x5660, symBinAddr: 0x169C0, symSize: 0x30 }
  - { offsetInCU: 0xE64, offset: 0xDD5D, size: 0x8, addend: 0x0, symName: ___swift_mutable_project_boxed_opaque_existential_1, symObjAddr: 0x250, symBinAddr: 0x11620, symSize: 0x30 }
  - { offsetInCU: 0xE77, offset: 0xDD70, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15defaultAudienceAA07DefaultF0OvpACTK', symObjAddr: 0x280, symBinAddr: 0x11650, symSize: 0x80 }
  - { offsetInCU: 0xEAD, offset: 0xDDA6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15defaultAudienceAA07DefaultF0OvpACTk', symObjAddr: 0x300, symBinAddr: 0x116D0, symSize: 0x80 }
  - { offsetInCU: 0xFB1, offset: 0xDEAA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit29UserInterfaceElementProviding_pWOb', symObjAddr: 0x1650, symBinAddr: 0x129B0, symSize: 0x20 }
  - { offsetInCU: 0xFC4, offset: 0xDEBD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28UserInterfaceStringProviding_pWOb', symObjAddr: 0x1790, symBinAddr: 0x12AF0, symSize: 0x20 }
  - { offsetInCU: 0xFD7, offset: 0xDED0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14LoginProviding_pWOb', symObjAddr: 0x1860, symBinAddr: 0x12BC0, symSize: 0x20 }
  - { offsetInCU: 0x102E, offset: 0xDF27, size: 0x8, addend: 0x0, symName: '_$sSo20FBSDKInternalUtilityCMa', symObjAddr: 0x1FC0, symBinAddr: 0x13320, symSize: 0x30 }
  - { offsetInCU: 0x135A, offset: 0xE253, size: 0x8, addend: 0x0, symName: '_$sSo13UIAlertActionCIegg_ABIeyBy_TR', symObjAddr: 0x4DE0, symBinAddr: 0x16140, symSize: 0x50 }
  - { offsetInCU: 0x13EE, offset: 0xE2E7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonCfETo', symObjAddr: 0x56C0, symBinAddr: 0x16A20, symSize: 0xE0 }
  - { offsetInCU: 0x15BF, offset: 0xE4B8, size: 0x8, addend: 0x0, symName: '_$sypSgWOb', symObjAddr: 0x5D20, symBinAddr: 0x17050, symSize: 0x40 }
  - { offsetInCU: 0x15D2, offset: 0xE4CB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC9logInUser33_2D5546723C2E9E390359F57C16888789LLyyFyAA11LoginResultOcfU_TA', symObjAddr: 0x5DF0, symBinAddr: 0x170C0, symSize: 0x10 }
  - { offsetInCU: 0x1635, offset: 0xE52E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC18fetchAndSetContentyyFySo27FBSDKGraphRequestConnecting_pSg_ypSgs5Error_pSgtcfU_TA', symObjAddr: 0x5FA0, symBinAddr: 0x17270, symSize: 0x10 }
  - { offsetInCU: 0x1648, offset: 0xE541, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x5FB0, symBinAddr: 0x17280, symSize: 0x20 }
  - { offsetInCU: 0x165B, offset: 0xE554, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x5FD0, symBinAddr: 0x172A0, symSize: 0x10 }
  - { offsetInCU: 0x166E, offset: 0xE567, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15TooltipBehaviorOSHAASQWb', symObjAddr: 0x5FE0, symBinAddr: 0x172B0, symSize: 0x10 }
  - { offsetInCU: 0x1681, offset: 0xE57A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15TooltipBehaviorOAESQAAWl', symObjAddr: 0x5FF0, symBinAddr: 0x172C0, symSize: 0x30 }
  - { offsetInCU: 0x1694, offset: 0xE58D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonCMa', symObjAddr: 0x6130, symBinAddr: 0x17400, symSize: 0x20 }
  - { offsetInCU: 0x16A7, offset: 0xE5A0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15TooltipBehaviorOMa', symObjAddr: 0x6150, symBinAddr: 0x17420, symSize: 0x10 }
  - { offsetInCU: 0x16BA, offset: 0xE5B3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19LoginButtonDelegate_pSgXwWOh', symObjAddr: 0x6190, symBinAddr: 0x17460, symSize: 0x20 }
  - { offsetInCU: 0x16CD, offset: 0xE5C6, size: 0x8, addend: 0x0, symName: '_$sypSgWOc', symObjAddr: 0x61B0, symBinAddr: 0x17480, symSize: 0x40 }
  - { offsetInCU: 0x16E0, offset: 0xE5D9, size: 0x8, addend: 0x0, symName: '_$s12FBSDKCoreKit10PermissionOACSHAAWl', symObjAddr: 0x61F0, symBinAddr: 0x174C0, symSize: 0x40 }
  - { offsetInCU: 0x16F3, offset: 0xE5EC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC26presentAlertViewController33_2D5546723C2E9E390359F57C16888789LLyyFySo13UIAlertActionCcfU_TA', symObjAddr: 0x6230, symBinAddr: 0x17500, symSize: 0x10 }
  - { offsetInCU: 0x1706, offset: 0xE5FF, size: 0x8, addend: 0x0, symName: '_$sS2Ss7CVarArg10FoundationWl', symObjAddr: 0x6240, symBinAddr: 0x17510, symSize: 0x30 }
  - { offsetInCU: 0x1719, offset: 0xE612, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultOWOe', symObjAddr: 0x6270, symBinAddr: 0x17540, symSize: 0x50 }
  - { offsetInCU: 0x181B, offset: 0xE714, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15TooltipBehaviorOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x1BC0, symBinAddr: 0x12F20, symSize: 0x10 }
  - { offsetInCU: 0x18B5, offset: 0xE7AE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15TooltipBehaviorOSHAASH9hashValueSivgTW', symObjAddr: 0x1BD0, symBinAddr: 0x12F30, symSize: 0x40 }
  - { offsetInCU: 0x195C, offset: 0xE855, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15TooltipBehaviorOSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0x1C10, symBinAddr: 0x12F70, symSize: 0x20 }
  - { offsetInCU: 0x19AB, offset: 0xE8A4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15TooltipBehaviorOSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0x1C30, symBinAddr: 0x12F90, symSize: 0x40 }
  - { offsetInCU: 0x1C6B, offset: 0xEB64, size: 0x8, addend: 0x0, symName: '_$sShyShyxGqd__nc7ElementQyd__RszSTRd__lufC12FBSDKCoreKit10PermissionO_SayAFGTgm5Tf4g_n', symObjAddr: 0x5E00, symBinAddr: 0x170D0, symSize: 0x110 }
  - { offsetInCU: 0x1D9D, offset: 0xEC96, size: 0x8, addend: 0x0, symName: '_$sShyShyxGqd__nc7ElementQyd__RszSTRd__lufCSS_SaySSGTgm5Tf4g_n', symObjAddr: 0x5F10, symBinAddr: 0x171E0, symSize: 0x90 }
  - { offsetInCU: 0x2112, offset: 0xF00B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15defaultAudienceAA07DefaultF0Ovg', symObjAddr: 0x90, symBinAddr: 0x11490, symSize: 0x80 }
  - { offsetInCU: 0x2157, offset: 0xF050, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15defaultAudienceAA07DefaultF0Ovs', symObjAddr: 0x1E0, symBinAddr: 0x115B0, symSize: 0x70 }
  - { offsetInCU: 0x217D, offset: 0xF076, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15defaultAudienceAA07DefaultF0OvM', symObjAddr: 0x380, symBinAddr: 0x11750, symSize: 0xA0 }
  - { offsetInCU: 0x21BD, offset: 0xF0B6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15defaultAudienceAA07DefaultF0OvM.resume.0', symObjAddr: 0x420, symBinAddr: 0x117F0, symSize: 0x80 }
  - { offsetInCU: 0x21F9, offset: 0xF0F2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC8delegateAA05LoginD8Delegate_pSgvg', symObjAddr: 0x4E0, symBinAddr: 0x118B0, symSize: 0x40 }
  - { offsetInCU: 0x2238, offset: 0xF131, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC8delegateAA05LoginD8Delegate_pSgvs', symObjAddr: 0x560, symBinAddr: 0x11930, symSize: 0x50 }
  - { offsetInCU: 0x225E, offset: 0xF157, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC8delegateAA05LoginD8Delegate_pSgvM', symObjAddr: 0x5B0, symBinAddr: 0x11980, symSize: 0x70 }
  - { offsetInCU: 0x227F, offset: 0xF178, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC8delegateAA05LoginD8Delegate_pSgvM.resume.0', symObjAddr: 0x620, symBinAddr: 0x119F0, symSize: 0x60 }
  - { offsetInCU: 0x22D2, offset: 0xF1CB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC11permissionsSaySSGvM', symObjAddr: 0x780, symBinAddr: 0x11B50, symSize: 0x40 }
  - { offsetInCU: 0x22F5, offset: 0xF1EE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC11permissionsSaySSGvM.resume.0', symObjAddr: 0x7C0, symBinAddr: 0x11B90, symSize: 0x10 }
  - { offsetInCU: 0x2326, offset: 0xF21F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15tooltipBehaviorAC07TooltipF0Ovg', symObjAddr: 0x810, symBinAddr: 0x11BE0, symSize: 0x30 }
  - { offsetInCU: 0x2363, offset: 0xF25C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15tooltipBehaviorAC07TooltipF0Ovs', symObjAddr: 0x880, symBinAddr: 0x11C50, symSize: 0x40 }
  - { offsetInCU: 0x2387, offset: 0xF280, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15tooltipBehaviorAC07TooltipF0OvM', symObjAddr: 0x8C0, symBinAddr: 0x11C90, symSize: 0x40 }
  - { offsetInCU: 0x23BC, offset: 0xF2B5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC17tooltipColorStyleAA13FBTooltipViewC0fG0Ovg', symObjAddr: 0x940, symBinAddr: 0x11D10, symSize: 0x30 }
  - { offsetInCU: 0x23F9, offset: 0xF2F2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC17tooltipColorStyleAA13FBTooltipViewC0fG0Ovs', symObjAddr: 0x9B0, symBinAddr: 0x11D80, symSize: 0x40 }
  - { offsetInCU: 0x241D, offset: 0xF316, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC17tooltipColorStyleAA13FBTooltipViewC0fG0OvM', symObjAddr: 0x9F0, symBinAddr: 0x11DC0, symSize: 0x40 }
  - { offsetInCU: 0x2452, offset: 0xF34B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC13loginTrackingAA05LoginF0Ovg', symObjAddr: 0xA70, symBinAddr: 0x11E40, symSize: 0x30 }
  - { offsetInCU: 0x248F, offset: 0xF388, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC13loginTrackingAA05LoginF0Ovs', symObjAddr: 0xAE0, symBinAddr: 0x11EB0, symSize: 0x40 }
  - { offsetInCU: 0x24B3, offset: 0xF3AC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC13loginTrackingAA05LoginF0OvM', symObjAddr: 0xB20, symBinAddr: 0x11EF0, symSize: 0x40 }
  - { offsetInCU: 0x24E8, offset: 0xF3E1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5nonceSSSgvg', symObjAddr: 0xBB0, symBinAddr: 0x11F80, symSize: 0x30 }
  - { offsetInCU: 0x2556, offset: 0xF44F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5nonceSSSgvs', symObjAddr: 0xC40, symBinAddr: 0x12010, symSize: 0x230 }
  - { offsetInCU: 0x2698, offset: 0xF591, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5nonceSSSgvM', symObjAddr: 0xEE0, symBinAddr: 0x12240, symSize: 0x40 }
  - { offsetInCU: 0x26D4, offset: 0xF5CD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5nonceSSSgvM.resume.0', symObjAddr: 0xF20, symBinAddr: 0x12280, symSize: 0x60 }
  - { offsetInCU: 0x26F3, offset: 0xF5EC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15messengerPageIdSSSgvM', symObjAddr: 0x1000, symBinAddr: 0x12360, symSize: 0x40 }
  - { offsetInCU: 0x2728, offset: 0xF621, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC8authTypeSo0a4AuthF0aSgvg', symObjAddr: 0x1080, symBinAddr: 0x123E0, symSize: 0x40 }
  - { offsetInCU: 0x2765, offset: 0xF65E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC8authTypeSo0a4AuthF0aSgvs', symObjAddr: 0x1120, symBinAddr: 0x12480, symSize: 0x50 }
  - { offsetInCU: 0x2789, offset: 0xF682, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC8authTypeSo0a4AuthF0aSgvM', symObjAddr: 0x1170, symBinAddr: 0x124D0, symSize: 0x40 }
  - { offsetInCU: 0x27BE, offset: 0xF6B7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC12codeVerifierAA04CodeF0Cvg', symObjAddr: 0x11F0, symBinAddr: 0x12550, symSize: 0x40 }
  - { offsetInCU: 0x27FB, offset: 0xF6F4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC12codeVerifierAA04CodeF0Cvs', symObjAddr: 0x1290, symBinAddr: 0x125F0, symSize: 0x50 }
  - { offsetInCU: 0x281F, offset: 0xF718, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC12codeVerifierAA04CodeF0CvM', symObjAddr: 0x12E0, symBinAddr: 0x12640, symSize: 0x40 }
  - { offsetInCU: 0x2842, offset: 0xF73B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC6userIDSSSgvM', symObjAddr: 0x13A0, symBinAddr: 0x12700, symSize: 0x40 }
  - { offsetInCU: 0x2865, offset: 0xF75E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC8userNameSSSgvM', symObjAddr: 0x15D0, symBinAddr: 0x12930, symSize: 0x40 }
  - { offsetInCU: 0x2888, offset: 0xF781, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15elementProviderAA29UserInterfaceElementProviding_pvM', symObjAddr: 0x1670, symBinAddr: 0x129D0, symSize: 0x40 }
  - { offsetInCU: 0x28AB, offset: 0xF7A4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC14stringProviderAA28UserInterfaceStringProviding_pvM', symObjAddr: 0x17B0, symBinAddr: 0x12B10, symSize: 0x40 }
  - { offsetInCU: 0x28CE, offset: 0xF7C7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC13loginProviderAA14LoginProviding_pvM', symObjAddr: 0x1880, symBinAddr: 0x12BE0, symSize: 0x40 }
  - { offsetInCU: 0x2925, offset: 0xF81E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC19graphRequestFactorySo010FBSDKGraphfG0_pvM', symObjAddr: 0x1A40, symBinAddr: 0x12DA0, symSize: 0x40 }
  - { offsetInCU: 0x295A, offset: 0xF853, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15isAuthenticatedSbvg', symObjAddr: 0x1B20, symBinAddr: 0x12E80, symSize: 0x70 }
  - { offsetInCU: 0x297A, offset: 0xF873, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15TooltipBehaviorO8rawValueAESgSu_tcfC', symObjAddr: 0x1B90, symBinAddr: 0x12EF0, symSize: 0x20 }
  - { offsetInCU: 0x2995, offset: 0xF88E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15TooltipBehaviorO8rawValueSuvg', symObjAddr: 0x1BB0, symBinAddr: 0x12F10, symSize: 0x10 }
  - { offsetInCU: 0x2A0D, offset: 0xF906, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5frameACSo6CGRectV_tcfC', symObjAddr: 0x1CA0, symBinAddr: 0x13000, symSize: 0x80 }
  - { offsetInCU: 0x2A3E, offset: 0xF937, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5frameACSo6CGRectV_tcfc', symObjAddr: 0x1D20, symBinAddr: 0x13080, symSize: 0x2A0 }
  - { offsetInCU: 0x2B03, offset: 0xF9FC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC09configureD033_2D5546723C2E9E390359F57C16888789LLyyF', symObjAddr: 0x1FF0, symBinAddr: 0x13350, symSize: 0x410 }
  - { offsetInCU: 0x2BB5, offset: 0xFAAE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5coderACSgSo7NSCoderC_tcfC', symObjAddr: 0x2430, symBinAddr: 0x13790, symSize: 0x40 }
  - { offsetInCU: 0x2BC8, offset: 0xFAC1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5coderACSgSo7NSCoderC_tcfc', symObjAddr: 0x2470, symBinAddr: 0x137D0, symSize: 0x30 }
  - { offsetInCU: 0x2BE1, offset: 0xFADA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5frame15elementProvider06stringG005loginG019graphRequestFactoryACSo6CGRectV_AA29UserInterfaceElementProviding_pAA0no6StringQ0_pAA05LoginQ0_pSo010FBSDKGraphkL0_ptcfC', symObjAddr: 0x24E0, symBinAddr: 0x13840, symSize: 0x180 }
  - { offsetInCU: 0x2CC0, offset: 0xFBB9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5frame11permissionsACSo6CGRectV_Say09FBSDKCoreB010PermissionOGtcfC', symObjAddr: 0x2660, symBinAddr: 0x139C0, symSize: 0x260 }
  - { offsetInCU: 0x2EC2, offset: 0xFDBB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC17initializeContentyyF', symObjAddr: 0x28C0, symBinAddr: 0x13C20, symSize: 0x180 }
  - { offsetInCU: 0x2F14, offset: 0xFE0D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15didMoveToWindowyyF', symObjAddr: 0x2A40, symBinAddr: 0x13DA0, symSize: 0xA0 }
  - { offsetInCU: 0x2FC4, offset: 0xFEBD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC19showTooltipIfNeeded33_2D5546723C2E9E390359F57C16888789LLyyF', symObjAddr: 0x2AE0, symBinAddr: 0x13E40, symSize: 0x160 }
  - { offsetInCU: 0x3181, offset: 0x1007A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC9imageRect010forContentF0So6CGRectVAG_tF', symObjAddr: 0x2C70, symBinAddr: 0x13FD0, symSize: 0x60 }
  - { offsetInCU: 0x31F2, offset: 0x100EB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC9titleRect010forContentF0So6CGRectVAG_tF', symObjAddr: 0x2D40, symBinAddr: 0x140A0, symSize: 0x1C0 }
  - { offsetInCU: 0x32FF, offset: 0x101F8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC14layoutSubviewsyyF', symObjAddr: 0x2FB0, symBinAddr: 0x14310, symSize: 0x3D0 }
  - { offsetInCU: 0x33D3, offset: 0x102CC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC12sizeThatFitsySo6CGSizeVAFF', symObjAddr: 0x33B0, symBinAddr: 0x14710, symSize: 0x4E0 }
  - { offsetInCU: 0x34EA, offset: 0x103E3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC20accessTokenDidChangeyy10Foundation12NotificationVF', symObjAddr: 0x38F0, symBinAddr: 0x14C50, symSize: 0x1B0 }
  - { offsetInCU: 0x358F, offset: 0x10488, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC27updateContentForAccessTokenyyF', symObjAddr: 0x3AA0, symBinAddr: 0x14E00, symSize: 0x140 }
  - { offsetInCU: 0x35CC, offset: 0x104C5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC16profileDidChangeyy10Foundation12NotificationVF', symObjAddr: 0x3C60, symBinAddr: 0x14FC0, symSize: 0x140 }
  - { offsetInCU: 0x3624, offset: 0x1051D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC20updateContentForUseryy09FBSDKCoreB07ProfileCSgF', symObjAddr: 0x3DA0, symBinAddr: 0x15100, symSize: 0x100 }
  - { offsetInCU: 0x36AA, offset: 0x105A3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC13buttonPressedyyypF', symObjAddr: 0x3F20, symBinAddr: 0x15280, symSize: 0x260 }
  - { offsetInCU: 0x3765, offset: 0x1065E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC26presentAlertViewController33_2D5546723C2E9E390359F57C16888789LLyyF', symObjAddr: 0x4180, symBinAddr: 0x154E0, symSize: 0x800 }
  - { offsetInCU: 0x38DC, offset: 0x107D5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC26presentAlertViewController33_2D5546723C2E9E390359F57C16888789LLyyFySo13UIAlertActionCcfU_', symObjAddr: 0x4E30, symBinAddr: 0x16190, symSize: 0xC0 }
  - { offsetInCU: 0x3981, offset: 0x1087A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC22makeLoginConfigurationAA0fG0CSgyF', symObjAddr: 0x49E0, symBinAddr: 0x15D40, symSize: 0x2F0 }
  - { offsetInCU: 0x3BD8, offset: 0x10AD1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC6logoutyyF', symObjAddr: 0x4EF0, symBinAddr: 0x16250, symSize: 0xC0 }
  - { offsetInCU: 0x3C25, offset: 0x10B1E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC18fetchAndSetContentyyF', symObjAddr: 0x4FE0, symBinAddr: 0x16340, symSize: 0x220 }
  - { offsetInCU: 0x3D15, offset: 0x10C0E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC18fetchAndSetContentyyFySo27FBSDKGraphRequestConnecting_pSg_ypSgs5Error_pSgtcfU_', symObjAddr: 0x5230, symBinAddr: 0x16590, symSize: 0x290 }
  - { offsetInCU: 0x3E2E, offset: 0x10D27, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC27userInformationDoesNotMatch33_2D5546723C2E9E390359F57C16888789LLySb09FBSDKCoreB07ProfileCF', symObjAddr: 0x54F0, symBinAddr: 0x16850, symSize: 0x110 }
  - { offsetInCU: 0x3EA8, offset: 0x10DA1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonCfD', symObjAddr: 0x5690, symBinAddr: 0x169F0, symSize: 0x30 }
  - { offsetInCU: 0x3EC9, offset: 0x10DC2, size: 0x8, addend: 0x0, symName: '_$ss15ContiguousArrayV16_createNewBuffer14bufferIsUnique15minimumCapacity13growForAppendySb_SiSbtFSS_Tg5', symObjAddr: 0x57A0, symBinAddr: 0x16B00, symSize: 0x20 }
  - { offsetInCU: 0x3EDD, offset: 0x10DD6, size: 0x8, addend: 0x0, symName: '_$ss15ContiguousArrayV16_createNewBuffer14bufferIsUnique15minimumCapacity13growForAppendySb_SiSbtF12FBSDKCoreKit10PermissionO_Tg5', symObjAddr: 0x57C0, symBinAddr: 0x16B20, symSize: 0x20 }
  - { offsetInCU: 0x3F1B, offset: 0x10E14, size: 0x8, addend: 0x0, symName: '_$ss22_ContiguousArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtFSS_Tg5', symObjAddr: 0x57E0, symBinAddr: 0x16B40, symSize: 0x110 }
  - { offsetInCU: 0x4014, offset: 0x10F0D, size: 0x8, addend: 0x0, symName: '_$ss22_ContiguousArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtF12FBSDKCoreKit10PermissionO_Tg5', symObjAddr: 0x58F0, symBinAddr: 0x16C50, symSize: 0x1A0 }
  - { offsetInCU: 0x411B, offset: 0x11014, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5coderACSgSo7NSCoderC_tcfcTf4gn_n', symObjAddr: 0x5A90, symBinAddr: 0x16DF0, symSize: 0x260 }
  - { offsetInCU: 0x77, offset: 0x11202, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC8delegateAA05LogindE8Delegate_pSgvgTo', symObjAddr: 0x20, symBinAddr: 0x176B0, symSize: 0x40 }
  - { offsetInCU: 0xCB, offset: 0x11256, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC8delegateAA05LogindE8Delegate_pSgvsTo', symObjAddr: 0xA0, symBinAddr: 0x17730, symSize: 0x40 }
  - { offsetInCU: 0x14B, offset: 0x112D6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC12forceDisplaySbvgTo', symObjAddr: 0x200, symBinAddr: 0x17890, symSize: 0x40 }
  - { offsetInCU: 0x290, offset: 0x1141B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewCACycfcTo', symObjAddr: 0x500, symBinAddr: 0x17B90, symSize: 0xA0 }
  - { offsetInCU: 0x36E, offset: 0x114F9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC7tagline7message10colorStyleACSSSg_AgA09FBTooltipE0C05ColorI0OtcfcTo', symObjAddr: 0x7D0, symBinAddr: 0x17E60, symSize: 0x70 }
  - { offsetInCU: 0x3ED, offset: 0x11578, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC7present2in13arrowPosition9directionySo6UIViewC_So7CGPointVAA09FBTooltipE0C14ArrowDirectionOtFTo', symObjAddr: 0xAD0, symBinAddr: 0x18160, symSize: 0x80 }
  - { offsetInCU: 0x408, offset: 0x11593, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC05fetchD13Configuration015_3798D0DCC12906H16A17D23FC0B3F34C1LL4with13arrowPosition9directionySo6UIViewC_So7CGPointVAA09FBTooltipE0C14ArrowDirectionOtFySo0aD0CSg_s5Error_pSgtcfU_', symObjAddr: 0xB50, symBinAddr: 0x181E0, symSize: 0x2A0 }
  - { offsetInCU: 0x585, offset: 0x11710, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC27serverConfigurationProvider06stringH0AcA06ServerG9Providing_p_AA019UserInterfaceStringK0_ptcfcTf4een_n09FBSDKCoreB00jgH0C_So20FBSDKInternalUtilityCTg5', symObjAddr: 0xEA0, symBinAddr: 0x18530, symSize: 0x160 }
  - { offsetInCU: 0x621, offset: 0x117AC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC27serverConfigurationProvider06stringH0AcA06ServerG9Providing_p_AA019UserInterfaceStringK0_ptcfcTf4een_n', symObjAddr: 0x1060, symBinAddr: 0x18690, symSize: 0x170 }
  - { offsetInCU: 0x6B0, offset: 0x1183B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC27serverConfigurationProvider06stringH0AcA06ServerG9Providing_p_AA019UserInterfaceStringK0_ptcfCTfq4een_nTf4ngn_nTf4gnn_n', symObjAddr: 0x11D0, symBinAddr: 0x18800, symSize: 0xC0 }
  - { offsetInCU: 0x7C0, offset: 0x1194B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewCfETo', symObjAddr: 0xE60, symBinAddr: 0x184F0, symSize: 0x40 }
  - { offsetInCU: 0x7ED, offset: 0x11978, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC05fetchD13Configuration015_3798D0DCC12906H16A17D23FC0B3F34C1LL4with13arrowPosition9directionySo6UIViewC_So7CGPointVAA09FBTooltipE0C14ArrowDirectionOtFySo0aD0CSg_s5Error_pSgtcfU_TA', symObjAddr: 0x1330, symBinAddr: 0x18900, symSize: 0x30 }
  - { offsetInCU: 0x800, offset: 0x1198B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewCMa', symObjAddr: 0x1460, symBinAddr: 0x18A30, symSize: 0x20 }
  - { offsetInCU: 0x813, offset: 0x1199E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24LoginTooltipViewDelegate_pSgXwWOh', symObjAddr: 0x14B0, symBinAddr: 0x18A80, symSize: 0x20 }
  - { offsetInCU: 0x826, offset: 0x119B1, size: 0x8, addend: 0x0, symName: ___swift_allocate_boxed_opaque_existential_1, symObjAddr: 0x14D0, symBinAddr: 0x18AA0, symSize: 0x30 }
  - { offsetInCU: 0x9D3, offset: 0x11B5E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewCACycfC', symObjAddr: 0x0, symBinAddr: 0x17690, symSize: 0x20 }
  - { offsetInCU: 0x9FD, offset: 0x11B88, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC8delegateAA05LogindE8Delegate_pSgvg', symObjAddr: 0x60, symBinAddr: 0x176F0, symSize: 0x40 }
  - { offsetInCU: 0xA42, offset: 0x11BCD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC8delegateAA05LogindE8Delegate_pSgvs', symObjAddr: 0xE0, symBinAddr: 0x17770, symSize: 0x50 }
  - { offsetInCU: 0xA68, offset: 0x11BF3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC8delegateAA05LogindE8Delegate_pSgvM', symObjAddr: 0x130, symBinAddr: 0x177C0, symSize: 0x70 }
  - { offsetInCU: 0xA89, offset: 0x11C14, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC8delegateAA05LogindE8Delegate_pSgvM.resume.0', symObjAddr: 0x1A0, symBinAddr: 0x17830, symSize: 0x60 }
  - { offsetInCU: 0xABA, offset: 0x11C45, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC12forceDisplaySbvg', symObjAddr: 0x240, symBinAddr: 0x178D0, symSize: 0x30 }
  - { offsetInCU: 0xAD5, offset: 0x11C60, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC12forceDisplaySbvs', symObjAddr: 0x280, symBinAddr: 0x17910, symSize: 0x40 }
  - { offsetInCU: 0xB05, offset: 0x11C90, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC12forceDisplaySbvM', symObjAddr: 0x2C0, symBinAddr: 0x17950, symSize: 0x40 }
  - { offsetInCU: 0xB28, offset: 0x11CB3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC12forceDisplaySbvM.resume.0', symObjAddr: 0x300, symBinAddr: 0x17990, symSize: 0x10 }
  - { offsetInCU: 0xB59, offset: 0x11CE4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC18shouldForceDisplaySbvM', symObjAddr: 0x360, symBinAddr: 0x179F0, symSize: 0x70 }
  - { offsetInCU: 0xB95, offset: 0x11D20, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC18shouldForceDisplaySbvM.resume.0', symObjAddr: 0x3D0, symBinAddr: 0x17A60, symSize: 0x20 }
  - { offsetInCU: 0xBC7, offset: 0x11D52, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC27serverConfigurationProviderAA06ServerG9Providing_pvg', symObjAddr: 0x3F0, symBinAddr: 0x17A80, symSize: 0x20 }
  - { offsetInCU: 0xBE8, offset: 0x11D73, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC14stringProviderAA28UserInterfaceStringProviding_pvg', symObjAddr: 0x410, symBinAddr: 0x17AA0, symSize: 0x20 }
  - { offsetInCU: 0xC21, offset: 0x11DAC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewCACycfc', symObjAddr: 0x460, symBinAddr: 0x17AF0, symSize: 0xA0 }
  - { offsetInCU: 0xCFE, offset: 0x11E89, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC7tagline7message10colorStyleACSSSg_AgA09FBTooltipE0C05ColorI0OtcfC', symObjAddr: 0x5A0, symBinAddr: 0x17C30, symSize: 0x120 }
  - { offsetInCU: 0xD4E, offset: 0x11ED9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC7tagline7message10colorStyleACSSSg_AgA09FBTooltipE0C05ColorI0Otcfc', symObjAddr: 0x6C0, symBinAddr: 0x17D50, symSize: 0x110 }
  - { offsetInCU: 0xD9B, offset: 0x11F26, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC27serverConfigurationProvider06stringH0AcA06ServerG9Providing_p_AA019UserInterfaceStringK0_ptcfC', symObjAddr: 0x840, symBinAddr: 0x17ED0, symSize: 0x90 }
  - { offsetInCU: 0xDD7, offset: 0x11F62, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC27serverConfigurationProvider06stringH0AcA06ServerG9Providing_p_AA019UserInterfaceStringK0_ptcfc', symObjAddr: 0x8D0, symBinAddr: 0x17F60, symSize: 0xE0 }
  - { offsetInCU: 0xE47, offset: 0x11FD2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC7present2in13arrowPosition9directionySo6UIViewC_So7CGPointVAA09FBTooltipE0C14ArrowDirectionOtF', symObjAddr: 0x9B0, symBinAddr: 0x18040, symSize: 0x120 }
  - { offsetInCU: 0xF3F, offset: 0x120CA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewCfE', symObjAddr: 0xDF0, symBinAddr: 0x18480, symSize: 0x40 }
  - { offsetInCU: 0xF62, offset: 0x120ED, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewCfD', symObjAddr: 0xE30, symBinAddr: 0x184C0, symSize: 0x30 }
  - { offsetInCU: 0x168, offset: 0x122C3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionC4hashSivgTo', symObjAddr: 0x3C0, symBinAddr: 0x18EC0, symSize: 0x40 }
  - { offsetInCU: 0x1D7, offset: 0x12332, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionC6stringACSgSS_tcfcTo', symObjAddr: 0x640, symBinAddr: 0x19110, symSize: 0x30 }
  - { offsetInCU: 0x20A, offset: 0x12365, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionC11permissions18fromRawPermissionsShyACGSgShySSG_tFZTo', symObjAddr: 0x680, symBinAddr: 0x19150, symSize: 0x90 }
  - { offsetInCU: 0x23A, offset: 0x12395, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionC14rawPermissions4fromShySSGShyACG_tFZTo', symObjAddr: 0x750, symBinAddr: 0x19220, symSize: 0x90 }
  - { offsetInCU: 0x2A0, offset: 0x123FB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionC7isEqualySbypSgFTo', symObjAddr: 0x8C0, symBinAddr: 0x19390, symSize: 0x90 }
  - { offsetInCU: 0x2E5, offset: 0x12440, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionCACycfcTo', symObjAddr: 0x9A0, symBinAddr: 0x19470, symSize: 0x30 }
  - { offsetInCU: 0x35D, offset: 0x124B8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionC11permissions18fromRawPermissionsShyACGSgShySSG_tFZTf4nd_n', symObjAddr: 0xA20, symBinAddr: 0x194F0, symSize: 0x3E0 }
  - { offsetInCU: 0x50B, offset: 0x12666, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionCfETo', symObjAddr: 0xA00, symBinAddr: 0x194D0, symSize: 0x20 }
  - { offsetInCU: 0x556, offset: 0x126B1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionCMa', symObjAddr: 0xEB0, symBinAddr: 0x198D0, symSize: 0x20 }
  - { offsetInCU: 0x569, offset: 0x126C4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionCSo8NSObjectCSH10ObjectiveCWl', symObjAddr: 0xF00, symBinAddr: 0x19920, symSize: 0x30 }
  - { offsetInCU: 0x57C, offset: 0x126D7, size: 0x8, addend: 0x0, symName: '_$sSh5IndexV8_VariantOyx__GSHRzlWOe', symObjAddr: 0xF30, symBinAddr: 0x19950, symSize: 0x20 }
  - { offsetInCU: 0x5CE, offset: 0x12729, size: 0x8, addend: 0x0, symName: '_$sSlsE3mapySayqd__Gqd__7ElementQzKXEKlFShy13FBSDKLoginKit12FBPermissionCG_SSTg504$s13c5Kit12E93C14rawPermissions4fromShySSGShyACG_tFZSSACcfu_32e0d58b938ad0b6cb17de1b825049cc00ACSSTf3nnpk_nTf1cn_n', symObjAddr: 0x0, symBinAddr: 0x18B00, symSize: 0x320 }
  - { offsetInCU: 0x98F, offset: 0x12AEA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionC14rawPermissions4fromShySSGShyACG_tFZ', symObjAddr: 0x710, symBinAddr: 0x191E0, symSize: 0x40 }
  - { offsetInCU: 0xA13, offset: 0x12B6E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionC11descriptionSSvg', symObjAddr: 0x390, symBinAddr: 0x18E90, symSize: 0x30 }
  - { offsetInCU: 0xA4B, offset: 0x12BA6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionC4hashSivg', symObjAddr: 0x400, symBinAddr: 0x18F00, symSize: 0x40 }
  - { offsetInCU: 0xA68, offset: 0x12BC3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionC6stringACSgSS_tcfC', symObjAddr: 0x470, symBinAddr: 0x18F40, symSize: 0x40 }
  - { offsetInCU: 0xA81, offset: 0x12BDC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionC6stringACSgSS_tcfc', symObjAddr: 0x4B0, symBinAddr: 0x18F80, symSize: 0x190 }
  - { offsetInCU: 0xACA, offset: 0x12C25, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionC11permissions18fromRawPermissionsShyACGSgShySSG_tFZ', symObjAddr: 0x670, symBinAddr: 0x19140, symSize: 0x10 }
  - { offsetInCU: 0xB16, offset: 0x12C71, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionC7isEqualySbypSgF', symObjAddr: 0x7E0, symBinAddr: 0x192B0, symSize: 0xE0 }
  - { offsetInCU: 0xB5E, offset: 0x12CB9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionCACycfC', symObjAddr: 0x950, symBinAddr: 0x19420, symSize: 0x20 }
  - { offsetInCU: 0xB71, offset: 0x12CCC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionCACycfc', symObjAddr: 0x970, symBinAddr: 0x19440, symSize: 0x30 }
  - { offsetInCU: 0xBC5, offset: 0x12D20, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionCfD', symObjAddr: 0x9D0, symBinAddr: 0x194A0, symSize: 0x30 }
  - { offsetInCU: 0x4E, offset: 0x12E20, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO13kNUXRectInset12CoreGraphics7CGFloatVvpZ', symObjAddr: 0x6D70, symBinAddr: 0x5EF38, symSize: 0x0 }
  - { offsetInCU: 0x68, offset: 0x12E3A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO16kNUXBubbleMargin12CoreGraphics7CGFloatVvpZ', symObjAddr: 0x6D78, symBinAddr: 0x5EF40, symSize: 0x0 }
  - { offsetInCU: 0x82, offset: 0x12E54, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO15kNUXPointMargin12CoreGraphics7CGFloatVvpZ', symObjAddr: 0x6D80, symBinAddr: 0x5EF48, symSize: 0x0 }
  - { offsetInCU: 0x9C, offset: 0x12E6E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO16kNUXCornerRadius12CoreGraphics7CGFloatVvpZ', symObjAddr: 0x6D88, symBinAddr: 0x5EF50, symSize: 0x0 }
  - { offsetInCU: 0xB6, offset: 0x12E88, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO11kNUXSideCap12CoreGraphics7CGFloatVvpZ', symObjAddr: 0x6D90, symBinAddr: 0x5EF58, symSize: 0x0 }
  - { offsetInCU: 0xD0, offset: 0x12EA2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO12kNUXFontSize12CoreGraphics7CGFloatVvpZ', symObjAddr: 0x6D98, symBinAddr: 0x5EF60, symSize: 0x0 }
  - { offsetInCU: 0xEA, offset: 0x12EBC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO18kNUXCrossGlyphSize12CoreGraphics7CGFloatVvpZ', symObjAddr: 0x6DA0, symBinAddr: 0x5EF68, symSize: 0x0 }
  - { offsetInCU: 0x104, offset: 0x12ED6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO27kFriendlyBlueGradientColorsSaySo10CGColorRefaGvpZ', symObjAddr: 0x6DA8, symBinAddr: 0x5EF70, symSize: 0x0 }
  - { offsetInCU: 0x11E, offset: 0x12EF0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO12kNeutralGraySaySo10CGColorRefaGvpZ', symObjAddr: 0x6DB0, symBinAddr: 0x5EF78, symSize: 0x0 }
  - { offsetInCU: 0x12C, offset: 0x12EFE, size: 0x8, addend: 0x0, symName: '_$sIeg_IeyB_TR', symObjAddr: 0x0, symBinAddr: 0x19980, symSize: 0x30 }
  - { offsetInCU: 0x1A8, offset: 0x12F7A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC10ColorStyleOSYAASY8rawValuexSg03RawH0Qz_tcfCTW', symObjAddr: 0x2990, symBinAddr: 0x1C310, symSize: 0x30 }
  - { offsetInCU: 0x1D7, offset: 0x12FA9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC10ColorStyleOSYAASY8rawValue03RawH0QzvgTW', symObjAddr: 0x29C0, symBinAddr: 0x1C340, symSize: 0x10 }
  - { offsetInCU: 0x67C, offset: 0x1344E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO13kNUXRectInset_WZ', symObjAddr: 0x29D0, symBinAddr: 0x1C350, symSize: 0x20 }
  - { offsetInCU: 0x695, offset: 0x13467, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO16kNUXBubbleMargin_WZ', symObjAddr: 0x29F0, symBinAddr: 0x1C370, symSize: 0x40 }
  - { offsetInCU: 0x6BD, offset: 0x1348F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO15kNUXPointMargin_WZ', symObjAddr: 0x2A30, symBinAddr: 0x1C3B0, symSize: 0x20 }
  - { offsetInCU: 0x6D6, offset: 0x134A8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO16kNUXCornerRadius_WZ', symObjAddr: 0x2A50, symBinAddr: 0x1C3D0, symSize: 0x20 }
  - { offsetInCU: 0x6EF, offset: 0x134C1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO11kNUXSideCap_WZ', symObjAddr: 0x2A70, symBinAddr: 0x1C3F0, symSize: 0x20 }
  - { offsetInCU: 0x708, offset: 0x134DA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO12kNUXFontSize_WZ', symObjAddr: 0x2A90, symBinAddr: 0x1C410, symSize: 0x20 }
  - { offsetInCU: 0x721, offset: 0x134F3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO18kNUXCrossGlyphSize_WZ', symObjAddr: 0x2AB0, symBinAddr: 0x1C430, symSize: 0x20 }
  - { offsetInCU: 0x73A, offset: 0x1350C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO27kFriendlyBlueGradientColors_WZ', symObjAddr: 0x2AD0, symBinAddr: 0x1C450, symSize: 0x120 }
  - { offsetInCU: 0x7F5, offset: 0x135C7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO12kNeutralGray_WZ', symObjAddr: 0x2BF0, symBinAddr: 0x1C570, symSize: 0x120 }
  - { offsetInCU: 0x8B0, offset: 0x13682, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC15displayDurationSdvgTo', symObjAddr: 0x2D10, symBinAddr: 0x1C690, symSize: 0x40 }
  - { offsetInCU: 0x8E9, offset: 0x136BB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC15displayDurationSdvsTo', symObjAddr: 0x2D50, symBinAddr: 0x1C6D0, symSize: 0x50 }
  - { offsetInCU: 0x92C, offset: 0x136FE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC10colorStyleAC05ColorF0OvgTo', symObjAddr: 0x2DA0, symBinAddr: 0x1C720, symSize: 0x40 }
  - { offsetInCU: 0x965, offset: 0x13737, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC10colorStyleAC05ColorF0OvsTo', symObjAddr: 0x2DE0, symBinAddr: 0x1C760, symSize: 0x60 }
  - { offsetInCU: 0x9B7, offset: 0x13789, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9textLabelSo7UILabelCvpfiAFyXEfU_', symObjAddr: 0x2F90, symBinAddr: 0x1C910, symSize: 0x140 }
  - { offsetInCU: 0xA31, offset: 0x13803, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9textLabelSo7UILabelCvgTo', symObjAddr: 0x3110, symBinAddr: 0x1CA90, symSize: 0x20 }
  - { offsetInCU: 0xA96, offset: 0x13868, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewCACycfcTo', symObjAddr: 0x3190, symBinAddr: 0x1CB10, symSize: 0x20 }
  - { offsetInCU: 0xAF4, offset: 0x138C6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7tagline7message10colorStyleACSSSg_AgC05ColorH0OtcfcTo', symObjAddr: 0x3200, symBinAddr: 0x1CB80, symSize: 0x70 }
  - { offsetInCU: 0xB41, offset: 0x13913, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC5coderACSgSo7NSCoderC_tcfcTo', symObjAddr: 0x32C0, symBinAddr: 0x1CC40, symSize: 0x20 }
  - { offsetInCU: 0xB86, offset: 0x13958, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewCfDTo', symObjAddr: 0x3330, symBinAddr: 0x1CCB0, symSize: 0x70 }
  - { offsetInCU: 0xBB6, offset: 0x13988, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7present4fromySo6UIViewC_tFTo', symObjAddr: 0x3430, symBinAddr: 0x1CDB0, symSize: 0x50 }
  - { offsetInCU: 0xBD1, offset: 0x139A3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7present2in13arrowPosition9directionySo6UIViewC_So7CGPointVAC14ArrowDirectionOtFTo', symObjAddr: 0x3480, symBinAddr: 0x1CE00, symSize: 0x80 }
  - { offsetInCU: 0xBEC, offset: 0x139BE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7dismissyyFTo', symObjAddr: 0x35D0, symBinAddr: 0x1CF50, symSize: 0x90 }
  - { offsetInCU: 0xC6C, offset: 0x13A3E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC13animateFadeInyyFTo', symObjAddr: 0x4320, symBinAddr: 0x1DCA0, symSize: 0x30 }
  - { offsetInCU: 0xCA3, offset: 0x13A75, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC14onTapInTooltip33_1C39B2F52DDA14663AEF238AF411735ALLyySo19UIGestureRecognizerCFTo', symObjAddr: 0x4350, symBinAddr: 0x1DCD0, symSize: 0x70 }
  - { offsetInCU: 0xD1F, offset: 0x13AF1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC4drawyySo6CGRectVFTo', symObjAddr: 0x43D0, symBinAddr: 0x1DD40, symSize: 0x30 }
  - { offsetInCU: 0xD68, offset: 0x13B3A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC14layoutSubviewsyyFTo', symObjAddr: 0x4430, symBinAddr: 0x1DD70, symSize: 0x60 }
  - { offsetInCU: 0xD9A, offset: 0x13B6C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC47scheduleFadeoutRespectingMinimumDisplayDuration33_1C39B2F52DDA14663AEF238AF411735ALLyyFTo', symObjAddr: 0x4490, symBinAddr: 0x1DDD0, symSize: 0x80 }
  - { offsetInCU: 0xE2E, offset: 0x13C00, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC5frameACSo6CGRectV_tcfcTo', symObjAddr: 0x45C0, symBinAddr: 0x1DF00, symSize: 0x30 }
  - { offsetInCU: 0xE91, offset: 0x13C63, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC35fbsdkCreateUpPointingBubbleWithRect33_1C39B2F52DDA14663AEF238AF411735ALLySo16CGMutablePathRefaSo6CGRectV_12CoreGraphics7CGFloatVA2LtFTf4nnnnd_n', symObjAddr: 0x4690, symBinAddr: 0x1DFD0, symSize: 0x640 }
  - { offsetInCU: 0x10D5, offset: 0x13EA7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC37fbsdkCreateDownPointingBubbleWithRect33_1C39B2F52DDA14663AEF238AF411735ALLySo16CGMutablePathRefaSo6CGRectV_12CoreGraphics7CGFloatVA2LtFTf4nnnnd_n', symObjAddr: 0x4CD0, symBinAddr: 0x1E610, symSize: 0x640 }
  - { offsetInCU: 0x1319, offset: 0x140EB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC29createCloseCrossGlyphWithRect33_1C39B2F52DDA14663AEF238AF411735ALLySo16CGMutablePathRefaSo6CGRectVFTf4nd_n', symObjAddr: 0x5310, symBinAddr: 0x1EC50, symSize: 0x600 }
  - { offsetInCU: 0x1680, offset: 0x14452, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewCMa', symObjAddr: 0x30D0, symBinAddr: 0x1CA50, symSize: 0x20 }
  - { offsetInCU: 0x1693, offset: 0x14465, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewCfETo', symObjAddr: 0x33A0, symBinAddr: 0x1CD20, symSize: 0x90 }
  - { offsetInCU: 0x16C0, offset: 0x14492, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7dismissyyFyycfU_TA', symObjAddr: 0x35C0, symBinAddr: 0x1CF40, symSize: 0x10 }
  - { offsetInCU: 0x16D3, offset: 0x144A5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC13animateFadeInyyFyycfU_TA', symObjAddr: 0x3AD0, symBinAddr: 0x1D450, symSize: 0x20 }
  - { offsetInCU: 0x16E6, offset: 0x144B8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC13animateFadeInyyFyycfU0_TA', symObjAddr: 0x3F60, symBinAddr: 0x1D8E0, symSize: 0x20 }
  - { offsetInCU: 0x16F9, offset: 0x144CB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC13animateFadeInyyFyycfU1_TA', symObjAddr: 0x4060, symBinAddr: 0x1D9E0, symSize: 0x10 }
  - { offsetInCU: 0x170C, offset: 0x144DE, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x4070, symBinAddr: 0x1D9F0, symSize: 0x20 }
  - { offsetInCU: 0x171F, offset: 0x144F1, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x4090, symBinAddr: 0x1DA10, symSize: 0x10 }
  - { offsetInCU: 0x1732, offset: 0x14504, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC13animateFadeInyyFySbcfU2_TA', symObjAddr: 0x4210, symBinAddr: 0x1DB90, symSize: 0x20 }
  - { offsetInCU: 0x1745, offset: 0x14517, size: 0x8, addend: 0x0, symName: '_$sSbIegy_10ObjectiveC8ObjCBoolVIeyBy_TR', symObjAddr: 0x42E0, symBinAddr: 0x1DC60, symSize: 0x40 }
  - { offsetInCU: 0x178E, offset: 0x14560, size: 0x8, addend: 0x0, symName: '_$ss29getContiguousArrayStorageType3fors01_bcD0CyxGmxm_tlF13FBSDKLoginKit18DeviceLoginManagerC_Tgm5', symObjAddr: 0x45F0, symBinAddr: 0x1DF30, symSize: 0x50 }
  - { offsetInCU: 0x17A5, offset: 0x14577, size: 0x8, addend: 0x0, symName: '_$ss29getContiguousArrayStorageType3fors01_bcD0CyxGmxm_tlFSo10CGColorRefa_Tgm5', symObjAddr: 0x4640, symBinAddr: 0x1DF80, symSize: 0x50 }
  - { offsetInCU: 0x17BC, offset: 0x1458E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC14ArrowDirectionOSHAASQWb', symObjAddr: 0x6420, symBinAddr: 0x1FD60, symSize: 0x10 }
  - { offsetInCU: 0x17CF, offset: 0x145A1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC14ArrowDirectionOAESQAAWl', symObjAddr: 0x6430, symBinAddr: 0x1FD70, symSize: 0x30 }
  - { offsetInCU: 0x17E2, offset: 0x145B4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC10ColorStyleOSHAASQWb', symObjAddr: 0x6460, symBinAddr: 0x1FDA0, symSize: 0x10 }
  - { offsetInCU: 0x17F5, offset: 0x145C7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC10ColorStyleOAESQAAWl', symObjAddr: 0x6470, symBinAddr: 0x1FDB0, symSize: 0x30 }
  - { offsetInCU: 0x1808, offset: 0x145DA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC14ArrowDirectionOMa', symObjAddr: 0x6890, symBinAddr: 0x201D0, symSize: 0x10 }
  - { offsetInCU: 0x181B, offset: 0x145ED, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC10ColorStyleOMa', symObjAddr: 0x68A0, symBinAddr: 0x201E0, symSize: 0x10 }
  - { offsetInCU: 0x182E, offset: 0x14600, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7dismissyyFyycfU_TA.23', symObjAddr: 0x68B0, symBinAddr: 0x201F0, symSize: 0x10 }
  - { offsetInCU: 0x1841, offset: 0x14613, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC13animateFadeInyyFySbcfU2_ySbcfU_TA', symObjAddr: 0x68E0, symBinAddr: 0x20220, symSize: 0x20 }
  - { offsetInCU: 0x1854, offset: 0x14626, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC14animateFadeOut33_1C39B2F52DDA14663AEF238AF411735ALL17completionHandleryyyc_tFyycfU_TA', symObjAddr: 0x6930, symBinAddr: 0x20270, symSize: 0x20 }
  - { offsetInCU: 0x1884, offset: 0x14656, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC14animateFadeOut33_1C39B2F52DDA14663AEF238AF411735ALL17completionHandleryyyc_tFySbcfU0_TA', symObjAddr: 0x6950, symBinAddr: 0x20290, symSize: 0x20 }
  - { offsetInCU: 0x19B4, offset: 0x14786, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC10ColorStyleOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x28C0, symBinAddr: 0x1C240, symSize: 0x10 }
  - { offsetInCU: 0x1A0F, offset: 0x147E1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC10ColorStyleOSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0x2920, symBinAddr: 0x1C2A0, symSize: 0x20 }
  - { offsetInCU: 0x1DA1, offset: 0x14B73, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7tagline7message10colorStyleACSSSg_AgC05ColorH0Otcfc', symObjAddr: 0x30, symBinAddr: 0x199B0, symSize: 0x570 }
  - { offsetInCU: 0x1EED, offset: 0x14CBF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7present2in13arrowPosition9directionySo6UIViewC_So7CGPointVAC14ArrowDirectionOtF', symObjAddr: 0x5A0, symBinAddr: 0x19F20, symSize: 0x120 }
  - { offsetInCU: 0x1FEC, offset: 0x14DBE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC15displayDurationSdvg', symObjAddr: 0x6C0, symBinAddr: 0x1A040, symSize: 0x30 }
  - { offsetInCU: 0x2007, offset: 0x14DD9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC15displayDurationSdvs', symObjAddr: 0x6F0, symBinAddr: 0x1A070, symSize: 0x40 }
  - { offsetInCU: 0x202B, offset: 0x14DFD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC15displayDurationSdvM', symObjAddr: 0x730, symBinAddr: 0x1A0B0, symSize: 0x40 }
  - { offsetInCU: 0x204E, offset: 0x14E20, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC15displayDurationSdvM.resume.0', symObjAddr: 0x770, symBinAddr: 0x1A0F0, symSize: 0x10 }
  - { offsetInCU: 0x206D, offset: 0x14E3F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC10colorStyleAC05ColorF0Ovg', symObjAddr: 0x780, symBinAddr: 0x1A100, symSize: 0x30 }
  - { offsetInCU: 0x209A, offset: 0x14E6C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC10colorStyleAC05ColorF0Ovs', symObjAddr: 0x7B0, symBinAddr: 0x1A130, symSize: 0x40 }
  - { offsetInCU: 0x20D9, offset: 0x14EAB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC10colorStyleAC05ColorF0OvM', symObjAddr: 0x7F0, symBinAddr: 0x1A170, symSize: 0x40 }
  - { offsetInCU: 0x20FC, offset: 0x14ECE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC10colorStyleAC05ColorF0OvM.resume.0', symObjAddr: 0x830, symBinAddr: 0x1A1B0, symSize: 0x30 }
  - { offsetInCU: 0x2166, offset: 0x14F38, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7messageSSSgvs', symObjAddr: 0x880, symBinAddr: 0x1A200, symSize: 0xC0 }
  - { offsetInCU: 0x21D6, offset: 0x14FA8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7messageSSSgvM', symObjAddr: 0x940, symBinAddr: 0x1A2C0, symSize: 0x80 }
  - { offsetInCU: 0x2214, offset: 0x14FE6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7messageSSSgvM.resume.0', symObjAddr: 0x9C0, symBinAddr: 0x1A340, symSize: 0x170 }
  - { offsetInCU: 0x2318, offset: 0x150EA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7taglineSSSgvs', symObjAddr: 0xB90, symBinAddr: 0x1A510, symSize: 0xC0 }
  - { offsetInCU: 0x2388, offset: 0x1515A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7taglineSSSgvM', symObjAddr: 0xC50, symBinAddr: 0x1A5D0, symSize: 0x80 }
  - { offsetInCU: 0x23C6, offset: 0x15198, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7taglineSSSgvM.resume.0', symObjAddr: 0xCD0, symBinAddr: 0x1A650, symSize: 0x170 }
  - { offsetInCU: 0x25AE, offset: 0x15380, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7present4fromySo6UIViewC_tF', symObjAddr: 0xE40, symBinAddr: 0x1A7C0, symSize: 0x2F0 }
  - { offsetInCU: 0x26E7, offset: 0x154B9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7dismissyyF', symObjAddr: 0x1130, symBinAddr: 0x1AAB0, symSize: 0x70 }
  - { offsetInCU: 0x274D, offset: 0x1551F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7dismissyyFyycfU_', symObjAddr: 0x3500, symBinAddr: 0x1CE80, symSize: 0xC0 }
  - { offsetInCU: 0x27D6, offset: 0x155A8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC13animateFadeInyyF', symObjAddr: 0x11A0, symBinAddr: 0x1AB20, symSize: 0x6E0 }
  - { offsetInCU: 0x2923, offset: 0x156F5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC13animateFadeInyyFyycfU_', symObjAddr: 0x3660, symBinAddr: 0x1CFE0, symSize: 0x430 }
  - { offsetInCU: 0x2A1E, offset: 0x157F0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC13animateFadeInyyFyycfU0_', symObjAddr: 0x3AF0, symBinAddr: 0x1D470, symSize: 0x430 }
  - { offsetInCU: 0x2B08, offset: 0x158DA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC13animateFadeInyyFyycfU1_', symObjAddr: 0x3F80, symBinAddr: 0x1D900, symSize: 0xE0 }
  - { offsetInCU: 0x2B35, offset: 0x15907, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC13animateFadeInyyFySbcfU2_', symObjAddr: 0x40A0, symBinAddr: 0x1DA20, symSize: 0x140 }
  - { offsetInCU: 0x2B84, offset: 0x15956, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC13animateFadeInyyFySbcfU2_ySbcfU_', symObjAddr: 0x4230, symBinAddr: 0x1DBB0, symSize: 0xB0 }
  - { offsetInCU: 0x2BCE, offset: 0x159A0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC14animateFadeOut33_1C39B2F52DDA14663AEF238AF411735ALL17completionHandleryyyc_tF', symObjAddr: 0x1880, symBinAddr: 0x1B200, symSize: 0x150 }
  - { offsetInCU: 0x2CA4, offset: 0x15A76, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC12updateColors33_1C39B2F52DDA14663AEF238AF411735ALLyyF', symObjAddr: 0x19D0, symBinAddr: 0x1B350, symSize: 0x260 }
  - { offsetInCU: 0x2EA6, offset: 0x15C78, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC31layoutSubviewsAndDetermineFrame33_1C39B2F52DDA14663AEF238AF411735ALLSo6CGRectVyF', symObjAddr: 0x1C30, symBinAddr: 0x1B5B0, symSize: 0x6F0 }
  - { offsetInCU: 0x31C5, offset: 0x15F97, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC3set33_1C39B2F52DDA14663AEF238AF411735ALL7message7taglineySSSg_AHtF', symObjAddr: 0x2320, symBinAddr: 0x1BCA0, symSize: 0x3B0 }
  - { offsetInCU: 0x33CA, offset: 0x1619C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC24scheduleAutomaticFadeout33_1C39B2F52DDA14663AEF238AF411735ALLyyF', symObjAddr: 0x26D0, symBinAddr: 0x1C050, symSize: 0x130 }
  - { offsetInCU: 0x348E, offset: 0x16260, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC32cancelAllScheduledFadeOutMethods33_1C39B2F52DDA14663AEF238AF411735ALLyyF', symObjAddr: 0x2800, symBinAddr: 0x1C180, symSize: 0x60 }
  - { offsetInCU: 0x34B1, offset: 0x16283, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC14ArrowDirectionO8rawValueSuvg', symObjAddr: 0x2860, symBinAddr: 0x1C1E0, symSize: 0x10 }
  - { offsetInCU: 0x34D6, offset: 0x162A8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC10ColorStyleO8rawValueAESgSu_tcfC', symObjAddr: 0x2890, symBinAddr: 0x1C210, symSize: 0x20 }
  - { offsetInCU: 0x34F1, offset: 0x162C3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC10ColorStyleO8rawValueSuvg', symObjAddr: 0x28B0, symBinAddr: 0x1C230, symSize: 0x10 }
  - { offsetInCU: 0x35CF, offset: 0x163A1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9textLabelSo7UILabelCvg', symObjAddr: 0x3130, symBinAddr: 0x1CAB0, symSize: 0x20 }
  - { offsetInCU: 0x35F0, offset: 0x163C2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewCACycfC', symObjAddr: 0x3150, symBinAddr: 0x1CAD0, symSize: 0x20 }
  - { offsetInCU: 0x3603, offset: 0x163D5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewCACycfc', symObjAddr: 0x3170, symBinAddr: 0x1CAF0, symSize: 0x20 }
  - { offsetInCU: 0x3642, offset: 0x16414, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7tagline7message10colorStyleACSSSg_AgC05ColorH0OtcfC', symObjAddr: 0x31B0, symBinAddr: 0x1CB30, symSize: 0x50 }
  - { offsetInCU: 0x3655, offset: 0x16427, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC5coderACSgSo7NSCoderC_tcfC', symObjAddr: 0x3270, symBinAddr: 0x1CBF0, symSize: 0x40 }
  - { offsetInCU: 0x3668, offset: 0x1643A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC5coderACSgSo7NSCoderC_tcfc', symObjAddr: 0x32B0, symBinAddr: 0x1CC30, symSize: 0x10 }
  - { offsetInCU: 0x3681, offset: 0x16453, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewCfD', symObjAddr: 0x32E0, symBinAddr: 0x1CC60, symSize: 0x50 }
  - { offsetInCU: 0x371A, offset: 0x164EC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC5frameACSo6CGRectV_tcfC', symObjAddr: 0x4510, symBinAddr: 0x1DE50, symSize: 0x80 }
  - { offsetInCU: 0x372D, offset: 0x164FF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC5frameACSo6CGRectV_tcfc', symObjAddr: 0x4590, symBinAddr: 0x1DED0, symSize: 0x30 }
  - { offsetInCU: 0x37A1, offset: 0x16573, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC5coderACSgSo7NSCoderC_tcfcTf4dn_n', symObjAddr: 0x5910, symBinAddr: 0x1F250, symSize: 0x200 }
  - { offsetInCU: 0x3816, offset: 0x165E8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC4drawyySo6CGRectVFTf4dn_n', symObjAddr: 0x5B10, symBinAddr: 0x1F450, symSize: 0x910 }
  - { offsetInCU: 0x2B, offset: 0x16B8D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVyACyAA0d7ManagerdE0CSg_s5Error_pSgtccfC', symObjAddr: 0x0, symBinAddr: 0x203E0, symSize: 0x40 }
  - { offsetInCU: 0xA4, offset: 0x16C06, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0xC0, symBinAddr: 0x204A0, symSize: 0x10 }
  - { offsetInCU: 0xE6, offset: 0x16C48, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVMa', symObjAddr: 0x40, symBinAddr: 0x20420, symSize: 0x30 }
  - { offsetInCU: 0xF9, offset: 0x16C5B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVwCP', symObjAddr: 0xD0, symBinAddr: 0x204B0, symSize: 0x80 }
  - { offsetInCU: 0x10C, offset: 0x16C6E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVwxx', symObjAddr: 0x150, symBinAddr: 0x20530, symSize: 0x40 }
  - { offsetInCU: 0x11F, offset: 0x16C81, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVwcp', symObjAddr: 0x190, symBinAddr: 0x20570, symSize: 0x60 }
  - { offsetInCU: 0x132, offset: 0x16C94, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVwca', symObjAddr: 0x1F0, symBinAddr: 0x205D0, symSize: 0x60 }
  - { offsetInCU: 0x145, offset: 0x16CA7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVwtk', symObjAddr: 0x250, symBinAddr: 0x20630, symSize: 0x50 }
  - { offsetInCU: 0x158, offset: 0x16CBA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVwta', symObjAddr: 0x2A0, symBinAddr: 0x20680, symSize: 0x50 }
  - { offsetInCU: 0x16B, offset: 0x16CCD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVwet', symObjAddr: 0x2F0, symBinAddr: 0x206D0, symSize: 0x20 }
  - { offsetInCU: 0x17E, offset: 0x16CE0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVwst', symObjAddr: 0x370, symBinAddr: 0x20750, symSize: 0x20 }
  - { offsetInCU: 0x191, offset: 0x16CF3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVMr', symObjAddr: 0x3F0, symBinAddr: 0x207D0, symSize: 0x64 }
  - { offsetInCU: 0x28F, offset: 0x16DF1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVyACyAA0d7ManagerdE0CSg_s5Error_pSgtccfC', symObjAddr: 0x0, symBinAddr: 0x203E0, symSize: 0x40 }
  - { offsetInCU: 0x2B8, offset: 0x16E1A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerV14callAsFunctionyyAA0d7ManagerdE0CSg_s5Error_pSgtF', symObjAddr: 0x70, symBinAddr: 0x20450, symSize: 0x40 }
  - { offsetInCU: 0x2F9, offset: 0x16E5B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerV2eeoiySbAC_ACtFZ', symObjAddr: 0xB0, symBinAddr: 0x20490, symSize: 0x10 }
  - { offsetInCU: 0x27, offset: 0x16EEE, size: 0x8, addend: 0x0, symName: '_$sSo20FBSDKInternalUtilityC13FBSDKLoginKit29UserInterfaceElementProvidingA2cDP21topMostViewControllerSo06UIViewL0CSgyFTW', symObjAddr: 0x0, symBinAddr: 0x20840, symSize: 0x20 }
  - { offsetInCU: 0x7E, offset: 0x16F45, size: 0x8, addend: 0x0, symName: '_$sSo20FBSDKInternalUtilityC13FBSDKLoginKit29UserInterfaceElementProvidingA2cDP21topMostViewControllerSo06UIViewL0CSgyFTW', symObjAddr: 0x0, symBinAddr: 0x20840, symSize: 0x20 }
  - { offsetInCU: 0xC5, offset: 0x16F8C, size: 0x8, addend: 0x0, symName: '_$sSo20FBSDKInternalUtilityC13FBSDKLoginKit29UserInterfaceElementProvidingA2cDP14viewController3forSo06UIViewJ0CSgSo0L0C_tFTW', symObjAddr: 0x20, symBinAddr: 0x20860, symSize: 0x30 }
  - { offsetInCU: 0x107, offset: 0x16FCE, size: 0x8, addend: 0x0, symName: '_$sSo20FBSDKInternalUtilityC13FBSDKLoginKit28UserInterfaceStringProvidingA2cDP16bundleForStringsSo8NSBundleCvgTW', symObjAddr: 0x50, symBinAddr: 0x20890, symSize: 0x1E }
  - { offsetInCU: 0x49, offset: 0x170EE, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE17loginButtonDidTapABvpZ', symObjAddr: 0x2838, symBinAddr: 0x61238, symSize: 0x0 }
  - { offsetInCU: 0x63, offset: 0x17108, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE17smartLoginServiceABvpZ', symObjAddr: 0x2840, symBinAddr: 0x61240, symSize: 0x0 }
  - { offsetInCU: 0x7D, offset: 0x17122, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE16sessionAuthStartABvpZ', symObjAddr: 0x2848, symBinAddr: 0x61248, symSize: 0x0 }
  - { offsetInCU: 0x97, offset: 0x1713C, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE14sessionAuthEndABvpZ', symObjAddr: 0x2850, symBinAddr: 0x61250, symSize: 0x0 }
  - { offsetInCU: 0xB1, offset: 0x17156, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE22sessionAuthMethodStartABvpZ', symObjAddr: 0x2858, symBinAddr: 0x61258, symSize: 0x0 }
  - { offsetInCU: 0xCB, offset: 0x17170, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE20sessionAuthMethodEndABvpZ', symObjAddr: 0x2860, symBinAddr: 0x61260, symSize: 0x0 }
  - { offsetInCU: 0xE5, offset: 0x1718A, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE20sessionAuthHeartbeatABvpZ', symObjAddr: 0x2868, symBinAddr: 0x61268, symSize: 0x0 }
  - { offsetInCU: 0xF3, offset: 0x17198, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE17loginButtonDidTap_WZ', symObjAddr: 0x0, symBinAddr: 0x208B0, symSize: 0x30 }
  - { offsetInCU: 0x10C, offset: 0x171B1, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE17loginButtonDidTapABvau', symObjAddr: 0x30, symBinAddr: 0x208E0, symSize: 0x30 }
  - { offsetInCU: 0x12A, offset: 0x171CF, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE17smartLoginService_WZ', symObjAddr: 0x80, symBinAddr: 0x20930, symSize: 0x30 }
  - { offsetInCU: 0x143, offset: 0x171E8, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE17smartLoginServiceABvau', symObjAddr: 0xB0, symBinAddr: 0x20960, symSize: 0x30 }
  - { offsetInCU: 0x161, offset: 0x17206, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE16sessionAuthStart_WZ', symObjAddr: 0x100, symBinAddr: 0x209B0, symSize: 0x30 }
  - { offsetInCU: 0x17A, offset: 0x1721F, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE16sessionAuthStartABvau', symObjAddr: 0x130, symBinAddr: 0x209E0, symSize: 0x30 }
  - { offsetInCU: 0x198, offset: 0x1723D, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE14sessionAuthEnd_WZ', symObjAddr: 0x180, symBinAddr: 0x20A30, symSize: 0x30 }
  - { offsetInCU: 0x1B1, offset: 0x17256, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE14sessionAuthEndABvau', symObjAddr: 0x1B0, symBinAddr: 0x20A60, symSize: 0x30 }
  - { offsetInCU: 0x1CF, offset: 0x17274, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE22sessionAuthMethodStart_WZ', symObjAddr: 0x200, symBinAddr: 0x20AB0, symSize: 0x30 }
  - { offsetInCU: 0x1E8, offset: 0x1728D, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE22sessionAuthMethodStartABvau', symObjAddr: 0x230, symBinAddr: 0x20AE0, symSize: 0x30 }
  - { offsetInCU: 0x206, offset: 0x172AB, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE20sessionAuthMethodEnd_WZ', symObjAddr: 0x280, symBinAddr: 0x20B30, symSize: 0x30 }
  - { offsetInCU: 0x21F, offset: 0x172C4, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE20sessionAuthMethodEndABvau', symObjAddr: 0x2B0, symBinAddr: 0x20B60, symSize: 0x30 }
  - { offsetInCU: 0x23D, offset: 0x172E2, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE20sessionAuthHeartbeat_WZ', symObjAddr: 0x300, symBinAddr: 0x20BB0, symSize: 0x30 }
  - { offsetInCU: 0x256, offset: 0x172FB, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE20sessionAuthHeartbeatABvau', symObjAddr: 0x330, symBinAddr: 0x20BE0, symSize: 0x30 }
  - { offsetInCU: 0x27, offset: 0x173F0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit21LoginCompleterFactoryV06createcD013urlParameters5appIDAA0C10Completing_pSDySSypG_SStF', symObjAddr: 0x0, symBinAddr: 0x20C60, symSize: 0x60 }
  - { offsetInCU: 0xA0, offset: 0x17469, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit21LoginCompleterFactoryVAA0cdE8ProtocolA2aDP06createcD013urlParameters5appIDAA0C10Completing_pSDySSypG_SStFTW', symObjAddr: 0x70, symBinAddr: 0x20CD0, symSize: 0x60 }
  - { offsetInCU: 0xFC, offset: 0x174C5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit21LoginCompleterFactoryVMa', symObjAddr: 0xD0, symBinAddr: 0x20D30, symSize: 0xA }
  - { offsetInCU: 0x1D1, offset: 0x1759A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit21LoginCompleterFactoryV06createcD013urlParameters5appIDAA0C10Completing_pSDySSypG_SStF', symObjAddr: 0x0, symBinAddr: 0x20C60, symSize: 0x60 }
  - { offsetInCU: 0x218, offset: 0x175E1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit21LoginCompleterFactoryVACycfC', symObjAddr: 0x60, symBinAddr: 0x20CC0, symSize: 0x10 }
  - { offsetInCU: 0x8E, offset: 0x176BF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC5nonceSSvgTo', symObjAddr: 0x10, symBinAddr: 0x20D80, symSize: 0x50 }
  - { offsetInCU: 0xDB, offset: 0x1770C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC8trackingAA0C8TrackingOvgTo', symObjAddr: 0x90, symBinAddr: 0x20E00, symSize: 0x20 }
  - { offsetInCU: 0x129, offset: 0x1775A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC20requestedPermissionsShyAA12FBPermissionCGvgTo', symObjAddr: 0xD0, symBinAddr: 0x20E40, symSize: 0x60 }
  - { offsetInCU: 0x176, offset: 0x177A7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC15messengerPageIdSSSgvgTo', symObjAddr: 0x150, symBinAddr: 0x20EC0, symSize: 0x50 }
  - { offsetInCU: 0x1BB, offset: 0x177EC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC8authTypeSo0a4AuthF0aSgvgTo', symObjAddr: 0x1D0, symBinAddr: 0x20F40, symSize: 0x20 }
  - { offsetInCU: 0x20A, offset: 0x1783B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC12codeVerifierAA04CodeF0CvgTo', symObjAddr: 0x220, symBinAddr: 0x20F90, symSize: 0x20 }
  - { offsetInCU: 0x297, offset: 0x178C8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking5nonce15messengerPageIdACSgSaySSG_AA0C8TrackingOS2SSgtcfcTo', symObjAddr: 0x410, symBinAddr: 0x21180, symSize: 0x110 }
  - { offsetInCU: 0x349, offset: 0x1797A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking5nonce15messengerPageId8authTypeACSgSaySSG_AA0C8TrackingOS2SSgSo0a4AuthL0aSgtcfcTo', symObjAddr: 0x690, symBinAddr: 0x21400, symSize: 0xA0 }
  - { offsetInCU: 0x3AC, offset: 0x179DD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking5nonceACSgSaySSG_AA0C8TrackingOSStcfcTo', symObjAddr: 0x860, symBinAddr: 0x215D0, symSize: 0xB0 }
  - { offsetInCU: 0x43E, offset: 0x17A6F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking15messengerPageIdACSgSaySSG_AA0C8TrackingOSSSgtcfcTo', symObjAddr: 0xA70, symBinAddr: 0x217E0, symSize: 0x60 }
  - { offsetInCU: 0x4AB, offset: 0x17ADC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking15messengerPageId8authTypeACSgSaySSG_AA0C8TrackingOSSSgSo0a4AuthK0aSgtcfcTo', symObjAddr: 0xC40, symBinAddr: 0x219B0, symSize: 0x80 }
  - { offsetInCU: 0x52C, offset: 0x17B5D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking5nonce15messengerPageId8authType12codeVerifierACSgSaySSG_AA0C8TrackingOS2SSgSo0a4AuthL0aSgAA04CodeN0CtcfcTo', symObjAddr: 0x14B0, symBinAddr: 0x22220, symSize: 0xB0 }
  - { offsetInCU: 0x585, offset: 0x17BB6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8trackingACSgSaySSG_AA0C8TrackingOtcfcTo', symObjAddr: 0x1720, symBinAddr: 0x22490, symSize: 0xF0 }
  - { offsetInCU: 0x5FD, offset: 0x17C2E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC8trackingACSgAA0C8TrackingO_tcfcTo', symObjAddr: 0x18C0, symBinAddr: 0x22630, symSize: 0x60 }
  - { offsetInCU: 0x66A, offset: 0x17C9B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationCACycfcTo', symObjAddr: 0x1970, symBinAddr: 0x226E0, symSize: 0x30 }
  - { offsetInCU: 0x703, offset: 0x17D34, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking5nonce15messengerPageId8authType12codeVerifierACSgShy09FBSDKCoreB010PermissionOG_AA0C8TrackingOS2SSgSo0a4AuthL0aSgAA04CodeN0CtcfCTf4nnnnnnd_n', symObjAddr: 0x1A40, symBinAddr: 0x227B0, symSize: 0x2A0 }
  - { offsetInCU: 0xBD5, offset: 0x18206, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationCfETo', symObjAddr: 0x19D0, symBinAddr: 0x22740, symSize: 0x70 }
  - { offsetInCU: 0xD65, offset: 0x18396, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationCMa', symObjAddr: 0x1D50, symBinAddr: 0x22A50, symSize: 0x20 }
  - { offsetInCU: 0x1096, offset: 0x186C7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking5nonce15messengerPageId8authType12codeVerifierACSgShy09FBSDKCoreB010PermissionOG_AA0C8TrackingOS2SSgSo0a4AuthL0aSgAA04CodeN0CtcfC', symObjAddr: 0x0, symBinAddr: 0x20D70, symSize: 0x10 }
  - { offsetInCU: 0x10C0, offset: 0x186F1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC5nonceSSvg', symObjAddr: 0x60, symBinAddr: 0x20DD0, symSize: 0x30 }
  - { offsetInCU: 0x10ED, offset: 0x1871E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC8trackingAA0C8TrackingOvg', symObjAddr: 0xB0, symBinAddr: 0x20E20, symSize: 0x20 }
  - { offsetInCU: 0x111A, offset: 0x1874B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC20requestedPermissionsShyAA12FBPermissionCGvg', symObjAddr: 0x130, symBinAddr: 0x20EA0, symSize: 0x20 }
  - { offsetInCU: 0x1147, offset: 0x18778, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC15messengerPageIdSSSgvg', symObjAddr: 0x1A0, symBinAddr: 0x20F10, symSize: 0x30 }
  - { offsetInCU: 0x1174, offset: 0x187A5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC8authTypeSo0a4AuthF0aSgvg', symObjAddr: 0x1F0, symBinAddr: 0x20F60, symSize: 0x30 }
  - { offsetInCU: 0x11A1, offset: 0x187D2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC12codeVerifierAA04CodeF0Cvg', symObjAddr: 0x240, symBinAddr: 0x20FB0, symSize: 0x20 }
  - { offsetInCU: 0x120E, offset: 0x1883F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking5nonce15messengerPageIdACSgSaySSG_AA0C8TrackingOS2SSgtcfC', symObjAddr: 0x260, symBinAddr: 0x20FD0, symSize: 0xE0 }
  - { offsetInCU: 0x1253, offset: 0x18884, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking5nonce15messengerPageIdACSgSaySSG_AA0C8TrackingOS2SSgtcfc', symObjAddr: 0x340, symBinAddr: 0x210B0, symSize: 0xD0 }
  - { offsetInCU: 0x1294, offset: 0x188C5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking5nonce15messengerPageId8authTypeACSgSaySSG_AA0C8TrackingOS2SSgSo0a4AuthL0aSgtcfC', symObjAddr: 0x520, symBinAddr: 0x21290, symSize: 0x60 }
  - { offsetInCU: 0x12AD, offset: 0x188DE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking5nonce15messengerPageId8authTypeACSgSaySSG_AA0C8TrackingOS2SSgSo0a4AuthL0aSgtcfc', symObjAddr: 0x580, symBinAddr: 0x212F0, symSize: 0x110 }
  - { offsetInCU: 0x1362, offset: 0x18993, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking5nonceACSgSaySSG_AA0C8TrackingOSStcfC', symObjAddr: 0x730, symBinAddr: 0x214A0, symSize: 0xA0 }
  - { offsetInCU: 0x13A5, offset: 0x189D6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking5nonceACSgSaySSG_AA0C8TrackingOSStcfc', symObjAddr: 0x7D0, symBinAddr: 0x21540, symSize: 0x90 }
  - { offsetInCU: 0x13DC, offset: 0x18A0D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking15messengerPageIdACSgSaySSG_AA0C8TrackingOSSSgtcfC', symObjAddr: 0x910, symBinAddr: 0x21680, symSize: 0x50 }
  - { offsetInCU: 0x13EF, offset: 0x18A20, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking15messengerPageIdACSgSaySSG_AA0C8TrackingOSSSgtcfc', symObjAddr: 0x960, symBinAddr: 0x216D0, symSize: 0x110 }
  - { offsetInCU: 0x143F, offset: 0x18A70, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking15messengerPageId8authTypeACSgSaySSG_AA0C8TrackingOSSSgSo0a4AuthK0aSgtcfC', symObjAddr: 0xAD0, symBinAddr: 0x21840, symSize: 0x50 }
  - { offsetInCU: 0x1452, offset: 0x18A83, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking15messengerPageId8authTypeACSgSaySSG_AA0C8TrackingOSSSgSo0a4AuthK0aSgtcfc', symObjAddr: 0xB20, symBinAddr: 0x21890, symSize: 0x120 }
  - { offsetInCU: 0x14B1, offset: 0x18AE2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking5nonce15messengerPageId8authType12codeVerifierACSgSaySSG_AA0C8TrackingOS2SSgSo0a4AuthL0aSgAA04CodeN0CtcfC', symObjAddr: 0xCC0, symBinAddr: 0x21A30, symSize: 0x60 }
  - { offsetInCU: 0x1548, offset: 0x18B79, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking5nonce15messengerPageId8authType12codeVerifierACSgSaySSG_AA0C8TrackingOS2SSgSo0a4AuthL0aSgAA04CodeN0Ctcfc', symObjAddr: 0xD20, symBinAddr: 0x21A90, symSize: 0x570 }
  - { offsetInCU: 0x183D, offset: 0x18E6E, size: 0x8, addend: 0x0, symName: '_$sSTsSQ7ElementRpzrlE8containsySbABFSaySo18FBSDKLoginAuthTypeaG_Tg5', symObjAddr: 0x1290, symBinAddr: 0x22000, symSize: 0x170 }
  - { offsetInCU: 0x1A20, offset: 0x19051, size: 0x8, addend: 0x0, symName: '_$sSTsSQ7ElementRpzrlE8containsySbABFSaySSG_Tg5', symObjAddr: 0x1400, symBinAddr: 0x22170, symSize: 0xB0 }
  - { offsetInCU: 0x1BC2, offset: 0x191F3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8trackingACSgSaySSG_AA0C8TrackingOtcfC', symObjAddr: 0x1560, symBinAddr: 0x222D0, symSize: 0xE0 }
  - { offsetInCU: 0x1BFE, offset: 0x1922F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8trackingACSgSaySSG_AA0C8TrackingOtcfc', symObjAddr: 0x1640, symBinAddr: 0x223B0, symSize: 0xE0 }
  - { offsetInCU: 0x1C4A, offset: 0x1927B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC8trackingACSgAA0C8TrackingO_tcfC', symObjAddr: 0x1810, symBinAddr: 0x22580, symSize: 0x60 }
  - { offsetInCU: 0x1C82, offset: 0x192B3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC8trackingACSgAA0C8TrackingO_tcfc', symObjAddr: 0x1870, symBinAddr: 0x225E0, symSize: 0x50 }
  - { offsetInCU: 0x1CA7, offset: 0x192D8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationCACycfC', symObjAddr: 0x1920, symBinAddr: 0x22690, symSize: 0x20 }
  - { offsetInCU: 0x1CBA, offset: 0x192EB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationCACycfc', symObjAddr: 0x1940, symBinAddr: 0x226B0, symSize: 0x30 }
  - { offsetInCU: 0x1D0E, offset: 0x1933F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationCfD', symObjAddr: 0x19A0, symBinAddr: 0x22710, symSize: 0x30 }
  - { offsetInCU: 0x211, offset: 0x1960D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV10Foundation13CustomNSErrorAadEP11errorDomainSSvgZTW', symObjAddr: 0x2B0, symBinAddr: 0x22D50, symSize: 0x50 }
  - { offsetInCU: 0x246, offset: 0x19642, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV10Foundation13CustomNSErrorAadEP9errorCodeSivgTW', symObjAddr: 0x300, symBinAddr: 0x22DA0, symSize: 0x10 }
  - { offsetInCU: 0x276, offset: 0x19672, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV10Foundation13CustomNSErrorAadEP13errorUserInfoSDySSypGvgTW', symObjAddr: 0x310, symBinAddr: 0x22DB0, symSize: 0x10 }
  - { offsetInCU: 0x2A6, offset: 0x196A2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVSHAASH9hashValueSivgTW', symObjAddr: 0x320, symBinAddr: 0x22DC0, symSize: 0x40 }
  - { offsetInCU: 0x381, offset: 0x1977D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0x360, symBinAddr: 0x22E00, symSize: 0x20 }
  - { offsetInCU: 0x3EF, offset: 0x197EB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x420, symBinAddr: 0x22EC0, symSize: 0x30 }
  - { offsetInCU: 0x4A0, offset: 0x1989C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14LoginErrorCodeOSYAASY8rawValuexSg03RawG0Qz_tcfCTW', symObjAddr: 0x500, symBinAddr: 0x22FA0, symSize: 0x30 }
  - { offsetInCU: 0x4CF, offset: 0x198CB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14LoginErrorCodeOSYAASY8rawValue03RawG0QzvgTW', symObjAddr: 0x530, symBinAddr: 0x22FD0, symSize: 0x10 }
  - { offsetInCU: 0x4EB, offset: 0x198E7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14LoginErrorCodeO8rawValueACSgSi_tcfCTf4nd_n', symObjAddr: 0x550, symBinAddr: 0x22FF0, symSize: 0x20 }
  - { offsetInCU: 0x54C, offset: 0x19948, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV10Foundation13CustomNSErrorAAs0D0PWb', symObjAddr: 0x570, symBinAddr: 0x23010, symSize: 0x10 }
  - { offsetInCU: 0x55F, offset: 0x1995B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVACs0D0AAWl', symObjAddr: 0x580, symBinAddr: 0x23020, symSize: 0x30 }
  - { offsetInCU: 0x572, offset: 0x1996E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVSHAASQWb', symObjAddr: 0x5B0, symBinAddr: 0x23050, symSize: 0x10 }
  - { offsetInCU: 0x585, offset: 0x19981, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVACSQAAWl', symObjAddr: 0x5C0, symBinAddr: 0x23060, symSize: 0x30 }
  - { offsetInCU: 0x598, offset: 0x19994, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14LoginErrorCodeOSHAASQWb', symObjAddr: 0x5F0, symBinAddr: 0x23090, symSize: 0x10 }
  - { offsetInCU: 0x5AB, offset: 0x199A7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14LoginErrorCodeOACSQAAWl', symObjAddr: 0x600, symBinAddr: 0x230A0, symSize: 0x30 }
  - { offsetInCU: 0x5BE, offset: 0x199BA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVwxx', symObjAddr: 0x640, symBinAddr: 0x230E0, symSize: 0x30 }
  - { offsetInCU: 0x5D1, offset: 0x199CD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVwca', symObjAddr: 0x6C0, symBinAddr: 0x23160, symSize: 0x60 }
  - { offsetInCU: 0x5E4, offset: 0x199E0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVwta', symObjAddr: 0x740, symBinAddr: 0x231C0, symSize: 0x40 }
  - { offsetInCU: 0x5F7, offset: 0x199F3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVwet', symObjAddr: 0x780, symBinAddr: 0x23200, symSize: 0x50 }
  - { offsetInCU: 0x60A, offset: 0x19A06, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVwst', symObjAddr: 0x7D0, symBinAddr: 0x23250, symSize: 0x40 }
  - { offsetInCU: 0x61D, offset: 0x19A19, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVMa', symObjAddr: 0x810, symBinAddr: 0x23290, symSize: 0x10 }
  - { offsetInCU: 0x630, offset: 0x19A2C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14LoginErrorCodeOMa', symObjAddr: 0x820, symBinAddr: 0x232A0, symSize: 0x10 }
  - { offsetInCU: 0x643, offset: 0x19A3F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVAC10Foundation13CustomNSErrorAAWl', symObjAddr: 0x830, symBinAddr: 0x232B0, symSize: 0x2E }
  - { offsetInCU: 0x6B3, offset: 0x19AAF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0x380, symBinAddr: 0x22E20, symSize: 0x40 }
  - { offsetInCU: 0x74A, offset: 0x19B46, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVs0D0AAsADP9_userInfoyXlSgvgTW', symObjAddr: 0x400, symBinAddr: 0x22EA0, symSize: 0x10 }
  - { offsetInCU: 0x765, offset: 0x19B61, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVs0D0AAsADP19_getEmbeddedNSErroryXlSgyFTW', symObjAddr: 0x410, symBinAddr: 0x22EB0, symSize: 0x10 }
  - { offsetInCU: 0x7E5, offset: 0x19BE1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14LoginErrorCodeOSHAASH9hashValueSivgTW', symObjAddr: 0x460, symBinAddr: 0x22F00, symSize: 0x40 }
  - { offsetInCU: 0x88C, offset: 0x19C88, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14LoginErrorCodeOSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0x4A0, symBinAddr: 0x22F40, symSize: 0x20 }
  - { offsetInCU: 0x8DB, offset: 0x19CD7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14LoginErrorCodeOSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0x4C0, symBinAddr: 0x22F60, symSize: 0x40 }
  - { offsetInCU: 0x960, offset: 0x19D5C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14LoginErrorCodeOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x540, symBinAddr: 0x22FE0, symSize: 0x10 }
  - { offsetInCU: 0x9F3, offset: 0x19DEF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVs0D0AAsADP7_domainSSvgTW', symObjAddr: 0x3C0, symBinAddr: 0x22E60, symSize: 0x20 }
  - { offsetInCU: 0xA0F, offset: 0x19E0B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVs0D0AAsADP5_codeSivgTW', symObjAddr: 0x3E0, symBinAddr: 0x22E80, symSize: 0x20 }
  - { offsetInCU: 0xA97, offset: 0x19E93, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV03_nsD0So7NSErrorCSgvg', symObjAddr: 0x0, symBinAddr: 0x22AA0, symSize: 0x20 }
  - { offsetInCU: 0xAAA, offset: 0x19EA6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV9errorCodeSivg', symObjAddr: 0x20, symBinAddr: 0x22AC0, symSize: 0x10 }
  - { offsetInCU: 0xABD, offset: 0x19EB9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV13errorUserInfoSDySSypGvg', symObjAddr: 0x30, symBinAddr: 0x22AD0, symSize: 0x10 }
  - { offsetInCU: 0xADB, offset: 0x19ED7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV03_nsD0ACSo7NSErrorC_tcfC', symObjAddr: 0x40, symBinAddr: 0x22AE0, symSize: 0xB0 }
  - { offsetInCU: 0xAFE, offset: 0x19EFA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV_8userInfoAcA0cD4CodeO_SDySSypGtcfC', symObjAddr: 0xF0, symBinAddr: 0x22B90, symSize: 0x20 }
  - { offsetInCU: 0xB2E, offset: 0x19F2A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14LoginErrorCodeO8rawValueSivg', symObjAddr: 0x110, symBinAddr: 0x22BB0, symSize: 0x10 }
  - { offsetInCU: 0xB53, offset: 0x19F4F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV11errorDomainSSvgZ', symObjAddr: 0x120, symBinAddr: 0x22BC0, symSize: 0x50 }
  - { offsetInCU: 0xB7A, offset: 0x19F76, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV8reservedAA0cD4CodeOvgZ', symObjAddr: 0x170, symBinAddr: 0x22C10, symSize: 0x10 }
  - { offsetInCU: 0xB9B, offset: 0x19F97, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV7unknownAA0cD4CodeOvgZ', symObjAddr: 0x180, symBinAddr: 0x22C20, symSize: 0x10 }
  - { offsetInCU: 0xBBC, offset: 0x19FB8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV15passwordChangedAA0cD4CodeOvgZ', symObjAddr: 0x190, symBinAddr: 0x22C30, symSize: 0x10 }
  - { offsetInCU: 0xBDD, offset: 0x19FD9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV16userCheckpointedAA0cD4CodeOvgZ', symObjAddr: 0x1A0, symBinAddr: 0x22C40, symSize: 0x10 }
  - { offsetInCU: 0xBFE, offset: 0x19FFA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV12userMismatchAA0cD4CodeOvgZ', symObjAddr: 0x1B0, symBinAddr: 0x22C50, symSize: 0x10 }
  - { offsetInCU: 0xC1F, offset: 0x1A01B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV15unconfirmedUserAA0cD4CodeOvgZ', symObjAddr: 0x1C0, symBinAddr: 0x22C60, symSize: 0x10 }
  - { offsetInCU: 0xC40, offset: 0x1A03C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV24systemAccountAppDisabledAA0cD4CodeOvgZ', symObjAddr: 0x1D0, symBinAddr: 0x22C70, symSize: 0x10 }
  - { offsetInCU: 0xC61, offset: 0x1A05D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV24systemAccountUnavailableAA0cD4CodeOvgZ', symObjAddr: 0x1E0, symBinAddr: 0x22C80, symSize: 0x10 }
  - { offsetInCU: 0xC82, offset: 0x1A07E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV18badChallengeStringAA0cD4CodeOvgZ', symObjAddr: 0x1F0, symBinAddr: 0x22C90, symSize: 0x10 }
  - { offsetInCU: 0xCA3, offset: 0x1A09F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV14invalidIDTokenAA0cD4CodeOvgZ', symObjAddr: 0x200, symBinAddr: 0x22CA0, symSize: 0x10 }
  - { offsetInCU: 0xCC4, offset: 0x1A0C0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV18missingAccessTokenAA0cD4CodeOvgZ', symObjAddr: 0x210, symBinAddr: 0x22CB0, symSize: 0x10 }
  - { offsetInCU: 0xCE5, offset: 0x1A0E1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV2eeoiySbAC_ACtFZ', symObjAddr: 0x220, symBinAddr: 0x22CC0, symSize: 0x30 }
  - { offsetInCU: 0xD54, offset: 0x1A150, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV4hash4intoys6HasherVz_tF', symObjAddr: 0x250, symBinAddr: 0x22CF0, symSize: 0x20 }
  - { offsetInCU: 0xDC6, offset: 0x1A1C2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV9hashValueSivg', symObjAddr: 0x270, symBinAddr: 0x22D10, symSize: 0x40 }
  - { offsetInCU: 0xEFF, offset: 0x1A2FB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14LoginErrorCodeO8rawValueACSgSi_tcfC', symObjAddr: 0x450, symBinAddr: 0x22EF0, symSize: 0x10 }
  - { offsetInCU: 0x27, offset: 0x1A367, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16LoginErrorDomain_WZ', symObjAddr: 0x0, symBinAddr: 0x232E0, symSize: 0x30 }
  - { offsetInCU: 0x49, offset: 0x1A389, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16LoginErrorDomainSSvp', symObjAddr: 0x78, symBinAddr: 0x5F190, symSize: 0x0 }
  - { offsetInCU: 0x57, offset: 0x1A397, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16LoginErrorDomain_WZ', symObjAddr: 0x0, symBinAddr: 0x232E0, symSize: 0x30 }
  - { offsetInCU: 0x92, offset: 0x1A3D2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16LoginErrorDomainSSvg', symObjAddr: 0x30, symBinAddr: 0x23310, symSize: 0x47 }
  - { offsetInCU: 0xC1, offset: 0x1A562, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC15defaultAudienceAA07DefaultF0OvgTo', symObjAddr: 0x5A0, symBinAddr: 0x23930, symSize: 0x40 }
  - { offsetInCU: 0x114, offset: 0x1A5B5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC15defaultAudienceAA07DefaultF0OvsTo', symObjAddr: 0x610, symBinAddr: 0x239A0, symSize: 0x40 }
  - { offsetInCU: 0x1D6, offset: 0x1A677, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC13configurationAA0C13ConfigurationCSgvgTo', symObjAddr: 0x800, symBinAddr: 0x23B50, symSize: 0x40 }
  - { offsetInCU: 0x228, offset: 0x1A6C9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC13configurationAA0C13ConfigurationCSgvsTo', symObjAddr: 0x880, symBinAddr: 0x23BD0, symSize: 0x60 }
  - { offsetInCU: 0x27C, offset: 0x1A71D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC20requestedPermissionsShyAA12FBPermissionCGSgvgTo', symObjAddr: 0x8E0, symBinAddr: 0x23C30, symSize: 0x90 }
  - { offsetInCU: 0x2CE, offset: 0x1A76F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC20requestedPermissionsShyAA12FBPermissionCGSgvsTo', symObjAddr: 0x9B0, symBinAddr: 0x23D00, symSize: 0x90 }
  - { offsetInCU: 0x3A5, offset: 0x1A846, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC17usedSafariSessionSbvgTo', symObjAddr: 0xC40, symBinAddr: 0x23F90, symSize: 0x40 }
  - { offsetInCU: 0x3F7, offset: 0x1A898, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC17usedSafariSessionSbvsTo', symObjAddr: 0xCB0, symBinAddr: 0x24000, symSize: 0x40 }
  - { offsetInCU: 0x465, offset: 0x1A906, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC012isPerformingC0SbvgTo', symObjAddr: 0xD70, symBinAddr: 0x240C0, symSize: 0x40 }
  - { offsetInCU: 0x596, offset: 0x1AA37, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC15defaultAudienceAcA07DefaultF0O_tcfcTo', symObjAddr: 0x15A0, symBinAddr: 0x248F0, symSize: 0x50 }
  - { offsetInCU: 0x65D, offset: 0x1AAFE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5logIn4from13configuration10completionySo16UIViewControllerCSg_AA0C13ConfigurationCSgyAA0cdC6ResultCSg_s5Error_pSgtctFTo', symObjAddr: 0x1A10, symBinAddr: 0x24D60, symSize: 0xB0 }
  - { offsetInCU: 0x6E1, offset: 0x1AB82, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5logIn11permissions4from7handlerySaySSG_So16UIViewControllerCSgyAA0cdC6ResultCSg_s5Error_pSgtcSgtFTo', symObjAddr: 0x21E0, symBinAddr: 0x25530, symSize: 0xD0 }
  - { offsetInCU: 0x724, offset: 0x1ABC5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5logIn33_C218275A97333B874EDDFE627110566CLLyyFySb_s5Error_pSgtcfU_', symObjAddr: 0x23C0, symBinAddr: 0x25710, symSize: 0x1E0 }
  - { offsetInCU: 0x8CA, offset: 0x1AD6B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC21reauthorizeDataAccess4from7handlerySo16UIViewControllerC_yAA0cdC6ResultCSg_s5Error_pSgtctFTo', symObjAddr: 0x3BD0, symBinAddr: 0x26F20, symSize: 0x80 }
  - { offsetInCU: 0x913, offset: 0x1ADB4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC6logOutyyFTo', symObjAddr: 0x3E00, symBinAddr: 0x27150, symSize: 0x30 }
  - { offsetInCU: 0xA22, offset: 0x1AEC3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC22completeAuthentication10parameters15expectChallengeyAA01_C20CompletionParametersC_SbtFTo', symObjAddr: 0x5560, symBinAddr: 0x288B0, symSize: 0x60 }
  - { offsetInCU: 0xAAD, offset: 0x1AF4E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC15logInParameters13configuration12loggingToken20authenticationMethodSDyS2SGSgAA0C13ConfigurationCSg_SSSgSStFTo', symObjAddr: 0x7550, symBinAddr: 0x2A8A0, symSize: 0xF0 }
  - { offsetInCU: 0xAE4, offset: 0x1AF85, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC24validateReauthentication11accessToken11loginResult04userH5NonceySo011FBSDKAccessH0C_AA0cdcJ0CSgSSSgtFTo', symObjAddr: 0x7AE0, symBinAddr: 0x2AE30, symSize: 0xA0 }
  - { offsetInCU: 0xB1B, offset: 0x1AFBC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18storeExpectedNonceyySSSgFTo', symObjAddr: 0x7EE0, symBinAddr: 0x2B230, symSize: 0x60 }
  - { offsetInCU: 0xB4B, offset: 0x1AFEC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCACycfcTo', symObjAddr: 0x8130, symBinAddr: 0x2B480, symSize: 0x20 }
  - { offsetInCU: 0xB7B, offset: 0x1B01C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCAA0C9ProvidingA2aDP15defaultAudienceAA07DefaultG0OvgTW', symObjAddr: 0x88F0, symBinAddr: 0x2BC40, symSize: 0x40 }
  - { offsetInCU: 0xBB3, offset: 0x1B054, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCAA0C9ProvidingA2aDP15defaultAudienceAA07DefaultG0OvsTW', symObjAddr: 0x8930, symBinAddr: 0x2BC80, symSize: 0x40 }
  - { offsetInCU: 0xBF2, offset: 0x1B093, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCAA0C9ProvidingA2aDP15defaultAudienceAA07DefaultG0OvMTW', symObjAddr: 0x8970, symBinAddr: 0x2BCC0, symSize: 0x40 }
  - { offsetInCU: 0xC2C, offset: 0x1B0CD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCAA0C9ProvidingA2aDP5logIn14viewController13configuration10completionySo06UIViewI0CSg_AA0C13ConfigurationCSgyAA0C6ResultOctFTW', symObjAddr: 0x89B0, symBinAddr: 0x2BD00, symSize: 0x80 }
  - { offsetInCU: 0xC93, offset: 0x1B134, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCAA0C9ProvidingA2aDP5logIn11permissions4from7handlerySaySSG_So16UIViewControllerCSgyAA0cdC6ResultCSg_s5Error_pSgtcSgtFTW', symObjAddr: 0x8A30, symBinAddr: 0x2BD80, symSize: 0x20 }
  - { offsetInCU: 0xCAE, offset: 0x1B14F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCAA0C9ProvidingA2aDP6logOutyyFTW', symObjAddr: 0x8A50, symBinAddr: 0x2BDA0, symSize: 0x20 }
  - { offsetInCU: 0xCC9, offset: 0x1B16A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCAA17DependentAsObjectA2aDP22configuredDependencies0gI0QzSgvgTW', symObjAddr: 0x8E10, symBinAddr: 0x2C160, symSize: 0x40 }
  - { offsetInCU: 0xCFB, offset: 0x1B19C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCAA17DependentAsObjectA2aDP22configuredDependencies0gI0QzSgvsTW', symObjAddr: 0x8E50, symBinAddr: 0x2C1A0, symSize: 0x60 }
  - { offsetInCU: 0xD3D, offset: 0x1B1DE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCAA17DependentAsObjectA2aDP22configuredDependencies0gI0QzSgvMTW', symObjAddr: 0x8EB0, symBinAddr: 0x2C200, symSize: 0x40 }
  - { offsetInCU: 0xD77, offset: 0x1B218, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCAA17DependentAsObjectA2aDP19defaultDependencies0gI0QzSgvgTW', symObjAddr: 0x8EF0, symBinAddr: 0x2C240, symSize: 0x10 }
  - { offsetInCU: 0xDD5, offset: 0x1B276, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC21reauthorizeDataAccess4from7handlerySo16UIViewControllerC_yAA0cdC6ResultCSg_s5Error_pSgtctF04$s13a6Kit012cd2C6l13CSgSo7NSErrorq11IeyByy_ADs5M12_pSgIeggg_TRAKSo0S0CSgIeyByy_Tf1ncn_nTf4dng_n', symObjAddr: 0xED20, symBinAddr: 0x31FC0, symSize: 0x550 }
  - { offsetInCU: 0x14C6, offset: 0x1B967, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC19defaultDependenciesAC06ObjectF0VSgvpACTk', symObjAddr: 0xFB0, symBinAddr: 0x24300, symSize: 0x90 }
  - { offsetInCU: 0x157B, offset: 0x1BA1C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultCSgSo7NSErrorCSgIeyByy_ADs5Error_pSgIeggg_TR', symObjAddr: 0x1AC0, symBinAddr: 0x24E10, symSize: 0x50 }
  - { offsetInCU: 0x15B7, offset: 0x1BA58, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultCSgs5Error_pSgytIegnnr_AdFIeggg_TR', symObjAddr: 0x1C00, symBinAddr: 0x24F50, symSize: 0x60 }
  - { offsetInCU: 0x1A48, offset: 0x1BEE9, size: 0x8, addend: 0x0, symName: '_$sSbs5Error_pSgIegyg_10ObjectiveC8ObjCBoolVSo7NSErrorCSgIeyByy_TR', symObjAddr: 0x7BA0, symBinAddr: 0x2AEF0, symSize: 0x60 }
  - { offsetInCU: 0x1A73, offset: 0x1BF14, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCfETo', symObjAddr: 0x8180, symBinAddr: 0x2B4D0, symSize: 0xA0 }
  - { offsetInCU: 0x1AB7, offset: 0x1BF58, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC10makeOpenerACyFZTo', symObjAddr: 0x8240, symBinAddr: 0x2B590, symSize: 0x30 }
  - { offsetInCU: 0x1B4F, offset: 0x1BFF0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC11application_4open17sourceApplication10annotationSbSo13UIApplicationCSg_10Foundation3URLVSgSSSgypSgtFTo', symObjAddr: 0x8290, symBinAddr: 0x2B5E0, symSize: 0x160 }
  - { offsetInCU: 0x1B81, offset: 0x1C022, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC7canOpen_3for17sourceApplication10annotationSb10Foundation3URLV_So13UIApplicationCSgSSSgypSgtFTo', symObjAddr: 0x83F0, symBinAddr: 0x2B740, symSize: 0x130 }
  - { offsetInCU: 0x1BCF, offset: 0x1C070, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC26applicationDidBecomeActiveyySo13UIApplicationCFTo', symObjAddr: 0x8560, symBinAddr: 0x2B8B0, symSize: 0x70 }
  - { offsetInCU: 0x1C63, offset: 0x1C104, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC19isAuthenticationURLySb10Foundation0G0VFTo', symObjAddr: 0x8620, symBinAddr: 0x2B970, symSize: 0xB0 }
  - { offsetInCU: 0x1CC4, offset: 0x1C165, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC21shouldStopPropagation2ofSb10Foundation3URLV_tFTo', symObjAddr: 0x8860, symBinAddr: 0x2BBB0, symSize: 0x90 }
  - { offsetInCU: 0x1CDF, offset: 0x1C180, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit29LoginCompleterFactoryProtocol_pWOb', symObjAddr: 0x8F30, symBinAddr: 0x2C280, symSize: 0x20 }
  - { offsetInCU: 0x1CF2, offset: 0x1C193, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5logIn14viewController13configuration10completionySo06UIViewH0CSg_AA0C13ConfigurationCSgyAA0C6ResultOctFyAA0cdcM0CSg_s5Error_pSgtcfU_TA', symObjAddr: 0x8F70, symBinAddr: 0x2C2C0, symSize: 0x20 }
  - { offsetInCU: 0x1D05, offset: 0x1C1A6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5logIn11permissions14viewController10completionySay09FBSDKCoreB010PermissionOG_So06UIViewI0CSgyAA0C6ResultOcSgtFyAA0cdcN0CSg_s5Error_pSgtcfU_TA', symObjAddr: 0x8FC0, symBinAddr: 0x2C310, symSize: 0x20 }
  - { offsetInCU: 0x1D18, offset: 0x1C1B9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesVWOh', symObjAddr: 0x8FE0, symBinAddr: 0x2C330, symSize: 0x20 }
  - { offsetInCU: 0x1DC6, offset: 0x1C267, size: 0x8, addend: 0x0, symName: '_$ss22_ContiguousArrayBufferV19_uninitializedCount15minimumCapacityAByxGSi_SitcfC13FBSDKLoginKit18DeviceLoginManagerC_Tgm5', symObjAddr: 0x9580, symBinAddr: 0x2C8A0, symSize: 0x80 }
  - { offsetInCU: 0x1E25, offset: 0x1C2C6, size: 0x8, addend: 0x0, symName: '_$ss22_ContiguousArrayBufferV19_uninitializedCount15minimumCapacityAByxGSi_SitcfCSS_Tgm5', symObjAddr: 0x9600, symBinAddr: 0x2C920, symSize: 0x80 }
  - { offsetInCU: 0x1FAB, offset: 0x1C44C, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV_8capacityAByxGs07__CocoaB0Vn_SitcfC13FBSDKLoginKit12FBPermissionC_Tgm5', symObjAddr: 0x9E10, symBinAddr: 0x2D130, symSize: 0x200 }
  - { offsetInCU: 0x23ED, offset: 0x1C88E, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV19genericIntersectionyAByxGqd__7ElementQyd__RszSTRd__lFADs13_UnsafeBitsetVXEfU_13FBSDKLoginKit12FBPermissionC_ShyAKGTg5', symObjAddr: 0xCF80, symBinAddr: 0x302A0, symSize: 0x4C0 }
  - { offsetInCU: 0x253E, offset: 0x1C9DF, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV12intersectionyAByxGADFADs13_UnsafeBitsetVXEfU_13FBSDKLoginKit12FBPermissionC_Tg5', symObjAddr: 0xD440, symBinAddr: 0x30760, symSize: 0x5B0 }
  - { offsetInCU: 0x2642, offset: 0x1CAE3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC24validateReauthentication11accessToken11loginResult04userH5NonceySo011FBSDKAccessH0C_AA0cdcJ0CSgSSSgtFySo27FBSDKGraphRequestConnecting_pSg_ypSgs5Error_pSgtcfU_TA', symObjAddr: 0xE220, symBinAddr: 0x31510, symSize: 0x40 }
  - { offsetInCU: 0x2655, offset: 0x1CAF6, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0xE260, symBinAddr: 0x31550, symSize: 0x20 }
  - { offsetInCU: 0x2668, offset: 0x1CB09, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0xE280, symBinAddr: 0x31570, symSize: 0x10 }
  - { offsetInCU: 0x274A, offset: 0x1CBEB, size: 0x8, addend: 0x0, symName: '_$sSTsE6reduce4into_qd__qd__n_yqd__z_7ElementQztKXEtKlFSDyS2SSgG_s17_NativeDictionaryVyS2SGTg5051$sSD16compactMapValuesySDyxqd__Gqd__Sgq_KXEKlFys17_dE44Vyxqd__Gz_x3key_q_5valuettKXEfU_SS_SSSgSSTG5xq_Sgs5Error_pr0_lyAESSIsgnrzo_Tf1ncn_nTf4nng_n', symObjAddr: 0xE290, symBinAddr: 0x31580, symSize: 0x3F0 }
  - { offsetInCU: 0x29B5, offset: 0x1CE56, size: 0x8, addend: 0x0, symName: '_$sSh21_nonEmptyArrayLiteralShyxGSayxG_tcfC13FBSDKLoginKit12FBPermissionC_Tgm5Tf4g_n', symObjAddr: 0xE8D0, symBinAddr: 0x31B70, symSize: 0x450 }
  - { offsetInCU: 0x2BAB, offset: 0x1D04C, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV12intersectionyAByxGADF13FBSDKLoginKit12FBPermissionC_Tg5Tf4ng_n', symObjAddr: 0xF270, symBinAddr: 0x32510, symSize: 0x140 }
  - { offsetInCU: 0x2CE7, offset: 0x1D188, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV19genericIntersectionyAByxGqd__7ElementQyd__RszSTRd__lF13FBSDKLoginKit12FBPermissionC_ShyAIGTg5Tf4ng_n', symObjAddr: 0xF3B0, symBinAddr: 0x32650, symSize: 0x140 }
  - { offsetInCU: 0x2E55, offset: 0x1D2F6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit29LoginCompleterFactoryProtocol_pWOc', symObjAddr: 0xFAF0, symBinAddr: 0x32D90, symSize: 0x30 }
  - { offsetInCU: 0x2E68, offset: 0x1D309, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCMU', symObjAddr: 0xFB50, symBinAddr: 0x32DF0, symSize: 0x10 }
  - { offsetInCU: 0x2E7B, offset: 0x1D31C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCMa', symObjAddr: 0xFB60, symBinAddr: 0x32E00, symSize: 0x30 }
  - { offsetInCU: 0x2E8E, offset: 0x1D32F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCMr', symObjAddr: 0xFB90, symBinAddr: 0x32E30, symSize: 0xC0 }
  - { offsetInCU: 0x2EA1, offset: 0x1D342, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVSgMa', symObjAddr: 0xFC50, symBinAddr: 0x32EF0, symSize: 0x50 }
  - { offsetInCU: 0x2EB4, offset: 0x1D355, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesVwCP', symObjAddr: 0xFCA0, symBinAddr: 0x32F40, symSize: 0x30 }
  - { offsetInCU: 0x2EC7, offset: 0x1D368, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesVwxx', symObjAddr: 0xFCD0, symBinAddr: 0x32F70, symSize: 0x50 }
  - { offsetInCU: 0x2EDA, offset: 0x1D37B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesVwcp', symObjAddr: 0xFD20, symBinAddr: 0x32FC0, symSize: 0xD0 }
  - { offsetInCU: 0x2EED, offset: 0x1D38E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesVwca', symObjAddr: 0xFDF0, symBinAddr: 0x33090, symSize: 0xE0 }
  - { offsetInCU: 0x2F00, offset: 0x1D3A1, size: 0x8, addend: 0x0, symName: ___swift_memcpy112_8, symObjAddr: 0x10000, symBinAddr: 0x33170, symSize: 0x40 }
  - { offsetInCU: 0x2F13, offset: 0x1D3B4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesVwta', symObjAddr: 0x10040, symBinAddr: 0x331B0, symSize: 0xB0 }
  - { offsetInCU: 0x2F26, offset: 0x1D3C7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesVwet', symObjAddr: 0x100F0, symBinAddr: 0x33260, symSize: 0x40 }
  - { offsetInCU: 0x2F39, offset: 0x1D3DA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesVwst', symObjAddr: 0x10130, symBinAddr: 0x332A0, symSize: 0x60 }
  - { offsetInCU: 0x2F4C, offset: 0x1D3ED, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesVMa', symObjAddr: 0x10190, symBinAddr: 0x33300, symSize: 0x10 }
  - { offsetInCU: 0x2F5F, offset: 0x1D400, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultCSgSo7NSErrorCSgIeyByy_ADs5Error_pSgIeggg_TRTA', symObjAddr: 0x101C0, symBinAddr: 0x33330, symSize: 0x10 }
  - { offsetInCU: 0x2F72, offset: 0x1D413, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultCSgs5Error_pSgIeggg_SgWOe', symObjAddr: 0x101D0, symBinAddr: 0x33340, symSize: 0x20 }
  - { offsetInCU: 0x2F85, offset: 0x1D426, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC11application_4open17sourceApplication10annotationSbSo13UIApplicationCSg_10Foundation3URLVSgSSSgypSgtFyAA01_C20CompletionParametersCcfU_TA', symObjAddr: 0x102B0, symBinAddr: 0x333C0, symSize: 0x20 }
  - { offsetInCU: 0x2FBC, offset: 0x1D45D, size: 0x8, addend: 0x0, symName: '_$sSh5IndexV8_VariantOyx__GSHRzlWOy', symObjAddr: 0x102F0, symBinAddr: 0x333E0, symSize: 0x20 }
  - { offsetInCU: 0x2FCF, offset: 0x1D470, size: 0x8, addend: 0x0, symName: '_$sSD8IteratorV8_VariantOyxq___GSHRzr0_lWOe', symObjAddr: 0x10310, symBinAddr: 0x33400, symSize: 0x20 }
  - { offsetInCU: 0x2FE2, offset: 0x1D483, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVWOb', symObjAddr: 0x10330, symBinAddr: 0x33420, symSize: 0x30 }
  - { offsetInCU: 0x2FF5, offset: 0x1D496, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVWOh', symObjAddr: 0x10360, symBinAddr: 0x33450, symSize: 0x30 }
  - { offsetInCU: 0x3012, offset: 0x1D4B3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultCSgs5Error_pSgIeggg_AdFytIegnnr_TRTA', symObjAddr: 0x10440, symBinAddr: 0x334E0, symSize: 0x20 }
  - { offsetInCU: 0x303A, offset: 0x1D4DB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultCSgs5Error_pSgytIegnnr_AdFIeggg_TRTA', symObjAddr: 0x10460, symBinAddr: 0x33500, symSize: 0x20 }
  - { offsetInCU: 0x304D, offset: 0x1D4EE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultCSgs5Error_pSgIeggg_SgWOy', symObjAddr: 0x10480, symBinAddr: 0x33520, symSize: 0x20 }
  - { offsetInCU: 0x3060, offset: 0x1D501, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5logIn33_C218275A97333B874EDDFE627110566CLLyyFySb_s5Error_pSgtcfU_TA', symObjAddr: 0x104A0, symBinAddr: 0x33540, symSize: 0x10 }
  - { offsetInCU: 0x307D, offset: 0x1D51E, size: 0x8, addend: 0x0, symName: '_$sSbs5Error_pSgytIegnnr_SbABIegyg_TRTA', symObjAddr: 0x104B0, symBinAddr: 0x33550, symSize: 0x50 }
  - { offsetInCU: 0x30AF, offset: 0x1D550, size: 0x8, addend: 0x0, symName: '_$sSbs5Error_pSgIegyg_SbABytIegnnr_TRTA', symObjAddr: 0x10500, symBinAddr: 0x335A0, symSize: 0x20 }
  - { offsetInCU: 0x314C, offset: 0x1D5ED, size: 0x8, addend: 0x0, symName: '_$sSlsE3mapySayqd__Gqd__7ElementQzKXEKlFSay12FBSDKCoreKit10PermissionOG_SSTg5085$s13FBSDKLoginKit12LoginManagerC5logIn11permissions14viewController10completionySay09c4B010E91OG_So06UIViewI0CSgyAA0C6ResultOcSgtFSSAJcfu_32f5ef5a0b9ade21eb65ffea7b618f60adAJSSTf3nnpk_nTf1cn_n', symObjAddr: 0x170, symBinAddr: 0x23500, symSize: 0x110 }
  - { offsetInCU: 0x3308, offset: 0x1D7A9, size: 0x8, addend: 0x0, symName: '_$sSlsE3mapySayqd__Gqd__7ElementQzKXEKlFShy13FBSDKLoginKit12FBPermissionCG_SSTg504$s13c132Kit12LoginManagerC15logInParameters13configuration12loggingToken20authenticationMethodSDyS2SGSgAA0C13ConfigurationCSg_SSSgSStFSSAA12E52Ccfu_32e0d58b938ad0b6cb17de1b825049cc00AOSSTf3nnpk_nTf1cn_n', symObjAddr: 0x280, symBinAddr: 0x23610, symSize: 0x320 }
  - { offsetInCU: 0x3EE9, offset: 0x1E38A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCACycfC', symObjAddr: 0x150, symBinAddr: 0x234E0, symSize: 0x20 }
  - { offsetInCU: 0x3F3E, offset: 0x1E3DF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5logIn11permissions14viewController10completionySay09FBSDKCoreB010PermissionOG_So06UIViewI0CSgyAA0C6ResultOcSgtF', symObjAddr: 0x22B0, symBinAddr: 0x25600, symSize: 0x90 }
  - { offsetInCU: 0x3F9E, offset: 0x1E43F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5logIn11permissions14viewController10completionySay09FBSDKCoreB010PermissionOG_So06UIViewI0CSgyAA0C6ResultOcSgtFyAA0cdcN0CSg_s5Error_pSgtcfU_', symObjAddr: 0x2340, symBinAddr: 0x25690, symSize: 0x80 }
  - { offsetInCU: 0x404A, offset: 0x1E4EB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC15logInParameters13configuration12loggingToken20authenticationMethodSDyS2SGSgAA0C13ConfigurationCSg_SSSgSStF', symObjAddr: 0x55C0, symBinAddr: 0x28910, symSize: 0x1D10 }
  - { offsetInCU: 0x4ED0, offset: 0x1F371, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC15logInParameters13configuration12loggingToken20authenticationMethodSDyS2SGSgAA0C13ConfigurationCSg_SSSgSStFA2MXEfU_', symObjAddr: 0x72D0, symBinAddr: 0x2A620, symSize: 0x30 }
  - { offsetInCU: 0x4F38, offset: 0x1F3D9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC15defaultAudienceAA07DefaultF0Ovg', symObjAddr: 0x5E0, symBinAddr: 0x23970, symSize: 0x30 }
  - { offsetInCU: 0x4F75, offset: 0x1F416, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC15defaultAudienceAA07DefaultF0Ovs', symObjAddr: 0x650, symBinAddr: 0x239E0, symSize: 0x40 }
  - { offsetInCU: 0x4F99, offset: 0x1F43A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC15defaultAudienceAA07DefaultF0OvM', symObjAddr: 0x690, symBinAddr: 0x23A20, symSize: 0x40 }
  - { offsetInCU: 0x4FB6, offset: 0x1F457, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC15defaultAudienceAA07DefaultF0OvM.resume.0', symObjAddr: 0x6D0, symBinAddr: 0x23A60, symSize: 0x10 }
  - { offsetInCU: 0x4FD5, offset: 0x1F476, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC7handlerAA010IdentifiedC13ResultHandlerVSgvg', symObjAddr: 0x6E0, symBinAddr: 0x23A70, symSize: 0x40 }
  - { offsetInCU: 0x4FF8, offset: 0x1F499, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC7handlerAA010IdentifiedC13ResultHandlerVSgvs', symObjAddr: 0x760, symBinAddr: 0x23AB0, symSize: 0x60 }
  - { offsetInCU: 0x502A, offset: 0x1F4CB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC7handlerAA010IdentifiedC13ResultHandlerVSgvM', symObjAddr: 0x7C0, symBinAddr: 0x23B10, symSize: 0x40 }
  - { offsetInCU: 0x505F, offset: 0x1F500, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC13configurationAA0C13ConfigurationCSgvg', symObjAddr: 0x840, symBinAddr: 0x23B90, symSize: 0x40 }
  - { offsetInCU: 0x50AE, offset: 0x1F54F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC20requestedPermissionsShyAA12FBPermissionCGSgvg', symObjAddr: 0x970, symBinAddr: 0x23CC0, symSize: 0x40 }
  - { offsetInCU: 0x50EB, offset: 0x1F58C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC20requestedPermissionsShyAA12FBPermissionCGSgvM', symObjAddr: 0xA60, symBinAddr: 0x23DB0, symSize: 0x40 }
  - { offsetInCU: 0x510E, offset: 0x1F5AF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC6loggerAA0cD6LoggerCSgvg', symObjAddr: 0xAA0, symBinAddr: 0x23DF0, symSize: 0x40 }
  - { offsetInCU: 0x512F, offset: 0x1F5D0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC6loggerAA0cD6LoggerCSgvM', symObjAddr: 0xB50, symBinAddr: 0x23EA0, symSize: 0x40 }
  - { offsetInCU: 0x5152, offset: 0x1F5F3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5stateAA0cD5StateOvg', symObjAddr: 0xB90, symBinAddr: 0x23EE0, symSize: 0x30 }
  - { offsetInCU: 0x5173, offset: 0x1F614, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5stateAA0cD5StateOvs', symObjAddr: 0xBC0, symBinAddr: 0x23F10, symSize: 0x40 }
  - { offsetInCU: 0x51A3, offset: 0x1F644, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5stateAA0cD5StateOvM', symObjAddr: 0xC00, symBinAddr: 0x23F50, symSize: 0x40 }
  - { offsetInCU: 0x51D8, offset: 0x1F679, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC17usedSafariSessionSbvg', symObjAddr: 0xC80, symBinAddr: 0x23FD0, symSize: 0x30 }
  - { offsetInCU: 0x5233, offset: 0x1F6D4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC17usedSafariSessionSbvs', symObjAddr: 0xCF0, symBinAddr: 0x24040, symSize: 0x40 }
  - { offsetInCU: 0x5257, offset: 0x1F6F8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC17usedSafariSessionSbvM', symObjAddr: 0xD30, symBinAddr: 0x24080, symSize: 0x40 }
  - { offsetInCU: 0x528C, offset: 0x1F72D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC012isPerformingC0Sbvg', symObjAddr: 0xDB0, symBinAddr: 0x24100, symSize: 0x40 }
  - { offsetInCU: 0x52CF, offset: 0x1F770, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC22configuredDependenciesAC06ObjectF0VSgvg', symObjAddr: 0xDF0, symBinAddr: 0x24140, symSize: 0x40 }
  - { offsetInCU: 0x52EC, offset: 0x1F78D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC22configuredDependenciesAC06ObjectF0VSgvs', symObjAddr: 0xE30, symBinAddr: 0x24180, symSize: 0x60 }
  - { offsetInCU: 0x5312, offset: 0x1F7B3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC22configuredDependenciesAC06ObjectF0VSgvM', symObjAddr: 0xE90, symBinAddr: 0x241E0, symSize: 0x40 }
  - { offsetInCU: 0x532F, offset: 0x1F7D0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC19defaultDependenciesAC06ObjectF0VSgvg', symObjAddr: 0xED0, symBinAddr: 0x24220, symSize: 0xE0 }
  - { offsetInCU: 0x5351, offset: 0x1F7F2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC19defaultDependenciesAC06ObjectF0VSgvgAGyXEfU_', symObjAddr: 0x10B0, symBinAddr: 0x24400, symSize: 0x2B0 }
  - { offsetInCU: 0x54A7, offset: 0x1F948, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC19defaultDependenciesAC06ObjectF0VSgvs', symObjAddr: 0x1040, symBinAddr: 0x24390, symSize: 0x70 }
  - { offsetInCU: 0x5545, offset: 0x1F9E6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV17accessTokenWallet014authenticationhI012errorFactory012graphRequestL015internalUtility13keychainStore014loginCompleterL015profileProvider8settings9urlOpenerAESo011FBSDKAccessH9Providing_pXp_So019FBSDKAuthenticationH9Providing_pXpSo18FBSDKErrorCreating_pSo010FBSDKGraphnL0_pSo27FBSDKAppAvailabilityChecker_So26FBSDKAppURLSchemeProvidingSo15FBSDKURLHostingpSo013FBSDKKeychainR0_pAA0ctL8Protocol_p09FBSDKCoreB016ProfileProviding_pXpAY16SettingsProtocol_pSo14FBSDKURLOpener_ptcfC', symObjAddr: 0x1360, symBinAddr: 0x246B0, symSize: 0x50 }
  - { offsetInCU: 0x5558, offset: 0x1F9F9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC19defaultDependenciesAC06ObjectF0VSgvM', symObjAddr: 0x13B0, symBinAddr: 0x24700, symSize: 0x40 }
  - { offsetInCU: 0x5579, offset: 0x1FA1A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC19defaultDependenciesAC06ObjectF0VSgvM.resume.0', symObjAddr: 0x13F0, symBinAddr: 0x24740, symSize: 0x100 }
  - { offsetInCU: 0x55F9, offset: 0x1FA9A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC15defaultAudienceAcA07DefaultF0O_tcfC', symObjAddr: 0x14F0, symBinAddr: 0x24840, symSize: 0x60 }
  - { offsetInCU: 0x563B, offset: 0x1FADC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC15defaultAudienceAcA07DefaultF0O_tcfc', symObjAddr: 0x1550, symBinAddr: 0x248A0, symSize: 0x50 }
  - { offsetInCU: 0x56C9, offset: 0x1FB6A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5logIn4from13configuration10completionySo16UIViewControllerCSg_AA0C13ConfigurationCSgyAA0cdC6ResultCSg_s5Error_pSgtctF', symObjAddr: 0x15F0, symBinAddr: 0x24940, symSize: 0x100 }
  - { offsetInCU: 0x576B, offset: 0x1FC0C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC11commonLogIn33_C218275A97333B874EDDFE627110566CLL4from13configuration10completionySo16UIViewControllerCSg_AA0C13ConfigurationCSgyAA0cdC6ResultCSg_s5Error_pSgtcSgtF', symObjAddr: 0x16F0, symBinAddr: 0x24A40, symSize: 0x320 }
  - { offsetInCU: 0x5896, offset: 0x1FD37, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5logIn14viewController13configuration10completionySo06UIViewH0CSg_AA0C13ConfigurationCSgyAA0C6ResultOctF', symObjAddr: 0x1B10, symBinAddr: 0x24E60, symSize: 0x80 }
  - { offsetInCU: 0x58E4, offset: 0x1FD85, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5logIn14viewController13configuration10completionySo06UIViewH0CSg_AA0C13ConfigurationCSgyAA0C6ResultOctFyAA0cdcM0CSg_s5Error_pSgtcfU_', symObjAddr: 0x1B90, symBinAddr: 0x24EE0, symSize: 0x70 }
  - { offsetInCU: 0x59D4, offset: 0x1FE75, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC13invokeHandler33_C218275A97333B874EDDFE627110566CLL6result5erroryAA0cdC6ResultCSg_s5Error_pSgtF', symObjAddr: 0x1C60, symBinAddr: 0x24FB0, symSize: 0x3B0 }
  - { offsetInCU: 0x5AF0, offset: 0x1FF91, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5logIn11permissions4from7handlerySaySSG_So16UIViewControllerCSgyAA0cdC6ResultCSg_s5Error_pSgtcSgtF', symObjAddr: 0x2010, symBinAddr: 0x25360, symSize: 0x1D0 }
  - { offsetInCU: 0x5CBA, offset: 0x2015B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC27handleImplicitCancelOfLogIn33_C218275A97333B874EDDFE627110566CLLyyF', symObjAddr: 0x25A0, symBinAddr: 0x258F0, symSize: 0x160 }
  - { offsetInCU: 0x5DF3, offset: 0x20294, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC19performBrowserLogIn33_C218275A97333B874EDDFE627110566CLL7handleryySb_s5Error_pSgtcSg_tF', symObjAddr: 0x2700, symBinAddr: 0x25A50, symSize: 0xFF0 }
  - { offsetInCU: 0x6124, offset: 0x205C5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC19performBrowserLogIn33_C218275A97333B874EDDFE627110566CLL7handleryySb_s5Error_pSgtcSg_tFySb_AHtcyKXEfu1_ySb_AHtcfU_', symObjAddr: 0x7B80, symBinAddr: 0x2AED0, symSize: 0x10 }
  - { offsetInCU: 0x613F, offset: 0x205E0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC19performBrowserLogIn33_C218275A97333B874EDDFE627110566CLL7handleryySb_s5Error_pSgtcSg_tFySb_AHtcyKXEfu2_ySb_AHtcfU0_', symObjAddr: 0x7B90, symBinAddr: 0x2AEE0, symSize: 0x10 }
  - { offsetInCU: 0x6199, offset: 0x2063A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC21reauthorizeDataAccess4from7handlerySo16UIViewControllerC_yAA0cdC6ResultCSg_s5Error_pSgtctF', symObjAddr: 0x36F0, symBinAddr: 0x26A40, symSize: 0x4E0 }
  - { offsetInCU: 0x6311, offset: 0x207B2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC6logOutyyF', symObjAddr: 0x3C50, symBinAddr: 0x26FA0, symSize: 0x1B0 }
  - { offsetInCU: 0x6471, offset: 0x20912, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC22completeAuthentication10parameters15expectChallengeyAA01_C20CompletionParametersC_SbtF', symObjAddr: 0x3E30, symBinAddr: 0x27180, symSize: 0x580 }
  - { offsetInCU: 0x65DF, offset: 0x20A80, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC22storeExpectedChallenge33_C218275A97333B874EDDFE627110566CLLyySSSgF', symObjAddr: 0x43B0, symBinAddr: 0x27700, symSize: 0x100 }
  - { offsetInCU: 0x663F, offset: 0x20AE0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC16getSuccessResult33_C218275A97333B874EDDFE627110566CLL4fromAA0cdcG0CAA01_C20CompletionParametersC_tF', symObjAddr: 0x44B0, symBinAddr: 0x27800, symSize: 0x7F0 }
  - { offsetInCU: 0x6CC1, offset: 0x21162, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC24validateReauthentication11accessToken11loginResult04userH5NonceySo011FBSDKAccessH0C_AA0cdcJ0CSgSSSgtF', symObjAddr: 0x4CA0, symBinAddr: 0x27FF0, symSize: 0x4A0 }
  - { offsetInCU: 0x6E5A, offset: 0x212FB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC24validateReauthentication11accessToken11loginResult04userH5NonceySo011FBSDKAccessH0C_AA0cdcJ0CSgSSSgtFySo27FBSDKGraphRequestConnecting_pSg_ypSgs5Error_pSgtcfU_', symObjAddr: 0x7760, symBinAddr: 0x2AAB0, symSize: 0x380 }
  - { offsetInCU: 0x6F51, offset: 0x213F2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18getCancelledResult33_C218275A97333B874EDDFE627110566CLL4fromAA0cdcG0CAA01_C20CompletionParametersC_tF', symObjAddr: 0x5140, symBinAddr: 0x28490, symSize: 0x1B0 }
  - { offsetInCU: 0x7093, offset: 0x21534, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC19setGlobalProperties33_C218275A97333B874EDDFE627110566CLL10parameters11loginResultyAA01_C20CompletionParametersC_AA0cdcN0CSgtF', symObjAddr: 0x52F0, symBinAddr: 0x28640, symSize: 0x270 }
  - { offsetInCU: 0x728D, offset: 0x2172E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC010addLimitedC14ShimParameters33_C218275A97333B874EDDFE627110566CLL10parameters13configurationySDyS2SGz_AA0C13ConfigurationCtF', symObjAddr: 0x7300, symBinAddr: 0x2A650, symSize: 0x120 }
  - { offsetInCU: 0x7372, offset: 0x21813, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC25storeExpectedCodeVerifier33_C218275A97333B874EDDFE627110566CLLyyAA0gH0CSgF', symObjAddr: 0x7640, symBinAddr: 0x2A990, symSize: 0x120 }
  - { offsetInCU: 0x73CB, offset: 0x2186C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC29getRecentlyGrantedPermissions4fromShyAA12FBPermissionCGAH_tF', symObjAddr: 0x7C00, symBinAddr: 0x2AF50, symSize: 0x150 }
  - { offsetInCU: 0x7489, offset: 0x2192A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC30getRecentlyDeclinedPermissions4fromShyAA12FBPermissionCGAH_tF', symObjAddr: 0x7D50, symBinAddr: 0x2B0A0, symSize: 0xA0 }
  - { offsetInCU: 0x7520, offset: 0x219C1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCACycfc', symObjAddr: 0x7FF0, symBinAddr: 0x2B340, symSize: 0x140 }
  - { offsetInCU: 0x7543, offset: 0x219E4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCfD', symObjAddr: 0x8150, symBinAddr: 0x2B4A0, symSize: 0x30 }
  - { offsetInCU: 0x756A, offset: 0x21A0B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC10makeOpenerACyFZ', symObjAddr: 0x8220, symBinAddr: 0x2B570, symSize: 0x20 }
  - { offsetInCU: 0x75B4, offset: 0x21A55, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC11application_4open17sourceApplication10annotationSbSo13UIApplicationCSg_10Foundation3URLVSgSSSgypSgtF', symObjAddr: 0x8270, symBinAddr: 0x2B5C0, symSize: 0x10 }
  - { offsetInCU: 0x75F1, offset: 0x21A92, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC7canOpen_3for17sourceApplication10annotationSb10Foundation3URLV_So13UIApplicationCSgSSSgypSgtF', symObjAddr: 0x8280, symBinAddr: 0x2B5D0, symSize: 0x10 }
  - { offsetInCU: 0x7610, offset: 0x21AB1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC26applicationDidBecomeActiveyySo13UIApplicationCF', symObjAddr: 0x8520, symBinAddr: 0x2B870, symSize: 0x40 }
  - { offsetInCU: 0x769D, offset: 0x21B3E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC19isAuthenticationURLySb10Foundation0G0VF', symObjAddr: 0x85D0, symBinAddr: 0x2B920, symSize: 0x50 }
  - { offsetInCU: 0x76E6, offset: 0x21B87, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC21shouldStopPropagation2ofSb10Foundation3URLV_tF', symObjAddr: 0x86D0, symBinAddr: 0x2BA20, symSize: 0x190 }
  - { offsetInCU: 0x7843, offset: 0x21CE4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV17accessTokenWalletSo011FBSDKAccessH9Providing_pXpvg', symObjAddr: 0x8A70, symBinAddr: 0x2BDC0, symSize: 0x10 }
  - { offsetInCU: 0x7856, offset: 0x21CF7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV17accessTokenWalletSo011FBSDKAccessH9Providing_pXpvs', symObjAddr: 0x8A80, symBinAddr: 0x2BDD0, symSize: 0x10 }
  - { offsetInCU: 0x7869, offset: 0x21D0A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV17accessTokenWalletSo011FBSDKAccessH9Providing_pXpvM', symObjAddr: 0x8A90, symBinAddr: 0x2BDE0, symSize: 0x10 }
  - { offsetInCU: 0x787C, offset: 0x21D1D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV17accessTokenWalletSo011FBSDKAccessH9Providing_pXpvM.resume.0', symObjAddr: 0x8AA0, symBinAddr: 0x2BDF0, symSize: 0x10 }
  - { offsetInCU: 0x788F, offset: 0x21D30, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV25authenticationTokenWalletSo019FBSDKAuthenticationH9Providing_pXpvg', symObjAddr: 0x8AB0, symBinAddr: 0x2BE00, symSize: 0x10 }
  - { offsetInCU: 0x78A2, offset: 0x21D43, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV25authenticationTokenWalletSo019FBSDKAuthenticationH9Providing_pXpvs', symObjAddr: 0x8AC0, symBinAddr: 0x2BE10, symSize: 0x10 }
  - { offsetInCU: 0x78B5, offset: 0x21D56, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV25authenticationTokenWalletSo019FBSDKAuthenticationH9Providing_pXpvM', symObjAddr: 0x8AD0, symBinAddr: 0x2BE20, symSize: 0x20 }
  - { offsetInCU: 0x78C8, offset: 0x21D69, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV25authenticationTokenWalletSo019FBSDKAuthenticationH9Providing_pXpvM.resume.0', symObjAddr: 0x8AF0, symBinAddr: 0x2BE40, symSize: 0x10 }
  - { offsetInCU: 0x78DB, offset: 0x21D7C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV12errorFactorySo18FBSDKErrorCreating_pvg', symObjAddr: 0x8B00, symBinAddr: 0x2BE50, symSize: 0x10 }
  - { offsetInCU: 0x78EE, offset: 0x21D8F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV12errorFactorySo18FBSDKErrorCreating_pvs', symObjAddr: 0x8B10, symBinAddr: 0x2BE60, symSize: 0x20 }
  - { offsetInCU: 0x7901, offset: 0x21DA2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV12errorFactorySo18FBSDKErrorCreating_pvM', symObjAddr: 0x8B30, symBinAddr: 0x2BE80, symSize: 0x20 }
  - { offsetInCU: 0x7914, offset: 0x21DB5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV12errorFactorySo18FBSDKErrorCreating_pvM.resume.0', symObjAddr: 0x8B50, symBinAddr: 0x2BEA0, symSize: 0x10 }
  - { offsetInCU: 0x7927, offset: 0x21DC8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV19graphRequestFactorySo010FBSDKGraphhI0_pvg', symObjAddr: 0x8B60, symBinAddr: 0x2BEB0, symSize: 0x10 }
  - { offsetInCU: 0x793A, offset: 0x21DDB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV19graphRequestFactorySo010FBSDKGraphhI0_pvs', symObjAddr: 0x8B70, symBinAddr: 0x2BEC0, symSize: 0x20 }
  - { offsetInCU: 0x794D, offset: 0x21DEE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV19graphRequestFactorySo010FBSDKGraphhI0_pvM', symObjAddr: 0x8B90, symBinAddr: 0x2BEE0, symSize: 0x20 }
  - { offsetInCU: 0x7960, offset: 0x21E01, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV19graphRequestFactorySo010FBSDKGraphhI0_pvM.resume.0', symObjAddr: 0x8BB0, symBinAddr: 0x2BF00, symSize: 0x10 }
  - { offsetInCU: 0x7973, offset: 0x21E14, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV15internalUtilitySo27FBSDKAppAvailabilityChecker_So0I18URLSchemeProvidingSo15FBSDKURLHostingpvg', symObjAddr: 0x8BC0, symBinAddr: 0x2BF10, symSize: 0x10 }
  - { offsetInCU: 0x7986, offset: 0x21E27, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV15internalUtilitySo27FBSDKAppAvailabilityChecker_So0I18URLSchemeProvidingSo15FBSDKURLHostingpvs', symObjAddr: 0x8BD0, symBinAddr: 0x2BF20, symSize: 0x20 }
  - { offsetInCU: 0x7999, offset: 0x21E3A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV15internalUtilitySo27FBSDKAppAvailabilityChecker_So0I18URLSchemeProvidingSo15FBSDKURLHostingpvM', symObjAddr: 0x8BF0, symBinAddr: 0x2BF40, symSize: 0x20 }
  - { offsetInCU: 0x79AC, offset: 0x21E4D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV15internalUtilitySo27FBSDKAppAvailabilityChecker_So0I18URLSchemeProvidingSo15FBSDKURLHostingpvM.resume.0', symObjAddr: 0x8C10, symBinAddr: 0x2BF60, symSize: 0x10 }
  - { offsetInCU: 0x79BF, offset: 0x21E60, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV13keychainStoreSo013FBSDKKeychainH0_pvg', symObjAddr: 0x8C20, symBinAddr: 0x2BF70, symSize: 0x10 }
  - { offsetInCU: 0x79D2, offset: 0x21E73, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV13keychainStoreSo013FBSDKKeychainH0_pvs', symObjAddr: 0x8C30, symBinAddr: 0x2BF80, symSize: 0x20 }
  - { offsetInCU: 0x79E5, offset: 0x21E86, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV13keychainStoreSo013FBSDKKeychainH0_pvM', symObjAddr: 0x8C50, symBinAddr: 0x2BFA0, symSize: 0x20 }
  - { offsetInCU: 0x79F8, offset: 0x21E99, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV13keychainStoreSo013FBSDKKeychainH0_pvM.resume.0', symObjAddr: 0x8C70, symBinAddr: 0x2BFC0, symSize: 0x10 }
  - { offsetInCU: 0x7A0B, offset: 0x21EAC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV21loginCompleterFactoryAA0chI8Protocol_pvg', symObjAddr: 0x8C80, symBinAddr: 0x2BFD0, symSize: 0x20 }
  - { offsetInCU: 0x7A1E, offset: 0x21EBF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV21loginCompleterFactoryAA0chI8Protocol_pvs', symObjAddr: 0x8CA0, symBinAddr: 0x2BFF0, symSize: 0x30 }
  - { offsetInCU: 0x7A31, offset: 0x21ED2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV21loginCompleterFactoryAA0chI8Protocol_pvM', symObjAddr: 0x8CD0, symBinAddr: 0x2C020, symSize: 0x20 }
  - { offsetInCU: 0x7A44, offset: 0x21EE5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV21loginCompleterFactoryAA0chI8Protocol_pvM.resume.0', symObjAddr: 0x8CF0, symBinAddr: 0x2C040, symSize: 0x10 }
  - { offsetInCU: 0x7A57, offset: 0x21EF8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV15profileProvider09FBSDKCoreB016ProfileProviding_pXpvg', symObjAddr: 0x8D00, symBinAddr: 0x2C050, symSize: 0x10 }
  - { offsetInCU: 0x7A6A, offset: 0x21F0B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV15profileProvider09FBSDKCoreB016ProfileProviding_pXpvs', symObjAddr: 0x8D10, symBinAddr: 0x2C060, symSize: 0x10 }
  - { offsetInCU: 0x7A7D, offset: 0x21F1E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV15profileProvider09FBSDKCoreB016ProfileProviding_pXpvM', symObjAddr: 0x8D20, symBinAddr: 0x2C070, symSize: 0x20 }
  - { offsetInCU: 0x7A90, offset: 0x21F31, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV15profileProvider09FBSDKCoreB016ProfileProviding_pXpvM.resume.0', symObjAddr: 0x8D40, symBinAddr: 0x2C090, symSize: 0x10 }
  - { offsetInCU: 0x7AA3, offset: 0x21F44, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV8settings09FBSDKCoreB016SettingsProtocol_pvg', symObjAddr: 0x8D50, symBinAddr: 0x2C0A0, symSize: 0x10 }
  - { offsetInCU: 0x7AB6, offset: 0x21F57, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV8settings09FBSDKCoreB016SettingsProtocol_pvs', symObjAddr: 0x8D60, symBinAddr: 0x2C0B0, symSize: 0x20 }
  - { offsetInCU: 0x7AC9, offset: 0x21F6A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV8settings09FBSDKCoreB016SettingsProtocol_pvM', symObjAddr: 0x8D80, symBinAddr: 0x2C0D0, symSize: 0x20 }
  - { offsetInCU: 0x7ADC, offset: 0x21F7D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV8settings09FBSDKCoreB016SettingsProtocol_pvM.resume.0', symObjAddr: 0x8DA0, symBinAddr: 0x2C0F0, symSize: 0x10 }
  - { offsetInCU: 0x7AEF, offset: 0x21F90, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV9urlOpenerSo14FBSDKURLOpener_pvg', symObjAddr: 0x8DB0, symBinAddr: 0x2C100, symSize: 0x10 }
  - { offsetInCU: 0x7B02, offset: 0x21FA3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV9urlOpenerSo14FBSDKURLOpener_pvs', symObjAddr: 0x8DC0, symBinAddr: 0x2C110, symSize: 0x20 }
  - { offsetInCU: 0x7B15, offset: 0x21FB6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV9urlOpenerSo14FBSDKURLOpener_pvM', symObjAddr: 0x8DE0, symBinAddr: 0x2C130, symSize: 0x20 }
  - { offsetInCU: 0x7B28, offset: 0x21FC9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV9urlOpenerSo14FBSDKURLOpener_pvM.resume.0', symObjAddr: 0x8E00, symBinAddr: 0x2C150, symSize: 0x10 }
  - { offsetInCU: 0x7B6A, offset: 0x2200B, size: 0x8, addend: 0x0, symName: '_$sSo16FBSDKAccessTokenC11tokenString11permissions19declinedPermissions07expiredG05appID04userJ014expirationDate07refreshM0020dataAccessExpirationM0ABSS_SaySSGA2LS2S10Foundation0M0VSgA2PtcfcTO', symObjAddr: 0x9030, symBinAddr: 0x2C350, symSize: 0x200 }
  - { offsetInCU: 0x7B89, offset: 0x2202A, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV7_insert2at3key5valueys10_HashTableV6BucketV_xnq_ntFSS_ypTg5', symObjAddr: 0x9230, symBinAddr: 0x2C550, symSize: 0x60 }
  - { offsetInCU: 0x7BF5, offset: 0x22096, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV7_insert2at3key5valueys10_HashTableV6BucketV_xnq_ntFSo26FBSDKAppEventParameterNamea_ypTg5', symObjAddr: 0x9290, symBinAddr: 0x2C5B0, symSize: 0x60 }
  - { offsetInCU: 0x7C55, offset: 0x220F6, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV7_insert2at3key5valueys10_HashTableV6BucketV_xnq_ntFSS_SSTg5', symObjAddr: 0x92F0, symBinAddr: 0x2C610, symSize: 0x50 }
  - { offsetInCU: 0x7CE6, offset: 0x22187, size: 0x8, addend: 0x0, symName: '_$sSh8_VariantV6insertySb8inserted_x17memberAfterInserttxnF13FBSDKLoginKit12FBPermissionC_Tg5', symObjAddr: 0x9680, symBinAddr: 0x2C9A0, symSize: 0x350 }
  - { offsetInCU: 0x7DB0, offset: 0x22251, size: 0x8, addend: 0x0, symName: '_$sSh8_VariantV6insertySb8inserted_x17memberAfterInserttxnFSS_Tg5', symObjAddr: 0x99D0, symBinAddr: 0x2CCF0, symSize: 0x210 }
  - { offsetInCU: 0x7E7D, offset: 0x2231E, size: 0x8, addend: 0x0, symName: '_$sSh8_VariantV6insertySb8inserted_x17memberAfterInserttxnF12FBSDKCoreKit10PermissionO_Tg5', symObjAddr: 0x9BE0, symBinAddr: 0x2CF00, symSize: 0x230 }
  - { offsetInCU: 0x7ED5, offset: 0x22376, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV9insertNew_2at8isUniqueyxn_s10_HashTableV6BucketVSbtF13FBSDKLoginKit12FBPermissionC_Tg5', symObjAddr: 0xA010, symBinAddr: 0x2D330, symSize: 0x1C0 }
  - { offsetInCU: 0x7F55, offset: 0x223F6, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV9insertNew_2at8isUniqueyxn_s10_HashTableV6BucketVSbtFSS_Tg5', symObjAddr: 0xA1D0, symBinAddr: 0x2D4F0, symSize: 0x190 }
  - { offsetInCU: 0x7FF7, offset: 0x22498, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV9insertNew_2at8isUniqueyxn_s10_HashTableV6BucketVSbtF12FBSDKCoreKit10PermissionO_Tg5', symObjAddr: 0xA360, symBinAddr: 0x2D680, symSize: 0x200 }
  - { offsetInCU: 0x8043, offset: 0x224E4, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV4copyyyF13FBSDKLoginKit12FBPermissionC_Tg5', symObjAddr: 0xA560, symBinAddr: 0x2D880, symSize: 0x1F0 }
  - { offsetInCU: 0x80AE, offset: 0x2254F, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV4copyyyFSS_Tg5', symObjAddr: 0xA750, symBinAddr: 0x2DA70, symSize: 0x200 }
  - { offsetInCU: 0x8119, offset: 0x225BA, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV4copyyyF12FBSDKCoreKit10PermissionO_Tg5', symObjAddr: 0xA950, symBinAddr: 0x2DC70, symSize: 0x260 }
  - { offsetInCU: 0x815E, offset: 0x225FF, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV13copyAndResize8capacityySi_tF13FBSDKLoginKit12FBPermissionC_Tg5', symObjAddr: 0xABB0, symBinAddr: 0x2DED0, symSize: 0x2B0 }
  - { offsetInCU: 0x81FA, offset: 0x2269B, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV13copyAndResize8capacityySi_tFSS_Tg5', symObjAddr: 0xAE60, symBinAddr: 0x2E180, symSize: 0x2F0 }
  - { offsetInCU: 0x829A, offset: 0x2273B, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV13copyAndResize8capacityySi_tF12FBSDKCoreKit10PermissionO_Tg5', symObjAddr: 0xB150, symBinAddr: 0x2E470, symSize: 0x330 }
  - { offsetInCU: 0x8303, offset: 0x227A4, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV6resize8capacityySi_tF13FBSDKLoginKit12FBPermissionC_Tg5', symObjAddr: 0xB480, symBinAddr: 0x2E7A0, symSize: 0x300 }
  - { offsetInCU: 0x83C5, offset: 0x22866, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV6resize8capacityySi_tFSS_Tg5', symObjAddr: 0xB780, symBinAddr: 0x2EAA0, symSize: 0x330 }
  - { offsetInCU: 0x849B, offset: 0x2293C, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV6resize8capacityySi_tF12FBSDKCoreKit10PermissionO_Tg5', symObjAddr: 0xBAB0, symBinAddr: 0x2EDD0, symSize: 0x3A0 }
  - { offsetInCU: 0x8516, offset: 0x229B7, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV16_unsafeInsertNewyyxnF13FBSDKLoginKit12FBPermissionC_Tg5', symObjAddr: 0xBE50, symBinAddr: 0x2F170, symSize: 0x60 }
  - { offsetInCU: 0x856F, offset: 0x22A10, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV12mutatingFind_8isUniques10_HashTableV6BucketV6bucket_Sb5foundtx_SbtFSo26FBSDKAppEventParameterNamea_ypTg5', symObjAddr: 0xBEB0, symBinAddr: 0x2F1D0, symSize: 0xB0 }
  - { offsetInCU: 0x85BB, offset: 0x22A5C, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV20_copyOrMoveAndResize8capacity12moveElementsySi_SbtFSo26FBSDKAppEventParameterNamea_ypTg5', symObjAddr: 0xC050, symBinAddr: 0x2F370, symSize: 0x3E0 }
  - { offsetInCU: 0x8691, offset: 0x22B32, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV20_copyOrMoveAndResize8capacity12moveElementsySi_SbtFSS_ypTg5', symObjAddr: 0xC430, symBinAddr: 0x2F750, symSize: 0x3C0 }
  - { offsetInCU: 0x8767, offset: 0x22C08, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV20_copyOrMoveAndResize8capacity12moveElementsySi_SbtFSS_SSTg5', symObjAddr: 0xC7F0, symBinAddr: 0x2FB10, symSize: 0x3D0 }
  - { offsetInCU: 0x8843, offset: 0x22CE4, size: 0x8, addend: 0x0, symName: '_$sSh8_VariantV12intersectionys10_NativeSetVyxGShyxGF13FBSDKLoginKit12FBPermissionC_Tg5', symObjAddr: 0xCBC0, symBinAddr: 0x2FEE0, symSize: 0x3C0 }
  - { offsetInCU: 0x89A3, offset: 0x22E44, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV13extractSubset5using5countAByxGs13_UnsafeBitsetV_SitF13FBSDKLoginKit12FBPermissionC_Tg5', symObjAddr: 0xD9F0, symBinAddr: 0x30D10, symSize: 0x270 }
  - { offsetInCU: 0x8A4C, offset: 0x22EED, size: 0x8, addend: 0x0, symName: '_$sShyxSh5IndexVyx_Gcig13FBSDKLoginKit12FBPermissionC_Tg5', symObjAddr: 0xDC60, symBinAddr: 0x30F80, symSize: 0x300 }
  - { offsetInCU: 0x8AD6, offset: 0x22F77, size: 0x8, addend: 0x0, symName: '_$sSTsE21_copySequenceContents12initializing8IteratorQz_SitSry7ElementQzG_tFShySSG_Tg5', symObjAddr: 0xDF60, symBinAddr: 0x31280, symSize: 0x200 }
  - { offsetInCU: 0x8C13, offset: 0x230B4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5logIn33_C218275A97333B874EDDFE627110566CLL11permissions7handleryShyAA12FBPermissionCG_yAA0cdC6ResultCSg_s5Error_pSgtcSgtFTf4dnn_n', symObjAddr: 0xE6D0, symBinAddr: 0x31970, symSize: 0x200 }
  - { offsetInCU: 0x8D1B, offset: 0x231BC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC7canOpen_3for17sourceApplication10annotationSb10Foundation3URLV_So13UIApplicationCSgSSSgypSgtFTf4nddnn_n', symObjAddr: 0xF4F0, symBinAddr: 0x32790, symSize: 0x190 }
  - { offsetInCU: 0x8E26, offset: 0x232C7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC11application_4open17sourceApplication10annotationSbSo13UIApplicationCSg_10Foundation3URLVSgSSSgypSgtFTf4dndnn_n', symObjAddr: 0xF680, symBinAddr: 0x32920, symSize: 0x470 }
  - { offsetInCU: 0x4E, offset: 0x235D8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO10identifierSo013FBSDKAppEventF4NameavpZ', symObjAddr: 0x67C0, symBinAddr: 0x5F298, symSize: 0x0 }
  - { offsetInCU: 0x68, offset: 0x235F2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO9timestampSo013FBSDKAppEventF4NameavpZ', symObjAddr: 0x67C8, symBinAddr: 0x5F2A0, symSize: 0x0 }
  - { offsetInCU: 0x82, offset: 0x2360C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO6resultSo013FBSDKAppEventF4NameavpZ', symObjAddr: 0x67D0, symBinAddr: 0x5F2A8, symSize: 0x0 }
  - { offsetInCU: 0x9C, offset: 0x23626, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO10authMethodSo013FBSDKAppEventF4NameavpZ', symObjAddr: 0x67D8, symBinAddr: 0x5F2B0, symSize: 0x0 }
  - { offsetInCU: 0xB6, offset: 0x23640, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO9errorCodeSo013FBSDKAppEventF4NameavpZ', symObjAddr: 0x67E0, symBinAddr: 0x5F2B8, symSize: 0x0 }
  - { offsetInCU: 0xD0, offset: 0x2365A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO12errorMessageSo013FBSDKAppEventF4NameavpZ', symObjAddr: 0x67E8, symBinAddr: 0x5F2C0, symSize: 0x0 }
  - { offsetInCU: 0xEA, offset: 0x23674, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO6extrasSo013FBSDKAppEventF4NameavpZ', symObjAddr: 0x67F0, symBinAddr: 0x5F2C8, symSize: 0x0 }
  - { offsetInCU: 0x104, offset: 0x2368E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO12loggingTokenSo013FBSDKAppEventF4NameavpZ', symObjAddr: 0x67F8, symBinAddr: 0x5F2D0, symSize: 0x0 }
  - { offsetInCU: 0x11E, offset: 0x236A8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO19declinedPermissionsSo013FBSDKAppEventF4NameavpZ', symObjAddr: 0x6800, symBinAddr: 0x5F2D8, symSize: 0x0 }
  - { offsetInCU: 0x139, offset: 0x236C3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC19defaultDependenciesAC04TypeG0VSgvpZ', symObjAddr: 0x1A660, symBinAddr: 0x612A8, symSize: 0x0 }
  - { offsetInCU: 0x1C8, offset: 0x23752, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC22configuredDependenciesAC04TypeG0VSgvpZ', symObjAddr: 0x1A688, symBinAddr: 0x612D0, symSize: 0x0 }
  - { offsetInCU: 0x4B1, offset: 0x23A3B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO10identifier_WZ', symObjAddr: 0x1270, symBinAddr: 0x34A40, symSize: 0x30 }
  - { offsetInCU: 0x4CA, offset: 0x23A54, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO9timestamp_WZ', symObjAddr: 0x12A0, symBinAddr: 0x34A70, symSize: 0x30 }
  - { offsetInCU: 0x4E3, offset: 0x23A6D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO6result_WZ', symObjAddr: 0x12D0, symBinAddr: 0x34AA0, symSize: 0x30 }
  - { offsetInCU: 0x4FC, offset: 0x23A86, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO10authMethod_WZ', symObjAddr: 0x1300, symBinAddr: 0x34AD0, symSize: 0x30 }
  - { offsetInCU: 0x515, offset: 0x23A9F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO9errorCode_WZ', symObjAddr: 0x1330, symBinAddr: 0x34B00, symSize: 0x30 }
  - { offsetInCU: 0x52E, offset: 0x23AB8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO12errorMessage_WZ', symObjAddr: 0x1360, symBinAddr: 0x34B30, symSize: 0x30 }
  - { offsetInCU: 0x547, offset: 0x23AD1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO6extras_WZ', symObjAddr: 0x1390, symBinAddr: 0x34B60, symSize: 0x30 }
  - { offsetInCU: 0x560, offset: 0x23AEA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO12loggingToken_WZ', symObjAddr: 0x13C0, symBinAddr: 0x34B90, symSize: 0x30 }
  - { offsetInCU: 0x579, offset: 0x23B03, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO19declinedPermissions_WZ', symObjAddr: 0x13F0, symBinAddr: 0x34BC0, symSize: 0x30 }
  - { offsetInCU: 0x6E2, offset: 0x23C6C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC21heartbeatTimerDidFireyyFTo', symObjAddr: 0x40B0, symBinAddr: 0x37880, symSize: 0xD0 }
  - { offsetInCU: 0x74A, offset: 0x23CD4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC14getClientState20authenticationMethod08existingH06loggerSSSgAH_SDySSypGSgACSgtFZTf4nnnd_n', symObjAddr: 0x5C00, symBinAddr: 0x39380, symSize: 0x3D0 }
  - { offsetInCU: 0xC75, offset: 0x241FF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC19defaultDependencies_WZ', symObjAddr: 0x4640, symBinAddr: 0x37E10, symSize: 0x70 }
  - { offsetInCU: 0xC90, offset: 0x2421A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC19defaultDependenciesAC04TypeG0VSgvau', symObjAddr: 0x46B0, symBinAddr: 0x37E80, symSize: 0x30 }
  - { offsetInCU: 0xCC7, offset: 0x24251, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC22configuredDependencies_WZ', symObjAddr: 0x4780, symBinAddr: 0x37F50, symSize: 0x30 }
  - { offsetInCU: 0xCE2, offset: 0x2426C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC22configuredDependenciesAC04TypeG0VSgvau', symObjAddr: 0x47B0, symBinAddr: 0x37F80, symSize: 0x30 }
  - { offsetInCU: 0xD31, offset: 0x242BB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerCAA15DependentAsTypeA2aDP22configuredDependencies0hJ0QzSgvsZTW', symObjAddr: 0x4960, symBinAddr: 0x38130, symSize: 0x80 }
  - { offsetInCU: 0xD67, offset: 0x242F1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerCAA15DependentAsTypeA2aDP22configuredDependencies0hJ0QzSgvMZTW', symObjAddr: 0x49E0, symBinAddr: 0x381B0, symSize: 0x60 }
  - { offsetInCU: 0xDB9, offset: 0x24343, size: 0x8, addend: 0x0, symName: '_$sxq_xq_Iegnnrr_x3key_q_5valuetx_q_tIegnr_SHRzr0_lTRSS_ypTg575$sSD5merge_16uniquingKeysWithySDyxq_Gn_q_q__q_tKXEtKFx_q_tx_q_tcfU_SS_ypTG5Tf3nnpf_n', symObjAddr: 0x4AB0, symBinAddr: 0x38280, symSize: 0x40 }
  - { offsetInCU: 0xFB0, offset: 0x2453A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginEventLogging_pWOb', symObjAddr: 0x5BB0, symBinAddr: 0x39360, symSize: 0x20 }
  - { offsetInCU: 0xFC3, offset: 0x2454D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginEventLogging_pWOc', symObjAddr: 0x6260, symBinAddr: 0x398E0, symSize: 0x30 }
  - { offsetInCU: 0xFD6, offset: 0x24560, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC16TypeDependenciesVSgWOf', symObjAddr: 0x6290, symBinAddr: 0x39910, symSize: 0x40 }
  - { offsetInCU: 0xFE9, offset: 0x24573, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerCMa', symObjAddr: 0x62D0, symBinAddr: 0x39950, symSize: 0x20 }
  - { offsetInCU: 0xFFC, offset: 0x24586, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC16TypeDependenciesVwCP', symObjAddr: 0x6320, symBinAddr: 0x399A0, symSize: 0x30 }
  - { offsetInCU: 0x100F, offset: 0x24599, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC16TypeDependenciesVwxx', symObjAddr: 0x6350, symBinAddr: 0x399D0, symSize: 0x10 }
  - { offsetInCU: 0x1022, offset: 0x245AC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC16TypeDependenciesVwcp', symObjAddr: 0x6360, symBinAddr: 0x399E0, symSize: 0x30 }
  - { offsetInCU: 0x1035, offset: 0x245BF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC16TypeDependenciesVwca', symObjAddr: 0x6390, symBinAddr: 0x39A10, symSize: 0x20 }
  - { offsetInCU: 0x1048, offset: 0x245D2, size: 0x8, addend: 0x0, symName: ___swift_memcpy40_8, symObjAddr: 0x64E0, symBinAddr: 0x39A30, symSize: 0x20 }
  - { offsetInCU: 0x105B, offset: 0x245E5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC16TypeDependenciesVwta', symObjAddr: 0x6500, symBinAddr: 0x39A50, symSize: 0x40 }
  - { offsetInCU: 0x106E, offset: 0x245F8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC16TypeDependenciesVwet', symObjAddr: 0x6540, symBinAddr: 0x39A90, symSize: 0x50 }
  - { offsetInCU: 0x1081, offset: 0x2460B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC16TypeDependenciesVwst', symObjAddr: 0x6590, symBinAddr: 0x39AE0, symSize: 0x50 }
  - { offsetInCU: 0x1094, offset: 0x2461E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC16TypeDependenciesVMa', symObjAddr: 0x65E0, symBinAddr: 0x39B30, symSize: 0x10 }
  - { offsetInCU: 0x12CB, offset: 0x24855, size: 0x8, addend: 0x0, symName: '_$sSTsE6reduceyqd__qd___qd__qd___7ElementQztKXEtKlFShy13FBSDKLoginKit12FBPermissionCG_SSTg504$s13c61Kit18LoginManagerLoggerC12startSession3foryAA0cD0C_tFS2S_AA12E7CtXEfU_Tf1ncn_n', symObjAddr: 0x1CA0, symBinAddr: 0x35470, symSize: 0x430 }
  - { offsetInCU: 0x14EF, offset: 0x24A79, size: 0x8, addend: 0x0, symName: '_$ss17_dictionaryUpCastySDyq0_q1_GSDyxq_GSHRzSHR0_r2_lFSS_SbSSypTg5', symObjAddr: 0x4180, symBinAddr: 0x37950, symSize: 0x3C0 }
  - { offsetInCU: 0x181D, offset: 0x24DA7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC12startSession3foryAA0cD0C_tF', symObjAddr: 0x0, symBinAddr: 0x33810, symSize: 0x3B0 }
  - { offsetInCU: 0x1A5C, offset: 0x24FE6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC03endC06result5erroryAA0cdC6ResultCSg_So7NSErrorCSgtF', symObjAddr: 0x3B0, symBinAddr: 0x33BC0, symSize: 0x560 }
  - { offsetInCU: 0x1CB8, offset: 0x25242, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC10endSessionyyF', symObjAddr: 0x910, symBinAddr: 0x34120, symSize: 0x190 }
  - { offsetInCU: 0x1D38, offset: 0x252C2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC04postC9HeartbeatyyF', symObjAddr: 0xAA0, symBinAddr: 0x342B0, symSize: 0x50 }
  - { offsetInCU: 0x1D5F, offset: 0x252E9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC14getClientState20authenticationMethod08existingH06loggerSSSgAH_SDySSypGSgACSgtFZ', symObjAddr: 0xB30, symBinAddr: 0x34300, symSize: 0x10 }
  - { offsetInCU: 0x1DA2, offset: 0x2532C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC31willAttemptAppSwitchingBehavior9urlSchemeySS_tF', symObjAddr: 0xB40, symBinAddr: 0x34310, symSize: 0x280 }
  - { offsetInCU: 0x1EE7, offset: 0x25471, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC5start20authenticationMethodySS_tF', symObjAddr: 0xDC0, symBinAddr: 0x34590, symSize: 0x90 }
  - { offsetInCU: 0x1F23, offset: 0x254AD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC10identifierSSSgvg', symObjAddr: 0xE50, symBinAddr: 0x34620, symSize: 0x40 }
  - { offsetInCU: 0x1F36, offset: 0x254C0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC10identifierSSSgvs', symObjAddr: 0xE90, symBinAddr: 0x34660, symSize: 0x40 }
  - { offsetInCU: 0x1F49, offset: 0x254D3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC10identifierSSSgvM', symObjAddr: 0xED0, symBinAddr: 0x346A0, symSize: 0x30 }
  - { offsetInCU: 0x1F5C, offset: 0x254E6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC6extrasSDySSypGvg', symObjAddr: 0xF00, symBinAddr: 0x346D0, symSize: 0x30 }
  - { offsetInCU: 0x1F6F, offset: 0x254F9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC6extrasSDySSypGvs', symObjAddr: 0xF30, symBinAddr: 0x34700, symSize: 0x40 }
  - { offsetInCU: 0x1F82, offset: 0x2550C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC6extrasSDySSypGvM', symObjAddr: 0xF70, symBinAddr: 0x34740, symSize: 0x30 }
  - { offsetInCU: 0x1F95, offset: 0x2551F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC10lastResultSSvg', symObjAddr: 0xFA0, symBinAddr: 0x34770, symSize: 0x40 }
  - { offsetInCU: 0x1FA8, offset: 0x25532, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC10lastResultSSvs', symObjAddr: 0xFE0, symBinAddr: 0x347B0, symSize: 0x40 }
  - { offsetInCU: 0x1FBB, offset: 0x25545, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC10lastResultSSvM', symObjAddr: 0x1020, symBinAddr: 0x347F0, symSize: 0x30 }
  - { offsetInCU: 0x1FCE, offset: 0x25558, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC9lastErrorSo7NSErrorCSgvg', symObjAddr: 0x1050, symBinAddr: 0x34820, symSize: 0x40 }
  - { offsetInCU: 0x1FE1, offset: 0x2556B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC9lastErrorSo7NSErrorCSgvs', symObjAddr: 0x1090, symBinAddr: 0x34860, symSize: 0x40 }
  - { offsetInCU: 0x1FF4, offset: 0x2557E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC9lastErrorSo7NSErrorCSgvM', symObjAddr: 0x10D0, symBinAddr: 0x348A0, symSize: 0x30 }
  - { offsetInCU: 0x2007, offset: 0x25591, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC10authMethodSSSgvg', symObjAddr: 0x1100, symBinAddr: 0x348D0, symSize: 0x40 }
  - { offsetInCU: 0x201A, offset: 0x255A4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC10authMethodSSSgvs', symObjAddr: 0x1140, symBinAddr: 0x34910, symSize: 0x40 }
  - { offsetInCU: 0x202D, offset: 0x255B7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC10authMethodSSSgvM', symObjAddr: 0x1180, symBinAddr: 0x34950, symSize: 0x30 }
  - { offsetInCU: 0x2040, offset: 0x255CA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC10authMethodSSSgvM.resume.0', symObjAddr: 0x11B0, symBinAddr: 0x34980, symSize: 0x10 }
  - { offsetInCU: 0x2053, offset: 0x255DD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC12loggingTokenSSSgvg', symObjAddr: 0x11C0, symBinAddr: 0x34990, symSize: 0x40 }
  - { offsetInCU: 0x2066, offset: 0x255F0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC12loggingTokenSSSgvs', symObjAddr: 0x1200, symBinAddr: 0x349D0, symSize: 0x40 }
  - { offsetInCU: 0x2079, offset: 0x25603, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC12loggingTokenSSSgvM', symObjAddr: 0x1240, symBinAddr: 0x34A10, symSize: 0x30 }
  - { offsetInCU: 0x209E, offset: 0x25628, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC10parameters8trackingACSgSDySSypGSg_AA0C8TrackingOtcfC', symObjAddr: 0x1420, symBinAddr: 0x34BF0, symSize: 0x800 }
  - { offsetInCU: 0x21EE, offset: 0x25778, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC12loggingToken8trackingACSgSSSg_AA0C8TrackingOtcfC', symObjAddr: 0x1C20, symBinAddr: 0x353F0, symSize: 0x50 }
  - { offsetInCU: 0x2216, offset: 0x257A0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC12loggingToken8trackingACSgSSSg_AA0C8TrackingOtcfc', symObjAddr: 0x1C70, symBinAddr: 0x35440, symSize: 0x30 }
  - { offsetInCU: 0x2299, offset: 0x25823, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC21parametersForNewEventSDySo08FBSDKAppI13ParameterNameaypGyF', symObjAddr: 0x2140, symBinAddr: 0x35910, symSize: 0x920 }
  - { offsetInCU: 0x25DB, offset: 0x25B65, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC8logEvent_6paramsySo08FBSDKAppG4Namea_SDySo0ig9ParameterJ0aypGSgtF', symObjAddr: 0x2A60, symBinAddr: 0x36230, symSize: 0x3A0 }
  - { offsetInCU: 0x26DE, offset: 0x25C68, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC8logEvent_6result5errorySo08FBSDKAppG4Namea_SSSo7NSErrorCSgtF', symObjAddr: 0x2E00, symBinAddr: 0x365D0, symSize: 0x11F0 }
  - { offsetInCU: 0x2B00, offset: 0x2608A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC21heartbeatTimerDidFireyyF', symObjAddr: 0x3FF0, symBinAddr: 0x377C0, symSize: 0xC0 }
  - { offsetInCU: 0x2B7A, offset: 0x26104, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerCfd', symObjAddr: 0x4540, symBinAddr: 0x37D10, symSize: 0x40 }
  - { offsetInCU: 0x2BA7, offset: 0x26131, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerCfD', symObjAddr: 0x4580, symBinAddr: 0x37D50, symSize: 0x50 }
  - { offsetInCU: 0x2BDC, offset: 0x26166, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC16TypeDependenciesV05eventE0AA0C12EventLogging_pvg', symObjAddr: 0x45D0, symBinAddr: 0x37DA0, symSize: 0x10 }
  - { offsetInCU: 0x2BEF, offset: 0x26179, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC16TypeDependenciesV05eventE0AA0C12EventLogging_pvs', symObjAddr: 0x45E0, symBinAddr: 0x37DB0, symSize: 0x30 }
  - { offsetInCU: 0x2C02, offset: 0x2618C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC16TypeDependenciesV05eventE0AA0C12EventLogging_pvM', symObjAddr: 0x4610, symBinAddr: 0x37DE0, symSize: 0x10 }
  - { offsetInCU: 0x2C15, offset: 0x2619F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC16TypeDependenciesV05eventE0AA0C12EventLogging_pvM.resume.0', symObjAddr: 0x4620, symBinAddr: 0x37DF0, symSize: 0x10 }
  - { offsetInCU: 0x2C2E, offset: 0x261B8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC16TypeDependenciesV05eventE0AeA0C12EventLogging_p_tcfC', symObjAddr: 0x4630, symBinAddr: 0x37E00, symSize: 0x10 }
  - { offsetInCU: 0x2C41, offset: 0x261CB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC19defaultDependenciesAC04TypeG0VSgvMZ', symObjAddr: 0x4720, symBinAddr: 0x37EF0, symSize: 0x60 }
  - { offsetInCU: 0x2C60, offset: 0x261EA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC22configuredDependenciesAC04TypeG0VSgvMZ', symObjAddr: 0x48E0, symBinAddr: 0x380B0, symSize: 0x60 }
  - { offsetInCU: 0x2C9D, offset: 0x26227, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV4copyyyFSo26FBSDKAppEventParameterNamea_ypTg5', symObjAddr: 0x4F70, symBinAddr: 0x38740, symSize: 0x240 }
  - { offsetInCU: 0x2D1B, offset: 0x262A5, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV4copyyyFSS_ypTg5', symObjAddr: 0x51B0, symBinAddr: 0x38980, symSize: 0x260 }
  - { offsetInCU: 0x2DA5, offset: 0x2632F, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV4copyyyFSS_SSTg5', symObjAddr: 0x5410, symBinAddr: 0x38BE0, symSize: 0x220 }
  - { offsetInCU: 0x2E67, offset: 0x263F1, size: 0x8, addend: 0x0, symName: '_$sSD11removeValue6forKeyq_Sgx_tFSS_ypTg5', symObjAddr: 0x5630, symBinAddr: 0x38E00, symSize: 0xE0 }
  - { offsetInCU: 0x2F15, offset: 0x2649F, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV7_delete2atys10_HashTableV6BucketV_tFSo26FBSDKAppEventParameterNamea_ypTg5', symObjAddr: 0x5710, symBinAddr: 0x38EE0, symSize: 0x260 }
  - { offsetInCU: 0x2FB4, offset: 0x2653E, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV7_delete2atys10_HashTableV6BucketV_tFSS_ypTg5', symObjAddr: 0x5970, symBinAddr: 0x39140, symSize: 0x220 }
  - { offsetInCU: 0x3041, offset: 0x265CB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC12loggingToken8trackingACSgSSSg_AA0C8TrackingOtcfcTf4gnn_n', symObjAddr: 0x6090, symBinAddr: 0x39750, symSize: 0x160 }
  - { offsetInCU: 0xD8, offset: 0x26734, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC5tokenSo16FBSDKAccessTokenCSgvgTo', symObjAddr: 0x120, symBinAddr: 0x39D70, symSize: 0x20 }
  - { offsetInCU: 0x127, offset: 0x26783, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC19authenticationTokenSo019FBSDKAuthenticationG0CSgvgTo', symObjAddr: 0x170, symBinAddr: 0x39DC0, symSize: 0x20 }
  - { offsetInCU: 0x176, offset: 0x267D2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC11isCancelledSbvgTo', symObjAddr: 0x1C0, symBinAddr: 0x39E10, symSize: 0x20 }
  - { offsetInCU: 0x1ED, offset: 0x26849, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC13loggingExtrasSDySSypGvgTo', symObjAddr: 0x2D0, symBinAddr: 0x39F20, symSize: 0x70 }
  - { offsetInCU: 0x23F, offset: 0x2689B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC13loggingExtrasSDySSypGvsTo', symObjAddr: 0x380, symBinAddr: 0x39FD0, symSize: 0x70 }
  - { offsetInCU: 0x27E, offset: 0x268DA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC5token19authenticationToken11isCancelled18grantedPermissions08declinedL0ACSo011FBSDKAccessH0CSg_So019FBSDKAuthenticationH0CSgSbShySSGAOtcfcTo', symObjAddr: 0x4D0, symBinAddr: 0x3A0F0, symSize: 0xF0 }
  - { offsetInCU: 0x2E6, offset: 0x26942, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC15addLoggingExtra_6forKeyyyp_SStFTo', symObjAddr: 0x5C0, symBinAddr: 0x3A1E0, symSize: 0xF0 }
  - { offsetInCU: 0x358, offset: 0x269B4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultCACycfcTo', symObjAddr: 0x700, symBinAddr: 0x3A320, symSize: 0x30 }
  - { offsetInCU: 0x3E5, offset: 0x26A41, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultCfETo', symObjAddr: 0x760, symBinAddr: 0x3A380, symSize: 0x70 }
  - { offsetInCU: 0x4C1, offset: 0x26B1D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultCMa', symObjAddr: 0x940, symBinAddr: 0x3A560, symSize: 0x20 }
  - { offsetInCU: 0x4D4, offset: 0x26B30, size: 0x8, addend: 0x0, symName: ___swift_destroy_boxed_opaque_existential_0, symObjAddr: 0x990, symBinAddr: 0x3A5B0, symSize: 0x30 }
  - { offsetInCU: 0x6CB, offset: 0x26D27, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC5token19authenticationToken11isCancelled18grantedPermissions08declinedL0ACSo011FBSDKAccessH0CSg_So019FBSDKAuthenticationH0CSgSbShySSGAOtcfC', symObjAddr: 0x0, symBinAddr: 0x39C50, symSize: 0xA0 }
  - { offsetInCU: 0x739, offset: 0x26D95, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC15addLoggingExtra_6forKeyyyp_SStF', symObjAddr: 0xA0, symBinAddr: 0x39CF0, symSize: 0x80 }
  - { offsetInCU: 0x786, offset: 0x26DE2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC5tokenSo16FBSDKAccessTokenCSgvg', symObjAddr: 0x140, symBinAddr: 0x39D90, symSize: 0x30 }
  - { offsetInCU: 0x7B3, offset: 0x26E0F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC19authenticationTokenSo019FBSDKAuthenticationG0CSgvg', symObjAddr: 0x190, symBinAddr: 0x39DE0, symSize: 0x30 }
  - { offsetInCU: 0x7E0, offset: 0x26E3C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC11isCancelledSbvg', symObjAddr: 0x1E0, symBinAddr: 0x39E30, symSize: 0x20 }
  - { offsetInCU: 0x7FB, offset: 0x26E57, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC18grantedPermissionsShySSGvg', symObjAddr: 0x220, symBinAddr: 0x39E70, symSize: 0x20 }
  - { offsetInCU: 0x81C, offset: 0x26E78, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC19declinedPermissionsShySSGvg', symObjAddr: 0x2B0, symBinAddr: 0x39F00, symSize: 0x20 }
  - { offsetInCU: 0x84F, offset: 0x26EAB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC13loggingExtrasSDySSypGvg', symObjAddr: 0x340, symBinAddr: 0x39F90, symSize: 0x40 }
  - { offsetInCU: 0x88C, offset: 0x26EE8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC5token19authenticationToken11isCancelled18grantedPermissions08declinedL0ACSo011FBSDKAccessH0CSg_So019FBSDKAuthenticationH0CSgSbShySSGAOtcfc', symObjAddr: 0x3F0, symBinAddr: 0x3A040, symSize: 0xB0 }
  - { offsetInCU: 0x926, offset: 0x26F82, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultCACycfC', symObjAddr: 0x6B0, symBinAddr: 0x3A2D0, symSize: 0x20 }
  - { offsetInCU: 0x939, offset: 0x26F95, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultCACycfc', symObjAddr: 0x6D0, symBinAddr: 0x3A2F0, symSize: 0x30 }
  - { offsetInCU: 0x98D, offset: 0x26FE9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultCfD', symObjAddr: 0x730, symBinAddr: 0x3A350, symSize: 0x30 }
  - { offsetInCU: 0x9C0, offset: 0x2701C, size: 0x8, addend: 0x0, symName: '_$sSD8_VariantV8setValue_6forKeyyq_n_xtFSS_SSTg5', symObjAddr: 0x7D0, symBinAddr: 0x3A3F0, symSize: 0xC0 }
  - { offsetInCU: 0xA25, offset: 0x27081, size: 0x8, addend: 0x0, symName: '_$sSD8_VariantV8setValue_6forKeyyq_n_xtFSS_ypTg5', symObjAddr: 0x890, symBinAddr: 0x3A4B0, symSize: 0xB0 }
  - { offsetInCU: 0x27, offset: 0x270F5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateO8rawValueACSgSi_tcfC', symObjAddr: 0x0, symBinAddr: 0x3A5E0, symSize: 0x20 }
  - { offsetInCU: 0x73, offset: 0x27141, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateOSYAASY8rawValuexSg03RawG0Qz_tcfCTW', symObjAddr: 0x120, symBinAddr: 0x3A700, symSize: 0x20 }
  - { offsetInCU: 0xA2, offset: 0x27170, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateOSYAASY8rawValue03RawG0QzvgTW', symObjAddr: 0x140, symBinAddr: 0x3A720, symSize: 0x10 }
  - { offsetInCU: 0xC9, offset: 0x27197, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateOSHAASQWb', symObjAddr: 0x40, symBinAddr: 0x3A620, symSize: 0x10 }
  - { offsetInCU: 0xDC, offset: 0x271AA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateOACSQAAWl', symObjAddr: 0x50, symBinAddr: 0x3A630, symSize: 0x30 }
  - { offsetInCU: 0x10D, offset: 0x271DB, size: 0x8, addend: 0x0, symName: ___swift_memcpy1_1, symObjAddr: 0x150, symBinAddr: 0x3A730, symSize: 0x10 }
  - { offsetInCU: 0x120, offset: 0x271EE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateOwet', symObjAddr: 0x170, symBinAddr: 0x3A740, symSize: 0x80 }
  - { offsetInCU: 0x133, offset: 0x27201, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateOwst', symObjAddr: 0x1F0, symBinAddr: 0x3A7C0, symSize: 0xD0 }
  - { offsetInCU: 0x146, offset: 0x27214, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateOwug', symObjAddr: 0x2C0, symBinAddr: 0x3A890, symSize: 0x10 }
  - { offsetInCU: 0x159, offset: 0x27227, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateOwup', symObjAddr: 0x2D0, symBinAddr: 0x3A8A0, symSize: 0x10 }
  - { offsetInCU: 0x16C, offset: 0x2723A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateOwui', symObjAddr: 0x2E0, symBinAddr: 0x3A8B0, symSize: 0x10 }
  - { offsetInCU: 0x17F, offset: 0x2724D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateOMa', symObjAddr: 0x2F0, symBinAddr: 0x3A8C0, symSize: 0xA }
  - { offsetInCU: 0x1B5, offset: 0x27283, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x30, symBinAddr: 0x3A610, symSize: 0x10 }
  - { offsetInCU: 0x257, offset: 0x27325, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateOSHAASH9hashValueSivgTW', symObjAddr: 0x80, symBinAddr: 0x3A660, symSize: 0x40 }
  - { offsetInCU: 0x2FE, offset: 0x273CC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateOSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0xC0, symBinAddr: 0x3A6A0, symSize: 0x20 }
  - { offsetInCU: 0x34D, offset: 0x2741B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateOSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0xE0, symBinAddr: 0x3A6C0, symSize: 0x40 }
  - { offsetInCU: 0x450, offset: 0x2751E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateO8rawValueACSgSi_tcfC', symObjAddr: 0x0, symBinAddr: 0x3A5E0, symSize: 0x20 }
  - { offsetInCU: 0x46B, offset: 0x27539, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateO8rawValueSivg', symObjAddr: 0x20, symBinAddr: 0x3A600, symSize: 0x10 }
  - { offsetInCU: 0x49, offset: 0x275D9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC19defaultDependenciesAC04TypeG0VSgvpZ', symObjAddr: 0x7EA8, symBinAddr: 0x612F8, symSize: 0x0 }
  - { offsetInCU: 0x130, offset: 0x276C0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC22configuredDependenciesAC04TypeG0VSgvpZ', symObjAddr: 0x7ED8, symBinAddr: 0x61328, symSize: 0x0 }
  - { offsetInCU: 0x21F, offset: 0x277AF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC07attemptD04from10completionys5Error_p_ySbctFTo', symObjAddr: 0x240, symBinAddr: 0x3AB70, symSize: 0x80 }
  - { offsetInCU: 0x372, offset: 0x27902, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC19defaultDependencies_WZ', symObjAddr: 0x3F0, symBinAddr: 0x3AD20, symSize: 0x60 }
  - { offsetInCU: 0x3B5, offset: 0x27945, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC19defaultDependenciesAC04TypeG0VSgvau', symObjAddr: 0x450, symBinAddr: 0x3AD80, symSize: 0x30 }
  - { offsetInCU: 0x3FF, offset: 0x2798F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC22configuredDependencies_WZ', symObjAddr: 0x530, symBinAddr: 0x3AE60, symSize: 0x20 }
  - { offsetInCU: 0x418, offset: 0x279A8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC22configuredDependenciesAC04TypeG0VSgvau', symObjAddr: 0x550, symBinAddr: 0x3AE80, symSize: 0x30 }
  - { offsetInCU: 0x467, offset: 0x279F7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterCAA15DependentAsTypeA2aDP22configuredDependencies0hJ0QzSgvsZTW', symObjAddr: 0x700, symBinAddr: 0x3B030, symSize: 0x80 }
  - { offsetInCU: 0x49B, offset: 0x27A2B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterCAA15DependentAsTypeA2aDP22configuredDependencies0hJ0QzSgvMZTW', symObjAddr: 0x780, symBinAddr: 0x3B0B0, symSize: 0x60 }
  - { offsetInCU: 0x4F0, offset: 0x27A80, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14LoginProviding_pWOc', symObjAddr: 0xC30, symBinAddr: 0x3B560, symSize: 0x30 }
  - { offsetInCU: 0x503, offset: 0x27A93, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesVSgWOc', symObjAddr: 0xCB0, symBinAddr: 0x3B590, symSize: 0x40 }
  - { offsetInCU: 0x516, offset: 0x27AA6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesVSgWOf', symObjAddr: 0xD30, symBinAddr: 0x3B5D0, symSize: 0x40 }
  - { offsetInCU: 0x529, offset: 0x27AB9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesVSgWOh', symObjAddr: 0xD70, symBinAddr: 0x3B610, symSize: 0x30 }
  - { offsetInCU: 0x53C, offset: 0x27ACC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterCMa', symObjAddr: 0xDA0, symBinAddr: 0x3B640, symSize: 0x20 }
  - { offsetInCU: 0x54F, offset: 0x27ADF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesVwCP', symObjAddr: 0xDF0, symBinAddr: 0x3B690, symSize: 0x30 }
  - { offsetInCU: 0x562, offset: 0x27AF2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesVwxx', symObjAddr: 0xE20, symBinAddr: 0x3B6C0, symSize: 0x10 }
  - { offsetInCU: 0x575, offset: 0x27B05, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesVwcp', symObjAddr: 0xE30, symBinAddr: 0x3B6D0, symSize: 0x40 }
  - { offsetInCU: 0x588, offset: 0x27B18, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesVwca', symObjAddr: 0xE70, symBinAddr: 0x3B710, symSize: 0x30 }
  - { offsetInCU: 0x59B, offset: 0x27B2B, size: 0x8, addend: 0x0, symName: ___swift_memcpy48_8, symObjAddr: 0xFD0, symBinAddr: 0x3B740, symSize: 0x20 }
  - { offsetInCU: 0x5AE, offset: 0x27B3E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesVwta', symObjAddr: 0xFF0, symBinAddr: 0x3B760, symSize: 0x40 }
  - { offsetInCU: 0x5C1, offset: 0x27B51, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesVwet', symObjAddr: 0x1030, symBinAddr: 0x3B7A0, symSize: 0x50 }
  - { offsetInCU: 0x5D4, offset: 0x27B64, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesVwst', symObjAddr: 0x1080, symBinAddr: 0x3B7F0, symSize: 0x50 }
  - { offsetInCU: 0x5E7, offset: 0x27B77, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesVMa', symObjAddr: 0x10D0, symBinAddr: 0x3B840, symSize: 0x10 }
  - { offsetInCU: 0x604, offset: 0x27B94, size: 0x8, addend: 0x0, symName: '_$s10ObjectiveC8ObjCBoolVIeyBy_SbIegy_TRTA', symObjAddr: 0x1100, symBinAddr: 0x3B870, symSize: 0x20 }
  - { offsetInCU: 0x62C, offset: 0x27BBC, size: 0x8, addend: 0x0, symName: '_$sSo16FBSDKAccessTokenCMa', symObjAddr: 0x1120, symBinAddr: 0x3B890, symSize: 0x30 }
  - { offsetInCU: 0x63F, offset: 0x27BCF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC07attemptD04from10completionys5Error_p_ySbctFyAA0c7ManagerC6ResultCSg_sAG_pSgtcfU_TA', symObjAddr: 0x11A0, symBinAddr: 0x3B8E0, symSize: 0x60 }
  - { offsetInCU: 0x6A9, offset: 0x27C39, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesVWOh', symObjAddr: 0x1200, symBinAddr: 0x3B940, symSize: 0x20 }
  - { offsetInCU: 0x715, offset: 0x27CA5, size: 0x8, addend: 0x0, symName: '_$sSlsE3mapySayqd__Gqd__7ElementQzKXEKlFShy12FBSDKCoreKit10PermissionOG_SSTg5091$s13FBSDKLoginKit22LoginRecoveryAttempterC07attemptD04from10completionys5Error_p_ySbctFSS09c4B010E52Ocfu_32f5ef5a0b9ade21eb65ffea7b618f60adAJSSTf3nnpk_nTf1cn_n', symObjAddr: 0x0, symBinAddr: 0x3A930, symSize: 0x230 }
  - { offsetInCU: 0xA22, offset: 0x27FB2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC07attemptD04from10completionys5Error_p_ySbctF', symObjAddr: 0x230, symBinAddr: 0x3AB60, symSize: 0x10 }
  - { offsetInCU: 0xACA, offset: 0x2805A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterCfd', symObjAddr: 0x2C0, symBinAddr: 0x3ABF0, symSize: 0x10 }
  - { offsetInCU: 0xAEB, offset: 0x2807B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterCfD', symObjAddr: 0x2D0, symBinAddr: 0x3AC00, symSize: 0x20 }
  - { offsetInCU: 0xB12, offset: 0x280A2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterCACycfC', symObjAddr: 0x2F0, symBinAddr: 0x3AC20, symSize: 0x20 }
  - { offsetInCU: 0xB25, offset: 0x280B5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterCACycfc', symObjAddr: 0x310, symBinAddr: 0x3AC40, symSize: 0x10 }
  - { offsetInCU: 0xB46, offset: 0x280D6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesV13loginProviderAA0C9Providing_pvg', symObjAddr: 0x320, symBinAddr: 0x3AC50, symSize: 0x10 }
  - { offsetInCU: 0xB59, offset: 0x280E9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesV13loginProviderAA0C9Providing_pvs', symObjAddr: 0x330, symBinAddr: 0x3AC60, symSize: 0x30 }
  - { offsetInCU: 0xB6C, offset: 0x280FC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesV13loginProviderAA0C9Providing_pvM', symObjAddr: 0x360, symBinAddr: 0x3AC90, symSize: 0x10 }
  - { offsetInCU: 0xB7F, offset: 0x2810F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesV13loginProviderAA0C9Providing_pvM.resume.0', symObjAddr: 0x370, symBinAddr: 0x3ACA0, symSize: 0x10 }
  - { offsetInCU: 0xB98, offset: 0x28128, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesV19accessTokenProviderSo011FBSDKAccessI9Providing_pXpvg', symObjAddr: 0x380, symBinAddr: 0x3ACB0, symSize: 0x10 }
  - { offsetInCU: 0xBAB, offset: 0x2813B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesV19accessTokenProviderSo011FBSDKAccessI9Providing_pXpvs', symObjAddr: 0x390, symBinAddr: 0x3ACC0, symSize: 0x10 }
  - { offsetInCU: 0xBBE, offset: 0x2814E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesV19accessTokenProviderSo011FBSDKAccessI9Providing_pXpvM', symObjAddr: 0x3A0, symBinAddr: 0x3ACD0, symSize: 0x20 }
  - { offsetInCU: 0xBD1, offset: 0x28161, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesV19accessTokenProviderSo011FBSDKAccessI9Providing_pXpvM.resume.0', symObjAddr: 0x3C0, symBinAddr: 0x3ACF0, symSize: 0x10 }
  - { offsetInCU: 0xBEA, offset: 0x2817A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesV13loginProvider011accessTokenI0AeA0C9Providing_p_So011FBSDKAccesskL0_pXptcfC', symObjAddr: 0x3D0, symBinAddr: 0x3AD00, symSize: 0x20 }
  - { offsetInCU: 0xC0F, offset: 0x2819F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC19defaultDependenciesAC04TypeG0VSgvMZ', symObjAddr: 0x4C0, symBinAddr: 0x3ADF0, symSize: 0x60 }
  - { offsetInCU: 0xC2E, offset: 0x281BE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC19defaultDependenciesAC04TypeG0VSgvMZ.resume.0', symObjAddr: 0x520, symBinAddr: 0x3AE50, symSize: 0x10 }
  - { offsetInCU: 0xC42, offset: 0x281D2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC22configuredDependenciesAC04TypeG0VSgvMZ', symObjAddr: 0x680, symBinAddr: 0x3AFB0, symSize: 0x60 }
  - { offsetInCU: 0xC79, offset: 0x28209, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC07attemptD04from10completionys5Error_p_ySbctFTf4dnn_n', symObjAddr: 0xA60, symBinAddr: 0x3B390, symSize: 0x1D0 }
  - { offsetInCU: 0x2B, offset: 0x282F5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultO012loginManagerD0AA0cfcD0CSgvg', symObjAddr: 0x230, symBinAddr: 0x3BBB0, symSize: 0x130 }
  - { offsetInCU: 0x10A, offset: 0x283D4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultO6result5errorAcA0c7ManagercD0CSg_s5Error_pSgtcfCTf4nnd_n', symObjAddr: 0x5C0, symBinAddr: 0x3BF40, symSize: 0x4D0 }
  - { offsetInCU: 0x5A4, offset: 0x2886E, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_ypTgmq5Tf4g_n', symObjAddr: 0x4E0, symBinAddr: 0x3BE60, symSize: 0xE0 }
  - { offsetInCU: 0x791, offset: 0x28A5B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultOwCP', symObjAddr: 0xA90, symBinAddr: 0x3C410, symSize: 0x30 }
  - { offsetInCU: 0x7A4, offset: 0x28A6E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultOWOy', symObjAddr: 0xAC0, symBinAddr: 0x3C440, symSize: 0x50 }
  - { offsetInCU: 0x7B7, offset: 0x28A81, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultOwxx', symObjAddr: 0xB10, symBinAddr: 0x3C490, symSize: 0x20 }
  - { offsetInCU: 0x7CA, offset: 0x28A94, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultOwcp', symObjAddr: 0xB80, symBinAddr: 0x3C4B0, symSize: 0x60 }
  - { offsetInCU: 0x7DD, offset: 0x28AA7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultOwca', symObjAddr: 0xBE0, symBinAddr: 0x3C510, symSize: 0x70 }
  - { offsetInCU: 0x7F0, offset: 0x28ABA, size: 0x8, addend: 0x0, symName: ___swift_memcpy25_8, symObjAddr: 0xC50, symBinAddr: 0x3C580, symSize: 0x20 }
  - { offsetInCU: 0x803, offset: 0x28ACD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultOwta', symObjAddr: 0xC70, symBinAddr: 0x3C5A0, symSize: 0x40 }
  - { offsetInCU: 0x816, offset: 0x28AE0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultOwet', symObjAddr: 0xCB0, symBinAddr: 0x3C5E0, symSize: 0x50 }
  - { offsetInCU: 0x829, offset: 0x28AF3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultOwst', symObjAddr: 0xD00, symBinAddr: 0x3C630, symSize: 0x50 }
  - { offsetInCU: 0x83C, offset: 0x28B06, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultOwug', symObjAddr: 0xD50, symBinAddr: 0x3C680, symSize: 0x20 }
  - { offsetInCU: 0x84F, offset: 0x28B19, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultOwup', symObjAddr: 0xD70, symBinAddr: 0x3C6A0, symSize: 0x10 }
  - { offsetInCU: 0x862, offset: 0x28B2C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultOwui', symObjAddr: 0xD80, symBinAddr: 0x3C6B0, symSize: 0x20 }
  - { offsetInCU: 0x875, offset: 0x28B3F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultOMa', symObjAddr: 0xDA0, symBinAddr: 0x3C6D0, symSize: 0x10 }
  - { offsetInCU: 0xB3B, offset: 0x28E05, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultO012loginManagerD0AA0cfcD0CSgvg', symObjAddr: 0x230, symBinAddr: 0x3BBB0, symSize: 0x130 }
  - { offsetInCU: 0xC5C, offset: 0x28F26, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultO5errors5Error_pSgvg', symObjAddr: 0x360, symBinAddr: 0x3BCE0, symSize: 0x30 }
  - { offsetInCU: 0xC85, offset: 0x28F4F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultO6result5errorAcA0c7ManagercD0CSg_s5Error_pSgtcfC', symObjAddr: 0x390, symBinAddr: 0x3BD10, symSize: 0x10 }
  - { offsetInCU: 0xC9E, offset: 0x28F68, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4findys10_HashTableV6BucketV6bucket_Sb5foundtxSHRzlFSS_Tgq5', symObjAddr: 0x3A0, symBinAddr: 0x3BD20, symSize: 0x60 }
  - { offsetInCU: 0xCBE, offset: 0x28F88, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4find_9hashValues10_HashTableV6BucketV6bucket_Sb5foundtx_SitSHRzlFSS_Tgq5', symObjAddr: 0x400, symBinAddr: 0x3BD80, symSize: 0xE0 }
  - { offsetInCU: 0x27, offset: 0x290B2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13LoginTrackingO8rawValueACSgSu_tcfC', symObjAddr: 0x0, symBinAddr: 0x3C6E0, symSize: 0x20 }
  - { offsetInCU: 0x73, offset: 0x290FE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13LoginTrackingOSYAASY8rawValuexSg03RawF0Qz_tcfCTW', symObjAddr: 0x120, symBinAddr: 0x3C800, symSize: 0x30 }
  - { offsetInCU: 0xA2, offset: 0x2912D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13LoginTrackingOSYAASY8rawValue03RawF0QzvgTW', symObjAddr: 0x150, symBinAddr: 0x3C830, symSize: 0x10 }
  - { offsetInCU: 0xC9, offset: 0x29154, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13LoginTrackingOSHAASQWb', symObjAddr: 0x40, symBinAddr: 0x3C720, symSize: 0x10 }
  - { offsetInCU: 0xDC, offset: 0x29167, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13LoginTrackingOACSQAAWl', symObjAddr: 0x50, symBinAddr: 0x3C730, symSize: 0x30 }
  - { offsetInCU: 0x10D, offset: 0x29198, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13LoginTrackingOMa', symObjAddr: 0x160, symBinAddr: 0x3C840, symSize: 0xA }
  - { offsetInCU: 0x143, offset: 0x291CE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13LoginTrackingOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x30, symBinAddr: 0x3C710, symSize: 0x10 }
  - { offsetInCU: 0x1E5, offset: 0x29270, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13LoginTrackingOSHAASH9hashValueSivgTW', symObjAddr: 0x80, symBinAddr: 0x3C760, symSize: 0x40 }
  - { offsetInCU: 0x28C, offset: 0x29317, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13LoginTrackingOSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0xC0, symBinAddr: 0x3C7A0, symSize: 0x20 }
  - { offsetInCU: 0x2DB, offset: 0x29366, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13LoginTrackingOSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0xE0, symBinAddr: 0x3C7C0, symSize: 0x40 }
  - { offsetInCU: 0x3DE, offset: 0x29469, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13LoginTrackingO8rawValueACSgSu_tcfC', symObjAddr: 0x0, symBinAddr: 0x3C6E0, symSize: 0x20 }
  - { offsetInCU: 0x3F9, offset: 0x29484, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13LoginTrackingO8rawValueSuvg', symObjAddr: 0x20, symBinAddr: 0x3C700, symSize: 0x10 }
  - { offsetInCU: 0x4A, offset: 0x29525, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV22configuredDependenciesAC04TypeF0VSgvpZ', symObjAddr: 0x18FF0, symBinAddr: 0x61358, symSize: 0x0 }
  - { offsetInCU: 0x236, offset: 0x29711, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV19defaultDependenciesAC04TypeF0VSgvpZ', symObjAddr: 0x19058, symBinAddr: 0x613C0, symSize: 0x0 }
  - { offsetInCU: 0x3C8, offset: 0x298A3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterVAA0C10CompletingA2aDP08completeC07handleryyAA01_C20CompletionParametersCc_tFTW', symObjAddr: 0x3930, symBinAddr: 0x40180, symSize: 0x30 }
  - { offsetInCU: 0x409, offset: 0x298E4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterVAA0C10CompletingA2aDP08completeC05nonce12codeVerifier7handlerySSSg_AJyAA01_C20CompletionParametersCctFTW', symObjAddr: 0x3960, symBinAddr: 0x401B0, symSize: 0x20 }
  - { offsetInCU: 0x424, offset: 0x298FF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV9challenge4fromSSSgSDySSypG_tFTf4nd_n', symObjAddr: 0x41C0, symBinAddr: 0x40A10, symSize: 0x390 }
  - { offsetInCU: 0x4B5, offset: 0x29990, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV5error4froms5Error_pSgSDySSypG_tFTf4nd_n', symObjAddr: 0x4550, symBinAddr: 0x40DA0, symSize: 0x820 }
  - { offsetInCU: 0x6D3, offset: 0x29BAE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV13urlParameters5appIDACSDySSypG_SStcfCTf4nnd_n', symObjAddr: 0x4D70, symBinAddr: 0x415C0, symSize: 0xA10 }
  - { offsetInCU: 0x864, offset: 0x29D3F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV7profile4with11permissions09FBSDKCoreB07ProfileCSgAG25AuthenticationTokenClaimsC_ShySSGSgtFTf4nnd_n', symObjAddr: 0x5AA0, symBinAddr: 0x421F0, symSize: 0x920 }
  - { offsetInCU: 0xBB5, offset: 0x2A090, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV22configuredDependencies_WZ', symObjAddr: 0x3BE0, symBinAddr: 0x40430, symSize: 0x40 }
  - { offsetInCU: 0xBD0, offset: 0x2A0AB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV22configuredDependenciesAC04TypeF0VSgvau', symObjAddr: 0x3C20, symBinAddr: 0x40470, symSize: 0x30 }
  - { offsetInCU: 0xC12, offset: 0x2A0ED, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV19defaultDependencies_WZ', symObjAddr: 0x3CF0, symBinAddr: 0x40540, symSize: 0x1C0 }
  - { offsetInCU: 0xCD1, offset: 0x2A1AC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV19defaultDependenciesAC04TypeF0VSgvau', symObjAddr: 0x3EB0, symBinAddr: 0x40700, symSize: 0x30 }
  - { offsetInCU: 0xD22, offset: 0x2A1FD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterVAA15DependentAsTypeA2aDP22configuredDependencies0gI0QzSgvsZTW', symObjAddr: 0x4070, symBinAddr: 0x408C0, symSize: 0x80 }
  - { offsetInCU: 0xD58, offset: 0x2A233, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterVAA15DependentAsTypeA2aDP22configuredDependencies0gI0QzSgvMZTW', symObjAddr: 0x40F0, symBinAddr: 0x40940, symSize: 0x60 }
  - { offsetInCU: 0xDDC, offset: 0x2A2B7, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DateVSgWOd', symObjAddr: 0x57F0, symBinAddr: 0x41FD0, symSize: 0x40 }
  - { offsetInCU: 0xDEF, offset: 0x2A2CA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesVWOh', symObjAddr: 0x5860, symBinAddr: 0x42010, symSize: 0x20 }
  - { offsetInCU: 0xE02, offset: 0x2A2DD, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x58C0, symBinAddr: 0x42070, symSize: 0x20 }
  - { offsetInCU: 0xE15, offset: 0x2A2F0, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x58E0, symBinAddr: 0x42090, symSize: 0x10 }
  - { offsetInCU: 0xE28, offset: 0x2A303, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV24fetchAndSetPropertiesFor10parameters5nonce7handleryAA01_C20CompletionParametersC_SSyAIctFySo24FBSDKAuthenticationTokenCSgcfU_TA', symObjAddr: 0x5A70, symBinAddr: 0x421C0, symSize: 0x30 }
  - { offsetInCU: 0xE3B, offset: 0x2A316, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15ProfileCreating_pWOb', symObjAddr: 0x63F0, symBinAddr: 0x42B40, symSize: 0x20 }
  - { offsetInCU: 0xE4E, offset: 0x2A329, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesVSgWOf', symObjAddr: 0x6410, symBinAddr: 0x42B60, symSize: 0x40 }
  - { offsetInCU: 0xE61, offset: 0x2A33C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterVMa', symObjAddr: 0x6450, symBinAddr: 0x42BA0, symSize: 0x10 }
  - { offsetInCU: 0xE74, offset: 0x2A34F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesVwCP', symObjAddr: 0x6460, symBinAddr: 0x42BB0, symSize: 0x30 }
  - { offsetInCU: 0xE87, offset: 0x2A362, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesVwxx', symObjAddr: 0x6490, symBinAddr: 0x42BE0, symSize: 0x40 }
  - { offsetInCU: 0xE9A, offset: 0x2A375, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesVwcp', symObjAddr: 0x64D0, symBinAddr: 0x42C20, symSize: 0x80 }
  - { offsetInCU: 0xEAD, offset: 0x2A388, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesVwca', symObjAddr: 0x6550, symBinAddr: 0x42CA0, symSize: 0x80 }
  - { offsetInCU: 0xEC0, offset: 0x2A39B, size: 0x8, addend: 0x0, symName: ___swift_memcpy104_8, symObjAddr: 0x6700, symBinAddr: 0x42D20, symSize: 0x40 }
  - { offsetInCU: 0xED3, offset: 0x2A3AE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesVwta', symObjAddr: 0x6740, symBinAddr: 0x42D60, symSize: 0x90 }
  - { offsetInCU: 0xEE6, offset: 0x2A3C1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesVwet', symObjAddr: 0x67D0, symBinAddr: 0x42DF0, symSize: 0x50 }
  - { offsetInCU: 0xEF9, offset: 0x2A3D4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesVwst', symObjAddr: 0x6820, symBinAddr: 0x42E40, symSize: 0x60 }
  - { offsetInCU: 0xF0C, offset: 0x2A3E7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesVMa', symObjAddr: 0x6880, symBinAddr: 0x42EA0, symSize: 0x10 }
  - { offsetInCU: 0xF1F, offset: 0x2A3FA, size: 0x8, addend: 0x0, symName: '_$sSo8NSNumberCMa', symObjAddr: 0x6890, symBinAddr: 0x42EB0, symSize: 0x30 }
  - { offsetInCU: 0x1305, offset: 0x2A7E0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV13urlParameters5appIDACSDySSypG_SStcfC', symObjAddr: 0x0, symBinAddr: 0x3C850, symSize: 0x10 }
  - { offsetInCU: 0x1318, offset: 0x2A7F3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV10parametersAA01_C20CompletionParametersCvg', symObjAddr: 0x10, symBinAddr: 0x3C860, symSize: 0x10 }
  - { offsetInCU: 0x136D, offset: 0x2A848, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV13setParameters6values5appIDySDySSypG_SStF', symObjAddr: 0x20, symBinAddr: 0x3C870, symSize: 0xE80 }
  - { offsetInCU: 0x163D, offset: 0x2AB18, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV5error4froms5Error_pSgSDySSypG_tF', symObjAddr: 0xEA0, symBinAddr: 0x3D6F0, symSize: 0x10 }
  - { offsetInCU: 0x165B, offset: 0x2AB36, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV08completeC07handleryyAA01_C20CompletionParametersCc_tF', symObjAddr: 0xEB0, symBinAddr: 0x3D700, symSize: 0x30 }
  - { offsetInCU: 0x1693, offset: 0x2AB6E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV08completeC05nonce12codeVerifier7handlerySSSg_AHyAA01_C20CompletionParametersCctF', symObjAddr: 0xEE0, symBinAddr: 0x3D730, symSize: 0x3B0 }
  - { offsetInCU: 0x179D, offset: 0x2AC78, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV25exchangeCodeForTokensWith5nonce12codeVerifier7handlerySSSg_AHyAA01_C20CompletionParametersCctF', symObjAddr: 0x1290, symBinAddr: 0x3DAE0, symSize: 0xA40 }
  - { offsetInCU: 0x19D1, offset: 0x2AEAC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV25exchangeCodeForTokensWith5nonce12codeVerifier7handlerySSSg_AHyAA01_C20CompletionParametersCctFySo27FBSDKGraphRequestConnecting_pSg_ypSgs5Error_pSgtcfU_', symObjAddr: 0x3200, symBinAddr: 0x3FA50, symSize: 0x730 }
  - { offsetInCU: 0x1B03, offset: 0x2AFDE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV25exchangeNonceForTokenWith7handler014authenticationF0yyAA01_C20CompletionParametersCc_SStF', symObjAddr: 0x1CD0, symBinAddr: 0x3E520, symSize: 0x6D0 }
  - { offsetInCU: 0x1CCD, offset: 0x2B1A8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV25exchangeNonceForTokenWith7handler014authenticationF0yyAA01_C20CompletionParametersCc_SStFySo27FBSDKGraphRequestConnecting_pSg_ypSgs5Error_pSgtcfU_', symObjAddr: 0x2DD0, symBinAddr: 0x3F620, symSize: 0x430 }
  - { offsetInCU: 0x1D6F, offset: 0x2B24A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV24fetchAndSetPropertiesFor10parameters5nonce7handleryAA01_C20CompletionParametersC_SSyAIctF', symObjAddr: 0x23A0, symBinAddr: 0x3EBF0, symSize: 0x420 }
  - { offsetInCU: 0x1E2E, offset: 0x2B309, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV24fetchAndSetPropertiesFor10parameters5nonce7handleryAA01_C20CompletionParametersC_SSyAIctFySo24FBSDKAuthenticationTokenCSgcfU_', symObjAddr: 0x27C0, symBinAddr: 0x3F010, symSize: 0x1E0 }
  - { offsetInCU: 0x1F2B, offset: 0x2B406, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV7profile4with11permissions09FBSDKCoreB07ProfileCSgAG25AuthenticationTokenClaimsC_ShySSGSgtF', symObjAddr: 0x29A0, symBinAddr: 0x3F1F0, symSize: 0x10 }
  - { offsetInCU: 0x1F3E, offset: 0x2B419, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV18expirationDateFrom10parameters10Foundation0F0VSDySSypG_tF', symObjAddr: 0x29B0, symBinAddr: 0x3F200, symSize: 0x310 }
  - { offsetInCU: 0x2020, offset: 0x2B4FB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV28dataAccessExpirationDateFrom10parameters10Foundation0H0VSDySSypG_tF', symObjAddr: 0x2CC0, symBinAddr: 0x3F510, symSize: 0x100 }
  - { offsetInCU: 0x2077, offset: 0x2B552, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV9challenge4fromSSSgSDySSypG_tF', symObjAddr: 0x2DC0, symBinAddr: 0x3F610, symSize: 0x10 }
  - { offsetInCU: 0x20B1, offset: 0x2B58C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV14profileFactoryAA15ProfileCreating_pvg', symObjAddr: 0x3980, symBinAddr: 0x401D0, symSize: 0x20 }
  - { offsetInCU: 0x20C4, offset: 0x2B59F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV14profileFactoryAA15ProfileCreating_pvs', symObjAddr: 0x39A0, symBinAddr: 0x401F0, symSize: 0x30 }
  - { offsetInCU: 0x20D7, offset: 0x2B5B2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV14profileFactoryAA15ProfileCreating_pvM', symObjAddr: 0x39D0, symBinAddr: 0x40220, symSize: 0x10 }
  - { offsetInCU: 0x20EA, offset: 0x2B5C5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV14profileFactoryAA15ProfileCreating_pvM.resume.0', symObjAddr: 0x39E0, symBinAddr: 0x40230, symSize: 0x10 }
  - { offsetInCU: 0x20FD, offset: 0x2B5D8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV26authenticationTokenCreatorAA014AuthenticationH8Creating_pvg', symObjAddr: 0x39F0, symBinAddr: 0x40240, symSize: 0x20 }
  - { offsetInCU: 0x2110, offset: 0x2B5EB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV26authenticationTokenCreatorAA014AuthenticationH8Creating_pvs', symObjAddr: 0x3A10, symBinAddr: 0x40260, symSize: 0x30 }
  - { offsetInCU: 0x2123, offset: 0x2B5FE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV26authenticationTokenCreatorAA014AuthenticationH8Creating_pvM', symObjAddr: 0x3A40, symBinAddr: 0x40290, symSize: 0x20 }
  - { offsetInCU: 0x2136, offset: 0x2B611, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV26authenticationTokenCreatorAA014AuthenticationH8Creating_pvM.resume.0', symObjAddr: 0x3A60, symBinAddr: 0x402B0, symSize: 0x10 }
  - { offsetInCU: 0x2149, offset: 0x2B624, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV19graphRequestFactorySo010FBSDKGraphhI0_pvg', symObjAddr: 0x3A70, symBinAddr: 0x402C0, symSize: 0x10 }
  - { offsetInCU: 0x215C, offset: 0x2B637, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV19graphRequestFactorySo010FBSDKGraphhI0_pvs', symObjAddr: 0x3A80, symBinAddr: 0x402D0, symSize: 0x20 }
  - { offsetInCU: 0x216F, offset: 0x2B64A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV19graphRequestFactorySo010FBSDKGraphhI0_pvM', symObjAddr: 0x3AA0, symBinAddr: 0x402F0, symSize: 0x20 }
  - { offsetInCU: 0x2182, offset: 0x2B65D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV19graphRequestFactorySo010FBSDKGraphhI0_pvM.resume.0', symObjAddr: 0x3AC0, symBinAddr: 0x40310, symSize: 0x10 }
  - { offsetInCU: 0x2195, offset: 0x2B670, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV15internalUtilitySo15FBSDKURLHosting_pvg', symObjAddr: 0x3AD0, symBinAddr: 0x40320, symSize: 0x10 }
  - { offsetInCU: 0x21A8, offset: 0x2B683, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV15internalUtilitySo15FBSDKURLHosting_pvs', symObjAddr: 0x3AE0, symBinAddr: 0x40330, symSize: 0x20 }
  - { offsetInCU: 0x21BB, offset: 0x2B696, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV15internalUtilitySo15FBSDKURLHosting_pvM', symObjAddr: 0x3B00, symBinAddr: 0x40350, symSize: 0x20 }
  - { offsetInCU: 0x21CE, offset: 0x2B6A9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV15internalUtilitySo15FBSDKURLHosting_pvM.resume.0', symObjAddr: 0x3B20, symBinAddr: 0x40370, symSize: 0x10 }
  - { offsetInCU: 0x21E1, offset: 0x2B6BC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV12errorFactorySo18FBSDKErrorCreating_pvg', symObjAddr: 0x3B30, symBinAddr: 0x40380, symSize: 0x10 }
  - { offsetInCU: 0x21F4, offset: 0x2B6CF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV12errorFactorySo18FBSDKErrorCreating_pvs', symObjAddr: 0x3B40, symBinAddr: 0x40390, symSize: 0x20 }
  - { offsetInCU: 0x2207, offset: 0x2B6E2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV12errorFactorySo18FBSDKErrorCreating_pvM', symObjAddr: 0x3B60, symBinAddr: 0x403B0, symSize: 0x20 }
  - { offsetInCU: 0x221A, offset: 0x2B6F5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV12errorFactorySo18FBSDKErrorCreating_pvM.resume.0', symObjAddr: 0x3B80, symBinAddr: 0x403D0, symSize: 0x10 }
  - { offsetInCU: 0x2233, offset: 0x2B70E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV14profileFactory26authenticationTokenCreator012graphRequestH015internalUtility05errorH0AeA15ProfileCreating_p_AA014AuthenticationjR0_pSo010FBSDKGraphmH0_pSo15FBSDKURLHosting_pSo010FBSDKErrorR0_ptcfC', symObjAddr: 0x3B90, symBinAddr: 0x403E0, symSize: 0x50 }
  - { offsetInCU: 0x2246, offset: 0x2B721, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV22configuredDependenciesAC04TypeF0VSgvMZ', symObjAddr: 0x3C90, symBinAddr: 0x404E0, symSize: 0x60 }
  - { offsetInCU: 0x22AC, offset: 0x2B787, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV19defaultDependenciesAC04TypeF0VSgvMZ', symObjAddr: 0x3FE0, symBinAddr: 0x40830, symSize: 0x60 }
  - { offsetInCU: 0x22CB, offset: 0x2B7A6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV19defaultDependenciesAC04TypeF0VSgvMZ.resume.0', symObjAddr: 0x4040, symBinAddr: 0x40890, symSize: 0x10 }
  - { offsetInCU: 0x8D, offset: 0x2B92C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginUtilityO9getUserID4fromSSSgAF_tFZTf4nd_n', symObjAddr: 0x80, symBinAddr: 0x43060, symSize: 0x340 }
  - { offsetInCU: 0x154, offset: 0x2B9F3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginUtilityO18getQueryParameters4fromSDySSypGSg10Foundation3URLV_tFZTf4nd_n', symObjAddr: 0x3C0, symBinAddr: 0x433A0, symSize: 0x5C0 }
  - { offsetInCU: 0x2B3, offset: 0x2BB52, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginUtilityOMa', symObjAddr: 0x980, symBinAddr: 0x43960, symSize: 0x10 }
  - { offsetInCU: 0x2C6, offset: 0x2BB65, size: 0x8, addend: 0x0, symName: '_$s10Foundation3URLVSgWOc', symObjAddr: 0xA70, symBinAddr: 0x43970, symSize: 0x40 }
  - { offsetInCU: 0x47F, offset: 0x2BD1E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginUtilityO17stringForAudienceySSAA07DefaultG0OFZ', symObjAddr: 0x0, symBinAddr: 0x42FE0, symSize: 0x60 }
  - { offsetInCU: 0x4AE, offset: 0x2BD4D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginUtilityO18getQueryParameters4fromSDySSypGSg10Foundation3URLV_tFZ', symObjAddr: 0x60, symBinAddr: 0x43040, symSize: 0x10 }
  - { offsetInCU: 0x4C1, offset: 0x2BD60, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginUtilityO9getUserID4fromSSSgAF_tFZ', symObjAddr: 0x70, symBinAddr: 0x43050, symSize: 0x10 }
  - { offsetInCU: 0x27, offset: 0x2BE0A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24MissingDependenciesErrorV3forACyxGxm_tcfC', symObjAddr: 0x0, symBinAddr: 0x439E0, symSize: 0x10 }
  - { offsetInCU: 0x72, offset: 0x2BE55, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24MissingDependenciesErrorVyxGs23CustomStringConvertibleAAsAEP11descriptionSSvgTW', symObjAddr: 0x110, symBinAddr: 0x43AF0, symSize: 0x10 }
  - { offsetInCU: 0xA3, offset: 0x2BE86, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24MissingDependenciesErrorVMi', symObjAddr: 0x120, symBinAddr: 0x43B00, symSize: 0x10 }
  - { offsetInCU: 0xB6, offset: 0x2BE99, size: 0x8, addend: 0x0, symName: ___swift_memcpy8_8, symObjAddr: 0x130, symBinAddr: 0x43B10, symSize: 0x10 }
  - { offsetInCU: 0xC9, offset: 0x2BEAC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24MissingDependenciesErrorVwet', symObjAddr: 0x150, symBinAddr: 0x43B20, symSize: 0x40 }
  - { offsetInCU: 0xDC, offset: 0x2BEBF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24MissingDependenciesErrorVwst', symObjAddr: 0x190, symBinAddr: 0x43B60, symSize: 0x40 }
  - { offsetInCU: 0xEF, offset: 0x2BED2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24MissingDependenciesErrorVMa', symObjAddr: 0x1D0, symBinAddr: 0x43BA0, symSize: 0x10 }
  - { offsetInCU: 0x102, offset: 0x2BEE5, size: 0x8, addend: 0x0, symName: ___swift_instantiateGenericMetadata, symObjAddr: 0x1E0, symBinAddr: 0x43BB0, symSize: 0x26 }
  - { offsetInCU: 0x18E, offset: 0x2BF71, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24MissingDependenciesErrorVyxGs0E0AAsAEP7_domainSSvgTW', symObjAddr: 0xD0, symBinAddr: 0x43AB0, symSize: 0x10 }
  - { offsetInCU: 0x1AA, offset: 0x2BF8D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24MissingDependenciesErrorVyxGs0E0AAsAEP5_codeSivgTW', symObjAddr: 0xE0, symBinAddr: 0x43AC0, symSize: 0x10 }
  - { offsetInCU: 0x1C6, offset: 0x2BFA9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24MissingDependenciesErrorVyxGs0E0AAsAEP9_userInfoyXlSgvgTW', symObjAddr: 0xF0, symBinAddr: 0x43AD0, symSize: 0x10 }
  - { offsetInCU: 0x1E1, offset: 0x2BFC4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24MissingDependenciesErrorVyxGs0E0AAsAEP19_getEmbeddedNSErroryXlSgyFTW', symObjAddr: 0x100, symBinAddr: 0x43AE0, symSize: 0x10 }
  - { offsetInCU: 0x299, offset: 0x2C07C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24MissingDependenciesErrorV3forACyxGxm_tcfC', symObjAddr: 0x0, symBinAddr: 0x439E0, symSize: 0x10 }
  - { offsetInCU: 0x2F5, offset: 0x2C0D8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24MissingDependenciesErrorV11descriptionSSvg', symObjAddr: 0x10, symBinAddr: 0x439F0, symSize: 0xC0 }
  - { offsetInCU: 0x27, offset: 0x2C1B4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14NonceValidatorO7isValid5nonceSbSS_tFZ', symObjAddr: 0x0, symBinAddr: 0x43BE0, symSize: 0xD0 }
  - { offsetInCU: 0x3D, offset: 0x2C1CA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14NonceValidatorOMa', symObjAddr: 0x100, symBinAddr: 0x43CB0, symSize: 0x10 }
  - { offsetInCU: 0x10B, offset: 0x2C298, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14NonceValidatorO7isValid5nonceSbSS_tFZ', symObjAddr: 0x0, symBinAddr: 0x43BE0, symSize: 0xD0 }
  - { offsetInCU: 0x27, offset: 0x2C30F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14ProfileFactoryCACycfC', symObjAddr: 0x0, symBinAddr: 0x43CE0, symSize: 0x20 }
  - { offsetInCU: 0xDA, offset: 0x2C3C2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14ProfileFactoryCACycfcTo', symObjAddr: 0x60, symBinAddr: 0x43D40, symSize: 0x30 }
  - { offsetInCU: 0x12A, offset: 0x2C412, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14ProfileFactoryCAA0C8CreatingA2aDP06createC06userID9firstName06middleJ004lastJ04name7linkURL11refreshDate05imageO05email9friendIDs8birthday8ageRange8hometown8location6gender11permissions9isLimited09FBSDKCoreB00C0CSS_SSSgA_A_A_10Foundation0O0VSgA0_0Q0VSgA3_A_SaySSGSgA6_So012FBSDKUserAgeX0CSgSo13FBSDKLocationCSgA14_A_ShySSGSgSbtFTW', symObjAddr: 0xC0, symBinAddr: 0x43DA0, symSize: 0x10 }
  - { offsetInCU: 0x15A, offset: 0x2C442, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14ProfileFactoryC06createC06userID9firstName06middleI004lastI04name7linkURL11refreshDate05imageN05email9friendIDs8birthday8ageRange8hometown8location6gender11permissions9isLimited09FBSDKCoreB00C0CSS_SSSgA3Y10Foundation0N0VSgAZ0P0VSgA1_AYSaySSGSgA4_So012FBSDKUserAgeW0CSgSo13FBSDKLocationCSgA12_AYShySSGSgSbtFTf4nnnnnnnnnnnnnnnnnd_n', symObjAddr: 0xD0, symBinAddr: 0x43DB0, symSize: 0x200 }
  - { offsetInCU: 0x28C, offset: 0x2C574, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14ProfileFactoryCMa', symObjAddr: 0x2D0, symBinAddr: 0x43FB0, symSize: 0x20 }
  - { offsetInCU: 0x4B1, offset: 0x2C799, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14ProfileFactoryCACycfC', symObjAddr: 0x0, symBinAddr: 0x43CE0, symSize: 0x20 }
  - { offsetInCU: 0x4C4, offset: 0x2C7AC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14ProfileFactoryC06createC06userID9firstName06middleI004lastI04name7linkURL11refreshDate05imageN05email9friendIDs8birthday8ageRange8hometown8location6gender11permissions9isLimited09FBSDKCoreB00C0CSS_SSSgA3Y10Foundation0N0VSgAZ0P0VSgA1_AYSaySSGSgA4_So012FBSDKUserAgeW0CSgSo13FBSDKLocationCSgA12_AYShySSGSgSbtF', symObjAddr: 0x20, symBinAddr: 0x43D00, symSize: 0x10 }
  - { offsetInCU: 0x4D7, offset: 0x2C7BF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14ProfileFactoryCACycfc', symObjAddr: 0x30, symBinAddr: 0x43D10, symSize: 0x30 }
  - { offsetInCU: 0x511, offset: 0x2C7F9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14ProfileFactoryCfD', symObjAddr: 0x90, symBinAddr: 0x43D70, symSize: 0x30 }
  - { offsetInCU: 0x27, offset: 0x2C880, size: 0x8, addend: 0x0, symName: '_$s12FBSDKCoreKit27ServerConfigurationProviderC010FBSDKLoginB00cD9ProvidingA2dEP04loadcD010completionyySo0F7TooltipCSg_s5Error_pSgtcSg_tFTW', symObjAddr: 0x0, symBinAddr: 0x44000, symSize: 0x18 }
  - { offsetInCU: 0x64, offset: 0x2C8BD, size: 0x8, addend: 0x0, symName: '_$s12FBSDKCoreKit27ServerConfigurationProviderC010FBSDKLoginB00cD9ProvidingA2dEP04loadcD010completionyySo0F7TooltipCSg_s5Error_pSgtcSg_tFTW', symObjAddr: 0x0, symBinAddr: 0x44000, symSize: 0x18 }
  - { offsetInCU: 0x183, offset: 0x2CACE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC5errors5Error_pSgvgTo', symObjAddr: 0xAC0, symBinAddr: 0x44B20, symSize: 0x60 }
  - { offsetInCU: 0x1D5, offset: 0x2CB20, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC5errors5Error_pSgvsTo', symObjAddr: 0xB60, symBinAddr: 0x44BC0, symSize: 0x60 }
  - { offsetInCU: 0x2BC, offset: 0x2CC07, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersCACycfcTo', symObjAddr: 0x1600, symBinAddr: 0x455E0, symSize: 0x20 }
  - { offsetInCU: 0x2ED, offset: 0x2CC38, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DateVSgWOc', symObjAddr: 0xCB0, symBinAddr: 0x44D10, symSize: 0x40 }
  - { offsetInCU: 0x300, offset: 0x2CC4B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersCfETo', symObjAddr: 0x1650, symBinAddr: 0x45630, symSize: 0x130 }
  - { offsetInCU: 0x32D, offset: 0x2CC78, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersCMU', symObjAddr: 0x1780, symBinAddr: 0x45760, symSize: 0x10 }
  - { offsetInCU: 0x340, offset: 0x2CC8B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersCMa', symObjAddr: 0x1790, symBinAddr: 0x45770, symSize: 0x30 }
  - { offsetInCU: 0x353, offset: 0x2CC9E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersCMr', symObjAddr: 0x17C0, symBinAddr: 0x457A0, symSize: 0xC0 }
  - { offsetInCU: 0x366, offset: 0x2CCB1, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DateVSgMa', symObjAddr: 0x1880, symBinAddr: 0x45860, symSize: 0x50 }
  - { offsetInCU: 0x379, offset: 0x2CCC4, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DateVSgWOh', symObjAddr: 0x1910, symBinAddr: 0x458B0, symSize: 0x30 }
  - { offsetInCU: 0x459, offset: 0x2CDA4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersCACycfC', symObjAddr: 0x0, symBinAddr: 0x44060, symSize: 0x20 }
  - { offsetInCU: 0x46C, offset: 0x2CDB7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC19authenticationTokenSo019FBSDKAuthenticationG0CSgvg', symObjAddr: 0x40, symBinAddr: 0x440A0, symSize: 0x40 }
  - { offsetInCU: 0x493, offset: 0x2CDDE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC19authenticationTokenSo019FBSDKAuthenticationG0CSgvM', symObjAddr: 0xC0, symBinAddr: 0x44120, symSize: 0x40 }
  - { offsetInCU: 0x4B6, offset: 0x2CE01, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC7profile09FBSDKCoreB07ProfileCSgvg', symObjAddr: 0x160, symBinAddr: 0x441C0, symSize: 0x40 }
  - { offsetInCU: 0x4D7, offset: 0x2CE22, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC7profile09FBSDKCoreB07ProfileCSgvM', symObjAddr: 0x280, symBinAddr: 0x442E0, symSize: 0x40 }
  - { offsetInCU: 0x4FA, offset: 0x2CE45, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC17accessTokenStringSSSgvM', symObjAddr: 0x340, symBinAddr: 0x443A0, symSize: 0x40 }
  - { offsetInCU: 0x51D, offset: 0x2CE68, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC11nonceStringSSSgvM', symObjAddr: 0x400, symBinAddr: 0x44460, symSize: 0x40 }
  - { offsetInCU: 0x540, offset: 0x2CE8B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC25authenticationTokenStringSSSgvM', symObjAddr: 0x4C0, symBinAddr: 0x44520, symSize: 0x40 }
  - { offsetInCU: 0x563, offset: 0x2CEAE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC4codeSSSgvM', symObjAddr: 0x580, symBinAddr: 0x445E0, symSize: 0x40 }
  - { offsetInCU: 0x586, offset: 0x2CED1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC11permissionsShyAA12FBPermissionCGSgvM', symObjAddr: 0x640, symBinAddr: 0x446A0, symSize: 0x40 }
  - { offsetInCU: 0x5A9, offset: 0x2CEF4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC19declinedPermissionsShyAA12FBPermissionCGSgvM', symObjAddr: 0x700, symBinAddr: 0x44760, symSize: 0x40 }
  - { offsetInCU: 0x5CC, offset: 0x2CF17, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC18expiredPermissionsShyAA12FBPermissionCGSgvM', symObjAddr: 0x900, symBinAddr: 0x44960, symSize: 0x40 }
  - { offsetInCU: 0x5EF, offset: 0x2CF3A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC5appIDSSSgvM', symObjAddr: 0x9C0, symBinAddr: 0x44A20, symSize: 0x40 }
  - { offsetInCU: 0x612, offset: 0x2CF5D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC6userIDSSSgvM', symObjAddr: 0xA80, symBinAddr: 0x44AE0, symSize: 0x40 }
  - { offsetInCU: 0x64C, offset: 0x2CF97, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC5errors5Error_pSgvg', symObjAddr: 0xB20, symBinAddr: 0x44B80, symSize: 0x40 }
  - { offsetInCU: 0x689, offset: 0x2CFD4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC5errors5Error_pSgvM', symObjAddr: 0xC30, symBinAddr: 0x44C90, symSize: 0x40 }
  - { offsetInCU: 0x6AC, offset: 0x2CFF7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC14expirationDate10Foundation0G0VSgvM', symObjAddr: 0xDB0, symBinAddr: 0x44D90, symSize: 0x40 }
  - { offsetInCU: 0x6CF, offset: 0x2D01A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC24dataAccessExpirationDate10Foundation0I0VSgvM', symObjAddr: 0x1090, symBinAddr: 0x45070, symSize: 0x40 }
  - { offsetInCU: 0x6F2, offset: 0x2D03D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC9challengeSSSgvM', symObjAddr: 0x1150, symBinAddr: 0x45130, symSize: 0x40 }
  - { offsetInCU: 0x715, offset: 0x2D060, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC11graphDomainSSSgvM', symObjAddr: 0x1210, symBinAddr: 0x451F0, symSize: 0x40 }
  - { offsetInCU: 0x738, offset: 0x2D083, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC11graphDomainSSSgvM.resume.0', symObjAddr: 0x1250, symBinAddr: 0x45230, symSize: 0x10 }
  - { offsetInCU: 0x757, offset: 0x2D0A2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC14userTokenNonceSSSgvM', symObjAddr: 0x1450, symBinAddr: 0x45430, symSize: 0x40 }
  - { offsetInCU: 0x77A, offset: 0x2D0C5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersCACycfc', symObjAddr: 0x1490, symBinAddr: 0x45470, symSize: 0x170 }
  - { offsetInCU: 0x79D, offset: 0x2D0E8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersCfD', symObjAddr: 0x1620, symBinAddr: 0x45600, symSize: 0x30 }
...
