#if 0
#elif defined(__arm64__) && __arm64__
// Generated by Apple Swift version 5.10 (swiftlang-5.10.0.13 clang-1500.3.9.4)
#ifndef FBSDKLOGINKIT_SWIFT_H
#define FBSDKLOGINKIT_SWIFT_H
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wgcc-compat"

#if !defined(__has_include)
# define __has_include(x) 0
#endif
#if !defined(__has_attribute)
# define __has_attribute(x) 0
#endif
#if !defined(__has_feature)
# define __has_feature(x) 0
#endif
#if !defined(__has_warning)
# define __has_warning(x) 0
#endif

#if __has_include(<swift/objc-prologue.h>)
# include <swift/objc-prologue.h>
#endif

#pragma clang diagnostic ignored "-Wauto-import"
#if defined(__OBJC__)
#include <Foundation/Foundation.h>
#endif
#if defined(__cplusplus)
#include <cstdint>
#include <cstddef>
#include <cstdbool>
#include <cstring>
#include <stdlib.h>
#include <new>
#include <type_traits>
#else
#include <stdint.h>
#include <stddef.h>
#include <stdbool.h>
#include <string.h>
#endif
#if defined(__cplusplus)
#if defined(__arm64e__) && __has_include(<ptrauth.h>)
# include <ptrauth.h>
#else
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wreserved-macro-identifier"
# ifndef __ptrauth_swift_value_witness_function_pointer
#  define __ptrauth_swift_value_witness_function_pointer(x)
# endif
# ifndef __ptrauth_swift_class_method_pointer
#  define __ptrauth_swift_class_method_pointer(x)
# endif
#pragma clang diagnostic pop
#endif
#endif

#if !defined(SWIFT_TYPEDEFS)
# define SWIFT_TYPEDEFS 1
# if __has_include(<uchar.h>)
#  include <uchar.h>
# elif !defined(__cplusplus)
typedef uint_least16_t char16_t;
typedef uint_least32_t char32_t;
# endif
typedef float swift_float2  __attribute__((__ext_vector_type__(2)));
typedef float swift_float3  __attribute__((__ext_vector_type__(3)));
typedef float swift_float4  __attribute__((__ext_vector_type__(4)));
typedef double swift_double2  __attribute__((__ext_vector_type__(2)));
typedef double swift_double3  __attribute__((__ext_vector_type__(3)));
typedef double swift_double4  __attribute__((__ext_vector_type__(4)));
typedef int swift_int2  __attribute__((__ext_vector_type__(2)));
typedef int swift_int3  __attribute__((__ext_vector_type__(3)));
typedef int swift_int4  __attribute__((__ext_vector_type__(4)));
typedef unsigned int swift_uint2  __attribute__((__ext_vector_type__(2)));
typedef unsigned int swift_uint3  __attribute__((__ext_vector_type__(3)));
typedef unsigned int swift_uint4  __attribute__((__ext_vector_type__(4)));
#endif

#if !defined(SWIFT_PASTE)
# define SWIFT_PASTE_HELPER(x, y) x##y
# define SWIFT_PASTE(x, y) SWIFT_PASTE_HELPER(x, y)
#endif
#if !defined(SWIFT_METATYPE)
# define SWIFT_METATYPE(X) Class
#endif
#if !defined(SWIFT_CLASS_PROPERTY)
# if __has_feature(objc_class_property)
#  define SWIFT_CLASS_PROPERTY(...) __VA_ARGS__
# else
#  define SWIFT_CLASS_PROPERTY(...) 
# endif
#endif
#if !defined(SWIFT_RUNTIME_NAME)
# if __has_attribute(objc_runtime_name)
#  define SWIFT_RUNTIME_NAME(X) __attribute__((objc_runtime_name(X)))
# else
#  define SWIFT_RUNTIME_NAME(X) 
# endif
#endif
#if !defined(SWIFT_COMPILE_NAME)
# if __has_attribute(swift_name)
#  define SWIFT_COMPILE_NAME(X) __attribute__((swift_name(X)))
# else
#  define SWIFT_COMPILE_NAME(X) 
# endif
#endif
#if !defined(SWIFT_METHOD_FAMILY)
# if __has_attribute(objc_method_family)
#  define SWIFT_METHOD_FAMILY(X) __attribute__((objc_method_family(X)))
# else
#  define SWIFT_METHOD_FAMILY(X) 
# endif
#endif
#if !defined(SWIFT_NOESCAPE)
# if __has_attribute(noescape)
#  define SWIFT_NOESCAPE __attribute__((noescape))
# else
#  define SWIFT_NOESCAPE 
# endif
#endif
#if !defined(SWIFT_RELEASES_ARGUMENT)
# if __has_attribute(ns_consumed)
#  define SWIFT_RELEASES_ARGUMENT __attribute__((ns_consumed))
# else
#  define SWIFT_RELEASES_ARGUMENT 
# endif
#endif
#if !defined(SWIFT_WARN_UNUSED_RESULT)
# if __has_attribute(warn_unused_result)
#  define SWIFT_WARN_UNUSED_RESULT __attribute__((warn_unused_result))
# else
#  define SWIFT_WARN_UNUSED_RESULT 
# endif
#endif
#if !defined(SWIFT_NORETURN)
# if __has_attribute(noreturn)
#  define SWIFT_NORETURN __attribute__((noreturn))
# else
#  define SWIFT_NORETURN 
# endif
#endif
#if !defined(SWIFT_CLASS_EXTRA)
# define SWIFT_CLASS_EXTRA 
#endif
#if !defined(SWIFT_PROTOCOL_EXTRA)
# define SWIFT_PROTOCOL_EXTRA 
#endif
#if !defined(SWIFT_ENUM_EXTRA)
# define SWIFT_ENUM_EXTRA 
#endif
#if !defined(SWIFT_CLASS)
# if __has_attribute(objc_subclassing_restricted)
#  define SWIFT_CLASS(SWIFT_NAME) SWIFT_RUNTIME_NAME(SWIFT_NAME) __attribute__((objc_subclassing_restricted)) SWIFT_CLASS_EXTRA
#  define SWIFT_CLASS_NAMED(SWIFT_NAME) __attribute__((objc_subclassing_restricted)) SWIFT_COMPILE_NAME(SWIFT_NAME) SWIFT_CLASS_EXTRA
# else
#  define SWIFT_CLASS(SWIFT_NAME) SWIFT_RUNTIME_NAME(SWIFT_NAME) SWIFT_CLASS_EXTRA
#  define SWIFT_CLASS_NAMED(SWIFT_NAME) SWIFT_COMPILE_NAME(SWIFT_NAME) SWIFT_CLASS_EXTRA
# endif
#endif
#if !defined(SWIFT_RESILIENT_CLASS)
# if __has_attribute(objc_class_stub)
#  define SWIFT_RESILIENT_CLASS(SWIFT_NAME) SWIFT_CLASS(SWIFT_NAME) __attribute__((objc_class_stub))
#  define SWIFT_RESILIENT_CLASS_NAMED(SWIFT_NAME) __attribute__((objc_class_stub)) SWIFT_CLASS_NAMED(SWIFT_NAME)
# else
#  define SWIFT_RESILIENT_CLASS(SWIFT_NAME) SWIFT_CLASS(SWIFT_NAME)
#  define SWIFT_RESILIENT_CLASS_NAMED(SWIFT_NAME) SWIFT_CLASS_NAMED(SWIFT_NAME)
# endif
#endif
#if !defined(SWIFT_PROTOCOL)
# define SWIFT_PROTOCOL(SWIFT_NAME) SWIFT_RUNTIME_NAME(SWIFT_NAME) SWIFT_PROTOCOL_EXTRA
# define SWIFT_PROTOCOL_NAMED(SWIFT_NAME) SWIFT_COMPILE_NAME(SWIFT_NAME) SWIFT_PROTOCOL_EXTRA
#endif
#if !defined(SWIFT_EXTENSION)
# define SWIFT_EXTENSION(M) SWIFT_PASTE(M##_Swift_, __LINE__)
#endif
#if !defined(OBJC_DESIGNATED_INITIALIZER)
# if __has_attribute(objc_designated_initializer)
#  define OBJC_DESIGNATED_INITIALIZER __attribute__((objc_designated_initializer))
# else
#  define OBJC_DESIGNATED_INITIALIZER 
# endif
#endif
#if !defined(SWIFT_ENUM_ATTR)
# if __has_attribute(enum_extensibility)
#  define SWIFT_ENUM_ATTR(_extensibility) __attribute__((enum_extensibility(_extensibility)))
# else
#  define SWIFT_ENUM_ATTR(_extensibility) 
# endif
#endif
#if !defined(SWIFT_ENUM)
# define SWIFT_ENUM(_type, _name, _extensibility) enum _name : _type _name; enum SWIFT_ENUM_ATTR(_extensibility) SWIFT_ENUM_EXTRA _name : _type
# if __has_feature(generalized_swift_name)
#  define SWIFT_ENUM_NAMED(_type, _name, SWIFT_NAME, _extensibility) enum _name : _type _name SWIFT_COMPILE_NAME(SWIFT_NAME); enum SWIFT_COMPILE_NAME(SWIFT_NAME) SWIFT_ENUM_ATTR(_extensibility) SWIFT_ENUM_EXTRA _name : _type
# else
#  define SWIFT_ENUM_NAMED(_type, _name, SWIFT_NAME, _extensibility) SWIFT_ENUM(_type, _name, _extensibility)
# endif
#endif
#if !defined(SWIFT_UNAVAILABLE)
# define SWIFT_UNAVAILABLE __attribute__((unavailable))
#endif
#if !defined(SWIFT_UNAVAILABLE_MSG)
# define SWIFT_UNAVAILABLE_MSG(msg) __attribute__((unavailable(msg)))
#endif
#if !defined(SWIFT_AVAILABILITY)
# define SWIFT_AVAILABILITY(plat, ...) __attribute__((availability(plat, __VA_ARGS__)))
#endif
#if !defined(SWIFT_WEAK_IMPORT)
# define SWIFT_WEAK_IMPORT __attribute__((weak_import))
#endif
#if !defined(SWIFT_DEPRECATED)
# define SWIFT_DEPRECATED __attribute__((deprecated))
#endif
#if !defined(SWIFT_DEPRECATED_MSG)
# define SWIFT_DEPRECATED_MSG(...) __attribute__((deprecated(__VA_ARGS__)))
#endif
#if !defined(SWIFT_DEPRECATED_OBJC)
# if __has_feature(attribute_diagnose_if_objc)
#  define SWIFT_DEPRECATED_OBJC(Msg) __attribute__((diagnose_if(1, Msg, "warning")))
# else
#  define SWIFT_DEPRECATED_OBJC(Msg) SWIFT_DEPRECATED_MSG(Msg)
# endif
#endif
#if defined(__OBJC__)
#if !defined(IBSegueAction)
# define IBSegueAction 
#endif
#endif
#if !defined(SWIFT_EXTERN)
# if defined(__cplusplus)
#  define SWIFT_EXTERN extern "C"
# else
#  define SWIFT_EXTERN extern
# endif
#endif
#if !defined(SWIFT_CALL)
# define SWIFT_CALL __attribute__((swiftcall))
#endif
#if !defined(SWIFT_INDIRECT_RESULT)
# define SWIFT_INDIRECT_RESULT __attribute__((swift_indirect_result))
#endif
#if !defined(SWIFT_CONTEXT)
# define SWIFT_CONTEXT __attribute__((swift_context))
#endif
#if !defined(SWIFT_ERROR_RESULT)
# define SWIFT_ERROR_RESULT __attribute__((swift_error_result))
#endif
#if defined(__cplusplus)
# define SWIFT_NOEXCEPT noexcept
#else
# define SWIFT_NOEXCEPT 
#endif
#if !defined(SWIFT_C_INLINE_THUNK)
# if __has_attribute(always_inline)
# if __has_attribute(nodebug)
#  define SWIFT_C_INLINE_THUNK inline __attribute__((always_inline)) __attribute__((nodebug))
# else
#  define SWIFT_C_INLINE_THUNK inline __attribute__((always_inline))
# endif
# else
#  define SWIFT_C_INLINE_THUNK inline
# endif
#endif
#if defined(_WIN32)
#if !defined(SWIFT_IMPORT_STDLIB_SYMBOL)
# define SWIFT_IMPORT_STDLIB_SYMBOL __declspec(dllimport)
#endif
#else
#if !defined(SWIFT_IMPORT_STDLIB_SYMBOL)
# define SWIFT_IMPORT_STDLIB_SYMBOL 
#endif
#endif
#if defined(__OBJC__)
#if __has_feature(objc_modules)
#if __has_warning("-Watimport-in-framework-header")
#pragma clang diagnostic ignored "-Watimport-in-framework-header"
#endif
@import CoreFoundation;
@import FBSDKCoreKit;
@import Foundation;
@import ObjectiveC;
@import UIKit;
#endif

#import <FBSDKLoginKit/FBSDKLoginKit.h>

#endif
#pragma clang diagnostic ignored "-Wproperty-attribute-mismatch"
#pragma clang diagnostic ignored "-Wduplicate-method-arg"
#if __has_warning("-Wpragma-clang-attribute")
# pragma clang diagnostic ignored "-Wpragma-clang-attribute"
#endif
#pragma clang diagnostic ignored "-Wunknown-pragmas"
#pragma clang diagnostic ignored "-Wnullability"
#pragma clang diagnostic ignored "-Wdollar-in-identifier-extension"

#if __has_attribute(external_source_symbol)
# pragma push_macro("any")
# undef any
# pragma clang attribute push(__attribute__((external_source_symbol(language="Swift", defined_in="FBSDKLoginKit",generated_declaration))), apply_to=any(function,enum,objc_interface,objc_category,objc_protocol))
# pragma pop_macro("any")
#endif

#if defined(__OBJC__)

@class NSString;

/// Represents a code verifier used in the PKCE (Proof Key for Code Exchange)
/// process. This is a cryptographically random string using the characters
/// A-Z, a-z, 0-9, and the punctuation characters -._~ (hyphen, period,
/// underscore, and tilde), between 43 and 128 characters long.
SWIFT_CLASS_NAMED("CodeVerifier")
@interface FBSDKCodeVerifier : NSObject
/// The string value of the code verifier
@property (nonatomic, readonly, copy) NSString * _Nonnull value;
/// The SHA256 hashed challenge of the code verifier
@property (nonatomic, readonly, copy) NSString * _Nonnull challenge;
/// Attempts to initialize a new code verifier instance with the given string.
/// Creation will fail and return nil if the string is invalid.
/// @param string the code verifier string
- (nullable instancetype)initWithString:(NSString * _Nonnull)string;
/// Initializes a new code verifier instance with a random string value
- (nonnull instancetype)init;
@end

/// Passed to openURL to indicate which default audience to use for sessions that post data to Facebook.
/// Certain operations such as publishing a status or publishing a photo require an audience. When the user
/// grants an application permission to perform a publish operation, a default audience is selected as the
/// publication ceiling for the application. This enumerated value allows the application to select which
/// audience to ask the user to grant publish permission for.
typedef SWIFT_ENUM_NAMED(NSUInteger, FBSDKDefaultAudience, "DefaultAudience", open) {
/// Indicates that the user’s friends are able to see posts made by the application
  FBSDKDefaultAudienceFriends = 0,
/// Indicates that only the user is able to see posts made by the application
  FBSDKDefaultAudienceOnlyMe = 1,
/// Indicates that all Facebook users are able to see posts made by the application
  FBSDKDefaultAudienceEveryone = 2,
};

@class NSURL;
@class NSDate;

/// Describes the initial response when starting the device login flow.
/// This is used by <code>DeviceLoginManager</code>.
SWIFT_CLASS_NAMED("DeviceLoginCodeInfo")
@interface FBSDKDeviceLoginCodeInfo : NSObject
/// The unique id for this login flow.
@property (nonatomic, readonly, copy) NSString * _Nonnull identifier;
/// The short “user_code” that should be presented to the user.
@property (nonatomic, readonly, copy) NSString * _Nonnull loginCode;
/// The verification URL.
@property (nonatomic, readonly, copy) NSURL * _Nonnull verificationURL;
/// The expiration date.
@property (nonatomic, readonly, copy) NSDate * _Nonnull expirationDate;
/// The polling interval
@property (nonatomic, readonly) NSUInteger pollingInterval;
- (nonnull instancetype)initWithIdentifier:(NSString * _Nonnull)identifier loginCode:(NSString * _Nonnull)loginCode verificationURL:(NSURL * _Nonnull)verificationURL expirationDate:(NSDate * _Nonnull)expirationDate pollingInterval:(NSUInteger)pollingInterval OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

/// Custom error codes for device login errors in the login error domain
typedef SWIFT_ENUM_NAMED(NSInteger, FBSDKDeviceLoginError, "DeviceLoginErrorCode", open) {
/// Your device is polling too frequently.
  FBSDKDeviceLoginErrorExcessivePolling = 1349172,
/// User has declined to authorize your application.
  FBSDKDeviceLoginErrorAuthorizationDeclined = 1349173,
/// User has not yet authorized your application. Continue polling.
  FBSDKDeviceLoginErrorAuthorizationPending = 1349174,
/// The code you entered has expired.
  FBSDKDeviceLoginErrorCodeExpired = 1349152,
};

@protocol FBSDKDeviceLoginManagerDelegate;

/// Use this class to perform a device login flow.
/// The device login flow starts by requesting a code from the device login API.
/// This class informs the delegate when this code is received. You should then present the
/// code to the user to enter. In the meantime, this class polls the device login API
/// periodically and informs the delegate of the results.
/// See <a href="https://developers.facebook.com/docs/facebook-login/for-devices">Facebook Device Login</a>.
SWIFT_CLASS_NAMED("DeviceLoginManager")
@interface FBSDKDeviceLoginManager : NSObject
/// The device login manager delegate.
@property (nonatomic, weak) id <FBSDKDeviceLoginManagerDelegate> _Nullable delegate;
/// The requested permissions.
@property (nonatomic, readonly, copy) NSArray<NSString *> * _Nonnull permissions;
/// The optional URL to redirect the user to after they complete the login.
/// The URL must be configured in your App Settings -> Advanced -> OAuth Redirect URIs
@property (nonatomic, copy) NSURL * _Nullable redirectURL;
/// Initializes a new instance.
/// @param permissions The permissions to request.
/// @param enableSmartLogin Whether to enable smart login.
- (nonnull instancetype)initWithPermissions:(NSArray<NSString *> * _Nonnull)permissions enableSmartLogin:(BOOL)enableSmartLogin OBJC_DESIGNATED_INITIALIZER;
/// Starts the device login flow
/// This instance will retain self until the flow is finished or cancelled.
- (void)start;
/// Attempts to cancel the device login flow.
- (void)cancel;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

@class NSNetService;
@class NSNumber;

@interface FBSDKDeviceLoginManager (SWIFT_EXTENSION(FBSDKLoginKit)) <NSNetServiceDelegate>
- (void)netService:(NSNetService * _Nonnull)service didNotPublish:(NSDictionary<NSString *, NSNumber *> * _Nonnull)errorValues;
@end


@class FBSDKDeviceLoginManagerResult;

/// A delegate for <code>DeviceLoginManager</code>.
SWIFT_PROTOCOL_NAMED("DeviceLoginManagerDelegate")
@protocol FBSDKDeviceLoginManagerDelegate
/// Indicates the device login flow has started. You should parse <code>codeInfo</code> to present the code to the user to enter.
/// @param loginManager the login manager instance.
/// @param codeInfo the code info data.
- (void)deviceLoginManager:(FBSDKDeviceLoginManager * _Nonnull)loginManager startedWithCodeInfo:(FBSDKDeviceLoginCodeInfo * _Nonnull)codeInfo;
/// Indicates the device login flow has finished.
/// @param loginManager the login manager instance.
/// @param result the results of the login flow.
/// @param error the error, if available.
/// The flow can be finished if the user completed the flow, cancelled, or if the code has expired.
- (void)deviceLoginManager:(FBSDKDeviceLoginManager * _Nonnull)loginManager completedWithResult:(FBSDKDeviceLoginManagerResult * _Nullable)result error:(NSError * _Nullable)error;
@end

@class FBSDKAccessToken;

/// Represents the results of the a device login flow. This is used by <code>DeviceLoginManager</code>
SWIFT_CLASS_NAMED("DeviceLoginManagerResult")
@interface FBSDKDeviceLoginManagerResult : NSObject
/// The token
@property (nonatomic, readonly, strong) FBSDKAccessToken * _Nullable accessToken;
/// Indicates if the login was cancelled by the user, or if the device login code has expired.
@property (nonatomic, readonly) BOOL isCancelled;
/// Internal method exposed to facilitate transition to Swift.
/// API Subject to change or removal without warning. Do not use.
/// @warning INTERNAL - DO NOT USE
- (nonnull instancetype)initWithToken:(FBSDKAccessToken * _Nullable)token isCancelled:(BOOL)cancelled OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

@protocol FBSDKLoginButtonDelegate;
enum FBSDKLoginButtonTooltipBehavior : NSUInteger;
enum FBSDKTooltipColorStyle : NSUInteger;
enum FBSDKLoginTracking : NSUInteger;
@class NSCoder;

/// A button that initiates a log in or log out flow upon tapping.
/// <code>LoginButton</code> works with <code>AccessToken.current</code> to determine what to display,
/// and automatically starts authentication when tapped (i.e., you do not need to manually subscribe action targets).
/// Like <code>LoginManager</code>, you should make sure your app delegate is connected to <code>ApplicationDelegate</code>
/// in order for the button’s delegate to receive messages.
/// <code>LoginButton</code> has a fixed height of 30 pixels, but you may change the width.
/// Initializing the button with <code>nil</code> frame will size the button to its minimum frame.
SWIFT_CLASS_NAMED("FBLoginButton")
@interface FBSDKLoginButton : FBSDKButton
/// The default audience to use, if publish permissions are requested at login time.
@property (nonatomic) enum FBSDKDefaultAudience defaultAudience;
/// Gets or sets the delegate.
@property (nonatomic, weak) IBOutlet id <FBSDKLoginButtonDelegate> _Nullable delegate;
/// The permissions to request.
/// To provide the best experience, you should minimize the number of permissions you request, and only ask for them when needed.
/// For example, do not ask for “user_location” until you the information is actually used by the app.
/// Note this is converted to NSSet and is only
/// an NSArray for the convenience of literal syntax.
/// See <a href="https://developers.facebook.com/docs/facebook-login/permissions/">the permissions guide</a> for more details.
@property (nonatomic, copy) NSArray<NSString *> * _Nonnull permissions;
/// Gets or sets the desired tooltip behavior.
@property (nonatomic) enum FBSDKLoginButtonTooltipBehavior tooltipBehavior;
/// Gets or sets the desired tooltip color style.
@property (nonatomic) enum FBSDKTooltipColorStyle tooltipColorStyle;
/// Gets or sets the desired tracking preference to use for login attempts. Defaults to <code>.enabled</code>
@property (nonatomic) enum FBSDKLoginTracking loginTracking;
/// Gets or sets an optional nonce to use for login attempts. A valid nonce must be a non-empty string without whitespace.
/// An invalid nonce will not be set. Instead, default unique nonces will be used for login attempts.
@property (nonatomic, copy) NSString * _Nullable nonce;
/// Gets or sets an optional page id to use for login attempts.
@property (nonatomic, copy) NSString * _Nullable messengerPageId;
/// Gets or sets the login authorization type to use in the login request. Defaults to <code>rerequest</code>. Use <code>nil</code> to avoid
/// requesting permissions that were previously denied.
@property (nonatomic) FBSDKLoginAuthType _Nullable authType;
/// The code verifier used in the PKCE process.
/// If not provided, a code verifier will be randomly generated.
@property (nonatomic, strong) FBSDKCodeVerifier * _Nonnull codeVerifier;
- (nonnull instancetype)initWithFrame:(CGRect)frame OBJC_DESIGNATED_INITIALIZER;
- (nullable instancetype)initWithCoder:(NSCoder * _Nonnull)coder SWIFT_UNAVAILABLE;
- (void)didMoveToWindow;
- (CGRect)imageRectForContentRect:(CGRect)contentRect SWIFT_WARN_UNUSED_RESULT;
- (CGRect)titleRectForContentRect:(CGRect)contentRect SWIFT_WARN_UNUSED_RESULT;
- (void)layoutSubviews;
- (CGSize)sizeThatFits:(CGSize)size SWIFT_WARN_UNUSED_RESULT;
@end

/// Indicates the desired login tooltip behavior.
typedef SWIFT_ENUM_NAMED(NSUInteger, FBSDKLoginButtonTooltipBehavior, "TooltipBehavior", open) {
/// The default behavior. The tooltip will only be displayed if
/// the app is eligible (determined by possible server round trip)
  FBSDKLoginButtonTooltipBehaviorAutomatic = 0,
/// Force display of the tooltip (typically for UI testing)
  FBSDKLoginButtonTooltipBehaviorForceDisplay = 1,
/// Force disable. In this case you can still exert more refined
/// control by manually constructing a <code>FBSDKLoginTooltipView</code> instance.
  FBSDKLoginButtonTooltipBehaviorDisable = 2,
};

enum FBSDKTooltipViewArrowDirection : NSUInteger;

/// Tooltip bubble with text in it used to display tips for UI elements,
/// with a pointed arrow (to refer to the UI element).
/// The tooltip fades in and will automatically fade out. See <code>displayDuration</code>.
SWIFT_CLASS_NAMED("FBTooltipView")
@interface FBSDKTooltipView : UIView
/// Gets or sets the amount of time in seconds the tooltip should be displayed.
/// Set this to zero to make the display permanent until explicitly dismissed.
/// Defaults to six seconds.
@property (nonatomic) NSTimeInterval displayDuration;
/// Gets or sets the color style after initialization.
/// Defaults to value passed to -initWithTagline:message:colorStyle:.
@property (nonatomic) enum FBSDKTooltipColorStyle colorStyle;
/// Gets or sets the message.
@property (nonatomic, copy) NSString * _Nullable message;
/// Gets or sets the optional phrase that comprises the first part of the label (and is highlighted differently).
@property (nonatomic, copy) NSString * _Nullable tagline;
/// Convenience constructor
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
/// Designated initializer.
/// If you need to show a tooltip for login, consider using the <code>FBSDKLoginTooltipView</code> view.
/// See FBSDKLoginTooltipView
/// \param tagline First part of the label, that will be highlighted with different color. Can be nil.
///
/// \param message Main message to display.
///
/// \param colorStyle Color style to use for tooltip.
///
- (nonnull instancetype)initWithTagline:(NSString * _Nullable)tagline message:(NSString * _Nullable)message colorStyle:(enum FBSDKTooltipColorStyle)colorStyle OBJC_DESIGNATED_INITIALIZER;
- (nullable instancetype)initWithCoder:(NSCoder * _Nonnull)coder OBJC_DESIGNATED_INITIALIZER SWIFT_UNAVAILABLE;
/// Show tooltip at the top or at the bottom of given view.
/// Tooltip will be added to anchorView.window.rootViewController.view
/// Use this method to present the tooltip with automatic positioning or
/// use -presentInView:withArrowPosition:direction: for manual positioning
/// If anchorView is nil or has no window - this method does nothing.
/// \param anchorView view to show at, must be already added to window view hierarchy, in order to decide
/// where tooltip will be shown. (If there’s not enough space at the top of the anchorView in window bounds -
/// tooltip will be shown at the bottom of it)
///
- (void)presentFromView:(UIView * _Nonnull)anchorView;
/// Adds tooltip to given view, with given position and arrow direction.
/// \param view View to be used as superview.
///
/// \param arrowPosition Point in view’s cordinates, where arrow will be pointing
///
/// \param direction whenever arrow should be pointing up (message bubble is below the arrow) or down (message bubble is above the arrow).
///
- (void)presentInView:(UIView * _Nonnull)view withArrowPosition:(CGPoint)arrowPosition direction:(enum FBSDKTooltipViewArrowDirection)direction;
/// Remove tooltip manually.
/// Calling this method isn’t necessary - tooltip will dismiss itself automatically after the <code>displayDuration</code>.
- (void)dismiss;
- (void)drawRect:(CGRect)rect;
- (void)layoutSubviews;
- (nonnull instancetype)initWithFrame:(CGRect)frame SWIFT_UNAVAILABLE;
@end

/// FBSDKTooltipViewArrowDirection enum
/// Passed on construction to determine arrow orientation.
typedef SWIFT_ENUM_NAMED(NSUInteger, FBSDKTooltipViewArrowDirection, "ArrowDirection", closed) {
  FBSDKTooltipViewArrowDirectionDown = 0,
  FBSDKTooltipViewArrowDirectionUp = 1,
};

/// FBSDKTooltipColorStyle enum
/// Passed on construction to determine color styling.
typedef SWIFT_ENUM_NAMED(NSUInteger, FBSDKTooltipColorStyle, "ColorStyle", closed) {
  FBSDKTooltipColorStyleFriendlyBlue = 0,
  FBSDKTooltipColorStyleNeutralGray = 1,
};

@protocol FBSDKLoginTooltipViewDelegate;

/// Represents a tooltip to be displayed next to a Facebook login button
/// to highlight features for new users.
/// The <code>FBSDKLoginButton</code> may display this view automatically. If you do
/// not use the <code>FBSDKLoginButton</code>, you can manually call one of the <code>present*</code> methods
/// as appropriate and customize behavior via <code>FBSDKLoginTooltipViewDelegate</code> delegate.
/// By default, the <code>FBSDKLoginTooltipView</code> is not added to the superview until it is
/// determined the app has migrated to the new login experience. You can override this
/// (e.g., to test the UI layout) by implementing the delegate or setting <code>forceDisplay</code> to YES.
SWIFT_CLASS_NAMED("FBLoginTooltipView")
@interface FBSDKLoginTooltipView : FBSDKTooltipView
/// the delegate
@property (nonatomic, weak) id <FBSDKLoginTooltipViewDelegate> _Nullable delegate;
/// if set to YES, the view will always be displayed and the delegate’s
/// <code>loginTooltipView:shouldAppear:</code> will NOT be called.
@property (nonatomic) BOOL forceDisplay;
/// if set to YES, the view will always be displayed and the delegate’s
/// <code>loginTooltipView:shouldAppear:</code> will NOT be called.
@property (nonatomic) BOOL shouldForceDisplay;
/// Create tooltip
- (nonnull instancetype)init;
- (nonnull instancetype)initWithTagline:(NSString * _Nullable)tagline message:(NSString * _Nullable)message colorStyle:(enum FBSDKTooltipColorStyle)colorStyle OBJC_DESIGNATED_INITIALIZER;
- (void)presentInView:(UIView * _Nonnull)view withArrowPosition:(CGPoint)arrowPosition direction:(enum FBSDKTooltipViewArrowDirection)direction;
@end


/// Internal Type exposed to facilitate transition to Swift.
/// API Subject to change or removal without warning. Do not use.
/// <ul>
///   <li>
///     Warning INTERNAL:  DO NOT USE
///   </li>
/// </ul>
SWIFT_CLASS_NAMED("FBPermission")
@interface FBSDKPermission : NSObject
@property (nonatomic, readonly, copy) NSString * _Nonnull description;
@property (nonatomic, readonly) NSUInteger hash;
/// Attempts to initialize a new permission with the given string.
/// Creation will fail and return nil if the string is invalid.
/// \param string The raw permission string
///
- (nullable instancetype)initWithString:(NSString * _Nonnull)string OBJC_DESIGNATED_INITIALIZER;
/// Returns a set of <code>FBPermission</code> from a set of raw permissions strings.
/// Will return nil if any of the input permissions is invalid.
+ (NSSet<FBSDKPermission *> * _Nullable)permissionsFromRawPermissions:(NSSet<NSString *> * _Nonnull)rawPermissions SWIFT_WARN_UNUSED_RESULT;
/// Returns a set of string permissions from a set of <code>FBPermission</code> by
/// extracting the “value” property for each element.
+ (NSSet<NSString *> * _Nonnull)rawPermissionsFromPermissions:(NSSet<FBSDKPermission *> * _Nonnull)permissions SWIFT_WARN_UNUSED_RESULT;
- (BOOL)isEqual:(id _Nullable)object SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end




@class FBSDKLoginManagerLoginResult;

/// A delegate for <code>FBSDKLoginButton</code>
SWIFT_PROTOCOL_NAMED("LoginButtonDelegate")
@protocol FBSDKLoginButtonDelegate <NSObject>
/// Sent to the delegate when the button was used to login.
/// @param loginButton The button being used to log in
/// @param result The results of the login
/// @param error The error (if any) from the login
- (void)loginButton:(FBSDKLoginButton * _Nonnull)loginButton didCompleteWithResult:(FBSDKLoginManagerLoginResult * _Nullable)result error:(NSError * _Nullable)error;
/// Sent to the delegate when the button was used to logout.
/// @param loginButton The button being used to log out.
- (void)loginButtonDidLogOut:(FBSDKLoginButton * _Nonnull)loginButton;
@optional
/// Sent to the delegate when the button is about to login.
/// @param loginButton The button being used to log in
/// @return <code>true</code> if the login should be allowed to proceed, <code>false</code> otherwise
- (BOOL)loginButtonWillLogin:(FBSDKLoginButton * _Nonnull)loginButton SWIFT_WARN_UNUSED_RESULT;
@end


/// A configuration to use for modifying the behavior of a login attempt.
SWIFT_CLASS_NAMED("LoginConfiguration")
@interface FBSDKLoginConfiguration : NSObject
/// The nonce that the configuration was created with.
/// A unique nonce will be used if none is provided to the initializer.
@property (nonatomic, readonly, copy) NSString * _Nonnull nonce;
/// The tracking  preference. Defaults to <code>.enabled</code>.
@property (nonatomic, readonly) enum FBSDKLoginTracking tracking;
/// The requested permissions for the login attempt. Defaults to an empty set.
@property (nonatomic, readonly, copy) NSSet<FBSDKPermission *> * _Nonnull requestedPermissions;
/// The Messenger Page Id associated with this login request.
@property (nonatomic, readonly, copy) NSString * _Nullable messengerPageId;
/// The auth type associated with this login request.
@property (nonatomic, readonly) FBSDKLoginAuthType _Nullable authType;
/// The code verifier used in the PKCE process.
/// If not provided, a code verifier will be randomly generated.
@property (nonatomic, readonly, strong) FBSDKCodeVerifier * _Nonnull codeVerifier;
/// Attempts to initialize a new configuration with the expected parameters.
/// @param permissions the requested permissions for a login attempt. Permissions must be an array of strings that do not contain whitespace.
/// @param tracking the tracking preference to use for a login attempt.
/// @param nonce an optional nonce to use for the login attempt. A valid nonce must be a non-empty string without whitespace.
/// Creation of the configuration will fail if the nonce is invalid.
/// @param messengerPageId the associated page id  to use for a login attempt.
- (nullable instancetype)initWithPermissions:(NSArray<NSString *> * _Nonnull)permissions tracking:(enum FBSDKLoginTracking)tracking nonce:(NSString * _Nonnull)nonce messengerPageId:(NSString * _Nullable)messengerPageId;
/// Attempts to initialize a new configuration with the expected parameters.
/// @param permissions the requested permissions for a login attempt. Permissions must be an array of strings that do not contain whitespace.
/// @param tracking the tracking preference to use for a login attempt.
/// @param nonce an optional nonce to use for the login attempt. A valid nonce must be a non-empty string without whitespace.
/// Creation of the configuration will fail if the nonce is invalid.
/// @param messengerPageId the associated page id  to use for a login attempt.
/// @param authType auth_type param to use for login.
- (nullable instancetype)initWithPermissions:(NSArray<NSString *> * _Nonnull)permissions tracking:(enum FBSDKLoginTracking)tracking nonce:(NSString * _Nonnull)nonce messengerPageId:(NSString * _Nullable)messengerPageId authType:(FBSDKLoginAuthType _Nullable)authType;
/// Attempts to initialize a new configuration with the expected parameters.
/// @param permissions the requested permissions for a login attempt. Permissions must be an array of strings that do not contain whitespace.
/// @param tracking the tracking preference to use for a login attempt.
/// @param nonce an optional nonce to use for the login attempt. A valid nonce must be a non-empty string without whitespace.
/// Creation of the configuration will fail if the nonce is invalid.
- (nullable instancetype)initWithPermissions:(NSArray<NSString *> * _Nonnull)permissions tracking:(enum FBSDKLoginTracking)tracking nonce:(NSString * _Nonnull)nonce;
/// Attempts to initialize a new configuration with the expected parameters.
/// @param permissions the requested permissions for the login attempt. Permissions must be an array of strings that do not contain whitespace.
/// @param tracking the tracking preference to use for a login attempt.
/// @param messengerPageId the associated page id  to use for a login attempt.
- (nullable instancetype)initWithPermissions:(NSArray<NSString *> * _Nonnull)permissions tracking:(enum FBSDKLoginTracking)tracking messengerPageId:(NSString * _Nullable)messengerPageId;
/// Attempts to initialize a new configuration with the expected parameters.
/// @param permissions the requested permissions for the login attempt. Permissions must be an array of strings that do not contain whitespace.
/// @param tracking the tracking preference to use for a login attempt.
/// @param messengerPageId the associated page id  to use for a login attempt.
/// @param authType auth_type param to use for login.
- (nullable instancetype)initWithPermissions:(NSArray<NSString *> * _Nonnull)permissions tracking:(enum FBSDKLoginTracking)tracking messengerPageId:(NSString * _Nullable)messengerPageId authType:(FBSDKLoginAuthType _Nullable)authType;
/// Attempts to initialize a new configuration with the expected parameters.
/// @param permissions the requested permissions for a login attempt. Permissions must be an array of strings that do not contain whitespace.
/// @param tracking the tracking preference to use for a login attempt.
/// @param nonce an optional nonce to use for the login attempt. A valid nonce must be a non-empty string without whitespace.
/// Creation of the configuration will fail if the nonce is invalid.
/// @param messengerPageId the associated page id  to use for a login attempt.
/// @param authType auth_type param to use for login.
/// @param codeVerifier The code verifier used in the PKCE process.
- (nullable instancetype)initWithPermissions:(NSArray<NSString *> * _Nonnull)permissions tracking:(enum FBSDKLoginTracking)tracking nonce:(NSString * _Nonnull)nonce messengerPageId:(NSString * _Nullable)messengerPageId authType:(FBSDKLoginAuthType _Nullable)authType codeVerifier:(FBSDKCodeVerifier * _Nonnull)codeVerifier OBJC_DESIGNATED_INITIALIZER;
/// Attempts to initialize a new configuration with the expected parameters.
/// @param permissions the requested permissions for the login attempt. Permissions must be an array of strings that do not contain whitespace.
/// @param tracking the tracking preference to use for a login attempt.
- (nullable instancetype)initWithPermissions:(NSArray<NSString *> * _Nonnull)permissions tracking:(enum FBSDKLoginTracking)tracking;
/// Attempts to initialize a new configuration with the expected parameters.
/// @param tracking the login tracking preference to use for a login attempt.
- (nullable instancetype)initWithTracking:(enum FBSDKLoginTracking)tracking;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

/// Custom error codes for login errors in the login error domain
typedef SWIFT_ENUM_NAMED(NSInteger, FBSDKLoginError, "LoginErrorCode", open) {
/// Reserved
  FBSDKLoginErrorReserved = 300,
/// The error code for unknown errors
  FBSDKLoginErrorUnknown = 301,
/// The user’s password has changed and must log in again
  FBSDKLoginErrorPasswordChanged = 302,
/// The user must log in to their account on www.facebook.com to restore access
  FBSDKLoginErrorUserCheckpointed = 303,
/// Indicates a failure to request new permissions because the user has changed
  FBSDKLoginErrorUserMismatch = 304,
/// The user must confirm their account with Facebook before logging in
  FBSDKLoginErrorUnconfirmedUser = 305,
/// The Accounts framework failed without returning an error, indicating the app’s slider in the
/// iOS Facebook Settings (device Settings -> Facebook -> App Name) has been disabled.
  FBSDKLoginErrorSystemAccountAppDisabled = 306,
/// An error occurred related to Facebook system Account store
  FBSDKLoginErrorSystemAccountUnavailable = 307,
/// The login response was missing a valid challenge string
  FBSDKLoginErrorBadChallengeString = 308,
/// The ID token returned in login response was invalid
  FBSDKLoginErrorInvalidIDToken = 309,
/// A current access token was required and not provided
  FBSDKLoginErrorMissingAccessToken = 310,
};

@class UIViewController;

/// Provides methods for logging the user in and out.
/// It works directly with <code>AccessToken</code> (for data access) and <code>AuthenticationToken</code> (for authentication);
/// it sets the “current” tokens upon successful authorizations (or sets to <code>nil</code> in case of <code>logOut</code>).
/// You should check <code>AccessToken.current</code> before calling a login method to see if there is
/// a cached token available (typically in a <code>viewDidLoad</code> implementation).
/// @warning If you are managing your own tokens outside of <code>AccessToken</code>, you will need to set
/// <code>AccessToken.current</code> before calling a login method to authorize further permissions on your tokens.
SWIFT_CLASS_NAMED("LoginManager")
@interface FBSDKLoginManager : NSObject
/// The default audience. You should set this if you intend to ask for publish permissions.
@property (nonatomic) enum FBSDKDefaultAudience defaultAudience;
/// Initialize an instance of <code>LoginManager.</code>
/// \param defaultAudience Optional default audience to use. Default: <code>.friends</code>.
///
- (nonnull instancetype)initWithDefaultAudience:(enum FBSDKDefaultAudience)defaultAudience;
/// Logs the user in or authorizes additional permissions.
/// @param viewController the view controller from which to present the login UI. If nil, the topmost view
/// controller will be automatically determined and used.
/// @param configuration the login configuration to use.
/// @param completion the login completion handler.
/// Use this method when asking for permissions. You should only ask for permissions when they
/// are needed and the value should be explained to the user. You can inspect the
/// <code>FBSDKLoginManagerLoginResultBlock</code>’s <code>result.declinedPermissions</code> to provide more information
/// to the user if they decline permissions.
/// To reduce unnecessary login attempts, you should typically check if <code>AccessToken.current</code>
/// already contains the permissions you need. If it does, you probably do not need to call this method.
/// @warning You can only perform one login call at a time. Calling a login method before the completion handler is
/// called on a previous login attempt will result in an error.
/// @warning This method will present a UI to the user and thus should be called on the main thread.
- (void)logInFromViewController:(UIViewController * _Nullable)viewController configuration:(FBSDKLoginConfiguration * _Nullable)configuration completion:(FBSDKLoginManagerLoginResultBlock _Nonnull)completion;
/// Logs the user in or authorizes additional permissions.
/// @param permissions the optional array of permissions. Note this is converted to NSSet and is only
/// an NSArray for the convenience of literal syntax.
/// @param viewController the view controller to present from. If nil, the topmost view controller will be
/// automatically determined as best as possible.
/// @param handler the callback.
/// Use this method when asking for read permissions. You should only ask for permissions when they
/// are needed and explain the value to the user. You can inspect the <code>FBSDKLoginManagerLoginResultBlock</code>’s
/// <code>result.declinedPermissions</code> to provide more information to the user if they decline permissions.
/// You typically should check if <code>AccessToken.current</code> already contains the permissions you need before
/// asking to reduce unnecessary login attempts. For example, you could perform that check in <code>viewDidLoad</code>.
/// @warning You can only perform one login call at a time. Calling a login method before the completion handler is
/// called on a previous login attempt will result in an error.
/// @warning This method will present a UI to the user and thus should be called on the main thread.
- (void)logInWithPermissions:(NSArray<NSString *> * _Nonnull)permissions fromViewController:(UIViewController * _Nullable)viewController handler:(FBSDKLoginManagerLoginResultBlock _Nullable)handler;
/// Requests user’s permission to reathorize application’s data access, after it has expired due to inactivity.
/// @param viewController the view controller from which to present the login UI. If nil, the topmost view
/// controller will be automatically determined and used.
/// @param handler the callback.
/// Use this method when you need to reathorize your app’s access to user data via the Graph API.
/// You should only call this after access has expired.
/// You should provide as much context to the user as possible as to why you need to reauthorize the access, the
/// scope of access being reathorized, and what added value your app provides when the access is reathorized.
/// You can inspect the <code>result.declinedPermissions</code> to determine if you should provide more information to the
/// user based on any declined permissions.
/// @warning This method will reauthorize using a <code>LoginConfiguration</code> with <code>FBSDKLoginTracking</code> set to <code>.enabled</code>.
/// @warning This method will present UI the user. You typically should call this if <code>AccessToken.isDataAccessExpired</code>
/// is true.
- (void)reauthorizeDataAccess:(UIViewController * _Nonnull)viewController handler:(FBSDKLoginManagerLoginResultBlock _Nonnull)handler;
/// Logs the user out
/// This nils out the singleton instances of <code>AccessToken</code>, <code>AuthenticationToken</code> and <code>Profle</code>.
/// @note This is only a client side logout. It will not log the user out of their Facebook account.
- (void)logOut;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end



@class UIApplication;

@interface FBSDKLoginManager (SWIFT_EXTENSION(FBSDKLoginKit)) <FBSDKURLOpening>
+ (FBSDKLoginManager * _Nonnull)makeOpener SWIFT_WARN_UNUSED_RESULT;
- (BOOL)application:(UIApplication * _Nullable)application openURL:(NSURL * _Nullable)url sourceApplication:(NSString * _Nullable)sourceApplication annotation:(id _Nullable)annotation SWIFT_WARN_UNUSED_RESULT;
- (BOOL)canOpenURL:(NSURL * _Nonnull)url forApplication:(UIApplication * _Nullable)application sourceApplication:(NSString * _Nullable)sourceApplication annotation:(id _Nullable)annotation SWIFT_WARN_UNUSED_RESULT;
- (void)applicationDidBecomeActive:(UIApplication * _Nonnull)application;
- (BOOL)isAuthenticationURL:(NSURL * _Nonnull)url SWIFT_WARN_UNUSED_RESULT;
- (BOOL)shouldStopPropagationOfURL:(NSURL * _Nonnull)url SWIFT_WARN_UNUSED_RESULT;
@end

@class FBSDKAuthenticationToken;

/// Describes the result of a login attempt.
SWIFT_CLASS_NAMED("LoginManagerLoginResult")
@interface FBSDKLoginManagerLoginResult : NSObject
/// The access token
@property (nonatomic, readonly, strong) FBSDKAccessToken * _Nullable token;
/// The authentication token
@property (nonatomic, readonly, strong) FBSDKAuthenticationToken * _Nullable authenticationToken;
/// Whether the login was cancelled by the user
@property (nonatomic, readonly) BOOL isCancelled;
/// The set of permissions granted by the user in the associated request.
/// Inspect the token’s permissions set for a complete list.
@property (nonatomic, readonly, copy) NSSet<NSString *> * _Nonnull grantedPermissions;
/// The set of permissions declined by the user in the associated request.
/// Inspect the token’s permissions set for a complete list.
@property (nonatomic, readonly, copy) NSSet<NSString *> * _Nonnull declinedPermissions;
/// Creates a new result
/// @param token The access token
/// @param authenticationToken The authentication token
/// @param isCancelled whether The login was cancelled by the user
/// @param grantedPermissions The set of granted permissions
/// @param declinedPermissions The set of declined permissions
- (nonnull instancetype)initWithToken:(FBSDKAccessToken * _Nullable)token authenticationToken:(FBSDKAuthenticationToken * _Nullable)authenticationToken isCancelled:(BOOL)isCancelled grantedPermissions:(NSSet<NSString *> * _Nonnull)grantedPermissions declinedPermissions:(NSSet<NSString *> * _Nonnull)declinedPermissions OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end


/// The <code>LoginTooltipViewDelegate</code> protocol defines the methods used to receive event
/// notifications from <code>FBLoginTooltipView</code> objects.
SWIFT_PROTOCOL_NAMED("LoginTooltipViewDelegate")
@protocol FBSDKLoginTooltipViewDelegate
@optional
/// Asks the delegate if the tooltip view should appear
/// @param view The tooltip view.
/// @param appIsEligible The value fetched from the server identifying if the app
/// is eligible for the new login experience.
/// Use this method to customize display behavior.
- (BOOL)loginTooltipView:(FBSDKLoginTooltipView * _Nonnull)view shouldAppear:(BOOL)appIsEligible SWIFT_WARN_UNUSED_RESULT;
/// Tells the delegate the tooltip view will appear, specifically after it’s been
/// added to the super view but before the fade in animation.
/// @param view The tooltip view.
- (void)loginTooltipViewWillAppear:(FBSDKLoginTooltipView * _Nonnull)view;
/// Tells the delegate the tooltip view will not appear (i.e., was not
/// added to the super view).
/// @param view The tooltip view.
- (void)loginTooltipViewWillNotAppear:(FBSDKLoginTooltipView * _Nonnull)view;
@end

/// <code>enabled</code> and <code>limited</code> see: https://developers.facebook.com/docs/facebook-login/ios/limited-login/
typedef SWIFT_ENUM_NAMED(NSUInteger, FBSDKLoginTracking, "LoginTracking", open) {
  FBSDKLoginTrackingEnabled = 0,
  FBSDKLoginTrackingLimited = 1,
};


@class FBSDKProfile;

/// Internal Type exposed to facilitate transition to Swift.
/// API Subject to change or removal without warning. Do not use.
/// <ul>
///   <li>
///     Warning INTERNAL:  DO NOT USE
///   </li>
/// </ul>
/// Structured interface for accessing the parameters used to complete a log in request.
/// If <code>authenticationTokenString</code> is non-<code>nil</code>, the authentication succeeded. If <code>error</code> is
/// non-<code>nil</code> the request failed. If both are <code>nil</code>, the request was cancelled.
SWIFT_CLASS_NAMED("_LoginCompletionParameters")
@interface FBSDKLoginCompletionParameters : NSObject
@property (nonatomic, strong) FBSDKAuthenticationToken * _Nullable authenticationToken;
@property (nonatomic, strong) FBSDKProfile * _Nullable profile;
@property (nonatomic, copy) NSString * _Nullable accessTokenString;
@property (nonatomic, copy) NSString * _Nullable nonceString;
@property (nonatomic, copy) NSString * _Nullable authenticationTokenString;
@property (nonatomic, copy) NSString * _Nullable code;
@property (nonatomic, copy) NSSet<FBSDKPermission *> * _Nullable permissions;
@property (nonatomic, copy) NSSet<FBSDKPermission *> * _Nullable declinedPermissions;
@property (nonatomic, copy) NSSet<FBSDKPermission *> * _Nullable expiredPermissions;
@property (nonatomic, copy) NSString * _Nullable appID;
@property (nonatomic, copy) NSString * _Nullable userID;
@property (nonatomic) NSError * _Nullable error;
@property (nonatomic, copy) NSDate * _Nullable expirationDate;
@property (nonatomic, copy) NSDate * _Nullable dataAccessExpirationDate;
@property (nonatomic, copy) NSString * _Nullable challenge;
@property (nonatomic, copy) NSString * _Nullable graphDomain;
@property (nonatomic, copy) NSString * _Nullable userTokenNonce;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

#endif
#if __has_attribute(external_source_symbol)
# pragma clang attribute pop
#endif
#if defined(__cplusplus)
#endif
#pragma clang diagnostic pop
#endif

#elif defined(__x86_64__) && __x86_64__
// Generated by Apple Swift version 5.10 (swiftlang-5.10.0.13 clang-1500.3.9.4)
#ifndef FBSDKLOGINKIT_SWIFT_H
#define FBSDKLOGINKIT_SWIFT_H
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wgcc-compat"

#if !defined(__has_include)
# define __has_include(x) 0
#endif
#if !defined(__has_attribute)
# define __has_attribute(x) 0
#endif
#if !defined(__has_feature)
# define __has_feature(x) 0
#endif
#if !defined(__has_warning)
# define __has_warning(x) 0
#endif

#if __has_include(<swift/objc-prologue.h>)
# include <swift/objc-prologue.h>
#endif

#pragma clang diagnostic ignored "-Wauto-import"
#if defined(__OBJC__)
#include <Foundation/Foundation.h>
#endif
#if defined(__cplusplus)
#include <cstdint>
#include <cstddef>
#include <cstdbool>
#include <cstring>
#include <stdlib.h>
#include <new>
#include <type_traits>
#else
#include <stdint.h>
#include <stddef.h>
#include <stdbool.h>
#include <string.h>
#endif
#if defined(__cplusplus)
#if defined(__arm64e__) && __has_include(<ptrauth.h>)
# include <ptrauth.h>
#else
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wreserved-macro-identifier"
# ifndef __ptrauth_swift_value_witness_function_pointer
#  define __ptrauth_swift_value_witness_function_pointer(x)
# endif
# ifndef __ptrauth_swift_class_method_pointer
#  define __ptrauth_swift_class_method_pointer(x)
# endif
#pragma clang diagnostic pop
#endif
#endif

#if !defined(SWIFT_TYPEDEFS)
# define SWIFT_TYPEDEFS 1
# if __has_include(<uchar.h>)
#  include <uchar.h>
# elif !defined(__cplusplus)
typedef uint_least16_t char16_t;
typedef uint_least32_t char32_t;
# endif
typedef float swift_float2  __attribute__((__ext_vector_type__(2)));
typedef float swift_float3  __attribute__((__ext_vector_type__(3)));
typedef float swift_float4  __attribute__((__ext_vector_type__(4)));
typedef double swift_double2  __attribute__((__ext_vector_type__(2)));
typedef double swift_double3  __attribute__((__ext_vector_type__(3)));
typedef double swift_double4  __attribute__((__ext_vector_type__(4)));
typedef int swift_int2  __attribute__((__ext_vector_type__(2)));
typedef int swift_int3  __attribute__((__ext_vector_type__(3)));
typedef int swift_int4  __attribute__((__ext_vector_type__(4)));
typedef unsigned int swift_uint2  __attribute__((__ext_vector_type__(2)));
typedef unsigned int swift_uint3  __attribute__((__ext_vector_type__(3)));
typedef unsigned int swift_uint4  __attribute__((__ext_vector_type__(4)));
#endif

#if !defined(SWIFT_PASTE)
# define SWIFT_PASTE_HELPER(x, y) x##y
# define SWIFT_PASTE(x, y) SWIFT_PASTE_HELPER(x, y)
#endif
#if !defined(SWIFT_METATYPE)
# define SWIFT_METATYPE(X) Class
#endif
#if !defined(SWIFT_CLASS_PROPERTY)
# if __has_feature(objc_class_property)
#  define SWIFT_CLASS_PROPERTY(...) __VA_ARGS__
# else
#  define SWIFT_CLASS_PROPERTY(...) 
# endif
#endif
#if !defined(SWIFT_RUNTIME_NAME)
# if __has_attribute(objc_runtime_name)
#  define SWIFT_RUNTIME_NAME(X) __attribute__((objc_runtime_name(X)))
# else
#  define SWIFT_RUNTIME_NAME(X) 
# endif
#endif
#if !defined(SWIFT_COMPILE_NAME)
# if __has_attribute(swift_name)
#  define SWIFT_COMPILE_NAME(X) __attribute__((swift_name(X)))
# else
#  define SWIFT_COMPILE_NAME(X) 
# endif
#endif
#if !defined(SWIFT_METHOD_FAMILY)
# if __has_attribute(objc_method_family)
#  define SWIFT_METHOD_FAMILY(X) __attribute__((objc_method_family(X)))
# else
#  define SWIFT_METHOD_FAMILY(X) 
# endif
#endif
#if !defined(SWIFT_NOESCAPE)
# if __has_attribute(noescape)
#  define SWIFT_NOESCAPE __attribute__((noescape))
# else
#  define SWIFT_NOESCAPE 
# endif
#endif
#if !defined(SWIFT_RELEASES_ARGUMENT)
# if __has_attribute(ns_consumed)
#  define SWIFT_RELEASES_ARGUMENT __attribute__((ns_consumed))
# else
#  define SWIFT_RELEASES_ARGUMENT 
# endif
#endif
#if !defined(SWIFT_WARN_UNUSED_RESULT)
# if __has_attribute(warn_unused_result)
#  define SWIFT_WARN_UNUSED_RESULT __attribute__((warn_unused_result))
# else
#  define SWIFT_WARN_UNUSED_RESULT 
# endif
#endif
#if !defined(SWIFT_NORETURN)
# if __has_attribute(noreturn)
#  define SWIFT_NORETURN __attribute__((noreturn))
# else
#  define SWIFT_NORETURN 
# endif
#endif
#if !defined(SWIFT_CLASS_EXTRA)
# define SWIFT_CLASS_EXTRA 
#endif
#if !defined(SWIFT_PROTOCOL_EXTRA)
# define SWIFT_PROTOCOL_EXTRA 
#endif
#if !defined(SWIFT_ENUM_EXTRA)
# define SWIFT_ENUM_EXTRA 
#endif
#if !defined(SWIFT_CLASS)
# if __has_attribute(objc_subclassing_restricted)
#  define SWIFT_CLASS(SWIFT_NAME) SWIFT_RUNTIME_NAME(SWIFT_NAME) __attribute__((objc_subclassing_restricted)) SWIFT_CLASS_EXTRA
#  define SWIFT_CLASS_NAMED(SWIFT_NAME) __attribute__((objc_subclassing_restricted)) SWIFT_COMPILE_NAME(SWIFT_NAME) SWIFT_CLASS_EXTRA
# else
#  define SWIFT_CLASS(SWIFT_NAME) SWIFT_RUNTIME_NAME(SWIFT_NAME) SWIFT_CLASS_EXTRA
#  define SWIFT_CLASS_NAMED(SWIFT_NAME) SWIFT_COMPILE_NAME(SWIFT_NAME) SWIFT_CLASS_EXTRA
# endif
#endif
#if !defined(SWIFT_RESILIENT_CLASS)
# if __has_attribute(objc_class_stub)
#  define SWIFT_RESILIENT_CLASS(SWIFT_NAME) SWIFT_CLASS(SWIFT_NAME) __attribute__((objc_class_stub))
#  define SWIFT_RESILIENT_CLASS_NAMED(SWIFT_NAME) __attribute__((objc_class_stub)) SWIFT_CLASS_NAMED(SWIFT_NAME)
# else
#  define SWIFT_RESILIENT_CLASS(SWIFT_NAME) SWIFT_CLASS(SWIFT_NAME)
#  define SWIFT_RESILIENT_CLASS_NAMED(SWIFT_NAME) SWIFT_CLASS_NAMED(SWIFT_NAME)
# endif
#endif
#if !defined(SWIFT_PROTOCOL)
# define SWIFT_PROTOCOL(SWIFT_NAME) SWIFT_RUNTIME_NAME(SWIFT_NAME) SWIFT_PROTOCOL_EXTRA
# define SWIFT_PROTOCOL_NAMED(SWIFT_NAME) SWIFT_COMPILE_NAME(SWIFT_NAME) SWIFT_PROTOCOL_EXTRA
#endif
#if !defined(SWIFT_EXTENSION)
# define SWIFT_EXTENSION(M) SWIFT_PASTE(M##_Swift_, __LINE__)
#endif
#if !defined(OBJC_DESIGNATED_INITIALIZER)
# if __has_attribute(objc_designated_initializer)
#  define OBJC_DESIGNATED_INITIALIZER __attribute__((objc_designated_initializer))
# else
#  define OBJC_DESIGNATED_INITIALIZER 
# endif
#endif
#if !defined(SWIFT_ENUM_ATTR)
# if __has_attribute(enum_extensibility)
#  define SWIFT_ENUM_ATTR(_extensibility) __attribute__((enum_extensibility(_extensibility)))
# else
#  define SWIFT_ENUM_ATTR(_extensibility) 
# endif
#endif
#if !defined(SWIFT_ENUM)
# define SWIFT_ENUM(_type, _name, _extensibility) enum _name : _type _name; enum SWIFT_ENUM_ATTR(_extensibility) SWIFT_ENUM_EXTRA _name : _type
# if __has_feature(generalized_swift_name)
#  define SWIFT_ENUM_NAMED(_type, _name, SWIFT_NAME, _extensibility) enum _name : _type _name SWIFT_COMPILE_NAME(SWIFT_NAME); enum SWIFT_COMPILE_NAME(SWIFT_NAME) SWIFT_ENUM_ATTR(_extensibility) SWIFT_ENUM_EXTRA _name : _type
# else
#  define SWIFT_ENUM_NAMED(_type, _name, SWIFT_NAME, _extensibility) SWIFT_ENUM(_type, _name, _extensibility)
# endif
#endif
#if !defined(SWIFT_UNAVAILABLE)
# define SWIFT_UNAVAILABLE __attribute__((unavailable))
#endif
#if !defined(SWIFT_UNAVAILABLE_MSG)
# define SWIFT_UNAVAILABLE_MSG(msg) __attribute__((unavailable(msg)))
#endif
#if !defined(SWIFT_AVAILABILITY)
# define SWIFT_AVAILABILITY(plat, ...) __attribute__((availability(plat, __VA_ARGS__)))
#endif
#if !defined(SWIFT_WEAK_IMPORT)
# define SWIFT_WEAK_IMPORT __attribute__((weak_import))
#endif
#if !defined(SWIFT_DEPRECATED)
# define SWIFT_DEPRECATED __attribute__((deprecated))
#endif
#if !defined(SWIFT_DEPRECATED_MSG)
# define SWIFT_DEPRECATED_MSG(...) __attribute__((deprecated(__VA_ARGS__)))
#endif
#if !defined(SWIFT_DEPRECATED_OBJC)
# if __has_feature(attribute_diagnose_if_objc)
#  define SWIFT_DEPRECATED_OBJC(Msg) __attribute__((diagnose_if(1, Msg, "warning")))
# else
#  define SWIFT_DEPRECATED_OBJC(Msg) SWIFT_DEPRECATED_MSG(Msg)
# endif
#endif
#if defined(__OBJC__)
#if !defined(IBSegueAction)
# define IBSegueAction 
#endif
#endif
#if !defined(SWIFT_EXTERN)
# if defined(__cplusplus)
#  define SWIFT_EXTERN extern "C"
# else
#  define SWIFT_EXTERN extern
# endif
#endif
#if !defined(SWIFT_CALL)
# define SWIFT_CALL __attribute__((swiftcall))
#endif
#if !defined(SWIFT_INDIRECT_RESULT)
# define SWIFT_INDIRECT_RESULT __attribute__((swift_indirect_result))
#endif
#if !defined(SWIFT_CONTEXT)
# define SWIFT_CONTEXT __attribute__((swift_context))
#endif
#if !defined(SWIFT_ERROR_RESULT)
# define SWIFT_ERROR_RESULT __attribute__((swift_error_result))
#endif
#if defined(__cplusplus)
# define SWIFT_NOEXCEPT noexcept
#else
# define SWIFT_NOEXCEPT 
#endif
#if !defined(SWIFT_C_INLINE_THUNK)
# if __has_attribute(always_inline)
# if __has_attribute(nodebug)
#  define SWIFT_C_INLINE_THUNK inline __attribute__((always_inline)) __attribute__((nodebug))
# else
#  define SWIFT_C_INLINE_THUNK inline __attribute__((always_inline))
# endif
# else
#  define SWIFT_C_INLINE_THUNK inline
# endif
#endif
#if defined(_WIN32)
#if !defined(SWIFT_IMPORT_STDLIB_SYMBOL)
# define SWIFT_IMPORT_STDLIB_SYMBOL __declspec(dllimport)
#endif
#else
#if !defined(SWIFT_IMPORT_STDLIB_SYMBOL)
# define SWIFT_IMPORT_STDLIB_SYMBOL 
#endif
#endif
#if defined(__OBJC__)
#if __has_feature(objc_modules)
#if __has_warning("-Watimport-in-framework-header")
#pragma clang diagnostic ignored "-Watimport-in-framework-header"
#endif
@import CoreFoundation;
@import FBSDKCoreKit;
@import Foundation;
@import ObjectiveC;
@import UIKit;
#endif

#import <FBSDKLoginKit/FBSDKLoginKit.h>

#endif
#pragma clang diagnostic ignored "-Wproperty-attribute-mismatch"
#pragma clang diagnostic ignored "-Wduplicate-method-arg"
#if __has_warning("-Wpragma-clang-attribute")
# pragma clang diagnostic ignored "-Wpragma-clang-attribute"
#endif
#pragma clang diagnostic ignored "-Wunknown-pragmas"
#pragma clang diagnostic ignored "-Wnullability"
#pragma clang diagnostic ignored "-Wdollar-in-identifier-extension"

#if __has_attribute(external_source_symbol)
# pragma push_macro("any")
# undef any
# pragma clang attribute push(__attribute__((external_source_symbol(language="Swift", defined_in="FBSDKLoginKit",generated_declaration))), apply_to=any(function,enum,objc_interface,objc_category,objc_protocol))
# pragma pop_macro("any")
#endif

#if defined(__OBJC__)

@class NSString;

/// Represents a code verifier used in the PKCE (Proof Key for Code Exchange)
/// process. This is a cryptographically random string using the characters
/// A-Z, a-z, 0-9, and the punctuation characters -._~ (hyphen, period,
/// underscore, and tilde), between 43 and 128 characters long.
SWIFT_CLASS_NAMED("CodeVerifier")
@interface FBSDKCodeVerifier : NSObject
/// The string value of the code verifier
@property (nonatomic, readonly, copy) NSString * _Nonnull value;
/// The SHA256 hashed challenge of the code verifier
@property (nonatomic, readonly, copy) NSString * _Nonnull challenge;
/// Attempts to initialize a new code verifier instance with the given string.
/// Creation will fail and return nil if the string is invalid.
/// @param string the code verifier string
- (nullable instancetype)initWithString:(NSString * _Nonnull)string;
/// Initializes a new code verifier instance with a random string value
- (nonnull instancetype)init;
@end

/// Passed to openURL to indicate which default audience to use for sessions that post data to Facebook.
/// Certain operations such as publishing a status or publishing a photo require an audience. When the user
/// grants an application permission to perform a publish operation, a default audience is selected as the
/// publication ceiling for the application. This enumerated value allows the application to select which
/// audience to ask the user to grant publish permission for.
typedef SWIFT_ENUM_NAMED(NSUInteger, FBSDKDefaultAudience, "DefaultAudience", open) {
/// Indicates that the user’s friends are able to see posts made by the application
  FBSDKDefaultAudienceFriends = 0,
/// Indicates that only the user is able to see posts made by the application
  FBSDKDefaultAudienceOnlyMe = 1,
/// Indicates that all Facebook users are able to see posts made by the application
  FBSDKDefaultAudienceEveryone = 2,
};

@class NSURL;
@class NSDate;

/// Describes the initial response when starting the device login flow.
/// This is used by <code>DeviceLoginManager</code>.
SWIFT_CLASS_NAMED("DeviceLoginCodeInfo")
@interface FBSDKDeviceLoginCodeInfo : NSObject
/// The unique id for this login flow.
@property (nonatomic, readonly, copy) NSString * _Nonnull identifier;
/// The short “user_code” that should be presented to the user.
@property (nonatomic, readonly, copy) NSString * _Nonnull loginCode;
/// The verification URL.
@property (nonatomic, readonly, copy) NSURL * _Nonnull verificationURL;
/// The expiration date.
@property (nonatomic, readonly, copy) NSDate * _Nonnull expirationDate;
/// The polling interval
@property (nonatomic, readonly) NSUInteger pollingInterval;
- (nonnull instancetype)initWithIdentifier:(NSString * _Nonnull)identifier loginCode:(NSString * _Nonnull)loginCode verificationURL:(NSURL * _Nonnull)verificationURL expirationDate:(NSDate * _Nonnull)expirationDate pollingInterval:(NSUInteger)pollingInterval OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

/// Custom error codes for device login errors in the login error domain
typedef SWIFT_ENUM_NAMED(NSInteger, FBSDKDeviceLoginError, "DeviceLoginErrorCode", open) {
/// Your device is polling too frequently.
  FBSDKDeviceLoginErrorExcessivePolling = 1349172,
/// User has declined to authorize your application.
  FBSDKDeviceLoginErrorAuthorizationDeclined = 1349173,
/// User has not yet authorized your application. Continue polling.
  FBSDKDeviceLoginErrorAuthorizationPending = 1349174,
/// The code you entered has expired.
  FBSDKDeviceLoginErrorCodeExpired = 1349152,
};

@protocol FBSDKDeviceLoginManagerDelegate;

/// Use this class to perform a device login flow.
/// The device login flow starts by requesting a code from the device login API.
/// This class informs the delegate when this code is received. You should then present the
/// code to the user to enter. In the meantime, this class polls the device login API
/// periodically and informs the delegate of the results.
/// See <a href="https://developers.facebook.com/docs/facebook-login/for-devices">Facebook Device Login</a>.
SWIFT_CLASS_NAMED("DeviceLoginManager")
@interface FBSDKDeviceLoginManager : NSObject
/// The device login manager delegate.
@property (nonatomic, weak) id <FBSDKDeviceLoginManagerDelegate> _Nullable delegate;
/// The requested permissions.
@property (nonatomic, readonly, copy) NSArray<NSString *> * _Nonnull permissions;
/// The optional URL to redirect the user to after they complete the login.
/// The URL must be configured in your App Settings -> Advanced -> OAuth Redirect URIs
@property (nonatomic, copy) NSURL * _Nullable redirectURL;
/// Initializes a new instance.
/// @param permissions The permissions to request.
/// @param enableSmartLogin Whether to enable smart login.
- (nonnull instancetype)initWithPermissions:(NSArray<NSString *> * _Nonnull)permissions enableSmartLogin:(BOOL)enableSmartLogin OBJC_DESIGNATED_INITIALIZER;
/// Starts the device login flow
/// This instance will retain self until the flow is finished or cancelled.
- (void)start;
/// Attempts to cancel the device login flow.
- (void)cancel;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

@class NSNetService;
@class NSNumber;

@interface FBSDKDeviceLoginManager (SWIFT_EXTENSION(FBSDKLoginKit)) <NSNetServiceDelegate>
- (void)netService:(NSNetService * _Nonnull)service didNotPublish:(NSDictionary<NSString *, NSNumber *> * _Nonnull)errorValues;
@end


@class FBSDKDeviceLoginManagerResult;

/// A delegate for <code>DeviceLoginManager</code>.
SWIFT_PROTOCOL_NAMED("DeviceLoginManagerDelegate")
@protocol FBSDKDeviceLoginManagerDelegate
/// Indicates the device login flow has started. You should parse <code>codeInfo</code> to present the code to the user to enter.
/// @param loginManager the login manager instance.
/// @param codeInfo the code info data.
- (void)deviceLoginManager:(FBSDKDeviceLoginManager * _Nonnull)loginManager startedWithCodeInfo:(FBSDKDeviceLoginCodeInfo * _Nonnull)codeInfo;
/// Indicates the device login flow has finished.
/// @param loginManager the login manager instance.
/// @param result the results of the login flow.
/// @param error the error, if available.
/// The flow can be finished if the user completed the flow, cancelled, or if the code has expired.
- (void)deviceLoginManager:(FBSDKDeviceLoginManager * _Nonnull)loginManager completedWithResult:(FBSDKDeviceLoginManagerResult * _Nullable)result error:(NSError * _Nullable)error;
@end

@class FBSDKAccessToken;

/// Represents the results of the a device login flow. This is used by <code>DeviceLoginManager</code>
SWIFT_CLASS_NAMED("DeviceLoginManagerResult")
@interface FBSDKDeviceLoginManagerResult : NSObject
/// The token
@property (nonatomic, readonly, strong) FBSDKAccessToken * _Nullable accessToken;
/// Indicates if the login was cancelled by the user, or if the device login code has expired.
@property (nonatomic, readonly) BOOL isCancelled;
/// Internal method exposed to facilitate transition to Swift.
/// API Subject to change or removal without warning. Do not use.
/// @warning INTERNAL - DO NOT USE
- (nonnull instancetype)initWithToken:(FBSDKAccessToken * _Nullable)token isCancelled:(BOOL)cancelled OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

@protocol FBSDKLoginButtonDelegate;
enum FBSDKLoginButtonTooltipBehavior : NSUInteger;
enum FBSDKTooltipColorStyle : NSUInteger;
enum FBSDKLoginTracking : NSUInteger;
@class NSCoder;

/// A button that initiates a log in or log out flow upon tapping.
/// <code>LoginButton</code> works with <code>AccessToken.current</code> to determine what to display,
/// and automatically starts authentication when tapped (i.e., you do not need to manually subscribe action targets).
/// Like <code>LoginManager</code>, you should make sure your app delegate is connected to <code>ApplicationDelegate</code>
/// in order for the button’s delegate to receive messages.
/// <code>LoginButton</code> has a fixed height of 30 pixels, but you may change the width.
/// Initializing the button with <code>nil</code> frame will size the button to its minimum frame.
SWIFT_CLASS_NAMED("FBLoginButton")
@interface FBSDKLoginButton : FBSDKButton
/// The default audience to use, if publish permissions are requested at login time.
@property (nonatomic) enum FBSDKDefaultAudience defaultAudience;
/// Gets or sets the delegate.
@property (nonatomic, weak) IBOutlet id <FBSDKLoginButtonDelegate> _Nullable delegate;
/// The permissions to request.
/// To provide the best experience, you should minimize the number of permissions you request, and only ask for them when needed.
/// For example, do not ask for “user_location” until you the information is actually used by the app.
/// Note this is converted to NSSet and is only
/// an NSArray for the convenience of literal syntax.
/// See <a href="https://developers.facebook.com/docs/facebook-login/permissions/">the permissions guide</a> for more details.
@property (nonatomic, copy) NSArray<NSString *> * _Nonnull permissions;
/// Gets or sets the desired tooltip behavior.
@property (nonatomic) enum FBSDKLoginButtonTooltipBehavior tooltipBehavior;
/// Gets or sets the desired tooltip color style.
@property (nonatomic) enum FBSDKTooltipColorStyle tooltipColorStyle;
/// Gets or sets the desired tracking preference to use for login attempts. Defaults to <code>.enabled</code>
@property (nonatomic) enum FBSDKLoginTracking loginTracking;
/// Gets or sets an optional nonce to use for login attempts. A valid nonce must be a non-empty string without whitespace.
/// An invalid nonce will not be set. Instead, default unique nonces will be used for login attempts.
@property (nonatomic, copy) NSString * _Nullable nonce;
/// Gets or sets an optional page id to use for login attempts.
@property (nonatomic, copy) NSString * _Nullable messengerPageId;
/// Gets or sets the login authorization type to use in the login request. Defaults to <code>rerequest</code>. Use <code>nil</code> to avoid
/// requesting permissions that were previously denied.
@property (nonatomic) FBSDKLoginAuthType _Nullable authType;
/// The code verifier used in the PKCE process.
/// If not provided, a code verifier will be randomly generated.
@property (nonatomic, strong) FBSDKCodeVerifier * _Nonnull codeVerifier;
- (nonnull instancetype)initWithFrame:(CGRect)frame OBJC_DESIGNATED_INITIALIZER;
- (nullable instancetype)initWithCoder:(NSCoder * _Nonnull)coder SWIFT_UNAVAILABLE;
- (void)didMoveToWindow;
- (CGRect)imageRectForContentRect:(CGRect)contentRect SWIFT_WARN_UNUSED_RESULT;
- (CGRect)titleRectForContentRect:(CGRect)contentRect SWIFT_WARN_UNUSED_RESULT;
- (void)layoutSubviews;
- (CGSize)sizeThatFits:(CGSize)size SWIFT_WARN_UNUSED_RESULT;
@end

/// Indicates the desired login tooltip behavior.
typedef SWIFT_ENUM_NAMED(NSUInteger, FBSDKLoginButtonTooltipBehavior, "TooltipBehavior", open) {
/// The default behavior. The tooltip will only be displayed if
/// the app is eligible (determined by possible server round trip)
  FBSDKLoginButtonTooltipBehaviorAutomatic = 0,
/// Force display of the tooltip (typically for UI testing)
  FBSDKLoginButtonTooltipBehaviorForceDisplay = 1,
/// Force disable. In this case you can still exert more refined
/// control by manually constructing a <code>FBSDKLoginTooltipView</code> instance.
  FBSDKLoginButtonTooltipBehaviorDisable = 2,
};

enum FBSDKTooltipViewArrowDirection : NSUInteger;

/// Tooltip bubble with text in it used to display tips for UI elements,
/// with a pointed arrow (to refer to the UI element).
/// The tooltip fades in and will automatically fade out. See <code>displayDuration</code>.
SWIFT_CLASS_NAMED("FBTooltipView")
@interface FBSDKTooltipView : UIView
/// Gets or sets the amount of time in seconds the tooltip should be displayed.
/// Set this to zero to make the display permanent until explicitly dismissed.
/// Defaults to six seconds.
@property (nonatomic) NSTimeInterval displayDuration;
/// Gets or sets the color style after initialization.
/// Defaults to value passed to -initWithTagline:message:colorStyle:.
@property (nonatomic) enum FBSDKTooltipColorStyle colorStyle;
/// Gets or sets the message.
@property (nonatomic, copy) NSString * _Nullable message;
/// Gets or sets the optional phrase that comprises the first part of the label (and is highlighted differently).
@property (nonatomic, copy) NSString * _Nullable tagline;
/// Convenience constructor
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
/// Designated initializer.
/// If you need to show a tooltip for login, consider using the <code>FBSDKLoginTooltipView</code> view.
/// See FBSDKLoginTooltipView
/// \param tagline First part of the label, that will be highlighted with different color. Can be nil.
///
/// \param message Main message to display.
///
/// \param colorStyle Color style to use for tooltip.
///
- (nonnull instancetype)initWithTagline:(NSString * _Nullable)tagline message:(NSString * _Nullable)message colorStyle:(enum FBSDKTooltipColorStyle)colorStyle OBJC_DESIGNATED_INITIALIZER;
- (nullable instancetype)initWithCoder:(NSCoder * _Nonnull)coder OBJC_DESIGNATED_INITIALIZER SWIFT_UNAVAILABLE;
/// Show tooltip at the top or at the bottom of given view.
/// Tooltip will be added to anchorView.window.rootViewController.view
/// Use this method to present the tooltip with automatic positioning or
/// use -presentInView:withArrowPosition:direction: for manual positioning
/// If anchorView is nil or has no window - this method does nothing.
/// \param anchorView view to show at, must be already added to window view hierarchy, in order to decide
/// where tooltip will be shown. (If there’s not enough space at the top of the anchorView in window bounds -
/// tooltip will be shown at the bottom of it)
///
- (void)presentFromView:(UIView * _Nonnull)anchorView;
/// Adds tooltip to given view, with given position and arrow direction.
/// \param view View to be used as superview.
///
/// \param arrowPosition Point in view’s cordinates, where arrow will be pointing
///
/// \param direction whenever arrow should be pointing up (message bubble is below the arrow) or down (message bubble is above the arrow).
///
- (void)presentInView:(UIView * _Nonnull)view withArrowPosition:(CGPoint)arrowPosition direction:(enum FBSDKTooltipViewArrowDirection)direction;
/// Remove tooltip manually.
/// Calling this method isn’t necessary - tooltip will dismiss itself automatically after the <code>displayDuration</code>.
- (void)dismiss;
- (void)drawRect:(CGRect)rect;
- (void)layoutSubviews;
- (nonnull instancetype)initWithFrame:(CGRect)frame SWIFT_UNAVAILABLE;
@end

/// FBSDKTooltipViewArrowDirection enum
/// Passed on construction to determine arrow orientation.
typedef SWIFT_ENUM_NAMED(NSUInteger, FBSDKTooltipViewArrowDirection, "ArrowDirection", closed) {
  FBSDKTooltipViewArrowDirectionDown = 0,
  FBSDKTooltipViewArrowDirectionUp = 1,
};

/// FBSDKTooltipColorStyle enum
/// Passed on construction to determine color styling.
typedef SWIFT_ENUM_NAMED(NSUInteger, FBSDKTooltipColorStyle, "ColorStyle", closed) {
  FBSDKTooltipColorStyleFriendlyBlue = 0,
  FBSDKTooltipColorStyleNeutralGray = 1,
};

@protocol FBSDKLoginTooltipViewDelegate;

/// Represents a tooltip to be displayed next to a Facebook login button
/// to highlight features for new users.
/// The <code>FBSDKLoginButton</code> may display this view automatically. If you do
/// not use the <code>FBSDKLoginButton</code>, you can manually call one of the <code>present*</code> methods
/// as appropriate and customize behavior via <code>FBSDKLoginTooltipViewDelegate</code> delegate.
/// By default, the <code>FBSDKLoginTooltipView</code> is not added to the superview until it is
/// determined the app has migrated to the new login experience. You can override this
/// (e.g., to test the UI layout) by implementing the delegate or setting <code>forceDisplay</code> to YES.
SWIFT_CLASS_NAMED("FBLoginTooltipView")
@interface FBSDKLoginTooltipView : FBSDKTooltipView
/// the delegate
@property (nonatomic, weak) id <FBSDKLoginTooltipViewDelegate> _Nullable delegate;
/// if set to YES, the view will always be displayed and the delegate’s
/// <code>loginTooltipView:shouldAppear:</code> will NOT be called.
@property (nonatomic) BOOL forceDisplay;
/// if set to YES, the view will always be displayed and the delegate’s
/// <code>loginTooltipView:shouldAppear:</code> will NOT be called.
@property (nonatomic) BOOL shouldForceDisplay;
/// Create tooltip
- (nonnull instancetype)init;
- (nonnull instancetype)initWithTagline:(NSString * _Nullable)tagline message:(NSString * _Nullable)message colorStyle:(enum FBSDKTooltipColorStyle)colorStyle OBJC_DESIGNATED_INITIALIZER;
- (void)presentInView:(UIView * _Nonnull)view withArrowPosition:(CGPoint)arrowPosition direction:(enum FBSDKTooltipViewArrowDirection)direction;
@end


/// Internal Type exposed to facilitate transition to Swift.
/// API Subject to change or removal without warning. Do not use.
/// <ul>
///   <li>
///     Warning INTERNAL:  DO NOT USE
///   </li>
/// </ul>
SWIFT_CLASS_NAMED("FBPermission")
@interface FBSDKPermission : NSObject
@property (nonatomic, readonly, copy) NSString * _Nonnull description;
@property (nonatomic, readonly) NSUInteger hash;
/// Attempts to initialize a new permission with the given string.
/// Creation will fail and return nil if the string is invalid.
/// \param string The raw permission string
///
- (nullable instancetype)initWithString:(NSString * _Nonnull)string OBJC_DESIGNATED_INITIALIZER;
/// Returns a set of <code>FBPermission</code> from a set of raw permissions strings.
/// Will return nil if any of the input permissions is invalid.
+ (NSSet<FBSDKPermission *> * _Nullable)permissionsFromRawPermissions:(NSSet<NSString *> * _Nonnull)rawPermissions SWIFT_WARN_UNUSED_RESULT;
/// Returns a set of string permissions from a set of <code>FBPermission</code> by
/// extracting the “value” property for each element.
+ (NSSet<NSString *> * _Nonnull)rawPermissionsFromPermissions:(NSSet<FBSDKPermission *> * _Nonnull)permissions SWIFT_WARN_UNUSED_RESULT;
- (BOOL)isEqual:(id _Nullable)object SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end




@class FBSDKLoginManagerLoginResult;

/// A delegate for <code>FBSDKLoginButton</code>
SWIFT_PROTOCOL_NAMED("LoginButtonDelegate")
@protocol FBSDKLoginButtonDelegate <NSObject>
/// Sent to the delegate when the button was used to login.
/// @param loginButton The button being used to log in
/// @param result The results of the login
/// @param error The error (if any) from the login
- (void)loginButton:(FBSDKLoginButton * _Nonnull)loginButton didCompleteWithResult:(FBSDKLoginManagerLoginResult * _Nullable)result error:(NSError * _Nullable)error;
/// Sent to the delegate when the button was used to logout.
/// @param loginButton The button being used to log out.
- (void)loginButtonDidLogOut:(FBSDKLoginButton * _Nonnull)loginButton;
@optional
/// Sent to the delegate when the button is about to login.
/// @param loginButton The button being used to log in
/// @return <code>true</code> if the login should be allowed to proceed, <code>false</code> otherwise
- (BOOL)loginButtonWillLogin:(FBSDKLoginButton * _Nonnull)loginButton SWIFT_WARN_UNUSED_RESULT;
@end


/// A configuration to use for modifying the behavior of a login attempt.
SWIFT_CLASS_NAMED("LoginConfiguration")
@interface FBSDKLoginConfiguration : NSObject
/// The nonce that the configuration was created with.
/// A unique nonce will be used if none is provided to the initializer.
@property (nonatomic, readonly, copy) NSString * _Nonnull nonce;
/// The tracking  preference. Defaults to <code>.enabled</code>.
@property (nonatomic, readonly) enum FBSDKLoginTracking tracking;
/// The requested permissions for the login attempt. Defaults to an empty set.
@property (nonatomic, readonly, copy) NSSet<FBSDKPermission *> * _Nonnull requestedPermissions;
/// The Messenger Page Id associated with this login request.
@property (nonatomic, readonly, copy) NSString * _Nullable messengerPageId;
/// The auth type associated with this login request.
@property (nonatomic, readonly) FBSDKLoginAuthType _Nullable authType;
/// The code verifier used in the PKCE process.
/// If not provided, a code verifier will be randomly generated.
@property (nonatomic, readonly, strong) FBSDKCodeVerifier * _Nonnull codeVerifier;
/// Attempts to initialize a new configuration with the expected parameters.
/// @param permissions the requested permissions for a login attempt. Permissions must be an array of strings that do not contain whitespace.
/// @param tracking the tracking preference to use for a login attempt.
/// @param nonce an optional nonce to use for the login attempt. A valid nonce must be a non-empty string without whitespace.
/// Creation of the configuration will fail if the nonce is invalid.
/// @param messengerPageId the associated page id  to use for a login attempt.
- (nullable instancetype)initWithPermissions:(NSArray<NSString *> * _Nonnull)permissions tracking:(enum FBSDKLoginTracking)tracking nonce:(NSString * _Nonnull)nonce messengerPageId:(NSString * _Nullable)messengerPageId;
/// Attempts to initialize a new configuration with the expected parameters.
/// @param permissions the requested permissions for a login attempt. Permissions must be an array of strings that do not contain whitespace.
/// @param tracking the tracking preference to use for a login attempt.
/// @param nonce an optional nonce to use for the login attempt. A valid nonce must be a non-empty string without whitespace.
/// Creation of the configuration will fail if the nonce is invalid.
/// @param messengerPageId the associated page id  to use for a login attempt.
/// @param authType auth_type param to use for login.
- (nullable instancetype)initWithPermissions:(NSArray<NSString *> * _Nonnull)permissions tracking:(enum FBSDKLoginTracking)tracking nonce:(NSString * _Nonnull)nonce messengerPageId:(NSString * _Nullable)messengerPageId authType:(FBSDKLoginAuthType _Nullable)authType;
/// Attempts to initialize a new configuration with the expected parameters.
/// @param permissions the requested permissions for a login attempt. Permissions must be an array of strings that do not contain whitespace.
/// @param tracking the tracking preference to use for a login attempt.
/// @param nonce an optional nonce to use for the login attempt. A valid nonce must be a non-empty string without whitespace.
/// Creation of the configuration will fail if the nonce is invalid.
- (nullable instancetype)initWithPermissions:(NSArray<NSString *> * _Nonnull)permissions tracking:(enum FBSDKLoginTracking)tracking nonce:(NSString * _Nonnull)nonce;
/// Attempts to initialize a new configuration with the expected parameters.
/// @param permissions the requested permissions for the login attempt. Permissions must be an array of strings that do not contain whitespace.
/// @param tracking the tracking preference to use for a login attempt.
/// @param messengerPageId the associated page id  to use for a login attempt.
- (nullable instancetype)initWithPermissions:(NSArray<NSString *> * _Nonnull)permissions tracking:(enum FBSDKLoginTracking)tracking messengerPageId:(NSString * _Nullable)messengerPageId;
/// Attempts to initialize a new configuration with the expected parameters.
/// @param permissions the requested permissions for the login attempt. Permissions must be an array of strings that do not contain whitespace.
/// @param tracking the tracking preference to use for a login attempt.
/// @param messengerPageId the associated page id  to use for a login attempt.
/// @param authType auth_type param to use for login.
- (nullable instancetype)initWithPermissions:(NSArray<NSString *> * _Nonnull)permissions tracking:(enum FBSDKLoginTracking)tracking messengerPageId:(NSString * _Nullable)messengerPageId authType:(FBSDKLoginAuthType _Nullable)authType;
/// Attempts to initialize a new configuration with the expected parameters.
/// @param permissions the requested permissions for a login attempt. Permissions must be an array of strings that do not contain whitespace.
/// @param tracking the tracking preference to use for a login attempt.
/// @param nonce an optional nonce to use for the login attempt. A valid nonce must be a non-empty string without whitespace.
/// Creation of the configuration will fail if the nonce is invalid.
/// @param messengerPageId the associated page id  to use for a login attempt.
/// @param authType auth_type param to use for login.
/// @param codeVerifier The code verifier used in the PKCE process.
- (nullable instancetype)initWithPermissions:(NSArray<NSString *> * _Nonnull)permissions tracking:(enum FBSDKLoginTracking)tracking nonce:(NSString * _Nonnull)nonce messengerPageId:(NSString * _Nullable)messengerPageId authType:(FBSDKLoginAuthType _Nullable)authType codeVerifier:(FBSDKCodeVerifier * _Nonnull)codeVerifier OBJC_DESIGNATED_INITIALIZER;
/// Attempts to initialize a new configuration with the expected parameters.
/// @param permissions the requested permissions for the login attempt. Permissions must be an array of strings that do not contain whitespace.
/// @param tracking the tracking preference to use for a login attempt.
- (nullable instancetype)initWithPermissions:(NSArray<NSString *> * _Nonnull)permissions tracking:(enum FBSDKLoginTracking)tracking;
/// Attempts to initialize a new configuration with the expected parameters.
/// @param tracking the login tracking preference to use for a login attempt.
- (nullable instancetype)initWithTracking:(enum FBSDKLoginTracking)tracking;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

/// Custom error codes for login errors in the login error domain
typedef SWIFT_ENUM_NAMED(NSInteger, FBSDKLoginError, "LoginErrorCode", open) {
/// Reserved
  FBSDKLoginErrorReserved = 300,
/// The error code for unknown errors
  FBSDKLoginErrorUnknown = 301,
/// The user’s password has changed and must log in again
  FBSDKLoginErrorPasswordChanged = 302,
/// The user must log in to their account on www.facebook.com to restore access
  FBSDKLoginErrorUserCheckpointed = 303,
/// Indicates a failure to request new permissions because the user has changed
  FBSDKLoginErrorUserMismatch = 304,
/// The user must confirm their account with Facebook before logging in
  FBSDKLoginErrorUnconfirmedUser = 305,
/// The Accounts framework failed without returning an error, indicating the app’s slider in the
/// iOS Facebook Settings (device Settings -> Facebook -> App Name) has been disabled.
  FBSDKLoginErrorSystemAccountAppDisabled = 306,
/// An error occurred related to Facebook system Account store
  FBSDKLoginErrorSystemAccountUnavailable = 307,
/// The login response was missing a valid challenge string
  FBSDKLoginErrorBadChallengeString = 308,
/// The ID token returned in login response was invalid
  FBSDKLoginErrorInvalidIDToken = 309,
/// A current access token was required and not provided
  FBSDKLoginErrorMissingAccessToken = 310,
};

@class UIViewController;

/// Provides methods for logging the user in and out.
/// It works directly with <code>AccessToken</code> (for data access) and <code>AuthenticationToken</code> (for authentication);
/// it sets the “current” tokens upon successful authorizations (or sets to <code>nil</code> in case of <code>logOut</code>).
/// You should check <code>AccessToken.current</code> before calling a login method to see if there is
/// a cached token available (typically in a <code>viewDidLoad</code> implementation).
/// @warning If you are managing your own tokens outside of <code>AccessToken</code>, you will need to set
/// <code>AccessToken.current</code> before calling a login method to authorize further permissions on your tokens.
SWIFT_CLASS_NAMED("LoginManager")
@interface FBSDKLoginManager : NSObject
/// The default audience. You should set this if you intend to ask for publish permissions.
@property (nonatomic) enum FBSDKDefaultAudience defaultAudience;
/// Initialize an instance of <code>LoginManager.</code>
/// \param defaultAudience Optional default audience to use. Default: <code>.friends</code>.
///
- (nonnull instancetype)initWithDefaultAudience:(enum FBSDKDefaultAudience)defaultAudience;
/// Logs the user in or authorizes additional permissions.
/// @param viewController the view controller from which to present the login UI. If nil, the topmost view
/// controller will be automatically determined and used.
/// @param configuration the login configuration to use.
/// @param completion the login completion handler.
/// Use this method when asking for permissions. You should only ask for permissions when they
/// are needed and the value should be explained to the user. You can inspect the
/// <code>FBSDKLoginManagerLoginResultBlock</code>’s <code>result.declinedPermissions</code> to provide more information
/// to the user if they decline permissions.
/// To reduce unnecessary login attempts, you should typically check if <code>AccessToken.current</code>
/// already contains the permissions you need. If it does, you probably do not need to call this method.
/// @warning You can only perform one login call at a time. Calling a login method before the completion handler is
/// called on a previous login attempt will result in an error.
/// @warning This method will present a UI to the user and thus should be called on the main thread.
- (void)logInFromViewController:(UIViewController * _Nullable)viewController configuration:(FBSDKLoginConfiguration * _Nullable)configuration completion:(FBSDKLoginManagerLoginResultBlock _Nonnull)completion;
/// Logs the user in or authorizes additional permissions.
/// @param permissions the optional array of permissions. Note this is converted to NSSet and is only
/// an NSArray for the convenience of literal syntax.
/// @param viewController the view controller to present from. If nil, the topmost view controller will be
/// automatically determined as best as possible.
/// @param handler the callback.
/// Use this method when asking for read permissions. You should only ask for permissions when they
/// are needed and explain the value to the user. You can inspect the <code>FBSDKLoginManagerLoginResultBlock</code>’s
/// <code>result.declinedPermissions</code> to provide more information to the user if they decline permissions.
/// You typically should check if <code>AccessToken.current</code> already contains the permissions you need before
/// asking to reduce unnecessary login attempts. For example, you could perform that check in <code>viewDidLoad</code>.
/// @warning You can only perform one login call at a time. Calling a login method before the completion handler is
/// called on a previous login attempt will result in an error.
/// @warning This method will present a UI to the user and thus should be called on the main thread.
- (void)logInWithPermissions:(NSArray<NSString *> * _Nonnull)permissions fromViewController:(UIViewController * _Nullable)viewController handler:(FBSDKLoginManagerLoginResultBlock _Nullable)handler;
/// Requests user’s permission to reathorize application’s data access, after it has expired due to inactivity.
/// @param viewController the view controller from which to present the login UI. If nil, the topmost view
/// controller will be automatically determined and used.
/// @param handler the callback.
/// Use this method when you need to reathorize your app’s access to user data via the Graph API.
/// You should only call this after access has expired.
/// You should provide as much context to the user as possible as to why you need to reauthorize the access, the
/// scope of access being reathorized, and what added value your app provides when the access is reathorized.
/// You can inspect the <code>result.declinedPermissions</code> to determine if you should provide more information to the
/// user based on any declined permissions.
/// @warning This method will reauthorize using a <code>LoginConfiguration</code> with <code>FBSDKLoginTracking</code> set to <code>.enabled</code>.
/// @warning This method will present UI the user. You typically should call this if <code>AccessToken.isDataAccessExpired</code>
/// is true.
- (void)reauthorizeDataAccess:(UIViewController * _Nonnull)viewController handler:(FBSDKLoginManagerLoginResultBlock _Nonnull)handler;
/// Logs the user out
/// This nils out the singleton instances of <code>AccessToken</code>, <code>AuthenticationToken</code> and <code>Profle</code>.
/// @note This is only a client side logout. It will not log the user out of their Facebook account.
- (void)logOut;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end



@class UIApplication;

@interface FBSDKLoginManager (SWIFT_EXTENSION(FBSDKLoginKit)) <FBSDKURLOpening>
+ (FBSDKLoginManager * _Nonnull)makeOpener SWIFT_WARN_UNUSED_RESULT;
- (BOOL)application:(UIApplication * _Nullable)application openURL:(NSURL * _Nullable)url sourceApplication:(NSString * _Nullable)sourceApplication annotation:(id _Nullable)annotation SWIFT_WARN_UNUSED_RESULT;
- (BOOL)canOpenURL:(NSURL * _Nonnull)url forApplication:(UIApplication * _Nullable)application sourceApplication:(NSString * _Nullable)sourceApplication annotation:(id _Nullable)annotation SWIFT_WARN_UNUSED_RESULT;
- (void)applicationDidBecomeActive:(UIApplication * _Nonnull)application;
- (BOOL)isAuthenticationURL:(NSURL * _Nonnull)url SWIFT_WARN_UNUSED_RESULT;
- (BOOL)shouldStopPropagationOfURL:(NSURL * _Nonnull)url SWIFT_WARN_UNUSED_RESULT;
@end

@class FBSDKAuthenticationToken;

/// Describes the result of a login attempt.
SWIFT_CLASS_NAMED("LoginManagerLoginResult")
@interface FBSDKLoginManagerLoginResult : NSObject
/// The access token
@property (nonatomic, readonly, strong) FBSDKAccessToken * _Nullable token;
/// The authentication token
@property (nonatomic, readonly, strong) FBSDKAuthenticationToken * _Nullable authenticationToken;
/// Whether the login was cancelled by the user
@property (nonatomic, readonly) BOOL isCancelled;
/// The set of permissions granted by the user in the associated request.
/// Inspect the token’s permissions set for a complete list.
@property (nonatomic, readonly, copy) NSSet<NSString *> * _Nonnull grantedPermissions;
/// The set of permissions declined by the user in the associated request.
/// Inspect the token’s permissions set for a complete list.
@property (nonatomic, readonly, copy) NSSet<NSString *> * _Nonnull declinedPermissions;
/// Creates a new result
/// @param token The access token
/// @param authenticationToken The authentication token
/// @param isCancelled whether The login was cancelled by the user
/// @param grantedPermissions The set of granted permissions
/// @param declinedPermissions The set of declined permissions
- (nonnull instancetype)initWithToken:(FBSDKAccessToken * _Nullable)token authenticationToken:(FBSDKAuthenticationToken * _Nullable)authenticationToken isCancelled:(BOOL)isCancelled grantedPermissions:(NSSet<NSString *> * _Nonnull)grantedPermissions declinedPermissions:(NSSet<NSString *> * _Nonnull)declinedPermissions OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end


/// The <code>LoginTooltipViewDelegate</code> protocol defines the methods used to receive event
/// notifications from <code>FBLoginTooltipView</code> objects.
SWIFT_PROTOCOL_NAMED("LoginTooltipViewDelegate")
@protocol FBSDKLoginTooltipViewDelegate
@optional
/// Asks the delegate if the tooltip view should appear
/// @param view The tooltip view.
/// @param appIsEligible The value fetched from the server identifying if the app
/// is eligible for the new login experience.
/// Use this method to customize display behavior.
- (BOOL)loginTooltipView:(FBSDKLoginTooltipView * _Nonnull)view shouldAppear:(BOOL)appIsEligible SWIFT_WARN_UNUSED_RESULT;
/// Tells the delegate the tooltip view will appear, specifically after it’s been
/// added to the super view but before the fade in animation.
/// @param view The tooltip view.
- (void)loginTooltipViewWillAppear:(FBSDKLoginTooltipView * _Nonnull)view;
/// Tells the delegate the tooltip view will not appear (i.e., was not
/// added to the super view).
/// @param view The tooltip view.
- (void)loginTooltipViewWillNotAppear:(FBSDKLoginTooltipView * _Nonnull)view;
@end

/// <code>enabled</code> and <code>limited</code> see: https://developers.facebook.com/docs/facebook-login/ios/limited-login/
typedef SWIFT_ENUM_NAMED(NSUInteger, FBSDKLoginTracking, "LoginTracking", open) {
  FBSDKLoginTrackingEnabled = 0,
  FBSDKLoginTrackingLimited = 1,
};


@class FBSDKProfile;

/// Internal Type exposed to facilitate transition to Swift.
/// API Subject to change or removal without warning. Do not use.
/// <ul>
///   <li>
///     Warning INTERNAL:  DO NOT USE
///   </li>
/// </ul>
/// Structured interface for accessing the parameters used to complete a log in request.
/// If <code>authenticationTokenString</code> is non-<code>nil</code>, the authentication succeeded. If <code>error</code> is
/// non-<code>nil</code> the request failed. If both are <code>nil</code>, the request was cancelled.
SWIFT_CLASS_NAMED("_LoginCompletionParameters")
@interface FBSDKLoginCompletionParameters : NSObject
@property (nonatomic, strong) FBSDKAuthenticationToken * _Nullable authenticationToken;
@property (nonatomic, strong) FBSDKProfile * _Nullable profile;
@property (nonatomic, copy) NSString * _Nullable accessTokenString;
@property (nonatomic, copy) NSString * _Nullable nonceString;
@property (nonatomic, copy) NSString * _Nullable authenticationTokenString;
@property (nonatomic, copy) NSString * _Nullable code;
@property (nonatomic, copy) NSSet<FBSDKPermission *> * _Nullable permissions;
@property (nonatomic, copy) NSSet<FBSDKPermission *> * _Nullable declinedPermissions;
@property (nonatomic, copy) NSSet<FBSDKPermission *> * _Nullable expiredPermissions;
@property (nonatomic, copy) NSString * _Nullable appID;
@property (nonatomic, copy) NSString * _Nullable userID;
@property (nonatomic) NSError * _Nullable error;
@property (nonatomic, copy) NSDate * _Nullable expirationDate;
@property (nonatomic, copy) NSDate * _Nullable dataAccessExpirationDate;
@property (nonatomic, copy) NSString * _Nullable challenge;
@property (nonatomic, copy) NSString * _Nullable graphDomain;
@property (nonatomic, copy) NSString * _Nullable userTokenNonce;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

#endif
#if __has_attribute(external_source_symbol)
# pragma clang attribute pop
#endif
#if defined(__cplusplus)
#endif
#pragma clang diagnostic pop
#endif

#else
#error unsupported Swift architecture
#endif
