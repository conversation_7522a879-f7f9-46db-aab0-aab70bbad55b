<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Resources/Info.plist</key>
		<data>
		UcZqwKk49CHSSU1nSSqGLaywRWs=
		</data>
		<key>Resources/PrivacyInfo.xcprivacy</key>
		<data>
		c/GhFel+DoHL4HradxiX7nIkOl4=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/FBSDKLoginAuthType.h</key>
		<dict>
			<key>hash2</key>
			<data>
			En8JspBXmCZrSWkWaxJV5tKzr8At6tqf53zIGNa2VYY=
			</data>
		</dict>
		<key>Headers/FBSDKLoginCompletionParametersBlock.h</key>
		<dict>
			<key>hash2</key>
			<data>
			7WGMXXull6LrlXHwbqyalo/ZMN0JSBtHbC6cWu8k2eI=
			</data>
		</dict>
		<key>Headers/FBSDKLoginErrorDomain.h</key>
		<dict>
			<key>hash2</key>
			<data>
			zFgW2vVnY7X9MEoilZ5/3iQAYiab+N4zlq9kMvgkl/4=
			</data>
		</dict>
		<key>Headers/FBSDKLoginKit-Swift.h</key>
		<dict>
			<key>hash2</key>
			<data>
			jMaPKo/8VzW30tDEgaa7AFlzku9Zp1zdjoGZik1KZjQ=
			</data>
		</dict>
		<key>Headers/FBSDKLoginKit.h</key>
		<dict>
			<key>hash2</key>
			<data>
			8b6xB2UCDMQ82B5SNTD1CpaH+pyHg+K3Yy0MxzTdgTE=
			</data>
		</dict>
		<key>Headers/FBSDKLoginManagerLoginResultBlock.h</key>
		<dict>
			<key>hash2</key>
			<data>
			Zb9GWGgaSc6xOtE0UsSlBLPOxWMfbTeNdSsr0fvIp74=
			</data>
		</dict>
		<key>Modules/FBSDKLoginKit.swiftmodule/arm64-apple-ios-macabi.abi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			otLvCaFFvvsH2p2rr92zyti2Xrh/LUYkV/dlnLl/VDw=
			</data>
		</dict>
		<key>Modules/FBSDKLoginKit.swiftmodule/arm64-apple-ios-macabi.private.swiftinterface</key>
		<dict>
			<key>hash2</key>
			<data>
			6ZSsdNFZNR+wvFwhvwVT/yXi0TprTCUHixhi8y1fHJ8=
			</data>
		</dict>
		<key>Modules/FBSDKLoginKit.swiftmodule/arm64-apple-ios-macabi.swiftdoc</key>
		<dict>
			<key>hash2</key>
			<data>
			ZK7rrAqObw2CwCzYg0PZKUIzbHijuRC5AvqXkJmqnmA=
			</data>
		</dict>
		<key>Modules/FBSDKLoginKit.swiftmodule/arm64-apple-ios-macabi.swiftinterface</key>
		<dict>
			<key>hash2</key>
			<data>
			6ZSsdNFZNR+wvFwhvwVT/yXi0TprTCUHixhi8y1fHJ8=
			</data>
		</dict>
		<key>Modules/FBSDKLoginKit.swiftmodule/x86_64-apple-ios-macabi.abi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			otLvCaFFvvsH2p2rr92zyti2Xrh/LUYkV/dlnLl/VDw=
			</data>
		</dict>
		<key>Modules/FBSDKLoginKit.swiftmodule/x86_64-apple-ios-macabi.private.swiftinterface</key>
		<dict>
			<key>hash2</key>
			<data>
			WLWBvqlu56aZbs130MmxC00ykDj1gRT3ZjX3RiwW3pI=
			</data>
		</dict>
		<key>Modules/FBSDKLoginKit.swiftmodule/x86_64-apple-ios-macabi.swiftdoc</key>
		<dict>
			<key>hash2</key>
			<data>
			9BPrGiCpu9MooNIIqjYNh7Yb0BS5W6n8+B3lKdPSTEU=
			</data>
		</dict>
		<key>Modules/FBSDKLoginKit.swiftmodule/x86_64-apple-ios-macabi.swiftinterface</key>
		<dict>
			<key>hash2</key>
			<data>
			WLWBvqlu56aZbs130MmxC00ykDj1gRT3ZjX3RiwW3pI=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash2</key>
			<data>
			/LNPo6mK3Ap58ptMqxKbx/hlGBOSkSGDoDN3+CL3VlA=
			</data>
		</dict>
		<key>Resources/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			Ryse+CGzrE6CUzusGTDM67WSTJ8g3e1S0DPxKuyIL4I=
			</data>
		</dict>
		<key>Resources/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			V+yTPiE3CaHxIWdHy5KWEryxwgIcGAfRgIV8XZH0Qpc=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^Resources/</key>
		<true/>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^(Frameworks|SharedFrameworks|PlugIns|Plug-ins|XPCServices|Helpers|MacOS|Library/(Automator|Spotlight|LoginItems))/</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^[^/]+$</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
