<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/FBSDKLoginAuthType.h</key>
		<data>
		dbPjG4QiKnnxeSs+knOQIJW0JVg=
		</data>
		<key>Headers/FBSDKLoginCompletionParametersBlock.h</key>
		<data>
		fPr6aC3BILzRqRBn1j6oocs3jMc=
		</data>
		<key>Headers/FBSDKLoginErrorDomain.h</key>
		<data>
		cneem1mczyhnLYxbDjbbR5mEmzM=
		</data>
		<key>Headers/FBSDKLoginKit-Swift.h</key>
		<data>
		PF51NW/pGV+IWoOrpBJGUhDbN30=
		</data>
		<key>Headers/FBSDKLoginKit.h</key>
		<data>
		OUJgU2YPcKajMNRVhOXvWNOomHs=
		</data>
		<key>Headers/FBSDKLoginManagerLoginResultBlock.h</key>
		<data>
		hdGFhcZwp8w9OhQTkCmMyUDevXU=
		</data>
		<key>Info.plist</key>
		<data>
		KVKW4hH6cqoHXSWusRLq2sWW/Zc=
		</data>
		<key>Modules/FBSDKLoginKit.swiftmodule/arm64-apple-ios.abi.json</key>
		<data>
		54b2zs1bfpk6hiYuhiW6naCu+h0=
		</data>
		<key>Modules/FBSDKLoginKit.swiftmodule/arm64-apple-ios.private.swiftinterface</key>
		<data>
		Qy1jW7TeaoFT1sLZ34yzpG5prLU=
		</data>
		<key>Modules/FBSDKLoginKit.swiftmodule/arm64-apple-ios.swiftdoc</key>
		<data>
		8J2FE7HMOp+RXbPwCDl/OxT8S7c=
		</data>
		<key>Modules/FBSDKLoginKit.swiftmodule/arm64-apple-ios.swiftinterface</key>
		<data>
		Qy1jW7TeaoFT1sLZ34yzpG5prLU=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		KrDKcg24trXz/SqZuZkQjJYkUsc=
		</data>
		<key>PrivacyInfo.xcprivacy</key>
		<data>
		c/GhFel+DoHL4HradxiX7nIkOl4=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/FBSDKLoginAuthType.h</key>
		<dict>
			<key>hash2</key>
			<data>
			En8JspBXmCZrSWkWaxJV5tKzr8At6tqf53zIGNa2VYY=
			</data>
		</dict>
		<key>Headers/FBSDKLoginCompletionParametersBlock.h</key>
		<dict>
			<key>hash2</key>
			<data>
			7WGMXXull6LrlXHwbqyalo/ZMN0JSBtHbC6cWu8k2eI=
			</data>
		</dict>
		<key>Headers/FBSDKLoginErrorDomain.h</key>
		<dict>
			<key>hash2</key>
			<data>
			zFgW2vVnY7X9MEoilZ5/3iQAYiab+N4zlq9kMvgkl/4=
			</data>
		</dict>
		<key>Headers/FBSDKLoginKit-Swift.h</key>
		<dict>
			<key>hash2</key>
			<data>
			D3li8kobOOJ4PbkOf7ON37Mqrx13pBP0uJ6CjdQ/xUQ=
			</data>
		</dict>
		<key>Headers/FBSDKLoginKit.h</key>
		<dict>
			<key>hash2</key>
			<data>
			8b6xB2UCDMQ82B5SNTD1CpaH+pyHg+K3Yy0MxzTdgTE=
			</data>
		</dict>
		<key>Headers/FBSDKLoginManagerLoginResultBlock.h</key>
		<dict>
			<key>hash2</key>
			<data>
			Zb9GWGgaSc6xOtE0UsSlBLPOxWMfbTeNdSsr0fvIp74=
			</data>
		</dict>
		<key>Modules/FBSDKLoginKit.swiftmodule/arm64-apple-ios.abi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			otLvCaFFvvsH2p2rr92zyti2Xrh/LUYkV/dlnLl/VDw=
			</data>
		</dict>
		<key>Modules/FBSDKLoginKit.swiftmodule/arm64-apple-ios.private.swiftinterface</key>
		<dict>
			<key>hash2</key>
			<data>
			W8vLX4P+Hcv5I9wT4f4A5UMDF2p8UzUzmd/c6SKqCcw=
			</data>
		</dict>
		<key>Modules/FBSDKLoginKit.swiftmodule/arm64-apple-ios.swiftdoc</key>
		<dict>
			<key>hash2</key>
			<data>
			2GpveiGwuliFv0HuLNq2LkoqEc04XZEydtpx9i96cQM=
			</data>
		</dict>
		<key>Modules/FBSDKLoginKit.swiftmodule/arm64-apple-ios.swiftinterface</key>
		<dict>
			<key>hash2</key>
			<data>
			W8vLX4P+Hcv5I9wT4f4A5UMDF2p8UzUzmd/c6SKqCcw=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash2</key>
			<data>
			/LNPo6mK3Ap58ptMqxKbx/hlGBOSkSGDoDN3+CL3VlA=
			</data>
		</dict>
		<key>PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			V+yTPiE3CaHxIWdHy5KWEryxwgIcGAfRgIV8XZH0Qpc=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
