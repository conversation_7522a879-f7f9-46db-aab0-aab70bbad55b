---
triple:          'arm64-apple-darwin'
binary-path:     '/data/sandcastle/boxes/trunk-git-facebook-ios-sdk/build/Build/Intermediates.noindex/ArchiveIntermediates/FBSDKLoginKit-Dynamic/InstallationBuildProductsLocation/@rpath/FBSDKLoginKit.framework/FBSDKLoginKit'
relocations:
  - { offsetInCU: 0x33, offset: 0x33, size: 0x8, addend: 0x0, symName: _FBSDKLoginKitVersionString, symObjAddr: 0x0, symBinAddr: 0x4A0F0, symSize: 0x0 }
  - { offsetInCU: 0x67, offset: 0x67, size: 0x8, addend: 0x0, symName: _FBSDKLoginKitVersionNumber, symObjAddr: 0x40, symBinAddr: 0x4A130, symSize: 0x0 }
  - { offsetInCU: 0x34, offset: 0xB0, size: 0x8, addend: 0x0, symName: _FBSDKLoginAuthTypeRerequest, symObjAddr: 0x58, symBinAddr: 0x58ED0, symSize: 0x0 }
  - { offsetInCU: 0xB7, offset: 0x133, size: 0x8, addend: 0x0, symName: _FBSDKLoginAuthTypeReauthorize, symObjAddr: 0x60, symBinAddr: 0x58ED8, symSize: 0x0 }
  - { offsetInCU: 0x34, offset: 0x184, size: 0x8, addend: 0x0, symName: _FBSDKLoginErrorDomain, symObjAddr: 0x38, symBinAddr: 0x58EE0, symSize: 0x0 }
  - { offsetInCU: 0x3D, offset: 0x23F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5frame15elementProvider06stringG005loginG019graphRequestFactoryACSo6CGRectV_AA29UserInterfaceElementProviding_pAA0no6StringQ0_pAA05LoginQ0_pSo010FBSDKGraphkL0_ptcfcfA_', symObjAddr: 0xC4, symBinAddr: 0x80C4, symSize: 0x14 }
  - { offsetInCU: 0x56, offset: 0x258, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5frame11permissionsACSo6CGRectV_Say09FBSDKCoreB010PermissionOGtcfcfA_', symObjAddr: 0xD8, symBinAddr: 0x80D8, symSize: 0x14 }
  - { offsetInCU: 0x6F, offset: 0x271, size: 0x8, addend: 0x0, symName: ___swift_instantiateConcreteTypeFromMangledName, symObjAddr: 0xF0, symBinAddr: 0x80F0, symSize: 0x40 }
  - { offsetInCU: 0x82, offset: 0x284, size: 0x8, addend: 0x0, symName: ___swift_memcpy16_8, symObjAddr: 0x1EC, symBinAddr: 0x81EC, symSize: 0xC }
  - { offsetInCU: 0x95, offset: 0x297, size: 0x8, addend: 0x0, symName: ___swift_noop_void_return, symObjAddr: 0x1F8, symBinAddr: 0x81F8, symSize: 0x4 }
  - { offsetInCU: 0xA8, offset: 0x2AA, size: 0x8, addend: 0x0, symName: '_$sSo7CGPointVwet', symObjAddr: 0x1FC, symBinAddr: 0x81FC, symSize: 0x20 }
  - { offsetInCU: 0xBB, offset: 0x2BD, size: 0x8, addend: 0x0, symName: '_$sSo7CGPointVwst', symObjAddr: 0x21C, symBinAddr: 0x821C, symSize: 0x28 }
  - { offsetInCU: 0xCE, offset: 0x2D0, size: 0x8, addend: 0x0, symName: '_$sSo18FBSDKLoginAuthTypeas20_SwiftNewtypeWrapperSCSYWb', symObjAddr: 0x5D4, symBinAddr: 0x854C, symSize: 0x2C }
  - { offsetInCU: 0xE1, offset: 0x2E3, size: 0x8, addend: 0x0, symName: '_$sSo18FBSDKLoginAuthTypeas20_SwiftNewtypeWrapperSCs35_HasCustomAnyHashableRepresentationPWb', symObjAddr: 0x600, symBinAddr: 0x8578, symSize: 0x2C }
  - { offsetInCU: 0xF4, offset: 0x2F6, size: 0x8, addend: 0x0, symName: '_$sSo18FBSDKLoginAuthTypeaSHSCSQWb', symObjAddr: 0x66C, symBinAddr: 0x85E4, symSize: 0x2C }
  - { offsetInCU: 0x107, offset: 0x309, size: 0x8, addend: 0x0, symName: '_$sSo26FBSDKAppEventParameterNameas20_SwiftNewtypeWrapperSCSYWb', symObjAddr: 0x804, symBinAddr: 0x877C, symSize: 0x2C }
  - { offsetInCU: 0x11A, offset: 0x31C, size: 0x8, addend: 0x0, symName: '_$sSo26FBSDKAppEventParameterNameas20_SwiftNewtypeWrapperSCs35_HasCustomAnyHashableRepresentationPWb', symObjAddr: 0x830, symBinAddr: 0x87A8, symSize: 0x2C }
  - { offsetInCU: 0x12D, offset: 0x32F, size: 0x8, addend: 0x0, symName: '_$sSo26FBSDKAppEventParameterNameaSHSCSQWb', symObjAddr: 0x85C, symBinAddr: 0x87D4, symSize: 0x2C }
  - { offsetInCU: 0x15B, offset: 0x35D, size: 0x8, addend: 0x0, symName: '_$sSo26FBSDKAppEventParameterNameas21_ObjectiveCBridgeableSCsACP016_forceBridgeFromE1C_6resulty01_E5CTypeQz_xSgztFZTW', symObjAddr: 0x294, symBinAddr: 0x8294, symSize: 0x14 }
  - { offsetInCU: 0x176, offset: 0x378, size: 0x8, addend: 0x0, symName: '_$sSo26FBSDKAppEventParameterNameas21_ObjectiveCBridgeableSCsACP024_conditionallyBridgeFromE1C_6resultSb01_E5CTypeQz_xSgztFZTW', symObjAddr: 0x2A8, symBinAddr: 0x82A8, symSize: 0x18 }
  - { offsetInCU: 0x197, offset: 0x399, size: 0x8, addend: 0x0, symName: '_$sSo18FBSDKLoginAuthTypeas21_ObjectiveCBridgeableSCsACP016_forceBridgeFromD1C_6resulty01_D5CTypeQz_xSgztFZTW', symObjAddr: 0x324, symBinAddr: 0x82D0, symSize: 0x14 }
  - { offsetInCU: 0x1B2, offset: 0x3B4, size: 0x8, addend: 0x0, symName: '_$sSo18FBSDKLoginAuthTypeas21_ObjectiveCBridgeableSCsACP024_conditionallyBridgeFromD1C_6resultSb01_D5CTypeQz_xSgztFZTW', symObjAddr: 0x338, symBinAddr: 0x82E4, symSize: 0x18 }
  - { offsetInCU: 0x1D3, offset: 0x3D5, size: 0x8, addend: 0x0, symName: '_$sSo26FBSDKAppEventParameterNameas35_HasCustomAnyHashableRepresentationSCsACP03_tofgH0s0gH0VSgyFTW', symObjAddr: 0x4CC, symBinAddr: 0x8444, symSize: 0x84 }
  - { offsetInCU: 0x1EE, offset: 0x3F0, size: 0x8, addend: 0x0, symName: '_$sSo18FBSDKLoginAuthTypeas35_HasCustomAnyHashableRepresentationSCsACP03_toefG0s0fG0VSgyFTW', symObjAddr: 0x550, symBinAddr: 0x84C8, symSize: 0x84 }
  - { offsetInCU: 0x242, offset: 0x444, size: 0x8, addend: 0x0, symName: '_$sSo14FBSDKAppEventsC13FBSDKLoginKit17LoginEventLoggingA2cDP13flushBehaviorSo0ab5FlushI0VvgTW', symObjAddr: 0x0, symBinAddr: 0x8000, symSize: 0x10 }
  - { offsetInCU: 0x293, offset: 0x495, size: 0x8, addend: 0x0, symName: '_$sSo14FBSDKAppEventsC13FBSDKLoginKit17LoginEventLoggingA2cDP011logInternalF0_10parameters18isImplicitlyLoggedySo0aF4Namea_SDySo0af9ParameterN0aypGSgSbtFTW', symObjAddr: 0x10, symBinAddr: 0x8010, symSize: 0xA4 }
  - { offsetInCU: 0x2D5, offset: 0x4D7, size: 0x8, addend: 0x0, symName: '_$sSo14FBSDKAppEventsC13FBSDKLoginKit17LoginEventLoggingA2cDP5flushyyFTW', symObjAddr: 0xB4, symBinAddr: 0x80B4, symSize: 0x10 }
  - { offsetInCU: 0x360, offset: 0x562, size: 0x8, addend: 0x0, symName: '_$sSo18FBSDKLoginAuthTypeaSYSCSY8rawValue03RawE0QzvgTW', symObjAddr: 0x45C, symBinAddr: 0x83D4, symSize: 0x28 }
  - { offsetInCU: 0x1D7, offset: 0x891, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryCAA0cD8CreatingA2aDP06createD011tokenString5nonce11graphDomain10completionySS_S2SySo019FBSDKAuthenticationD0CSgctFTW', symObjAddr: 0x1878, symBinAddr: 0xA1A0, symSize: 0x20 }
  - { offsetInCU: 0x41E, offset: 0xAD8, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataVSgSo13NSURLResponseCSgs5Error_pSgIegggg_So6NSDataCSgAGSo7NSErrorCSgIeyByyy_TR', symObjAddr: 0x166C, symBinAddr: 0x9F94, symSize: 0xC8 }
  - { offsetInCU: 0x435, offset: 0xAEF, size: 0x8, addend: 0x0, symName: '_$s10Foundation13__DataStorageC15withUnsafeBytes2in5applyxSnySiG_xSWKXEtKlFyt_Tg5153$s13FBSDKLoginKit26AuthenticationTokenFactoryC15verifySignature_6header6claims14certificateKey10completionySS_S3SySbctFySo03SecK3RefaSgcfU_yycfU_ySWXEfU_AA0B0VSays5UInt8VGTf1nncn_n', symObjAddr: 0x1898, symBinAddr: 0xA1C0, symSize: 0xCC }
  - { offsetInCU: 0x507, offset: 0xBC1, size: 0x8, addend: 0x0, symName: '_$sS2SSysWl', symObjAddr: 0x1A68, symBinAddr: 0xA390, symSize: 0x44 }
  - { offsetInCU: 0x51A, offset: 0xBD4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC06createD011tokenString5nonce11graphDomain10completionySS_S2SySo019FBSDKAuthenticationD0CSgctFySbcfU_TA', symObjAddr: 0x1AE8, symBinAddr: 0xA410, symSize: 0x30 }
  - { offsetInCU: 0x52D, offset: 0xBE7, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV15_RepresentationOWOe', symObjAddr: 0x1B6C, symBinAddr: 0xA494, symSize: 0x44 }
  - { offsetInCU: 0x540, offset: 0xBFA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC15verifySignature_6header6claims14certificateKey10completionySS_S3SySbctFySo03SecK3RefaSgcfU_TA', symObjAddr: 0x1BB0, symBinAddr: 0xA4D8, symSize: 0x10 }
  - { offsetInCU: 0x553, offset: 0xC0D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC16getPublicKeyWith011certificateH010completionySS_ySo03SecH3RefaSgctFySo0l11CertificateM0aSgcfU_TA', symObjAddr: 0x1BE4, symBinAddr: 0xA50C, symSize: 0x8 }
  - { offsetInCU: 0x566, offset: 0xC20, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataVSgWOy', symObjAddr: 0x1BEC, symBinAddr: 0xA514, symSize: 0x14 }
  - { offsetInCU: 0x579, offset: 0xC33, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV15_RepresentationOWOy', symObjAddr: 0x1C00, symBinAddr: 0xA528, symSize: 0x44 }
  - { offsetInCU: 0x58C, offset: 0xC46, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataVSgWOe', symObjAddr: 0x1C44, symBinAddr: 0xA56C, symSize: 0x14 }
  - { offsetInCU: 0x59F, offset: 0xC59, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC18getCertificateWith14certificateKey10completionySS_ySo03SecG3RefaSgctFy10Foundation4DataVSg_So13NSURLResponseCSgs5Error_pSgtcfU_TA', symObjAddr: 0x1C8C, symBinAddr: 0xA5B4, symSize: 0x2C }
  - { offsetInCU: 0x5B2, offset: 0xC6C, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x1CB8, symBinAddr: 0xA5E0, symSize: 0x10 }
  - { offsetInCU: 0x5C5, offset: 0xC7F, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x1CC8, symBinAddr: 0xA5F0, symSize: 0x8 }
  - { offsetInCU: 0x5D8, offset: 0xC92, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryCMa', symObjAddr: 0x1CD0, symBinAddr: 0xA5F8, symSize: 0x20 }
  - { offsetInCU: 0x8B6, offset: 0xF70, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC15verifySignature_6header6claims14certificateKey10completionySS_S3SySbctFySo03SecK3RefaSgcfU_yycfU_TA', symObjAddr: 0x22C8, symBinAddr: 0xABB0, symSize: 0x14 }
  - { offsetInCU: 0x8C9, offset: 0xF83, size: 0x8, addend: 0x0, symName: '_$sSS_yptWOc', symObjAddr: 0x22DC, symBinAddr: 0xABC4, symSize: 0x48 }
  - { offsetInCU: 0x8DC, offset: 0xF96, size: 0x8, addend: 0x0, symName: '_$sypWOb', symObjAddr: 0x2324, symBinAddr: 0xAC0C, symSize: 0x10 }
  - { offsetInCU: 0xAC4, offset: 0x117E, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_ypTgm5Tf4g_n', symObjAddr: 0x1964, symBinAddr: 0xA28C, symSize: 0x104 }
  - { offsetInCU: 0xC0E, offset: 0x12C8, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_SbTgm5Tf4g_n', symObjAddr: 0x1D04, symBinAddr: 0xA62C, symSize: 0xF8 }
  - { offsetInCU: 0xD58, offset: 0x1412, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_SSSgTgm5Tf4g_n', symObjAddr: 0x1DFC, symBinAddr: 0xA724, symSize: 0x114 }
  - { offsetInCU: 0xE96, offset: 0x1550, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_SSTgm5Tf4g_n', symObjAddr: 0x1F10, symBinAddr: 0xA838, symSize: 0x114 }
  - { offsetInCU: 0xFE6, offset: 0x16A0, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_SdTgm5Tf4g_n', symObjAddr: 0x2024, symBinAddr: 0xA94C, symSize: 0x104 }
  - { offsetInCU: 0x1130, offset: 0x17EA, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_So8NSNumberCTgm5Tf4g_n', symObjAddr: 0x2128, symBinAddr: 0xAA50, symSize: 0x104 }
  - { offsetInCU: 0x1585, offset: 0x1C3F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC16beginCertificateSSvg', symObjAddr: 0x0, symBinAddr: 0x8928, symSize: 0x2C }
  - { offsetInCU: 0x1598, offset: 0x1C52, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC14endCertificateSSvg', symObjAddr: 0x2C, symBinAddr: 0x8954, symSize: 0x2C }
  - { offsetInCU: 0x15AB, offset: 0x1C65, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC15sessionProviderSo24FBSDKURLSessionProviding_pvg', symObjAddr: 0x58, symBinAddr: 0x8980, symSize: 0x34 }
  - { offsetInCU: 0x15C4, offset: 0x1C7E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC15sessionProviderSo24FBSDKURLSessionProviding_pvs', symObjAddr: 0x8C, symBinAddr: 0x89B4, symSize: 0x44 }
  - { offsetInCU: 0x15D7, offset: 0x1C91, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC15sessionProviderSo24FBSDKURLSessionProviding_pvM', symObjAddr: 0xD0, symBinAddr: 0x89F8, symSize: 0x3C }
  - { offsetInCU: 0x15EA, offset: 0x1CA4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC15sessionProviderSo24FBSDKURLSessionProviding_pvM.resume.0', symObjAddr: 0x10C, symBinAddr: 0x8A34, symSize: 0x4 }
  - { offsetInCU: 0x15FD, offset: 0x1CB7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC19certificateEndpoint10Foundation3URLVvg', symObjAddr: 0x110, symBinAddr: 0x8A38, symSize: 0x158 }
  - { offsetInCU: 0x165F, offset: 0x1D19, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC15sessionProviderACSo24FBSDKURLSessionProviding_p_tcfC', symObjAddr: 0x268, symBinAddr: 0x8B90, symSize: 0xF4 }
  - { offsetInCU: 0x1701, offset: 0x1DBB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC06createD011tokenString5nonce11graphDomain10completionySS_S2SySo019FBSDKAuthenticationD0CSgctF', symObjAddr: 0x35C, symBinAddr: 0x8C84, symSize: 0x300 }
  - { offsetInCU: 0x18E2, offset: 0x1F9C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC06createD011tokenString5nonce11graphDomain10completionySS_S2SySo019FBSDKAuthenticationD0CSgctFySbcfU_', symObjAddr: 0x65C, symBinAddr: 0x8F84, symSize: 0x134 }
  - { offsetInCU: 0x19ED, offset: 0x20A7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC15verifySignature_6header6claims14certificateKey10completionySS_S3SySbctF', symObjAddr: 0x790, symBinAddr: 0x90B8, symSize: 0x278 }
  - { offsetInCU: 0x1B39, offset: 0x21F3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC15verifySignature_6header6claims14certificateKey10completionySS_S3SySbctFySo03SecK3RefaSgcfU_', symObjAddr: 0xA08, symBinAddr: 0x9330, symSize: 0x100 }
  - { offsetInCU: 0x1BC2, offset: 0x227C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC15verifySignature_6header6claims14certificateKey10completionySS_S3SySbctFySo03SecK3RefaSgcfU_yycfU_', symObjAddr: 0xB08, symBinAddr: 0x9430, symSize: 0x324 }
  - { offsetInCU: 0x1EF8, offset: 0x25B2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC15verifySignature_6header6claims14certificateKey10completionySS_S3SySbctFySo03SecK3RefaSgcfU_yycfU_ySWXEfU_', symObjAddr: 0xE3C, symBinAddr: 0x9764, symSize: 0xDC }
  - { offsetInCU: 0x20F0, offset: 0x27AA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC16getPublicKeyWith011certificateH010completionySS_ySo03SecH3RefaSgctF', symObjAddr: 0xF28, symBinAddr: 0x9850, symSize: 0x78 }
  - { offsetInCU: 0x211E, offset: 0x27D8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC16getPublicKeyWith011certificateH010completionySS_ySo03SecH3RefaSgctFySo0l11CertificateM0aSgcfU_', symObjAddr: 0xFA0, symBinAddr: 0x98C8, symSize: 0xCC }
  - { offsetInCU: 0x21E1, offset: 0x289B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC18getCertificateWith14certificateKey10completionySS_ySo03SecG3RefaSgctF', symObjAddr: 0x106C, symBinAddr: 0x9994, symSize: 0x1D4 }
  - { offsetInCU: 0x2236, offset: 0x28F0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryC18getCertificateWith14certificateKey10completionySS_ySo03SecG3RefaSgctFy10Foundation4DataVSg_So13NSURLResponseCSgs5Error_pSgtcfU_', symObjAddr: 0x1240, symBinAddr: 0x9B68, symSize: 0x42C }
  - { offsetInCU: 0x2344, offset: 0x29FE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryCfd', symObjAddr: 0x1734, symBinAddr: 0xA05C, symSize: 0x2C }
  - { offsetInCU: 0x2371, offset: 0x2A2B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryCfD', symObjAddr: 0x1760, symBinAddr: 0xA088, symSize: 0x34 }
  - { offsetInCU: 0x23A6, offset: 0x2A60, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryCACycfC', symObjAddr: 0x1794, symBinAddr: 0xA0BC, symSize: 0x34 }
  - { offsetInCU: 0x23B9, offset: 0x2A73, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26AuthenticationTokenFactoryCACycfc', symObjAddr: 0x17C8, symBinAddr: 0xA0F0, symSize: 0xB0 }
  - { offsetInCU: 0x105, offset: 0x2DAC, size: 0x8, addend: 0x0, symName: '_$sypSgWOh', symObjAddr: 0x6A0, symBinAddr: 0xB248, symSize: 0x40 }
  - { offsetInCU: 0x118, offset: 0x2DBF, size: 0x8, addend: 0x0, symName: '_$sypWOc', symObjAddr: 0x6E0, symBinAddr: 0xB288, symSize: 0x3C }
  - { offsetInCU: 0x12B, offset: 0x2DD2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit25AuthenticationTokenHeaderCMa', symObjAddr: 0x71C, symBinAddr: 0xB2C4, symSize: 0x20 }
  - { offsetInCU: 0x1ED, offset: 0x2E94, size: 0x8, addend: 0x0, symName: '_$ss11AnyHashableVWOc', symObjAddr: 0xB18, symBinAddr: 0xB6C0, symSize: 0x3C }
  - { offsetInCU: 0x200, offset: 0x2EA7, size: 0x8, addend: 0x0, symName: '_$ss11AnyHashableVWOh', symObjAddr: 0xB54, symBinAddr: 0xB6FC, symSize: 0x34 }
  - { offsetInCU: 0x35C, offset: 0x3003, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit25AuthenticationTokenHeaderC17fromEncodedStringACSgSS_tcfC', symObjAddr: 0x0, symBinAddr: 0xAC2C, symSize: 0x48 }
  - { offsetInCU: 0x36F, offset: 0x3016, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit25AuthenticationTokenHeaderC3kidSSvg', symObjAddr: 0x48, symBinAddr: 0xAC74, symSize: 0x2C }
  - { offsetInCU: 0x3A0, offset: 0x3047, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit25AuthenticationTokenHeaderC17fromEncodedStringACSgSS_tcfc', symObjAddr: 0x74, symBinAddr: 0xACA0, symSize: 0x504 }
  - { offsetInCU: 0x4C6, offset: 0x316D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit25AuthenticationTokenHeaderCfd', symObjAddr: 0x5FC, symBinAddr: 0xB1A4, symSize: 0x1C }
  - { offsetInCU: 0x4FE, offset: 0x31A5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit25AuthenticationTokenHeaderCfD', symObjAddr: 0x618, symBinAddr: 0xB1C0, symSize: 0x24 }
  - { offsetInCU: 0x547, offset: 0x31EE, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4findys10_HashTableV6BucketV6bucket_Sb5foundtxSHRzlFSS_Tg5', symObjAddr: 0x63C, symBinAddr: 0xB1E4, symSize: 0x64 }
  - { offsetInCU: 0x582, offset: 0x3229, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4findys10_HashTableV6BucketV6bucket_Sb5foundtxSHRzlFs11AnyHashableV_Tg5', symObjAddr: 0x750, symBinAddr: 0xB2F8, symSize: 0x30 }
  - { offsetInCU: 0x5AF, offset: 0x3256, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4findys10_HashTableV6BucketV6bucket_Sb5foundtxSHRzlFSo26FBSDKAppEventParameterNamea_Tg5', symObjAddr: 0x780, symBinAddr: 0xB328, symSize: 0x80 }
  - { offsetInCU: 0x5EA, offset: 0x3291, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4find_9hashValues10_HashTableV6BucketV6bucket_Sb5foundtx_SitSHRzlFSS_Tg5', symObjAddr: 0x800, symBinAddr: 0xB3A8, symSize: 0xE0 }
  - { offsetInCU: 0x658, offset: 0x32FF, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4find_9hashValues10_HashTableV6BucketV6bucket_Sb5foundtx_SitSHRzlFs11AnyHashableV_Tg5', symObjAddr: 0x8E0, symBinAddr: 0xB488, symSize: 0xC4 }
  - { offsetInCU: 0x67F, offset: 0x3326, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4find_9hashValues10_HashTableV6BucketV6bucket_Sb5foundtx_SitSHRzlFSo26FBSDKAppEventParameterNamea_Tg5', symObjAddr: 0x9A4, symBinAddr: 0xB54C, symSize: 0x174 }
  - { offsetInCU: 0x2B, offset: 0x33B5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12CodeVerifierC5valueSSvgTo', symObjAddr: 0x0, symBinAddr: 0xB730, symSize: 0x4C }
  - { offsetInCU: 0x61, offset: 0x33EB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12CodeVerifierC5valueSSvgTo', symObjAddr: 0x0, symBinAddr: 0xB730, symSize: 0x4C }
  - { offsetInCU: 0x99, offset: 0x3423, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12CodeVerifierC9challengeSSvgTo', symObjAddr: 0x84, symBinAddr: 0xB7B4, symSize: 0x58 }
  - { offsetInCU: 0x14B, offset: 0x34D5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12CodeVerifierC6stringACSgSS_tcfcTo', symObjAddr: 0xA5C, symBinAddr: 0xC148, symSize: 0x28 }
  - { offsetInCU: 0x190, offset: 0x351A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12CodeVerifierCACycfcTo', symObjAddr: 0xD40, symBinAddr: 0xC42C, symSize: 0x20 }
  - { offsetInCU: 0x400, offset: 0x378A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12CodeVerifierCfETo', symObjAddr: 0xD94, symBinAddr: 0xC480, symSize: 0x14 }
  - { offsetInCU: 0x42D, offset: 0x37B7, size: 0x8, addend: 0x0, symName: '_$s10Foundation13__DataStorageC15withUnsafeBytes2in5applyxSnySiG_xSWKXEtKlFyt_Tg554$s13FBSDKLoginKit12CodeVerifierC9challengeSSvgySWXEfU_AA0B0VSays5UInt8VGTf1nncn_n', symObjAddr: 0xDA8, symBinAddr: 0xC494, symSize: 0xCC }
  - { offsetInCU: 0x455, offset: 0x37DF, size: 0x8, addend: 0x0, symName: '_$sSays5UInt8VGSayxG10Foundation15ContiguousBytesAeBRszlWl', symObjAddr: 0xF64, symBinAddr: 0xC560, symSize: 0x48 }
  - { offsetInCU: 0x468, offset: 0x37F2, size: 0x8, addend: 0x0, symName: '_$sSays5UInt8VGMa', symObjAddr: 0xFAC, symBinAddr: 0xC5A8, symSize: 0x54 }
  - { offsetInCU: 0x47B, offset: 0x3805, size: 0x8, addend: 0x0, symName: ___swift_project_boxed_opaque_existential_1, symObjAddr: 0x1000, symBinAddr: 0xC5FC, symSize: 0x24 }
  - { offsetInCU: 0x745, offset: 0x3ACF, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV06InlineB0VyAESWcfCTf4nd_n', symObjAddr: 0x13F0, symBinAddr: 0xC9EC, symSize: 0xC4 }
  - { offsetInCU: 0x7B5, offset: 0x3B3F, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV10LargeSliceVyAESWcfCTf4nd_n', symObjAddr: 0x14B4, symBinAddr: 0xCAB0, symSize: 0x78 }
  - { offsetInCU: 0x7E0, offset: 0x3B6A, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV11InlineSliceVyAESWcfCTf4nd_n', symObjAddr: 0x152C, symBinAddr: 0xCB28, symSize: 0x80 }
  - { offsetInCU: 0x86D, offset: 0x3BF7, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV15_RepresentationOyAESWcfCTf4nd_n', symObjAddr: 0x15AC, symBinAddr: 0xCBA8, symSize: 0x68 }
  - { offsetInCU: 0x8BE, offset: 0x3C48, size: 0x8, addend: 0x0, symName: ___swift_destroy_boxed_opaque_existential_1, symObjAddr: 0x1614, symBinAddr: 0xCC10, symSize: 0x20 }
  - { offsetInCU: 0x8D1, offset: 0x3C5B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12CodeVerifierCMa', symObjAddr: 0x1634, symBinAddr: 0xCC30, symSize: 0x20 }
  - { offsetInCU: 0xB43, offset: 0x3ECD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12CodeVerifierC5valueSSvg', symObjAddr: 0x4C, symBinAddr: 0xB77C, symSize: 0x38 }
  - { offsetInCU: 0xBB3, offset: 0x3F3D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12CodeVerifierC9challengeSSvg', symObjAddr: 0xDC, symBinAddr: 0xB80C, symSize: 0x448 }
  - { offsetInCU: 0xE8D, offset: 0x4217, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12CodeVerifierC9challengeSSvgySWXEfU_', symObjAddr: 0x534, symBinAddr: 0xBC64, symSize: 0xDC }
  - { offsetInCU: 0x10D6, offset: 0x4460, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12CodeVerifierC6stringACSgSS_tcfC', symObjAddr: 0x664, symBinAddr: 0xBD50, symSize: 0x1F8 }
  - { offsetInCU: 0x1168, offset: 0x44F2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12CodeVerifierC6stringACSgSS_tcfc', symObjAddr: 0x85C, symBinAddr: 0xBF48, symSize: 0x200 }
  - { offsetInCU: 0x11F6, offset: 0x4580, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12CodeVerifierCACycfC', symObjAddr: 0xA84, symBinAddr: 0xC170, symSize: 0x20 }
  - { offsetInCU: 0x1233, offset: 0x45BD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12CodeVerifierCACycfc', symObjAddr: 0xAA4, symBinAddr: 0xC190, symSize: 0x29C }
  - { offsetInCU: 0x1387, offset: 0x4711, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12CodeVerifierCfD', symObjAddr: 0xD60, symBinAddr: 0xC44C, symSize: 0x34 }
  - { offsetInCU: 0x13CC, offset: 0x4756, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtFs5UInt8V_Tg5', symObjAddr: 0x1024, symBinAddr: 0xC620, symSize: 0xB4 }
  - { offsetInCU: 0x14B1, offset: 0x483B, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtF13FBSDKLoginKit18DeviceLoginManagerC_Tg5', symObjAddr: 0x10D8, symBinAddr: 0xC6D4, symSize: 0x160 }
  - { offsetInCU: 0x1606, offset: 0x4990, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtFSS_Tg5', symObjAddr: 0x1238, symBinAddr: 0xC834, symSize: 0xBC }
  - { offsetInCU: 0x16D9, offset: 0x4A63, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV13_copyContents8subRange12initializingSpyxGSnySiG_AFtF13FBSDKLoginKit18DeviceLoginManagerC_Tg5', symObjAddr: 0x12F4, symBinAddr: 0xC8F0, symSize: 0xFC }
  - { offsetInCU: 0x27, offset: 0x4B9E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15DefaultAudienceO8rawValueACSgSu_tcfC', symObjAddr: 0x0, symBinAddr: 0xCC5C, symSize: 0x14 }
  - { offsetInCU: 0x73, offset: 0x4BEA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15DefaultAudienceOSYAASY8rawValuexSg03RawF0Qz_tcfCTW', symObjAddr: 0x120, symBinAddr: 0xCD7C, symSize: 0x20 }
  - { offsetInCU: 0xA2, offset: 0x4C19, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15DefaultAudienceOSYAASY8rawValue03RawF0QzvgTW', symObjAddr: 0x140, symBinAddr: 0xCD9C, symSize: 0xC }
  - { offsetInCU: 0xC8, offset: 0x4C3F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15DefaultAudienceOSHAASQWb', symObjAddr: 0x2C, symBinAddr: 0xCC88, symSize: 0x4 }
  - { offsetInCU: 0xDB, offset: 0x4C52, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15DefaultAudienceOACSQAAWl', symObjAddr: 0x30, symBinAddr: 0xCC8C, symSize: 0x44 }
  - { offsetInCU: 0x10C, offset: 0x4C83, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15DefaultAudienceOMa', symObjAddr: 0x14C, symBinAddr: 0xCDA8, symSize: 0x10 }
  - { offsetInCU: 0x14D, offset: 0x4CC4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15DefaultAudienceOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x18, symBinAddr: 0xCC74, symSize: 0x14 }
  - { offsetInCU: 0x1F5, offset: 0x4D6C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15DefaultAudienceOSHAASH9hashValueSivgTW', symObjAddr: 0x74, symBinAddr: 0xCCD0, symSize: 0x44 }
  - { offsetInCU: 0x29C, offset: 0x4E13, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15DefaultAudienceOSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0xB8, symBinAddr: 0xCD14, symSize: 0x28 }
  - { offsetInCU: 0x2EB, offset: 0x4E62, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15DefaultAudienceOSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0xE0, symBinAddr: 0xCD3C, symSize: 0x40 }
  - { offsetInCU: 0x3F0, offset: 0x4F67, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15DefaultAudienceO8rawValueACSgSu_tcfC', symObjAddr: 0x0, symBinAddr: 0xCC5C, symSize: 0x14 }
  - { offsetInCU: 0x40D, offset: 0x4F84, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15DefaultAudienceO8rawValueSuvg', symObjAddr: 0x14, symBinAddr: 0xCC70, symSize: 0x4 }
  - { offsetInCU: 0xE4, offset: 0x50BF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerCAA17DependentAsObjectA2aDP15setDependenciesyy0hJ0QzFTW', symObjAddr: 0x434, symBinAddr: 0xD1EC, symSize: 0x68 }
  - { offsetInCU: 0x1B8, offset: 0x5193, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCAA17DependentAsObjectA2aDP15setDependenciesyy0gI0QzFTW', symObjAddr: 0x49C, symBinAddr: 0xD254, symSize: 0x68 }
  - { offsetInCU: 0x233, offset: 0x520E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesVWOc', symObjAddr: 0x504, symBinAddr: 0xD2BC, symSize: 0x3C }
  - { offsetInCU: 0x246, offset: 0x5221, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesVWOc', symObjAddr: 0x580, symBinAddr: 0xD2F8, symSize: 0x3C }
  - { offsetInCU: 0x349, offset: 0x5324, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17DependentAsObjectPAAE15setDependenciesyy0eG0QzF', symObjAddr: 0x0, symBinAddr: 0xCDB8, symSize: 0xDC }
  - { offsetInCU: 0x388, offset: 0x5363, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17DependentAsObjectPAAE15getDependencies0eG0QzyKF', symObjAddr: 0xDC, symBinAddr: 0xCE94, symSize: 0x214 }
  - { offsetInCU: 0x3B8, offset: 0x5393, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17DependentAsObjectPAAE13dynamicMemberqd__Sgs7KeyPathCy0E12DependenciesQzqd__G_tcluig', symObjAddr: 0x2F0, symBinAddr: 0xD0A8, symSize: 0x11C }
  - { offsetInCU: 0xED, offset: 0x55D0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerCAA15DependentAsTypeA2aDP15setDependenciesyy0hJ0QzFZTW', symObjAddr: 0x434, symBinAddr: 0xD7AC, symSize: 0x98 }
  - { offsetInCU: 0x1A4, offset: 0x5687, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterCAA15DependentAsTypeA2aDP15setDependenciesyy0hJ0QzFZTW', symObjAddr: 0x4CC, symBinAddr: 0xD844, symSize: 0x98 }
  - { offsetInCU: 0x259, offset: 0x573C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterVAA15DependentAsTypeA2aDP15setDependenciesyy0gI0QzFZTW', symObjAddr: 0x564, symBinAddr: 0xD8DC, symSize: 0x98 }
  - { offsetInCU: 0x2B2, offset: 0x5795, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesVWOc', symObjAddr: 0x5FC, symBinAddr: 0xD974, symSize: 0x3C }
  - { offsetInCU: 0x2C5, offset: 0x57A8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesVWOc', symObjAddr: 0x678, symBinAddr: 0xD9B0, symSize: 0x3C }
  - { offsetInCU: 0x2D8, offset: 0x57BB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC16TypeDependenciesVWOc', symObjAddr: 0x6B4, symBinAddr: 0xD9EC, symSize: 0x3C }
  - { offsetInCU: 0x3BA, offset: 0x589D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15DependentAsTypePAAE15setDependenciesyy0eG0QzFZ', symObjAddr: 0x0, symBinAddr: 0xD378, symSize: 0xDC }
  - { offsetInCU: 0x3FA, offset: 0x58DD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15DependentAsTypePAAE15getDependencies0eG0QzyKFZ', symObjAddr: 0xDC, symBinAddr: 0xD454, symSize: 0x214 }
  - { offsetInCU: 0x42B, offset: 0x590E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15DependentAsTypePAAE13dynamicMemberqd__Sgs7KeyPathCy0E12DependenciesQzqd__G_tcluigZ', symObjAddr: 0x2F0, symBinAddr: 0xD668, symSize: 0x11C }
  - { offsetInCU: 0x8C, offset: 0x5AB1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoC15verificationURL10Foundation0H0VvgTo', symObjAddr: 0xD0, symBinAddr: 0xDB78, symSize: 0x28 }
  - { offsetInCU: 0xD9, offset: 0x5AFE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoC14expirationDate10Foundation0H0VvgTo', symObjAddr: 0x10C, symBinAddr: 0xDBB4, symSize: 0x28 }
  - { offsetInCU: 0x126, offset: 0x5B4B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoC15pollingIntervalSuvgTo', symObjAddr: 0x188, symBinAddr: 0xDC30, symSize: 0x10 }
  - { offsetInCU: 0x146, offset: 0x5B6B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoC15pollingIntervalSuvgTo', symObjAddr: 0x188, symBinAddr: 0xDC30, symSize: 0x10 }
  - { offsetInCU: 0x1BB, offset: 0x5BE0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoC10identifier05loginE015verificationURL14expirationDate15pollingIntervalACSS_SS10Foundation0J0VAI0L0VSutcfcTo', symObjAddr: 0x428, symBinAddr: 0xDED0, symSize: 0x19C }
  - { offsetInCU: 0x259, offset: 0x5C7E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoCACycfcTo', symObjAddr: 0x610, symBinAddr: 0xE0B8, symSize: 0x2C }
  - { offsetInCU: 0x2E6, offset: 0x5D0B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoCfETo', symObjAddr: 0x670, symBinAddr: 0xE118, symSize: 0x90 }
  - { offsetInCU: 0x314, offset: 0x5D39, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoCMU', symObjAddr: 0x700, symBinAddr: 0xE1A8, symSize: 0x8 }
  - { offsetInCU: 0x327, offset: 0x5D4C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoCMa', symObjAddr: 0x708, symBinAddr: 0xE1B0, symSize: 0x3C }
  - { offsetInCU: 0x33A, offset: 0x5D5F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoCMr', symObjAddr: 0x744, symBinAddr: 0xE1EC, symSize: 0xA0 }
  - { offsetInCU: 0x424, offset: 0x5E49, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoC10identifierSSvg', symObjAddr: 0xC, symBinAddr: 0xDAB4, symSize: 0x38 }
  - { offsetInCU: 0x447, offset: 0x5E6C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoC05loginE0SSvg', symObjAddr: 0x98, symBinAddr: 0xDB40, symSize: 0x38 }
  - { offsetInCU: 0x4A5, offset: 0x5ECA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoC15pollingIntervalSuvg', symObjAddr: 0x198, symBinAddr: 0xDC40, symSize: 0x10 }
  - { offsetInCU: 0x523, offset: 0x5F48, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoC10identifier05loginE015verificationURL14expirationDate15pollingIntervalACSS_SS10Foundation0J0VAI0L0VSutcfC', symObjAddr: 0x1A8, symBinAddr: 0xDC50, symSize: 0x140 }
  - { offsetInCU: 0x591, offset: 0x5FB6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoC10identifier05loginE015verificationURL14expirationDate15pollingIntervalACSS_SS10Foundation0J0VAI0L0VSutcfc', symObjAddr: 0x2E8, symBinAddr: 0xDD90, symSize: 0x140 }
  - { offsetInCU: 0x5EF, offset: 0x6014, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoCACycfC', symObjAddr: 0x5C4, symBinAddr: 0xE06C, symSize: 0x20 }
  - { offsetInCU: 0x608, offset: 0x602D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoCACycfc', symObjAddr: 0x5E4, symBinAddr: 0xE08C, symSize: 0x2C }
  - { offsetInCU: 0x65C, offset: 0x6081, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19DeviceLoginCodeInfoCfD', symObjAddr: 0x63C, symBinAddr: 0xE0E4, symSize: 0x34 }
  - { offsetInCU: 0x186, offset: 0x6228, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV10Foundation13CustomNSErrorAadEP11errorDomainSSvgZTW', symObjAddr: 0x214, symBinAddr: 0xE4B4, symSize: 0x5C }
  - { offsetInCU: 0x1BB, offset: 0x625D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV10Foundation13CustomNSErrorAadEP9errorCodeSivgTW', symObjAddr: 0x270, symBinAddr: 0xE510, symSize: 0x8 }
  - { offsetInCU: 0x1DA, offset: 0x627C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV10Foundation13CustomNSErrorAadEP9errorCodeSivgTW', symObjAddr: 0x270, symBinAddr: 0xE510, symSize: 0x8 }
  - { offsetInCU: 0x1EB, offset: 0x628D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV10Foundation13CustomNSErrorAadEP13errorUserInfoSDySSypGvgTW', symObjAddr: 0x278, symBinAddr: 0xE518, symSize: 0x8 }
  - { offsetInCU: 0x20A, offset: 0x62AC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV10Foundation13CustomNSErrorAadEP13errorUserInfoSDySSypGvgTW', symObjAddr: 0x278, symBinAddr: 0xE518, symSize: 0x8 }
  - { offsetInCU: 0x21B, offset: 0x62BD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVSHAASH9hashValueSivgTW', symObjAddr: 0x280, symBinAddr: 0xE520, symSize: 0x44 }
  - { offsetInCU: 0x2F5, offset: 0x6397, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0x2C4, symBinAddr: 0xE564, symSize: 0x28 }
  - { offsetInCU: 0x363, offset: 0x6405, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x384, symBinAddr: 0xE624, symSize: 0x34 }
  - { offsetInCU: 0x40C, offset: 0x64AE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceLoginErrorCodeOSYAASY8rawValuexSg03RawH0Qz_tcfCTW', symObjAddr: 0x47C, symBinAddr: 0xE71C, symSize: 0x30 }
  - { offsetInCU: 0x43B, offset: 0x64DD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceLoginErrorCodeOSYAASY8rawValue03RawH0QzvgTW', symObjAddr: 0x4AC, symBinAddr: 0xE74C, symSize: 0xC }
  - { offsetInCU: 0x457, offset: 0x64F9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceLoginErrorCodeO8rawValueACSgSi_tcfCTf4nd_n', symObjAddr: 0x4CC, symBinAddr: 0xE76C, symSize: 0x30 }
  - { offsetInCU: 0x4BB, offset: 0x655D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV10Foundation13CustomNSErrorAAs0E0PWb', symObjAddr: 0x4FC, symBinAddr: 0xE79C, symSize: 0x4 }
  - { offsetInCU: 0x4CE, offset: 0x6570, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVACs0E0AAWl', symObjAddr: 0x500, symBinAddr: 0xE7A0, symSize: 0x44 }
  - { offsetInCU: 0x4E1, offset: 0x6583, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVSHAASQWb', symObjAddr: 0x544, symBinAddr: 0xE7E4, symSize: 0x4 }
  - { offsetInCU: 0x4F4, offset: 0x6596, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVACSQAAWl', symObjAddr: 0x548, symBinAddr: 0xE7E8, symSize: 0x44 }
  - { offsetInCU: 0x507, offset: 0x65A9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceLoginErrorCodeOSHAASQWb', symObjAddr: 0x58C, symBinAddr: 0xE82C, symSize: 0x4 }
  - { offsetInCU: 0x51A, offset: 0x65BC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceLoginErrorCodeOACSQAAWl', symObjAddr: 0x590, symBinAddr: 0xE830, symSize: 0x44 }
  - { offsetInCU: 0x52D, offset: 0x65CF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVwxx', symObjAddr: 0x5D8, symBinAddr: 0xE878, symSize: 0x28 }
  - { offsetInCU: 0x540, offset: 0x65E2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVwca', symObjAddr: 0x640, symBinAddr: 0xE8E0, symSize: 0x64 }
  - { offsetInCU: 0x553, offset: 0x65F5, size: 0x8, addend: 0x0, symName: ___swift_memcpy24_8, symObjAddr: 0x6A4, symBinAddr: 0xE944, symSize: 0x14 }
  - { offsetInCU: 0x566, offset: 0x6608, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVwta', symObjAddr: 0x6B8, symBinAddr: 0xE958, symSize: 0x44 }
  - { offsetInCU: 0x579, offset: 0x661B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVwet', symObjAddr: 0x6FC, symBinAddr: 0xE99C, symSize: 0x48 }
  - { offsetInCU: 0x58C, offset: 0x662E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVwst', symObjAddr: 0x744, symBinAddr: 0xE9E4, symSize: 0x40 }
  - { offsetInCU: 0x59F, offset: 0x6641, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVMa', symObjAddr: 0x784, symBinAddr: 0xEA24, symSize: 0x10 }
  - { offsetInCU: 0x5B2, offset: 0x6654, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceLoginErrorCodeOMa', symObjAddr: 0x794, symBinAddr: 0xEA34, symSize: 0x10 }
  - { offsetInCU: 0x5C5, offset: 0x6667, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVAC10Foundation13CustomNSErrorAAWl', symObjAddr: 0x7A4, symBinAddr: 0xEA44, symSize: 0x44 }
  - { offsetInCU: 0x659, offset: 0x66FB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0x2EC, symBinAddr: 0xE58C, symSize: 0x40 }
  - { offsetInCU: 0x6EF, offset: 0x6791, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVs0E0AAsADP9_userInfoyXlSgvgTW', symObjAddr: 0x37C, symBinAddr: 0xE61C, symSize: 0x4 }
  - { offsetInCU: 0x70A, offset: 0x67AC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVs0E0AAsADP19_getEmbeddedNSErroryXlSgyFTW', symObjAddr: 0x380, symBinAddr: 0xE620, symSize: 0x4 }
  - { offsetInCU: 0x78A, offset: 0x682C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceLoginErrorCodeOSHAASH9hashValueSivgTW', symObjAddr: 0x3D0, symBinAddr: 0xE670, symSize: 0x44 }
  - { offsetInCU: 0x831, offset: 0x68D3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceLoginErrorCodeOSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0x414, symBinAddr: 0xE6B4, symSize: 0x28 }
  - { offsetInCU: 0x880, offset: 0x6922, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceLoginErrorCodeOSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0x43C, symBinAddr: 0xE6DC, symSize: 0x40 }
  - { offsetInCU: 0x905, offset: 0x69A7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceLoginErrorCodeOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x4B8, symBinAddr: 0xE758, symSize: 0x14 }
  - { offsetInCU: 0x998, offset: 0x6A3A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVs0E0AAsADP7_domainSSvgTW', symObjAddr: 0x32C, symBinAddr: 0xE5CC, symSize: 0x28 }
  - { offsetInCU: 0x9B4, offset: 0x6A56, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorVs0E0AAsADP5_codeSivgTW', symObjAddr: 0x354, symBinAddr: 0xE5F4, symSize: 0x28 }
  - { offsetInCU: 0xA3C, offset: 0x6ADE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV03_nsE0So7NSErrorCSgvg', symObjAddr: 0x0, symBinAddr: 0xE2A0, symSize: 0x28 }
  - { offsetInCU: 0xA4F, offset: 0x6AF1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV9errorCodeSivg', symObjAddr: 0x28, symBinAddr: 0xE2C8, symSize: 0x8 }
  - { offsetInCU: 0xA63, offset: 0x6B05, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV13errorUserInfoSDySSypGvg', symObjAddr: 0x30, symBinAddr: 0xE2D0, symSize: 0x8 }
  - { offsetInCU: 0xA82, offset: 0x6B24, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV03_nsE0ACSo7NSErrorC_tcfC', symObjAddr: 0x38, symBinAddr: 0xE2D8, symSize: 0xA0 }
  - { offsetInCU: 0xAA5, offset: 0x6B47, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV_8userInfoAcA0cdE4CodeO_SDySSypGtcfC', symObjAddr: 0xD8, symBinAddr: 0xE378, symSize: 0xC }
  - { offsetInCU: 0xAD5, offset: 0x6B77, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceLoginErrorCodeO8rawValueSivg', symObjAddr: 0xE4, symBinAddr: 0xE384, symSize: 0x4 }
  - { offsetInCU: 0xAFA, offset: 0x6B9C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV11errorDomainSSvgZ', symObjAddr: 0xE8, symBinAddr: 0xE388, symSize: 0x5C }
  - { offsetInCU: 0xB21, offset: 0x6BC3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV16excessivePollingAA0cdE4CodeOvgZ', symObjAddr: 0x144, symBinAddr: 0xE3E4, symSize: 0xC }
  - { offsetInCU: 0xB42, offset: 0x6BE4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV21authorizationDeclinedAA0cdE4CodeOvgZ', symObjAddr: 0x150, symBinAddr: 0xE3F0, symSize: 0xC }
  - { offsetInCU: 0xB63, offset: 0x6C05, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV20authorizationPendingAA0cdE4CodeOvgZ', symObjAddr: 0x15C, symBinAddr: 0xE3FC, symSize: 0xC }
  - { offsetInCU: 0xB84, offset: 0x6C26, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV11codeExpiredAA0cdE4CodeOvgZ', symObjAddr: 0x168, symBinAddr: 0xE408, symSize: 0xC }
  - { offsetInCU: 0xBA5, offset: 0x6C47, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV2eeoiySbAC_ACtFZ', symObjAddr: 0x174, symBinAddr: 0xE414, symSize: 0x34 }
  - { offsetInCU: 0xC14, offset: 0x6CB6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV4hash4intoys6HasherVz_tF', symObjAddr: 0x1A8, symBinAddr: 0xE448, symSize: 0x28 }
  - { offsetInCU: 0xC86, offset: 0x6D28, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16DeviceLoginErrorV9hashValueSivg', symObjAddr: 0x1D0, symBinAddr: 0xE470, symSize: 0x44 }
  - { offsetInCU: 0xDBE, offset: 0x6E60, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceLoginErrorCodeO8rawValueACSgSi_tcfC', symObjAddr: 0x3B8, symBinAddr: 0xE658, symSize: 0x18 }
  - { offsetInCU: 0x4E, offset: 0x6EF3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC05loginE9Instances33_2E1868FF91A815585B124C0140A60DCBLLSayACGvpZ', symObjAddr: 0x7468, symBinAddr: 0x61DC8, symSize: 0x0 }
  - { offsetInCU: 0x7C, offset: 0x6F21, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC8delegateAA0cdE8Delegate_pSgvgTo', symObjAddr: 0x14, symBinAddr: 0xEA9C, symSize: 0x48 }
  - { offsetInCU: 0xD0, offset: 0x6F75, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC8delegateAA0cdE8Delegate_pSgvsTo', symObjAddr: 0xA4, symBinAddr: 0xEB2C, symSize: 0x50 }
  - { offsetInCU: 0x150, offset: 0x6FF5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC11permissionsSaySSGvgTo', symObjAddr: 0x228, symBinAddr: 0xECB0, symSize: 0x48 }
  - { offsetInCU: 0x19D, offset: 0x7042, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC11redirectURL10Foundation0G0VSgvgTo', symObjAddr: 0x280, symBinAddr: 0xED08, symSize: 0xD4 }
  - { offsetInCU: 0x1F1, offset: 0x7096, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC11redirectURL10Foundation0G0VSgvsTo', symObjAddr: 0x3EC, symBinAddr: 0xEE34, symSize: 0xFC }
  - { offsetInCU: 0x26B, offset: 0x7110, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC8codeInfoAA0cd4CodeG0CSgvgTo', symObjAddr: 0x590, symBinAddr: 0xEFD8, symSize: 0x48 }
  - { offsetInCU: 0x2BF, offset: 0x7164, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC8codeInfoAA0cd4CodeG0CSgvsTo', symObjAddr: 0x628, symBinAddr: 0xF070, symSize: 0x64 }
  - { offsetInCU: 0x37D, offset: 0x7222, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC11permissions011enableSmartD0ACSaySSG_SbtcfcTo', symObjAddr: 0xB20, symBinAddr: 0xF568, symSize: 0x38 }
  - { offsetInCU: 0x441, offset: 0x72E6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC5startyyFTo', symObjAddr: 0x2530, symBinAddr: 0x10F78, symSize: 0x28 }
  - { offsetInCU: 0x471, offset: 0x7316, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC6cancelyyFTo', symObjAddr: 0x272C, symBinAddr: 0x11174, symSize: 0x28 }
  - { offsetInCU: 0x4B0, offset: 0x7355, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC14notifyDelegate5token14expirationDate020dataAccessExpirationJ0ySSSg_10Foundation0J0VSgALtFTo', symObjAddr: 0x3A00, symBinAddr: 0x12448, symSize: 0x18C }
  - { offsetInCU: 0x4CB, offset: 0x7370, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC12processErroryys0G0_pFTo', symObjAddr: 0x3B8C, symBinAddr: 0x125D4, symSize: 0x50 }
  - { offsetInCU: 0x4E6, offset: 0x738B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC12schedulePoll8intervalySu_tFTo', symObjAddr: 0x4DE0, symBinAddr: 0x13828, symSize: 0x30 }
  - { offsetInCU: 0x52B, offset: 0x73D0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerCACycfcTo', symObjAddr: 0x4E5C, symBinAddr: 0x138A4, symSize: 0x2C }
  - { offsetInCU: 0x5BD, offset: 0x7462, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerCAA17DependentAsObjectA2aDP22configuredDependencies0hJ0QzSgvsTW', symObjAddr: 0x510C, symBinAddr: 0x13B54, symSize: 0x60 }
  - { offsetInCU: 0x5FF, offset: 0x74A4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerCAA17DependentAsObjectA2aDP22configuredDependencies0hJ0QzSgvMTW', symObjAddr: 0x516C, symBinAddr: 0x13BB4, symSize: 0x44 }
  - { offsetInCU: 0x63A, offset: 0x74DF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC05loginE9Instances33_2E1868FF91A815585B124C0140A60DCBLL_WZ', symObjAddr: 0x0, symBinAddr: 0xEA88, symSize: 0x14 }
  - { offsetInCU: 0x830, offset: 0x76D5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13DevicePolling_pWOb', symObjAddr: 0x80C, symBinAddr: 0xF254, symSize: 0x18 }
  - { offsetInCU: 0xBD1, offset: 0x7A76, size: 0x8, addend: 0x0, symName: '_$sSo27FBSDKGraphRequestConnecting_pSgypSgs5Error_pSgIeggng_AByXlSgSo7NSErrorCSgIeyByyy_TR', symObjAddr: 0x2474, symBinAddr: 0x10EBC, symSize: 0xBC }
  - { offsetInCU: 0xE2F, offset: 0x7CD4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerCfETo', symObjAddr: 0x4EBC, symBinAddr: 0x13904, symSize: 0x94 }
  - { offsetInCU: 0xE7A, offset: 0x7D1F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC10netService_13didNotPublishySo05NSNetG0C_SDySSSo8NSNumberCGtFTo', symObjAddr: 0x4F54, symBinAddr: 0x1399C, symSize: 0x50 }
  - { offsetInCU: 0xF01, offset: 0x7DA6, size: 0x8, addend: 0x0, symName: '_$sSaySSGMa', symObjAddr: 0x535C, symBinAddr: 0x13DA4, symSize: 0x54 }
  - { offsetInCU: 0xF3D, offset: 0x7DE2, size: 0x8, addend: 0x0, symName: '_$ss13_parseInteger5ascii5radixq_Sgx_SitSyRzs010FixedWidthB0R_r0_lFSS_SiTg5', symObjAddr: 0x53B0, symBinAddr: 0x13DF8, symSize: 0xE4 }
  - { offsetInCU: 0xFEC, offset: 0x7E91, size: 0x8, addend: 0x0, symName: '_$ss13_parseInteger5ascii5radixq_Sgx_SitSyRzs010FixedWidthB0R_r0_lFADSRys5UInt8VGXEfU_SS_SiTg5', symObjAddr: 0x5494, symBinAddr: 0x13EDC, symSize: 0x284 }
  - { offsetInCU: 0x1175, offset: 0x801A, size: 0x8, addend: 0x0, symName: '_$ss32_copyCollectionToContiguousArrayys0dE0Vy7ElementQzGxSlRzlFSs8UTF8ViewV_Tgq5', symObjAddr: 0x58F0, symBinAddr: 0x14338, symSize: 0xC0 }
  - { offsetInCU: 0x11C3, offset: 0x8068, size: 0x8, addend: 0x0, symName: '_$ss22_ContiguousArrayBufferV19_uninitializedCount15minimumCapacityAByxGSi_SitcfCs5UInt8V_Tgmq5', symObjAddr: 0x5AA0, symBinAddr: 0x144E8, symSize: 0x6C }
  - { offsetInCU: 0x1325, offset: 0x81CA, size: 0x8, addend: 0x0, symName: '_$sSS11withCStringyxxSPys4Int8VGKXEKlFSb_Tg5024$sSdySdSgxcSyRzlufcSbSpyf6GXEfU_j5SPys4C7VGXEfU_SpySdGTf1cn_n', symObjAddr: 0x5E00, symBinAddr: 0x14848, symSize: 0x10C }
  - { offsetInCU: 0x1481, offset: 0x8326, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC5startyyFySo27FBSDKGraphRequestConnecting_pSg_ypSgs5Error_pSgtcfU_TA', symObjAddr: 0x5F44, symBinAddr: 0x14958, symSize: 0xC }
  - { offsetInCU: 0x1494, offset: 0x8339, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x5F50, symBinAddr: 0x14964, symSize: 0x10 }
  - { offsetInCU: 0x14A7, offset: 0x834C, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x5F60, symBinAddr: 0x14974, symSize: 0x8 }
  - { offsetInCU: 0x14BA, offset: 0x835F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesVWOh', symObjAddr: 0x5F68, symBinAddr: 0x1497C, symSize: 0x2C }
  - { offsetInCU: 0x14CD, offset: 0x8372, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13DevicePolling_pWOc', symObjAddr: 0x5FD0, symBinAddr: 0x149A8, symSize: 0x44 }
  - { offsetInCU: 0x14E0, offset: 0x8385, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC12schedulePoll8intervalySu_tFyycfU_TA', symObjAddr: 0x6088, symBinAddr: 0x14A3C, symSize: 0xC }
  - { offsetInCU: 0x1642, offset: 0x84E7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC14notifyDelegate5token14expirationDate020dataAccessExpirationJ0ySSSg_10Foundation0J0VSgALtFyAA0cdE6ResultCcfU_TA', symObjAddr: 0x68D4, symBinAddr: 0x15288, symSize: 0x8 }
  - { offsetInCU: 0x166A, offset: 0x850F, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DateVSgWOb', symObjAddr: 0x6A00, symBinAddr: 0x153B4, symSize: 0x48 }
  - { offsetInCU: 0x167D, offset: 0x8522, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC14notifyDelegate5token14expirationDate020dataAccessExpirationJ0ySSSg_10Foundation0J0VSgALtFySo27FBSDKGraphRequestConnecting_pSg_ypSgs5Error_pSgtcfU0_TA', symObjAddr: 0x6A48, symBinAddr: 0x153FC, symSize: 0xA0 }
  - { offsetInCU: 0x16AC, offset: 0x8551, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerCMU', symObjAddr: 0x6CDC, symBinAddr: 0x15690, symSize: 0x8 }
  - { offsetInCU: 0x16BF, offset: 0x8564, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerCMa', symObjAddr: 0x6CE4, symBinAddr: 0x15698, symSize: 0x3C }
  - { offsetInCU: 0x16D2, offset: 0x8577, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerCMr', symObjAddr: 0x6D20, symBinAddr: 0x156D4, symSize: 0xA4 }
  - { offsetInCU: 0x16E5, offset: 0x858A, size: 0x8, addend: 0x0, symName: '_$s10Foundation3URLVSgMa', symObjAddr: 0x6DD8, symBinAddr: 0x1578C, symSize: 0x54 }
  - { offsetInCU: 0x16F8, offset: 0x859D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesVwCP', symObjAddr: 0x6E2C, symBinAddr: 0x157E0, symSize: 0x30 }
  - { offsetInCU: 0x170B, offset: 0x85B0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesVwxx', symObjAddr: 0x6E5C, symBinAddr: 0x15810, symSize: 0x3C }
  - { offsetInCU: 0x171E, offset: 0x85C3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesVwcp', symObjAddr: 0x6E98, symBinAddr: 0x1584C, symSize: 0x6C }
  - { offsetInCU: 0x1731, offset: 0x85D6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesVwca', symObjAddr: 0x6F04, symBinAddr: 0x158B8, symSize: 0x90 }
  - { offsetInCU: 0x1744, offset: 0x85E9, size: 0x8, addend: 0x0, symName: ___swift_assign_boxed_opaque_existential_1, symObjAddr: 0x6F94, symBinAddr: 0x15948, symSize: 0x168 }
  - { offsetInCU: 0x1757, offset: 0x85FC, size: 0x8, addend: 0x0, symName: ___swift_memcpy72_8, symObjAddr: 0x70FC, symBinAddr: 0x15AB0, symSize: 0x24 }
  - { offsetInCU: 0x176A, offset: 0x860F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesVwta', symObjAddr: 0x7120, symBinAddr: 0x15AD4, symSize: 0x70 }
  - { offsetInCU: 0x177D, offset: 0x8622, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesVwet', symObjAddr: 0x7190, symBinAddr: 0x15B44, symSize: 0x48 }
  - { offsetInCU: 0x1790, offset: 0x8635, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesVwst', symObjAddr: 0x71D8, symBinAddr: 0x15B8C, symSize: 0x50 }
  - { offsetInCU: 0x17A3, offset: 0x8648, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesVMa', symObjAddr: 0x7228, symBinAddr: 0x15BDC, symSize: 0x10 }
  - { offsetInCU: 0x17B6, offset: 0x865B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26DeviceLoginManagerDelegate_pSgXwWOh', symObjAddr: 0x7238, symBinAddr: 0x15BEC, symSize: 0x24 }
  - { offsetInCU: 0x17C9, offset: 0x866E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC12schedulePoll8intervalySu_tFyycfU_ySo27FBSDKGraphRequestConnecting_pSg_ypSgs5Error_pSgtcfU_TA', symObjAddr: 0x7350, symBinAddr: 0x15CF4, symSize: 0xC }
  - { offsetInCU: 0x17DC, offset: 0x8681, size: 0x8, addend: 0x0, symName: '_$sSdySdSgxcSyRzlufcSbSpySdGXEfU_SbSPys4Int8VGXEfU_TA', symObjAddr: 0x7394, symBinAddr: 0x15D38, symSize: 0x6C }
  - { offsetInCU: 0x1ACE, offset: 0x8973, size: 0x8, addend: 0x0, symName: '_$ss17FixedWidthIntegerPsEyxSgSScfCSi_Tgm5', symObjAddr: 0x4A28, symBinAddr: 0x13470, symSize: 0x3B8 }
  - { offsetInCU: 0x2199, offset: 0x903E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC8delegateAA0cdE8Delegate_pSgvg', symObjAddr: 0x5C, symBinAddr: 0xEAE4, symSize: 0x48 }
  - { offsetInCU: 0x21DE, offset: 0x9083, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC8delegateAA0cdE8Delegate_pSgvs', symObjAddr: 0xF4, symBinAddr: 0xEB7C, symSize: 0x58 }
  - { offsetInCU: 0x2204, offset: 0x90A9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC8delegateAA0cdE8Delegate_pSgvM', symObjAddr: 0x14C, symBinAddr: 0xEBD4, symSize: 0x70 }
  - { offsetInCU: 0x2227, offset: 0x90CC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC8delegateAA0cdE8Delegate_pSgvM.resume.0', symObjAddr: 0x1BC, symBinAddr: 0xEC44, symSize: 0x6C }
  - { offsetInCU: 0x2258, offset: 0x90FD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC11permissionsSaySSGvg', symObjAddr: 0x270, symBinAddr: 0xECF8, symSize: 0x10 }
  - { offsetInCU: 0x2285, offset: 0x912A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC11redirectURL10Foundation0G0VSgvg', symObjAddr: 0x354, symBinAddr: 0xEDDC, symSize: 0x58 }
  - { offsetInCU: 0x22C4, offset: 0x9169, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC11redirectURL10Foundation0G0VSgvs', symObjAddr: 0x4E8, symBinAddr: 0xEF30, symSize: 0x60 }
  - { offsetInCU: 0x22EA, offset: 0x918F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC11redirectURL10Foundation0G0VSgvM', symObjAddr: 0x548, symBinAddr: 0xEF90, symSize: 0x44 }
  - { offsetInCU: 0x230D, offset: 0x91B2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC11redirectURL10Foundation0G0VSgvM.resume.0', symObjAddr: 0x58C, symBinAddr: 0xEFD4, symSize: 0x4 }
  - { offsetInCU: 0x233E, offset: 0x91E3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC8codeInfoAA0cd4CodeG0CSgvg', symObjAddr: 0x5D8, symBinAddr: 0xF020, symSize: 0x50 }
  - { offsetInCU: 0x237D, offset: 0x9222, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC8codeInfoAA0cd4CodeG0CSgvs', symObjAddr: 0x68C, symBinAddr: 0xF0D4, symSize: 0x50 }
  - { offsetInCU: 0x23A3, offset: 0x9248, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC8codeInfoAA0cd4CodeG0CSgvM', symObjAddr: 0x6DC, symBinAddr: 0xF124, symSize: 0x44 }
  - { offsetInCU: 0x23C6, offset: 0x926B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC22configuredDependenciesAC06ObjectG0VSgvM', symObjAddr: 0x77C, symBinAddr: 0xF1C4, symSize: 0x44 }
  - { offsetInCU: 0x23E9, offset: 0x928E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV12devicePoller12errorFactory012graphRequestK015internalUtility8settingsAeA0C7Polling_p_So18FBSDKErrorCreating_pSo010FBSDKGraphmK0_pSo013FBSDKInternalO0_p09FBSDKCoreB016SettingsProtocol_ptcfC', symObjAddr: 0x7C0, symBinAddr: 0xF208, symSize: 0x4C }
  - { offsetInCU: 0x23FC, offset: 0x92A1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC19defaultDependenciesAC06ObjectG0VSgvM', symObjAddr: 0x8EC, symBinAddr: 0xF334, symSize: 0x44 }
  - { offsetInCU: 0x2425, offset: 0x92CA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC11permissions011enableSmartD0ACSaySSG_SbtcfC', symObjAddr: 0x930, symBinAddr: 0xF378, symSize: 0x40 }
  - { offsetInCU: 0x245C, offset: 0x9301, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC11permissions011enableSmartD0ACSaySSG_Sbtcfc', symObjAddr: 0x970, symBinAddr: 0xF3B8, symSize: 0x1B0 }
  - { offsetInCU: 0x2592, offset: 0x9437, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC5startyyF', symObjAddr: 0xB58, symBinAddr: 0xF5A0, symSize: 0x5C8 }
  - { offsetInCU: 0x27F6, offset: 0x969B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC5startyyFySo27FBSDKGraphRequestConnecting_pSg_ypSgs5Error_pSgtcfU_', symObjAddr: 0x1120, symBinAddr: 0xFB68, symSize: 0xC0C }
  - { offsetInCU: 0x2CC5, offset: 0x9B6A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC12processErroryys0G0_pF', symObjAddr: 0x1D2C, symBinAddr: 0x10774, symSize: 0x2F8 }
  - { offsetInCU: 0x2E38, offset: 0x9CDD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC14notifyDelegate33_2E1868FF91A815585B124C0140A60DCBLL5errorys5Error_p_tF', symObjAddr: 0x2080, symBinAddr: 0x10AC8, symSize: 0x238 }
  - { offsetInCU: 0x2FA3, offset: 0x9E48, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC12schedulePoll8intervalySu_tF', symObjAddr: 0x22B8, symBinAddr: 0x10D00, symSize: 0x1BC }
  - { offsetInCU: 0x3061, offset: 0x9F06, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC12schedulePoll8intervalySu_tFyycfU_', symObjAddr: 0x3BDC, symBinAddr: 0x12624, symSize: 0x360 }
  - { offsetInCU: 0x31A5, offset: 0xA04A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC12schedulePoll8intervalySu_tFyycfU_ySo27FBSDKGraphRequestConnecting_pSg_ypSgs5Error_pSgtcfU_', symObjAddr: 0x3F3C, symBinAddr: 0x12984, symSize: 0xAEC }
  - { offsetInCU: 0x3665, offset: 0xA50A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC6cancelyyF', symObjAddr: 0x2558, symBinAddr: 0x10FA0, symSize: 0x1D4 }
  - { offsetInCU: 0x3804, offset: 0xA6A9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC14notifyDelegate5token14expirationDate020dataAccessExpirationJ0ySSSg_10Foundation0J0VSgALtF', symObjAddr: 0x2754, symBinAddr: 0x1119C, symSize: 0x734 }
  - { offsetInCU: 0x3BFA, offset: 0xAA9F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC14notifyDelegate5token14expirationDate020dataAccessExpirationJ0ySSSg_10Foundation0J0VSgALtFySo27FBSDKGraphRequestConnecting_pSg_ypSgs5Error_pSgtcfU0_', symObjAddr: 0x2E88, symBinAddr: 0x118D0, symSize: 0xB78 }
  - { offsetInCU: 0x41BD, offset: 0xB062, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC14notifyDelegate5token14expirationDate020dataAccessExpirationJ0ySSSg_10Foundation0J0VSgALtFyAA0cdE6ResultCcfU_Tf4nnd_n', symObjAddr: 0x6390, symBinAddr: 0x14D44, symSize: 0x158 }
  - { offsetInCU: 0x4398, offset: 0xB23D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerCACycfC', symObjAddr: 0x4E10, symBinAddr: 0x13858, symSize: 0x20 }
  - { offsetInCU: 0x43AB, offset: 0xB250, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerCACycfc', symObjAddr: 0x4E30, symBinAddr: 0x13878, symSize: 0x2C }
  - { offsetInCU: 0x43FF, offset: 0xB2A4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerCfD', symObjAddr: 0x4E88, symBinAddr: 0x138D0, symSize: 0x34 }
  - { offsetInCU: 0x4420, offset: 0xB2C5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC10netService_13didNotPublishySo05NSNetG0C_SDySSSo8NSNumberCGtF', symObjAddr: 0x4F50, symBinAddr: 0x13998, symSize: 0x4 }
  - { offsetInCU: 0x443A, offset: 0xB2DF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV12devicePollerAA0C7Polling_pvg', symObjAddr: 0x4FA4, symBinAddr: 0x139EC, symSize: 0xC }
  - { offsetInCU: 0x444D, offset: 0xB2F2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV12devicePollerAA0C7Polling_pvs', symObjAddr: 0x4FB0, symBinAddr: 0x139F8, symSize: 0x2C }
  - { offsetInCU: 0x4460, offset: 0xB305, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV12devicePollerAA0C7Polling_pvM', symObjAddr: 0x4FDC, symBinAddr: 0x13A24, symSize: 0x10 }
  - { offsetInCU: 0x4473, offset: 0xB318, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV12devicePollerAA0C7Polling_pvM.resume.0', symObjAddr: 0x4FEC, symBinAddr: 0x13A34, symSize: 0x4 }
  - { offsetInCU: 0x4486, offset: 0xB32B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV12errorFactorySo18FBSDKErrorCreating_pvg', symObjAddr: 0x4FF0, symBinAddr: 0x13A38, symSize: 0x8 }
  - { offsetInCU: 0x4499, offset: 0xB33E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV12errorFactorySo18FBSDKErrorCreating_pvs', symObjAddr: 0x4FF8, symBinAddr: 0x13A40, symSize: 0x28 }
  - { offsetInCU: 0x44AC, offset: 0xB351, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV12errorFactorySo18FBSDKErrorCreating_pvM', symObjAddr: 0x5020, symBinAddr: 0x13A68, symSize: 0x10 }
  - { offsetInCU: 0x44BF, offset: 0xB364, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV12errorFactorySo18FBSDKErrorCreating_pvM.resume.0', symObjAddr: 0x5030, symBinAddr: 0x13A78, symSize: 0x4 }
  - { offsetInCU: 0x44D2, offset: 0xB377, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV19graphRequestFactorySo010FBSDKGraphiJ0_pvg', symObjAddr: 0x5034, symBinAddr: 0x13A7C, symSize: 0x8 }
  - { offsetInCU: 0x44E5, offset: 0xB38A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV19graphRequestFactorySo010FBSDKGraphiJ0_pvs', symObjAddr: 0x503C, symBinAddr: 0x13A84, symSize: 0x28 }
  - { offsetInCU: 0x44F8, offset: 0xB39D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV19graphRequestFactorySo010FBSDKGraphiJ0_pvM', symObjAddr: 0x5064, symBinAddr: 0x13AAC, symSize: 0x10 }
  - { offsetInCU: 0x450B, offset: 0xB3B0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV19graphRequestFactorySo010FBSDKGraphiJ0_pvM.resume.0', symObjAddr: 0x5074, symBinAddr: 0x13ABC, symSize: 0x4 }
  - { offsetInCU: 0x451E, offset: 0xB3C3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV15internalUtilitySo013FBSDKInternalI0_pvg', symObjAddr: 0x5078, symBinAddr: 0x13AC0, symSize: 0x8 }
  - { offsetInCU: 0x4531, offset: 0xB3D6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV15internalUtilitySo013FBSDKInternalI0_pvs', symObjAddr: 0x5080, symBinAddr: 0x13AC8, symSize: 0x28 }
  - { offsetInCU: 0x4544, offset: 0xB3E9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV15internalUtilitySo013FBSDKInternalI0_pvM', symObjAddr: 0x50A8, symBinAddr: 0x13AF0, symSize: 0x10 }
  - { offsetInCU: 0x4557, offset: 0xB3FC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV15internalUtilitySo013FBSDKInternalI0_pvM.resume.0', symObjAddr: 0x50B8, symBinAddr: 0x13B00, symSize: 0x4 }
  - { offsetInCU: 0x456A, offset: 0xB40F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV8settings09FBSDKCoreB016SettingsProtocol_pvg', symObjAddr: 0x50BC, symBinAddr: 0x13B04, symSize: 0x8 }
  - { offsetInCU: 0x457D, offset: 0xB422, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV8settings09FBSDKCoreB016SettingsProtocol_pvs', symObjAddr: 0x50C4, symBinAddr: 0x13B0C, symSize: 0x28 }
  - { offsetInCU: 0x4590, offset: 0xB435, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV8settings09FBSDKCoreB016SettingsProtocol_pvM', symObjAddr: 0x50EC, symBinAddr: 0x13B34, symSize: 0x10 }
  - { offsetInCU: 0x45A3, offset: 0xB448, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC18ObjectDependenciesV8settings09FBSDKCoreB016SettingsProtocol_pvM.resume.0', symObjAddr: 0x50FC, symBinAddr: 0x13B44, symSize: 0x4 }
  - { offsetInCU: 0x45FC, offset: 0xB4A1, size: 0x8, addend: 0x0, symName: '_$sSa20_reserveCapacityImpl07minimumB013growForAppendySi_SbtF13FBSDKLoginKit18DeviceLoginManagerC_Tg5', symObjAddr: 0x5210, symBinAddr: 0x13C58, symSize: 0xBC }
  - { offsetInCU: 0x469C, offset: 0xB541, size: 0x8, addend: 0x0, symName: '_$sSa034_makeUniqueAndReserveCapacityIfNotB0yyF13FBSDKLoginKit18DeviceLoginManagerC_Tg5', symObjAddr: 0x52CC, symBinAddr: 0x13D14, symSize: 0x90 }
  - { offsetInCU: 0x472C, offset: 0xB5D1, size: 0x8, addend: 0x0, symName: '_$sSS8_copyingyS2SFZ', symObjAddr: 0x5718, symBinAddr: 0x14160, symSize: 0x8C }
  - { offsetInCU: 0x4740, offset: 0xB5E5, size: 0x8, addend: 0x0, symName: '_$sSlsEy11SubSequenceQzqd__cSXRd__5BoundQyd__5IndexRtzluigSS_s16PartialRangeFromVySSAEVGTgq5', symObjAddr: 0x57A4, symBinAddr: 0x141EC, symSize: 0x4C }
  - { offsetInCU: 0x4768, offset: 0xB60D, size: 0x8, addend: 0x0, symName: '_$sSS8_copyingySSSsFZ', symObjAddr: 0x57F0, symBinAddr: 0x14238, symSize: 0x100 }
  - { offsetInCU: 0x47A6, offset: 0xB64B, size: 0x8, addend: 0x0, symName: '_$sSlsE5countSivgSs8UTF8ViewV_Tgq5', symObjAddr: 0x59B0, symBinAddr: 0x143F8, symSize: 0xF0 }
  - { offsetInCU: 0x47CB, offset: 0xB670, size: 0x8, addend: 0x0, symName: '_$sSTsE21_copySequenceContents12initializing8IteratorQz_SitSry7ElementQzG_tFSs8UTF8ViewV_Tgq5', symObjAddr: 0x5B0C, symBinAddr: 0x14554, symSize: 0x214 }
  - { offsetInCU: 0x47FD, offset: 0xB6A2, size: 0x8, addend: 0x0, symName: '_$ss11_StringGutsV27_slowEnsureMatchingEncodingySS5IndexVAEF', symObjAddr: 0x5D20, symBinAddr: 0x14768, symSize: 0x78 }
  - { offsetInCU: 0x4811, offset: 0xB6B6, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV20_consumeAndCreateNewAByxGyF13FBSDKLoginKit18DeviceLoginManagerC_Tg5', symObjAddr: 0x5D98, symBinAddr: 0x147E0, symSize: 0x68 }
  - { offsetInCU: 0x48B2, offset: 0xB757, size: 0x8, addend: 0x0, symName: '_$ss20_ArrayBufferProtocolPsE15replaceSubrange_4with10elementsOfySnySiG_Siqd__ntSlRd__7ElementQyd__AGRtzlFs01_aB0Vy13FBSDKLoginKit18DeviceLoginManagerCG_s15EmptyCollectionVyANGTg5Tf4nndn_n', symObjAddr: 0x6094, symBinAddr: 0x14A48, symSize: 0x1FC }
  - { offsetInCU: 0x4B4D, offset: 0xB9F2, size: 0x8, addend: 0x0, symName: '_$sSa15replaceSubrange_4withySnySiG_qd__nt7ElementQyd__RszSlRd__lF13FBSDKLoginKit18DeviceLoginManagerC_s15EmptyCollectionVyAHGTg5Tf4ndn_n', symObjAddr: 0x6290, symBinAddr: 0x14C44, symSize: 0x100 }
  - { offsetInCU: 0x4C52, offset: 0xBAF7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18DeviceLoginManagerC10netService_13didNotPublishySo05NSNetG0C_SDySSSo8NSNumberCGtFTf4ndn_n', symObjAddr: 0x6AE8, symBinAddr: 0x1549C, symSize: 0x14C }
  - { offsetInCU: 0x27, offset: 0xBC8B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultC5token11isCancelledACSo16FBSDKAccessTokenCSg_SbtcfC', symObjAddr: 0x0, symBinAddr: 0x15DC4, symSize: 0xC8 }
  - { offsetInCU: 0x9B, offset: 0xBCFF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultC11accessTokenSo011FBSDKAccessH0CSgvgTo', symObjAddr: 0xC8, symBinAddr: 0x15E8C, symSize: 0x48 }
  - { offsetInCU: 0xEF, offset: 0xBD53, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultC11accessTokenSo011FBSDKAccessH0CSgvsTo', symObjAddr: 0x160, symBinAddr: 0x15F24, symSize: 0x64 }
  - { offsetInCU: 0x145, offset: 0xBDA9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultC11isCancelledSbvgTo', symObjAddr: 0x1C4, symBinAddr: 0x15F88, symSize: 0x44 }
  - { offsetInCU: 0x199, offset: 0xBDFD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultC11isCancelledSbvsTo', symObjAddr: 0x24C, symBinAddr: 0x16010, symSize: 0x48 }
  - { offsetInCU: 0x1D3, offset: 0xBE37, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultC5token11isCancelledACSo16FBSDKAccessTokenCSg_SbtcfcTo', symObjAddr: 0x35C, symBinAddr: 0x16120, symSize: 0xCC }
  - { offsetInCU: 0x239, offset: 0xBE9D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultCACycfcTo', symObjAddr: 0x474, symBinAddr: 0x16238, symSize: 0x2C }
  - { offsetInCU: 0x2C6, offset: 0xBF2A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultCfETo', symObjAddr: 0x4D4, symBinAddr: 0x16298, symSize: 0x10 }
  - { offsetInCU: 0x2F3, offset: 0xBF57, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultCMa', symObjAddr: 0x4E4, symBinAddr: 0x162A8, symSize: 0x20 }
  - { offsetInCU: 0x405, offset: 0xC069, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultC5token11isCancelledACSo16FBSDKAccessTokenCSg_SbtcfC', symObjAddr: 0x0, symBinAddr: 0x15DC4, symSize: 0xC8 }
  - { offsetInCU: 0x44A, offset: 0xC0AE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultC11accessTokenSo011FBSDKAccessH0CSgvg', symObjAddr: 0x110, symBinAddr: 0x15ED4, symSize: 0x50 }
  - { offsetInCU: 0x49C, offset: 0xC100, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultC11isCancelledSbvg', symObjAddr: 0x208, symBinAddr: 0x15FCC, symSize: 0x44 }
  - { offsetInCU: 0x4D6, offset: 0xC13A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultC5token11isCancelledACSo16FBSDKAccessTokenCSg_Sbtcfc', symObjAddr: 0x294, symBinAddr: 0x16058, symSize: 0xC8 }
  - { offsetInCU: 0x502, offset: 0xC166, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultCACycfC', symObjAddr: 0x428, symBinAddr: 0x161EC, symSize: 0x20 }
  - { offsetInCU: 0x515, offset: 0xC179, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultCACycfc', symObjAddr: 0x448, symBinAddr: 0x1620C, symSize: 0x2C }
  - { offsetInCU: 0x569, offset: 0xC1CD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24DeviceLoginManagerResultCfD', symObjAddr: 0x4A0, symBinAddr: 0x16264, symSize: 0x34 }
  - { offsetInCU: 0x75, offset: 0xC273, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12DevicePollerVAA0C7PollingA2aDP8schedule8interval5blockySu_yyctFTW', symObjAddr: 0x8, symBinAddr: 0x162E4, symSize: 0x4 }
  - { offsetInCU: 0x94, offset: 0xC292, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12DevicePollerVAA0C7PollingA2aDP8schedule8interval5blockySu_yyctFTW', symObjAddr: 0x8, symBinAddr: 0x162E4, symSize: 0x4 }
  - { offsetInCU: 0xA5, offset: 0xC2A3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12DevicePollerV8schedule8interval5blockySu_yyctFTf4nnd_n', symObjAddr: 0xC, symBinAddr: 0x162E8, symSize: 0x2A8 }
  - { offsetInCU: 0x138, offset: 0xC336, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12DevicePollerVMa', symObjAddr: 0x2B4, symBinAddr: 0x16590, symSize: 0x10 }
  - { offsetInCU: 0x14B, offset: 0xC349, size: 0x8, addend: 0x0, symName: '_$sSo17OS_dispatch_queueCMa', symObjAddr: 0x2C4, symBinAddr: 0x165A0, symSize: 0x3C }
  - { offsetInCU: 0x15E, offset: 0xC35C, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x300, symBinAddr: 0x165DC, symSize: 0x10 }
  - { offsetInCU: 0x171, offset: 0xC36F, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x310, symBinAddr: 0x165EC, symSize: 0x8 }
  - { offsetInCU: 0x184, offset: 0xC382, size: 0x8, addend: 0x0, symName: '_$sSay8Dispatch0A13WorkItemFlagsVGMa', symObjAddr: 0x398, symBinAddr: 0x16634, symSize: 0x54 }
  - { offsetInCU: 0x240, offset: 0xC43E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12DevicePollerVACycfC', symObjAddr: 0x0, symBinAddr: 0x162DC, symSize: 0x4 }
  - { offsetInCU: 0x264, offset: 0xC462, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12DevicePollerV8schedule8interval5blockySu_yyctF', symObjAddr: 0x4, symBinAddr: 0x162E0, symSize: 0x4 }
  - { offsetInCU: 0x2B, offset: 0xC4F6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperO03getC4InfoSSyFZ', symObjAddr: 0x0, symBinAddr: 0x16690, symSize: 0x4 }
  - { offsetInCU: 0x4D, offset: 0xC518, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperO25mdnsAdvertisementServicesSo10NSMapTableCySo20NSNetServiceDelegate_pyXlGvpZ', symObjAddr: 0x4E10, symBinAddr: 0x644C8, symSize: 0x0 }
  - { offsetInCU: 0x67, offset: 0xC532, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperO16NetServiceValues33_D51BDA1F8F5A5E4604761D8AFA9F40BBLLO10sdkVersionSSvpZ', symObjAddr: 0xA80, symBinAddr: 0x61F38, symSize: 0x0 }
  - { offsetInCU: 0x121, offset: 0xC5EC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperO16NetServiceValues33_D51BDA1F8F5A5E4604761D8AFA9F40BBLLO10sdkVersion_WZ', symObjAddr: 0x180, symBinAddr: 0x16810, symSize: 0x144 }
  - { offsetInCU: 0x16D, offset: 0xC638, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperO27cleanUpAdvertisementService3forySo05NSNetI8Delegate_p_tFZTf4en_n', symObjAddr: 0x304, symBinAddr: 0x16994, symSize: 0xF4 }
  - { offsetInCU: 0x1E7, offset: 0xC6B2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperO25startAdvertisementService9loginCode8delegateSbSS_So05NSNetH8Delegate_ptFZTf4nen_n', symObjAddr: 0x3F8, symBinAddr: 0x16A88, symSize: 0x240 }
  - { offsetInCU: 0x375, offset: 0xC840, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperO10isDelegate_23forAdvertisementServiceSbSo05NSNetjG0_p_So0kJ0CtFZTf4enn_n', symObjAddr: 0x638, symBinAddr: 0x16CC8, symSize: 0xC8 }
  - { offsetInCU: 0x3F1, offset: 0xC8BC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperO03getC4InfoSSyFZTf4d_n', symObjAddr: 0x700, symBinAddr: 0x16D90, symSize: 0x2A0 }
  - { offsetInCU: 0x61C, offset: 0xCAE7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperO25mdnsAdvertisementServices_WZ', symObjAddr: 0xA0, symBinAddr: 0x16730, symSize: 0x38 }
  - { offsetInCU: 0x636, offset: 0xCB01, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperO25mdnsAdvertisementServicesSo10NSMapTableCySo20NSNetServiceDelegate_pyXlGvau', symObjAddr: 0xD8, symBinAddr: 0x16768, symSize: 0x40 }
  - { offsetInCU: 0x6E6, offset: 0xCBB1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperOMa', symObjAddr: 0x9A0, symBinAddr: 0x17030, symSize: 0x10 }
  - { offsetInCU: 0x6F9, offset: 0xCBC4, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataVACSTAAWl', symObjAddr: 0x9F4, symBinAddr: 0x17040, symSize: 0x44 }
  - { offsetInCU: 0x89B, offset: 0xCD66, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperO03getC4InfoSSyFZ', symObjAddr: 0x0, symBinAddr: 0x16690, symSize: 0x4 }
  - { offsetInCU: 0x8AE, offset: 0xCD79, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperO25startAdvertisementService9loginCode8delegateSbSS_So05NSNetH8Delegate_ptFZ', symObjAddr: 0x4, symBinAddr: 0x16694, symSize: 0x44 }
  - { offsetInCU: 0x8C7, offset: 0xCD92, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperO27cleanUpAdvertisementService3forySo05NSNetI8Delegate_p_tFZ', symObjAddr: 0x48, symBinAddr: 0x166D8, symSize: 0x28 }
  - { offsetInCU: 0x8DA, offset: 0xCDA5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperO10isDelegate_23forAdvertisementServiceSbSo05NSNetjG0_p_So0kJ0CtFZ', symObjAddr: 0x70, symBinAddr: 0x16700, symSize: 0x30 }
  - { offsetInCU: 0x8F3, offset: 0xCDBE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit20DeviceRequestsHelperO25mdnsAdvertisementServicesSo10NSMapTableCySo20NSNetServiceDelegate_pyXlGvgZ', symObjAddr: 0x118, symBinAddr: 0x167A8, symSize: 0x68 }
  - { offsetInCU: 0x923, offset: 0xCDEE, size: 0x8, addend: 0x0, symName: '_$sSlsE5first7ElementQzSgvgSS_Tg5', symObjAddr: 0x2C4, symBinAddr: 0x16954, symSize: 0x40 }
  - { offsetInCU: 0x62, offset: 0xCF4F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15defaultAudienceAA07DefaultF0OvgTo', symObjAddr: 0x0, symBinAddr: 0x17084, symSize: 0x9C }
  - { offsetInCU: 0xAF, offset: 0xCF9C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15defaultAudienceAA07DefaultF0OvsTo', symObjAddr: 0x13C, symBinAddr: 0x1719C, symSize: 0xA8 }
  - { offsetInCU: 0x127, offset: 0xD014, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC8delegateAA05LoginD8Delegate_pSgvgTo', symObjAddr: 0x4B0, symBinAddr: 0x17510, symSize: 0x48 }
  - { offsetInCU: 0x17B, offset: 0xD068, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC8delegateAA05LoginD8Delegate_pSgvsTo', symObjAddr: 0x540, symBinAddr: 0x175A0, symSize: 0x50 }
  - { offsetInCU: 0x1FB, offset: 0xD0E8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC11permissionsSaySSGvgTo', symObjAddr: 0x6C4, symBinAddr: 0x17724, symSize: 0x68 }
  - { offsetInCU: 0x24F, offset: 0xD13C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC11permissionsSaySSGvsTo', symObjAddr: 0x740, symBinAddr: 0x177A0, symSize: 0x64 }
  - { offsetInCU: 0x305, offset: 0xD1F2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15TooltipBehaviorOSYAASY8rawValuexSg03RawH0Qz_tcfCTW', symObjAddr: 0x1C3C, symBinAddr: 0x18C18, symSize: 0x20 }
  - { offsetInCU: 0x334, offset: 0xD221, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15TooltipBehaviorOSYAASY8rawValue03RawH0QzvgTW', symObjAddr: 0x1C5C, symBinAddr: 0x18C38, symSize: 0xC }
  - { offsetInCU: 0x351, offset: 0xD23E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15tooltipBehaviorAC07TooltipF0OvgTo', symObjAddr: 0x800, symBinAddr: 0x17860, symSize: 0x44 }
  - { offsetInCU: 0x3A6, offset: 0xD293, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15tooltipBehaviorAC07TooltipF0OvsTo', symObjAddr: 0x888, symBinAddr: 0x178E8, symSize: 0x48 }
  - { offsetInCU: 0x411, offset: 0xD2FE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC17tooltipColorStyleAA13FBTooltipViewC0fG0OvgTo', symObjAddr: 0x95C, symBinAddr: 0x179BC, symSize: 0x44 }
  - { offsetInCU: 0x465, offset: 0xD352, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC17tooltipColorStyleAA13FBTooltipViewC0fG0OvsTo', symObjAddr: 0x9E4, symBinAddr: 0x17A44, symSize: 0x48 }
  - { offsetInCU: 0x4D0, offset: 0xD3BD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC13loginTrackingAA05LoginF0OvgTo', symObjAddr: 0xAB8, symBinAddr: 0x17B18, symSize: 0x44 }
  - { offsetInCU: 0x524, offset: 0xD411, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC13loginTrackingAA05LoginF0OvsTo', symObjAddr: 0xB40, symBinAddr: 0x17BA0, symSize: 0x48 }
  - { offsetInCU: 0x58F, offset: 0xD47C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5nonceSSSgvgTo', symObjAddr: 0xC14, symBinAddr: 0x17C74, symSize: 0x5C }
  - { offsetInCU: 0x5BF, offset: 0xD4AC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5nonceSSSgvsTo', symObjAddr: 0xCA8, symBinAddr: 0x17D08, symSize: 0x64 }
  - { offsetInCU: 0x648, offset: 0xD535, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC8authTypeSo0a4AuthF0aSgvgTo', symObjAddr: 0x10A8, symBinAddr: 0x18084, symSize: 0x48 }
  - { offsetInCU: 0x69C, offset: 0xD589, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC8authTypeSo0a4AuthF0aSgvsTo', symObjAddr: 0x1140, symBinAddr: 0x1811C, symSize: 0x64 }
  - { offsetInCU: 0x707, offset: 0xD5F4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC12codeVerifierAA04CodeF0CvgTo', symObjAddr: 0x1238, symBinAddr: 0x18214, symSize: 0x48 }
  - { offsetInCU: 0x75C, offset: 0xD649, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC12codeVerifierAA04CodeF0CvsTo', symObjAddr: 0x12C4, symBinAddr: 0x182A0, symSize: 0x64 }
  - { offsetInCU: 0x830, offset: 0xD71D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC19graphRequestFactorySo010FBSDKGraphfG0_pvgTo', symObjAddr: 0x18A4, symBinAddr: 0x18880, symSize: 0x48 }
  - { offsetInCU: 0x884, offset: 0xD771, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC19graphRequestFactorySo010FBSDKGraphfG0_pvsTo', symObjAddr: 0x1948, symBinAddr: 0x18924, symSize: 0x64 }
  - { offsetInCU: 0x8EF, offset: 0xD7DC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15isAuthenticatedSbvgTo', symObjAddr: 0x1A5C, symBinAddr: 0x18A38, symSize: 0xA8 }
  - { offsetInCU: 0x9AF, offset: 0xD89C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5frameACSo6CGRectV_tcfcTo', symObjAddr: 0x23A8, symBinAddr: 0x19384, symSize: 0x20 }
  - { offsetInCU: 0x9FC, offset: 0xD8E9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5coderACSgSo7NSCoderC_tcfcTo', symObjAddr: 0x243C, symBinAddr: 0x19418, symSize: 0x3C }
  - { offsetInCU: 0xAF6, offset: 0xD9E3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15didMoveToWindowyyFTo', symObjAddr: 0x2BA4, symBinAddr: 0x19B80, symSize: 0x28 }
  - { offsetInCU: 0xB2B, offset: 0xDA18, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC9imageRect010forContentF0So6CGRectVAG_tFTo', symObjAddr: 0x2BF4, symBinAddr: 0x19BD0, symSize: 0x28 }
  - { offsetInCU: 0xB93, offset: 0xDA80, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC9titleRect010forContentF0So6CGRectVAG_tFTo', symObjAddr: 0x2D0C, symBinAddr: 0x19CE8, symSize: 0x110 }
  - { offsetInCU: 0xC97, offset: 0xDB84, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC14layoutSubviewsyyFTo', symObjAddr: 0x3200, symBinAddr: 0x1A1DC, symSize: 0x28 }
  - { offsetInCU: 0xCCC, offset: 0xDBB9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC12sizeThatFitsySo6CGSizeVAFFTo', symObjAddr: 0x371C, symBinAddr: 0x1A6F8, symSize: 0x54 }
  - { offsetInCU: 0xD1A, offset: 0xDC07, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC20accessTokenDidChangeyy10Foundation12NotificationVFTo', symObjAddr: 0x3A40, symBinAddr: 0x1AA1C, symSize: 0x94 }
  - { offsetInCU: 0xD51, offset: 0xDC3E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC16profileDidChangeyy10Foundation12NotificationVFTo', symObjAddr: 0x3D1C, symBinAddr: 0x1ACF8, symSize: 0x90 }
  - { offsetInCU: 0xDB6, offset: 0xDCA3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC13buttonPressedyyypFTo', symObjAddr: 0x47C4, symBinAddr: 0x1B7A0, symSize: 0x64 }
  - { offsetInCU: 0xDE8, offset: 0xDCD5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC22makeLoginConfigurationAA0fG0CSgyFTo', symObjAddr: 0x4B0C, symBinAddr: 0x1BAE8, symSize: 0x34 }
  - { offsetInCU: 0xE03, offset: 0xDCF0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC9logInUser33_2D5546723C2E9E390359F57C16888789LLyyFyAA11LoginResultOcfU_', symObjAddr: 0x4B40, symBinAddr: 0x1BB1C, symSize: 0x104 }
  - { offsetInCU: 0xE58, offset: 0xDD45, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC17initializeContentyyFTo', symObjAddr: 0x4E28, symBinAddr: 0x1BE04, symSize: 0x28 }
  - { offsetInCU: 0xE8A, offset: 0xDD77, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC27updateContentForAccessTokenyyFTo', symObjAddr: 0x5068, symBinAddr: 0x1C044, symSize: 0x28 }
  - { offsetInCU: 0xEA5, offset: 0xDD92, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC18fetchAndSetContentyyFTo', symObjAddr: 0x52F8, symBinAddr: 0x1C2D4, symSize: 0x28 }
  - { offsetInCU: 0xEDC, offset: 0xDDC9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC20updateContentForUseryy09FBSDKCoreB07ProfileCSgFTo', symObjAddr: 0x5460, symBinAddr: 0x1C43C, symSize: 0x54 }
  - { offsetInCU: 0xEF7, offset: 0xDDE4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC6logoutyyFTo', symObjAddr: 0x54B4, symBinAddr: 0x1C490, symSize: 0x28 }
  - { offsetInCU: 0xF42, offset: 0xDE2F, size: 0x8, addend: 0x0, symName: ___swift_mutable_project_boxed_opaque_existential_1, symObjAddr: 0x260, symBinAddr: 0x172C0, symSize: 0x28 }
  - { offsetInCU: 0xF55, offset: 0xDE42, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15defaultAudienceAA07DefaultF0OvpACTK', symObjAddr: 0x288, symBinAddr: 0x172E8, symSize: 0x84 }
  - { offsetInCU: 0xF8B, offset: 0xDE78, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15defaultAudienceAA07DefaultF0OvpACTk', symObjAddr: 0x30C, symBinAddr: 0x1736C, symSize: 0x80 }
  - { offsetInCU: 0x108F, offset: 0xDF7C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit29UserInterfaceElementProviding_pWOb', symObjAddr: 0x1658, symBinAddr: 0x18634, symSize: 0x18 }
  - { offsetInCU: 0x10A2, offset: 0xDF8F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28UserInterfaceStringProviding_pWOb', symObjAddr: 0x1788, symBinAddr: 0x18764, symSize: 0x18 }
  - { offsetInCU: 0x10B5, offset: 0xDFA2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14LoginProviding_pWOb', symObjAddr: 0x1848, symBinAddr: 0x18824, symSize: 0x18 }
  - { offsetInCU: 0x110C, offset: 0xDFF9, size: 0x8, addend: 0x0, symName: '_$sSo20FBSDKInternalUtilityCMa', symObjAddr: 0x1F30, symBinAddr: 0x18F0C, symSize: 0x3C }
  - { offsetInCU: 0x146A, offset: 0xE357, size: 0x8, addend: 0x0, symName: '_$sSo13UIAlertActionCIegg_ABIeyBy_TR', symObjAddr: 0x4C44, symBinAddr: 0x1BC20, symSize: 0x4C }
  - { offsetInCU: 0x14EF, offset: 0xE3DC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonCfETo', symObjAddr: 0x5510, symBinAddr: 0x1C4EC, symSize: 0xE8 }
  - { offsetInCU: 0x1667, offset: 0xE554, size: 0x8, addend: 0x0, symName: '_$sypSgWOb', symObjAddr: 0x5A70, symBinAddr: 0x1CA18, symSize: 0x48 }
  - { offsetInCU: 0x167A, offset: 0xE567, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC9logInUser33_2D5546723C2E9E390359F57C16888789LLyyFyAA11LoginResultOcfU_TA', symObjAddr: 0x5B58, symBinAddr: 0x1CA84, symSize: 0x8 }
  - { offsetInCU: 0x16DD, offset: 0xE5CA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC18fetchAndSetContentyyFySo27FBSDKGraphRequestConnecting_pSg_ypSgs5Error_pSgtcfU_TA', symObjAddr: 0x5CE0, symBinAddr: 0x1CC0C, symSize: 0x8 }
  - { offsetInCU: 0x16F0, offset: 0xE5DD, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x5CE8, symBinAddr: 0x1CC14, symSize: 0x10 }
  - { offsetInCU: 0x1703, offset: 0xE5F0, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x5CF8, symBinAddr: 0x1CC24, symSize: 0x8 }
  - { offsetInCU: 0x1716, offset: 0xE603, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15TooltipBehaviorOSHAASQWb', symObjAddr: 0x5D00, symBinAddr: 0x1CC2C, symSize: 0x4 }
  - { offsetInCU: 0x1729, offset: 0xE616, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15TooltipBehaviorOAESQAAWl', symObjAddr: 0x5D04, symBinAddr: 0x1CC30, symSize: 0x44 }
  - { offsetInCU: 0x173C, offset: 0xE629, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonCMa', symObjAddr: 0x5E50, symBinAddr: 0x1CD7C, symSize: 0x20 }
  - { offsetInCU: 0x174F, offset: 0xE63C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15TooltipBehaviorOMa', symObjAddr: 0x5E70, symBinAddr: 0x1CD9C, symSize: 0x10 }
  - { offsetInCU: 0x1762, offset: 0xE64F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit19LoginButtonDelegate_pSgXwWOh', symObjAddr: 0x5EA0, symBinAddr: 0x1CDCC, symSize: 0x24 }
  - { offsetInCU: 0x1775, offset: 0xE662, size: 0x8, addend: 0x0, symName: '_$sypSgWOc', symObjAddr: 0x5EC4, symBinAddr: 0x1CDF0, symSize: 0x48 }
  - { offsetInCU: 0x1788, offset: 0xE675, size: 0x8, addend: 0x0, symName: '_$s12FBSDKCoreKit10PermissionOACSHAAWl', symObjAddr: 0x5F0C, symBinAddr: 0x1CE38, symSize: 0x48 }
  - { offsetInCU: 0x179B, offset: 0xE688, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC26presentAlertViewController33_2D5546723C2E9E390359F57C16888789LLyyFySo13UIAlertActionCcfU_TA', symObjAddr: 0x5F54, symBinAddr: 0x1CE80, symSize: 0x8 }
  - { offsetInCU: 0x17AE, offset: 0xE69B, size: 0x8, addend: 0x0, symName: '_$sS2Ss7CVarArg10FoundationWl', symObjAddr: 0x5F5C, symBinAddr: 0x1CE88, symSize: 0x44 }
  - { offsetInCU: 0x17C1, offset: 0xE6AE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultOWOe', symObjAddr: 0x5FA0, symBinAddr: 0x1CECC, symSize: 0x48 }
  - { offsetInCU: 0x18C3, offset: 0xE7B0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15TooltipBehaviorOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x1B7C, symBinAddr: 0x18B58, symSize: 0x14 }
  - { offsetInCU: 0x1965, offset: 0xE852, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15TooltipBehaviorOSHAASH9hashValueSivgTW', symObjAddr: 0x1B90, symBinAddr: 0x18B6C, symSize: 0x44 }
  - { offsetInCU: 0x1A0C, offset: 0xE8F9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15TooltipBehaviorOSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0x1BD4, symBinAddr: 0x18BB0, symSize: 0x28 }
  - { offsetInCU: 0x1A5B, offset: 0xE948, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15TooltipBehaviorOSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0x1BFC, symBinAddr: 0x18BD8, symSize: 0x40 }
  - { offsetInCU: 0x1D0B, offset: 0xEBF8, size: 0x8, addend: 0x0, symName: '_$sShyShyxGqd__nc7ElementQyd__RszSTRd__lufC12FBSDKCoreKit10PermissionO_SayAFGTgm5Tf4g_n', symObjAddr: 0x5B60, symBinAddr: 0x1CA8C, symSize: 0xEC }
  - { offsetInCU: 0x1E3D, offset: 0xED2A, size: 0x8, addend: 0x0, symName: '_$sShyShyxGqd__nc7ElementQyd__RszSTRd__lufCSS_SaySSGTgm5Tf4g_n', symObjAddr: 0x5C4C, symBinAddr: 0x1CB78, symSize: 0x94 }
  - { offsetInCU: 0x21B2, offset: 0xF09F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15defaultAudienceAA07DefaultF0Ovg', symObjAddr: 0x9C, symBinAddr: 0x17120, symSize: 0x7C }
  - { offsetInCU: 0x21F7, offset: 0xF0E4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15defaultAudienceAA07DefaultF0Ovs', symObjAddr: 0x1E4, symBinAddr: 0x17244, symSize: 0x7C }
  - { offsetInCU: 0x221D, offset: 0xF10A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15defaultAudienceAA07DefaultF0OvM', symObjAddr: 0x38C, symBinAddr: 0x173EC, symSize: 0xA0 }
  - { offsetInCU: 0x2255, offset: 0xF142, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15defaultAudienceAA07DefaultF0OvM.resume.0', symObjAddr: 0x42C, symBinAddr: 0x1748C, symSize: 0x84 }
  - { offsetInCU: 0x2291, offset: 0xF17E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC8delegateAA05LoginD8Delegate_pSgvg', symObjAddr: 0x4F8, symBinAddr: 0x17558, symSize: 0x48 }
  - { offsetInCU: 0x22D0, offset: 0xF1BD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC8delegateAA05LoginD8Delegate_pSgvs', symObjAddr: 0x590, symBinAddr: 0x175F0, symSize: 0x58 }
  - { offsetInCU: 0x22F6, offset: 0xF1E3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC8delegateAA05LoginD8Delegate_pSgvM', symObjAddr: 0x5E8, symBinAddr: 0x17648, symSize: 0x70 }
  - { offsetInCU: 0x2319, offset: 0xF206, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC8delegateAA05LoginD8Delegate_pSgvM.resume.0', symObjAddr: 0x658, symBinAddr: 0x176B8, symSize: 0x6C }
  - { offsetInCU: 0x236C, offset: 0xF259, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC11permissionsSaySSGvM', symObjAddr: 0x7B8, symBinAddr: 0x17818, symSize: 0x44 }
  - { offsetInCU: 0x238F, offset: 0xF27C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC11permissionsSaySSGvM.resume.0', symObjAddr: 0x7FC, symBinAddr: 0x1785C, symSize: 0x4 }
  - { offsetInCU: 0x23C0, offset: 0xF2AD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15tooltipBehaviorAC07TooltipF0Ovg', symObjAddr: 0x844, symBinAddr: 0x178A4, symSize: 0x44 }
  - { offsetInCU: 0x23FF, offset: 0xF2EC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15tooltipBehaviorAC07TooltipF0Ovs', symObjAddr: 0x8D0, symBinAddr: 0x17930, symSize: 0x48 }
  - { offsetInCU: 0x2425, offset: 0xF312, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15tooltipBehaviorAC07TooltipF0OvM', symObjAddr: 0x918, symBinAddr: 0x17978, symSize: 0x44 }
  - { offsetInCU: 0x245A, offset: 0xF347, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC17tooltipColorStyleAA13FBTooltipViewC0fG0Ovg', symObjAddr: 0x9A0, symBinAddr: 0x17A00, symSize: 0x44 }
  - { offsetInCU: 0x2499, offset: 0xF386, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC17tooltipColorStyleAA13FBTooltipViewC0fG0Ovs', symObjAddr: 0xA2C, symBinAddr: 0x17A8C, symSize: 0x48 }
  - { offsetInCU: 0x24BF, offset: 0xF3AC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC17tooltipColorStyleAA13FBTooltipViewC0fG0OvM', symObjAddr: 0xA74, symBinAddr: 0x17AD4, symSize: 0x44 }
  - { offsetInCU: 0x24F4, offset: 0xF3E1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC13loginTrackingAA05LoginF0Ovg', symObjAddr: 0xAFC, symBinAddr: 0x17B5C, symSize: 0x44 }
  - { offsetInCU: 0x2533, offset: 0xF420, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC13loginTrackingAA05LoginF0Ovs', symObjAddr: 0xB88, symBinAddr: 0x17BE8, symSize: 0x48 }
  - { offsetInCU: 0x2559, offset: 0xF446, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC13loginTrackingAA05LoginF0OvM', symObjAddr: 0xBD0, symBinAddr: 0x17C30, symSize: 0x44 }
  - { offsetInCU: 0x258E, offset: 0xF47B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5nonceSSSgvg', symObjAddr: 0xC70, symBinAddr: 0x17CD0, symSize: 0x38 }
  - { offsetInCU: 0x25FE, offset: 0xF4EB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5nonceSSSgvs', symObjAddr: 0xD0C, symBinAddr: 0x17D6C, symSize: 0x1FC }
  - { offsetInCU: 0x2738, offset: 0xF625, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5nonceSSSgvM', symObjAddr: 0xF8C, symBinAddr: 0x17F68, symSize: 0x48 }
  - { offsetInCU: 0x2776, offset: 0xF663, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5nonceSSSgvM.resume.0', symObjAddr: 0xFD4, symBinAddr: 0x17FB0, symSize: 0x60 }
  - { offsetInCU: 0x2795, offset: 0xF682, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15messengerPageIdSSSgvM', symObjAddr: 0x1064, symBinAddr: 0x18040, symSize: 0x44 }
  - { offsetInCU: 0x27CA, offset: 0xF6B7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC8authTypeSo0a4AuthF0aSgvg', symObjAddr: 0x10F0, symBinAddr: 0x180CC, symSize: 0x50 }
  - { offsetInCU: 0x2809, offset: 0xF6F6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC8authTypeSo0a4AuthF0aSgvs', symObjAddr: 0x11A4, symBinAddr: 0x18180, symSize: 0x50 }
  - { offsetInCU: 0x282F, offset: 0xF71C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC8authTypeSo0a4AuthF0aSgvM', symObjAddr: 0x11F4, symBinAddr: 0x181D0, symSize: 0x44 }
  - { offsetInCU: 0x2864, offset: 0xF751, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC12codeVerifierAA04CodeF0Cvg', symObjAddr: 0x1280, symBinAddr: 0x1825C, symSize: 0x44 }
  - { offsetInCU: 0x28A3, offset: 0xF790, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC12codeVerifierAA04CodeF0Cvs', symObjAddr: 0x1328, symBinAddr: 0x18304, symSize: 0x50 }
  - { offsetInCU: 0x28C9, offset: 0xF7B6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC12codeVerifierAA04CodeF0CvM', symObjAddr: 0x1378, symBinAddr: 0x18354, symSize: 0x44 }
  - { offsetInCU: 0x28EC, offset: 0xF7D9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC6userIDSSSgvM', symObjAddr: 0x13EC, symBinAddr: 0x183C8, symSize: 0x44 }
  - { offsetInCU: 0x290F, offset: 0xF7FC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC8userNameSSSgvM', symObjAddr: 0x15F4, symBinAddr: 0x185D0, symSize: 0x44 }
  - { offsetInCU: 0x2932, offset: 0xF81F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15elementProviderAA29UserInterfaceElementProviding_pvM', symObjAddr: 0x1670, symBinAddr: 0x1864C, symSize: 0x44 }
  - { offsetInCU: 0x2955, offset: 0xF842, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC14stringProviderAA28UserInterfaceStringProviding_pvM', symObjAddr: 0x17A0, symBinAddr: 0x1877C, symSize: 0x44 }
  - { offsetInCU: 0x2978, offset: 0xF865, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC13loginProviderAA14LoginProviding_pvM', symObjAddr: 0x1860, symBinAddr: 0x1883C, symSize: 0x44 }
  - { offsetInCU: 0x29CF, offset: 0xF8BC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC19graphRequestFactorySo010FBSDKGraphfG0_pvM', symObjAddr: 0x1A18, symBinAddr: 0x189F4, symSize: 0x44 }
  - { offsetInCU: 0x2A04, offset: 0xF8F1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15isAuthenticatedSbvg', symObjAddr: 0x1B04, symBinAddr: 0x18AE0, symSize: 0x60 }
  - { offsetInCU: 0x2A24, offset: 0xF911, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15TooltipBehaviorO8rawValueAESgSu_tcfC', symObjAddr: 0x1B64, symBinAddr: 0x18B40, symSize: 0x14 }
  - { offsetInCU: 0x2A41, offset: 0xF92E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15TooltipBehaviorO8rawValueSuvg', symObjAddr: 0x1B78, symBinAddr: 0x18B54, symSize: 0x4 }
  - { offsetInCU: 0x2AB9, offset: 0xF9A6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5frameACSo6CGRectV_tcfC', symObjAddr: 0x1C68, symBinAddr: 0x18C44, symSize: 0x50 }
  - { offsetInCU: 0x2AEA, offset: 0xF9D7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5frameACSo6CGRectV_tcfc', symObjAddr: 0x1CB8, symBinAddr: 0x18C94, symSize: 0x278 }
  - { offsetInCU: 0x2BAF, offset: 0xFA9C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC09configureD033_2D5546723C2E9E390359F57C16888789LLyyF', symObjAddr: 0x1F6C, symBinAddr: 0x18F48, symSize: 0x43C }
  - { offsetInCU: 0x2C5D, offset: 0xFB4A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5coderACSgSo7NSCoderC_tcfC', symObjAddr: 0x23C8, symBinAddr: 0x193A4, symSize: 0x44 }
  - { offsetInCU: 0x2C70, offset: 0xFB5D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5coderACSgSo7NSCoderC_tcfc', symObjAddr: 0x240C, symBinAddr: 0x193E8, symSize: 0x30 }
  - { offsetInCU: 0x2C89, offset: 0xFB76, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5frame15elementProvider06stringG005loginG019graphRequestFactoryACSo6CGRectV_AA29UserInterfaceElementProviding_pAA0no6StringQ0_pAA05LoginQ0_pSo010FBSDKGraphkL0_ptcfC', symObjAddr: 0x2478, symBinAddr: 0x19454, symSize: 0x16C }
  - { offsetInCU: 0x2D74, offset: 0xFC61, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5frame11permissionsACSo6CGRectV_Say09FBSDKCoreB010PermissionOGtcfC', symObjAddr: 0x25E4, symBinAddr: 0x195C0, symSize: 0x228 }
  - { offsetInCU: 0x2FAF, offset: 0xFE9C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC17initializeContentyyF', symObjAddr: 0x280C, symBinAddr: 0x197E8, symSize: 0x17C }
  - { offsetInCU: 0x3001, offset: 0xFEEE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC15didMoveToWindowyyF', symObjAddr: 0x2988, symBinAddr: 0x19964, symSize: 0xA8 }
  - { offsetInCU: 0x308B, offset: 0xFF78, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC19showTooltipIfNeeded33_2D5546723C2E9E390359F57C16888789LLyyF', symObjAddr: 0x2A30, symBinAddr: 0x19A0C, symSize: 0x174 }
  - { offsetInCU: 0x31E0, offset: 0x100CD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC9imageRect010forContentF0So6CGRectVAG_tF', symObjAddr: 0x2BCC, symBinAddr: 0x19BA8, symSize: 0x28 }
  - { offsetInCU: 0x3251, offset: 0x1013E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC9titleRect010forContentF0So6CGRectVAG_tF', symObjAddr: 0x2C1C, symBinAddr: 0x19BF8, symSize: 0xF0 }
  - { offsetInCU: 0x3395, offset: 0x10282, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC14layoutSubviewsyyF', symObjAddr: 0x2E1C, symBinAddr: 0x19DF8, symSize: 0x3E4 }
  - { offsetInCU: 0x3473, offset: 0x10360, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC12sizeThatFitsySo6CGSizeVAFF', symObjAddr: 0x3228, symBinAddr: 0x1A204, symSize: 0x4F4 }
  - { offsetInCU: 0x358E, offset: 0x1047B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC20accessTokenDidChangeyy10Foundation12NotificationVF', symObjAddr: 0x3770, symBinAddr: 0x1A74C, symSize: 0x18C }
  - { offsetInCU: 0x365B, offset: 0x10548, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC27updateContentForAccessTokenyyF', symObjAddr: 0x38FC, symBinAddr: 0x1A8D8, symSize: 0x144 }
  - { offsetInCU: 0x36BD, offset: 0x105AA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC16profileDidChangeyy10Foundation12NotificationVF', symObjAddr: 0x3AD4, symBinAddr: 0x1AAB0, symSize: 0x140 }
  - { offsetInCU: 0x3715, offset: 0x10602, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC20updateContentForUseryy09FBSDKCoreB07ProfileCSgF', symObjAddr: 0x3C14, symBinAddr: 0x1ABF0, symSize: 0x108 }
  - { offsetInCU: 0x377D, offset: 0x1066A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC13buttonPressedyyypF', symObjAddr: 0x3DAC, symBinAddr: 0x1AD88, symSize: 0x254 }
  - { offsetInCU: 0x382A, offset: 0x10717, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC26presentAlertViewController33_2D5546723C2E9E390359F57C16888789LLyyF', symObjAddr: 0x4000, symBinAddr: 0x1AFDC, symSize: 0x7C4 }
  - { offsetInCU: 0x39A2, offset: 0x1088F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC26presentAlertViewController33_2D5546723C2E9E390359F57C16888789LLyyFySo13UIAlertActionCcfU_', symObjAddr: 0x4C90, symBinAddr: 0x1BC6C, symSize: 0xCC }
  - { offsetInCU: 0x3A47, offset: 0x10934, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC22makeLoginConfigurationAA0fG0CSgyF', symObjAddr: 0x4828, symBinAddr: 0x1B804, symSize: 0x2E4 }
  - { offsetInCU: 0x3C9E, offset: 0x10B8B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC6logoutyyF', symObjAddr: 0x4D5C, symBinAddr: 0x1BD38, symSize: 0xCC }
  - { offsetInCU: 0x3CE5, offset: 0x10BD2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC18fetchAndSetContentyyF', symObjAddr: 0x4E50, symBinAddr: 0x1BE2C, symSize: 0x218 }
  - { offsetInCU: 0x3DC4, offset: 0x10CB1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC18fetchAndSetContentyyFySo27FBSDKGraphRequestConnecting_pSg_ypSgs5Error_pSgtcfU_', symObjAddr: 0x5090, symBinAddr: 0x1C06C, symSize: 0x268 }
  - { offsetInCU: 0x3EDD, offset: 0x10DCA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC27userInformationDoesNotMatch33_2D5546723C2E9E390359F57C16888789LLySb09FBSDKCoreB07ProfileCF', symObjAddr: 0x5320, symBinAddr: 0x1C2FC, symSize: 0x140 }
  - { offsetInCU: 0x3F67, offset: 0x10E54, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonCfD', symObjAddr: 0x54DC, symBinAddr: 0x1C4B8, symSize: 0x34 }
  - { offsetInCU: 0x3F88, offset: 0x10E75, size: 0x8, addend: 0x0, symName: '_$ss15ContiguousArrayV16_createNewBuffer14bufferIsUnique15minimumCapacity13growForAppendySb_SiSbtFSS_Tg5', symObjAddr: 0x55F8, symBinAddr: 0x1C5D4, symSize: 0x1C }
  - { offsetInCU: 0x3F9C, offset: 0x10E89, size: 0x8, addend: 0x0, symName: '_$ss15ContiguousArrayV16_createNewBuffer14bufferIsUnique15minimumCapacity13growForAppendySb_SiSbtF12FBSDKCoreKit10PermissionO_Tg5', symObjAddr: 0x5614, symBinAddr: 0x1C5F0, symSize: 0x1C }
  - { offsetInCU: 0x3FD4, offset: 0x10EC1, size: 0x8, addend: 0x0, symName: '_$ss22_ContiguousArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtFSS_Tg5', symObjAddr: 0x5630, symBinAddr: 0x1C60C, symSize: 0xBC }
  - { offsetInCU: 0x4079, offset: 0x10F66, size: 0x8, addend: 0x0, symName: '_$ss22_ContiguousArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtF12FBSDKCoreKit10PermissionO_Tg5', symObjAddr: 0x56EC, symBinAddr: 0x1C6C8, symSize: 0xEC }
  - { offsetInCU: 0x40F8, offset: 0x10FE5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBLoginButtonC5coderACSgSo7NSCoderC_tcfcTf4gn_n', symObjAddr: 0x57D8, symBinAddr: 0x1C7B4, symSize: 0x264 }
  - { offsetInCU: 0x77, offset: 0x11182, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC8delegateAA05LogindE8Delegate_pSgvgTo', symObjAddr: 0x20, symBinAddr: 0x1CF74, symSize: 0x48 }
  - { offsetInCU: 0xCB, offset: 0x111D6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC8delegateAA05LogindE8Delegate_pSgvsTo', symObjAddr: 0xB0, symBinAddr: 0x1D004, symSize: 0x50 }
  - { offsetInCU: 0x14B, offset: 0x11256, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC12forceDisplaySbvgTo', symObjAddr: 0x234, symBinAddr: 0x1D188, symSize: 0x44 }
  - { offsetInCU: 0x19F, offset: 0x112AA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC12forceDisplaySbvsTo', symObjAddr: 0x2BC, symBinAddr: 0x1D210, symSize: 0x48 }
  - { offsetInCU: 0x2CC, offset: 0x113D7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewCACycfcTo', symObjAddr: 0x548, symBinAddr: 0x1D49C, symSize: 0xA8 }
  - { offsetInCU: 0x3AA, offset: 0x114B5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC7tagline7message10colorStyleACSSSg_AgA09FBTooltipE0C05ColorI0OtcfcTo', symObjAddr: 0x83C, symBinAddr: 0x1D790, symSize: 0x84 }
  - { offsetInCU: 0x429, offset: 0x11534, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC7present2in13arrowPosition9directionySo6UIViewC_So7CGPointVAA09FBTooltipE0C14ArrowDirectionOtFTo', symObjAddr: 0xB6C, symBinAddr: 0x1DAC0, symSize: 0x70 }
  - { offsetInCU: 0x444, offset: 0x1154F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC05fetchD13Configuration015_3798D0DCC12906H16A17D23FC0B3F34C1LL4with13arrowPosition9directionySo6UIViewC_So7CGPointVAA09FBTooltipE0C14ArrowDirectionOtFySo0aD0CSg_s5Error_pSgtcfU_', symObjAddr: 0xBDC, symBinAddr: 0x1DB30, symSize: 0x270 }
  - { offsetInCU: 0x599, offset: 0x116A4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC27serverConfigurationProvider06stringH0AcA06ServerG9Providing_p_AA019UserInterfaceStringK0_ptcfcTf4een_n09FBSDKCoreB00jgH0C_So20FBSDKInternalUtilityCTg5', symObjAddr: 0xF04, symBinAddr: 0x1DE58, symSize: 0x178 }
  - { offsetInCU: 0x633, offset: 0x1173E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC27serverConfigurationProvider06stringH0AcA06ServerG9Providing_p_AA019UserInterfaceStringK0_ptcfcTf4een_n', symObjAddr: 0x10E0, symBinAddr: 0x1DFD0, symSize: 0x19C }
  - { offsetInCU: 0x6C0, offset: 0x117CB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC27serverConfigurationProvider06stringH0AcA06ServerG9Providing_p_AA019UserInterfaceStringK0_ptcfCTfq4een_nTf4ngn_nTf4gnn_n', symObjAddr: 0x127C, symBinAddr: 0x1E16C, symSize: 0xE4 }
  - { offsetInCU: 0x7D0, offset: 0x118DB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewCfETo', symObjAddr: 0xEBC, symBinAddr: 0x1DE10, symSize: 0x48 }
  - { offsetInCU: 0x7FE, offset: 0x11909, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC05fetchD13Configuration015_3798D0DCC12906H16A17D23FC0B3F34C1LL4with13arrowPosition9directionySo6UIViewC_So7CGPointVAA09FBTooltipE0C14ArrowDirectionOtFySo0aD0CSg_s5Error_pSgtcfU_TA', symObjAddr: 0x13D0, symBinAddr: 0x1E27C, symSize: 0x10 }
  - { offsetInCU: 0x811, offset: 0x1191C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewCMa', symObjAddr: 0x1524, symBinAddr: 0x1E3D0, symSize: 0x20 }
  - { offsetInCU: 0x824, offset: 0x1192F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24LoginTooltipViewDelegate_pSgXwWOh', symObjAddr: 0x1558, symBinAddr: 0x1E404, symSize: 0x24 }
  - { offsetInCU: 0x837, offset: 0x11942, size: 0x8, addend: 0x0, symName: ___swift_allocate_boxed_opaque_existential_1, symObjAddr: 0x157C, symBinAddr: 0x1E428, symSize: 0x3C }
  - { offsetInCU: 0x9E4, offset: 0x11AEF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewCACycfC', symObjAddr: 0x0, symBinAddr: 0x1CF54, symSize: 0x20 }
  - { offsetInCU: 0xA0E, offset: 0x11B19, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC8delegateAA05LogindE8Delegate_pSgvg', symObjAddr: 0x68, symBinAddr: 0x1CFBC, symSize: 0x48 }
  - { offsetInCU: 0xA53, offset: 0x11B5E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC8delegateAA05LogindE8Delegate_pSgvs', symObjAddr: 0x100, symBinAddr: 0x1D054, symSize: 0x58 }
  - { offsetInCU: 0xA79, offset: 0x11B84, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC8delegateAA05LogindE8Delegate_pSgvM', symObjAddr: 0x158, symBinAddr: 0x1D0AC, symSize: 0x70 }
  - { offsetInCU: 0xA9C, offset: 0x11BA7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC8delegateAA05LogindE8Delegate_pSgvM.resume.0', symObjAddr: 0x1C8, symBinAddr: 0x1D11C, symSize: 0x6C }
  - { offsetInCU: 0xACD, offset: 0x11BD8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC12forceDisplaySbvg', symObjAddr: 0x278, symBinAddr: 0x1D1CC, symSize: 0x44 }
  - { offsetInCU: 0xB0C, offset: 0x11C17, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC12forceDisplaySbvs', symObjAddr: 0x304, symBinAddr: 0x1D258, symSize: 0x48 }
  - { offsetInCU: 0xB2F, offset: 0x11C3A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC12forceDisplaySbvM', symObjAddr: 0x34C, symBinAddr: 0x1D2A0, symSize: 0x44 }
  - { offsetInCU: 0xB52, offset: 0x11C5D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC12forceDisplaySbvM.resume.0', symObjAddr: 0x390, symBinAddr: 0x1D2E4, symSize: 0x4 }
  - { offsetInCU: 0xB83, offset: 0x11C8E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC18shouldForceDisplaySbvM', symObjAddr: 0x394, symBinAddr: 0x1D2E8, symSize: 0x6C }
  - { offsetInCU: 0xBBB, offset: 0x11CC6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC18shouldForceDisplaySbvM.resume.0', symObjAddr: 0x400, symBinAddr: 0x1D354, symSize: 0x14 }
  - { offsetInCU: 0xBED, offset: 0x11CF8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC27serverConfigurationProviderAA06ServerG9Providing_pvg', symObjAddr: 0x414, symBinAddr: 0x1D368, symSize: 0x24 }
  - { offsetInCU: 0xC0E, offset: 0x11D19, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC14stringProviderAA28UserInterfaceStringProviding_pvg', symObjAddr: 0x438, symBinAddr: 0x1D38C, symSize: 0x24 }
  - { offsetInCU: 0xC47, offset: 0x11D52, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewCACycfc', symObjAddr: 0x4A0, symBinAddr: 0x1D3F4, symSize: 0xA8 }
  - { offsetInCU: 0xD24, offset: 0x11E2F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC7tagline7message10colorStyleACSSSg_AgA09FBTooltipE0C05ColorI0OtcfC', symObjAddr: 0x5F0, symBinAddr: 0x1D544, symSize: 0x12C }
  - { offsetInCU: 0xD73, offset: 0x11E7E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC7tagline7message10colorStyleACSSSg_AgA09FBTooltipE0C05ColorI0Otcfc', symObjAddr: 0x71C, symBinAddr: 0x1D670, symSize: 0x120 }
  - { offsetInCU: 0xDC0, offset: 0x11ECB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC27serverConfigurationProvider06stringH0AcA06ServerG9Providing_p_AA019UserInterfaceStringK0_ptcfC', symObjAddr: 0x8C0, symBinAddr: 0x1D814, symSize: 0x90 }
  - { offsetInCU: 0xDFC, offset: 0x11F07, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC27serverConfigurationProvider06stringH0AcA06ServerG9Providing_p_AA019UserInterfaceStringK0_ptcfc', symObjAddr: 0x950, symBinAddr: 0x1D8A4, symSize: 0xFC }
  - { offsetInCU: 0xE6C, offset: 0x11F77, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewC7present2in13arrowPosition9directionySo6UIViewC_So7CGPointVAA09FBTooltipE0C14ArrowDirectionOtF', symObjAddr: 0xA4C, symBinAddr: 0x1D9A0, symSize: 0x120 }
  - { offsetInCU: 0xF64, offset: 0x1206F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewCfE', symObjAddr: 0xE4C, symBinAddr: 0x1DDA0, symSize: 0x3C }
  - { offsetInCU: 0xF85, offset: 0x12090, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18FBLoginTooltipViewCfD', symObjAddr: 0xE88, symBinAddr: 0x1DDDC, symSize: 0x34 }
  - { offsetInCU: 0x169, offset: 0x12262, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionC4hashSivgTo', symObjAddr: 0x344, symBinAddr: 0x1E7B8, symSize: 0x4C }
  - { offsetInCU: 0x1D9, offset: 0x122D2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionC6stringACSgSS_tcfcTo', symObjAddr: 0x5D0, symBinAddr: 0x1EA00, symSize: 0x28 }
  - { offsetInCU: 0x20D, offset: 0x12306, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionC11permissions18fromRawPermissionsShyACGSgShySSG_tFZTo', symObjAddr: 0x5FC, symBinAddr: 0x1EA2C, symSize: 0x90 }
  - { offsetInCU: 0x23D, offset: 0x12336, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionC14rawPermissions4fromShySSGShyACG_tFZTo', symObjAddr: 0x6D4, symBinAddr: 0x1EB04, symSize: 0x98 }
  - { offsetInCU: 0x2A3, offset: 0x1239C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionC7isEqualySbypSgFTo', symObjAddr: 0x860, symBinAddr: 0x1EC90, symSize: 0x80 }
  - { offsetInCU: 0x2E9, offset: 0x123E2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionCACycfcTo', symObjAddr: 0x92C, symBinAddr: 0x1ED5C, symSize: 0x2C }
  - { offsetInCU: 0x361, offset: 0x1245A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionC11permissions18fromRawPermissionsShyACGSgShySSG_tFZTf4nd_n', symObjAddr: 0x9A0, symBinAddr: 0x1EDD0, symSize: 0x334 }
  - { offsetInCU: 0x535, offset: 0x1262E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionCfETo', symObjAddr: 0x98C, symBinAddr: 0x1EDBC, symSize: 0x14 }
  - { offsetInCU: 0x580, offset: 0x12679, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionCMa', symObjAddr: 0xD9C, symBinAddr: 0x1F104, symSize: 0x20 }
  - { offsetInCU: 0x593, offset: 0x1268C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionCSo8NSObjectCSH10ObjectiveCWl', symObjAddr: 0xDD0, symBinAddr: 0x1F138, symSize: 0x44 }
  - { offsetInCU: 0x5A6, offset: 0x1269F, size: 0x8, addend: 0x0, symName: '_$sSh5IndexV8_VariantOyx__GSHRzlWOe', symObjAddr: 0xE14, symBinAddr: 0x1F17C, symSize: 0xC }
  - { offsetInCU: 0x5F8, offset: 0x126F1, size: 0x8, addend: 0x0, symName: '_$sSlsE3mapySayqd__Gqd__7ElementQzKXEKlFShy13FBSDKLoginKit12FBPermissionCG_SSTg504$s13c5Kit12E93C14rawPermissions4fromShySSGShyACG_tFZSSACcfu_32e0d58b938ad0b6cb17de1b825049cc00ACSSTf3nnpk_nTf1cn_n', symObjAddr: 0x0, symBinAddr: 0x1E474, symSize: 0x2B8 }
  - { offsetInCU: 0x9B4, offset: 0x12AAD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionC14rawPermissions4fromShySSGShyACG_tFZ', symObjAddr: 0x68C, symBinAddr: 0x1EABC, symSize: 0x48 }
  - { offsetInCU: 0xA3E, offset: 0x12B37, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionC11descriptionSSvg', symObjAddr: 0x30C, symBinAddr: 0x1E780, symSize: 0x38 }
  - { offsetInCU: 0xA78, offset: 0x12B71, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionC4hashSivg', symObjAddr: 0x390, symBinAddr: 0x1E804, symSize: 0x4C }
  - { offsetInCU: 0xA95, offset: 0x12B8E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionC6stringACSgSS_tcfC', symObjAddr: 0x420, symBinAddr: 0x1E850, symSize: 0x40 }
  - { offsetInCU: 0xAAE, offset: 0x12BA7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionC6stringACSgSS_tcfc', symObjAddr: 0x460, symBinAddr: 0x1E890, symSize: 0x170 }
  - { offsetInCU: 0xAF7, offset: 0x12BF0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionC11permissions18fromRawPermissionsShyACGSgShySSG_tFZ', symObjAddr: 0x5F8, symBinAddr: 0x1EA28, symSize: 0x4 }
  - { offsetInCU: 0xB44, offset: 0x12C3D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionC7isEqualySbypSgF', symObjAddr: 0x76C, symBinAddr: 0x1EB9C, symSize: 0xF4 }
  - { offsetInCU: 0xB9E, offset: 0x12C97, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionCACycfC', symObjAddr: 0x8E0, symBinAddr: 0x1ED10, symSize: 0x20 }
  - { offsetInCU: 0xBB1, offset: 0x12CAA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionCACycfc', symObjAddr: 0x900, symBinAddr: 0x1ED30, symSize: 0x2C }
  - { offsetInCU: 0xC05, offset: 0x12CFE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12FBPermissionCfD', symObjAddr: 0x958, symBinAddr: 0x1ED88, symSize: 0x34 }
  - { offsetInCU: 0x4E, offset: 0x12DFE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO13kNUXRectInset12CoreGraphics7CGFloatVvpZ', symObjAddr: 0x50F0, symBinAddr: 0x620B0, symSize: 0x0 }
  - { offsetInCU: 0x68, offset: 0x12E18, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO16kNUXBubbleMargin12CoreGraphics7CGFloatVvpZ', symObjAddr: 0x50F8, symBinAddr: 0x620B8, symSize: 0x0 }
  - { offsetInCU: 0x82, offset: 0x12E32, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO15kNUXPointMargin12CoreGraphics7CGFloatVvpZ', symObjAddr: 0x5100, symBinAddr: 0x620C0, symSize: 0x0 }
  - { offsetInCU: 0x9C, offset: 0x12E4C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO16kNUXCornerRadius12CoreGraphics7CGFloatVvpZ', symObjAddr: 0x5108, symBinAddr: 0x620C8, symSize: 0x0 }
  - { offsetInCU: 0xB6, offset: 0x12E66, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO11kNUXSideCap12CoreGraphics7CGFloatVvpZ', symObjAddr: 0x5110, symBinAddr: 0x620D0, symSize: 0x0 }
  - { offsetInCU: 0xD0, offset: 0x12E80, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO12kNUXFontSize12CoreGraphics7CGFloatVvpZ', symObjAddr: 0x5118, symBinAddr: 0x620D8, symSize: 0x0 }
  - { offsetInCU: 0xEA, offset: 0x12E9A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO18kNUXCrossGlyphSize12CoreGraphics7CGFloatVvpZ', symObjAddr: 0x5120, symBinAddr: 0x620E0, symSize: 0x0 }
  - { offsetInCU: 0x104, offset: 0x12EB4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO27kFriendlyBlueGradientColorsSaySo10CGColorRefaGvpZ', symObjAddr: 0x5128, symBinAddr: 0x620E8, symSize: 0x0 }
  - { offsetInCU: 0x11E, offset: 0x12ECE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO12kNeutralGraySaySo10CGColorRefaGvpZ', symObjAddr: 0x5130, symBinAddr: 0x620F0, symSize: 0x0 }
  - { offsetInCU: 0x12C, offset: 0x12EDC, size: 0x8, addend: 0x0, symName: '_$sIeg_IeyB_TR', symObjAddr: 0x0, symBinAddr: 0x1F18C, symSize: 0x2C }
  - { offsetInCU: 0x1A8, offset: 0x12F58, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC10ColorStyleOSYAASY8rawValuexSg03RawH0Qz_tcfCTW', symObjAddr: 0x22C8, symBinAddr: 0x21454, symSize: 0x30 }
  - { offsetInCU: 0x1D7, offset: 0x12F87, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC10ColorStyleOSYAASY8rawValue03RawH0QzvgTW', symObjAddr: 0x22F8, symBinAddr: 0x21484, symSize: 0xC }
  - { offsetInCU: 0x67C, offset: 0x1342C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO13kNUXRectInset_WZ', symObjAddr: 0x2304, symBinAddr: 0x21490, symSize: 0x10 }
  - { offsetInCU: 0x695, offset: 0x13445, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO16kNUXBubbleMargin_WZ', symObjAddr: 0x2314, symBinAddr: 0x214A0, symSize: 0x50 }
  - { offsetInCU: 0x6BD, offset: 0x1346D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO15kNUXPointMargin_WZ', symObjAddr: 0x2364, symBinAddr: 0x214F0, symSize: 0x10 }
  - { offsetInCU: 0x6D6, offset: 0x13486, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO16kNUXCornerRadius_WZ', symObjAddr: 0x2374, symBinAddr: 0x21500, symSize: 0x10 }
  - { offsetInCU: 0x6EF, offset: 0x1349F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO11kNUXSideCap_WZ', symObjAddr: 0x2384, symBinAddr: 0x21510, symSize: 0x10 }
  - { offsetInCU: 0x708, offset: 0x134B8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO12kNUXFontSize_WZ', symObjAddr: 0x2394, symBinAddr: 0x21520, symSize: 0x10 }
  - { offsetInCU: 0x721, offset: 0x134D1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO18kNUXCrossGlyphSize_WZ', symObjAddr: 0x23A4, symBinAddr: 0x21530, symSize: 0x10 }
  - { offsetInCU: 0x73A, offset: 0x134EA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO27kFriendlyBlueGradientColors_WZ', symObjAddr: 0x23B4, symBinAddr: 0x21540, symSize: 0x10C }
  - { offsetInCU: 0x7F5, offset: 0x135A5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9Constants33_1C39B2F52DDA14663AEF238AF411735ALLO12kNeutralGray_WZ', symObjAddr: 0x24C0, symBinAddr: 0x2164C, symSize: 0x108 }
  - { offsetInCU: 0x8B0, offset: 0x13660, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC15displayDurationSdvgTo', symObjAddr: 0x25C8, symBinAddr: 0x21754, symSize: 0x44 }
  - { offsetInCU: 0x8EB, offset: 0x1369B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC15displayDurationSdvsTo', symObjAddr: 0x260C, symBinAddr: 0x21798, symSize: 0x50 }
  - { offsetInCU: 0x92D, offset: 0x136DD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC10colorStyleAC05ColorF0OvgTo', symObjAddr: 0x265C, symBinAddr: 0x217E8, symSize: 0x44 }
  - { offsetInCU: 0x968, offset: 0x13718, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC10colorStyleAC05ColorF0OvsTo', symObjAddr: 0x26A0, symBinAddr: 0x2182C, symSize: 0x6C }
  - { offsetInCU: 0x9B4, offset: 0x13764, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9textLabelSo7UILabelCvpfiAFyXEfU_', symObjAddr: 0x2814, symBinAddr: 0x219A0, symSize: 0x13C }
  - { offsetInCU: 0xA2E, offset: 0x137DE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9textLabelSo7UILabelCvgTo', symObjAddr: 0x2994, symBinAddr: 0x21B20, symSize: 0x10 }
  - { offsetInCU: 0xA4E, offset: 0x137FE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9textLabelSo7UILabelCvgTo', symObjAddr: 0x2994, symBinAddr: 0x21B20, symSize: 0x10 }
  - { offsetInCU: 0xA93, offset: 0x13843, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewCACycfcTo', symObjAddr: 0x29F0, symBinAddr: 0x21B7C, symSize: 0x18 }
  - { offsetInCU: 0xAB3, offset: 0x13863, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewCACycfcTo', symObjAddr: 0x29F0, symBinAddr: 0x21B7C, symSize: 0x18 }
  - { offsetInCU: 0xAF1, offset: 0x138A1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7tagline7message10colorStyleACSSSg_AgC05ColorH0OtcfcTo', symObjAddr: 0x2A68, symBinAddr: 0x21BF4, symSize: 0x84 }
  - { offsetInCU: 0xB3E, offset: 0x138EE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC5coderACSgSo7NSCoderC_tcfcTo', symObjAddr: 0x2B34, symBinAddr: 0x21CC0, symSize: 0x28 }
  - { offsetInCU: 0xB83, offset: 0x13933, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewCfDTo', symObjAddr: 0x2BB0, symBinAddr: 0x21D3C, symSize: 0x78 }
  - { offsetInCU: 0xBB3, offset: 0x13963, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7present4fromySo6UIViewC_tFTo', symObjAddr: 0x2CB8, symBinAddr: 0x21E44, symSize: 0x50 }
  - { offsetInCU: 0xBCE, offset: 0x1397E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7present2in13arrowPosition9directionySo6UIViewC_So7CGPointVAC14ArrowDirectionOtFTo', symObjAddr: 0x2D08, symBinAddr: 0x21E94, symSize: 0x70 }
  - { offsetInCU: 0xBE9, offset: 0x13999, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7dismissyyFTo', symObjAddr: 0x2E3C, symBinAddr: 0x21FC8, symSize: 0x98 }
  - { offsetInCU: 0xC69, offset: 0x13A19, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC13animateFadeInyyFTo', symObjAddr: 0x3690, symBinAddr: 0x2281C, symSize: 0x28 }
  - { offsetInCU: 0xCA0, offset: 0x13A50, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC14onTapInTooltip33_1C39B2F52DDA14663AEF238AF411735ALLyySo19UIGestureRecognizerCFTo', symObjAddr: 0x36B8, symBinAddr: 0x22844, symSize: 0x74 }
  - { offsetInCU: 0xCEA, offset: 0x13A9A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC4drawyySo6CGRectVFTo', symObjAddr: 0x3730, symBinAddr: 0x228B8, symSize: 0x28 }
  - { offsetInCU: 0xD33, offset: 0x13AE3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC14layoutSubviewsyyFTo', symObjAddr: 0x378C, symBinAddr: 0x228E0, symSize: 0x5C }
  - { offsetInCU: 0xD67, offset: 0x13B17, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC47scheduleFadeoutRespectingMinimumDisplayDuration33_1C39B2F52DDA14663AEF238AF411735ALLyyFTo', symObjAddr: 0x37E8, symBinAddr: 0x2293C, symSize: 0x84 }
  - { offsetInCU: 0xDFB, offset: 0x13BAB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC5frameACSo6CGRectV_tcfcTo', symObjAddr: 0x38E8, symBinAddr: 0x22A3C, symSize: 0x2C }
  - { offsetInCU: 0xE5E, offset: 0x13C0E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC35fbsdkCreateUpPointingBubbleWithRect33_1C39B2F52DDA14663AEF238AF411735ALLySo16CGMutablePathRefaSo6CGRectV_12CoreGraphics7CGFloatVA2LtFTf4nnnnd_n', symObjAddr: 0x39BC, symBinAddr: 0x22B10, symSize: 0x2D4 }
  - { offsetInCU: 0x10A2, offset: 0x13E52, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC37fbsdkCreateDownPointingBubbleWithRect33_1C39B2F52DDA14663AEF238AF411735ALLySo16CGMutablePathRefaSo6CGRectV_12CoreGraphics7CGFloatVA2LtFTf4nnnnd_n', symObjAddr: 0x3C90, symBinAddr: 0x22DE4, symSize: 0x2D4 }
  - { offsetInCU: 0x12E6, offset: 0x14096, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC29createCloseCrossGlyphWithRect33_1C39B2F52DDA14663AEF238AF411735ALLySo16CGMutablePathRefaSo6CGRectVFTf4nd_n', symObjAddr: 0x3F64, symBinAddr: 0x230B8, symSize: 0x2AC }
  - { offsetInCU: 0x164D, offset: 0x143FD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewCMa', symObjAddr: 0x2950, symBinAddr: 0x21ADC, symSize: 0x20 }
  - { offsetInCU: 0x1660, offset: 0x14410, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewCfETo', symObjAddr: 0x2C28, symBinAddr: 0x21DB4, symSize: 0x90 }
  - { offsetInCU: 0x168E, offset: 0x1443E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7dismissyyFyycfU_TA', symObjAddr: 0x2E34, symBinAddr: 0x21FC0, symSize: 0x8 }
  - { offsetInCU: 0x16A1, offset: 0x14451, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC13animateFadeInyyFyycfU_TA', symObjAddr: 0x3124, symBinAddr: 0x222B0, symSize: 0x10 }
  - { offsetInCU: 0x16B4, offset: 0x14464, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC13animateFadeInyyFyycfU0_TA', symObjAddr: 0x3380, symBinAddr: 0x2250C, symSize: 0x8 }
  - { offsetInCU: 0x16C7, offset: 0x14477, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC13animateFadeInyyFyycfU1_TA', symObjAddr: 0x3408, symBinAddr: 0x22594, symSize: 0x8 }
  - { offsetInCU: 0x16DA, offset: 0x1448A, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x3410, symBinAddr: 0x2259C, symSize: 0x10 }
  - { offsetInCU: 0x16ED, offset: 0x1449D, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x3420, symBinAddr: 0x225AC, symSize: 0x8 }
  - { offsetInCU: 0x1700, offset: 0x144B0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC13animateFadeInyyFySbcfU2_TA', symObjAddr: 0x3594, symBinAddr: 0x22720, symSize: 0xC }
  - { offsetInCU: 0x1713, offset: 0x144C3, size: 0x8, addend: 0x0, symName: '_$sSbIegy_SbIeyBy_TR', symObjAddr: 0x3654, symBinAddr: 0x227E0, symSize: 0x3C }
  - { offsetInCU: 0x1748, offset: 0x144F8, size: 0x8, addend: 0x0, symName: '_$ss29getContiguousArrayStorageType3fors01_bcD0CyxGmxm_tlF13FBSDKLoginKit18DeviceLoginManagerC_Tgm5', symObjAddr: 0x3914, symBinAddr: 0x22A68, symSize: 0x54 }
  - { offsetInCU: 0x1773, offset: 0x14523, size: 0x8, addend: 0x0, symName: '_$ss29getContiguousArrayStorageType3fors01_bcD0CyxGmxm_tlFSo10CGColorRefa_Tgm5', symObjAddr: 0x3968, symBinAddr: 0x22ABC, symSize: 0x54 }
  - { offsetInCU: 0x179E, offset: 0x1454E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC14ArrowDirectionOSHAASQWb', symObjAddr: 0x4A04, symBinAddr: 0x23B58, symSize: 0x4 }
  - { offsetInCU: 0x17B1, offset: 0x14561, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC14ArrowDirectionOAESQAAWl', symObjAddr: 0x4A08, symBinAddr: 0x23B5C, symSize: 0x44 }
  - { offsetInCU: 0x17C4, offset: 0x14574, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC10ColorStyleOSHAASQWb', symObjAddr: 0x4A4C, symBinAddr: 0x23BA0, symSize: 0x4 }
  - { offsetInCU: 0x17D7, offset: 0x14587, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC10ColorStyleOAESQAAWl', symObjAddr: 0x4A50, symBinAddr: 0x23BA4, symSize: 0x44 }
  - { offsetInCU: 0x17EA, offset: 0x1459A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC14ArrowDirectionOMa', symObjAddr: 0x4E88, symBinAddr: 0x23FDC, symSize: 0x10 }
  - { offsetInCU: 0x17FD, offset: 0x145AD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC10ColorStyleOMa', symObjAddr: 0x4E98, symBinAddr: 0x23FEC, symSize: 0x10 }
  - { offsetInCU: 0x1810, offset: 0x145C0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7dismissyyFyycfU_TA.23', symObjAddr: 0x4EA8, symBinAddr: 0x23FFC, symSize: 0x8 }
  - { offsetInCU: 0x1823, offset: 0x145D3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC13animateFadeInyyFySbcfU2_ySbcfU_TA', symObjAddr: 0x4ED4, symBinAddr: 0x24028, symSize: 0x8 }
  - { offsetInCU: 0x1836, offset: 0x145E6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC14animateFadeOut33_1C39B2F52DDA14663AEF238AF411735ALL17completionHandleryyyc_tFyycfU_TA', symObjAddr: 0x4F00, symBinAddr: 0x24054, symSize: 0x14 }
  - { offsetInCU: 0x1866, offset: 0x14616, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC14animateFadeOut33_1C39B2F52DDA14663AEF238AF411735ALL17completionHandleryyyc_tFySbcfU0_TA', symObjAddr: 0x4F14, symBinAddr: 0x24068, symSize: 0x20 }
  - { offsetInCU: 0x1996, offset: 0x14746, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC10ColorStyleOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x2200, symBinAddr: 0x2138C, symSize: 0x14 }
  - { offsetInCU: 0x19F1, offset: 0x147A1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC10ColorStyleOSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0x225C, symBinAddr: 0x213E8, symSize: 0x28 }
  - { offsetInCU: 0x1D75, offset: 0x14B25, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7tagline7message10colorStyleACSSSg_AgC05ColorH0Otcfc', symObjAddr: 0x2C, symBinAddr: 0x1F1B8, symSize: 0x508 }
  - { offsetInCU: 0x1EC1, offset: 0x14C71, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7present2in13arrowPosition9directionySo6UIViewC_So7CGPointVAC14ArrowDirectionOtF', symObjAddr: 0x534, symBinAddr: 0x1F6C0, symSize: 0xF8 }
  - { offsetInCU: 0x1FCA, offset: 0x14D7A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC15displayDurationSdvg', symObjAddr: 0x62C, symBinAddr: 0x1F7B8, symSize: 0x44 }
  - { offsetInCU: 0x1FE7, offset: 0x14D97, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC15displayDurationSdvs', symObjAddr: 0x670, symBinAddr: 0x1F7FC, symSize: 0x50 }
  - { offsetInCU: 0x200D, offset: 0x14DBD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC15displayDurationSdvM', symObjAddr: 0x6C0, symBinAddr: 0x1F84C, symSize: 0x44 }
  - { offsetInCU: 0x2030, offset: 0x14DE0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC15displayDurationSdvM.resume.0', symObjAddr: 0x704, symBinAddr: 0x1F890, symSize: 0x4 }
  - { offsetInCU: 0x204F, offset: 0x14DFF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC10colorStyleAC05ColorF0Ovg', symObjAddr: 0x708, symBinAddr: 0x1F894, symSize: 0x44 }
  - { offsetInCU: 0x207E, offset: 0x14E2E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC10colorStyleAC05ColorF0Ovs', symObjAddr: 0x74C, symBinAddr: 0x1F8D8, symSize: 0x54 }
  - { offsetInCU: 0x20BF, offset: 0x14E6F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC10colorStyleAC05ColorF0OvM', symObjAddr: 0x7A0, symBinAddr: 0x1F92C, symSize: 0x48 }
  - { offsetInCU: 0x20E2, offset: 0x14E92, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC10colorStyleAC05ColorF0OvM.resume.0', symObjAddr: 0x7E8, symBinAddr: 0x1F974, symSize: 0x30 }
  - { offsetInCU: 0x2142, offset: 0x14EF2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7messageSSSgvs', symObjAddr: 0x824, symBinAddr: 0x1F9B0, symSize: 0xD8 }
  - { offsetInCU: 0x21B4, offset: 0x14F64, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7messageSSSgvM', symObjAddr: 0x8FC, symBinAddr: 0x1FA88, symSize: 0x74 }
  - { offsetInCU: 0x21F4, offset: 0x14FA4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7messageSSSgvM.resume.0', symObjAddr: 0x970, symBinAddr: 0x1FAFC, symSize: 0x170 }
  - { offsetInCU: 0x22B0, offset: 0x15060, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7taglineSSSgvs', symObjAddr: 0xB3C, symBinAddr: 0x1FCC8, symSize: 0xD0 }
  - { offsetInCU: 0x2322, offset: 0x150D2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7taglineSSSgvM', symObjAddr: 0xC0C, symBinAddr: 0x1FD98, symSize: 0x74 }
  - { offsetInCU: 0x2362, offset: 0x15112, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7taglineSSSgvM.resume.0', symObjAddr: 0xC80, symBinAddr: 0x1FE0C, symSize: 0x160 }
  - { offsetInCU: 0x2502, offset: 0x152B2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7present4fromySo6UIViewC_tF', symObjAddr: 0xDE0, symBinAddr: 0x1FF6C, symSize: 0x228 }
  - { offsetInCU: 0x263D, offset: 0x153ED, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7dismissyyF', symObjAddr: 0x1008, symBinAddr: 0x20194, symSize: 0x78 }
  - { offsetInCU: 0x26A5, offset: 0x15455, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7dismissyyFyycfU_', symObjAddr: 0x2D78, symBinAddr: 0x21F04, symSize: 0xBC }
  - { offsetInCU: 0x272E, offset: 0x154DE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC13animateFadeInyyF', symObjAddr: 0x1080, symBinAddr: 0x2020C, symSize: 0x434 }
  - { offsetInCU: 0x2877, offset: 0x15627, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC13animateFadeInyyFyycfU_', symObjAddr: 0x2ED4, symBinAddr: 0x22060, symSize: 0x224 }
  - { offsetInCU: 0x2956, offset: 0x15706, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC13animateFadeInyyFyycfU0_', symObjAddr: 0x3134, symBinAddr: 0x222C0, symSize: 0x220 }
  - { offsetInCU: 0x2A1C, offset: 0x157CC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC13animateFadeInyyFyycfU1_', symObjAddr: 0x3388, symBinAddr: 0x22514, symSize: 0x80 }
  - { offsetInCU: 0x2A49, offset: 0x157F9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC13animateFadeInyyFySbcfU2_', symObjAddr: 0x3428, symBinAddr: 0x225B4, symSize: 0x140 }
  - { offsetInCU: 0x2A94, offset: 0x15844, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC13animateFadeInyyFySbcfU2_ySbcfU_', symObjAddr: 0x35A0, symBinAddr: 0x2272C, symSize: 0xB4 }
  - { offsetInCU: 0x2ADA, offset: 0x1588A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC14animateFadeOut33_1C39B2F52DDA14663AEF238AF411735ALL17completionHandleryyyc_tF', symObjAddr: 0x14B4, symBinAddr: 0x20640, symSize: 0x168 }
  - { offsetInCU: 0x2BB2, offset: 0x15962, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC12updateColors33_1C39B2F52DDA14663AEF238AF411735ALLyyF', symObjAddr: 0x161C, symBinAddr: 0x207A8, symSize: 0x238 }
  - { offsetInCU: 0x2DB6, offset: 0x15B66, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC31layoutSubviewsAndDetermineFrame33_1C39B2F52DDA14663AEF238AF411735ALLSo6CGRectVyF', symObjAddr: 0x1854, symBinAddr: 0x209E0, symSize: 0x494 }
  - { offsetInCU: 0x30FA, offset: 0x15EAA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC3set33_1C39B2F52DDA14663AEF238AF411735ALL7message7taglineySSSg_AHtF', symObjAddr: 0x1CE8, symBinAddr: 0x20E74, symSize: 0x338 }
  - { offsetInCU: 0x3307, offset: 0x160B7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC24scheduleAutomaticFadeout33_1C39B2F52DDA14663AEF238AF411735ALLyyF', symObjAddr: 0x2020, symBinAddr: 0x211AC, symSize: 0x14C }
  - { offsetInCU: 0x33CD, offset: 0x1617D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC32cancelAllScheduledFadeOutMethods33_1C39B2F52DDA14663AEF238AF411735ALLyyF', symObjAddr: 0x216C, symBinAddr: 0x212F8, symSize: 0x64 }
  - { offsetInCU: 0x33F2, offset: 0x161A2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC14ArrowDirectionO8rawValueSuvg', symObjAddr: 0x21D0, symBinAddr: 0x2135C, symSize: 0x4 }
  - { offsetInCU: 0x3417, offset: 0x161C7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC10ColorStyleO8rawValueAESgSu_tcfC', symObjAddr: 0x21DC, symBinAddr: 0x21368, symSize: 0x20 }
  - { offsetInCU: 0x3434, offset: 0x161E4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC10ColorStyleO8rawValueSuvg', symObjAddr: 0x21FC, symBinAddr: 0x21388, symSize: 0x4 }
  - { offsetInCU: 0x3512, offset: 0x162C2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC9textLabelSo7UILabelCvg', symObjAddr: 0x29A4, symBinAddr: 0x21B30, symSize: 0x10 }
  - { offsetInCU: 0x3533, offset: 0x162E3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewCACycfC', symObjAddr: 0x29B4, symBinAddr: 0x21B40, symSize: 0x20 }
  - { offsetInCU: 0x3546, offset: 0x162F6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewCACycfc', symObjAddr: 0x29D4, symBinAddr: 0x21B60, symSize: 0x1C }
  - { offsetInCU: 0x3586, offset: 0x16336, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC7tagline7message10colorStyleACSSSg_AgC05ColorH0OtcfC', symObjAddr: 0x2A08, symBinAddr: 0x21B94, symSize: 0x60 }
  - { offsetInCU: 0x3599, offset: 0x16349, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC5coderACSgSo7NSCoderC_tcfC', symObjAddr: 0x2AEC, symBinAddr: 0x21C78, symSize: 0x44 }
  - { offsetInCU: 0x35AC, offset: 0x1635C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC5coderACSgSo7NSCoderC_tcfc', symObjAddr: 0x2B30, symBinAddr: 0x21CBC, symSize: 0x4 }
  - { offsetInCU: 0x35C6, offset: 0x16376, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewCfD', symObjAddr: 0x2B5C, symBinAddr: 0x21CE8, symSize: 0x54 }
  - { offsetInCU: 0x3647, offset: 0x163F7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC5frameACSo6CGRectV_tcfC', symObjAddr: 0x386C, symBinAddr: 0x229C0, symSize: 0x50 }
  - { offsetInCU: 0x365A, offset: 0x1640A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC5frameACSo6CGRectV_tcfc', symObjAddr: 0x38BC, symBinAddr: 0x22A10, symSize: 0x2C }
  - { offsetInCU: 0x36D4, offset: 0x16484, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC5coderACSgSo7NSCoderC_tcfcTf4dn_n', symObjAddr: 0x4210, symBinAddr: 0x23364, symSize: 0x1E0 }
  - { offsetInCU: 0x374B, offset: 0x164FB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13FBTooltipViewC4drawyySo6CGRectVFTf4dn_n', symObjAddr: 0x43F0, symBinAddr: 0x23544, symSize: 0x614 }
  - { offsetInCU: 0x2B, offset: 0x16ADE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVyACyAA0d7ManagerdE0CSg_s5Error_pSgtccfC', symObjAddr: 0x0, symBinAddr: 0x240D4, symSize: 0x44 }
  - { offsetInCU: 0xA6, offset: 0x16B59, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0xCC, symBinAddr: 0x241A0, symSize: 0x4 }
  - { offsetInCU: 0xC5, offset: 0x16B78, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0xCC, symBinAddr: 0x241A0, symSize: 0x4 }
  - { offsetInCU: 0xE8, offset: 0x16B9B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVMa', symObjAddr: 0x44, symBinAddr: 0x24118, symSize: 0x3C }
  - { offsetInCU: 0xFB, offset: 0x16BAE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVwCP', symObjAddr: 0xD0, symBinAddr: 0x241A4, symSize: 0x90 }
  - { offsetInCU: 0x10E, offset: 0x16BC1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVwxx', symObjAddr: 0x160, symBinAddr: 0x24234, symSize: 0x48 }
  - { offsetInCU: 0x121, offset: 0x16BD4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVwcp', symObjAddr: 0x1A8, symBinAddr: 0x2427C, symSize: 0x68 }
  - { offsetInCU: 0x134, offset: 0x16BE7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVwca', symObjAddr: 0x210, symBinAddr: 0x242E4, symSize: 0x74 }
  - { offsetInCU: 0x147, offset: 0x16BFA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVwtk', symObjAddr: 0x284, symBinAddr: 0x24358, symSize: 0x64 }
  - { offsetInCU: 0x15A, offset: 0x16C0D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVwta', symObjAddr: 0x2E8, symBinAddr: 0x243BC, symSize: 0x6C }
  - { offsetInCU: 0x16D, offset: 0x16C20, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVwet', symObjAddr: 0x354, symBinAddr: 0x24428, symSize: 0xC }
  - { offsetInCU: 0x180, offset: 0x16C33, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVwst', symObjAddr: 0x3DC, symBinAddr: 0x244B0, symSize: 0xC }
  - { offsetInCU: 0x193, offset: 0x16C46, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVMr', symObjAddr: 0x460, symBinAddr: 0x24534, symSize: 0x74 }
  - { offsetInCU: 0x291, offset: 0x16D44, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVyACyAA0d7ManagerdE0CSg_s5Error_pSgtccfC', symObjAddr: 0x0, symBinAddr: 0x240D4, symSize: 0x44 }
  - { offsetInCU: 0x2BA, offset: 0x16D6D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerV14callAsFunctionyyAA0d7ManagerdE0CSg_s5Error_pSgtF', symObjAddr: 0x80, symBinAddr: 0x24154, symSize: 0x48 }
  - { offsetInCU: 0x2FB, offset: 0x16DAE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerV2eeoiySbAC_ACtFZ', symObjAddr: 0xC8, symBinAddr: 0x2419C, symSize: 0x4 }
  - { offsetInCU: 0x27, offset: 0x16E41, size: 0x8, addend: 0x0, symName: '_$sSo20FBSDKInternalUtilityC13FBSDKLoginKit29UserInterfaceElementProvidingA2cDP21topMostViewControllerSo06UIViewL0CSgyFTW', symObjAddr: 0x0, symBinAddr: 0x245A8, symSize: 0x24 }
  - { offsetInCU: 0x7E, offset: 0x16E98, size: 0x8, addend: 0x0, symName: '_$sSo20FBSDKInternalUtilityC13FBSDKLoginKit29UserInterfaceElementProvidingA2cDP21topMostViewControllerSo06UIViewL0CSgyFTW', symObjAddr: 0x0, symBinAddr: 0x245A8, symSize: 0x24 }
  - { offsetInCU: 0xC5, offset: 0x16EDF, size: 0x8, addend: 0x0, symName: '_$sSo20FBSDKInternalUtilityC13FBSDKLoginKit29UserInterfaceElementProvidingA2cDP14viewController3forSo06UIViewJ0CSgSo0L0C_tFTW', symObjAddr: 0x24, symBinAddr: 0x245CC, symSize: 0x28 }
  - { offsetInCU: 0x107, offset: 0x16F21, size: 0x8, addend: 0x0, symName: '_$sSo20FBSDKInternalUtilityC13FBSDKLoginKit28UserInterfaceStringProvidingA2cDP16bundleForStringsSo8NSBundleCvgTW', symObjAddr: 0x4C, symBinAddr: 0x245F4, symSize: 0x24 }
  - { offsetInCU: 0x49, offset: 0x17041, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE17loginButtonDidTapABvpZ', symObjAddr: 0x24C8, symBinAddr: 0x644D0, symSize: 0x0 }
  - { offsetInCU: 0x63, offset: 0x1705B, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE17smartLoginServiceABvpZ', symObjAddr: 0x24D0, symBinAddr: 0x644D8, symSize: 0x0 }
  - { offsetInCU: 0x7D, offset: 0x17075, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE16sessionAuthStartABvpZ', symObjAddr: 0x24D8, symBinAddr: 0x644E0, symSize: 0x0 }
  - { offsetInCU: 0x97, offset: 0x1708F, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE14sessionAuthEndABvpZ', symObjAddr: 0x24E0, symBinAddr: 0x644E8, symSize: 0x0 }
  - { offsetInCU: 0xB1, offset: 0x170A9, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE22sessionAuthMethodStartABvpZ', symObjAddr: 0x24E8, symBinAddr: 0x644F0, symSize: 0x0 }
  - { offsetInCU: 0xCB, offset: 0x170C3, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE20sessionAuthMethodEndABvpZ', symObjAddr: 0x24F0, symBinAddr: 0x644F8, symSize: 0x0 }
  - { offsetInCU: 0xE5, offset: 0x170DD, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE20sessionAuthHeartbeatABvpZ', symObjAddr: 0x24F8, symBinAddr: 0x64500, symSize: 0x0 }
  - { offsetInCU: 0xF3, offset: 0x170EB, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE17loginButtonDidTap_WZ', symObjAddr: 0x0, symBinAddr: 0x24618, symSize: 0x34 }
  - { offsetInCU: 0x10D, offset: 0x17105, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE17loginButtonDidTapABvau', symObjAddr: 0x34, symBinAddr: 0x2464C, symSize: 0x40 }
  - { offsetInCU: 0x12B, offset: 0x17123, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE17smartLoginService_WZ', symObjAddr: 0x90, symBinAddr: 0x246A8, symSize: 0x34 }
  - { offsetInCU: 0x145, offset: 0x1713D, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE17smartLoginServiceABvau', symObjAddr: 0xC4, symBinAddr: 0x246DC, symSize: 0x40 }
  - { offsetInCU: 0x163, offset: 0x1715B, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE16sessionAuthStart_WZ', symObjAddr: 0x120, symBinAddr: 0x24738, symSize: 0x34 }
  - { offsetInCU: 0x17D, offset: 0x17175, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE16sessionAuthStartABvau', symObjAddr: 0x154, symBinAddr: 0x2476C, symSize: 0x40 }
  - { offsetInCU: 0x19B, offset: 0x17193, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE14sessionAuthEnd_WZ', symObjAddr: 0x1B0, symBinAddr: 0x247C8, symSize: 0x34 }
  - { offsetInCU: 0x1B5, offset: 0x171AD, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE14sessionAuthEndABvau', symObjAddr: 0x1E4, symBinAddr: 0x247FC, symSize: 0x40 }
  - { offsetInCU: 0x1D3, offset: 0x171CB, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE22sessionAuthMethodStart_WZ', symObjAddr: 0x240, symBinAddr: 0x24858, symSize: 0x34 }
  - { offsetInCU: 0x1ED, offset: 0x171E5, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE22sessionAuthMethodStartABvau', symObjAddr: 0x274, symBinAddr: 0x2488C, symSize: 0x40 }
  - { offsetInCU: 0x20B, offset: 0x17203, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE20sessionAuthMethodEnd_WZ', symObjAddr: 0x2D0, symBinAddr: 0x248E8, symSize: 0x34 }
  - { offsetInCU: 0x225, offset: 0x1721D, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE20sessionAuthMethodEndABvau', symObjAddr: 0x304, symBinAddr: 0x2491C, symSize: 0x40 }
  - { offsetInCU: 0x243, offset: 0x1723B, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE20sessionAuthHeartbeat_WZ', symObjAddr: 0x360, symBinAddr: 0x24978, symSize: 0x34 }
  - { offsetInCU: 0x25D, offset: 0x17255, size: 0x8, addend: 0x0, symName: '_$sSo17FBSDKAppEventNamea13FBSDKLoginKitE20sessionAuthHeartbeatABvau', symObjAddr: 0x394, symBinAddr: 0x249AC, symSize: 0x40 }
  - { offsetInCU: 0x27, offset: 0x1734A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit21LoginCompleterFactoryV06createcD013urlParameters5appIDAA0C10Completing_pSDySSypG_SStF', symObjAddr: 0x0, symBinAddr: 0x24A40, symSize: 0x64 }
  - { offsetInCU: 0xA0, offset: 0x173C3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit21LoginCompleterFactoryVAA0cdE8ProtocolA2aDP06createcD013urlParameters5appIDAA0C10Completing_pSDySSypG_SStFTW', symObjAddr: 0x68, symBinAddr: 0x24AA8, symSize: 0x64 }
  - { offsetInCU: 0xFC, offset: 0x1741F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit21LoginCompleterFactoryVMa', symObjAddr: 0xCC, symBinAddr: 0x24B0C, symSize: 0x10 }
  - { offsetInCU: 0x1D1, offset: 0x174F4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit21LoginCompleterFactoryV06createcD013urlParameters5appIDAA0C10Completing_pSDySSypG_SStF', symObjAddr: 0x0, symBinAddr: 0x24A40, symSize: 0x64 }
  - { offsetInCU: 0x218, offset: 0x1753B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit21LoginCompleterFactoryVACycfC', symObjAddr: 0x64, symBinAddr: 0x24AA4, symSize: 0x4 }
  - { offsetInCU: 0x8E, offset: 0x17619, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC5nonceSSvgTo', symObjAddr: 0x4, symBinAddr: 0x24B38, symSize: 0x4C }
  - { offsetInCU: 0xDB, offset: 0x17666, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC8trackingAA0C8TrackingOvgTo', symObjAddr: 0x88, symBinAddr: 0x24BBC, symSize: 0x10 }
  - { offsetInCU: 0xFB, offset: 0x17686, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC8trackingAA0C8TrackingOvgTo', symObjAddr: 0x88, symBinAddr: 0x24BBC, symSize: 0x10 }
  - { offsetInCU: 0x129, offset: 0x176B4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC20requestedPermissionsShyAA12FBPermissionCGvgTo', symObjAddr: 0xA8, symBinAddr: 0x24BDC, symSize: 0x64 }
  - { offsetInCU: 0x176, offset: 0x17701, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC15messengerPageIdSSSgvgTo', symObjAddr: 0x11C, symBinAddr: 0x24C50, symSize: 0x5C }
  - { offsetInCU: 0x1BB, offset: 0x17746, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC8authTypeSo0a4AuthF0aSgvgTo', symObjAddr: 0x1B0, symBinAddr: 0x24CE4, symSize: 0x10 }
  - { offsetInCU: 0x1DA, offset: 0x17765, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC8authTypeSo0a4AuthF0aSgvgTo', symObjAddr: 0x1B0, symBinAddr: 0x24CE4, symSize: 0x10 }
  - { offsetInCU: 0x20A, offset: 0x17795, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC12codeVerifierAA04CodeF0CvgTo', symObjAddr: 0x1F0, symBinAddr: 0x24D24, symSize: 0x10 }
  - { offsetInCU: 0x22A, offset: 0x177B5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC12codeVerifierAA04CodeF0CvgTo', symObjAddr: 0x1F0, symBinAddr: 0x24D24, symSize: 0x10 }
  - { offsetInCU: 0x297, offset: 0x17822, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking5nonce15messengerPageIdACSgSaySSG_AA0C8TrackingOS2SSgtcfcTo', symObjAddr: 0x3F0, symBinAddr: 0x24F24, symSize: 0x128 }
  - { offsetInCU: 0x349, offset: 0x178D4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking5nonce15messengerPageId8authTypeACSgSaySSG_AA0C8TrackingOS2SSgSo0a4AuthL0aSgtcfcTo', symObjAddr: 0x6AC, symBinAddr: 0x251E0, symSize: 0xAC }
  - { offsetInCU: 0x3AC, offset: 0x17937, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking5nonceACSgSaySSG_AA0C8TrackingOSStcfcTo', symObjAddr: 0x8A8, symBinAddr: 0x253DC, symSize: 0xC4 }
  - { offsetInCU: 0x43D, offset: 0x179C8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking15messengerPageIdACSgSaySSG_AA0C8TrackingOSSSgtcfcTo', symObjAddr: 0xAFC, symBinAddr: 0x25630, symSize: 0x6C }
  - { offsetInCU: 0x4AA, offset: 0x17A35, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking15messengerPageId8authTypeACSgSaySSG_AA0C8TrackingOSSSgSo0a4AuthK0aSgtcfcTo', symObjAddr: 0xD14, symBinAddr: 0x25848, symSize: 0x88 }
  - { offsetInCU: 0x52B, offset: 0x17AB6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking5nonce15messengerPageId8authType12codeVerifierACSgSaySSG_AA0C8TrackingOS2SSgSo0a4AuthL0aSgAA04CodeN0CtcfcTo', symObjAddr: 0x153C, symBinAddr: 0x26070, symSize: 0xC4 }
  - { offsetInCU: 0x584, offset: 0x17B0F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8trackingACSgSaySSG_AA0C8TrackingOtcfcTo', symObjAddr: 0x17F8, symBinAddr: 0x2632C, symSize: 0x108 }
  - { offsetInCU: 0x5FC, offset: 0x17B87, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC8trackingACSgAA0C8TrackingO_tcfcTo', symObjAddr: 0x19F4, symBinAddr: 0x26528, symSize: 0x78 }
  - { offsetInCU: 0x661, offset: 0x17BEC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationCACycfcTo', symObjAddr: 0x1AB8, symBinAddr: 0x265EC, symSize: 0x2C }
  - { offsetInCU: 0x6FA, offset: 0x17C85, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking5nonce15messengerPageId8authType12codeVerifierACSgShy09FBSDKCoreB010PermissionOG_AA0C8TrackingOS2SSgSo0a4AuthL0aSgAA04CodeN0CtcfCTf4nnnnnnd_n', symObjAddr: 0x1B88, symBinAddr: 0x266BC, symSize: 0x288 }
  - { offsetInCU: 0xC0C, offset: 0x18197, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationCfETo', symObjAddr: 0x1B18, symBinAddr: 0x2664C, symSize: 0x70 }
  - { offsetInCU: 0xDCF, offset: 0x1835A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationCMa', symObjAddr: 0x1E94, symBinAddr: 0x26944, symSize: 0x20 }
  - { offsetInCU: 0x110C, offset: 0x18697, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking5nonce15messengerPageId8authType12codeVerifierACSgShy09FBSDKCoreB010PermissionOG_AA0C8TrackingOS2SSgSo0a4AuthL0aSgAA04CodeN0CtcfC', symObjAddr: 0x0, symBinAddr: 0x24B34, symSize: 0x4 }
  - { offsetInCU: 0x1136, offset: 0x186C1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC5nonceSSvg', symObjAddr: 0x50, symBinAddr: 0x24B84, symSize: 0x38 }
  - { offsetInCU: 0x1165, offset: 0x186F0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC8trackingAA0C8TrackingOvg', symObjAddr: 0x98, symBinAddr: 0x24BCC, symSize: 0x10 }
  - { offsetInCU: 0x1192, offset: 0x1871D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC20requestedPermissionsShyAA12FBPermissionCGvg', symObjAddr: 0x10C, symBinAddr: 0x24C40, symSize: 0x10 }
  - { offsetInCU: 0x11BF, offset: 0x1874A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC15messengerPageIdSSSgvg', symObjAddr: 0x178, symBinAddr: 0x24CAC, symSize: 0x38 }
  - { offsetInCU: 0x11EE, offset: 0x18779, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC8authTypeSo0a4AuthF0aSgvg', symObjAddr: 0x1C0, symBinAddr: 0x24CF4, symSize: 0x30 }
  - { offsetInCU: 0x121D, offset: 0x187A8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC12codeVerifierAA04CodeF0Cvg', symObjAddr: 0x200, symBinAddr: 0x24D34, symSize: 0x10 }
  - { offsetInCU: 0x128A, offset: 0x18815, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking5nonce15messengerPageIdACSgSaySSG_AA0C8TrackingOS2SSgtcfC', symObjAddr: 0x210, symBinAddr: 0x24D44, symSize: 0xF8 }
  - { offsetInCU: 0x12CF, offset: 0x1885A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking5nonce15messengerPageIdACSgSaySSG_AA0C8TrackingOS2SSgtcfc', symObjAddr: 0x308, symBinAddr: 0x24E3C, symSize: 0xE8 }
  - { offsetInCU: 0x1310, offset: 0x1889B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking5nonce15messengerPageId8authTypeACSgSaySSG_AA0C8TrackingOS2SSgSo0a4AuthL0aSgtcfC', symObjAddr: 0x518, symBinAddr: 0x2504C, symSize: 0x78 }
  - { offsetInCU: 0x1329, offset: 0x188B4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking5nonce15messengerPageId8authTypeACSgSaySSG_AA0C8TrackingOS2SSgSo0a4AuthL0aSgtcfc', symObjAddr: 0x590, symBinAddr: 0x250C4, symSize: 0x11C }
  - { offsetInCU: 0x13DE, offset: 0x18969, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking5nonceACSgSaySSG_AA0C8TrackingOSStcfC', symObjAddr: 0x758, symBinAddr: 0x2528C, symSize: 0xB0 }
  - { offsetInCU: 0x141E, offset: 0x189A9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking5nonceACSgSaySSG_AA0C8TrackingOSStcfc', symObjAddr: 0x808, symBinAddr: 0x2533C, symSize: 0xA0 }
  - { offsetInCU: 0x1456, offset: 0x189E1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking15messengerPageIdACSgSaySSG_AA0C8TrackingOSSSgtcfC', symObjAddr: 0x96C, symBinAddr: 0x254A0, symSize: 0x58 }
  - { offsetInCU: 0x1469, offset: 0x189F4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking15messengerPageIdACSgSaySSG_AA0C8TrackingOSSSgtcfc', symObjAddr: 0x9C4, symBinAddr: 0x254F8, symSize: 0x138 }
  - { offsetInCU: 0x14B9, offset: 0x18A44, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking15messengerPageId8authTypeACSgSaySSG_AA0C8TrackingOSSSgSo0a4AuthK0aSgtcfC', symObjAddr: 0xB68, symBinAddr: 0x2569C, symSize: 0x60 }
  - { offsetInCU: 0x14CC, offset: 0x18A57, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking15messengerPageId8authTypeACSgSaySSG_AA0C8TrackingOSSSgSo0a4AuthK0aSgtcfc', symObjAddr: 0xBC8, symBinAddr: 0x256FC, symSize: 0x14C }
  - { offsetInCU: 0x152B, offset: 0x18AB6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking5nonce15messengerPageId8authType12codeVerifierACSgSaySSG_AA0C8TrackingOS2SSgSo0a4AuthL0aSgAA04CodeN0CtcfC', symObjAddr: 0xD9C, symBinAddr: 0x258D0, symSize: 0x88 }
  - { offsetInCU: 0x15BC, offset: 0x18B47, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8tracking5nonce15messengerPageId8authType12codeVerifierACSgSaySSG_AA0C8TrackingOS2SSgSo0a4AuthL0aSgAA04CodeN0Ctcfc', symObjAddr: 0xE24, symBinAddr: 0x25958, symSize: 0x500 }
  - { offsetInCU: 0x18A2, offset: 0x18E2D, size: 0x8, addend: 0x0, symName: '_$sSTsSQ7ElementRpzrlE8containsySbABFSaySo18FBSDKLoginAuthTypeaG_Tg5', symObjAddr: 0x1324, symBinAddr: 0x25E58, symSize: 0x154 }
  - { offsetInCU: 0x1A62, offset: 0x18FED, size: 0x8, addend: 0x0, symName: '_$sSTsSQ7ElementRpzrlE8containsySbABFSaySSG_Tg5', symObjAddr: 0x1478, symBinAddr: 0x25FAC, symSize: 0xC4 }
  - { offsetInCU: 0x1BF1, offset: 0x1917C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8trackingACSgSaySSG_AA0C8TrackingOtcfC', symObjAddr: 0x1600, symBinAddr: 0x26134, symSize: 0x100 }
  - { offsetInCU: 0x1C2D, offset: 0x191B8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC11permissions8trackingACSgSaySSG_AA0C8TrackingOtcfc', symObjAddr: 0x1700, symBinAddr: 0x26234, symSize: 0xF8 }
  - { offsetInCU: 0x1C79, offset: 0x19204, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC8trackingACSgAA0C8TrackingO_tcfC', symObjAddr: 0x1900, symBinAddr: 0x26434, symSize: 0x80 }
  - { offsetInCU: 0x1CA9, offset: 0x19234, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationC8trackingACSgAA0C8TrackingO_tcfc', symObjAddr: 0x1980, symBinAddr: 0x264B4, symSize: 0x74 }
  - { offsetInCU: 0x1CCF, offset: 0x1925A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationCACycfC', symObjAddr: 0x1A6C, symBinAddr: 0x265A0, symSize: 0x20 }
  - { offsetInCU: 0x1CE2, offset: 0x1926D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationCACycfc', symObjAddr: 0x1A8C, symBinAddr: 0x265C0, symSize: 0x2C }
  - { offsetInCU: 0x1D36, offset: 0x192C1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginConfigurationCfD', symObjAddr: 0x1AE4, symBinAddr: 0x26618, symSize: 0x34 }
  - { offsetInCU: 0x220, offset: 0x195B0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV10Foundation13CustomNSErrorAadEP11errorDomainSSvgZTW', symObjAddr: 0x23C, symBinAddr: 0x26BB4, symSize: 0x5C }
  - { offsetInCU: 0x255, offset: 0x195E5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV10Foundation13CustomNSErrorAadEP9errorCodeSivgTW', symObjAddr: 0x298, symBinAddr: 0x26C10, symSize: 0x8 }
  - { offsetInCU: 0x274, offset: 0x19604, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV10Foundation13CustomNSErrorAadEP9errorCodeSivgTW', symObjAddr: 0x298, symBinAddr: 0x26C10, symSize: 0x8 }
  - { offsetInCU: 0x285, offset: 0x19615, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV10Foundation13CustomNSErrorAadEP13errorUserInfoSDySSypGvgTW', symObjAddr: 0x2A0, symBinAddr: 0x26C18, symSize: 0x8 }
  - { offsetInCU: 0x2A4, offset: 0x19634, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV10Foundation13CustomNSErrorAadEP13errorUserInfoSDySSypGvgTW', symObjAddr: 0x2A0, symBinAddr: 0x26C18, symSize: 0x8 }
  - { offsetInCU: 0x2B5, offset: 0x19645, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVSHAASH9hashValueSivgTW', symObjAddr: 0x2A8, symBinAddr: 0x26C20, symSize: 0x44 }
  - { offsetInCU: 0x38F, offset: 0x1971F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0x2EC, symBinAddr: 0x26C64, symSize: 0x28 }
  - { offsetInCU: 0x3FD, offset: 0x1978D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x3AC, symBinAddr: 0x26D24, symSize: 0x34 }
  - { offsetInCU: 0x4A6, offset: 0x19836, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14LoginErrorCodeOSYAASY8rawValuexSg03RawG0Qz_tcfCTW', symObjAddr: 0x4A4, symBinAddr: 0x26E1C, symSize: 0x30 }
  - { offsetInCU: 0x4D5, offset: 0x19865, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14LoginErrorCodeOSYAASY8rawValue03RawG0QzvgTW', symObjAddr: 0x4D4, symBinAddr: 0x26E4C, symSize: 0xC }
  - { offsetInCU: 0x4F1, offset: 0x19881, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14LoginErrorCodeO8rawValueACSgSi_tcfCTf4nd_n', symObjAddr: 0x4F4, symBinAddr: 0x26E6C, symSize: 0x18 }
  - { offsetInCU: 0x555, offset: 0x198E5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV10Foundation13CustomNSErrorAAs0D0PWb', symObjAddr: 0x50C, symBinAddr: 0x26E84, symSize: 0x4 }
  - { offsetInCU: 0x568, offset: 0x198F8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVACs0D0AAWl', symObjAddr: 0x510, symBinAddr: 0x26E88, symSize: 0x44 }
  - { offsetInCU: 0x57B, offset: 0x1990B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVSHAASQWb', symObjAddr: 0x554, symBinAddr: 0x26ECC, symSize: 0x4 }
  - { offsetInCU: 0x58E, offset: 0x1991E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVACSQAAWl', symObjAddr: 0x558, symBinAddr: 0x26ED0, symSize: 0x44 }
  - { offsetInCU: 0x5A1, offset: 0x19931, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14LoginErrorCodeOSHAASQWb', symObjAddr: 0x59C, symBinAddr: 0x26F14, symSize: 0x4 }
  - { offsetInCU: 0x5B4, offset: 0x19944, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14LoginErrorCodeOACSQAAWl', symObjAddr: 0x5A0, symBinAddr: 0x26F18, symSize: 0x44 }
  - { offsetInCU: 0x5C7, offset: 0x19957, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVwxx', symObjAddr: 0x5E8, symBinAddr: 0x26F60, symSize: 0x28 }
  - { offsetInCU: 0x5DA, offset: 0x1996A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVwca', symObjAddr: 0x650, symBinAddr: 0x26FC8, symSize: 0x64 }
  - { offsetInCU: 0x5ED, offset: 0x1997D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVwta', symObjAddr: 0x6C8, symBinAddr: 0x2702C, symSize: 0x44 }
  - { offsetInCU: 0x600, offset: 0x19990, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVwet', symObjAddr: 0x70C, symBinAddr: 0x27070, symSize: 0x48 }
  - { offsetInCU: 0x613, offset: 0x199A3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVwst', symObjAddr: 0x754, symBinAddr: 0x270B8, symSize: 0x40 }
  - { offsetInCU: 0x626, offset: 0x199B6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVMa', symObjAddr: 0x794, symBinAddr: 0x270F8, symSize: 0x10 }
  - { offsetInCU: 0x639, offset: 0x199C9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14LoginErrorCodeOMa', symObjAddr: 0x7A4, symBinAddr: 0x27108, symSize: 0x10 }
  - { offsetInCU: 0x64C, offset: 0x199DC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVAC10Foundation13CustomNSErrorAAWl', symObjAddr: 0x7B4, symBinAddr: 0x27118, symSize: 0x44 }
  - { offsetInCU: 0x6BC, offset: 0x19A4C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0x314, symBinAddr: 0x26C8C, symSize: 0x40 }
  - { offsetInCU: 0x752, offset: 0x19AE2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVs0D0AAsADP9_userInfoyXlSgvgTW', symObjAddr: 0x3A4, symBinAddr: 0x26D1C, symSize: 0x4 }
  - { offsetInCU: 0x76D, offset: 0x19AFD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVs0D0AAsADP19_getEmbeddedNSErroryXlSgyFTW', symObjAddr: 0x3A8, symBinAddr: 0x26D20, symSize: 0x4 }
  - { offsetInCU: 0x7ED, offset: 0x19B7D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14LoginErrorCodeOSHAASH9hashValueSivgTW', symObjAddr: 0x3F8, symBinAddr: 0x26D70, symSize: 0x44 }
  - { offsetInCU: 0x894, offset: 0x19C24, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14LoginErrorCodeOSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0x43C, symBinAddr: 0x26DB4, symSize: 0x28 }
  - { offsetInCU: 0x8E3, offset: 0x19C73, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14LoginErrorCodeOSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0x464, symBinAddr: 0x26DDC, symSize: 0x40 }
  - { offsetInCU: 0x968, offset: 0x19CF8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14LoginErrorCodeOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x4E0, symBinAddr: 0x26E58, symSize: 0x14 }
  - { offsetInCU: 0x9FB, offset: 0x19D8B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVs0D0AAsADP7_domainSSvgTW', symObjAddr: 0x354, symBinAddr: 0x26CCC, symSize: 0x28 }
  - { offsetInCU: 0xA17, offset: 0x19DA7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorVs0D0AAsADP5_codeSivgTW', symObjAddr: 0x37C, symBinAddr: 0x26CF4, symSize: 0x28 }
  - { offsetInCU: 0xA9F, offset: 0x19E2F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV03_nsD0So7NSErrorCSgvg', symObjAddr: 0x0, symBinAddr: 0x26978, symSize: 0x28 }
  - { offsetInCU: 0xAB2, offset: 0x19E42, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV9errorCodeSivg', symObjAddr: 0x28, symBinAddr: 0x269A0, symSize: 0x8 }
  - { offsetInCU: 0xAC6, offset: 0x19E56, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV13errorUserInfoSDySSypGvg', symObjAddr: 0x30, symBinAddr: 0x269A8, symSize: 0x8 }
  - { offsetInCU: 0xAE5, offset: 0x19E75, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV03_nsD0ACSo7NSErrorC_tcfC', symObjAddr: 0x38, symBinAddr: 0x269B0, symSize: 0xA0 }
  - { offsetInCU: 0xB08, offset: 0x19E98, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV_8userInfoAcA0cD4CodeO_SDySSypGtcfC', symObjAddr: 0xD8, symBinAddr: 0x26A50, symSize: 0xC }
  - { offsetInCU: 0xB38, offset: 0x19EC8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14LoginErrorCodeO8rawValueSivg', symObjAddr: 0xE4, symBinAddr: 0x26A5C, symSize: 0x4 }
  - { offsetInCU: 0xB5D, offset: 0x19EED, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV11errorDomainSSvgZ', symObjAddr: 0xE8, symBinAddr: 0x26A60, symSize: 0x5C }
  - { offsetInCU: 0xB84, offset: 0x19F14, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV8reservedAA0cD4CodeOvgZ', symObjAddr: 0x144, symBinAddr: 0x26ABC, symSize: 0x8 }
  - { offsetInCU: 0xBA5, offset: 0x19F35, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV7unknownAA0cD4CodeOvgZ', symObjAddr: 0x14C, symBinAddr: 0x26AC4, symSize: 0x8 }
  - { offsetInCU: 0xBC6, offset: 0x19F56, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV15passwordChangedAA0cD4CodeOvgZ', symObjAddr: 0x154, symBinAddr: 0x26ACC, symSize: 0x8 }
  - { offsetInCU: 0xBE7, offset: 0x19F77, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV16userCheckpointedAA0cD4CodeOvgZ', symObjAddr: 0x15C, symBinAddr: 0x26AD4, symSize: 0x8 }
  - { offsetInCU: 0xC08, offset: 0x19F98, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV12userMismatchAA0cD4CodeOvgZ', symObjAddr: 0x164, symBinAddr: 0x26ADC, symSize: 0x8 }
  - { offsetInCU: 0xC29, offset: 0x19FB9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV15unconfirmedUserAA0cD4CodeOvgZ', symObjAddr: 0x16C, symBinAddr: 0x26AE4, symSize: 0x8 }
  - { offsetInCU: 0xC4A, offset: 0x19FDA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV24systemAccountAppDisabledAA0cD4CodeOvgZ', symObjAddr: 0x174, symBinAddr: 0x26AEC, symSize: 0x8 }
  - { offsetInCU: 0xC6B, offset: 0x19FFB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV24systemAccountUnavailableAA0cD4CodeOvgZ', symObjAddr: 0x17C, symBinAddr: 0x26AF4, symSize: 0x8 }
  - { offsetInCU: 0xC8C, offset: 0x1A01C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV18badChallengeStringAA0cD4CodeOvgZ', symObjAddr: 0x184, symBinAddr: 0x26AFC, symSize: 0x8 }
  - { offsetInCU: 0xCAD, offset: 0x1A03D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV14invalidIDTokenAA0cD4CodeOvgZ', symObjAddr: 0x18C, symBinAddr: 0x26B04, symSize: 0x8 }
  - { offsetInCU: 0xCCE, offset: 0x1A05E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV18missingAccessTokenAA0cD4CodeOvgZ', symObjAddr: 0x194, symBinAddr: 0x26B0C, symSize: 0x8 }
  - { offsetInCU: 0xCEF, offset: 0x1A07F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV2eeoiySbAC_ACtFZ', symObjAddr: 0x19C, symBinAddr: 0x26B14, symSize: 0x34 }
  - { offsetInCU: 0xD5E, offset: 0x1A0EE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV4hash4intoys6HasherVz_tF', symObjAddr: 0x1D0, symBinAddr: 0x26B48, symSize: 0x28 }
  - { offsetInCU: 0xDD0, offset: 0x1A160, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit10LoginErrorV9hashValueSivg', symObjAddr: 0x1F8, symBinAddr: 0x26B70, symSize: 0x44 }
  - { offsetInCU: 0xF08, offset: 0x1A298, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14LoginErrorCodeO8rawValueACSgSi_tcfC', symObjAddr: 0x3E0, symBinAddr: 0x26D58, symSize: 0x18 }
  - { offsetInCU: 0x27, offset: 0x1A304, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16LoginErrorDomain_WZ', symObjAddr: 0x0, symBinAddr: 0x2715C, symSize: 0x2C }
  - { offsetInCU: 0x49, offset: 0x1A326, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16LoginErrorDomainSSvp', symObjAddr: 0x88, symBinAddr: 0x62308, symSize: 0x0 }
  - { offsetInCU: 0x57, offset: 0x1A334, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16LoginErrorDomain_WZ', symObjAddr: 0x0, symBinAddr: 0x2715C, symSize: 0x2C }
  - { offsetInCU: 0x93, offset: 0x1A370, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit16LoginErrorDomainSSvg', symObjAddr: 0x2C, symBinAddr: 0x27188, symSize: 0x5C }
  - { offsetInCU: 0xC2, offset: 0x1A501, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC15defaultAudienceAA07DefaultF0OvgTo', symObjAddr: 0x4EC, symBinAddr: 0x276E8, symSize: 0x44 }
  - { offsetInCU: 0x117, offset: 0x1A556, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC15defaultAudienceAA07DefaultF0OvsTo', symObjAddr: 0x574, symBinAddr: 0x27770, symSize: 0x48 }
  - { offsetInCU: 0x1DB, offset: 0x1A61A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC13configurationAA0C13ConfigurationCSgvgTo', symObjAddr: 0x788, symBinAddr: 0x27944, symSize: 0x48 }
  - { offsetInCU: 0x22F, offset: 0x1A66E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC13configurationAA0C13ConfigurationCSgvsTo', symObjAddr: 0x820, symBinAddr: 0x279DC, symSize: 0x64 }
  - { offsetInCU: 0x285, offset: 0x1A6C4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC20requestedPermissionsShyAA12FBPermissionCGSgvgTo', symObjAddr: 0x884, symBinAddr: 0x27A40, symSize: 0xA8 }
  - { offsetInCU: 0x2D9, offset: 0x1A718, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC20requestedPermissionsShyAA12FBPermissionCGSgvsTo', symObjAddr: 0x974, symBinAddr: 0x27B30, symSize: 0x9C }
  - { offsetInCU: 0x3B2, offset: 0x1A7F1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC17usedSafariSessionSbvgTo', symObjAddr: 0xC30, symBinAddr: 0x27DEC, symSize: 0x44 }
  - { offsetInCU: 0x406, offset: 0x1A845, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC17usedSafariSessionSbvsTo', symObjAddr: 0xCB8, symBinAddr: 0x27E74, symSize: 0x48 }
  - { offsetInCU: 0x46A, offset: 0x1A8A9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC012isPerformingC0SbvgTo', symObjAddr: 0xD8C, symBinAddr: 0x27F48, symSize: 0x4C }
  - { offsetInCU: 0x59E, offset: 0x1A9DD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC15defaultAudienceAcA07DefaultF0O_tcfcTo', symObjAddr: 0x15F0, symBinAddr: 0x287AC, symSize: 0x64 }
  - { offsetInCU: 0x665, offset: 0x1AAA4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5logIn4from13configuration10completionySo16UIViewControllerCSg_AA0C13ConfigurationCSgyAA0cdC6ResultCSg_s5Error_pSgtctFTo', symObjAddr: 0x1A98, symBinAddr: 0x28C54, symSize: 0xB8 }
  - { offsetInCU: 0x6E9, offset: 0x1AB28, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5logIn11permissions4from7handlerySaySSG_So16UIViewControllerCSgyAA0cdC6ResultCSg_s5Error_pSgtcSgtFTo', symObjAddr: 0x22A0, symBinAddr: 0x2945C, symSize: 0xD4 }
  - { offsetInCU: 0x72C, offset: 0x1AB6B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5logIn33_C218275A97333B874EDDFE627110566CLLyyFySb_s5Error_pSgtcfU_', symObjAddr: 0x2484, symBinAddr: 0x29640, symSize: 0x1F8 }
  - { offsetInCU: 0x8D0, offset: 0x1AD0F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC21reauthorizeDataAccess4from7handlerySo16UIViewControllerC_yAA0cdC6ResultCSg_s5Error_pSgtctFTo', symObjAddr: 0x39F0, symBinAddr: 0x2ABAC, symSize: 0x74 }
  - { offsetInCU: 0x935, offset: 0x1AD74, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC6logOutyyFTo', symObjAddr: 0x3C28, symBinAddr: 0x2ADE4, symSize: 0x28 }
  - { offsetInCU: 0xA44, offset: 0x1AE83, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC22completeAuthentication10parameters15expectChallengeyAA01_C20CompletionParametersC_SbtFTo', symObjAddr: 0x51F0, symBinAddr: 0x2C3AC, symSize: 0x58 }
  - { offsetInCU: 0xAD4, offset: 0x1AF13, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC15logInParameters13configuration12loggingToken20authenticationMethodSDyS2SGSgAA0C13ConfigurationCSg_SSSgSStFTo', symObjAddr: 0x6E40, symBinAddr: 0x2DFFC, symSize: 0xFC }
  - { offsetInCU: 0xB0B, offset: 0x1AF4A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC24validateReauthentication11accessToken11loginResult04userH5NonceySo011FBSDKAccessH0C_AA0cdcJ0CSgSSSgtFTo', symObjAddr: 0x7370, symBinAddr: 0x2E52C, symSize: 0xAC }
  - { offsetInCU: 0xB42, offset: 0x1AF81, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18storeExpectedNonceyySSSgFTo', symObjAddr: 0x7790, symBinAddr: 0x2E94C, symSize: 0x68 }
  - { offsetInCU: 0xB72, offset: 0x1AFB1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCACycfcTo', symObjAddr: 0x79E0, symBinAddr: 0x2EB9C, symSize: 0x20 }
  - { offsetInCU: 0xBA2, offset: 0x1AFE1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCAA0C9ProvidingA2aDP15defaultAudienceAA07DefaultG0OvgTW', symObjAddr: 0x81F8, symBinAddr: 0x2F3B4, symSize: 0x48 }
  - { offsetInCU: 0xBDC, offset: 0x1B01B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCAA0C9ProvidingA2aDP15defaultAudienceAA07DefaultG0OvsTW', symObjAddr: 0x8240, symBinAddr: 0x2F3FC, symSize: 0x4C }
  - { offsetInCU: 0xC1D, offset: 0x1B05C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCAA0C9ProvidingA2aDP15defaultAudienceAA07DefaultG0OvMTW', symObjAddr: 0x828C, symBinAddr: 0x2F448, symSize: 0x48 }
  - { offsetInCU: 0xC57, offset: 0x1B096, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCAA0C9ProvidingA2aDP5logIn14viewController13configuration10completionySo06UIViewI0CSg_AA0C13ConfigurationCSgyAA0C6ResultOctFTW', symObjAddr: 0x82D4, symBinAddr: 0x2F490, symSize: 0x7C }
  - { offsetInCU: 0xCBD, offset: 0x1B0FC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCAA0C9ProvidingA2aDP5logIn11permissions4from7handlerySaySSG_So16UIViewControllerCSgyAA0cdC6ResultCSg_s5Error_pSgtcSgtFTW', symObjAddr: 0x8350, symBinAddr: 0x2F50C, symSize: 0x20 }
  - { offsetInCU: 0xCD8, offset: 0x1B117, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCAA0C9ProvidingA2aDP6logOutyyFTW', symObjAddr: 0x8370, symBinAddr: 0x2F52C, symSize: 0x20 }
  - { offsetInCU: 0xCF3, offset: 0x1B132, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCAA17DependentAsObjectA2aDP22configuredDependencies0gI0QzSgvgTW', symObjAddr: 0x85E4, symBinAddr: 0x2F7A0, symSize: 0x58 }
  - { offsetInCU: 0xD25, offset: 0x1B164, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCAA17DependentAsObjectA2aDP22configuredDependencies0gI0QzSgvsTW', symObjAddr: 0x863C, symBinAddr: 0x2F7F8, symSize: 0x60 }
  - { offsetInCU: 0xD67, offset: 0x1B1A6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCAA17DependentAsObjectA2aDP22configuredDependencies0gI0QzSgvMTW', symObjAddr: 0x869C, symBinAddr: 0x2F858, symSize: 0x44 }
  - { offsetInCU: 0xDA1, offset: 0x1B1E0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCAA17DependentAsObjectA2aDP19defaultDependencies0gI0QzSgvgTW', symObjAddr: 0x86E0, symBinAddr: 0x2F89C, symSize: 0x4 }
  - { offsetInCU: 0xE00, offset: 0x1B23F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC21reauthorizeDataAccess4from7handlerySo16UIViewControllerC_yAA0cdC6ResultCSg_s5Error_pSgtctF04$s13a6Kit012cd2C6l13CSgSo7NSErrorq11IeyByy_ADs5M12_pSgIeggg_TRAKSo0S0CSgIeyByy_Tf1ncn_nTf4dng_n', symObjAddr: 0xDD94, symBinAddr: 0x34E68, symSize: 0x4CC }
  - { offsetInCU: 0x14FF, offset: 0x1B93E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC19defaultDependenciesAC06ObjectF0VSgvpACTk', symObjAddr: 0x1000, symBinAddr: 0x281BC, symSize: 0x90 }
  - { offsetInCU: 0x15B4, offset: 0x1B9F3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultCSgSo7NSErrorCSgIeyByy_ADs5Error_pSgIeggg_TR', symObjAddr: 0x1B50, symBinAddr: 0x28D0C, symSize: 0x58 }
  - { offsetInCU: 0x15F0, offset: 0x1BA2F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultCSgs5Error_pSgytIegnnr_AdFIeggg_TR', symObjAddr: 0x1C94, symBinAddr: 0x28E50, symSize: 0x60 }
  - { offsetInCU: 0x1A09, offset: 0x1BE48, size: 0x8, addend: 0x0, symName: '_$sSbs5Error_pSgIegyg_SbSo7NSErrorCSgIeyByy_TR', symObjAddr: 0x7424, symBinAddr: 0x2E5E0, symSize: 0x64 }
  - { offsetInCU: 0x1A2F, offset: 0x1BE6E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCfETo', symObjAddr: 0x7A34, symBinAddr: 0x2EBF0, symSize: 0xA4 }
  - { offsetInCU: 0x1A75, offset: 0x1BEB4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC10makeOpenerACyFZTo', symObjAddr: 0x7AF8, symBinAddr: 0x2ECB4, symSize: 0x24 }
  - { offsetInCU: 0x1B0D, offset: 0x1BF4C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC11application_4open17sourceApplication10annotationSbSo13UIApplicationCSg_10Foundation3URLVSgSSSgypSgtFTo', symObjAddr: 0x7B30, symBinAddr: 0x2ECEC, symSize: 0x164 }
  - { offsetInCU: 0x1B3F, offset: 0x1BF7E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC7canOpen_3for17sourceApplication10annotationSb10Foundation3URLV_So13UIApplicationCSgSSSgypSgtFTo', symObjAddr: 0x7C94, symBinAddr: 0x2EE50, symSize: 0x128 }
  - { offsetInCU: 0x1B8D, offset: 0x1BFCC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC26applicationDidBecomeActiveyySo13UIApplicationCFTo', symObjAddr: 0x7E0C, symBinAddr: 0x2EFC8, symSize: 0x88 }
  - { offsetInCU: 0x1BF9, offset: 0x1C038, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC19isAuthenticationURLySb10Foundation0G0VFTo', symObjAddr: 0x7EF4, symBinAddr: 0x2F0B0, symSize: 0xD4 }
  - { offsetInCU: 0x1C5A, offset: 0x1C099, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC21shouldStopPropagation2ofSb10Foundation3URLV_tFTo', symObjAddr: 0x815C, symBinAddr: 0x2F318, symSize: 0x9C }
  - { offsetInCU: 0x1C75, offset: 0x1C0B4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit29LoginCompleterFactoryProtocol_pWOb', symObjAddr: 0x8728, symBinAddr: 0x2F8E4, symSize: 0x18 }
  - { offsetInCU: 0x1C88, offset: 0x1C0C7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5logIn14viewController13configuration10completionySo06UIViewH0CSg_AA0C13ConfigurationCSgyAA0C6ResultOctFyAA0cdcM0CSg_s5Error_pSgtcfU_TA', symObjAddr: 0x8764, symBinAddr: 0x2F920, symSize: 0x8 }
  - { offsetInCU: 0x1C9B, offset: 0x1C0DA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5logIn11permissions14viewController10completionySay09FBSDKCoreB010PermissionOG_So06UIViewI0CSgyAA0C6ResultOcSgtFyAA0cdcN0CSg_s5Error_pSgtcfU_TA', symObjAddr: 0x8798, symBinAddr: 0x2F954, symSize: 0x8 }
  - { offsetInCU: 0x1CAE, offset: 0x1C0ED, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesVWOh', symObjAddr: 0x87A0, symBinAddr: 0x2F95C, symSize: 0x2C }
  - { offsetInCU: 0x1D75, offset: 0x1C1B4, size: 0x8, addend: 0x0, symName: '_$ss22_ContiguousArrayBufferV19_uninitializedCount15minimumCapacityAByxGSi_SitcfC13FBSDKLoginKit18DeviceLoginManagerC_Tgm5', symObjAddr: 0x8E00, symBinAddr: 0x2FF6C, symSize: 0x88 }
  - { offsetInCU: 0x1DF3, offset: 0x1C232, size: 0x8, addend: 0x0, symName: '_$ss22_ContiguousArrayBufferV19_uninitializedCount15minimumCapacityAByxGSi_SitcfCSS_Tgm5', symObjAddr: 0x8E88, symBinAddr: 0x2FFF4, symSize: 0x80 }
  - { offsetInCU: 0x1E71, offset: 0x1C2B0, size: 0x8, addend: 0x0, symName: '_$ss22_ContiguousArrayBufferV19_uninitializedCount15minimumCapacityAByxGSi_SitcfC12FBSDKCoreKit10PermissionO_Tgm5', symObjAddr: 0x8F08, symBinAddr: 0x30074, symSize: 0xC8 }
  - { offsetInCU: 0x2041, offset: 0x1C480, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV_8capacityAByxGs07__CocoaB0Vn_SitcfC13FBSDKLoginKit12FBPermissionC_Tgm5', symObjAddr: 0x9674, symBinAddr: 0x307E0, symSize: 0x214 }
  - { offsetInCU: 0x2474, offset: 0x1C8B3, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV19genericIntersectionyAByxGqd__7ElementQyd__RszSTRd__lFADs13_UnsafeBitsetVXEfU_13FBSDKLoginKit12FBPermissionC_ShyAKGTg5', symObjAddr: 0xC3F8, symBinAddr: 0x33564, symSize: 0x3E4 }
  - { offsetInCU: 0x25BA, offset: 0x1C9F9, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV12intersectionyAByxGADFADs13_UnsafeBitsetVXEfU_13FBSDKLoginKit12FBPermissionC_Tg5', symObjAddr: 0xC7DC, symBinAddr: 0x33948, symSize: 0x4F4 }
  - { offsetInCU: 0x26CE, offset: 0x1CB0D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC24validateReauthentication11accessToken11loginResult04userH5NonceySo011FBSDKAccessH0C_AA0cdcJ0CSgSSSgtFySo27FBSDKGraphRequestConnecting_pSg_ypSgs5Error_pSgtcfU_TA', symObjAddr: 0xD448, symBinAddr: 0x34580, symSize: 0x30 }
  - { offsetInCU: 0x26E1, offset: 0x1CB20, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0xD478, symBinAddr: 0x345B0, symSize: 0x10 }
  - { offsetInCU: 0x26F4, offset: 0x1CB33, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0xD488, symBinAddr: 0x345C0, symSize: 0x8 }
  - { offsetInCU: 0x27C7, offset: 0x1CC06, size: 0x8, addend: 0x0, symName: '_$sSTsE6reduce4into_qd__qd__n_yqd__z_7ElementQztKXEtKlFSDyS2SSgG_s17_NativeDictionaryVyS2SGTg5051$sSD16compactMapValuesySDyxqd__Gqd__Sgq_KXEKlFys17_dE44Vyxqd__Gz_x3key_q_5valuettKXEfU_SS_SSSgSSTG5xq_Sgs5Error_pr0_lyAESSIsgnrzo_Tf1ncn_nTf4nng_n', symObjAddr: 0xD490, symBinAddr: 0x345C8, symSize: 0x344 }
  - { offsetInCU: 0x2A1F, offset: 0x1CE5E, size: 0x8, addend: 0x0, symName: '_$sSh21_nonEmptyArrayLiteralShyxGSayxG_tcfC13FBSDKLoginKit12FBPermissionC_Tgm5Tf4g_n', symObjAddr: 0xDA54, symBinAddr: 0x34B28, symSize: 0x340 }
  - { offsetInCU: 0x2C05, offset: 0x1D044, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV12intersectionyAByxGADF13FBSDKLoginKit12FBPermissionC_Tg5Tf4ng_n', symObjAddr: 0xE260, symBinAddr: 0x35334, symSize: 0x14C }
  - { offsetInCU: 0x2D24, offset: 0x1D163, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV19genericIntersectionyAByxGqd__7ElementQyd__RszSTRd__lF13FBSDKLoginKit12FBPermissionC_ShyAIGTg5Tf4ng_n', symObjAddr: 0xE3AC, symBinAddr: 0x35480, symSize: 0x14C }
  - { offsetInCU: 0x2EE8, offset: 0x1D327, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit29LoginCompleterFactoryProtocol_pWOc', symObjAddr: 0xEC84, symBinAddr: 0x35D58, symSize: 0x44 }
  - { offsetInCU: 0x2EFB, offset: 0x1D33A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCMU', symObjAddr: 0xECEC, symBinAddr: 0x35DC0, symSize: 0x8 }
  - { offsetInCU: 0x2F0E, offset: 0x1D34D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCMa', symObjAddr: 0xECF4, symBinAddr: 0x35DC8, symSize: 0x3C }
  - { offsetInCU: 0x2F21, offset: 0x1D360, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCMr', symObjAddr: 0xED30, symBinAddr: 0x35E04, symSize: 0xBC }
  - { offsetInCU: 0x2F34, offset: 0x1D373, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVSgMa', symObjAddr: 0xEDEC, symBinAddr: 0x35EC0, symSize: 0x54 }
  - { offsetInCU: 0x2F47, offset: 0x1D386, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesVwCP', symObjAddr: 0xEE40, symBinAddr: 0x35F14, symSize: 0x30 }
  - { offsetInCU: 0x2F5A, offset: 0x1D399, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesVwxx', symObjAddr: 0xEE70, symBinAddr: 0x35F44, symSize: 0x50 }
  - { offsetInCU: 0x2F6D, offset: 0x1D3AC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesVwcp', symObjAddr: 0xEEC0, symBinAddr: 0x35F94, symSize: 0xBC }
  - { offsetInCU: 0x2F80, offset: 0x1D3BF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesVwca', symObjAddr: 0xEF7C, symBinAddr: 0x36050, symSize: 0xE0 }
  - { offsetInCU: 0x2F93, offset: 0x1D3D2, size: 0x8, addend: 0x0, symName: ___swift_memcpy112_8, symObjAddr: 0xF1C4, symBinAddr: 0x36130, symSize: 0x24 }
  - { offsetInCU: 0x2FA6, offset: 0x1D3E5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesVwta', symObjAddr: 0xF1E8, symBinAddr: 0x36154, symSize: 0xA4 }
  - { offsetInCU: 0x2FB9, offset: 0x1D3F8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesVwet', symObjAddr: 0xF28C, symBinAddr: 0x361F8, symSize: 0x48 }
  - { offsetInCU: 0x2FCC, offset: 0x1D40B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesVwst', symObjAddr: 0xF2D4, symBinAddr: 0x36240, symSize: 0x5C }
  - { offsetInCU: 0x2FDF, offset: 0x1D41E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesVMa', symObjAddr: 0xF330, symBinAddr: 0x3629C, symSize: 0x10 }
  - { offsetInCU: 0x2FF2, offset: 0x1D431, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultCSgSo7NSErrorCSgIeyByy_ADs5Error_pSgIeggg_TRTA', symObjAddr: 0xF364, symBinAddr: 0x362D0, symSize: 0x8 }
  - { offsetInCU: 0x3005, offset: 0x1D444, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultCSgs5Error_pSgIeggg_SgWOe', symObjAddr: 0xF36C, symBinAddr: 0x362D8, symSize: 0x10 }
  - { offsetInCU: 0x3018, offset: 0x1D457, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC11application_4open17sourceApplication10annotationSbSo13UIApplicationCSg_10Foundation3URLVSgSSSgypSgtFyAA01_C20CompletionParametersCcfU_TA', symObjAddr: 0xF43C, symBinAddr: 0x36348, symSize: 0x24 }
  - { offsetInCU: 0x304F, offset: 0x1D48E, size: 0x8, addend: 0x0, symName: '_$sSh5IndexV8_VariantOyx__GSHRzlWOy', symObjAddr: 0xF46C, symBinAddr: 0x3636C, symSize: 0xC }
  - { offsetInCU: 0x3062, offset: 0x1D4A1, size: 0x8, addend: 0x0, symName: '_$sSD8IteratorV8_VariantOyxq___GSHRzr0_lWOe', symObjAddr: 0xF478, symBinAddr: 0x36378, symSize: 0x8 }
  - { offsetInCU: 0x3075, offset: 0x1D4B4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVWOb', symObjAddr: 0xF480, symBinAddr: 0x36380, symSize: 0x44 }
  - { offsetInCU: 0x3088, offset: 0x1D4C7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit28IdentifiedLoginResultHandlerVWOh', symObjAddr: 0xF4C4, symBinAddr: 0x363C4, symSize: 0x3C }
  - { offsetInCU: 0x30A6, offset: 0x1D4E5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultCSgs5Error_pSgIeggg_AdFytIegnnr_TRTA', symObjAddr: 0xF5AC, symBinAddr: 0x36464, symSize: 0x28 }
  - { offsetInCU: 0x30CE, offset: 0x1D50D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultCSgs5Error_pSgytIegnnr_AdFIeggg_TRTA', symObjAddr: 0xF5D4, symBinAddr: 0x3648C, symSize: 0x8 }
  - { offsetInCU: 0x30E1, offset: 0x1D520, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultCSgs5Error_pSgIeggg_SgWOy', symObjAddr: 0xF5DC, symBinAddr: 0x36494, symSize: 0x10 }
  - { offsetInCU: 0x30F4, offset: 0x1D533, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5logIn33_C218275A97333B874EDDFE627110566CLLyyFySb_s5Error_pSgtcfU_TA', symObjAddr: 0xF5EC, symBinAddr: 0x364A4, symSize: 0x8 }
  - { offsetInCU: 0x3112, offset: 0x1D551, size: 0x8, addend: 0x0, symName: '_$sSbs5Error_pSgytIegnnr_SbABIegyg_TRTA', symObjAddr: 0xF5F4, symBinAddr: 0x364AC, symSize: 0x50 }
  - { offsetInCU: 0x3145, offset: 0x1D584, size: 0x8, addend: 0x0, symName: '_$sSbs5Error_pSgIegyg_SbABytIegnnr_TRTA', symObjAddr: 0xF644, symBinAddr: 0x364FC, symSize: 0x28 }
  - { offsetInCU: 0x31E2, offset: 0x1D621, size: 0x8, addend: 0x0, symName: '_$sSlsE3mapySayqd__Gqd__7ElementQzKXEKlFSay12FBSDKCoreKit10PermissionOG_SSTg5085$s13FBSDKLoginKit12LoginManagerC5logIn11permissions14viewController10completionySay09c4B010E91OG_So06UIViewI0CSgyAA0C6ResultOcSgtFSSAJcfu_32f5ef5a0b9ade21eb65ffea7b618f60adAJSSTf3nnpk_nTf1cn_n', symObjAddr: 0x11C, symBinAddr: 0x27318, symSize: 0x114 }
  - { offsetInCU: 0x33A1, offset: 0x1D7E0, size: 0x8, addend: 0x0, symName: '_$sSlsE3mapySayqd__Gqd__7ElementQzKXEKlFShy13FBSDKLoginKit12FBPermissionCG_SSTg504$s13c132Kit12LoginManagerC15logInParameters13configuration12loggingToken20authenticationMethodSDyS2SGSgAA0C13ConfigurationCSg_SSSgSStFSSAA12E52Ccfu_32e0d58b938ad0b6cb17de1b825049cc00AOSSTf3nnpk_nTf1cn_n', symObjAddr: 0x230, symBinAddr: 0x2742C, symSize: 0x2BC }
  - { offsetInCU: 0x3B02, offset: 0x1DF41, size: 0x8, addend: 0x0, symName: '_$sSaySayxGqd__c7ElementQyd__RszSTRd__lufCSS_ShySSGTgm5Tf4g_n', symObjAddr: 0xE4F8, symBinAddr: 0x355CC, symSize: 0xD8 }
  - { offsetInCU: 0x403E, offset: 0x1E47D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCACycfC', symObjAddr: 0xFC, symBinAddr: 0x272F8, symSize: 0x20 }
  - { offsetInCU: 0x4093, offset: 0x1E4D2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5logIn11permissions14viewController10completionySay09FBSDKCoreB010PermissionOG_So06UIViewI0CSgyAA0C6ResultOcSgtF', symObjAddr: 0x2374, symBinAddr: 0x29530, symSize: 0x94 }
  - { offsetInCU: 0x40F3, offset: 0x1E532, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5logIn11permissions14viewController10completionySay09FBSDKCoreB010PermissionOG_So06UIViewI0CSgyAA0C6ResultOcSgtFyAA0cdcN0CSg_s5Error_pSgtcfU_', symObjAddr: 0x2408, symBinAddr: 0x295C4, symSize: 0x7C }
  - { offsetInCU: 0x41A5, offset: 0x1E5E4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC15logInParameters13configuration12loggingToken20authenticationMethodSDyS2SGSgAA0C13ConfigurationCSg_SSSgSStF', symObjAddr: 0x5248, symBinAddr: 0x2C404, symSize: 0x19A0 }
  - { offsetInCU: 0x5030, offset: 0x1F46F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC15logInParameters13configuration12loggingToken20authenticationMethodSDyS2SGSgAA0C13ConfigurationCSg_SSSgSStFA2MXEfU_', symObjAddr: 0x6BE8, symBinAddr: 0x2DDA4, symSize: 0x1C }
  - { offsetInCU: 0x5098, offset: 0x1F4D7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC15defaultAudienceAA07DefaultF0Ovg', symObjAddr: 0x530, symBinAddr: 0x2772C, symSize: 0x44 }
  - { offsetInCU: 0x50D7, offset: 0x1F516, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC15defaultAudienceAA07DefaultF0Ovs', symObjAddr: 0x5BC, symBinAddr: 0x277B8, symSize: 0x48 }
  - { offsetInCU: 0x50FD, offset: 0x1F53C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC15defaultAudienceAA07DefaultF0OvM', symObjAddr: 0x604, symBinAddr: 0x27800, symSize: 0x44 }
  - { offsetInCU: 0x511A, offset: 0x1F559, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC15defaultAudienceAA07DefaultF0OvM.resume.0', symObjAddr: 0x648, symBinAddr: 0x27844, symSize: 0x4 }
  - { offsetInCU: 0x5139, offset: 0x1F578, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC7handlerAA010IdentifiedC13ResultHandlerVSgvg', symObjAddr: 0x64C, symBinAddr: 0x27848, symSize: 0x58 }
  - { offsetInCU: 0x515C, offset: 0x1F59B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC7handlerAA010IdentifiedC13ResultHandlerVSgvs', symObjAddr: 0x6E4, symBinAddr: 0x278A0, symSize: 0x60 }
  - { offsetInCU: 0x518E, offset: 0x1F5CD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC7handlerAA010IdentifiedC13ResultHandlerVSgvM', symObjAddr: 0x744, symBinAddr: 0x27900, symSize: 0x44 }
  - { offsetInCU: 0x51C3, offset: 0x1F602, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC13configurationAA0C13ConfigurationCSgvg', symObjAddr: 0x7D0, symBinAddr: 0x2798C, symSize: 0x50 }
  - { offsetInCU: 0x5214, offset: 0x1F653, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC20requestedPermissionsShyAA12FBPermissionCGSgvg', symObjAddr: 0x92C, symBinAddr: 0x27AE8, symSize: 0x48 }
  - { offsetInCU: 0x5253, offset: 0x1F692, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC20requestedPermissionsShyAA12FBPermissionCGSgvM', symObjAddr: 0xA24, symBinAddr: 0x27BE0, symSize: 0x44 }
  - { offsetInCU: 0x5276, offset: 0x1F6B5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC6loggerAA0cD6LoggerCSgvg', symObjAddr: 0xA68, symBinAddr: 0x27C24, symSize: 0x48 }
  - { offsetInCU: 0x5299, offset: 0x1F6D8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC6loggerAA0cD6LoggerCSgvM', symObjAddr: 0xB1C, symBinAddr: 0x27CD8, symSize: 0x44 }
  - { offsetInCU: 0x52BC, offset: 0x1F6FB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5stateAA0cD5StateOvg', symObjAddr: 0xB60, symBinAddr: 0x27D1C, symSize: 0x44 }
  - { offsetInCU: 0x52DF, offset: 0x1F71E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5stateAA0cD5StateOvs', symObjAddr: 0xBA4, symBinAddr: 0x27D60, symSize: 0x48 }
  - { offsetInCU: 0x5311, offset: 0x1F750, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5stateAA0cD5StateOvM', symObjAddr: 0xBEC, symBinAddr: 0x27DA8, symSize: 0x44 }
  - { offsetInCU: 0x5346, offset: 0x1F785, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC17usedSafariSessionSbvg', symObjAddr: 0xC74, symBinAddr: 0x27E30, symSize: 0x44 }
  - { offsetInCU: 0x5385, offset: 0x1F7C4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC17usedSafariSessionSbvs', symObjAddr: 0xD00, symBinAddr: 0x27EBC, symSize: 0x48 }
  - { offsetInCU: 0x53A8, offset: 0x1F7E7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC17usedSafariSessionSbvM', symObjAddr: 0xD48, symBinAddr: 0x27F04, symSize: 0x44 }
  - { offsetInCU: 0x53DD, offset: 0x1F81C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC012isPerformingC0Sbvg', symObjAddr: 0xDD8, symBinAddr: 0x27F94, symSize: 0x4C }
  - { offsetInCU: 0x5422, offset: 0x1F861, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC22configuredDependenciesAC06ObjectF0VSgvg', symObjAddr: 0xE24, symBinAddr: 0x27FE0, symSize: 0x58 }
  - { offsetInCU: 0x543F, offset: 0x1F87E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC22configuredDependenciesAC06ObjectF0VSgvs', symObjAddr: 0xE7C, symBinAddr: 0x28038, symSize: 0x60 }
  - { offsetInCU: 0x5465, offset: 0x1F8A4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC22configuredDependenciesAC06ObjectF0VSgvM', symObjAddr: 0xEDC, symBinAddr: 0x28098, symSize: 0x44 }
  - { offsetInCU: 0x5482, offset: 0x1F8C1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC19defaultDependenciesAC06ObjectF0VSgvg', symObjAddr: 0xF20, symBinAddr: 0x280DC, symSize: 0xE0 }
  - { offsetInCU: 0x54A4, offset: 0x1F8E3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC19defaultDependenciesAC06ObjectF0VSgvgAGyXEfU_', symObjAddr: 0x10FC, symBinAddr: 0x282B8, symSize: 0x27C }
  - { offsetInCU: 0x55FB, offset: 0x1FA3A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC19defaultDependenciesAC06ObjectF0VSgvs', symObjAddr: 0x1090, symBinAddr: 0x2824C, symSize: 0x6C }
  - { offsetInCU: 0x5699, offset: 0x1FAD8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV17accessTokenWallet014authenticationhI012errorFactory012graphRequestL015internalUtility13keychainStore014loginCompleterL015profileProvider8settings9urlOpenerAESo011FBSDKAccessH9Providing_pXp_So019FBSDKAuthenticationH9Providing_pXpSo18FBSDKErrorCreating_pSo010FBSDKGraphnL0_pSo27FBSDKAppAvailabilityChecker_So26FBSDKAppURLSchemeProvidingSo15FBSDKURLHostingpSo013FBSDKKeychainR0_pAA0ctL8Protocol_p09FBSDKCoreB016ProfileProviding_pXpAY16SettingsProtocol_pSo14FBSDKURLOpener_ptcfC', symObjAddr: 0x1378, symBinAddr: 0x28534, symSize: 0x4C }
  - { offsetInCU: 0x56AC, offset: 0x1FAEB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC19defaultDependenciesAC06ObjectF0VSgvM', symObjAddr: 0x13C4, symBinAddr: 0x28580, symSize: 0x4C }
  - { offsetInCU: 0x56CF, offset: 0x1FB0E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC19defaultDependenciesAC06ObjectF0VSgvM.resume.0', symObjAddr: 0x1410, symBinAddr: 0x285CC, symSize: 0x10C }
  - { offsetInCU: 0x5746, offset: 0x1FB85, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC15defaultAudienceAcA07DefaultF0O_tcfC', symObjAddr: 0x151C, symBinAddr: 0x286D8, symSize: 0x6C }
  - { offsetInCU: 0x5788, offset: 0x1FBC7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC15defaultAudienceAcA07DefaultF0O_tcfc', symObjAddr: 0x1588, symBinAddr: 0x28744, symSize: 0x68 }
  - { offsetInCU: 0x5817, offset: 0x1FC56, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5logIn4from13configuration10completionySo16UIViewControllerCSg_AA0C13ConfigurationCSgyAA0cdC6ResultCSg_s5Error_pSgtctF', symObjAddr: 0x1654, symBinAddr: 0x28810, symSize: 0x124 }
  - { offsetInCU: 0x58BD, offset: 0x1FCFC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC11commonLogIn33_C218275A97333B874EDDFE627110566CLL4from13configuration10completionySo16UIViewControllerCSg_AA0C13ConfigurationCSgyAA0cdC6ResultCSg_s5Error_pSgtcSgtF', symObjAddr: 0x1778, symBinAddr: 0x28934, symSize: 0x320 }
  - { offsetInCU: 0x59EA, offset: 0x1FE29, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5logIn14viewController13configuration10completionySo06UIViewH0CSg_AA0C13ConfigurationCSgyAA0C6ResultOctF', symObjAddr: 0x1BA8, symBinAddr: 0x28D64, symSize: 0x78 }
  - { offsetInCU: 0x5A38, offset: 0x1FE77, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5logIn14viewController13configuration10completionySo06UIViewH0CSg_AA0C13ConfigurationCSgyAA0C6ResultOctFyAA0cdcM0CSg_s5Error_pSgtcfU_', symObjAddr: 0x1C20, symBinAddr: 0x28DDC, symSize: 0x74 }
  - { offsetInCU: 0x5B28, offset: 0x1FF67, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC13invokeHandler33_C218275A97333B874EDDFE627110566CLL6result5erroryAA0cdC6ResultCSg_s5Error_pSgtF', symObjAddr: 0x1CF4, symBinAddr: 0x28EB0, symSize: 0x3AC }
  - { offsetInCU: 0x5C44, offset: 0x20083, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5logIn11permissions4from7handlerySaySSG_So16UIViewControllerCSgyAA0cdC6ResultCSg_s5Error_pSgtcSgtF', symObjAddr: 0x20A0, symBinAddr: 0x2925C, symSize: 0x200 }
  - { offsetInCU: 0x5E0E, offset: 0x2024D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC27handleImplicitCancelOfLogIn33_C218275A97333B874EDDFE627110566CLLyyF', symObjAddr: 0x267C, symBinAddr: 0x29838, symSize: 0x184 }
  - { offsetInCU: 0x5F37, offset: 0x20376, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC19performBrowserLogIn33_C218275A97333B874EDDFE627110566CLL7handleryySb_s5Error_pSgtcSg_tF', symObjAddr: 0x2800, symBinAddr: 0x299BC, symSize: 0xD94 }
  - { offsetInCU: 0x6268, offset: 0x206A7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC19performBrowserLogIn33_C218275A97333B874EDDFE627110566CLL7handleryySb_s5Error_pSgtcSg_tFySb_AHtcyKXEfu1_ySb_AHtcfU_', symObjAddr: 0x741C, symBinAddr: 0x2E5D8, symSize: 0x4 }
  - { offsetInCU: 0x6283, offset: 0x206C2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC19performBrowserLogIn33_C218275A97333B874EDDFE627110566CLL7handleryySb_s5Error_pSgtcSg_tFySb_AHtcyKXEfu2_ySb_AHtcfU0_', symObjAddr: 0x7420, symBinAddr: 0x2E5DC, symSize: 0x4 }
  - { offsetInCU: 0x62DD, offset: 0x2071C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC21reauthorizeDataAccess4from7handlerySo16UIViewControllerC_yAA0cdC6ResultCSg_s5Error_pSgtctF', symObjAddr: 0x3594, symBinAddr: 0x2A750, symSize: 0x45C }
  - { offsetInCU: 0x64BF, offset: 0x208FE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC6logOutyyF', symObjAddr: 0x3A64, symBinAddr: 0x2AC20, symSize: 0x1C4 }
  - { offsetInCU: 0x661B, offset: 0x20A5A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC22completeAuthentication10parameters15expectChallengeyAA01_C20CompletionParametersC_SbtF', symObjAddr: 0x3C50, symBinAddr: 0x2AE0C, symSize: 0x538 }
  - { offsetInCU: 0x6787, offset: 0x20BC6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC22storeExpectedChallenge33_C218275A97333B874EDDFE627110566CLLyySSSgF', symObjAddr: 0x4188, symBinAddr: 0x2B344, symSize: 0xFC }
  - { offsetInCU: 0x67D7, offset: 0x20C16, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC16getSuccessResult33_C218275A97333B874EDDFE627110566CLL4fromAA0cdcG0CAA01_C20CompletionParametersC_tF', symObjAddr: 0x4284, symBinAddr: 0x2B440, symSize: 0x7B4 }
  - { offsetInCU: 0x6D3F, offset: 0x2117E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC24validateReauthentication11accessToken11loginResult04userH5NonceySo011FBSDKAccessH0C_AA0cdcJ0CSgSSSgtF', symObjAddr: 0x4A38, symBinAddr: 0x2BBF4, symSize: 0x3A8 }
  - { offsetInCU: 0x6EC5, offset: 0x21304, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC24validateReauthentication11accessToken11loginResult04userH5NonceySo011FBSDKAccessH0C_AA0cdcJ0CSgSSSgtFySo27FBSDKGraphRequestConnecting_pSg_ypSgs5Error_pSgtcfU_', symObjAddr: 0x703C, symBinAddr: 0x2E1F8, symSize: 0x334 }
  - { offsetInCU: 0x6FD3, offset: 0x21412, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18getCancelledResult33_C218275A97333B874EDDFE627110566CLL4fromAA0cdcG0CAA01_C20CompletionParametersC_tF', symObjAddr: 0x4DE0, symBinAddr: 0x2BF9C, symSize: 0x21C }
  - { offsetInCU: 0x7124, offset: 0x21563, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC19setGlobalProperties33_C218275A97333B874EDDFE627110566CLL10parameters11loginResultyAA01_C20CompletionParametersC_AA0cdcN0CSgtF', symObjAddr: 0x4FFC, symBinAddr: 0x2C1B8, symSize: 0x1F4 }
  - { offsetInCU: 0x7368, offset: 0x217A7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC010addLimitedC14ShimParameters33_C218275A97333B874EDDFE627110566CLL10parameters13configurationySDyS2SGz_AA0C13ConfigurationCtF', symObjAddr: 0x6C04, symBinAddr: 0x2DDC0, symSize: 0x138 }
  - { offsetInCU: 0x744C, offset: 0x2188B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC25storeExpectedCodeVerifier33_C218275A97333B874EDDFE627110566CLLyyAA0gH0CSgF', symObjAddr: 0x6F3C, symBinAddr: 0x2E0F8, symSize: 0x100 }
  - { offsetInCU: 0x7498, offset: 0x218D7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC29getRecentlyGrantedPermissions4fromShyAA12FBPermissionCGAH_tF', symObjAddr: 0x7488, symBinAddr: 0x2E644, symSize: 0x148 }
  - { offsetInCU: 0x756A, offset: 0x219A9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC30getRecentlyDeclinedPermissions4fromShyAA12FBPermissionCGAH_tF', symObjAddr: 0x75D0, symBinAddr: 0x2E78C, symSize: 0xEC }
  - { offsetInCU: 0x7616, offset: 0x21A55, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCACycfc', symObjAddr: 0x78BC, symBinAddr: 0x2EA78, symSize: 0x124 }
  - { offsetInCU: 0x7639, offset: 0x21A78, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerCfD', symObjAddr: 0x7A00, symBinAddr: 0x2EBBC, symSize: 0x34 }
  - { offsetInCU: 0x7660, offset: 0x21A9F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC10makeOpenerACyFZ', symObjAddr: 0x7AD8, symBinAddr: 0x2EC94, symSize: 0x20 }
  - { offsetInCU: 0x76AC, offset: 0x21AEB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC11application_4open17sourceApplication10annotationSbSo13UIApplicationCSg_10Foundation3URLVSgSSSgypSgtF', symObjAddr: 0x7B1C, symBinAddr: 0x2ECD8, symSize: 0xC }
  - { offsetInCU: 0x76E9, offset: 0x21B28, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC7canOpen_3for17sourceApplication10annotationSb10Foundation3URLV_So13UIApplicationCSgSSSgypSgtF', symObjAddr: 0x7B28, symBinAddr: 0x2ECE4, symSize: 0x8 }
  - { offsetInCU: 0x7709, offset: 0x21B48, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC26applicationDidBecomeActiveyySo13UIApplicationCF', symObjAddr: 0x7DBC, symBinAddr: 0x2EF78, symSize: 0x50 }
  - { offsetInCU: 0x7770, offset: 0x21BAF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC19isAuthenticationURLySb10Foundation0G0VF', symObjAddr: 0x7E94, symBinAddr: 0x2F050, symSize: 0x60 }
  - { offsetInCU: 0x77B9, offset: 0x21BF8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC21shouldStopPropagation2ofSb10Foundation3URLV_tF', symObjAddr: 0x7FC8, symBinAddr: 0x2F184, symSize: 0x194 }
  - { offsetInCU: 0x7916, offset: 0x21D55, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV17accessTokenWalletSo011FBSDKAccessH9Providing_pXpvg', symObjAddr: 0x8390, symBinAddr: 0x2F54C, symSize: 0x8 }
  - { offsetInCU: 0x7929, offset: 0x21D68, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV17accessTokenWalletSo011FBSDKAccessH9Providing_pXpvs', symObjAddr: 0x8398, symBinAddr: 0x2F554, symSize: 0x8 }
  - { offsetInCU: 0x793C, offset: 0x21D7B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV17accessTokenWalletSo011FBSDKAccessH9Providing_pXpvM', symObjAddr: 0x83A0, symBinAddr: 0x2F55C, symSize: 0x10 }
  - { offsetInCU: 0x794F, offset: 0x21D8E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV17accessTokenWalletSo011FBSDKAccessH9Providing_pXpvM.resume.0', symObjAddr: 0x83B0, symBinAddr: 0x2F56C, symSize: 0x4 }
  - { offsetInCU: 0x7962, offset: 0x21DA1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV25authenticationTokenWalletSo019FBSDKAuthenticationH9Providing_pXpvg', symObjAddr: 0x83B4, symBinAddr: 0x2F570, symSize: 0x8 }
  - { offsetInCU: 0x7975, offset: 0x21DB4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV25authenticationTokenWalletSo019FBSDKAuthenticationH9Providing_pXpvs', symObjAddr: 0x83BC, symBinAddr: 0x2F578, symSize: 0x8 }
  - { offsetInCU: 0x7988, offset: 0x21DC7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV25authenticationTokenWalletSo019FBSDKAuthenticationH9Providing_pXpvM', symObjAddr: 0x83C4, symBinAddr: 0x2F580, symSize: 0x10 }
  - { offsetInCU: 0x799B, offset: 0x21DDA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV25authenticationTokenWalletSo019FBSDKAuthenticationH9Providing_pXpvM.resume.0', symObjAddr: 0x83D4, symBinAddr: 0x2F590, symSize: 0x4 }
  - { offsetInCU: 0x79AE, offset: 0x21DED, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV12errorFactorySo18FBSDKErrorCreating_pvg', symObjAddr: 0x83D8, symBinAddr: 0x2F594, symSize: 0x8 }
  - { offsetInCU: 0x79C1, offset: 0x21E00, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV12errorFactorySo18FBSDKErrorCreating_pvs', symObjAddr: 0x83E0, symBinAddr: 0x2F59C, symSize: 0x28 }
  - { offsetInCU: 0x79D4, offset: 0x21E13, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV12errorFactorySo18FBSDKErrorCreating_pvM', symObjAddr: 0x8408, symBinAddr: 0x2F5C4, symSize: 0x10 }
  - { offsetInCU: 0x79E7, offset: 0x21E26, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV12errorFactorySo18FBSDKErrorCreating_pvM.resume.0', symObjAddr: 0x8418, symBinAddr: 0x2F5D4, symSize: 0x4 }
  - { offsetInCU: 0x79FA, offset: 0x21E39, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV19graphRequestFactorySo010FBSDKGraphhI0_pvg', symObjAddr: 0x841C, symBinAddr: 0x2F5D8, symSize: 0x8 }
  - { offsetInCU: 0x7A0D, offset: 0x21E4C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV19graphRequestFactorySo010FBSDKGraphhI0_pvs', symObjAddr: 0x8424, symBinAddr: 0x2F5E0, symSize: 0x28 }
  - { offsetInCU: 0x7A20, offset: 0x21E5F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV19graphRequestFactorySo010FBSDKGraphhI0_pvM', symObjAddr: 0x844C, symBinAddr: 0x2F608, symSize: 0x10 }
  - { offsetInCU: 0x7A33, offset: 0x21E72, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV19graphRequestFactorySo010FBSDKGraphhI0_pvM.resume.0', symObjAddr: 0x845C, symBinAddr: 0x2F618, symSize: 0x4 }
  - { offsetInCU: 0x7A46, offset: 0x21E85, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV15internalUtilitySo27FBSDKAppAvailabilityChecker_So0I18URLSchemeProvidingSo15FBSDKURLHostingpvg', symObjAddr: 0x8460, symBinAddr: 0x2F61C, symSize: 0x8 }
  - { offsetInCU: 0x7A59, offset: 0x21E98, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV15internalUtilitySo27FBSDKAppAvailabilityChecker_So0I18URLSchemeProvidingSo15FBSDKURLHostingpvs', symObjAddr: 0x8468, symBinAddr: 0x2F624, symSize: 0x28 }
  - { offsetInCU: 0x7A6C, offset: 0x21EAB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV15internalUtilitySo27FBSDKAppAvailabilityChecker_So0I18URLSchemeProvidingSo15FBSDKURLHostingpvM', symObjAddr: 0x8490, symBinAddr: 0x2F64C, symSize: 0x10 }
  - { offsetInCU: 0x7A7F, offset: 0x21EBE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV15internalUtilitySo27FBSDKAppAvailabilityChecker_So0I18URLSchemeProvidingSo15FBSDKURLHostingpvM.resume.0', symObjAddr: 0x84A0, symBinAddr: 0x2F65C, symSize: 0x4 }
  - { offsetInCU: 0x7A92, offset: 0x21ED1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV13keychainStoreSo013FBSDKKeychainH0_pvg', symObjAddr: 0x84A4, symBinAddr: 0x2F660, symSize: 0x8 }
  - { offsetInCU: 0x7AA5, offset: 0x21EE4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV13keychainStoreSo013FBSDKKeychainH0_pvs', symObjAddr: 0x84AC, symBinAddr: 0x2F668, symSize: 0x28 }
  - { offsetInCU: 0x7AB8, offset: 0x21EF7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV13keychainStoreSo013FBSDKKeychainH0_pvM', symObjAddr: 0x84D4, symBinAddr: 0x2F690, symSize: 0x10 }
  - { offsetInCU: 0x7ACB, offset: 0x21F0A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV13keychainStoreSo013FBSDKKeychainH0_pvM.resume.0', symObjAddr: 0x84E4, symBinAddr: 0x2F6A0, symSize: 0x4 }
  - { offsetInCU: 0x7ADE, offset: 0x21F1D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV21loginCompleterFactoryAA0chI8Protocol_pvg', symObjAddr: 0x84E8, symBinAddr: 0x2F6A4, symSize: 0xC }
  - { offsetInCU: 0x7AF1, offset: 0x21F30, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV21loginCompleterFactoryAA0chI8Protocol_pvs', symObjAddr: 0x84F4, symBinAddr: 0x2F6B0, symSize: 0x30 }
  - { offsetInCU: 0x7B04, offset: 0x21F43, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV21loginCompleterFactoryAA0chI8Protocol_pvM', symObjAddr: 0x8524, symBinAddr: 0x2F6E0, symSize: 0x10 }
  - { offsetInCU: 0x7B17, offset: 0x21F56, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV21loginCompleterFactoryAA0chI8Protocol_pvM.resume.0', symObjAddr: 0x8534, symBinAddr: 0x2F6F0, symSize: 0x4 }
  - { offsetInCU: 0x7B2A, offset: 0x21F69, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV15profileProvider09FBSDKCoreB016ProfileProviding_pXpvg', symObjAddr: 0x8538, symBinAddr: 0x2F6F4, symSize: 0x8 }
  - { offsetInCU: 0x7B3D, offset: 0x21F7C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV15profileProvider09FBSDKCoreB016ProfileProviding_pXpvs', symObjAddr: 0x8540, symBinAddr: 0x2F6FC, symSize: 0x8 }
  - { offsetInCU: 0x7B50, offset: 0x21F8F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV15profileProvider09FBSDKCoreB016ProfileProviding_pXpvM', symObjAddr: 0x8548, symBinAddr: 0x2F704, symSize: 0x10 }
  - { offsetInCU: 0x7B63, offset: 0x21FA2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV15profileProvider09FBSDKCoreB016ProfileProviding_pXpvM.resume.0', symObjAddr: 0x8558, symBinAddr: 0x2F714, symSize: 0x4 }
  - { offsetInCU: 0x7B76, offset: 0x21FB5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV8settings09FBSDKCoreB016SettingsProtocol_pvg', symObjAddr: 0x855C, symBinAddr: 0x2F718, symSize: 0x8 }
  - { offsetInCU: 0x7B89, offset: 0x21FC8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV8settings09FBSDKCoreB016SettingsProtocol_pvs', symObjAddr: 0x8564, symBinAddr: 0x2F720, symSize: 0x28 }
  - { offsetInCU: 0x7B9C, offset: 0x21FDB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV8settings09FBSDKCoreB016SettingsProtocol_pvM', symObjAddr: 0x858C, symBinAddr: 0x2F748, symSize: 0x10 }
  - { offsetInCU: 0x7BAF, offset: 0x21FEE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV8settings09FBSDKCoreB016SettingsProtocol_pvM.resume.0', symObjAddr: 0x859C, symBinAddr: 0x2F758, symSize: 0x4 }
  - { offsetInCU: 0x7BC2, offset: 0x22001, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV9urlOpenerSo14FBSDKURLOpener_pvg', symObjAddr: 0x85A0, symBinAddr: 0x2F75C, symSize: 0x8 }
  - { offsetInCU: 0x7BD5, offset: 0x22014, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV9urlOpenerSo14FBSDKURLOpener_pvs', symObjAddr: 0x85A8, symBinAddr: 0x2F764, symSize: 0x28 }
  - { offsetInCU: 0x7BE8, offset: 0x22027, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV9urlOpenerSo14FBSDKURLOpener_pvM', symObjAddr: 0x85D0, symBinAddr: 0x2F78C, symSize: 0x10 }
  - { offsetInCU: 0x7BFB, offset: 0x2203A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC18ObjectDependenciesV9urlOpenerSo14FBSDKURLOpener_pvM.resume.0', symObjAddr: 0x85E0, symBinAddr: 0x2F79C, symSize: 0x4 }
  - { offsetInCU: 0x7C3D, offset: 0x2207C, size: 0x8, addend: 0x0, symName: '_$sSo16FBSDKAccessTokenC11tokenString11permissions19declinedPermissions07expiredG05appID04userJ014expirationDate07refreshM0020dataAccessExpirationM0ABSS_SaySSGA2LS2S10Foundation0M0VSgA2PtcfcTO', symObjAddr: 0x8810, symBinAddr: 0x2F988, symSize: 0x248 }
  - { offsetInCU: 0x7C5C, offset: 0x2209B, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV7_insert2at3key5valueys10_HashTableV6BucketV_xnq_ntFSS_ypTg5', symObjAddr: 0x8A58, symBinAddr: 0x2FBD0, symSize: 0x6C }
  - { offsetInCU: 0x7CE1, offset: 0x22120, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV7_insert2at3key5valueys10_HashTableV6BucketV_xnq_ntFSo26FBSDKAppEventParameterNamea_ypTg5', symObjAddr: 0x8AC4, symBinAddr: 0x2FC3C, symSize: 0x68 }
  - { offsetInCU: 0x7D54, offset: 0x22193, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV7_insert2at3key5valueys10_HashTableV6BucketV_xnq_ntFSS_SSTg5', symObjAddr: 0x8B2C, symBinAddr: 0x2FCA4, symSize: 0x54 }
  - { offsetInCU: 0x7DF7, offset: 0x22236, size: 0x8, addend: 0x0, symName: '_$sSh8_VariantV6insertySb8inserted_x17memberAfterInserttxnF13FBSDKLoginKit12FBPermissionC_Tg5', symObjAddr: 0x8FD0, symBinAddr: 0x3013C, symSize: 0x2B4 }
  - { offsetInCU: 0x7EC9, offset: 0x22308, size: 0x8, addend: 0x0, symName: '_$sSh8_VariantV6insertySb8inserted_x17memberAfterInserttxnFSS_Tg5', symObjAddr: 0x9284, symBinAddr: 0x303F0, symSize: 0x1AC }
  - { offsetInCU: 0x7FAC, offset: 0x223EB, size: 0x8, addend: 0x0, symName: '_$sSh8_VariantV6insertySb8inserted_x17memberAfterInserttxnF12FBSDKCoreKit10PermissionO_Tg5', symObjAddr: 0x9430, symBinAddr: 0x3059C, symSize: 0x244 }
  - { offsetInCU: 0x802A, offset: 0x22469, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV9insertNew_2at8isUniqueyxn_s10_HashTableV6BucketVSbtF13FBSDKLoginKit12FBPermissionC_Tg5', symObjAddr: 0x9888, symBinAddr: 0x309F4, symSize: 0x19C }
  - { offsetInCU: 0x80B2, offset: 0x224F1, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV9insertNew_2at8isUniqueyxn_s10_HashTableV6BucketVSbtFSS_Tg5', symObjAddr: 0x9A24, symBinAddr: 0x30B90, symSize: 0x1B4 }
  - { offsetInCU: 0x819B, offset: 0x225DA, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV9insertNew_2at8isUniqueyxn_s10_HashTableV6BucketVSbtF12FBSDKCoreKit10PermissionO_Tg5', symObjAddr: 0x9BD8, symBinAddr: 0x30D44, symSize: 0x22C }
  - { offsetInCU: 0x820D, offset: 0x2264C, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV4copyyyF13FBSDKLoginKit12FBPermissionC_Tg5', symObjAddr: 0x9E04, symBinAddr: 0x30F70, symSize: 0x1A4 }
  - { offsetInCU: 0x8278, offset: 0x226B7, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV4copyyyFSS_Tg5', symObjAddr: 0x9FA8, symBinAddr: 0x31114, symSize: 0x1AC }
  - { offsetInCU: 0x82E3, offset: 0x22722, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV4copyyyF12FBSDKCoreKit10PermissionO_Tg5', symObjAddr: 0xA154, symBinAddr: 0x312C0, symSize: 0x210 }
  - { offsetInCU: 0x834E, offset: 0x2278D, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV13copyAndResize8capacityySi_tF13FBSDKLoginKit12FBPermissionC_Tg5', symObjAddr: 0xA364, symBinAddr: 0x314D0, symSize: 0x268 }
  - { offsetInCU: 0x83EA, offset: 0x22829, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV13copyAndResize8capacityySi_tFSS_Tg5', symObjAddr: 0xA5CC, symBinAddr: 0x31738, symSize: 0x29C }
  - { offsetInCU: 0x848A, offset: 0x228C9, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV13copyAndResize8capacityySi_tF12FBSDKCoreKit10PermissionO_Tg5', symObjAddr: 0xA868, symBinAddr: 0x319D4, symSize: 0x308 }
  - { offsetInCU: 0x8519, offset: 0x22958, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV6resize8capacityySi_tF13FBSDKLoginKit12FBPermissionC_Tg5', symObjAddr: 0xAB70, symBinAddr: 0x31CDC, symSize: 0x288 }
  - { offsetInCU: 0x85DB, offset: 0x22A1A, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV6resize8capacityySi_tFSS_Tg5', symObjAddr: 0xADF8, symBinAddr: 0x31F64, symSize: 0x2C8 }
  - { offsetInCU: 0x86B1, offset: 0x22AF0, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV6resize8capacityySi_tF12FBSDKCoreKit10PermissionO_Tg5', symObjAddr: 0xB0C0, symBinAddr: 0x3222C, symSize: 0x348 }
  - { offsetInCU: 0x875A, offset: 0x22B99, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV16_unsafeInsertNewyyxnF13FBSDKLoginKit12FBPermissionC_Tg5', symObjAddr: 0xB408, symBinAddr: 0x32574, symSize: 0x80 }
  - { offsetInCU: 0x87B3, offset: 0x22BF2, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV12mutatingFind_8isUniques10_HashTableV6BucketV6bucket_Sb5foundtx_SbtFSo26FBSDKAppEventParameterNamea_ypTg5', symObjAddr: 0xB488, symBinAddr: 0x325F4, symSize: 0xC8 }
  - { offsetInCU: 0x87FF, offset: 0x22C3E, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV20_copyOrMoveAndResize8capacity12moveElementsySi_SbtFSo26FBSDKAppEventParameterNamea_ypTg5', symObjAddr: 0xB660, symBinAddr: 0x327CC, symSize: 0x368 }
  - { offsetInCU: 0x88E8, offset: 0x22D27, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV20_copyOrMoveAndResize8capacity12moveElementsySi_SbtFSS_ypTg5', symObjAddr: 0xB9C8, symBinAddr: 0x32B34, symSize: 0x340 }
  - { offsetInCU: 0x89D1, offset: 0x22E10, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV20_copyOrMoveAndResize8capacity12moveElementsySi_SbtFSS_SSTg5', symObjAddr: 0xBD08, symBinAddr: 0x32E74, symSize: 0x354 }
  - { offsetInCU: 0x8AAD, offset: 0x22EEC, size: 0x8, addend: 0x0, symName: '_$sSh8_VariantV12intersectionys10_NativeSetVyxGShyxGF13FBSDKLoginKit12FBPermissionC_Tg5', symObjAddr: 0xC05C, symBinAddr: 0x331C8, symSize: 0x39C }
  - { offsetInCU: 0x8C0F, offset: 0x2304E, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV13extractSubset5using5countAByxGs13_UnsafeBitsetV_SitF13FBSDKLoginKit12FBPermissionC_Tg5', symObjAddr: 0xCCD0, symBinAddr: 0x33E3C, symSize: 0x244 }
  - { offsetInCU: 0x8CB8, offset: 0x230F7, size: 0x8, addend: 0x0, symName: '_$sShyxSh5IndexVyx_Gcig13FBSDKLoginKit12FBPermissionC_Tg5', symObjAddr: 0xCF14, symBinAddr: 0x34080, symSize: 0x2DC }
  - { offsetInCU: 0x8D50, offset: 0x2318F, size: 0x8, addend: 0x0, symName: '_$sSTsE21_copySequenceContents12initializing8IteratorQz_SitSry7ElementQzG_tFShySSG_Tg5', symObjAddr: 0xD1F0, symBinAddr: 0x3435C, symSize: 0x1B0 }
  - { offsetInCU: 0x8E74, offset: 0x232B3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC5logIn33_C218275A97333B874EDDFE627110566CLL11permissions7handleryShyAA12FBPermissionCG_yAA0cdC6ResultCSg_s5Error_pSgtcSgtFTf4dnn_n', symObjAddr: 0xD838, symBinAddr: 0x3490C, symSize: 0x21C }
  - { offsetInCU: 0x8F9A, offset: 0x233D9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC7canOpen_3for17sourceApplication10annotationSb10Foundation3URLV_So13UIApplicationCSgSSSgypSgtFTf4nddnn_n', symObjAddr: 0xE5D0, symBinAddr: 0x356A4, symSize: 0x1A4 }
  - { offsetInCU: 0x90E9, offset: 0x23528, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginManagerC11application_4open17sourceApplication10annotationSbSo13UIApplicationCSg_10Foundation3URLVSgSSSgypSgtFTf4dndnn_n', symObjAddr: 0xE774, symBinAddr: 0x35848, symSize: 0x510 }
  - { offsetInCU: 0x4E, offset: 0x23859, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO10identifierSo013FBSDKAppEventF4NameavpZ', symObjAddr: 0x5F00, symBinAddr: 0x62450, symSize: 0x0 }
  - { offsetInCU: 0x68, offset: 0x23873, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO9timestampSo013FBSDKAppEventF4NameavpZ', symObjAddr: 0x5F08, symBinAddr: 0x62458, symSize: 0x0 }
  - { offsetInCU: 0x82, offset: 0x2388D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO6resultSo013FBSDKAppEventF4NameavpZ', symObjAddr: 0x5F10, symBinAddr: 0x62460, symSize: 0x0 }
  - { offsetInCU: 0x9C, offset: 0x238A7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO10authMethodSo013FBSDKAppEventF4NameavpZ', symObjAddr: 0x5F18, symBinAddr: 0x62468, symSize: 0x0 }
  - { offsetInCU: 0xB6, offset: 0x238C1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO9errorCodeSo013FBSDKAppEventF4NameavpZ', symObjAddr: 0x5F20, symBinAddr: 0x62470, symSize: 0x0 }
  - { offsetInCU: 0xD0, offset: 0x238DB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO12errorMessageSo013FBSDKAppEventF4NameavpZ', symObjAddr: 0x5F28, symBinAddr: 0x62478, symSize: 0x0 }
  - { offsetInCU: 0xEA, offset: 0x238F5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO6extrasSo013FBSDKAppEventF4NameavpZ', symObjAddr: 0x5F30, symBinAddr: 0x62480, symSize: 0x0 }
  - { offsetInCU: 0x104, offset: 0x2390F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO12loggingTokenSo013FBSDKAppEventF4NameavpZ', symObjAddr: 0x5F38, symBinAddr: 0x62488, symSize: 0x0 }
  - { offsetInCU: 0x11E, offset: 0x23929, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO19declinedPermissionsSo013FBSDKAppEventF4NameavpZ', symObjAddr: 0x5F40, symBinAddr: 0x62490, symSize: 0x0 }
  - { offsetInCU: 0x139, offset: 0x23944, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC19defaultDependenciesAC04TypeG0VSgvpZ', symObjAddr: 0x18650, symBinAddr: 0x64508, symSize: 0x0 }
  - { offsetInCU: 0x1C8, offset: 0x239D3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC22configuredDependenciesAC04TypeG0VSgvpZ', symObjAddr: 0x18678, symBinAddr: 0x64530, symSize: 0x0 }
  - { offsetInCU: 0x4A2, offset: 0x23CAD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO10identifier_WZ', symObjAddr: 0x120C, symBinAddr: 0x3781C, symSize: 0x34 }
  - { offsetInCU: 0x4BC, offset: 0x23CC7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO9timestamp_WZ', symObjAddr: 0x1240, symBinAddr: 0x37850, symSize: 0x3C }
  - { offsetInCU: 0x4D6, offset: 0x23CE1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO6result_WZ', symObjAddr: 0x127C, symBinAddr: 0x3788C, symSize: 0x30 }
  - { offsetInCU: 0x4F0, offset: 0x23CFB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO10authMethod_WZ', symObjAddr: 0x12AC, symBinAddr: 0x378BC, symSize: 0x30 }
  - { offsetInCU: 0x50A, offset: 0x23D15, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO9errorCode_WZ', symObjAddr: 0x12DC, symBinAddr: 0x378EC, symSize: 0x38 }
  - { offsetInCU: 0x524, offset: 0x23D2F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO12errorMessage_WZ', symObjAddr: 0x1314, symBinAddr: 0x37924, symSize: 0x3C }
  - { offsetInCU: 0x53E, offset: 0x23D49, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO6extras_WZ', symObjAddr: 0x1350, symBinAddr: 0x37960, symSize: 0x30 }
  - { offsetInCU: 0x558, offset: 0x23D63, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO12loggingToken_WZ', symObjAddr: 0x1380, symBinAddr: 0x37990, symSize: 0x3C }
  - { offsetInCU: 0x572, offset: 0x23D7D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC0E13ParameterKeys33_A797D0CABA1B6A375C28D6F4DDD8E30FLLO19declinedPermissions_WZ', symObjAddr: 0x13BC, symBinAddr: 0x379CC, symSize: 0x34 }
  - { offsetInCU: 0x6DC, offset: 0x23EE7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC21heartbeatTimerDidFireyyFTo', symObjAddr: 0x3B14, symBinAddr: 0x3A124, symSize: 0xDC }
  - { offsetInCU: 0x744, offset: 0x23F4F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC14getClientState20authenticationMethod08existingH06loggerSSSgAH_SDySSypGSgACSgtFZTf4nnnd_n', symObjAddr: 0x53A4, symBinAddr: 0x3B980, symSize: 0x390 }
  - { offsetInCU: 0xC65, offset: 0x24470, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC19defaultDependencies_WZ', symObjAddr: 0x4048, symBinAddr: 0x3A658, symSize: 0x6C }
  - { offsetInCU: 0xC80, offset: 0x2448B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC19defaultDependenciesAC04TypeG0VSgvau', symObjAddr: 0x40B4, symBinAddr: 0x3A6C4, symSize: 0x40 }
  - { offsetInCU: 0xCB8, offset: 0x244C3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC22configuredDependencies_WZ', symObjAddr: 0x4198, symBinAddr: 0x3A7A8, symSize: 0x18 }
  - { offsetInCU: 0xCD3, offset: 0x244DE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC22configuredDependenciesAC04TypeG0VSgvau', symObjAddr: 0x41B0, symBinAddr: 0x3A7C0, symSize: 0x40 }
  - { offsetInCU: 0xD23, offset: 0x2452E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerCAA15DependentAsTypeA2aDP22configuredDependencies0hJ0QzSgvsZTW', symObjAddr: 0x4394, symBinAddr: 0x3A9A4, symSize: 0x8C }
  - { offsetInCU: 0xD59, offset: 0x24564, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerCAA15DependentAsTypeA2aDP22configuredDependencies0hJ0QzSgvMZTW', symObjAddr: 0x4420, symBinAddr: 0x3AA30, symSize: 0x6C }
  - { offsetInCU: 0xDAB, offset: 0x245B6, size: 0x8, addend: 0x0, symName: '_$sxq_xq_Iegnnrr_x3key_q_5valuetx_q_tIegnr_SHRzr0_lTRSS_ypTg575$sSD5merge_16uniquingKeysWithySDyxq_Gn_q_q__q_tKXEtKFx_q_tx_q_tcfU_SS_ypTG5Tf3nnpf_n', symObjAddr: 0x4514, symBinAddr: 0x3AB24, symSize: 0x40 }
  - { offsetInCU: 0xF84, offset: 0x2478F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginEventLogging_pWOb', symObjAddr: 0x5368, symBinAddr: 0x3B968, symSize: 0x18 }
  - { offsetInCU: 0xF97, offset: 0x247A2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginEventLogging_pWOc', symObjAddr: 0x59F4, symBinAddr: 0x3BEB0, symSize: 0x44 }
  - { offsetInCU: 0xFAA, offset: 0x247B5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC16TypeDependenciesVSgWOf', symObjAddr: 0x5A38, symBinAddr: 0x3BEF4, symSize: 0x48 }
  - { offsetInCU: 0xFBD, offset: 0x247C8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerCMa', symObjAddr: 0x5A80, symBinAddr: 0x3BF3C, symSize: 0x20 }
  - { offsetInCU: 0xFD0, offset: 0x247DB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC16TypeDependenciesVwCP', symObjAddr: 0x5AB4, symBinAddr: 0x3BF70, symSize: 0x30 }
  - { offsetInCU: 0xFE3, offset: 0x247EE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC16TypeDependenciesVwxx', symObjAddr: 0x5AE4, symBinAddr: 0x3BFA0, symSize: 0x14 }
  - { offsetInCU: 0xFF6, offset: 0x24801, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC16TypeDependenciesVwcp', symObjAddr: 0x5AF8, symBinAddr: 0x3BFB4, symSize: 0x34 }
  - { offsetInCU: 0x1009, offset: 0x24814, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC16TypeDependenciesVwca', symObjAddr: 0x5B2C, symBinAddr: 0x3BFE8, symSize: 0x24 }
  - { offsetInCU: 0x101C, offset: 0x24827, size: 0x8, addend: 0x0, symName: ___swift_memcpy40_8, symObjAddr: 0x5CB8, symBinAddr: 0x3C00C, symSize: 0x14 }
  - { offsetInCU: 0x102F, offset: 0x2483A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC16TypeDependenciesVwta', symObjAddr: 0x5CCC, symBinAddr: 0x3C020, symSize: 0x38 }
  - { offsetInCU: 0x1042, offset: 0x2484D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC16TypeDependenciesVwet', symObjAddr: 0x5D04, symBinAddr: 0x3C058, symSize: 0x48 }
  - { offsetInCU: 0x1055, offset: 0x24860, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC16TypeDependenciesVwst', symObjAddr: 0x5D4C, symBinAddr: 0x3C0A0, symSize: 0x48 }
  - { offsetInCU: 0x1068, offset: 0x24873, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC16TypeDependenciesVMa', symObjAddr: 0x5D94, symBinAddr: 0x3C0E8, symSize: 0x10 }
  - { offsetInCU: 0x1289, offset: 0x24A94, size: 0x8, addend: 0x0, symName: '_$sSTsE6reduceyqd__qd___qd__qd___7ElementQztKXEtKlFShy13FBSDKLoginKit12FBPermissionCG_SSTg504$s13c61Kit18LoginManagerLoggerC12startSession3foryAA0cD0C_tFS2S_AA12E7CtXEfU_Tf1ncn_n', symObjAddr: 0x1B98, symBinAddr: 0x381A8, symSize: 0x308 }
  - { offsetInCU: 0x1488, offset: 0x24C93, size: 0x8, addend: 0x0, symName: '_$ss17_dictionaryUpCastySDyq0_q1_GSDyxq_GSHRzSHR0_r2_lFSS_SbSSypTg5', symObjAddr: 0x3BF0, symBinAddr: 0x3A200, symSize: 0x374 }
  - { offsetInCU: 0x17C3, offset: 0x24FCE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC12startSession3foryAA0cD0C_tF', symObjAddr: 0x0, symBinAddr: 0x36650, symSize: 0x398 }
  - { offsetInCU: 0x19F1, offset: 0x251FC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC03endC06result5erroryAA0cdC6ResultCSg_So7NSErrorCSgtF', symObjAddr: 0x398, symBinAddr: 0x369E8, symSize: 0x48C }
  - { offsetInCU: 0x1C56, offset: 0x25461, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC10endSessionyyF', symObjAddr: 0x824, symBinAddr: 0x36E74, symSize: 0x188 }
  - { offsetInCU: 0x1C99, offset: 0x254A4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC04postC9HeartbeatyyF', symObjAddr: 0x9AC, symBinAddr: 0x36FFC, symSize: 0x48 }
  - { offsetInCU: 0x1CC2, offset: 0x254CD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC14getClientState20authenticationMethod08existingH06loggerSSSgAH_SDySSypGSgACSgtFZ', symObjAddr: 0xA34, symBinAddr: 0x37044, symSize: 0x4 }
  - { offsetInCU: 0x1CFF, offset: 0x2550A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC31willAttemptAppSwitchingBehavior9urlSchemeySS_tF', symObjAddr: 0xA38, symBinAddr: 0x37048, symSize: 0x260 }
  - { offsetInCU: 0x1E41, offset: 0x2564C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC5start20authenticationMethodySS_tF', symObjAddr: 0xC98, symBinAddr: 0x372A8, symSize: 0xA8 }
  - { offsetInCU: 0x1E7F, offset: 0x2568A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC10identifierSSSgvg', symObjAddr: 0xD40, symBinAddr: 0x37350, symSize: 0x48 }
  - { offsetInCU: 0x1E92, offset: 0x2569D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC10identifierSSSgvs', symObjAddr: 0xD88, symBinAddr: 0x37398, symSize: 0x50 }
  - { offsetInCU: 0x1EA5, offset: 0x256B0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC10identifierSSSgvM', symObjAddr: 0xDD8, symBinAddr: 0x373E8, symSize: 0x3C }
  - { offsetInCU: 0x1EB8, offset: 0x256C3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC6extrasSDySSypGvg', symObjAddr: 0xE14, symBinAddr: 0x37424, symSize: 0x34 }
  - { offsetInCU: 0x1ECB, offset: 0x256D6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC6extrasSDySSypGvs', symObjAddr: 0xE48, symBinAddr: 0x37458, symSize: 0x44 }
  - { offsetInCU: 0x1EDE, offset: 0x256E9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC6extrasSDySSypGvM', symObjAddr: 0xE8C, symBinAddr: 0x3749C, symSize: 0x3C }
  - { offsetInCU: 0x1EF1, offset: 0x256FC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC10lastResultSSvg', symObjAddr: 0xEC8, symBinAddr: 0x374D8, symSize: 0x48 }
  - { offsetInCU: 0x1F04, offset: 0x2570F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC10lastResultSSvs', symObjAddr: 0xF10, symBinAddr: 0x37520, symSize: 0x50 }
  - { offsetInCU: 0x1F17, offset: 0x25722, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC10lastResultSSvM', symObjAddr: 0xF60, symBinAddr: 0x37570, symSize: 0x3C }
  - { offsetInCU: 0x1F2A, offset: 0x25735, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC9lastErrorSo7NSErrorCSgvg', symObjAddr: 0xF9C, symBinAddr: 0x375AC, symSize: 0x44 }
  - { offsetInCU: 0x1F3D, offset: 0x25748, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC9lastErrorSo7NSErrorCSgvs', symObjAddr: 0xFE0, symBinAddr: 0x375F0, symSize: 0x44 }
  - { offsetInCU: 0x1F50, offset: 0x2575B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC9lastErrorSo7NSErrorCSgvM', symObjAddr: 0x1024, symBinAddr: 0x37634, symSize: 0x3C }
  - { offsetInCU: 0x1F63, offset: 0x2576E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC10authMethodSSSgvg', symObjAddr: 0x1060, symBinAddr: 0x37670, symSize: 0x48 }
  - { offsetInCU: 0x1F76, offset: 0x25781, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC10authMethodSSSgvs', symObjAddr: 0x10A8, symBinAddr: 0x376B8, symSize: 0x50 }
  - { offsetInCU: 0x1F89, offset: 0x25794, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC10authMethodSSSgvM', symObjAddr: 0x10F8, symBinAddr: 0x37708, symSize: 0x3C }
  - { offsetInCU: 0x1F9C, offset: 0x257A7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC10authMethodSSSgvM.resume.0', symObjAddr: 0x1134, symBinAddr: 0x37744, symSize: 0x4 }
  - { offsetInCU: 0x1FAF, offset: 0x257BA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC12loggingTokenSSSgvg', symObjAddr: 0x1138, symBinAddr: 0x37748, symSize: 0x48 }
  - { offsetInCU: 0x1FC2, offset: 0x257CD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC12loggingTokenSSSgvs', symObjAddr: 0x1180, symBinAddr: 0x37790, symSize: 0x50 }
  - { offsetInCU: 0x1FD5, offset: 0x257E0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC12loggingTokenSSSgvM', symObjAddr: 0x11D0, symBinAddr: 0x377E0, symSize: 0x3C }
  - { offsetInCU: 0x2000, offset: 0x2580B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC10parameters8trackingACSgSDySSypGSg_AA0C8TrackingOtcfC', symObjAddr: 0x13F0, symBinAddr: 0x37A00, symSize: 0x718 }
  - { offsetInCU: 0x21AC, offset: 0x259B7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC12loggingToken8trackingACSgSSSg_AA0C8TrackingOtcfC', symObjAddr: 0x1B08, symBinAddr: 0x38118, symSize: 0x60 }
  - { offsetInCU: 0x21D4, offset: 0x259DF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC12loggingToken8trackingACSgSSSg_AA0C8TrackingOtcfc', symObjAddr: 0x1B68, symBinAddr: 0x38178, symSize: 0x30 }
  - { offsetInCU: 0x225D, offset: 0x25A68, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC21parametersForNewEventSDySo08FBSDKAppI13ParameterNameaypGyF', symObjAddr: 0x1F1C, symBinAddr: 0x3852C, symSize: 0x7F0 }
  - { offsetInCU: 0x263F, offset: 0x25E4A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC8logEvent_6paramsySo08FBSDKAppG4Namea_SDySo0ig9ParameterJ0aypGSgtF', symObjAddr: 0x270C, symBinAddr: 0x38D1C, symSize: 0x350 }
  - { offsetInCU: 0x2756, offset: 0x25F61, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC8logEvent_6result5errorySo08FBSDKAppG4Namea_SSSo7NSErrorCSgtF', symObjAddr: 0x2A5C, symBinAddr: 0x3906C, symSize: 0xFEC }
  - { offsetInCU: 0x2C68, offset: 0x26473, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC21heartbeatTimerDidFireyyF', symObjAddr: 0x3A48, symBinAddr: 0x3A058, symSize: 0xCC }
  - { offsetInCU: 0x2CDE, offset: 0x264E9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerCfd', symObjAddr: 0x3F64, symBinAddr: 0x3A574, symSize: 0x44 }
  - { offsetInCU: 0x2D0B, offset: 0x26516, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerCfD', symObjAddr: 0x3FA8, symBinAddr: 0x3A5B8, symSize: 0x4C }
  - { offsetInCU: 0x2D40, offset: 0x2654B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC16TypeDependenciesV05eventE0AA0C12EventLogging_pvg', symObjAddr: 0x3FF4, symBinAddr: 0x3A604, symSize: 0xC }
  - { offsetInCU: 0x2D53, offset: 0x2655E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC16TypeDependenciesV05eventE0AA0C12EventLogging_pvs', symObjAddr: 0x4000, symBinAddr: 0x3A610, symSize: 0x2C }
  - { offsetInCU: 0x2D66, offset: 0x26571, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC16TypeDependenciesV05eventE0AA0C12EventLogging_pvM', symObjAddr: 0x402C, symBinAddr: 0x3A63C, symSize: 0x10 }
  - { offsetInCU: 0x2D79, offset: 0x26584, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC16TypeDependenciesV05eventE0AA0C12EventLogging_pvM.resume.0', symObjAddr: 0x403C, symBinAddr: 0x3A64C, symSize: 0x4 }
  - { offsetInCU: 0x2D92, offset: 0x2659D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC16TypeDependenciesV05eventE0AeA0C12EventLogging_p_tcfC', symObjAddr: 0x4040, symBinAddr: 0x3A650, symSize: 0x8 }
  - { offsetInCU: 0x2DA5, offset: 0x265B0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC19defaultDependenciesAC04TypeG0VSgvMZ', symObjAddr: 0x412C, symBinAddr: 0x3A73C, symSize: 0x6C }
  - { offsetInCU: 0x2DC4, offset: 0x265CF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC22configuredDependenciesAC04TypeG0VSgvMZ', symObjAddr: 0x430C, symBinAddr: 0x3A91C, symSize: 0x6C }
  - { offsetInCU: 0x2E01, offset: 0x2660C, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV4copyyyFSo26FBSDKAppEventParameterNamea_ypTg5', symObjAddr: 0x48F8, symBinAddr: 0x3AF08, symSize: 0x1D8 }
  - { offsetInCU: 0x2E92, offset: 0x2669D, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV4copyyyFSS_ypTg5', symObjAddr: 0x4AD0, symBinAddr: 0x3B0E0, symSize: 0x1F4 }
  - { offsetInCU: 0x2F2F, offset: 0x2673A, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV4copyyyFSS_SSTg5', symObjAddr: 0x4CC4, symBinAddr: 0x3B2D4, symSize: 0x1C8 }
  - { offsetInCU: 0x2FDE, offset: 0x267E9, size: 0x8, addend: 0x0, symName: '_$sSD11removeValue6forKeyq_Sgx_tFSS_ypTg5', symObjAddr: 0x4E8C, symBinAddr: 0x3B49C, symSize: 0xE8 }
  - { offsetInCU: 0x3086, offset: 0x26891, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV7_delete2atys10_HashTableV6BucketV_tFSo26FBSDKAppEventParameterNamea_ypTg5', symObjAddr: 0x4F74, symBinAddr: 0x3B584, symSize: 0x208 }
  - { offsetInCU: 0x3127, offset: 0x26932, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV7_delete2atys10_HashTableV6BucketV_tFSS_ypTg5', symObjAddr: 0x517C, symBinAddr: 0x3B78C, symSize: 0x1DC }
  - { offsetInCU: 0x31BC, offset: 0x269C7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit18LoginManagerLoggerC12loggingToken8trackingACSgSSSg_AA0C8TrackingOtcfcTf4gnn_n', symObjAddr: 0x580C, symBinAddr: 0x3BD10, symSize: 0x180 }
  - { offsetInCU: 0xD8, offset: 0x26B30, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC5tokenSo16FBSDKAccessTokenCSgvgTo', symObjAddr: 0x144, symBinAddr: 0x3C314, symSize: 0x10 }
  - { offsetInCU: 0xF7, offset: 0x26B4F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC5tokenSo16FBSDKAccessTokenCSgvgTo', symObjAddr: 0x144, symBinAddr: 0x3C314, symSize: 0x10 }
  - { offsetInCU: 0x127, offset: 0x26B7F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC19authenticationTokenSo019FBSDKAuthenticationG0CSgvgTo', symObjAddr: 0x184, symBinAddr: 0x3C354, symSize: 0x10 }
  - { offsetInCU: 0x146, offset: 0x26B9E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC19authenticationTokenSo019FBSDKAuthenticationG0CSgvgTo', symObjAddr: 0x184, symBinAddr: 0x3C354, symSize: 0x10 }
  - { offsetInCU: 0x176, offset: 0x26BCE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC11isCancelledSbvgTo', symObjAddr: 0x1C4, symBinAddr: 0x3C394, symSize: 0x10 }
  - { offsetInCU: 0x195, offset: 0x26BED, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC11isCancelledSbvgTo', symObjAddr: 0x1C4, symBinAddr: 0x3C394, symSize: 0x10 }
  - { offsetInCU: 0x1ED, offset: 0x26C45, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC13loggingExtrasSDySSypGvgTo', symObjAddr: 0x268, symBinAddr: 0x3C438, symSize: 0x7C }
  - { offsetInCU: 0x241, offset: 0x26C99, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC13loggingExtrasSDySSypGvsTo', symObjAddr: 0x32C, symBinAddr: 0x3C4FC, symSize: 0x7C }
  - { offsetInCU: 0x282, offset: 0x26CDA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC5token19authenticationToken11isCancelled18grantedPermissions08declinedL0ACSo011FBSDKAccessH0CSg_So019FBSDKAuthenticationH0CSgSbShySSGAOtcfcTo', symObjAddr: 0x4A0, symBinAddr: 0x3C634, symSize: 0x114 }
  - { offsetInCU: 0x2D9, offset: 0x26D31, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC15addLoggingExtra_6forKeyyyp_SStFTo', symObjAddr: 0x5B4, symBinAddr: 0x3C748, symSize: 0xF0 }
  - { offsetInCU: 0x34B, offset: 0x26DA3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultCACycfcTo', symObjAddr: 0x6F0, symBinAddr: 0x3C884, symSize: 0x2C }
  - { offsetInCU: 0x3D8, offset: 0x26E30, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultCfETo', symObjAddr: 0x750, symBinAddr: 0x3C8E4, symSize: 0x68 }
  - { offsetInCU: 0x4C4, offset: 0x26F1C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultCMa', symObjAddr: 0x928, symBinAddr: 0x3CABC, symSize: 0x20 }
  - { offsetInCU: 0x4D7, offset: 0x26F2F, size: 0x8, addend: 0x0, symName: ___swift_destroy_boxed_opaque_existential_0, symObjAddr: 0x95C, symBinAddr: 0x3CAF0, symSize: 0x20 }
  - { offsetInCU: 0x6D4, offset: 0x2712C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC5token19authenticationToken11isCancelled18grantedPermissions08declinedL0ACSo011FBSDKAccessH0CSg_So019FBSDKAuthenticationH0CSgSbShySSGAOtcfC', symObjAddr: 0x0, symBinAddr: 0x3C1D0, symSize: 0xBC }
  - { offsetInCU: 0x73B, offset: 0x27193, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC15addLoggingExtra_6forKeyyyp_SStF', symObjAddr: 0xBC, symBinAddr: 0x3C28C, symSize: 0x88 }
  - { offsetInCU: 0x788, offset: 0x271E0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC5tokenSo16FBSDKAccessTokenCSgvg', symObjAddr: 0x154, symBinAddr: 0x3C324, symSize: 0x30 }
  - { offsetInCU: 0x7B7, offset: 0x2720F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC19authenticationTokenSo019FBSDKAuthenticationG0CSgvg', symObjAddr: 0x194, symBinAddr: 0x3C364, symSize: 0x30 }
  - { offsetInCU: 0x7E6, offset: 0x2723E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC11isCancelledSbvg', symObjAddr: 0x1D4, symBinAddr: 0x3C3A4, symSize: 0x10 }
  - { offsetInCU: 0x801, offset: 0x27259, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC18grantedPermissionsShySSGvg', symObjAddr: 0x1F0, symBinAddr: 0x3C3C0, symSize: 0x10 }
  - { offsetInCU: 0x822, offset: 0x2727A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC19declinedPermissionsShySSGvg', symObjAddr: 0x258, symBinAddr: 0x3C428, symSize: 0x10 }
  - { offsetInCU: 0x855, offset: 0x272AD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC13loggingExtrasSDySSypGvg', symObjAddr: 0x2E4, symBinAddr: 0x3C4B4, symSize: 0x48 }
  - { offsetInCU: 0x894, offset: 0x272EC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultC5token19authenticationToken11isCancelled18grantedPermissions08declinedL0ACSo011FBSDKAccessH0CSg_So019FBSDKAuthenticationH0CSgSbShySSGAOtcfc', symObjAddr: 0x3A8, symBinAddr: 0x3C578, symSize: 0xBC }
  - { offsetInCU: 0x90D, offset: 0x27365, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultCACycfC', symObjAddr: 0x6A4, symBinAddr: 0x3C838, symSize: 0x20 }
  - { offsetInCU: 0x920, offset: 0x27378, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultCACycfc', symObjAddr: 0x6C4, symBinAddr: 0x3C858, symSize: 0x2C }
  - { offsetInCU: 0x974, offset: 0x273CC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit012LoginManagerC6ResultCfD', symObjAddr: 0x71C, symBinAddr: 0x3C8B0, symSize: 0x34 }
  - { offsetInCU: 0x9A7, offset: 0x273FF, size: 0x8, addend: 0x0, symName: '_$sSD8_VariantV8setValue_6forKeyyq_n_xtFSS_SSTg5', symObjAddr: 0x7B8, symBinAddr: 0x3C94C, symSize: 0xB8 }
  - { offsetInCU: 0xA12, offset: 0x2746A, size: 0x8, addend: 0x0, symName: '_$sSD8_VariantV8setValue_6forKeyyq_n_xtFSS_ypTg5', symObjAddr: 0x870, symBinAddr: 0x3CA04, symSize: 0xB8 }
  - { offsetInCU: 0x27, offset: 0x27502, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateO8rawValueACSgSi_tcfC', symObjAddr: 0x0, symBinAddr: 0x3CB10, symSize: 0x10 }
  - { offsetInCU: 0x73, offset: 0x2754E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateOSYAASY8rawValuexSg03RawG0Qz_tcfCTW', symObjAddr: 0x120, symBinAddr: 0x3CC30, symSize: 0x18 }
  - { offsetInCU: 0xA2, offset: 0x2757D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateOSYAASY8rawValue03RawG0QzvgTW', symObjAddr: 0x138, symBinAddr: 0x3CC48, symSize: 0xC }
  - { offsetInCU: 0xC9, offset: 0x275A4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateOSHAASQWb', symObjAddr: 0x2C, symBinAddr: 0x3CB3C, symSize: 0x4 }
  - { offsetInCU: 0xDC, offset: 0x275B7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateOACSQAAWl', symObjAddr: 0x30, symBinAddr: 0x3CB40, symSize: 0x44 }
  - { offsetInCU: 0x10D, offset: 0x275E8, size: 0x8, addend: 0x0, symName: ___swift_memcpy1_1, symObjAddr: 0x144, symBinAddr: 0x3CC54, symSize: 0xC }
  - { offsetInCU: 0x120, offset: 0x275FB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateOwet', symObjAddr: 0x154, symBinAddr: 0x3CC60, symSize: 0x90 }
  - { offsetInCU: 0x133, offset: 0x2760E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateOwst', symObjAddr: 0x1E4, symBinAddr: 0x3CCF0, symSize: 0xBC }
  - { offsetInCU: 0x146, offset: 0x27621, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateOwug', symObjAddr: 0x2A0, symBinAddr: 0x3CDAC, symSize: 0x8 }
  - { offsetInCU: 0x159, offset: 0x27634, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateOwup', symObjAddr: 0x2A8, symBinAddr: 0x3CDB4, symSize: 0x4 }
  - { offsetInCU: 0x16C, offset: 0x27647, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateOwui', symObjAddr: 0x2AC, symBinAddr: 0x3CDB8, symSize: 0x8 }
  - { offsetInCU: 0x17F, offset: 0x2765A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateOMa', symObjAddr: 0x2B4, symBinAddr: 0x3CDC0, symSize: 0x10 }
  - { offsetInCU: 0x1B5, offset: 0x27690, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x18, symBinAddr: 0x3CB28, symSize: 0x14 }
  - { offsetInCU: 0x257, offset: 0x27732, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateOSHAASH9hashValueSivgTW', symObjAddr: 0x74, symBinAddr: 0x3CB84, symSize: 0x44 }
  - { offsetInCU: 0x2FE, offset: 0x277D9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateOSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0xB8, symBinAddr: 0x3CBC8, symSize: 0x28 }
  - { offsetInCU: 0x34D, offset: 0x27828, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateOSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0xE0, symBinAddr: 0x3CBF0, symSize: 0x40 }
  - { offsetInCU: 0x450, offset: 0x2792B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateO8rawValueACSgSi_tcfC', symObjAddr: 0x0, symBinAddr: 0x3CB10, symSize: 0x10 }
  - { offsetInCU: 0x46D, offset: 0x27948, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginManagerStateO8rawValueSivg', symObjAddr: 0x10, symBinAddr: 0x3CB20, symSize: 0x8 }
  - { offsetInCU: 0x49, offset: 0x279EB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC19defaultDependenciesAC04TypeG0VSgvpZ', symObjAddr: 0x7568, symBinAddr: 0x64558, symSize: 0x0 }
  - { offsetInCU: 0x130, offset: 0x27AD2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC22configuredDependenciesAC04TypeG0VSgvpZ', symObjAddr: 0x7598, symBinAddr: 0x64588, symSize: 0x0 }
  - { offsetInCU: 0x22E, offset: 0x27BD0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC07attemptD04from10completionys5Error_p_ySbctFTo', symObjAddr: 0x244, symBinAddr: 0x3D044, symSize: 0x88 }
  - { offsetInCU: 0x390, offset: 0x27D32, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC19defaultDependencies_WZ', symObjAddr: 0x398, symBinAddr: 0x3D198, symSize: 0x58 }
  - { offsetInCU: 0x3D3, offset: 0x27D75, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC19defaultDependenciesAC04TypeG0VSgvau', symObjAddr: 0x3F0, symBinAddr: 0x3D1F0, symSize: 0x40 }
  - { offsetInCU: 0x41D, offset: 0x27DBF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC22configuredDependencies_WZ', symObjAddr: 0x4D8, symBinAddr: 0x3D2D8, symSize: 0x18 }
  - { offsetInCU: 0x436, offset: 0x27DD8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC22configuredDependenciesAC04TypeG0VSgvau', symObjAddr: 0x4F0, symBinAddr: 0x3D2F0, symSize: 0x40 }
  - { offsetInCU: 0x485, offset: 0x27E27, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterCAA15DependentAsTypeA2aDP22configuredDependencies0hJ0QzSgvsZTW', symObjAddr: 0x6C4, symBinAddr: 0x3D4C4, symSize: 0x84 }
  - { offsetInCU: 0x4B9, offset: 0x27E5B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterCAA15DependentAsTypeA2aDP22configuredDependencies0hJ0QzSgvMZTW', symObjAddr: 0x748, symBinAddr: 0x3D548, symSize: 0x6C }
  - { offsetInCU: 0x52C, offset: 0x27ECE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14LoginProviding_pWOc', symObjAddr: 0xB74, symBinAddr: 0x3D974, symSize: 0x44 }
  - { offsetInCU: 0x53F, offset: 0x27EE1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesVSgWOc', symObjAddr: 0xBF0, symBinAddr: 0x3D9B8, symSize: 0x48 }
  - { offsetInCU: 0x552, offset: 0x27EF4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesVSgWOf', symObjAddr: 0xC78, symBinAddr: 0x3DA00, symSize: 0x48 }
  - { offsetInCU: 0x565, offset: 0x27F07, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesVSgWOh', symObjAddr: 0xCC0, symBinAddr: 0x3DA48, symSize: 0x40 }
  - { offsetInCU: 0x578, offset: 0x27F1A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterCMa', symObjAddr: 0xD00, symBinAddr: 0x3DA88, symSize: 0x20 }
  - { offsetInCU: 0x58B, offset: 0x27F2D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesVwCP', symObjAddr: 0xD34, symBinAddr: 0x3DABC, symSize: 0x30 }
  - { offsetInCU: 0x59E, offset: 0x27F40, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesVwxx', symObjAddr: 0xD64, symBinAddr: 0x3DAEC, symSize: 0x4 }
  - { offsetInCU: 0x5B2, offset: 0x27F54, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesVwcp', symObjAddr: 0xD68, symBinAddr: 0x3DAF0, symSize: 0x40 }
  - { offsetInCU: 0x5C5, offset: 0x27F67, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesVwca', symObjAddr: 0xDA8, symBinAddr: 0x3DB30, symSize: 0x30 }
  - { offsetInCU: 0x5D8, offset: 0x27F7A, size: 0x8, addend: 0x0, symName: ___swift_memcpy48_8, symObjAddr: 0xF40, symBinAddr: 0x3DB60, symSize: 0x14 }
  - { offsetInCU: 0x5EB, offset: 0x27F8D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesVwta', symObjAddr: 0xF54, symBinAddr: 0x3DB74, symSize: 0x38 }
  - { offsetInCU: 0x5FE, offset: 0x27FA0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesVwet', symObjAddr: 0xF8C, symBinAddr: 0x3DBAC, symSize: 0x48 }
  - { offsetInCU: 0x611, offset: 0x27FB3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesVwst', symObjAddr: 0xFD4, symBinAddr: 0x3DBF4, symSize: 0x4C }
  - { offsetInCU: 0x624, offset: 0x27FC6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesVMa', symObjAddr: 0x1020, symBinAddr: 0x3DC40, symSize: 0x10 }
  - { offsetInCU: 0x642, offset: 0x27FE4, size: 0x8, addend: 0x0, symName: '_$s10ObjectiveC8ObjCBoolVIeyBy_SbIegy_TRTA', symObjAddr: 0x1054, symBinAddr: 0x3DC74, symSize: 0x14 }
  - { offsetInCU: 0x66A, offset: 0x2800C, size: 0x8, addend: 0x0, symName: '_$sSo16FBSDKAccessTokenCMa', symObjAddr: 0x1068, symBinAddr: 0x3DC88, symSize: 0x3C }
  - { offsetInCU: 0x68C, offset: 0x2802E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC07attemptD04from10completionys5Error_p_ySbctFyAA0c7ManagerC6ResultCSg_sAG_pSgtcfU_TA', symObjAddr: 0x10EC, symBinAddr: 0x3DCE8, symSize: 0x68 }
  - { offsetInCU: 0x70B, offset: 0x280AD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesVWOh', symObjAddr: 0x1154, symBinAddr: 0x3DD50, symSize: 0x24 }
  - { offsetInCU: 0x783, offset: 0x28125, size: 0x8, addend: 0x0, symName: '_$sSlsE3mapySayqd__Gqd__7ElementQzKXEKlFShy12FBSDKCoreKit10PermissionOG_SSTg5091$s13FBSDKLoginKit22LoginRecoveryAttempterC07attemptD04from10completionys5Error_p_ySbctFSS09c4B010E52Ocfu_32f5ef5a0b9ade21eb65ffea7b618f60adAJSSTf3nnpk_nTf1cn_n', symObjAddr: 0x0, symBinAddr: 0x3CE00, symSize: 0x238 }
  - { offsetInCU: 0xACA, offset: 0x2846C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC07attemptD04from10completionys5Error_p_ySbctF', symObjAddr: 0x238, symBinAddr: 0x3D038, symSize: 0xC }
  - { offsetInCU: 0xB78, offset: 0x2851A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterCfd', symObjAddr: 0x2CC, symBinAddr: 0x3D0CC, symSize: 0x8 }
  - { offsetInCU: 0xB9B, offset: 0x2853D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterCfD', symObjAddr: 0x2D4, symBinAddr: 0x3D0D4, symSize: 0x10 }
  - { offsetInCU: 0xBC4, offset: 0x28566, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterCACycfC', symObjAddr: 0x2E4, symBinAddr: 0x3D0E4, symSize: 0x10 }
  - { offsetInCU: 0xBD7, offset: 0x28579, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterCACycfc', symObjAddr: 0x2F4, symBinAddr: 0x3D0F4, symSize: 0x8 }
  - { offsetInCU: 0xBFA, offset: 0x2859C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesV13loginProviderAA0C9Providing_pvg', symObjAddr: 0x2FC, symBinAddr: 0x3D0FC, symSize: 0xC }
  - { offsetInCU: 0xC0D, offset: 0x285AF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesV13loginProviderAA0C9Providing_pvs', symObjAddr: 0x308, symBinAddr: 0x3D108, symSize: 0x2C }
  - { offsetInCU: 0xC20, offset: 0x285C2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesV13loginProviderAA0C9Providing_pvM', symObjAddr: 0x334, symBinAddr: 0x3D134, symSize: 0x10 }
  - { offsetInCU: 0xC33, offset: 0x285D5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesV13loginProviderAA0C9Providing_pvM.resume.0', symObjAddr: 0x344, symBinAddr: 0x3D144, symSize: 0x4 }
  - { offsetInCU: 0xC4C, offset: 0x285EE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesV19accessTokenProviderSo011FBSDKAccessI9Providing_pXpvg', symObjAddr: 0x348, symBinAddr: 0x3D148, symSize: 0x8 }
  - { offsetInCU: 0xC5F, offset: 0x28601, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesV19accessTokenProviderSo011FBSDKAccessI9Providing_pXpvs', symObjAddr: 0x350, symBinAddr: 0x3D150, symSize: 0x8 }
  - { offsetInCU: 0xC72, offset: 0x28614, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesV19accessTokenProviderSo011FBSDKAccessI9Providing_pXpvM', symObjAddr: 0x358, symBinAddr: 0x3D158, symSize: 0x10 }
  - { offsetInCU: 0xC85, offset: 0x28627, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesV19accessTokenProviderSo011FBSDKAccessI9Providing_pXpvM.resume.0', symObjAddr: 0x368, symBinAddr: 0x3D168, symSize: 0x4 }
  - { offsetInCU: 0xC9E, offset: 0x28640, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC16TypeDependenciesV13loginProvider011accessTokenI0AeA0C9Providing_p_So011FBSDKAccesskL0_pXptcfC', symObjAddr: 0x36C, symBinAddr: 0x3D16C, symSize: 0x2C }
  - { offsetInCU: 0xCC3, offset: 0x28665, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC19defaultDependenciesAC04TypeG0VSgvMZ', symObjAddr: 0x468, symBinAddr: 0x3D268, symSize: 0x6C }
  - { offsetInCU: 0xCE2, offset: 0x28684, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC19defaultDependenciesAC04TypeG0VSgvMZ.resume.0', symObjAddr: 0x4D4, symBinAddr: 0x3D2D4, symSize: 0x4 }
  - { offsetInCU: 0xCF6, offset: 0x28698, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC22configuredDependenciesAC04TypeG0VSgvMZ', symObjAddr: 0x63C, symBinAddr: 0x3D43C, symSize: 0x6C }
  - { offsetInCU: 0xD39, offset: 0x286DB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit22LoginRecoveryAttempterC07attemptD04from10completionys5Error_p_ySbctFTf4dnn_n', symObjAddr: 0x9E0, symBinAddr: 0x3D7E0, symSize: 0x194 }
  - { offsetInCU: 0x2B, offset: 0x287F5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultO012loginManagerD0AA0cfcD0CSgvg', symObjAddr: 0x238, symBinAddr: 0x3DFB4, symSize: 0x15C }
  - { offsetInCU: 0x10A, offset: 0x288D4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultO6result5errorAcA0c7ManagercD0CSg_s5Error_pSgtcfCTf4nnd_n', symObjAddr: 0x618, symBinAddr: 0x3E394, symSize: 0x4D4 }
  - { offsetInCU: 0x5D4, offset: 0x28D9E, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_ypTgmq5Tf4g_n', symObjAddr: 0x514, symBinAddr: 0x3E290, symSize: 0x104 }
  - { offsetInCU: 0x7C1, offset: 0x28F8B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultOwCP', symObjAddr: 0xAEC, symBinAddr: 0x3E868, symSize: 0x30 }
  - { offsetInCU: 0x7D4, offset: 0x28F9E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultOWOy', symObjAddr: 0xB1C, symBinAddr: 0x3E898, symSize: 0x60 }
  - { offsetInCU: 0x7E7, offset: 0x28FB1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultOwxx', symObjAddr: 0xB7C, symBinAddr: 0x3E8F8, symSize: 0x14 }
  - { offsetInCU: 0x7FA, offset: 0x28FC4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultOwcp', symObjAddr: 0xBD8, symBinAddr: 0x3E90C, symSize: 0x5C }
  - { offsetInCU: 0x80D, offset: 0x28FD7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultOwca', symObjAddr: 0xC34, symBinAddr: 0x3E968, symSize: 0x6C }
  - { offsetInCU: 0x820, offset: 0x28FEA, size: 0x8, addend: 0x0, symName: ___swift_memcpy25_8, symObjAddr: 0xCA0, symBinAddr: 0x3E9D4, symSize: 0x14 }
  - { offsetInCU: 0x833, offset: 0x28FFD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultOwta', symObjAddr: 0xCB4, symBinAddr: 0x3E9E8, symSize: 0x4C }
  - { offsetInCU: 0x846, offset: 0x29010, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultOwet', symObjAddr: 0xD00, symBinAddr: 0x3EA34, symSize: 0x48 }
  - { offsetInCU: 0x859, offset: 0x29023, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultOwst', symObjAddr: 0xD48, symBinAddr: 0x3EA7C, symSize: 0x48 }
  - { offsetInCU: 0x86C, offset: 0x29036, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultOwug', symObjAddr: 0xD90, symBinAddr: 0x3EAC4, symSize: 0x18 }
  - { offsetInCU: 0x87F, offset: 0x29049, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultOwup', symObjAddr: 0xDA8, symBinAddr: 0x3EADC, symSize: 0x4 }
  - { offsetInCU: 0x892, offset: 0x2905C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultOwui', symObjAddr: 0xDAC, symBinAddr: 0x3EAE0, symSize: 0x1C }
  - { offsetInCU: 0x8A5, offset: 0x2906F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultOMa', symObjAddr: 0xDC8, symBinAddr: 0x3EAFC, symSize: 0x10 }
  - { offsetInCU: 0xB6B, offset: 0x29335, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultO012loginManagerD0AA0cfcD0CSgvg', symObjAddr: 0x238, symBinAddr: 0x3DFB4, symSize: 0x15C }
  - { offsetInCU: 0xC97, offset: 0x29461, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultO5errors5Error_pSgvg', symObjAddr: 0x394, symBinAddr: 0x3E110, symSize: 0x38 }
  - { offsetInCU: 0xCC0, offset: 0x2948A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit11LoginResultO6result5errorAcA0c7ManagercD0CSg_s5Error_pSgtcfC', symObjAddr: 0x3CC, symBinAddr: 0x3E148, symSize: 0x4 }
  - { offsetInCU: 0xCD9, offset: 0x294A3, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4findys10_HashTableV6BucketV6bucket_Sb5foundtxSHRzlFSS_Tgq5', symObjAddr: 0x3D0, symBinAddr: 0x3E14C, symSize: 0x64 }
  - { offsetInCU: 0xCF9, offset: 0x294C3, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4find_9hashValues10_HashTableV6BucketV6bucket_Sb5foundtx_SitSHRzlFSS_Tgq5', symObjAddr: 0x434, symBinAddr: 0x3E1B0, symSize: 0xE0 }
  - { offsetInCU: 0x27, offset: 0x295FD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13LoginTrackingO8rawValueACSgSu_tcfC', symObjAddr: 0x0, symBinAddr: 0x3EB0C, symSize: 0x20 }
  - { offsetInCU: 0x73, offset: 0x29649, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13LoginTrackingOSYAASY8rawValuexSg03RawF0Qz_tcfCTW', symObjAddr: 0x12C, symBinAddr: 0x3EC38, symSize: 0x30 }
  - { offsetInCU: 0xA2, offset: 0x29678, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13LoginTrackingOSYAASY8rawValue03RawF0QzvgTW', symObjAddr: 0x15C, symBinAddr: 0x3EC68, symSize: 0xC }
  - { offsetInCU: 0xC9, offset: 0x2969F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13LoginTrackingOSHAASQWb', symObjAddr: 0x38, symBinAddr: 0x3EB44, symSize: 0x4 }
  - { offsetInCU: 0xDC, offset: 0x296B2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13LoginTrackingOACSQAAWl', symObjAddr: 0x3C, symBinAddr: 0x3EB48, symSize: 0x44 }
  - { offsetInCU: 0x10D, offset: 0x296E3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13LoginTrackingOMa', symObjAddr: 0x168, symBinAddr: 0x3EC74, symSize: 0x10 }
  - { offsetInCU: 0x143, offset: 0x29719, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13LoginTrackingOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x24, symBinAddr: 0x3EB30, symSize: 0x14 }
  - { offsetInCU: 0x1E5, offset: 0x297BB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13LoginTrackingOSHAASH9hashValueSivgTW', symObjAddr: 0x80, symBinAddr: 0x3EB8C, symSize: 0x44 }
  - { offsetInCU: 0x28C, offset: 0x29862, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13LoginTrackingOSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0xC4, symBinAddr: 0x3EBD0, symSize: 0x28 }
  - { offsetInCU: 0x2DB, offset: 0x298B1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13LoginTrackingOSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0xEC, symBinAddr: 0x3EBF8, symSize: 0x40 }
  - { offsetInCU: 0x3DE, offset: 0x299B4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13LoginTrackingO8rawValueACSgSu_tcfC', symObjAddr: 0x0, symBinAddr: 0x3EB0C, symSize: 0x20 }
  - { offsetInCU: 0x3FB, offset: 0x299D1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit13LoginTrackingO8rawValueSuvg', symObjAddr: 0x20, symBinAddr: 0x3EB2C, symSize: 0x4 }
  - { offsetInCU: 0x4A, offset: 0x29A72, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV22configuredDependenciesAC04TypeF0VSgvpZ', symObjAddr: 0x16DE8, symBinAddr: 0x645B8, symSize: 0x0 }
  - { offsetInCU: 0x236, offset: 0x29C5E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV19defaultDependenciesAC04TypeF0VSgvpZ', symObjAddr: 0x16E50, symBinAddr: 0x64620, symSize: 0x0 }
  - { offsetInCU: 0x3C8, offset: 0x29DF0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterVAA0C10CompletingA2aDP08completeC07handleryyAA01_C20CompletionParametersCc_tFTW', symObjAddr: 0x31C8, symBinAddr: 0x41E4C, symSize: 0x20 }
  - { offsetInCU: 0x409, offset: 0x29E31, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterVAA0C10CompletingA2aDP08completeC05nonce12codeVerifier7handlerySSSg_AJyAA01_C20CompletionParametersCctFTW', symObjAddr: 0x31E8, symBinAddr: 0x41E6C, symSize: 0x8 }
  - { offsetInCU: 0x424, offset: 0x29E4C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV9challenge4fromSSSgSDySSypG_tFTf4nd_n', symObjAddr: 0x39CC, symBinAddr: 0x42650, symSize: 0x348 }
  - { offsetInCU: 0x4DD, offset: 0x29F05, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV5error4froms5Error_pSgSDySSypG_tFTf4nd_n', symObjAddr: 0x3D14, symBinAddr: 0x42998, symSize: 0x77C }
  - { offsetInCU: 0x7CF, offset: 0x2A1F7, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV13urlParameters5appIDACSDySSypG_SStcfCTf4nnd_n', symObjAddr: 0x4490, symBinAddr: 0x43114, symSize: 0x7B0 }
  - { offsetInCU: 0x9D4, offset: 0x2A3FC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV7profile4with11permissions09FBSDKCoreB07ProfileCSgAG25AuthenticationTokenClaimsC_ShySSGSgtFTf4nnd_n', symObjAddr: 0x4EF0, symBinAddr: 0x43A5C, symSize: 0x808 }
  - { offsetInCU: 0xD27, offset: 0x2A74F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV22configuredDependencies_WZ', symObjAddr: 0x33D0, symBinAddr: 0x42054, symSize: 0x20 }
  - { offsetInCU: 0xD42, offset: 0x2A76A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV22configuredDependenciesAC04TypeF0VSgvau', symObjAddr: 0x33F0, symBinAddr: 0x42074, symSize: 0x40 }
  - { offsetInCU: 0xD84, offset: 0x2A7AC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV19defaultDependencies_WZ', symObjAddr: 0x34D4, symBinAddr: 0x42158, symSize: 0x190 }
  - { offsetInCU: 0xE43, offset: 0x2A86B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV19defaultDependenciesAC04TypeF0VSgvau', symObjAddr: 0x3664, symBinAddr: 0x422E8, symSize: 0x40 }
  - { offsetInCU: 0xE94, offset: 0x2A8BC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterVAA15DependentAsTypeA2aDP22configuredDependencies0gI0QzSgvsZTW', symObjAddr: 0x384C, symBinAddr: 0x424D0, symSize: 0x8C }
  - { offsetInCU: 0xECA, offset: 0x2A8F2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterVAA15DependentAsTypeA2aDP22configuredDependencies0gI0QzSgvMZTW', symObjAddr: 0x38D8, symBinAddr: 0x4255C, symSize: 0x6C }
  - { offsetInCU: 0xF4E, offset: 0x2A976, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DateVSgWOd', symObjAddr: 0x4CC4, symBinAddr: 0x438C4, symSize: 0x48 }
  - { offsetInCU: 0xF61, offset: 0x2A989, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesVWOh', symObjAddr: 0x4D48, symBinAddr: 0x4390C, symSize: 0x2C }
  - { offsetInCU: 0xF74, offset: 0x2A99C, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x4D94, symBinAddr: 0x43958, symSize: 0x10 }
  - { offsetInCU: 0xF87, offset: 0x2A9AF, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x4DA4, symBinAddr: 0x43968, symSize: 0x8 }
  - { offsetInCU: 0xF9A, offset: 0x2A9C2, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV24fetchAndSetPropertiesFor10parameters5nonce7handleryAA01_C20CompletionParametersC_SSyAIctFySo24FBSDKAuthenticationTokenCSgcfU_TA', symObjAddr: 0x4EE0, symBinAddr: 0x43A4C, symSize: 0x10 }
  - { offsetInCU: 0xFAD, offset: 0x2A9D5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit15ProfileCreating_pWOb', symObjAddr: 0x573C, symBinAddr: 0x442A8, symSize: 0x18 }
  - { offsetInCU: 0xFC0, offset: 0x2A9E8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesVSgWOf', symObjAddr: 0x5754, symBinAddr: 0x442C0, symSize: 0x48 }
  - { offsetInCU: 0xFD3, offset: 0x2A9FB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterVMa', symObjAddr: 0x579C, symBinAddr: 0x44308, symSize: 0x10 }
  - { offsetInCU: 0xFE6, offset: 0x2AA0E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesVwCP', symObjAddr: 0x57AC, symBinAddr: 0x44318, symSize: 0x30 }
  - { offsetInCU: 0xFF9, offset: 0x2AA21, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesVwxx', symObjAddr: 0x57DC, symBinAddr: 0x44348, symSize: 0x3C }
  - { offsetInCU: 0x100C, offset: 0x2AA34, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesVwcp', symObjAddr: 0x5818, symBinAddr: 0x44384, symSize: 0x80 }
  - { offsetInCU: 0x101F, offset: 0x2AA47, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesVwca', symObjAddr: 0x5898, symBinAddr: 0x44404, symSize: 0x84 }
  - { offsetInCU: 0x1032, offset: 0x2AA5A, size: 0x8, addend: 0x0, symName: ___swift_memcpy104_8, symObjAddr: 0x5A84, symBinAddr: 0x44488, symSize: 0x2C }
  - { offsetInCU: 0x1045, offset: 0x2AA6D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesVwta', symObjAddr: 0x5AB0, symBinAddr: 0x444B4, symSize: 0x80 }
  - { offsetInCU: 0x1058, offset: 0x2AA80, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesVwet', symObjAddr: 0x5B30, symBinAddr: 0x44534, symSize: 0x48 }
  - { offsetInCU: 0x106B, offset: 0x2AA93, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesVwst', symObjAddr: 0x5B78, symBinAddr: 0x4457C, symSize: 0x58 }
  - { offsetInCU: 0x107E, offset: 0x2AAA6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesVMa', symObjAddr: 0x5BD0, symBinAddr: 0x445D4, symSize: 0x10 }
  - { offsetInCU: 0x1091, offset: 0x2AAB9, size: 0x8, addend: 0x0, symName: '_$sSo8NSNumberCMa', symObjAddr: 0x5BE0, symBinAddr: 0x445E4, symSize: 0x3C }
  - { offsetInCU: 0x148B, offset: 0x2AEB3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV13urlParameters5appIDACSDySSypG_SStcfC', symObjAddr: 0x0, symBinAddr: 0x3EC84, symSize: 0x4 }
  - { offsetInCU: 0x149E, offset: 0x2AEC6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV10parametersAA01_C20CompletionParametersCvg', symObjAddr: 0x4, symBinAddr: 0x3EC88, symSize: 0x4 }
  - { offsetInCU: 0x14F9, offset: 0x2AF21, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV13setParameters6values5appIDySDySSypG_SStF', symObjAddr: 0x8, symBinAddr: 0x3EC8C, symSize: 0xD60 }
  - { offsetInCU: 0x18A4, offset: 0x2B2CC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV5error4froms5Error_pSgSDySSypG_tF', symObjAddr: 0xD68, symBinAddr: 0x3F9EC, symSize: 0x4 }
  - { offsetInCU: 0x18C3, offset: 0x2B2EB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV08completeC07handleryyAA01_C20CompletionParametersCc_tF', symObjAddr: 0xD6C, symBinAddr: 0x3F9F0, symSize: 0x20 }
  - { offsetInCU: 0x18FB, offset: 0x2B323, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV08completeC05nonce12codeVerifier7handlerySSSg_AHyAA01_C20CompletionParametersCctF', symObjAddr: 0xD8C, symBinAddr: 0x3FA10, symSize: 0x340 }
  - { offsetInCU: 0x1A00, offset: 0x2B428, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV25exchangeCodeForTokensWith5nonce12codeVerifier7handlerySSSg_AHyAA01_C20CompletionParametersCctF', symObjAddr: 0x10CC, symBinAddr: 0x3FD50, symSize: 0x870 }
  - { offsetInCU: 0x1C22, offset: 0x2B64A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV25exchangeCodeForTokensWith5nonce12codeVerifier7handlerySSSg_AHyAA01_C20CompletionParametersCctFySo27FBSDKGraphRequestConnecting_pSg_ypSgs5Error_pSgtcfU_', symObjAddr: 0x2B88, symBinAddr: 0x4180C, symSize: 0x640 }
  - { offsetInCU: 0x1D92, offset: 0x2B7BA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV25exchangeNonceForTokenWith7handler014authenticationF0yyAA01_C20CompletionParametersCc_SStF', symObjAddr: 0x193C, symBinAddr: 0x405C0, symSize: 0x55C }
  - { offsetInCU: 0x1F49, offset: 0x2B971, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV25exchangeNonceForTokenWith7handler014authenticationF0yyAA01_C20CompletionParametersCc_SStFySo27FBSDKGraphRequestConnecting_pSg_ypSgs5Error_pSgtcfU_', symObjAddr: 0x2760, symBinAddr: 0x413E4, symSize: 0x428 }
  - { offsetInCU: 0x2013, offset: 0x2BA3B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV24fetchAndSetPropertiesFor10parameters5nonce7handleryAA01_C20CompletionParametersC_SSyAIctF', symObjAddr: 0x1E98, symBinAddr: 0x40B1C, symSize: 0x308 }
  - { offsetInCU: 0x20D2, offset: 0x2BAFA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV24fetchAndSetPropertiesFor10parameters5nonce7handleryAA01_C20CompletionParametersC_SSyAIctFySo24FBSDKAuthenticationTokenCSgcfU_', symObjAddr: 0x21A0, symBinAddr: 0x40E24, symSize: 0x204 }
  - { offsetInCU: 0x21EA, offset: 0x2BC12, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV7profile4with11permissions09FBSDKCoreB07ProfileCSgAG25AuthenticationTokenClaimsC_ShySSGSgtF', symObjAddr: 0x23A4, symBinAddr: 0x41028, symSize: 0x4 }
  - { offsetInCU: 0x21FE, offset: 0x2BC26, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV18expirationDateFrom10parameters10Foundation0F0VSDySSypG_tF', symObjAddr: 0x23A8, symBinAddr: 0x4102C, symSize: 0x2CC }
  - { offsetInCU: 0x231C, offset: 0x2BD44, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV28dataAccessExpirationDateFrom10parameters10Foundation0H0VSDySSypG_tF', symObjAddr: 0x2674, symBinAddr: 0x412F8, symSize: 0xE8 }
  - { offsetInCU: 0x2387, offset: 0x2BDAF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV9challenge4fromSSSgSDySSypG_tF', symObjAddr: 0x275C, symBinAddr: 0x413E0, symSize: 0x4 }
  - { offsetInCU: 0x23C2, offset: 0x2BDEA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV14profileFactoryAA15ProfileCreating_pvg', symObjAddr: 0x31F0, symBinAddr: 0x41E74, symSize: 0x1C }
  - { offsetInCU: 0x23D5, offset: 0x2BDFD, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV14profileFactoryAA15ProfileCreating_pvs', symObjAddr: 0x320C, symBinAddr: 0x41E90, symSize: 0x2C }
  - { offsetInCU: 0x23E8, offset: 0x2BE10, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV14profileFactoryAA15ProfileCreating_pvM', symObjAddr: 0x3238, symBinAddr: 0x41EBC, symSize: 0x10 }
  - { offsetInCU: 0x23FB, offset: 0x2BE23, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV14profileFactoryAA15ProfileCreating_pvM.resume.0', symObjAddr: 0x3248, symBinAddr: 0x41ECC, symSize: 0x4 }
  - { offsetInCU: 0x240E, offset: 0x2BE36, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV26authenticationTokenCreatorAA014AuthenticationH8Creating_pvg', symObjAddr: 0x324C, symBinAddr: 0x41ED0, symSize: 0x1C }
  - { offsetInCU: 0x2421, offset: 0x2BE49, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV26authenticationTokenCreatorAA014AuthenticationH8Creating_pvs', symObjAddr: 0x3268, symBinAddr: 0x41EEC, symSize: 0x30 }
  - { offsetInCU: 0x2434, offset: 0x2BE5C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV26authenticationTokenCreatorAA014AuthenticationH8Creating_pvM', symObjAddr: 0x3298, symBinAddr: 0x41F1C, symSize: 0x10 }
  - { offsetInCU: 0x2447, offset: 0x2BE6F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV26authenticationTokenCreatorAA014AuthenticationH8Creating_pvM.resume.0', symObjAddr: 0x32A8, symBinAddr: 0x41F2C, symSize: 0x4 }
  - { offsetInCU: 0x245A, offset: 0x2BE82, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV19graphRequestFactorySo010FBSDKGraphhI0_pvg', symObjAddr: 0x32AC, symBinAddr: 0x41F30, symSize: 0x8 }
  - { offsetInCU: 0x246D, offset: 0x2BE95, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV19graphRequestFactorySo010FBSDKGraphhI0_pvs', symObjAddr: 0x32B4, symBinAddr: 0x41F38, symSize: 0x28 }
  - { offsetInCU: 0x2480, offset: 0x2BEA8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV19graphRequestFactorySo010FBSDKGraphhI0_pvM', symObjAddr: 0x32DC, symBinAddr: 0x41F60, symSize: 0x10 }
  - { offsetInCU: 0x2493, offset: 0x2BEBB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV19graphRequestFactorySo010FBSDKGraphhI0_pvM.resume.0', symObjAddr: 0x32EC, symBinAddr: 0x41F70, symSize: 0x4 }
  - { offsetInCU: 0x24A6, offset: 0x2BECE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV15internalUtilitySo15FBSDKURLHosting_pvg', symObjAddr: 0x32F0, symBinAddr: 0x41F74, symSize: 0x8 }
  - { offsetInCU: 0x24B9, offset: 0x2BEE1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV15internalUtilitySo15FBSDKURLHosting_pvs', symObjAddr: 0x32F8, symBinAddr: 0x41F7C, symSize: 0x28 }
  - { offsetInCU: 0x24CC, offset: 0x2BEF4, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV15internalUtilitySo15FBSDKURLHosting_pvM', symObjAddr: 0x3320, symBinAddr: 0x41FA4, symSize: 0x10 }
  - { offsetInCU: 0x24DF, offset: 0x2BF07, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV15internalUtilitySo15FBSDKURLHosting_pvM.resume.0', symObjAddr: 0x3330, symBinAddr: 0x41FB4, symSize: 0x4 }
  - { offsetInCU: 0x24F2, offset: 0x2BF1A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV12errorFactorySo18FBSDKErrorCreating_pvg', symObjAddr: 0x3334, symBinAddr: 0x41FB8, symSize: 0x8 }
  - { offsetInCU: 0x2505, offset: 0x2BF2D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV12errorFactorySo18FBSDKErrorCreating_pvs', symObjAddr: 0x333C, symBinAddr: 0x41FC0, symSize: 0x28 }
  - { offsetInCU: 0x2518, offset: 0x2BF40, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV12errorFactorySo18FBSDKErrorCreating_pvM', symObjAddr: 0x3364, symBinAddr: 0x41FE8, symSize: 0x10 }
  - { offsetInCU: 0x252B, offset: 0x2BF53, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV12errorFactorySo18FBSDKErrorCreating_pvM.resume.0', symObjAddr: 0x3374, symBinAddr: 0x41FF8, symSize: 0x4 }
  - { offsetInCU: 0x2544, offset: 0x2BF6C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV16TypeDependenciesV14profileFactory26authenticationTokenCreator012graphRequestH015internalUtility05errorH0AeA15ProfileCreating_p_AA014AuthenticationjR0_pSo010FBSDKGraphmH0_pSo15FBSDKURLHosting_pSo010FBSDKErrorR0_ptcfC', symObjAddr: 0x3378, symBinAddr: 0x41FFC, symSize: 0x58 }
  - { offsetInCU: 0x2557, offset: 0x2BF7F, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV22configuredDependenciesAC04TypeF0VSgvMZ', symObjAddr: 0x3468, symBinAddr: 0x420EC, symSize: 0x6C }
  - { offsetInCU: 0x25BD, offset: 0x2BFE5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV19defaultDependenciesAC04TypeF0VSgvMZ', symObjAddr: 0x37C0, symBinAddr: 0x42444, symSize: 0x6C }
  - { offsetInCU: 0x25DC, offset: 0x2C004, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit17LoginURLCompleterV19defaultDependenciesAC04TypeF0VSgvMZ.resume.0', symObjAddr: 0x382C, symBinAddr: 0x424B0, symSize: 0x4 }
  - { offsetInCU: 0x90, offset: 0x2C18D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginUtilityO9getUserID4fromSSSgAF_tFZTf4nd_n', symObjAddr: 0x70, symBinAddr: 0x44784, symSize: 0x304 }
  - { offsetInCU: 0x1AB, offset: 0x2C2A8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginUtilityO18getQueryParameters4fromSDySSypGSg10Foundation3URLV_tFZTf4nd_n', symObjAddr: 0x374, symBinAddr: 0x44A88, symSize: 0x5B4 }
  - { offsetInCU: 0x35F, offset: 0x2C45C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginUtilityOMa', symObjAddr: 0x928, symBinAddr: 0x4503C, symSize: 0x10 }
  - { offsetInCU: 0x372, offset: 0x2C46F, size: 0x8, addend: 0x0, symName: '_$s10Foundation3URLVSgWOc', symObjAddr: 0xA3C, symBinAddr: 0x4504C, symSize: 0x48 }
  - { offsetInCU: 0x54A, offset: 0x2C647, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginUtilityO17stringForAudienceySSAA07DefaultG0OFZ', symObjAddr: 0x0, symBinAddr: 0x44714, symSize: 0x68 }
  - { offsetInCU: 0x57A, offset: 0x2C677, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginUtilityO18getQueryParameters4fromSDySSypGSg10Foundation3URLV_tFZ', symObjAddr: 0x68, symBinAddr: 0x4477C, symSize: 0x4 }
  - { offsetInCU: 0x58D, offset: 0x2C68A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit12LoginUtilityO9getUserID4fromSSSgAF_tFZ', symObjAddr: 0x6C, symBinAddr: 0x44780, symSize: 0x4 }
  - { offsetInCU: 0x27, offset: 0x2C74D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24MissingDependenciesErrorV3forACyxGxm_tcfC', symObjAddr: 0x0, symBinAddr: 0x450D0, symSize: 0x4 }
  - { offsetInCU: 0x73, offset: 0x2C799, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24MissingDependenciesErrorVyxGs23CustomStringConvertibleAAsAEP11descriptionSSvgTW', symObjAddr: 0xC4, symBinAddr: 0x45194, symSize: 0x8 }
  - { offsetInCU: 0xA4, offset: 0x2C7CA, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24MissingDependenciesErrorVMi', symObjAddr: 0xCC, symBinAddr: 0x4519C, symSize: 0x8 }
  - { offsetInCU: 0xB7, offset: 0x2C7DD, size: 0x8, addend: 0x0, symName: ___swift_memcpy8_8, symObjAddr: 0xD4, symBinAddr: 0x451A4, symSize: 0xC }
  - { offsetInCU: 0xCA, offset: 0x2C7F0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24MissingDependenciesErrorVwet', symObjAddr: 0xE4, symBinAddr: 0x451B0, symSize: 0x48 }
  - { offsetInCU: 0xDD, offset: 0x2C803, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24MissingDependenciesErrorVwst', symObjAddr: 0x12C, symBinAddr: 0x451F8, symSize: 0x3C }
  - { offsetInCU: 0xF0, offset: 0x2C816, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24MissingDependenciesErrorVMa', symObjAddr: 0x168, symBinAddr: 0x45234, symSize: 0xC }
  - { offsetInCU: 0x103, offset: 0x2C829, size: 0x8, addend: 0x0, symName: ___swift_instantiateGenericMetadata, symObjAddr: 0x174, symBinAddr: 0x45240, symSize: 0x2C }
  - { offsetInCU: 0x18F, offset: 0x2C8B5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24MissingDependenciesErrorVyxGs0E0AAsAEP7_domainSSvgTW', symObjAddr: 0xB4, symBinAddr: 0x45184, symSize: 0x4 }
  - { offsetInCU: 0x1AB, offset: 0x2C8D1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24MissingDependenciesErrorVyxGs0E0AAsAEP5_codeSivgTW', symObjAddr: 0xB8, symBinAddr: 0x45188, symSize: 0x4 }
  - { offsetInCU: 0x1C7, offset: 0x2C8ED, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24MissingDependenciesErrorVyxGs0E0AAsAEP9_userInfoyXlSgvgTW', symObjAddr: 0xBC, symBinAddr: 0x4518C, symSize: 0x4 }
  - { offsetInCU: 0x1E2, offset: 0x2C908, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24MissingDependenciesErrorVyxGs0E0AAsAEP19_getEmbeddedNSErroryXlSgyFTW', symObjAddr: 0xC0, symBinAddr: 0x45190, symSize: 0x4 }
  - { offsetInCU: 0x29A, offset: 0x2C9C0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24MissingDependenciesErrorV3forACyxGxm_tcfC', symObjAddr: 0x0, symBinAddr: 0x450D0, symSize: 0x4 }
  - { offsetInCU: 0x2F6, offset: 0x2CA1C, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit24MissingDependenciesErrorV11descriptionSSvg', symObjAddr: 0x4, symBinAddr: 0x450D4, symSize: 0xB0 }
  - { offsetInCU: 0x27, offset: 0x2CAF8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14NonceValidatorO7isValid5nonceSbSS_tFZ', symObjAddr: 0x0, symBinAddr: 0x4526C, symSize: 0xCC }
  - { offsetInCU: 0x3D, offset: 0x2CB0E, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14NonceValidatorOMa', symObjAddr: 0x110, symBinAddr: 0x45338, symSize: 0x10 }
  - { offsetInCU: 0x10B, offset: 0x2CBDC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14NonceValidatorO7isValid5nonceSbSS_tFZ', symObjAddr: 0x0, symBinAddr: 0x4526C, symSize: 0xCC }
  - { offsetInCU: 0x27, offset: 0x2CC53, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14ProfileFactoryCACycfC', symObjAddr: 0x0, symBinAddr: 0x453B0, symSize: 0x20 }
  - { offsetInCU: 0xDA, offset: 0x2CD06, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14ProfileFactoryCACycfcTo', symObjAddr: 0xA0, symBinAddr: 0x45450, symSize: 0x3C }
  - { offsetInCU: 0x12A, offset: 0x2CD56, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14ProfileFactoryCAA0C8CreatingA2aDP06createC06userID9firstName06middleJ004lastJ04name7linkURL11refreshDate05imageO05email9friendIDs8birthday8ageRange8hometown8location6gender11permissions9isLimited09FBSDKCoreB00C0CSS_SSSgA_A_A_10Foundation0O0VSgA0_0Q0VSgA3_A_SaySSGSgA6_So012FBSDKUserAgeX0CSgSo13FBSDKLocationCSgA14_A_ShySSGSgSbtFTW', symObjAddr: 0x110, symBinAddr: 0x454C0, symSize: 0x4C }
  - { offsetInCU: 0x15A, offset: 0x2CD86, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14ProfileFactoryC06createC06userID9firstName06middleI004lastI04name7linkURL11refreshDate05imageN05email9friendIDs8birthday8ageRange8hometown8location6gender11permissions9isLimited09FBSDKCoreB00C0CSS_SSSgA3Y10Foundation0N0VSgAZ0P0VSgA1_AYSaySSGSgA4_So012FBSDKUserAgeW0CSgSo13FBSDKLocationCSgA12_AYShySSGSgSbtFTf4nnnnnnnnnnnnnnnnnd_n', symObjAddr: 0x15C, symBinAddr: 0x4550C, symSize: 0x294 }
  - { offsetInCU: 0x28F, offset: 0x2CEBB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14ProfileFactoryCMa', symObjAddr: 0x3F0, symBinAddr: 0x457A0, symSize: 0x20 }
  - { offsetInCU: 0x4B4, offset: 0x2D0E0, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14ProfileFactoryCACycfC', symObjAddr: 0x0, symBinAddr: 0x453B0, symSize: 0x20 }
  - { offsetInCU: 0x4C7, offset: 0x2D0F3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14ProfileFactoryC06createC06userID9firstName06middleI004lastI04name7linkURL11refreshDate05imageN05email9friendIDs8birthday8ageRange8hometown8location6gender11permissions9isLimited09FBSDKCoreB00C0CSS_SSSgA3Y10Foundation0N0VSgAZ0P0VSgA1_AYSaySSGSgA4_So012FBSDKUserAgeW0CSgSo13FBSDKLocationCSgA12_AYShySSGSgSbtF', symObjAddr: 0x20, symBinAddr: 0x453D0, symSize: 0x4C }
  - { offsetInCU: 0x4DB, offset: 0x2D107, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14ProfileFactoryCACycfc', symObjAddr: 0x6C, symBinAddr: 0x4541C, symSize: 0x34 }
  - { offsetInCU: 0x515, offset: 0x2D141, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit14ProfileFactoryCfD', symObjAddr: 0xDC, symBinAddr: 0x4548C, symSize: 0x34 }
  - { offsetInCU: 0x27, offset: 0x2D1C8, size: 0x8, addend: 0x0, symName: '_$s12FBSDKCoreKit27ServerConfigurationProviderC010FBSDKLoginB00cD9ProvidingA2dEP04loadcD010completionyySo0F7TooltipCSg_s5Error_pSgtcSg_tFTW', symObjAddr: 0x0, symBinAddr: 0x45804, symSize: 0x20 }
  - { offsetInCU: 0x64, offset: 0x2D205, size: 0x8, addend: 0x0, symName: '_$s12FBSDKCoreKit27ServerConfigurationProviderC010FBSDKLoginB00cD9ProvidingA2dEP04loadcD010completionyySo0F7TooltipCSg_s5Error_pSgtcSg_tFTW', symObjAddr: 0x0, symBinAddr: 0x45804, symSize: 0x20 }
  - { offsetInCU: 0x183, offset: 0x2D416, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC5errors5Error_pSgvgTo', symObjAddr: 0x800, symBinAddr: 0x46044, symSize: 0x70 }
  - { offsetInCU: 0x1D7, offset: 0x2D46A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC5errors5Error_pSgvsTo', symObjAddr: 0x8C0, symBinAddr: 0x46104, symSize: 0x64 }
  - { offsetInCU: 0x2C0, offset: 0x2D553, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersCACycfcTo', symObjAddr: 0x1258, symBinAddr: 0x46A14, symSize: 0x20 }
  - { offsetInCU: 0x2F1, offset: 0x2D584, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DateVSgWOc', symObjAddr: 0x9EC, symBinAddr: 0x46230, symSize: 0x48 }
  - { offsetInCU: 0x304, offset: 0x2D597, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersCfETo', symObjAddr: 0x12AC, symBinAddr: 0x46A68, symSize: 0x14C }
  - { offsetInCU: 0x332, offset: 0x2D5C5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersCMU', symObjAddr: 0x13F8, symBinAddr: 0x46BB4, symSize: 0x8 }
  - { offsetInCU: 0x345, offset: 0x2D5D8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersCMa', symObjAddr: 0x1400, symBinAddr: 0x46BBC, symSize: 0x3C }
  - { offsetInCU: 0x358, offset: 0x2D5EB, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersCMr', symObjAddr: 0x143C, symBinAddr: 0x46BF8, symSize: 0x98 }
  - { offsetInCU: 0x36B, offset: 0x2D5FE, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DateVSgMa', symObjAddr: 0x14D4, symBinAddr: 0x46C90, symSize: 0x54 }
  - { offsetInCU: 0x37E, offset: 0x2D611, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DateVSgWOh', symObjAddr: 0x1570, symBinAddr: 0x46CE4, symSize: 0x40 }
  - { offsetInCU: 0x45E, offset: 0x2D6F1, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersCACycfC', symObjAddr: 0x0, symBinAddr: 0x45844, symSize: 0x20 }
  - { offsetInCU: 0x471, offset: 0x2D704, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC19authenticationTokenSo019FBSDKAuthenticationG0CSgvg', symObjAddr: 0x2C, symBinAddr: 0x45870, symSize: 0x50 }
  - { offsetInCU: 0x49A, offset: 0x2D72D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC19authenticationTokenSo019FBSDKAuthenticationG0CSgvM', symObjAddr: 0x94, symBinAddr: 0x458D8, symSize: 0x44 }
  - { offsetInCU: 0x4BD, offset: 0x2D750, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC7profile09FBSDKCoreB07ProfileCSgvg', symObjAddr: 0x128, symBinAddr: 0x4596C, symSize: 0x50 }
  - { offsetInCU: 0x4E0, offset: 0x2D773, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC7profile09FBSDKCoreB07ProfileCSgvM', symObjAddr: 0x23C, symBinAddr: 0x45A80, symSize: 0x44 }
  - { offsetInCU: 0x503, offset: 0x2D796, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC17accessTokenStringSSSgvM', symObjAddr: 0x2B0, symBinAddr: 0x45AF4, symSize: 0x44 }
  - { offsetInCU: 0x526, offset: 0x2D7B9, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC11nonceStringSSSgvM', symObjAddr: 0x324, symBinAddr: 0x45B68, symSize: 0x44 }
  - { offsetInCU: 0x549, offset: 0x2D7DC, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC25authenticationTokenStringSSSgvM', symObjAddr: 0x398, symBinAddr: 0x45BDC, symSize: 0x44 }
  - { offsetInCU: 0x56C, offset: 0x2D7FF, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC4codeSSSgvM', symObjAddr: 0x40C, symBinAddr: 0x45C50, symSize: 0x44 }
  - { offsetInCU: 0x58F, offset: 0x2D822, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC11permissionsShyAA12FBPermissionCGSgvM', symObjAddr: 0x488, symBinAddr: 0x45CCC, symSize: 0x44 }
  - { offsetInCU: 0x5B2, offset: 0x2D845, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC19declinedPermissionsShyAA12FBPermissionCGSgvM', symObjAddr: 0x504, symBinAddr: 0x45D48, symSize: 0x44 }
  - { offsetInCU: 0x5D5, offset: 0x2D868, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC18expiredPermissionsShyAA12FBPermissionCGSgvM', symObjAddr: 0x6D4, symBinAddr: 0x45F18, symSize: 0x44 }
  - { offsetInCU: 0x5F8, offset: 0x2D88B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC5appIDSSSgvM', symObjAddr: 0x748, symBinAddr: 0x45F8C, symSize: 0x44 }
  - { offsetInCU: 0x61B, offset: 0x2D8AE, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC6userIDSSSgvM', symObjAddr: 0x7BC, symBinAddr: 0x46000, symSize: 0x44 }
  - { offsetInCU: 0x655, offset: 0x2D8E8, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC5errors5Error_pSgvg', symObjAddr: 0x870, symBinAddr: 0x460B4, symSize: 0x50 }
  - { offsetInCU: 0x694, offset: 0x2D927, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC5errors5Error_pSgvM', symObjAddr: 0x990, symBinAddr: 0x461D4, symSize: 0x44 }
  - { offsetInCU: 0x6B7, offset: 0x2D94A, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC14expirationDate10Foundation0G0VSgvM', symObjAddr: 0xAD4, symBinAddr: 0x46290, symSize: 0x44 }
  - { offsetInCU: 0x6DA, offset: 0x2D96D, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC24dataAccessExpirationDate10Foundation0I0VSgvM', symObjAddr: 0xDA8, symBinAddr: 0x46564, symSize: 0x44 }
  - { offsetInCU: 0x6FD, offset: 0x2D990, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC9challengeSSSgvM', symObjAddr: 0xE1C, symBinAddr: 0x465D8, symSize: 0x44 }
  - { offsetInCU: 0x720, offset: 0x2D9B3, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC11graphDomainSSSgvM', symObjAddr: 0xE90, symBinAddr: 0x4664C, symSize: 0x44 }
  - { offsetInCU: 0x743, offset: 0x2D9D6, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC11graphDomainSSSgvM.resume.0', symObjAddr: 0xED4, symBinAddr: 0x46690, symSize: 0x4 }
  - { offsetInCU: 0x762, offset: 0x2D9F5, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersC14userTokenNonceSSSgvM', symObjAddr: 0x109C, symBinAddr: 0x46858, symSize: 0x44 }
  - { offsetInCU: 0x785, offset: 0x2DA18, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersCACycfc', symObjAddr: 0x10E0, symBinAddr: 0x4689C, symSize: 0x178 }
  - { offsetInCU: 0x7A8, offset: 0x2DA3B, size: 0x8, addend: 0x0, symName: '_$s13FBSDKLoginKit26_LoginCompletionParametersCfD', symObjAddr: 0x1278, symBinAddr: 0x46A34, symSize: 0x34 }
...
