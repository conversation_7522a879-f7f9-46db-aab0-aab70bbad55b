ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES
CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO
FRAMEWORK_SEARCH_PATHS = $(inherited) "${PODS_CONFIGURATION_BUILD_DIR}/Alamofire" "${PODS_CONFIGURATION_BUILD_DIR}/AppAuth" "${PODS_CONFIGURATION_BUILD_DIR}/DGCharts" "${PODS_CONFIGURATION_BUILD_DIR}/FMDB" "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseAppCheckInterop" "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseAuth" "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseAuthInterop" "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCore" "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCoreExtension" "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCoreInternal" "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseInstallations" "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseMessaging" "${PODS_CONFIGURATION_BUILD_DIR}/GTMAppAuth" "${PODS_CONFIGURATION_BUILD_DIR}/GTMSessionFetcher" "${PODS_CONFIGURATION_BUILD_DIR}/GoogleDataTransport" "${PODS_CONFIGURATION_BUILD_DIR}/GoogleSignIn" "${PODS_CONFIGURATION_BUILD_DIR}/GoogleUtilities" "${PODS_CONFIGURATION_BUILD_DIR}/HandyJSON" "${PODS_CONFIGURATION_BUILD_DIR}/IQKeyboardManagerSwift" "${PODS_CONFIGURATION_BUILD_DIR}/JTAppleCalendar" "${PODS_CONFIGURATION_BUILD_DIR}/Kingfisher" "${PODS_CONFIGURATION_BUILD_DIR}/MJRefresh" "${PODS_CONFIGURATION_BUILD_DIR}/ObjectMapper" "${PODS_CONFIGURATION_BUILD_DIR}/PromisesObjC" "${PODS_CONFIGURATION_BUILD_DIR}/RecaptchaInterop" "${PODS_CONFIGURATION_BUILD_DIR}/SDWebImage" "${PODS_CONFIGURATION_BUILD_DIR}/SnapKit" "${PODS_CONFIGURATION_BUILD_DIR}/SwiftyJSON" "${PODS_CONFIGURATION_BUILD_DIR}/nanopb" "${PODS_ROOT}/FBAEMKit/XCFrameworks" "${PODS_ROOT}/FBSDKCoreKit/XCFrameworks" "${PODS_ROOT}/FBSDKCoreKit_Basics/XCFrameworks" "${PODS_ROOT}/FBSDKLoginKit/XCFrameworks" "${PODS_ROOT}/FirebaseAnalytics/Frameworks" "${PODS_ROOT}/GoogleAppMeasurement/Frameworks" "${PODS_XCFRAMEWORKS_BUILD_DIR}/FBAEMKit" "${PODS_XCFRAMEWORKS_BUILD_DIR}/FBSDKCoreKit" "${PODS_XCFRAMEWORKS_BUILD_DIR}/FBSDKCoreKit_Basics" "${PODS_XCFRAMEWORKS_BUILD_DIR}/FBSDKLoginKit" "${PODS_XCFRAMEWORKS_BUILD_DIR}/FirebaseAnalytics/AdIdSupport" "${PODS_XCFRAMEWORKS_BUILD_DIR}/GoogleAppMeasurement/AdIdSupport" "${PODS_XCFRAMEWORKS_BUILD_DIR}/GoogleAppMeasurement/WithoutAdIdSupport"
GCC_PREPROCESSOR_DEFINITIONS = $(inherited) COCOAPODS=1 $(inherited) PB_FIELD_32BIT=1 PB_NO_PACKED_STRUCTS=1 PB_ENABLE_MALLOC=1
HEADER_SEARCH_PATHS = $(inherited) "${PODS_CONFIGURATION_BUILD_DIR}/Alamofire/Alamofire.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/AppAuth/AppAuth.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/DGCharts/DGCharts.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/FMDB/FMDB.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseAppCheckInterop/FirebaseAppCheckInterop.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseAuth/FirebaseAuth.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseAuthInterop/FirebaseAuthInterop.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCore/FirebaseCore.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCoreExtension/FirebaseCoreExtension.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCoreInternal/FirebaseCoreInternal.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseInstallations/FirebaseInstallations.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseMessaging/FirebaseMessaging.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/GTMAppAuth/GTMAppAuth.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/GTMSessionFetcher/GTMSessionFetcher.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/GoogleDataTransport/GoogleDataTransport.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/GoogleSignIn/GoogleSignIn.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/GoogleUtilities/GoogleUtilities.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/HandyJSON/HandyJSON.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/IQKeyboardManagerSwift/IQKeyboardManagerSwift.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/JTAppleCalendar/JTAppleCalendar.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/Kingfisher/Kingfisher.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/MJRefresh/MJRefresh.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/ObjectMapper/ObjectMapper.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/PromisesObjC/FBLPromises.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/RecaptchaInterop/RecaptchaInterop.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/SDWebImage/SDWebImage.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/SnapKit/SnapKit.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/SwiftyJSON/SwiftyJSON.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/nanopb/nanopb.framework/Headers" "${PODS_ROOT}/Headers/Public" "${PODS_ROOT}/Headers/Public/Firebase" $(inherited) ${PODS_ROOT}/Firebase/CoreOnly/Sources
LD_RUNPATH_SEARCH_PATHS = $(inherited) /usr/lib/swift '@executable_path/Frameworks' '@loader_path/Frameworks'
LIBRARY_SEARCH_PATHS = $(inherited) "${TOOLCHAIN_DIR}/usr/lib/swift/${PLATFORM_NAME}" /usr/lib/swift $(SDKROOT)/usr/lib/swift
OTHER_LDFLAGS = $(inherited) -ObjC -l"c++" -l"sqlite3" -l"swiftCoreGraphics" -l"z" -framework "Accelerate" -framework "Alamofire" -framework "AppAuth" -framework "CFNetwork" -framework "CoreGraphics" -framework "CoreTelephony" -framework "CoreText" -framework "DGCharts" -framework "FBAEMKit" -framework "FBLPromises" -framework "FBSDKCoreKit" -framework "FBSDKCoreKit_Basics" -framework "FBSDKLoginKit" -framework "FMDB" -framework "FirebaseAnalytics" -framework "FirebaseAppCheckInterop" -framework "FirebaseAuth" -framework "FirebaseAuthInterop" -framework "FirebaseCore" -framework "FirebaseCoreExtension" -framework "FirebaseCoreInternal" -framework "FirebaseInstallations" -framework "FirebaseMessaging" -framework "Foundation" -framework "GTMAppAuth" -framework "GTMSessionFetcher" -framework "GoogleAppMeasurement" -framework "GoogleAppMeasurementIdentitySupport" -framework "GoogleDataTransport" -framework "GoogleSignIn" -framework "GoogleUtilities" -framework "HandyJSON" -framework "IQKeyboardManagerSwift" -framework "ImageIO" -framework "JTAppleCalendar" -framework "Kingfisher" -framework "LocalAuthentication" -framework "MJRefresh" -framework "ObjectMapper" -framework "QuartzCore" -framework "RecaptchaInterop" -framework "SDWebImage" -framework "SafariServices" -framework "Security" -framework "SnapKit" -framework "StoreKit" -framework "SwiftyJSON" -framework "SystemConfiguration" -framework "UIKit" -framework "nanopb" -weak_framework "AuthenticationServices" -weak_framework "Combine" -weak_framework "SwiftUI" -weak_framework "UserNotifications"
OTHER_MODULE_VERIFIER_FLAGS = $(inherited) "-F${PODS_CONFIGURATION_BUILD_DIR}/Alamofire" "-F${PODS_CONFIGURATION_BUILD_DIR}/AppAuth" "-F${PODS_CONFIGURATION_BUILD_DIR}/DGCharts" "-F${PODS_CONFIGURATION_BUILD_DIR}/FBAEMKit" "-F${PODS_CONFIGURATION_BUILD_DIR}/FBSDKCoreKit" "-F${PODS_CONFIGURATION_BUILD_DIR}/FBSDKCoreKit_Basics" "-F${PODS_CONFIGURATION_BUILD_DIR}/FBSDKLoginKit" "-F${PODS_CONFIGURATION_BUILD_DIR}/FMDB" "-F${PODS_CONFIGURATION_BUILD_DIR}/Firebase" "-F${PODS_CONFIGURATION_BUILD_DIR}/FirebaseAnalytics" "-F${PODS_CONFIGURATION_BUILD_DIR}/FirebaseAppCheckInterop" "-F${PODS_CONFIGURATION_BUILD_DIR}/FirebaseAuth" "-F${PODS_CONFIGURATION_BUILD_DIR}/FirebaseAuthInterop" "-F${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCore" "-F${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCoreExtension" "-F${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCoreInternal" "-F${PODS_CONFIGURATION_BUILD_DIR}/FirebaseInstallations" "-F${PODS_CONFIGURATION_BUILD_DIR}/FirebaseMessaging" "-F${PODS_CONFIGURATION_BUILD_DIR}/GTMAppAuth" "-F${PODS_CONFIGURATION_BUILD_DIR}/GTMSessionFetcher" "-F${PODS_CONFIGURATION_BUILD_DIR}/GoogleAppMeasurement" "-F${PODS_CONFIGURATION_BUILD_DIR}/GoogleDataTransport" "-F${PODS_CONFIGURATION_BUILD_DIR}/GoogleSignIn" "-F${PODS_CONFIGURATION_BUILD_DIR}/GoogleUtilities" "-F${PODS_CONFIGURATION_BUILD_DIR}/HandyJSON" "-F${PODS_CONFIGURATION_BUILD_DIR}/IQKeyboardManagerSwift" "-F${PODS_CONFIGURATION_BUILD_DIR}/JTAppleCalendar" "-F${PODS_CONFIGURATION_BUILD_DIR}/Kingfisher" "-F${PODS_CONFIGURATION_BUILD_DIR}/MJRefresh" "-F${PODS_CONFIGURATION_BUILD_DIR}/ObjectMapper" "-F${PODS_CONFIGURATION_BUILD_DIR}/PromisesObjC" "-F${PODS_CONFIGURATION_BUILD_DIR}/RecaptchaInterop" "-F${PODS_CONFIGURATION_BUILD_DIR}/SDWebImage" "-F${PODS_CONFIGURATION_BUILD_DIR}/SnapKit" "-F${PODS_CONFIGURATION_BUILD_DIR}/SwiftyJSON" "-F${PODS_CONFIGURATION_BUILD_DIR}/nanopb"
OTHER_SWIFT_FLAGS = $(inherited) -D COCOAPODS
PODS_BUILD_DIR = ${BUILD_DIR}
PODS_CONFIGURATION_BUILD_DIR = ${PODS_BUILD_DIR}/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)
PODS_PODFILE_DIR_PATH = ${SRCROOT}/.
PODS_ROOT = ${SRCROOT}/Pods
PODS_XCFRAMEWORKS_BUILD_DIR = $(PODS_CONFIGURATION_BUILD_DIR)/XCFrameworkIntermediates
USE_RECURSIVE_SCRIPT_INPUTS_IN_SCRIPT_PHASES = YES
