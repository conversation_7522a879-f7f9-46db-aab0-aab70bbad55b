<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Resources/Info.plist</key>
		<data>
		37Y61+qha52D32CCpc+VOPwBBzs=
		</data>
		<key>Resources/PrivacyInfo.xcprivacy</key>
		<data>
		Fo7sebV/R02g8kqyPtqICO8eVyI=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/FBAEMKit-Swift.h</key>
		<dict>
			<key>hash2</key>
			<data>
			rnoOnLQ4Ls7PXoieTZ+/SZby/qY9o1psqoiAc8qJxKM=
			</data>
		</dict>
		<key>Modules/FBAEMKit.swiftmodule/arm64-apple-ios-macabi.abi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			zhvZWrKs4KE/ICaMDH4+lrdNAD56u6Z9aXaEhEsKflE=
			</data>
		</dict>
		<key>Modules/FBAEMKit.swiftmodule/arm64-apple-ios-macabi.private.swiftinterface</key>
		<dict>
			<key>hash2</key>
			<data>
			aku9cybKdeyGKDjeFNP2CFlEHeADke7u6kJrxQrasXg=
			</data>
		</dict>
		<key>Modules/FBAEMKit.swiftmodule/arm64-apple-ios-macabi.swiftdoc</key>
		<dict>
			<key>hash2</key>
			<data>
			zJB9/TquAluszrvxDZ/RlnQbtfxyXZY9WKp+71LZsj4=
			</data>
		</dict>
		<key>Modules/FBAEMKit.swiftmodule/arm64-apple-ios-macabi.swiftinterface</key>
		<dict>
			<key>hash2</key>
			<data>
			aku9cybKdeyGKDjeFNP2CFlEHeADke7u6kJrxQrasXg=
			</data>
		</dict>
		<key>Modules/FBAEMKit.swiftmodule/x86_64-apple-ios-macabi.abi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			zhvZWrKs4KE/ICaMDH4+lrdNAD56u6Z9aXaEhEsKflE=
			</data>
		</dict>
		<key>Modules/FBAEMKit.swiftmodule/x86_64-apple-ios-macabi.private.swiftinterface</key>
		<dict>
			<key>hash2</key>
			<data>
			M4c8FtwmzDceC3gx3FZiaWBMwIyUA7qsI1foAS9Y83k=
			</data>
		</dict>
		<key>Modules/FBAEMKit.swiftmodule/x86_64-apple-ios-macabi.swiftdoc</key>
		<dict>
			<key>hash2</key>
			<data>
			VpGERY4YXc1E8LgyJ5s26ZDzj1RMV1HFbd9URQ0ZPnY=
			</data>
		</dict>
		<key>Modules/FBAEMKit.swiftmodule/x86_64-apple-ios-macabi.swiftinterface</key>
		<dict>
			<key>hash2</key>
			<data>
			M4c8FtwmzDceC3gx3FZiaWBMwIyUA7qsI1foAS9Y83k=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash2</key>
			<data>
			jq+nSulDKiN4tenILjgXg82TzM040TCurYFTCVRKXxM=
			</data>
		</dict>
		<key>Resources/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			HN3ObRPluvG9f4sC+PEQHaVJJVXDcdA1zgXg9YgTwLk=
			</data>
		</dict>
		<key>Resources/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			ZFIpWmrSklTJLGaAOPLGos/UQMB82oH4FOmWrCFbhBU=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^Resources/</key>
		<true/>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^(Frameworks|SharedFrameworks|PlugIns|Plug-ins|XPCServices|Helpers|MacOS|Library/(Automator|Spotlight|LoginItems))/</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^[^/]+$</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
