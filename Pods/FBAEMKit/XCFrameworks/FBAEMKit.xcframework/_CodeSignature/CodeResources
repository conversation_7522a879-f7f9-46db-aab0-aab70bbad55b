<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>LICENSE</key>
		<data>
		42oX9oAD1yj/wA6aqkh1wyx+qqA=
		</data>
		<key>ios-arm64/FBAEMKit.framework/FBAEMKit</key>
		<data>
		NEDcB3q+MVoruqJxbK4kyirzmXU=
		</data>
		<key>ios-arm64/FBAEMKit.framework/Headers/FBAEMKit-Swift.h</key>
		<data>
		XwfElLRDNnpKKn0PH/hO9BM6QRU=
		</data>
		<key>ios-arm64/FBAEMKit.framework/Info.plist</key>
		<data>
		wqtigv2WSFQZigZ0UgT7OvtN2nI=
		</data>
		<key>ios-arm64/FBAEMKit.framework/Modules/FBAEMKit.swiftmodule/arm64-apple-ios.abi.json</key>
		<data>
		x/cYBL8fG5PLfr6/8sf/Ngx0NbM=
		</data>
		<key>ios-arm64/FBAEMKit.framework/Modules/FBAEMKit.swiftmodule/arm64-apple-ios.private.swiftinterface</key>
		<data>
		vuOt4lh95Y0TvldvN/y3KNDKZfs=
		</data>
		<key>ios-arm64/FBAEMKit.framework/Modules/FBAEMKit.swiftmodule/arm64-apple-ios.swiftdoc</key>
		<data>
		nb9852QDL6O4NKXWpLS4zgC6tQ8=
		</data>
		<key>ios-arm64/FBAEMKit.framework/Modules/FBAEMKit.swiftmodule/arm64-apple-ios.swiftinterface</key>
		<data>
		vuOt4lh95Y0TvldvN/y3KNDKZfs=
		</data>
		<key>ios-arm64/FBAEMKit.framework/Modules/module.modulemap</key>
		<data>
		Ci3QIdviXpIzxSC88i1rGvW2cSs=
		</data>
		<key>ios-arm64/FBAEMKit.framework/PrivacyInfo.xcprivacy</key>
		<data>
		Fo7sebV/R02g8kqyPtqICO8eVyI=
		</data>
		<key>ios-arm64/FBAEMKit.framework/_CodeSignature/CodeResources</key>
		<data>
		JKaq8c1r5NXLpu4wRTZzPg506oE=
		</data>
		<key>ios-arm64/dSYMs/FBAEMKit.framework.dSYM/Contents/Info.plist</key>
		<data>
		VaKEymQ/1yy7HfCzMAxX0WLRXk0=
		</data>
		<key>ios-arm64/dSYMs/FBAEMKit.framework.dSYM/Contents/Resources/DWARF/FBAEMKit</key>
		<data>
		0tfRgcJO2i/mvpkTciCAaOWTOjI=
		</data>
		<key>ios-arm64/dSYMs/FBAEMKit.framework.dSYM/Contents/Resources/Relocations/aarch64/FBAEMKit.yml</key>
		<data>
		nRBsO5Ivvcwrm6603jkz8D7Gwlo=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBAEMKit.framework/Versions/A/FBAEMKit</key>
		<data>
		SF8cobMvcSDDtigS+dMXsRSSFLA=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBAEMKit.framework/Versions/A/Headers/FBAEMKit-Swift.h</key>
		<data>
		7sS/nlKqm4Wov9u8GGLhdrKFOFY=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBAEMKit.framework/Versions/A/Modules/FBAEMKit.swiftmodule/arm64-apple-ios-macabi.abi.json</key>
		<data>
		x/cYBL8fG5PLfr6/8sf/Ngx0NbM=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBAEMKit.framework/Versions/A/Modules/FBAEMKit.swiftmodule/arm64-apple-ios-macabi.private.swiftinterface</key>
		<data>
		MO1TrGA4m9oCtdnCAO0EbR07LKQ=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBAEMKit.framework/Versions/A/Modules/FBAEMKit.swiftmodule/arm64-apple-ios-macabi.swiftdoc</key>
		<data>
		OkYO43wY1y0NN0zzA8+AsT+MDJE=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBAEMKit.framework/Versions/A/Modules/FBAEMKit.swiftmodule/arm64-apple-ios-macabi.swiftinterface</key>
		<data>
		MO1TrGA4m9oCtdnCAO0EbR07LKQ=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBAEMKit.framework/Versions/A/Modules/FBAEMKit.swiftmodule/x86_64-apple-ios-macabi.abi.json</key>
		<data>
		x/cYBL8fG5PLfr6/8sf/Ngx0NbM=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBAEMKit.framework/Versions/A/Modules/FBAEMKit.swiftmodule/x86_64-apple-ios-macabi.private.swiftinterface</key>
		<data>
		LeloTBayKYzvy1My8aTPDkYKoVM=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBAEMKit.framework/Versions/A/Modules/FBAEMKit.swiftmodule/x86_64-apple-ios-macabi.swiftdoc</key>
		<data>
		WOqpjF2fH3ZciGxFwbdpLzqULxM=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBAEMKit.framework/Versions/A/Modules/FBAEMKit.swiftmodule/x86_64-apple-ios-macabi.swiftinterface</key>
		<data>
		LeloTBayKYzvy1My8aTPDkYKoVM=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBAEMKit.framework/Versions/A/Modules/module.modulemap</key>
		<data>
		Ci3QIdviXpIzxSC88i1rGvW2cSs=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBAEMKit.framework/Versions/A/Resources/Info.plist</key>
		<data>
		37Y61+qha52D32CCpc+VOPwBBzs=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBAEMKit.framework/Versions/A/Resources/PrivacyInfo.xcprivacy</key>
		<data>
		Fo7sebV/R02g8kqyPtqICO8eVyI=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBAEMKit.framework/Versions/A/_CodeSignature/CodeResources</key>
		<data>
		C8r2RKYGgTidUZfDsnA4j3KoNuk=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/dSYMs/FBAEMKit.framework.dSYM/Contents/Info.plist</key>
		<data>
		VaKEymQ/1yy7HfCzMAxX0WLRXk0=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/dSYMs/FBAEMKit.framework.dSYM/Contents/Resources/DWARF/FBAEMKit</key>
		<data>
		UzMPJmIj5jchwtev9dExQ3vGaJE=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/dSYMs/FBAEMKit.framework.dSYM/Contents/Resources/Relocations/aarch64/FBAEMKit.yml</key>
		<data>
		SaT/0gPKP7X06UK9uRN0FqZcn8A=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/dSYMs/FBAEMKit.framework.dSYM/Contents/Resources/Relocations/x86_64/FBAEMKit.yml</key>
		<data>
		iSVc74qNkP3pa1TO96c/NHNKy8A=
		</data>
		<key>ios-arm64_x86_64-simulator/FBAEMKit.framework/FBAEMKit</key>
		<data>
		I6oQ3gS5ZGDhzqPbNfWCFf+/8pU=
		</data>
		<key>ios-arm64_x86_64-simulator/FBAEMKit.framework/Headers/FBAEMKit-Swift.h</key>
		<data>
		7sS/nlKqm4Wov9u8GGLhdrKFOFY=
		</data>
		<key>ios-arm64_x86_64-simulator/FBAEMKit.framework/Info.plist</key>
		<data>
		jY5vUqohSxw5/EjUZlfO7Njse10=
		</data>
		<key>ios-arm64_x86_64-simulator/FBAEMKit.framework/Modules/FBAEMKit.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<data>
		x/cYBL8fG5PLfr6/8sf/Ngx0NbM=
		</data>
		<key>ios-arm64_x86_64-simulator/FBAEMKit.framework/Modules/FBAEMKit.swiftmodule/arm64-apple-ios-simulator.private.swiftinterface</key>
		<data>
		o+7uaJqh/sAx1Tf2xiGSBVExosE=
		</data>
		<key>ios-arm64_x86_64-simulator/FBAEMKit.framework/Modules/FBAEMKit.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<data>
		zJaXLkkA6yIO2IF1tP55khlzpg0=
		</data>
		<key>ios-arm64_x86_64-simulator/FBAEMKit.framework/Modules/FBAEMKit.swiftmodule/arm64-apple-ios-simulator.swiftinterface</key>
		<data>
		o+7uaJqh/sAx1Tf2xiGSBVExosE=
		</data>
		<key>ios-arm64_x86_64-simulator/FBAEMKit.framework/Modules/FBAEMKit.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<data>
		x/cYBL8fG5PLfr6/8sf/Ngx0NbM=
		</data>
		<key>ios-arm64_x86_64-simulator/FBAEMKit.framework/Modules/FBAEMKit.swiftmodule/x86_64-apple-ios-simulator.private.swiftinterface</key>
		<data>
		iEuyJdx39q5xkazPBRMuG8QnpjQ=
		</data>
		<key>ios-arm64_x86_64-simulator/FBAEMKit.framework/Modules/FBAEMKit.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<data>
		eCU8L9BgWw+wbfH5k6uK6cqUJCg=
		</data>
		<key>ios-arm64_x86_64-simulator/FBAEMKit.framework/Modules/FBAEMKit.swiftmodule/x86_64-apple-ios-simulator.swiftinterface</key>
		<data>
		iEuyJdx39q5xkazPBRMuG8QnpjQ=
		</data>
		<key>ios-arm64_x86_64-simulator/FBAEMKit.framework/Modules/module.modulemap</key>
		<data>
		Ci3QIdviXpIzxSC88i1rGvW2cSs=
		</data>
		<key>ios-arm64_x86_64-simulator/FBAEMKit.framework/PrivacyInfo.xcprivacy</key>
		<data>
		Fo7sebV/R02g8kqyPtqICO8eVyI=
		</data>
		<key>ios-arm64_x86_64-simulator/FBAEMKit.framework/_CodeSignature/CodeResources</key>
		<data>
		r742uaxfYXkRhrr0N0AGYoqW28s=
		</data>
		<key>ios-arm64_x86_64-simulator/dSYMs/FBAEMKit.framework.dSYM/Contents/Info.plist</key>
		<data>
		VaKEymQ/1yy7HfCzMAxX0WLRXk0=
		</data>
		<key>ios-arm64_x86_64-simulator/dSYMs/FBAEMKit.framework.dSYM/Contents/Resources/DWARF/FBAEMKit</key>
		<data>
		e1sGbN59bSZ0NcRcEo5oaC5c6jo=
		</data>
		<key>ios-arm64_x86_64-simulator/dSYMs/FBAEMKit.framework.dSYM/Contents/Resources/Relocations/aarch64/FBAEMKit.yml</key>
		<data>
		BqMGg0SiTn3lsl0eZaZG48HXe9M=
		</data>
		<key>ios-arm64_x86_64-simulator/dSYMs/FBAEMKit.framework.dSYM/Contents/Resources/Relocations/x86_64/FBAEMKit.yml</key>
		<data>
		VoMiG6xhI9qDG8f8zC7DVFSrnaU=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>LICENSE</key>
		<dict>
			<key>hash</key>
			<data>
			42oX9oAD1yj/wA6aqkh1wyx+qqA=
			</data>
			<key>hash2</key>
			<data>
			JGiNyKThXtEPUCL2A80E+FzHN+UTW+RkFoApZE8iHm8=
			</data>
		</dict>
		<key>ios-arm64/FBAEMKit.framework/FBAEMKit</key>
		<dict>
			<key>hash</key>
			<data>
			NEDcB3q+MVoruqJxbK4kyirzmXU=
			</data>
			<key>hash2</key>
			<data>
			HzZxNPkjm3EyM5gdyRErxHnuIGv4YqoT9JQje2De0CQ=
			</data>
		</dict>
		<key>ios-arm64/FBAEMKit.framework/Headers/FBAEMKit-Swift.h</key>
		<dict>
			<key>hash</key>
			<data>
			XwfElLRDNnpKKn0PH/hO9BM6QRU=
			</data>
			<key>hash2</key>
			<data>
			GImGAI1xnOe3jvV+cbROfPGbUXhQPKpIfyqbrRqfgOI=
			</data>
		</dict>
		<key>ios-arm64/FBAEMKit.framework/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			wqtigv2WSFQZigZ0UgT7OvtN2nI=
			</data>
			<key>hash2</key>
			<data>
			k+Jmq+LL7xnOL/PM442Q0HB1x72SzrxWlaYcHbo5bck=
			</data>
		</dict>
		<key>ios-arm64/FBAEMKit.framework/Modules/FBAEMKit.swiftmodule/arm64-apple-ios.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			x/cYBL8fG5PLfr6/8sf/Ngx0NbM=
			</data>
			<key>hash2</key>
			<data>
			zhvZWrKs4KE/ICaMDH4+lrdNAD56u6Z9aXaEhEsKflE=
			</data>
		</dict>
		<key>ios-arm64/FBAEMKit.framework/Modules/FBAEMKit.swiftmodule/arm64-apple-ios.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			vuOt4lh95Y0TvldvN/y3KNDKZfs=
			</data>
			<key>hash2</key>
			<data>
			z/WKCqfpEndXznM7q+3+k6CfNqCAVHFlJQ+73I3leD0=
			</data>
		</dict>
		<key>ios-arm64/FBAEMKit.framework/Modules/FBAEMKit.swiftmodule/arm64-apple-ios.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			nb9852QDL6O4NKXWpLS4zgC6tQ8=
			</data>
			<key>hash2</key>
			<data>
			wyQas2yzjsYVhwrz030g2aq0yBDc4YNETg4qd+GtQ+Y=
			</data>
		</dict>
		<key>ios-arm64/FBAEMKit.framework/Modules/FBAEMKit.swiftmodule/arm64-apple-ios.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			vuOt4lh95Y0TvldvN/y3KNDKZfs=
			</data>
			<key>hash2</key>
			<data>
			z/WKCqfpEndXznM7q+3+k6CfNqCAVHFlJQ+73I3leD0=
			</data>
		</dict>
		<key>ios-arm64/FBAEMKit.framework/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			Ci3QIdviXpIzxSC88i1rGvW2cSs=
			</data>
			<key>hash2</key>
			<data>
			jq+nSulDKiN4tenILjgXg82TzM040TCurYFTCVRKXxM=
			</data>
		</dict>
		<key>ios-arm64/FBAEMKit.framework/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash</key>
			<data>
			Fo7sebV/R02g8kqyPtqICO8eVyI=
			</data>
			<key>hash2</key>
			<data>
			ZFIpWmrSklTJLGaAOPLGos/UQMB82oH4FOmWrCFbhBU=
			</data>
		</dict>
		<key>ios-arm64/FBAEMKit.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash</key>
			<data>
			JKaq8c1r5NXLpu4wRTZzPg506oE=
			</data>
			<key>hash2</key>
			<data>
			IWXPObZnBWNz0RIP3KU0tR3AwONIyLceiZeMIzNYszA=
			</data>
		</dict>
		<key>ios-arm64/dSYMs/FBAEMKit.framework.dSYM/Contents/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			VaKEymQ/1yy7HfCzMAxX0WLRXk0=
			</data>
			<key>hash2</key>
			<data>
			pixPhJHJn9s5nRRo8xOpoEO9uj+TVOVjIiXT03xaKI4=
			</data>
		</dict>
		<key>ios-arm64/dSYMs/FBAEMKit.framework.dSYM/Contents/Resources/DWARF/FBAEMKit</key>
		<dict>
			<key>hash</key>
			<data>
			0tfRgcJO2i/mvpkTciCAaOWTOjI=
			</data>
			<key>hash2</key>
			<data>
			EktBkbIbBuPmOd+XnpjTxF1lke3l98CPWYQzb3HXldk=
			</data>
		</dict>
		<key>ios-arm64/dSYMs/FBAEMKit.framework.dSYM/Contents/Resources/Relocations/aarch64/FBAEMKit.yml</key>
		<dict>
			<key>hash</key>
			<data>
			nRBsO5Ivvcwrm6603jkz8D7Gwlo=
			</data>
			<key>hash2</key>
			<data>
			MvgZ8xSGGjoVpDLD3Hjh4LS9Gm5ii0rXtbalBjNFTn0=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBAEMKit.framework/FBAEMKit</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/FBAEMKit</string>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBAEMKit.framework/Headers</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/Headers</string>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBAEMKit.framework/Modules</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/Modules</string>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBAEMKit.framework/Resources</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/Resources</string>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBAEMKit.framework/Versions/A/FBAEMKit</key>
		<dict>
			<key>hash</key>
			<data>
			SF8cobMvcSDDtigS+dMXsRSSFLA=
			</data>
			<key>hash2</key>
			<data>
			rQD3w1cMsJ6q3YbROB69rkTEhqs7dWVt4NDHg8ho4GQ=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBAEMKit.framework/Versions/A/Headers/FBAEMKit-Swift.h</key>
		<dict>
			<key>hash</key>
			<data>
			7sS/nlKqm4Wov9u8GGLhdrKFOFY=
			</data>
			<key>hash2</key>
			<data>
			rnoOnLQ4Ls7PXoieTZ+/SZby/qY9o1psqoiAc8qJxKM=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBAEMKit.framework/Versions/A/Modules/FBAEMKit.swiftmodule/arm64-apple-ios-macabi.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			x/cYBL8fG5PLfr6/8sf/Ngx0NbM=
			</data>
			<key>hash2</key>
			<data>
			zhvZWrKs4KE/ICaMDH4+lrdNAD56u6Z9aXaEhEsKflE=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBAEMKit.framework/Versions/A/Modules/FBAEMKit.swiftmodule/arm64-apple-ios-macabi.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			MO1TrGA4m9oCtdnCAO0EbR07LKQ=
			</data>
			<key>hash2</key>
			<data>
			aku9cybKdeyGKDjeFNP2CFlEHeADke7u6kJrxQrasXg=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBAEMKit.framework/Versions/A/Modules/FBAEMKit.swiftmodule/arm64-apple-ios-macabi.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			OkYO43wY1y0NN0zzA8+AsT+MDJE=
			</data>
			<key>hash2</key>
			<data>
			zJB9/TquAluszrvxDZ/RlnQbtfxyXZY9WKp+71LZsj4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBAEMKit.framework/Versions/A/Modules/FBAEMKit.swiftmodule/arm64-apple-ios-macabi.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			MO1TrGA4m9oCtdnCAO0EbR07LKQ=
			</data>
			<key>hash2</key>
			<data>
			aku9cybKdeyGKDjeFNP2CFlEHeADke7u6kJrxQrasXg=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBAEMKit.framework/Versions/A/Modules/FBAEMKit.swiftmodule/x86_64-apple-ios-macabi.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			x/cYBL8fG5PLfr6/8sf/Ngx0NbM=
			</data>
			<key>hash2</key>
			<data>
			zhvZWrKs4KE/ICaMDH4+lrdNAD56u6Z9aXaEhEsKflE=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBAEMKit.framework/Versions/A/Modules/FBAEMKit.swiftmodule/x86_64-apple-ios-macabi.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			LeloTBayKYzvy1My8aTPDkYKoVM=
			</data>
			<key>hash2</key>
			<data>
			M4c8FtwmzDceC3gx3FZiaWBMwIyUA7qsI1foAS9Y83k=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBAEMKit.framework/Versions/A/Modules/FBAEMKit.swiftmodule/x86_64-apple-ios-macabi.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			WOqpjF2fH3ZciGxFwbdpLzqULxM=
			</data>
			<key>hash2</key>
			<data>
			VpGERY4YXc1E8LgyJ5s26ZDzj1RMV1HFbd9URQ0ZPnY=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBAEMKit.framework/Versions/A/Modules/FBAEMKit.swiftmodule/x86_64-apple-ios-macabi.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			LeloTBayKYzvy1My8aTPDkYKoVM=
			</data>
			<key>hash2</key>
			<data>
			M4c8FtwmzDceC3gx3FZiaWBMwIyUA7qsI1foAS9Y83k=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBAEMKit.framework/Versions/A/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			Ci3QIdviXpIzxSC88i1rGvW2cSs=
			</data>
			<key>hash2</key>
			<data>
			jq+nSulDKiN4tenILjgXg82TzM040TCurYFTCVRKXxM=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBAEMKit.framework/Versions/A/Resources/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			37Y61+qha52D32CCpc+VOPwBBzs=
			</data>
			<key>hash2</key>
			<data>
			HN3ObRPluvG9f4sC+PEQHaVJJVXDcdA1zgXg9YgTwLk=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBAEMKit.framework/Versions/A/Resources/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash</key>
			<data>
			Fo7sebV/R02g8kqyPtqICO8eVyI=
			</data>
			<key>hash2</key>
			<data>
			ZFIpWmrSklTJLGaAOPLGos/UQMB82oH4FOmWrCFbhBU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBAEMKit.framework/Versions/A/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash</key>
			<data>
			C8r2RKYGgTidUZfDsnA4j3KoNuk=
			</data>
			<key>hash2</key>
			<data>
			lQJCHQTW9g4KcNyha+qWxpmEepdDIJM7cVUKakw1I30=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBAEMKit.framework/Versions/Current</key>
		<dict>
			<key>symlink</key>
			<string>A</string>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/dSYMs/FBAEMKit.framework.dSYM/Contents/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			VaKEymQ/1yy7HfCzMAxX0WLRXk0=
			</data>
			<key>hash2</key>
			<data>
			pixPhJHJn9s5nRRo8xOpoEO9uj+TVOVjIiXT03xaKI4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/dSYMs/FBAEMKit.framework.dSYM/Contents/Resources/DWARF/FBAEMKit</key>
		<dict>
			<key>hash</key>
			<data>
			UzMPJmIj5jchwtev9dExQ3vGaJE=
			</data>
			<key>hash2</key>
			<data>
			4x5FjzFta+e99C9seP9oZAyYV+KMqCywENhY9dtlWas=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/dSYMs/FBAEMKit.framework.dSYM/Contents/Resources/Relocations/aarch64/FBAEMKit.yml</key>
		<dict>
			<key>hash</key>
			<data>
			SaT/0gPKP7X06UK9uRN0FqZcn8A=
			</data>
			<key>hash2</key>
			<data>
			H1kr/Frows8yzPrJYLWb8QndzYDOqadG4nHXh9MAGDg=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/dSYMs/FBAEMKit.framework.dSYM/Contents/Resources/Relocations/x86_64/FBAEMKit.yml</key>
		<dict>
			<key>hash</key>
			<data>
			iSVc74qNkP3pa1TO96c/NHNKy8A=
			</data>
			<key>hash2</key>
			<data>
			yWPCOCLSRquw05wyagbivVywtWcE7VBmMaR8+HdcEFI=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBAEMKit.framework/FBAEMKit</key>
		<dict>
			<key>hash</key>
			<data>
			I6oQ3gS5ZGDhzqPbNfWCFf+/8pU=
			</data>
			<key>hash2</key>
			<data>
			4yd3JaxwGmrIx6rbGzsxj86MJN808TcCuP/X5CqWakg=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBAEMKit.framework/Headers/FBAEMKit-Swift.h</key>
		<dict>
			<key>hash</key>
			<data>
			7sS/nlKqm4Wov9u8GGLhdrKFOFY=
			</data>
			<key>hash2</key>
			<data>
			rnoOnLQ4Ls7PXoieTZ+/SZby/qY9o1psqoiAc8qJxKM=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBAEMKit.framework/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			jY5vUqohSxw5/EjUZlfO7Njse10=
			</data>
			<key>hash2</key>
			<data>
			hPJ+E5nBjdFRkFV5IIQlVO8kcXeAu9oaIFKXdUoJtK8=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBAEMKit.framework/Modules/FBAEMKit.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			x/cYBL8fG5PLfr6/8sf/Ngx0NbM=
			</data>
			<key>hash2</key>
			<data>
			zhvZWrKs4KE/ICaMDH4+lrdNAD56u6Z9aXaEhEsKflE=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBAEMKit.framework/Modules/FBAEMKit.swiftmodule/arm64-apple-ios-simulator.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			o+7uaJqh/sAx1Tf2xiGSBVExosE=
			</data>
			<key>hash2</key>
			<data>
			FNSe3QaY5n/t5GHRf3LdCbL0ZQBHUCUhEFGTt4y8Wxo=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBAEMKit.framework/Modules/FBAEMKit.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			zJaXLkkA6yIO2IF1tP55khlzpg0=
			</data>
			<key>hash2</key>
			<data>
			AM3qM4pBd8dSfu1jbLD9SssEcT1XyHtBYgyOqPYfhh0=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBAEMKit.framework/Modules/FBAEMKit.swiftmodule/arm64-apple-ios-simulator.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			o+7uaJqh/sAx1Tf2xiGSBVExosE=
			</data>
			<key>hash2</key>
			<data>
			FNSe3QaY5n/t5GHRf3LdCbL0ZQBHUCUhEFGTt4y8Wxo=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBAEMKit.framework/Modules/FBAEMKit.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			x/cYBL8fG5PLfr6/8sf/Ngx0NbM=
			</data>
			<key>hash2</key>
			<data>
			zhvZWrKs4KE/ICaMDH4+lrdNAD56u6Z9aXaEhEsKflE=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBAEMKit.framework/Modules/FBAEMKit.swiftmodule/x86_64-apple-ios-simulator.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			iEuyJdx39q5xkazPBRMuG8QnpjQ=
			</data>
			<key>hash2</key>
			<data>
			ncIlY8ww+d3SpvUFqBLdG0quFn6l7dPZ/GOcAJvIGeQ=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBAEMKit.framework/Modules/FBAEMKit.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			eCU8L9BgWw+wbfH5k6uK6cqUJCg=
			</data>
			<key>hash2</key>
			<data>
			Sn+Fr2y6BA70VnenLXs6yHz2l7MDrqCJd7dJbaj4K1Q=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBAEMKit.framework/Modules/FBAEMKit.swiftmodule/x86_64-apple-ios-simulator.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			iEuyJdx39q5xkazPBRMuG8QnpjQ=
			</data>
			<key>hash2</key>
			<data>
			ncIlY8ww+d3SpvUFqBLdG0quFn6l7dPZ/GOcAJvIGeQ=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBAEMKit.framework/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			Ci3QIdviXpIzxSC88i1rGvW2cSs=
			</data>
			<key>hash2</key>
			<data>
			jq+nSulDKiN4tenILjgXg82TzM040TCurYFTCVRKXxM=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBAEMKit.framework/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash</key>
			<data>
			Fo7sebV/R02g8kqyPtqICO8eVyI=
			</data>
			<key>hash2</key>
			<data>
			ZFIpWmrSklTJLGaAOPLGos/UQMB82oH4FOmWrCFbhBU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBAEMKit.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash</key>
			<data>
			r742uaxfYXkRhrr0N0AGYoqW28s=
			</data>
			<key>hash2</key>
			<data>
			jQU4PVeySNXrsBhc7PVSzbsRlaOBRR77xUHMXLmvmdM=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/dSYMs/FBAEMKit.framework.dSYM/Contents/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			VaKEymQ/1yy7HfCzMAxX0WLRXk0=
			</data>
			<key>hash2</key>
			<data>
			pixPhJHJn9s5nRRo8xOpoEO9uj+TVOVjIiXT03xaKI4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/dSYMs/FBAEMKit.framework.dSYM/Contents/Resources/DWARF/FBAEMKit</key>
		<dict>
			<key>hash</key>
			<data>
			e1sGbN59bSZ0NcRcEo5oaC5c6jo=
			</data>
			<key>hash2</key>
			<data>
			zC6+DxatwfLEv72PJkXkXy2IOP1bG/vRa3m/lF4TzC0=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/dSYMs/FBAEMKit.framework.dSYM/Contents/Resources/Relocations/aarch64/FBAEMKit.yml</key>
		<dict>
			<key>hash</key>
			<data>
			BqMGg0SiTn3lsl0eZaZG48HXe9M=
			</data>
			<key>hash2</key>
			<data>
			4qGJlbuzAxaPo1LyI7xs0T1zUjLKLMgtrV3Y5cZ2R08=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/dSYMs/FBAEMKit.framework.dSYM/Contents/Resources/Relocations/x86_64/FBAEMKit.yml</key>
		<dict>
			<key>hash</key>
			<data>
			VoMiG6xhI9qDG8f8zC7DVFSrnaU=
			</data>
			<key>hash2</key>
			<data>
			DhoF5XPDl6YeXQBs1hj9nSzyvd8uN8lsyNW/oxDWZ7I=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
