<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/FBAEMKit-Swift.h</key>
		<data>
		XwfElLRDNnpKKn0PH/hO9BM6QRU=
		</data>
		<key>Info.plist</key>
		<data>
		wqtigv2WSFQZigZ0UgT7OvtN2nI=
		</data>
		<key>Modules/FBAEMKit.swiftmodule/arm64-apple-ios.abi.json</key>
		<data>
		x/cYBL8fG5PLfr6/8sf/Ngx0NbM=
		</data>
		<key>Modules/FBAEMKit.swiftmodule/arm64-apple-ios.private.swiftinterface</key>
		<data>
		vuOt4lh95Y0TvldvN/y3KNDKZfs=
		</data>
		<key>Modules/FBAEMKit.swiftmodule/arm64-apple-ios.swiftdoc</key>
		<data>
		nb9852QDL6O4NKXWpLS4zgC6tQ8=
		</data>
		<key>Modules/FBAEMKit.swiftmodule/arm64-apple-ios.swiftinterface</key>
		<data>
		vuOt4lh95Y0TvldvN/y3KNDKZfs=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		Ci3QIdviXpIzxSC88i1rGvW2cSs=
		</data>
		<key>PrivacyInfo.xcprivacy</key>
		<data>
		Fo7sebV/R02g8kqyPtqICO8eVyI=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/FBAEMKit-Swift.h</key>
		<dict>
			<key>hash2</key>
			<data>
			GImGAI1xnOe3jvV+cbROfPGbUXhQPKpIfyqbrRqfgOI=
			</data>
		</dict>
		<key>Modules/FBAEMKit.swiftmodule/arm64-apple-ios.abi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			zhvZWrKs4KE/ICaMDH4+lrdNAD56u6Z9aXaEhEsKflE=
			</data>
		</dict>
		<key>Modules/FBAEMKit.swiftmodule/arm64-apple-ios.private.swiftinterface</key>
		<dict>
			<key>hash2</key>
			<data>
			z/WKCqfpEndXznM7q+3+k6CfNqCAVHFlJQ+73I3leD0=
			</data>
		</dict>
		<key>Modules/FBAEMKit.swiftmodule/arm64-apple-ios.swiftdoc</key>
		<dict>
			<key>hash2</key>
			<data>
			wyQas2yzjsYVhwrz030g2aq0yBDc4YNETg4qd+GtQ+Y=
			</data>
		</dict>
		<key>Modules/FBAEMKit.swiftmodule/arm64-apple-ios.swiftinterface</key>
		<dict>
			<key>hash2</key>
			<data>
			z/WKCqfpEndXznM7q+3+k6CfNqCAVHFlJQ+73I3leD0=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash2</key>
			<data>
			jq+nSulDKiN4tenILjgXg82TzM040TCurYFTCVRKXxM=
			</data>
		</dict>
		<key>PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			ZFIpWmrSklTJLGaAOPLGos/UQMB82oH4FOmWrCFbhBU=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
