<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/FBAEMKit-Swift.h</key>
		<data>
		7sS/nlKqm4Wov9u8GGLhdrKFOFY=
		</data>
		<key>Info.plist</key>
		<data>
		jY5vUqohSxw5/EjUZlfO7Njse10=
		</data>
		<key>Modules/FBAEMKit.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<data>
		x/cYBL8fG5PLfr6/8sf/Ngx0NbM=
		</data>
		<key>Modules/FBAEMKit.swiftmodule/arm64-apple-ios-simulator.private.swiftinterface</key>
		<data>
		o+7uaJqh/sAx1Tf2xiGSBVExosE=
		</data>
		<key>Modules/FBAEMKit.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<data>
		zJaXLkkA6yIO2IF1tP55khlzpg0=
		</data>
		<key>Modules/FBAEMKit.swiftmodule/arm64-apple-ios-simulator.swiftinterface</key>
		<data>
		o+7uaJqh/sAx1Tf2xiGSBVExosE=
		</data>
		<key>Modules/FBAEMKit.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<data>
		x/cYBL8fG5PLfr6/8sf/Ngx0NbM=
		</data>
		<key>Modules/FBAEMKit.swiftmodule/x86_64-apple-ios-simulator.private.swiftinterface</key>
		<data>
		iEuyJdx39q5xkazPBRMuG8QnpjQ=
		</data>
		<key>Modules/FBAEMKit.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<data>
		eCU8L9BgWw+wbfH5k6uK6cqUJCg=
		</data>
		<key>Modules/FBAEMKit.swiftmodule/x86_64-apple-ios-simulator.swiftinterface</key>
		<data>
		iEuyJdx39q5xkazPBRMuG8QnpjQ=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		Ci3QIdviXpIzxSC88i1rGvW2cSs=
		</data>
		<key>PrivacyInfo.xcprivacy</key>
		<data>
		Fo7sebV/R02g8kqyPtqICO8eVyI=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/FBAEMKit-Swift.h</key>
		<dict>
			<key>hash2</key>
			<data>
			rnoOnLQ4Ls7PXoieTZ+/SZby/qY9o1psqoiAc8qJxKM=
			</data>
		</dict>
		<key>Modules/FBAEMKit.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			zhvZWrKs4KE/ICaMDH4+lrdNAD56u6Z9aXaEhEsKflE=
			</data>
		</dict>
		<key>Modules/FBAEMKit.swiftmodule/arm64-apple-ios-simulator.private.swiftinterface</key>
		<dict>
			<key>hash2</key>
			<data>
			FNSe3QaY5n/t5GHRf3LdCbL0ZQBHUCUhEFGTt4y8Wxo=
			</data>
		</dict>
		<key>Modules/FBAEMKit.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash2</key>
			<data>
			AM3qM4pBd8dSfu1jbLD9SssEcT1XyHtBYgyOqPYfhh0=
			</data>
		</dict>
		<key>Modules/FBAEMKit.swiftmodule/arm64-apple-ios-simulator.swiftinterface</key>
		<dict>
			<key>hash2</key>
			<data>
			FNSe3QaY5n/t5GHRf3LdCbL0ZQBHUCUhEFGTt4y8Wxo=
			</data>
		</dict>
		<key>Modules/FBAEMKit.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			zhvZWrKs4KE/ICaMDH4+lrdNAD56u6Z9aXaEhEsKflE=
			</data>
		</dict>
		<key>Modules/FBAEMKit.swiftmodule/x86_64-apple-ios-simulator.private.swiftinterface</key>
		<dict>
			<key>hash2</key>
			<data>
			ncIlY8ww+d3SpvUFqBLdG0quFn6l7dPZ/GOcAJvIGeQ=
			</data>
		</dict>
		<key>Modules/FBAEMKit.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash2</key>
			<data>
			Sn+Fr2y6BA70VnenLXs6yHz2l7MDrqCJd7dJbaj4K1Q=
			</data>
		</dict>
		<key>Modules/FBAEMKit.swiftmodule/x86_64-apple-ios-simulator.swiftinterface</key>
		<dict>
			<key>hash2</key>
			<data>
			ncIlY8ww+d3SpvUFqBLdG0quFn6l7dPZ/GOcAJvIGeQ=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash2</key>
			<data>
			jq+nSulDKiN4tenILjgXg82TzM040TCurYFTCVRKXxM=
			</data>
		</dict>
		<key>PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			ZFIpWmrSklTJLGaAOPLGos/UQMB82oH4FOmWrCFbhBU=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
