<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>LICENSE</key>
		<data>
		42oX9oAD1yj/wA6aqkh1wyx+qqA=
		</data>
		<key>ios-arm64/FBSDKCoreKit_Basics.framework/FBSDKCoreKit_Basics</key>
		<data>
		NTPej7H55kYgR54cuIYleDuYxyo=
		</data>
		<key>ios-arm64/FBSDKCoreKit_Basics.framework/Headers/FBSDKBase64.h</key>
		<data>
		4WPA7ov2bAhRn5QP5eZDBYX+spk=
		</data>
		<key>ios-arm64/FBSDKCoreKit_Basics.framework/Headers/FBSDKBasicUtility.h</key>
		<data>
		3Ij+0qMFvTmtUIuRbymzor6FDzg=
		</data>
		<key>ios-arm64/FBSDKCoreKit_Basics.framework/Headers/FBSDKCoreKit_Basics.h</key>
		<data>
		98/O+nEXsNQ8AcNbx/iw8ai/ktE=
		</data>
		<key>ios-arm64/FBSDKCoreKit_Basics.framework/Headers/FBSDKCrashHandler.h</key>
		<data>
		CZiEDVm8gHGkALwyjZtYF2K3LGk=
		</data>
		<key>ios-arm64/FBSDKCoreKit_Basics.framework/Headers/FBSDKCrashHandlerProtocol.h</key>
		<data>
		vSB3Xn5mAIXVnisb8nAGwYKzAe0=
		</data>
		<key>ios-arm64/FBSDKCoreKit_Basics.framework/Headers/FBSDKCrashObserving.h</key>
		<data>
		UhJc2ZYtB2hcyXxk00KbwKbTgcc=
		</data>
		<key>ios-arm64/FBSDKCoreKit_Basics.framework/Headers/FBSDKDataPersisting.h</key>
		<data>
		DtnVkIyBlZEE5hHK6RM8kIawq7w=
		</data>
		<key>ios-arm64/FBSDKCoreKit_Basics.framework/Headers/FBSDKFileDataExtracting.h</key>
		<data>
		BQDcfAulsMenmeoahsXxQgOwOH8=
		</data>
		<key>ios-arm64/FBSDKCoreKit_Basics.framework/Headers/FBSDKFileManaging.h</key>
		<data>
		p/4j5P/SU+cTiWeHYIULKGdiaEg=
		</data>
		<key>ios-arm64/FBSDKCoreKit_Basics.framework/Headers/FBSDKInfoDictionaryProviding.h</key>
		<data>
		okxIBK/wxL+ugvqad38fKg7+VyU=
		</data>
		<key>ios-arm64/FBSDKCoreKit_Basics.framework/Headers/FBSDKLibAnalyzer.h</key>
		<data>
		Sr7GpMqIG4HmNNhoU6xSJrR36F8=
		</data>
		<key>ios-arm64/FBSDKCoreKit_Basics.framework/Headers/FBSDKLinking.h</key>
		<data>
		1OBirQhOiNPiwhFBBuCxquzs+8o=
		</data>
		<key>ios-arm64/FBSDKCoreKit_Basics.framework/Headers/FBSDKNetworkTask.h</key>
		<data>
		0sGmRF6Hdq6tvoAuo8957/Esjbc=
		</data>
		<key>ios-arm64/FBSDKCoreKit_Basics.framework/Headers/FBSDKNotificationDelivering.h</key>
		<data>
		H1EtkXfryfCD6P8NlmWazMezAP0=
		</data>
		<key>ios-arm64/FBSDKCoreKit_Basics.framework/Headers/FBSDKTypeUtility.h</key>
		<data>
		qgaA6wWpYIa+hM+VZSTB1P0w8Jk=
		</data>
		<key>ios-arm64/FBSDKCoreKit_Basics.framework/Headers/FBSDKURLSession.h</key>
		<data>
		lNyxKy0KC2f37l17PcuP0Gv/EDA=
		</data>
		<key>ios-arm64/FBSDKCoreKit_Basics.framework/Headers/FBSDKURLSessionProviding.h</key>
		<data>
		1wsWv3wp81s7ODMRhEscWZBDXkc=
		</data>
		<key>ios-arm64/FBSDKCoreKit_Basics.framework/Headers/FBSDKURLSessionTask.h</key>
		<data>
		aOm3T7/JlTQP0GIRlnLQzT+NKc8=
		</data>
		<key>ios-arm64/FBSDKCoreKit_Basics.framework/Headers/NSNotificationCenter+NotificationDelivering.h</key>
		<data>
		7k8yl29uNS0GjwEBkfViBHp6lN0=
		</data>
		<key>ios-arm64/FBSDKCoreKit_Basics.framework/Info.plist</key>
		<data>
		FemhVoYjpVZGAoM5GI9iYBLq4vU=
		</data>
		<key>ios-arm64/FBSDKCoreKit_Basics.framework/Modules/module.modulemap</key>
		<data>
		dAKyD+E6uMaCMsZEO7c47okujEM=
		</data>
		<key>ios-arm64/FBSDKCoreKit_Basics.framework/PrivacyInfo.xcprivacy</key>
		<data>
		OyS/P0To2cmktqAfP4sDQxHrwXo=
		</data>
		<key>ios-arm64/FBSDKCoreKit_Basics.framework/_CodeSignature/CodeResources</key>
		<data>
		Tp+eIbHjZIO+l1AqRoAI39LPCnY=
		</data>
		<key>ios-arm64/dSYMs/FBSDKCoreKit_Basics.framework.dSYM/Contents/Info.plist</key>
		<data>
		8u6APwbQM/MbAsvk0wBXG3E35LI=
		</data>
		<key>ios-arm64/dSYMs/FBSDKCoreKit_Basics.framework.dSYM/Contents/Resources/DWARF/FBSDKCoreKit_Basics</key>
		<data>
		fh8icEdOSXmr0Y9tiemUvfG3HHw=
		</data>
		<key>ios-arm64/dSYMs/FBSDKCoreKit_Basics.framework.dSYM/Contents/Resources/Relocations/aarch64/FBSDKCoreKit_Basics.yml</key>
		<data>
		9+WqlpnX0QM+Mz/lqhjfpY0bZos=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit_Basics.framework/Versions/A/FBSDKCoreKit_Basics</key>
		<data>
		5rg08hQ8Sm7a9DclRPpUI91uXOc=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit_Basics.framework/Versions/A/Headers/FBSDKBase64.h</key>
		<data>
		4WPA7ov2bAhRn5QP5eZDBYX+spk=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit_Basics.framework/Versions/A/Headers/FBSDKBasicUtility.h</key>
		<data>
		3Ij+0qMFvTmtUIuRbymzor6FDzg=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit_Basics.framework/Versions/A/Headers/FBSDKCoreKit_Basics.h</key>
		<data>
		98/O+nEXsNQ8AcNbx/iw8ai/ktE=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit_Basics.framework/Versions/A/Headers/FBSDKCrashHandler.h</key>
		<data>
		CZiEDVm8gHGkALwyjZtYF2K3LGk=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit_Basics.framework/Versions/A/Headers/FBSDKCrashHandlerProtocol.h</key>
		<data>
		vSB3Xn5mAIXVnisb8nAGwYKzAe0=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit_Basics.framework/Versions/A/Headers/FBSDKCrashObserving.h</key>
		<data>
		UhJc2ZYtB2hcyXxk00KbwKbTgcc=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit_Basics.framework/Versions/A/Headers/FBSDKDataPersisting.h</key>
		<data>
		DtnVkIyBlZEE5hHK6RM8kIawq7w=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit_Basics.framework/Versions/A/Headers/FBSDKFileDataExtracting.h</key>
		<data>
		BQDcfAulsMenmeoahsXxQgOwOH8=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit_Basics.framework/Versions/A/Headers/FBSDKFileManaging.h</key>
		<data>
		p/4j5P/SU+cTiWeHYIULKGdiaEg=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit_Basics.framework/Versions/A/Headers/FBSDKInfoDictionaryProviding.h</key>
		<data>
		okxIBK/wxL+ugvqad38fKg7+VyU=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit_Basics.framework/Versions/A/Headers/FBSDKLibAnalyzer.h</key>
		<data>
		Sr7GpMqIG4HmNNhoU6xSJrR36F8=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit_Basics.framework/Versions/A/Headers/FBSDKLinking.h</key>
		<data>
		1OBirQhOiNPiwhFBBuCxquzs+8o=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit_Basics.framework/Versions/A/Headers/FBSDKNetworkTask.h</key>
		<data>
		0sGmRF6Hdq6tvoAuo8957/Esjbc=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit_Basics.framework/Versions/A/Headers/FBSDKNotificationDelivering.h</key>
		<data>
		H1EtkXfryfCD6P8NlmWazMezAP0=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit_Basics.framework/Versions/A/Headers/FBSDKTypeUtility.h</key>
		<data>
		qgaA6wWpYIa+hM+VZSTB1P0w8Jk=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit_Basics.framework/Versions/A/Headers/FBSDKURLSession.h</key>
		<data>
		lNyxKy0KC2f37l17PcuP0Gv/EDA=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit_Basics.framework/Versions/A/Headers/FBSDKURLSessionProviding.h</key>
		<data>
		1wsWv3wp81s7ODMRhEscWZBDXkc=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit_Basics.framework/Versions/A/Headers/FBSDKURLSessionTask.h</key>
		<data>
		aOm3T7/JlTQP0GIRlnLQzT+NKc8=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit_Basics.framework/Versions/A/Headers/NSNotificationCenter+NotificationDelivering.h</key>
		<data>
		7k8yl29uNS0GjwEBkfViBHp6lN0=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit_Basics.framework/Versions/A/Modules/module.modulemap</key>
		<data>
		dAKyD+E6uMaCMsZEO7c47okujEM=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit_Basics.framework/Versions/A/Resources/Info.plist</key>
		<data>
		7ODZZUF3FBaHuREIEAcvVk03r1E=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit_Basics.framework/Versions/A/Resources/PrivacyInfo.xcprivacy</key>
		<data>
		OyS/P0To2cmktqAfP4sDQxHrwXo=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit_Basics.framework/Versions/A/_CodeSignature/CodeResources</key>
		<data>
		DrjOCEEt+2JknvyT7YwUoX8JInI=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/dSYMs/FBSDKCoreKit_Basics.framework.dSYM/Contents/Info.plist</key>
		<data>
		8u6APwbQM/MbAsvk0wBXG3E35LI=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/dSYMs/FBSDKCoreKit_Basics.framework.dSYM/Contents/Resources/DWARF/FBSDKCoreKit_Basics</key>
		<data>
		/s7x8/nqUKK8/Xw0hvjAN/9BW74=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/dSYMs/FBSDKCoreKit_Basics.framework.dSYM/Contents/Resources/Relocations/aarch64/FBSDKCoreKit_Basics.yml</key>
		<data>
		CpCfxheF8dg8rZ0DvPKovjtjBuc=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/dSYMs/FBSDKCoreKit_Basics.framework.dSYM/Contents/Resources/Relocations/x86_64/FBSDKCoreKit_Basics.yml</key>
		<data>
		jwzif19A6oHgyOuAFhAmHmwrSLc=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit_Basics.framework/FBSDKCoreKit_Basics</key>
		<data>
		sAqKxGz/LjkWQ6yO8IwnkNBx9TE=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit_Basics.framework/Headers/FBSDKBase64.h</key>
		<data>
		4WPA7ov2bAhRn5QP5eZDBYX+spk=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit_Basics.framework/Headers/FBSDKBasicUtility.h</key>
		<data>
		3Ij+0qMFvTmtUIuRbymzor6FDzg=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit_Basics.framework/Headers/FBSDKCoreKit_Basics.h</key>
		<data>
		98/O+nEXsNQ8AcNbx/iw8ai/ktE=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit_Basics.framework/Headers/FBSDKCrashHandler.h</key>
		<data>
		CZiEDVm8gHGkALwyjZtYF2K3LGk=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit_Basics.framework/Headers/FBSDKCrashHandlerProtocol.h</key>
		<data>
		vSB3Xn5mAIXVnisb8nAGwYKzAe0=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit_Basics.framework/Headers/FBSDKCrashObserving.h</key>
		<data>
		UhJc2ZYtB2hcyXxk00KbwKbTgcc=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit_Basics.framework/Headers/FBSDKDataPersisting.h</key>
		<data>
		DtnVkIyBlZEE5hHK6RM8kIawq7w=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit_Basics.framework/Headers/FBSDKFileDataExtracting.h</key>
		<data>
		BQDcfAulsMenmeoahsXxQgOwOH8=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit_Basics.framework/Headers/FBSDKFileManaging.h</key>
		<data>
		p/4j5P/SU+cTiWeHYIULKGdiaEg=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit_Basics.framework/Headers/FBSDKInfoDictionaryProviding.h</key>
		<data>
		okxIBK/wxL+ugvqad38fKg7+VyU=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit_Basics.framework/Headers/FBSDKLibAnalyzer.h</key>
		<data>
		Sr7GpMqIG4HmNNhoU6xSJrR36F8=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit_Basics.framework/Headers/FBSDKLinking.h</key>
		<data>
		1OBirQhOiNPiwhFBBuCxquzs+8o=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit_Basics.framework/Headers/FBSDKNetworkTask.h</key>
		<data>
		0sGmRF6Hdq6tvoAuo8957/Esjbc=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit_Basics.framework/Headers/FBSDKNotificationDelivering.h</key>
		<data>
		H1EtkXfryfCD6P8NlmWazMezAP0=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit_Basics.framework/Headers/FBSDKTypeUtility.h</key>
		<data>
		qgaA6wWpYIa+hM+VZSTB1P0w8Jk=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit_Basics.framework/Headers/FBSDKURLSession.h</key>
		<data>
		lNyxKy0KC2f37l17PcuP0Gv/EDA=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit_Basics.framework/Headers/FBSDKURLSessionProviding.h</key>
		<data>
		1wsWv3wp81s7ODMRhEscWZBDXkc=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit_Basics.framework/Headers/FBSDKURLSessionTask.h</key>
		<data>
		aOm3T7/JlTQP0GIRlnLQzT+NKc8=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit_Basics.framework/Headers/NSNotificationCenter+NotificationDelivering.h</key>
		<data>
		7k8yl29uNS0GjwEBkfViBHp6lN0=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit_Basics.framework/Info.plist</key>
		<data>
		lvmc2jbr0W9Po7R51UBu3hiuJDU=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit_Basics.framework/Modules/module.modulemap</key>
		<data>
		dAKyD+E6uMaCMsZEO7c47okujEM=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit_Basics.framework/PrivacyInfo.xcprivacy</key>
		<data>
		OyS/P0To2cmktqAfP4sDQxHrwXo=
		</data>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit_Basics.framework/_CodeSignature/CodeResources</key>
		<data>
		y/TELoaijTVbg3Vu9vcVdns7+mk=
		</data>
		<key>ios-arm64_x86_64-simulator/dSYMs/FBSDKCoreKit_Basics.framework.dSYM/Contents/Info.plist</key>
		<data>
		8u6APwbQM/MbAsvk0wBXG3E35LI=
		</data>
		<key>ios-arm64_x86_64-simulator/dSYMs/FBSDKCoreKit_Basics.framework.dSYM/Contents/Resources/DWARF/FBSDKCoreKit_Basics</key>
		<data>
		dgupeYokz8yTm4ZVua3D6ne7ggc=
		</data>
		<key>ios-arm64_x86_64-simulator/dSYMs/FBSDKCoreKit_Basics.framework.dSYM/Contents/Resources/Relocations/aarch64/FBSDKCoreKit_Basics.yml</key>
		<data>
		F5Cs5Z1ttauxw0pvEfG4dV23ojA=
		</data>
		<key>ios-arm64_x86_64-simulator/dSYMs/FBSDKCoreKit_Basics.framework.dSYM/Contents/Resources/Relocations/x86_64/FBSDKCoreKit_Basics.yml</key>
		<data>
		rrjkE4FM//GiH1XPBo4nM8xZIKo=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>LICENSE</key>
		<dict>
			<key>hash</key>
			<data>
			42oX9oAD1yj/wA6aqkh1wyx+qqA=
			</data>
			<key>hash2</key>
			<data>
			JGiNyKThXtEPUCL2A80E+FzHN+UTW+RkFoApZE8iHm8=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit_Basics.framework/FBSDKCoreKit_Basics</key>
		<dict>
			<key>hash</key>
			<data>
			NTPej7H55kYgR54cuIYleDuYxyo=
			</data>
			<key>hash2</key>
			<data>
			QMf76M8LcKXH+edRLI8BZ3usDg+Hsh39B+G5QCbS1qo=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit_Basics.framework/Headers/FBSDKBase64.h</key>
		<dict>
			<key>hash</key>
			<data>
			4WPA7ov2bAhRn5QP5eZDBYX+spk=
			</data>
			<key>hash2</key>
			<data>
			ePF4YsPJC4jFg1uvNt6fb83WN1IDTijvBhGt+V0Rh/8=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit_Basics.framework/Headers/FBSDKBasicUtility.h</key>
		<dict>
			<key>hash</key>
			<data>
			3Ij+0qMFvTmtUIuRbymzor6FDzg=
			</data>
			<key>hash2</key>
			<data>
			4HOJGD9dL2BJIlaj/ej0J6MTNqBvYgF0/XrWiZtsT3E=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit_Basics.framework/Headers/FBSDKCoreKit_Basics.h</key>
		<dict>
			<key>hash</key>
			<data>
			98/O+nEXsNQ8AcNbx/iw8ai/ktE=
			</data>
			<key>hash2</key>
			<data>
			n96Ist8MxHyeU+TJA3OhwPRry3QcVtumXYCUmhcHycw=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit_Basics.framework/Headers/FBSDKCrashHandler.h</key>
		<dict>
			<key>hash</key>
			<data>
			CZiEDVm8gHGkALwyjZtYF2K3LGk=
			</data>
			<key>hash2</key>
			<data>
			3ILHiPoEMzce96plPruxswh7D9L+ptDaLZH98MYQ5rY=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit_Basics.framework/Headers/FBSDKCrashHandlerProtocol.h</key>
		<dict>
			<key>hash</key>
			<data>
			vSB3Xn5mAIXVnisb8nAGwYKzAe0=
			</data>
			<key>hash2</key>
			<data>
			qZ25WCONiSxncOC5vN2J90QWqM9a21jnkfZjwxq0odA=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit_Basics.framework/Headers/FBSDKCrashObserving.h</key>
		<dict>
			<key>hash</key>
			<data>
			UhJc2ZYtB2hcyXxk00KbwKbTgcc=
			</data>
			<key>hash2</key>
			<data>
			tO6F4Bbijxuct9m9xB8pcqmAlnyPPJ0EqbN+Z8/EM1w=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit_Basics.framework/Headers/FBSDKDataPersisting.h</key>
		<dict>
			<key>hash</key>
			<data>
			DtnVkIyBlZEE5hHK6RM8kIawq7w=
			</data>
			<key>hash2</key>
			<data>
			VcT2tlzFWSs7Ld7eUXcIezAttUSWdAIip7lZdNoZiGc=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit_Basics.framework/Headers/FBSDKFileDataExtracting.h</key>
		<dict>
			<key>hash</key>
			<data>
			BQDcfAulsMenmeoahsXxQgOwOH8=
			</data>
			<key>hash2</key>
			<data>
			s/7qW6G7GCOOyCS4jkWz8szFjT6uujuGXGhTa5JF4G4=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit_Basics.framework/Headers/FBSDKFileManaging.h</key>
		<dict>
			<key>hash</key>
			<data>
			p/4j5P/SU+cTiWeHYIULKGdiaEg=
			</data>
			<key>hash2</key>
			<data>
			6+3pxgKpUWQwvlj3RwBFPC0rr5+wK651qUwh0n4fPHs=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit_Basics.framework/Headers/FBSDKInfoDictionaryProviding.h</key>
		<dict>
			<key>hash</key>
			<data>
			okxIBK/wxL+ugvqad38fKg7+VyU=
			</data>
			<key>hash2</key>
			<data>
			udZpX0hlPOkgV99Ck52TrAmlNiyK4r2qtK+kyPsJl8M=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit_Basics.framework/Headers/FBSDKLibAnalyzer.h</key>
		<dict>
			<key>hash</key>
			<data>
			Sr7GpMqIG4HmNNhoU6xSJrR36F8=
			</data>
			<key>hash2</key>
			<data>
			vcTMWhsIb/z29oxTpsnEfTWCnlgLS7qlPQuTTREW6Ek=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit_Basics.framework/Headers/FBSDKLinking.h</key>
		<dict>
			<key>hash</key>
			<data>
			1OBirQhOiNPiwhFBBuCxquzs+8o=
			</data>
			<key>hash2</key>
			<data>
			5NmQW3WO6TuTqGtMxocXiOeGiV7p3hyX2Saia2UhhL4=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit_Basics.framework/Headers/FBSDKNetworkTask.h</key>
		<dict>
			<key>hash</key>
			<data>
			0sGmRF6Hdq6tvoAuo8957/Esjbc=
			</data>
			<key>hash2</key>
			<data>
			WXUdURA38eU1UT+VzBqNd3NJZ/tMnqMgcYFfskguL9w=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit_Basics.framework/Headers/FBSDKNotificationDelivering.h</key>
		<dict>
			<key>hash</key>
			<data>
			H1EtkXfryfCD6P8NlmWazMezAP0=
			</data>
			<key>hash2</key>
			<data>
			jCbQRKCjKWBaILGHzII7b5E1nAY37YAd0KWD5lhnyBM=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit_Basics.framework/Headers/FBSDKTypeUtility.h</key>
		<dict>
			<key>hash</key>
			<data>
			qgaA6wWpYIa+hM+VZSTB1P0w8Jk=
			</data>
			<key>hash2</key>
			<data>
			Xm2cMC3iy1kvQn8EXXkZ0eKLNRVCmSlSbefuvvTCTTs=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit_Basics.framework/Headers/FBSDKURLSession.h</key>
		<dict>
			<key>hash</key>
			<data>
			lNyxKy0KC2f37l17PcuP0Gv/EDA=
			</data>
			<key>hash2</key>
			<data>
			5L3MvrHaYDNtK2zrxdEtZNCRnhTQHNM0bvoh0pAfKg0=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit_Basics.framework/Headers/FBSDKURLSessionProviding.h</key>
		<dict>
			<key>hash</key>
			<data>
			1wsWv3wp81s7ODMRhEscWZBDXkc=
			</data>
			<key>hash2</key>
			<data>
			O0czQhC3wEX5AKGOHblqZYeevvp4+w3IVFYRSr4Uv9o=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit_Basics.framework/Headers/FBSDKURLSessionTask.h</key>
		<dict>
			<key>hash</key>
			<data>
			aOm3T7/JlTQP0GIRlnLQzT+NKc8=
			</data>
			<key>hash2</key>
			<data>
			3Efn6F2Y10Aul+I89unuMGdO1GJTM5xkZOglY+a5XtE=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit_Basics.framework/Headers/NSNotificationCenter+NotificationDelivering.h</key>
		<dict>
			<key>hash</key>
			<data>
			7k8yl29uNS0GjwEBkfViBHp6lN0=
			</data>
			<key>hash2</key>
			<data>
			ODQiUcnulvLSI4AHqIqtQvUPeAp+/9qi2eIHXnVKxh8=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit_Basics.framework/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			FemhVoYjpVZGAoM5GI9iYBLq4vU=
			</data>
			<key>hash2</key>
			<data>
			5QWTy39z47AzD2Tzx6HwSJk77s6lasOGdaC/ZKHlE/k=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit_Basics.framework/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			dAKyD+E6uMaCMsZEO7c47okujEM=
			</data>
			<key>hash2</key>
			<data>
			Ax5EJgz2ae9iwvEaJiJhRzHL4ePWe4qZhIaEdKJk27I=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit_Basics.framework/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash</key>
			<data>
			OyS/P0To2cmktqAfP4sDQxHrwXo=
			</data>
			<key>hash2</key>
			<data>
			WsyF5Q/iCf15mIOe/RlsYmYVYjlwU787E0oxD4TOhVs=
			</data>
		</dict>
		<key>ios-arm64/FBSDKCoreKit_Basics.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash</key>
			<data>
			Tp+eIbHjZIO+l1AqRoAI39LPCnY=
			</data>
			<key>hash2</key>
			<data>
			aCfZ+PHmsRUOUbmfeDMCUoFtU5O8ETIN6k+LKa6o/4I=
			</data>
		</dict>
		<key>ios-arm64/dSYMs/FBSDKCoreKit_Basics.framework.dSYM/Contents/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			8u6APwbQM/MbAsvk0wBXG3E35LI=
			</data>
			<key>hash2</key>
			<data>
			9RIqGM9whfQTtWCpiDKDvp0WIM4mLIofDOYs23hqeNE=
			</data>
		</dict>
		<key>ios-arm64/dSYMs/FBSDKCoreKit_Basics.framework.dSYM/Contents/Resources/DWARF/FBSDKCoreKit_Basics</key>
		<dict>
			<key>hash</key>
			<data>
			fh8icEdOSXmr0Y9tiemUvfG3HHw=
			</data>
			<key>hash2</key>
			<data>
			gBvXq3Il7R0RBOYXrNA+7MYPynccweQZBRDCEQwaQ2U=
			</data>
		</dict>
		<key>ios-arm64/dSYMs/FBSDKCoreKit_Basics.framework.dSYM/Contents/Resources/Relocations/aarch64/FBSDKCoreKit_Basics.yml</key>
		<dict>
			<key>hash</key>
			<data>
			9+WqlpnX0QM+Mz/lqhjfpY0bZos=
			</data>
			<key>hash2</key>
			<data>
			d0STnqrN/pbThlGP6puVZjNrHLaomx1g4oU9mcGKQW8=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit_Basics.framework/FBSDKCoreKit_Basics</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/FBSDKCoreKit_Basics</string>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit_Basics.framework/Headers</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/Headers</string>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit_Basics.framework/Modules</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/Modules</string>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit_Basics.framework/Resources</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/Resources</string>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit_Basics.framework/Versions/A/FBSDKCoreKit_Basics</key>
		<dict>
			<key>hash</key>
			<data>
			5rg08hQ8Sm7a9DclRPpUI91uXOc=
			</data>
			<key>hash2</key>
			<data>
			jOE1ehQqMUBtlGTSLIf11Kl/OTlRpuorFPoWG4ZiBxk=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit_Basics.framework/Versions/A/Headers/FBSDKBase64.h</key>
		<dict>
			<key>hash</key>
			<data>
			4WPA7ov2bAhRn5QP5eZDBYX+spk=
			</data>
			<key>hash2</key>
			<data>
			ePF4YsPJC4jFg1uvNt6fb83WN1IDTijvBhGt+V0Rh/8=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit_Basics.framework/Versions/A/Headers/FBSDKBasicUtility.h</key>
		<dict>
			<key>hash</key>
			<data>
			3Ij+0qMFvTmtUIuRbymzor6FDzg=
			</data>
			<key>hash2</key>
			<data>
			4HOJGD9dL2BJIlaj/ej0J6MTNqBvYgF0/XrWiZtsT3E=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit_Basics.framework/Versions/A/Headers/FBSDKCoreKit_Basics.h</key>
		<dict>
			<key>hash</key>
			<data>
			98/O+nEXsNQ8AcNbx/iw8ai/ktE=
			</data>
			<key>hash2</key>
			<data>
			n96Ist8MxHyeU+TJA3OhwPRry3QcVtumXYCUmhcHycw=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit_Basics.framework/Versions/A/Headers/FBSDKCrashHandler.h</key>
		<dict>
			<key>hash</key>
			<data>
			CZiEDVm8gHGkALwyjZtYF2K3LGk=
			</data>
			<key>hash2</key>
			<data>
			3ILHiPoEMzce96plPruxswh7D9L+ptDaLZH98MYQ5rY=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit_Basics.framework/Versions/A/Headers/FBSDKCrashHandlerProtocol.h</key>
		<dict>
			<key>hash</key>
			<data>
			vSB3Xn5mAIXVnisb8nAGwYKzAe0=
			</data>
			<key>hash2</key>
			<data>
			qZ25WCONiSxncOC5vN2J90QWqM9a21jnkfZjwxq0odA=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit_Basics.framework/Versions/A/Headers/FBSDKCrashObserving.h</key>
		<dict>
			<key>hash</key>
			<data>
			UhJc2ZYtB2hcyXxk00KbwKbTgcc=
			</data>
			<key>hash2</key>
			<data>
			tO6F4Bbijxuct9m9xB8pcqmAlnyPPJ0EqbN+Z8/EM1w=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit_Basics.framework/Versions/A/Headers/FBSDKDataPersisting.h</key>
		<dict>
			<key>hash</key>
			<data>
			DtnVkIyBlZEE5hHK6RM8kIawq7w=
			</data>
			<key>hash2</key>
			<data>
			VcT2tlzFWSs7Ld7eUXcIezAttUSWdAIip7lZdNoZiGc=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit_Basics.framework/Versions/A/Headers/FBSDKFileDataExtracting.h</key>
		<dict>
			<key>hash</key>
			<data>
			BQDcfAulsMenmeoahsXxQgOwOH8=
			</data>
			<key>hash2</key>
			<data>
			s/7qW6G7GCOOyCS4jkWz8szFjT6uujuGXGhTa5JF4G4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit_Basics.framework/Versions/A/Headers/FBSDKFileManaging.h</key>
		<dict>
			<key>hash</key>
			<data>
			p/4j5P/SU+cTiWeHYIULKGdiaEg=
			</data>
			<key>hash2</key>
			<data>
			6+3pxgKpUWQwvlj3RwBFPC0rr5+wK651qUwh0n4fPHs=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit_Basics.framework/Versions/A/Headers/FBSDKInfoDictionaryProviding.h</key>
		<dict>
			<key>hash</key>
			<data>
			okxIBK/wxL+ugvqad38fKg7+VyU=
			</data>
			<key>hash2</key>
			<data>
			udZpX0hlPOkgV99Ck52TrAmlNiyK4r2qtK+kyPsJl8M=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit_Basics.framework/Versions/A/Headers/FBSDKLibAnalyzer.h</key>
		<dict>
			<key>hash</key>
			<data>
			Sr7GpMqIG4HmNNhoU6xSJrR36F8=
			</data>
			<key>hash2</key>
			<data>
			vcTMWhsIb/z29oxTpsnEfTWCnlgLS7qlPQuTTREW6Ek=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit_Basics.framework/Versions/A/Headers/FBSDKLinking.h</key>
		<dict>
			<key>hash</key>
			<data>
			1OBirQhOiNPiwhFBBuCxquzs+8o=
			</data>
			<key>hash2</key>
			<data>
			5NmQW3WO6TuTqGtMxocXiOeGiV7p3hyX2Saia2UhhL4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit_Basics.framework/Versions/A/Headers/FBSDKNetworkTask.h</key>
		<dict>
			<key>hash</key>
			<data>
			0sGmRF6Hdq6tvoAuo8957/Esjbc=
			</data>
			<key>hash2</key>
			<data>
			WXUdURA38eU1UT+VzBqNd3NJZ/tMnqMgcYFfskguL9w=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit_Basics.framework/Versions/A/Headers/FBSDKNotificationDelivering.h</key>
		<dict>
			<key>hash</key>
			<data>
			H1EtkXfryfCD6P8NlmWazMezAP0=
			</data>
			<key>hash2</key>
			<data>
			jCbQRKCjKWBaILGHzII7b5E1nAY37YAd0KWD5lhnyBM=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit_Basics.framework/Versions/A/Headers/FBSDKTypeUtility.h</key>
		<dict>
			<key>hash</key>
			<data>
			qgaA6wWpYIa+hM+VZSTB1P0w8Jk=
			</data>
			<key>hash2</key>
			<data>
			Xm2cMC3iy1kvQn8EXXkZ0eKLNRVCmSlSbefuvvTCTTs=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit_Basics.framework/Versions/A/Headers/FBSDKURLSession.h</key>
		<dict>
			<key>hash</key>
			<data>
			lNyxKy0KC2f37l17PcuP0Gv/EDA=
			</data>
			<key>hash2</key>
			<data>
			5L3MvrHaYDNtK2zrxdEtZNCRnhTQHNM0bvoh0pAfKg0=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit_Basics.framework/Versions/A/Headers/FBSDKURLSessionProviding.h</key>
		<dict>
			<key>hash</key>
			<data>
			1wsWv3wp81s7ODMRhEscWZBDXkc=
			</data>
			<key>hash2</key>
			<data>
			O0czQhC3wEX5AKGOHblqZYeevvp4+w3IVFYRSr4Uv9o=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit_Basics.framework/Versions/A/Headers/FBSDKURLSessionTask.h</key>
		<dict>
			<key>hash</key>
			<data>
			aOm3T7/JlTQP0GIRlnLQzT+NKc8=
			</data>
			<key>hash2</key>
			<data>
			3Efn6F2Y10Aul+I89unuMGdO1GJTM5xkZOglY+a5XtE=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit_Basics.framework/Versions/A/Headers/NSNotificationCenter+NotificationDelivering.h</key>
		<dict>
			<key>hash</key>
			<data>
			7k8yl29uNS0GjwEBkfViBHp6lN0=
			</data>
			<key>hash2</key>
			<data>
			ODQiUcnulvLSI4AHqIqtQvUPeAp+/9qi2eIHXnVKxh8=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit_Basics.framework/Versions/A/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			dAKyD+E6uMaCMsZEO7c47okujEM=
			</data>
			<key>hash2</key>
			<data>
			Ax5EJgz2ae9iwvEaJiJhRzHL4ePWe4qZhIaEdKJk27I=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit_Basics.framework/Versions/A/Resources/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			7ODZZUF3FBaHuREIEAcvVk03r1E=
			</data>
			<key>hash2</key>
			<data>
			ZTTz6FOs1CbxqvEa0w9zU6agEEbmvL8BYZyAlQxxxck=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit_Basics.framework/Versions/A/Resources/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash</key>
			<data>
			OyS/P0To2cmktqAfP4sDQxHrwXo=
			</data>
			<key>hash2</key>
			<data>
			WsyF5Q/iCf15mIOe/RlsYmYVYjlwU787E0oxD4TOhVs=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit_Basics.framework/Versions/A/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash</key>
			<data>
			DrjOCEEt+2JknvyT7YwUoX8JInI=
			</data>
			<key>hash2</key>
			<data>
			JJsxE7Kfh3/+V8yfxvpQedeUC8+ijup6J1g4wDBY6+o=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FBSDKCoreKit_Basics.framework/Versions/Current</key>
		<dict>
			<key>symlink</key>
			<string>A</string>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/dSYMs/FBSDKCoreKit_Basics.framework.dSYM/Contents/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			8u6APwbQM/MbAsvk0wBXG3E35LI=
			</data>
			<key>hash2</key>
			<data>
			9RIqGM9whfQTtWCpiDKDvp0WIM4mLIofDOYs23hqeNE=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/dSYMs/FBSDKCoreKit_Basics.framework.dSYM/Contents/Resources/DWARF/FBSDKCoreKit_Basics</key>
		<dict>
			<key>hash</key>
			<data>
			/s7x8/nqUKK8/Xw0hvjAN/9BW74=
			</data>
			<key>hash2</key>
			<data>
			B+B4JrooBjceN+aiqiS3EtBiUwwe+PCXj/q7srhD6hg=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/dSYMs/FBSDKCoreKit_Basics.framework.dSYM/Contents/Resources/Relocations/aarch64/FBSDKCoreKit_Basics.yml</key>
		<dict>
			<key>hash</key>
			<data>
			CpCfxheF8dg8rZ0DvPKovjtjBuc=
			</data>
			<key>hash2</key>
			<data>
			GA6VpGXj63l+ptDTacI9oZdB24YNEklyKW0nGwtsysY=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/dSYMs/FBSDKCoreKit_Basics.framework.dSYM/Contents/Resources/Relocations/x86_64/FBSDKCoreKit_Basics.yml</key>
		<dict>
			<key>hash</key>
			<data>
			jwzif19A6oHgyOuAFhAmHmwrSLc=
			</data>
			<key>hash2</key>
			<data>
			UXDU8jpE48yTtWWbqMbDtzHMeG1g1KENB30oVbHri3w=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit_Basics.framework/FBSDKCoreKit_Basics</key>
		<dict>
			<key>hash</key>
			<data>
			sAqKxGz/LjkWQ6yO8IwnkNBx9TE=
			</data>
			<key>hash2</key>
			<data>
			qFPpmsTSmMGZL4+JVyUQA7NvxtstnhHAZqbvn7I+a9A=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit_Basics.framework/Headers/FBSDKBase64.h</key>
		<dict>
			<key>hash</key>
			<data>
			4WPA7ov2bAhRn5QP5eZDBYX+spk=
			</data>
			<key>hash2</key>
			<data>
			ePF4YsPJC4jFg1uvNt6fb83WN1IDTijvBhGt+V0Rh/8=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit_Basics.framework/Headers/FBSDKBasicUtility.h</key>
		<dict>
			<key>hash</key>
			<data>
			3Ij+0qMFvTmtUIuRbymzor6FDzg=
			</data>
			<key>hash2</key>
			<data>
			4HOJGD9dL2BJIlaj/ej0J6MTNqBvYgF0/XrWiZtsT3E=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit_Basics.framework/Headers/FBSDKCoreKit_Basics.h</key>
		<dict>
			<key>hash</key>
			<data>
			98/O+nEXsNQ8AcNbx/iw8ai/ktE=
			</data>
			<key>hash2</key>
			<data>
			n96Ist8MxHyeU+TJA3OhwPRry3QcVtumXYCUmhcHycw=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit_Basics.framework/Headers/FBSDKCrashHandler.h</key>
		<dict>
			<key>hash</key>
			<data>
			CZiEDVm8gHGkALwyjZtYF2K3LGk=
			</data>
			<key>hash2</key>
			<data>
			3ILHiPoEMzce96plPruxswh7D9L+ptDaLZH98MYQ5rY=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit_Basics.framework/Headers/FBSDKCrashHandlerProtocol.h</key>
		<dict>
			<key>hash</key>
			<data>
			vSB3Xn5mAIXVnisb8nAGwYKzAe0=
			</data>
			<key>hash2</key>
			<data>
			qZ25WCONiSxncOC5vN2J90QWqM9a21jnkfZjwxq0odA=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit_Basics.framework/Headers/FBSDKCrashObserving.h</key>
		<dict>
			<key>hash</key>
			<data>
			UhJc2ZYtB2hcyXxk00KbwKbTgcc=
			</data>
			<key>hash2</key>
			<data>
			tO6F4Bbijxuct9m9xB8pcqmAlnyPPJ0EqbN+Z8/EM1w=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit_Basics.framework/Headers/FBSDKDataPersisting.h</key>
		<dict>
			<key>hash</key>
			<data>
			DtnVkIyBlZEE5hHK6RM8kIawq7w=
			</data>
			<key>hash2</key>
			<data>
			VcT2tlzFWSs7Ld7eUXcIezAttUSWdAIip7lZdNoZiGc=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit_Basics.framework/Headers/FBSDKFileDataExtracting.h</key>
		<dict>
			<key>hash</key>
			<data>
			BQDcfAulsMenmeoahsXxQgOwOH8=
			</data>
			<key>hash2</key>
			<data>
			s/7qW6G7GCOOyCS4jkWz8szFjT6uujuGXGhTa5JF4G4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit_Basics.framework/Headers/FBSDKFileManaging.h</key>
		<dict>
			<key>hash</key>
			<data>
			p/4j5P/SU+cTiWeHYIULKGdiaEg=
			</data>
			<key>hash2</key>
			<data>
			6+3pxgKpUWQwvlj3RwBFPC0rr5+wK651qUwh0n4fPHs=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit_Basics.framework/Headers/FBSDKInfoDictionaryProviding.h</key>
		<dict>
			<key>hash</key>
			<data>
			okxIBK/wxL+ugvqad38fKg7+VyU=
			</data>
			<key>hash2</key>
			<data>
			udZpX0hlPOkgV99Ck52TrAmlNiyK4r2qtK+kyPsJl8M=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit_Basics.framework/Headers/FBSDKLibAnalyzer.h</key>
		<dict>
			<key>hash</key>
			<data>
			Sr7GpMqIG4HmNNhoU6xSJrR36F8=
			</data>
			<key>hash2</key>
			<data>
			vcTMWhsIb/z29oxTpsnEfTWCnlgLS7qlPQuTTREW6Ek=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit_Basics.framework/Headers/FBSDKLinking.h</key>
		<dict>
			<key>hash</key>
			<data>
			1OBirQhOiNPiwhFBBuCxquzs+8o=
			</data>
			<key>hash2</key>
			<data>
			5NmQW3WO6TuTqGtMxocXiOeGiV7p3hyX2Saia2UhhL4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit_Basics.framework/Headers/FBSDKNetworkTask.h</key>
		<dict>
			<key>hash</key>
			<data>
			0sGmRF6Hdq6tvoAuo8957/Esjbc=
			</data>
			<key>hash2</key>
			<data>
			WXUdURA38eU1UT+VzBqNd3NJZ/tMnqMgcYFfskguL9w=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit_Basics.framework/Headers/FBSDKNotificationDelivering.h</key>
		<dict>
			<key>hash</key>
			<data>
			H1EtkXfryfCD6P8NlmWazMezAP0=
			</data>
			<key>hash2</key>
			<data>
			jCbQRKCjKWBaILGHzII7b5E1nAY37YAd0KWD5lhnyBM=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit_Basics.framework/Headers/FBSDKTypeUtility.h</key>
		<dict>
			<key>hash</key>
			<data>
			qgaA6wWpYIa+hM+VZSTB1P0w8Jk=
			</data>
			<key>hash2</key>
			<data>
			Xm2cMC3iy1kvQn8EXXkZ0eKLNRVCmSlSbefuvvTCTTs=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit_Basics.framework/Headers/FBSDKURLSession.h</key>
		<dict>
			<key>hash</key>
			<data>
			lNyxKy0KC2f37l17PcuP0Gv/EDA=
			</data>
			<key>hash2</key>
			<data>
			5L3MvrHaYDNtK2zrxdEtZNCRnhTQHNM0bvoh0pAfKg0=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit_Basics.framework/Headers/FBSDKURLSessionProviding.h</key>
		<dict>
			<key>hash</key>
			<data>
			1wsWv3wp81s7ODMRhEscWZBDXkc=
			</data>
			<key>hash2</key>
			<data>
			O0czQhC3wEX5AKGOHblqZYeevvp4+w3IVFYRSr4Uv9o=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit_Basics.framework/Headers/FBSDKURLSessionTask.h</key>
		<dict>
			<key>hash</key>
			<data>
			aOm3T7/JlTQP0GIRlnLQzT+NKc8=
			</data>
			<key>hash2</key>
			<data>
			3Efn6F2Y10Aul+I89unuMGdO1GJTM5xkZOglY+a5XtE=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit_Basics.framework/Headers/NSNotificationCenter+NotificationDelivering.h</key>
		<dict>
			<key>hash</key>
			<data>
			7k8yl29uNS0GjwEBkfViBHp6lN0=
			</data>
			<key>hash2</key>
			<data>
			ODQiUcnulvLSI4AHqIqtQvUPeAp+/9qi2eIHXnVKxh8=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit_Basics.framework/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			lvmc2jbr0W9Po7R51UBu3hiuJDU=
			</data>
			<key>hash2</key>
			<data>
			YUMwIpEtzn/OMTXF1FEPCcCfaMKAJqd0xR+YBCuKyHM=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit_Basics.framework/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			dAKyD+E6uMaCMsZEO7c47okujEM=
			</data>
			<key>hash2</key>
			<data>
			Ax5EJgz2ae9iwvEaJiJhRzHL4ePWe4qZhIaEdKJk27I=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit_Basics.framework/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash</key>
			<data>
			OyS/P0To2cmktqAfP4sDQxHrwXo=
			</data>
			<key>hash2</key>
			<data>
			WsyF5Q/iCf15mIOe/RlsYmYVYjlwU787E0oxD4TOhVs=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FBSDKCoreKit_Basics.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash</key>
			<data>
			y/TELoaijTVbg3Vu9vcVdns7+mk=
			</data>
			<key>hash2</key>
			<data>
			/DPdB6kke146lugMq0KkqwjmXM8QXuTdUWvUfhdNo+E=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/dSYMs/FBSDKCoreKit_Basics.framework.dSYM/Contents/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			8u6APwbQM/MbAsvk0wBXG3E35LI=
			</data>
			<key>hash2</key>
			<data>
			9RIqGM9whfQTtWCpiDKDvp0WIM4mLIofDOYs23hqeNE=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/dSYMs/FBSDKCoreKit_Basics.framework.dSYM/Contents/Resources/DWARF/FBSDKCoreKit_Basics</key>
		<dict>
			<key>hash</key>
			<data>
			dgupeYokz8yTm4ZVua3D6ne7ggc=
			</data>
			<key>hash2</key>
			<data>
			6hfKDeFDaYq+HurOm23b2n78c1aWWhukoICjgrfOZQ0=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/dSYMs/FBSDKCoreKit_Basics.framework.dSYM/Contents/Resources/Relocations/aarch64/FBSDKCoreKit_Basics.yml</key>
		<dict>
			<key>hash</key>
			<data>
			F5Cs5Z1ttauxw0pvEfG4dV23ojA=
			</data>
			<key>hash2</key>
			<data>
			k32gUCEBw5MHLDl7xG+KIhcmGDMST2btV995tw3eKOc=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/dSYMs/FBSDKCoreKit_Basics.framework.dSYM/Contents/Resources/Relocations/x86_64/FBSDKCoreKit_Basics.yml</key>
		<dict>
			<key>hash</key>
			<data>
			rrjkE4FM//GiH1XPBo4nM8xZIKo=
			</data>
			<key>hash2</key>
			<data>
			Vz1HaFEJpPumKjFjem7UMuCm9Tax9a0Qk5eLxjnWnck=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
