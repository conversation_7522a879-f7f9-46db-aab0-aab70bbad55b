<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Resources/Info.plist</key>
		<data>
		7ODZZUF3FBaHuREIEAcvVk03r1E=
		</data>
		<key>Resources/PrivacyInfo.xcprivacy</key>
		<data>
		OyS/P0To2cmktqAfP4sDQxHrwXo=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/FBSDKBase64.h</key>
		<dict>
			<key>hash2</key>
			<data>
			ePF4YsPJC4jFg1uvNt6fb83WN1IDTijvBhGt+V0Rh/8=
			</data>
		</dict>
		<key>Headers/FBSDKBasicUtility.h</key>
		<dict>
			<key>hash2</key>
			<data>
			4HOJGD9dL2BJIlaj/ej0J6MTNqBvYgF0/XrWiZtsT3E=
			</data>
		</dict>
		<key>Headers/FBSDKCoreKit_Basics.h</key>
		<dict>
			<key>hash2</key>
			<data>
			n96Ist8MxHyeU+TJA3OhwPRry3QcVtumXYCUmhcHycw=
			</data>
		</dict>
		<key>Headers/FBSDKCrashHandler.h</key>
		<dict>
			<key>hash2</key>
			<data>
			3ILHiPoEMzce96plPruxswh7D9L+ptDaLZH98MYQ5rY=
			</data>
		</dict>
		<key>Headers/FBSDKCrashHandlerProtocol.h</key>
		<dict>
			<key>hash2</key>
			<data>
			qZ25WCONiSxncOC5vN2J90QWqM9a21jnkfZjwxq0odA=
			</data>
		</dict>
		<key>Headers/FBSDKCrashObserving.h</key>
		<dict>
			<key>hash2</key>
			<data>
			tO6F4Bbijxuct9m9xB8pcqmAlnyPPJ0EqbN+Z8/EM1w=
			</data>
		</dict>
		<key>Headers/FBSDKDataPersisting.h</key>
		<dict>
			<key>hash2</key>
			<data>
			VcT2tlzFWSs7Ld7eUXcIezAttUSWdAIip7lZdNoZiGc=
			</data>
		</dict>
		<key>Headers/FBSDKFileDataExtracting.h</key>
		<dict>
			<key>hash2</key>
			<data>
			s/7qW6G7GCOOyCS4jkWz8szFjT6uujuGXGhTa5JF4G4=
			</data>
		</dict>
		<key>Headers/FBSDKFileManaging.h</key>
		<dict>
			<key>hash2</key>
			<data>
			6+3pxgKpUWQwvlj3RwBFPC0rr5+wK651qUwh0n4fPHs=
			</data>
		</dict>
		<key>Headers/FBSDKInfoDictionaryProviding.h</key>
		<dict>
			<key>hash2</key>
			<data>
			udZpX0hlPOkgV99Ck52TrAmlNiyK4r2qtK+kyPsJl8M=
			</data>
		</dict>
		<key>Headers/FBSDKLibAnalyzer.h</key>
		<dict>
			<key>hash2</key>
			<data>
			vcTMWhsIb/z29oxTpsnEfTWCnlgLS7qlPQuTTREW6Ek=
			</data>
		</dict>
		<key>Headers/FBSDKLinking.h</key>
		<dict>
			<key>hash2</key>
			<data>
			5NmQW3WO6TuTqGtMxocXiOeGiV7p3hyX2Saia2UhhL4=
			</data>
		</dict>
		<key>Headers/FBSDKNetworkTask.h</key>
		<dict>
			<key>hash2</key>
			<data>
			WXUdURA38eU1UT+VzBqNd3NJZ/tMnqMgcYFfskguL9w=
			</data>
		</dict>
		<key>Headers/FBSDKNotificationDelivering.h</key>
		<dict>
			<key>hash2</key>
			<data>
			jCbQRKCjKWBaILGHzII7b5E1nAY37YAd0KWD5lhnyBM=
			</data>
		</dict>
		<key>Headers/FBSDKTypeUtility.h</key>
		<dict>
			<key>hash2</key>
			<data>
			Xm2cMC3iy1kvQn8EXXkZ0eKLNRVCmSlSbefuvvTCTTs=
			</data>
		</dict>
		<key>Headers/FBSDKURLSession.h</key>
		<dict>
			<key>hash2</key>
			<data>
			5L3MvrHaYDNtK2zrxdEtZNCRnhTQHNM0bvoh0pAfKg0=
			</data>
		</dict>
		<key>Headers/FBSDKURLSessionProviding.h</key>
		<dict>
			<key>hash2</key>
			<data>
			O0czQhC3wEX5AKGOHblqZYeevvp4+w3IVFYRSr4Uv9o=
			</data>
		</dict>
		<key>Headers/FBSDKURLSessionTask.h</key>
		<dict>
			<key>hash2</key>
			<data>
			3Efn6F2Y10Aul+I89unuMGdO1GJTM5xkZOglY+a5XtE=
			</data>
		</dict>
		<key>Headers/NSNotificationCenter+NotificationDelivering.h</key>
		<dict>
			<key>hash2</key>
			<data>
			ODQiUcnulvLSI4AHqIqtQvUPeAp+/9qi2eIHXnVKxh8=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash2</key>
			<data>
			Ax5EJgz2ae9iwvEaJiJhRzHL4ePWe4qZhIaEdKJk27I=
			</data>
		</dict>
		<key>Resources/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			ZTTz6FOs1CbxqvEa0w9zU6agEEbmvL8BYZyAlQxxxck=
			</data>
		</dict>
		<key>Resources/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			WsyF5Q/iCf15mIOe/RlsYmYVYjlwU787E0oxD4TOhVs=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^Resources/</key>
		<true/>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^(Frameworks|SharedFrameworks|PlugIns|Plug-ins|XPCServices|Helpers|MacOS|Library/(Automator|Spotlight|LoginItems))/</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^[^/]+$</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
