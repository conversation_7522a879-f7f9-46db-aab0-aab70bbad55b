// Copyright 2022 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#import <Foundation/Foundation.h>

#import "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORMetricsControllerProtocol.h"

@protocol GDTCORStoragePromiseProtocol;

NS_ASSUME_NONNULL_BEGIN

@interface GDTCORMetricsController : NSObject <GDTCORMetricsControllerProtocol>

/// Returns the event metrics controller singleton.
+ (instancetype)sharedInstance;

/// Designated initializer.
/// @param storage The storage object to read and write metrics data from.
- (instancetype)initWithStorage:(id<GDTCORStoragePromiseProtocol>)storage NS_DESIGNATED_INITIALIZER;

/// This API is unavailable.
- (instancetype)init NS_UNAVAILABLE;

@end

NS_ASSUME_NONNULL_END
