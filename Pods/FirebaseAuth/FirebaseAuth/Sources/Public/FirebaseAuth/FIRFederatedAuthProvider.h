/*
 * Copyright 2017 Google
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#import <Foundation/Foundation.h>

@class FIRAuthCredential;

NS_ASSUME_NONNULL_BEGIN

/** @typedef FIRAuthCredentialCallback
    @brief The type of block invoked when obtaining an auth credential.
    @param credential The credential obtained.
    @param error The error that occurred if any.
 */
typedef void (^FIRAuthCredentialCallback)(FIRAuthCredential *_Nullable credential,
                                          NSError *_Nullable error)
    NS_SWIFT_UNAVAILABLE("Use <PERSON>'s closure syntax instead.");

NS_ASSUME_NONNULL_END
