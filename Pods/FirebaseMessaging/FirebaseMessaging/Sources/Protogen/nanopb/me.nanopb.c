/*
 * Copyright 2022 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/* Automatically generated nanopb constant definitions */
/* Generated by nanopb-0.3.9.9 */

#include "FirebaseMessaging/Sources/Protogen/nanopb/me.nanopb.h"

/* @@protoc_insertion_point(includes) */
#if PB_PROTO_HEADER_VERSION != 30
#error Regenerate this file with the current version of nanopb generator.
#endif



const pb_field_t fm_MessagingClientEvent_fields[11] = {
    PB_FIELD(  1, INT64   , SINGULAR, STATIC  , FIRST, fm_MessagingClientEvent, project_number, project_number, 0),
    PB_FIELD(  2, BYTES   , SINGULAR, POINTER , OTHER, fm_MessagingClientEvent, message_id, project_number, 0),
    PB_FIELD(  3, BYTES   , SINGULAR, POINTER , OTHER, fm_MessagingClientEvent, instance_id, message_id, 0),
    PB_FIELD(  4, UENUM   , SINGULAR, STATIC  , OTHER, fm_MessagingClientEvent, message_type, instance_id, 0),
    PB_FIELD(  5, UENUM   , SINGULAR, STATIC  , OTHER, fm_MessagingClientEvent, sdk_platform, message_type, 0),
    PB_FIELD(  6, BYTES   , SINGULAR, POINTER , OTHER, fm_MessagingClientEvent, package_name, sdk_platform, 0),
    PB_FIELD( 12, UENUM   , SINGULAR, STATIC  , OTHER, fm_MessagingClientEvent, event, package_name, 0),
    PB_FIELD( 13, BYTES   , SINGULAR, POINTER , OTHER, fm_MessagingClientEvent, analytics_label, event, 0),
    PB_FIELD( 14, INT64   , SINGULAR, STATIC  , OTHER, fm_MessagingClientEvent, campaign_id, analytics_label, 0),
    PB_FIELD( 15, BYTES   , SINGULAR, POINTER , OTHER, fm_MessagingClientEvent, composer_label, campaign_id, 0),
    PB_LAST_FIELD
};

const pb_field_t fm_MessagingClientEventExtension_fields[2] = {
    PB_FIELD(  1, MESSAGE , SINGULAR, POINTER , FIRST, fm_MessagingClientEventExtension, messaging_client_event, messaging_client_event, &fm_MessagingClientEvent_fields),
    PB_LAST_FIELD
};





/* @@protoc_insertion_point(eof) */
