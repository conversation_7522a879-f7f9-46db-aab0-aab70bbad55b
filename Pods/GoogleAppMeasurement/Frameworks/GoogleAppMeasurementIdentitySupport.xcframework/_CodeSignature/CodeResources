<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>ios-arm64/GoogleAppMeasurementIdentitySupport.framework/GoogleAppMeasurementIdentitySupport</key>
		<data>
		KXJZtjKaR8+YFsmFIJgmCBAi61k=
		</data>
		<key>ios-arm64/GoogleAppMeasurementIdentitySupport.framework/Info.plist</key>
		<data>
		fj1aEQuyhDwGv/WF74p6q70sxCc=
		</data>
		<key>ios-arm64/GoogleAppMeasurementIdentitySupport.framework/Modules/module.modulemap</key>
		<data>
		ZCuUZzVZ47W3gsydCwK/7TEMjVw=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/GoogleAppMeasurementIdentitySupport.framework/Versions/A/GoogleAppMeasurementIdentitySupport</key>
		<data>
		qjEEWBYUsYmWP/dGYGqk6H0XtCc=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/GoogleAppMeasurementIdentitySupport.framework/Versions/A/Modules/module.modulemap</key>
		<data>
		ZCuUZzVZ47W3gsydCwK/7TEMjVw=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/GoogleAppMeasurementIdentitySupport.framework/Versions/A/Resources/Info.plist</key>
		<data>
		4w7ZZN02ldypYlogPRfzjvZ6sGQ=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleAppMeasurementIdentitySupport.framework/GoogleAppMeasurementIdentitySupport</key>
		<data>
		Y0csxyURm/Vw+EjN6Vs8F4OYyD0=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleAppMeasurementIdentitySupport.framework/Info.plist</key>
		<data>
		mfY6DQXP3dOX3W3PiF5qyUrK9kI=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleAppMeasurementIdentitySupport.framework/Modules/module.modulemap</key>
		<data>
		ZCuUZzVZ47W3gsydCwK/7TEMjVw=
		</data>
		<key>macos-arm64_x86_64/GoogleAppMeasurementIdentitySupport.framework/Versions/A/GoogleAppMeasurementIdentitySupport</key>
		<data>
		SGutHPksaT+14CgfJDiEGr9l0IA=
		</data>
		<key>macos-arm64_x86_64/GoogleAppMeasurementIdentitySupport.framework/Versions/A/Modules/module.modulemap</key>
		<data>
		ZCuUZzVZ47W3gsydCwK/7TEMjVw=
		</data>
		<key>macos-arm64_x86_64/GoogleAppMeasurementIdentitySupport.framework/Versions/A/Resources/Info.plist</key>
		<data>
		m7cWZduxfnTYJxTlDBim5LALViA=
		</data>
		<key>tvos-arm64/GoogleAppMeasurementIdentitySupport.framework/GoogleAppMeasurementIdentitySupport</key>
		<data>
		faJon13KEa7exKXSm0k3ONIu1BM=
		</data>
		<key>tvos-arm64/GoogleAppMeasurementIdentitySupport.framework/Info.plist</key>
		<data>
		JkqjOPcsblSN7qvAOxG3mUNy/Jo=
		</data>
		<key>tvos-arm64/GoogleAppMeasurementIdentitySupport.framework/Modules/module.modulemap</key>
		<data>
		ZCuUZzVZ47W3gsydCwK/7TEMjVw=
		</data>
		<key>tvos-arm64_x86_64-simulator/GoogleAppMeasurementIdentitySupport.framework/GoogleAppMeasurementIdentitySupport</key>
		<data>
		RoKwMHmW5ksPuLfswh3w2eHBEU4=
		</data>
		<key>tvos-arm64_x86_64-simulator/GoogleAppMeasurementIdentitySupport.framework/Info.plist</key>
		<data>
		2Ar+emR67oINTNDmtL2omd9eW3k=
		</data>
		<key>tvos-arm64_x86_64-simulator/GoogleAppMeasurementIdentitySupport.framework/Modules/module.modulemap</key>
		<data>
		ZCuUZzVZ47W3gsydCwK/7TEMjVw=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>ios-arm64/GoogleAppMeasurementIdentitySupport.framework/GoogleAppMeasurementIdentitySupport</key>
		<dict>
			<key>hash</key>
			<data>
			KXJZtjKaR8+YFsmFIJgmCBAi61k=
			</data>
			<key>hash2</key>
			<data>
			SSBwUA5LG1rznKuV90TjTTBKvsrPOTlLIXroVyKCCso=
			</data>
		</dict>
		<key>ios-arm64/GoogleAppMeasurementIdentitySupport.framework/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			fj1aEQuyhDwGv/WF74p6q70sxCc=
			</data>
			<key>hash2</key>
			<data>
			rBrrvNSz1OmBNSUUv5FrQ53st98UvJ48c0CloB3/ysw=
			</data>
		</dict>
		<key>ios-arm64/GoogleAppMeasurementIdentitySupport.framework/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			ZCuUZzVZ47W3gsydCwK/7TEMjVw=
			</data>
			<key>hash2</key>
			<data>
			UGhX/JwInh1Ja0+DYSnYvdaBKLCWcOy/3nH/ZRnTP7I=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/GoogleAppMeasurementIdentitySupport.framework/GoogleAppMeasurementIdentitySupport</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/GoogleAppMeasurementIdentitySupport</string>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/GoogleAppMeasurementIdentitySupport.framework/Modules</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/Modules</string>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/GoogleAppMeasurementIdentitySupport.framework/Resources</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/Resources</string>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/GoogleAppMeasurementIdentitySupport.framework/Versions/A/GoogleAppMeasurementIdentitySupport</key>
		<dict>
			<key>hash</key>
			<data>
			qjEEWBYUsYmWP/dGYGqk6H0XtCc=
			</data>
			<key>hash2</key>
			<data>
			y0WmxTyqtT5MXs2kylo+inVKWcvbAZgu72qvvDs8gDU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/GoogleAppMeasurementIdentitySupport.framework/Versions/A/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			ZCuUZzVZ47W3gsydCwK/7TEMjVw=
			</data>
			<key>hash2</key>
			<data>
			UGhX/JwInh1Ja0+DYSnYvdaBKLCWcOy/3nH/ZRnTP7I=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/GoogleAppMeasurementIdentitySupport.framework/Versions/A/Resources/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			4w7ZZN02ldypYlogPRfzjvZ6sGQ=
			</data>
			<key>hash2</key>
			<data>
			zNUL4kfQTZZOp8qXFVovcngjxbKFxCEzxjSZmX/V8fI=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/GoogleAppMeasurementIdentitySupport.framework/Versions/Current</key>
		<dict>
			<key>symlink</key>
			<string>A</string>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleAppMeasurementIdentitySupport.framework/GoogleAppMeasurementIdentitySupport</key>
		<dict>
			<key>hash</key>
			<data>
			Y0csxyURm/Vw+EjN6Vs8F4OYyD0=
			</data>
			<key>hash2</key>
			<data>
			v0KVW0rp5DREsHYrxC2KN52zrBir04ScktCmxbBeJ9Y=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleAppMeasurementIdentitySupport.framework/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			mfY6DQXP3dOX3W3PiF5qyUrK9kI=
			</data>
			<key>hash2</key>
			<data>
			UgOwFuG38NwHVEWRPMYb5v4149os/ah0ybL09I4JnLY=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleAppMeasurementIdentitySupport.framework/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			ZCuUZzVZ47W3gsydCwK/7TEMjVw=
			</data>
			<key>hash2</key>
			<data>
			UGhX/JwInh1Ja0+DYSnYvdaBKLCWcOy/3nH/ZRnTP7I=
			</data>
		</dict>
		<key>macos-arm64_x86_64/GoogleAppMeasurementIdentitySupport.framework/GoogleAppMeasurementIdentitySupport</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/GoogleAppMeasurementIdentitySupport</string>
		</dict>
		<key>macos-arm64_x86_64/GoogleAppMeasurementIdentitySupport.framework/Modules</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/Modules</string>
		</dict>
		<key>macos-arm64_x86_64/GoogleAppMeasurementIdentitySupport.framework/Resources</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/Resources</string>
		</dict>
		<key>macos-arm64_x86_64/GoogleAppMeasurementIdentitySupport.framework/Versions/A/GoogleAppMeasurementIdentitySupport</key>
		<dict>
			<key>hash</key>
			<data>
			SGutHPksaT+14CgfJDiEGr9l0IA=
			</data>
			<key>hash2</key>
			<data>
			3/KTDnax/a4lkIpOY51sPDPXrgIjkWqjC8Mo99OjoOI=
			</data>
		</dict>
		<key>macos-arm64_x86_64/GoogleAppMeasurementIdentitySupport.framework/Versions/A/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			ZCuUZzVZ47W3gsydCwK/7TEMjVw=
			</data>
			<key>hash2</key>
			<data>
			UGhX/JwInh1Ja0+DYSnYvdaBKLCWcOy/3nH/ZRnTP7I=
			</data>
		</dict>
		<key>macos-arm64_x86_64/GoogleAppMeasurementIdentitySupport.framework/Versions/A/Resources/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			m7cWZduxfnTYJxTlDBim5LALViA=
			</data>
			<key>hash2</key>
			<data>
			i3AZrQxVi9A15l6bnbzv4VD+VvtvWV9zWdZEB8q2Wu0=
			</data>
		</dict>
		<key>macos-arm64_x86_64/GoogleAppMeasurementIdentitySupport.framework/Versions/Current</key>
		<dict>
			<key>symlink</key>
			<string>A</string>
		</dict>
		<key>tvos-arm64/GoogleAppMeasurementIdentitySupport.framework/GoogleAppMeasurementIdentitySupport</key>
		<dict>
			<key>hash</key>
			<data>
			faJon13KEa7exKXSm0k3ONIu1BM=
			</data>
			<key>hash2</key>
			<data>
			pZRyALHO66+MYJ/QmPT1mnN13JC1vKURRe/N0KZ/yNs=
			</data>
		</dict>
		<key>tvos-arm64/GoogleAppMeasurementIdentitySupport.framework/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			JkqjOPcsblSN7qvAOxG3mUNy/Jo=
			</data>
			<key>hash2</key>
			<data>
			4CMorB6K34sO9IiRwqFj+tHPoHadGMwt7Vc4owEjQ5Q=
			</data>
		</dict>
		<key>tvos-arm64/GoogleAppMeasurementIdentitySupport.framework/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			ZCuUZzVZ47W3gsydCwK/7TEMjVw=
			</data>
			<key>hash2</key>
			<data>
			UGhX/JwInh1Ja0+DYSnYvdaBKLCWcOy/3nH/ZRnTP7I=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/GoogleAppMeasurementIdentitySupport.framework/GoogleAppMeasurementIdentitySupport</key>
		<dict>
			<key>hash</key>
			<data>
			RoKwMHmW5ksPuLfswh3w2eHBEU4=
			</data>
			<key>hash2</key>
			<data>
			jPxlTfTZN9FNClyN7c/JaqNjogJLtzvqoVLKw60g11o=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/GoogleAppMeasurementIdentitySupport.framework/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			2Ar+emR67oINTNDmtL2omd9eW3k=
			</data>
			<key>hash2</key>
			<data>
			QYupJjtFdSYajwRad4l5fUYuR7u0oNPaWZNGEtdY5gY=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/GoogleAppMeasurementIdentitySupport.framework/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			ZCuUZzVZ47W3gsydCwK/7TEMjVw=
			</data>
			<key>hash2</key>
			<data>
			UGhX/JwInh1Ja0+DYSnYvdaBKLCWcOy/3nH/ZRnTP7I=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
