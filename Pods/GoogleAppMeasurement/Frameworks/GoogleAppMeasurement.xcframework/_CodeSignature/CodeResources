<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>ios-arm64/GoogleAppMeasurement.framework/GoogleAppMeasurement</key>
		<data>
		RhwOnDgavPI0zzRA0WB6EWR67xE=
		</data>
		<key>ios-arm64/GoogleAppMeasurement.framework/Info.plist</key>
		<data>
		TohHMyxskUGDOjkXbJi0itb5D9U=
		</data>
		<key>ios-arm64/GoogleAppMeasurement.framework/Modules/module.modulemap</key>
		<data>
		on1mkulwTtm+ufPJ4eClavLWAuQ=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/GoogleAppMeasurement.framework/Versions/A/GoogleAppMeasurement</key>
		<data>
		wdhEOCyyKr4lZWM3dxKCl8QfRA4=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/GoogleAppMeasurement.framework/Versions/A/Modules/module.modulemap</key>
		<data>
		on1mkulwTtm+ufPJ4eClavLWAuQ=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/GoogleAppMeasurement.framework/Versions/A/Resources/Info.plist</key>
		<data>
		z1EuM9KThmsjsoFiyOvRpDiVtqE=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleAppMeasurement.framework/GoogleAppMeasurement</key>
		<data>
		c2UR8YXU8HyZa+0RkRxLqmrdzJw=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleAppMeasurement.framework/Info.plist</key>
		<data>
		/M81j4oqJeDqrhP9iEqnTal1ihM=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleAppMeasurement.framework/Modules/module.modulemap</key>
		<data>
		on1mkulwTtm+ufPJ4eClavLWAuQ=
		</data>
		<key>macos-arm64_x86_64/GoogleAppMeasurement.framework/Versions/A/GoogleAppMeasurement</key>
		<data>
		4i2iwJQn8lCCe4+SXuHYN5bW1fs=
		</data>
		<key>macos-arm64_x86_64/GoogleAppMeasurement.framework/Versions/A/Modules/module.modulemap</key>
		<data>
		on1mkulwTtm+ufPJ4eClavLWAuQ=
		</data>
		<key>macos-arm64_x86_64/GoogleAppMeasurement.framework/Versions/A/Resources/Info.plist</key>
		<data>
		EIdUhDAwIJU2sWgyuI0ahJQMp6U=
		</data>
		<key>tvos-arm64/GoogleAppMeasurement.framework/GoogleAppMeasurement</key>
		<data>
		QWUAHXGhl0l1xRtTRylpXYpa34Q=
		</data>
		<key>tvos-arm64/GoogleAppMeasurement.framework/Info.plist</key>
		<data>
		joXl/6bsmOnp1E9e9aW+BX/rhMY=
		</data>
		<key>tvos-arm64/GoogleAppMeasurement.framework/Modules/module.modulemap</key>
		<data>
		on1mkulwTtm+ufPJ4eClavLWAuQ=
		</data>
		<key>tvos-arm64_x86_64-simulator/GoogleAppMeasurement.framework/GoogleAppMeasurement</key>
		<data>
		KrQJsA2fmpy5usLPXaZD8W8gsvU=
		</data>
		<key>tvos-arm64_x86_64-simulator/GoogleAppMeasurement.framework/Info.plist</key>
		<data>
		M8duF9DAJJ//vbRxTm/6UMQcZyo=
		</data>
		<key>tvos-arm64_x86_64-simulator/GoogleAppMeasurement.framework/Modules/module.modulemap</key>
		<data>
		on1mkulwTtm+ufPJ4eClavLWAuQ=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>ios-arm64/GoogleAppMeasurement.framework/GoogleAppMeasurement</key>
		<dict>
			<key>hash</key>
			<data>
			RhwOnDgavPI0zzRA0WB6EWR67xE=
			</data>
			<key>hash2</key>
			<data>
			zYeOjtixNWS5bpe7xjl/ytCdbt+/ZYidvHsnqaGTkMs=
			</data>
		</dict>
		<key>ios-arm64/GoogleAppMeasurement.framework/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			TohHMyxskUGDOjkXbJi0itb5D9U=
			</data>
			<key>hash2</key>
			<data>
			REaKna8zIK92XFv2PJlt+jA2tK44gO9c8n/udDmpZ0Q=
			</data>
		</dict>
		<key>ios-arm64/GoogleAppMeasurement.framework/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			on1mkulwTtm+ufPJ4eClavLWAuQ=
			</data>
			<key>hash2</key>
			<data>
			gd8e5hMrJihnXu8TQ0xecVPl3Z71GpsKaYxiLbmHezw=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/GoogleAppMeasurement.framework/GoogleAppMeasurement</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/GoogleAppMeasurement</string>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/GoogleAppMeasurement.framework/Modules</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/Modules</string>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/GoogleAppMeasurement.framework/Resources</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/Resources</string>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/GoogleAppMeasurement.framework/Versions/A/GoogleAppMeasurement</key>
		<dict>
			<key>hash</key>
			<data>
			wdhEOCyyKr4lZWM3dxKCl8QfRA4=
			</data>
			<key>hash2</key>
			<data>
			wC1JQnsxDHo+Kzu4wkghxKqzKRnhrHrAFNGPRjeDF8E=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/GoogleAppMeasurement.framework/Versions/A/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			on1mkulwTtm+ufPJ4eClavLWAuQ=
			</data>
			<key>hash2</key>
			<data>
			gd8e5hMrJihnXu8TQ0xecVPl3Z71GpsKaYxiLbmHezw=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/GoogleAppMeasurement.framework/Versions/A/Resources/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			z1EuM9KThmsjsoFiyOvRpDiVtqE=
			</data>
			<key>hash2</key>
			<data>
			0h3aZwqICpfwliOo8JosbEUX48v4TfEgLpdTaaZPAXk=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/GoogleAppMeasurement.framework/Versions/Current</key>
		<dict>
			<key>symlink</key>
			<string>A</string>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleAppMeasurement.framework/GoogleAppMeasurement</key>
		<dict>
			<key>hash</key>
			<data>
			c2UR8YXU8HyZa+0RkRxLqmrdzJw=
			</data>
			<key>hash2</key>
			<data>
			ty8sBd/t7Qi6xWfk3vbFmo+EosuPqE+0q77XONgbdWs=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleAppMeasurement.framework/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			/M81j4oqJeDqrhP9iEqnTal1ihM=
			</data>
			<key>hash2</key>
			<data>
			unKbLbtCTs9txHR0V1s1plgcGXLCCtWn88KI4DV/DKs=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleAppMeasurement.framework/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			on1mkulwTtm+ufPJ4eClavLWAuQ=
			</data>
			<key>hash2</key>
			<data>
			gd8e5hMrJihnXu8TQ0xecVPl3Z71GpsKaYxiLbmHezw=
			</data>
		</dict>
		<key>macos-arm64_x86_64/GoogleAppMeasurement.framework/GoogleAppMeasurement</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/GoogleAppMeasurement</string>
		</dict>
		<key>macos-arm64_x86_64/GoogleAppMeasurement.framework/Modules</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/Modules</string>
		</dict>
		<key>macos-arm64_x86_64/GoogleAppMeasurement.framework/Resources</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/Resources</string>
		</dict>
		<key>macos-arm64_x86_64/GoogleAppMeasurement.framework/Versions/A/GoogleAppMeasurement</key>
		<dict>
			<key>hash</key>
			<data>
			4i2iwJQn8lCCe4+SXuHYN5bW1fs=
			</data>
			<key>hash2</key>
			<data>
			L/dl84bL/cLlnxW//k3SFtypAdc/OwYuiRWKLP4GX/0=
			</data>
		</dict>
		<key>macos-arm64_x86_64/GoogleAppMeasurement.framework/Versions/A/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			on1mkulwTtm+ufPJ4eClavLWAuQ=
			</data>
			<key>hash2</key>
			<data>
			gd8e5hMrJihnXu8TQ0xecVPl3Z71GpsKaYxiLbmHezw=
			</data>
		</dict>
		<key>macos-arm64_x86_64/GoogleAppMeasurement.framework/Versions/A/Resources/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			EIdUhDAwIJU2sWgyuI0ahJQMp6U=
			</data>
			<key>hash2</key>
			<data>
			vzMy1QnhlwBa6EHftCo6LSR8kHdARzWQneh6KZ975Mc=
			</data>
		</dict>
		<key>macos-arm64_x86_64/GoogleAppMeasurement.framework/Versions/Current</key>
		<dict>
			<key>symlink</key>
			<string>A</string>
		</dict>
		<key>tvos-arm64/GoogleAppMeasurement.framework/GoogleAppMeasurement</key>
		<dict>
			<key>hash</key>
			<data>
			QWUAHXGhl0l1xRtTRylpXYpa34Q=
			</data>
			<key>hash2</key>
			<data>
			sChbVVaQfLlS3eOUUi6oouL2/ezcnETulzHiRd2bjM4=
			</data>
		</dict>
		<key>tvos-arm64/GoogleAppMeasurement.framework/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			joXl/6bsmOnp1E9e9aW+BX/rhMY=
			</data>
			<key>hash2</key>
			<data>
			98drm5iKDtABG2vmZs1x1ILVi0pUDTTqEMXti1Pmnec=
			</data>
		</dict>
		<key>tvos-arm64/GoogleAppMeasurement.framework/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			on1mkulwTtm+ufPJ4eClavLWAuQ=
			</data>
			<key>hash2</key>
			<data>
			gd8e5hMrJihnXu8TQ0xecVPl3Z71GpsKaYxiLbmHezw=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/GoogleAppMeasurement.framework/GoogleAppMeasurement</key>
		<dict>
			<key>hash</key>
			<data>
			KrQJsA2fmpy5usLPXaZD8W8gsvU=
			</data>
			<key>hash2</key>
			<data>
			kV1x9fGtJdV9/k/53dwA7jqL35trIXl38N5fsRyZfcY=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/GoogleAppMeasurement.framework/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			M8duF9DAJJ//vbRxTm/6UMQcZyo=
			</data>
			<key>hash2</key>
			<data>
			piSEYn0SIJcbjzha9NYnSpPCYjmuIG43gXfGpP/FOu0=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/GoogleAppMeasurement.framework/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			on1mkulwTtm+ufPJ4eClavLWAuQ=
			</data>
			<key>hash2</key>
			<data>
			gd8e5hMrJihnXu8TQ0xecVPl3Z71GpsKaYxiLbmHezw=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
