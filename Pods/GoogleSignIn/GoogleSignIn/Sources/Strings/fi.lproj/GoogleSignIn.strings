/* Sign-in button text */
"Sign in" = "Kirjaudu sisään";

/* Long form sign-in button text */
"Sign in with Google" = "Kirjaudu Google-tilin tunnuksilla";

/* The text for the button for user to acknowledge and dismiss a dialog. */
"OK" = "OK";

/* The text for the button for user to dismiss a dialog without taking any action. */
"Cancel" = "Peruuta";

/* The name of the iOS native "Settings" app. */
"SettingsAppName" = "Asetukset";

/* The title for the error dialog for unable to sign in because of EMM policy. */
"EmmErrorTitle" = "Kirjautuminen tilille ei onnistu";

/* The text in the error dialog asking user to set up a passcode for the device due to EMM policy. */
"EmmPasscodeRequired" = "Järjestelmänvalvoja edellyttää tunnuskoodin määrittämistä, ennen kuin voit käyttää tiliä tällä laitteella. Määritä tunnuskoodi ja yritä uudelleen.";

/* The text in the error dialog informing user that EMM policy prevented sign-in on the device. */
"EmmGeneralError" = "Laite ei noudata järjestelmänvalvojan määrittämää verkkotunnuskäytäntöä.";

/* The title in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectTitle" = "Muodostetaanko yhteys Device Policy ‑sovellukseen?";

/* The text in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectText" = "Suojaa organisaatiosi dataa muodostamalla yhteys Device Policy ‑sovellukseen ennen kirjautumista.";

/* The action button label in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectLabel" = "Muodosta yhteys";
