/* Sign-in button text */
"Sign in" = "Увійти";

/* Long form sign-in button text */
"Sign in with Google" = "Увійти в обліковий запис Google";

/* The text for the button for user to acknowledge and dismiss a dialog. */
"OK" = "OK";

/* The text for the button for user to dismiss a dialog without taking any action. */
"Cancel" = "Скасувати";

/* The name of the iOS native "Settings" app. */
"SettingsAppName" = "Налаштування";

/* The title for the error dialog for unable to sign in because of EMM policy. */
"EmmErrorTitle" = "Не вдається ввійти в обліковий запис";

/* The text in the error dialog asking user to set up a passcode for the device due to EMM policy. */
"EmmPasscodeRequired" = "Щоб увійти в обліковий запис, потрібно налаштувати код доступу на пристрої. Зробіть це й повторіть спробу.";

/* The text in the error dialog informing user that EMM policy prevented sign-in on the device. */
"EmmGeneralError" = "Пристрій не відповідає правилу безпеки, яке налаштував адміністратор.";

/* The title in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectTitle" = "З’єднатися з додатком Device Policy?";

/* The text in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectText" = "Щоб захистити дані організації, потрібно з’єднатися з додатком Device Policy, перш ніж увійти.";

/* The action button label in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectLabel" = "З’єднатися";
