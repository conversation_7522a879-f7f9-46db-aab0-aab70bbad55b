/* Sign-in button text */
"Sign in" = "Inloggen";

/* Long form sign-in button text */
"Sign in with Google" = "Inloggen met Google";

/* The text for the button for user to acknowledge and dismiss a dialog. */
"OK" = "OK";

/* The text for the button for user to dismiss a dialog without taking any action. */
"Cancel" = "Annuleren";

/* The name of the iOS native "Settings" app. */
"SettingsAppName" = "Instellingen";

/* The title for the error dialog for unable to sign in because of EMM policy. */
"EmmErrorTitle" = "Kan niet inloggen op account";

/* The text in the error dialog asking user to set up a passcode for the device due to EMM policy. */
"EmmPasscodeRequired" = "Uw beheerder vereist dat u een toegangscode instelt op dit apparaat om toegang te krijgen tot dit account. Stel een toegangscode in en probeer het opnieuw.";

/* The text in the error dialog informing user that EMM policy prevented sign-in on the device. */
"EmmGeneralError" = "Het apparaat voldoet niet aan het beveiligingsbeleid dat is ingesteld door uw beheerder.";

/* The title in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectTitle" = "Verbinden met Device Policy-app?";

/* The text in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectText" = "Ter bescherming van de gegevens van uw organisatie moet u verbinding maken met de Device Policy-app voordat u inlogt.";

/* The action button label in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectLabel" = "Verbinden";
