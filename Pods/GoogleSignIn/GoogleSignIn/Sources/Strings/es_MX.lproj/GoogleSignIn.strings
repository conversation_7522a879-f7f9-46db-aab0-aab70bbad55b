/* Sign-in button text */
"Sign in" = "Acceder";

/* Long form sign-in button text */
"Sign in with Google" = "Acceder con Google";

/* The text for the button for user to acknowledge and dismiss a dialog. */
"OK" = "Aceptar";

/* The text for the button for user to dismiss a dialog without taking any action. */
"Cancel" = "Cancelar";

/* The name of the iOS native "Settings" app. */
"SettingsAppName" = "Configuración";

/* The title for the error dialog for unable to sign in because of EMM policy. */
"EmmErrorTitle" = "No es posible acceder a la cuenta";

/* The text in the error dialog asking user to set up a passcode for the device due to EMM policy. */
"EmmPasscodeRequired" = "Para acceder a esta cuenta, tu administrador requiere que establezcas una contraseña en el dispositivo. Configúrala y vuelve a intentarlo.";

/* The text in the error dialog informing user that EMM policy prevented sign-in on the device. */
"EmmGeneralError" = "El dispositivo no cumple con la política de seguridad que estableció el administrador.";

/* The title in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectTitle" = "¿Deseas conectarte con la app de Device Policy?";

/* The text in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectText" = "Para proteger los datos de tu organización, debes conectarte con la app de Device Policy antes de acceder.";

/* The action button label in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectLabel" = "Conectar";
