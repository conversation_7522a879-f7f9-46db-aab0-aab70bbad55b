/* Sign-in button text */
"Sign in" = "登入";

/* Long form sign-in button text */
"Sign in with Google" = "登入 Google 帳戶";

/* The text for the button for user to acknowledge and dismiss a dialog. */
"OK" = "確定";

/* The text for the button for user to dismiss a dialog without taking any action. */
"Cancel" = "取消";

/* The name of the iOS native "Settings" app. */
"SettingsAppName" = "設定";

/* The title for the error dialog for unable to sign in because of EMM policy. */
"EmmErrorTitle" = "無法登入帳戶";

/* The text in the error dialog asking user to set up a passcode for the device due to EMM policy. */
"EmmPasscodeRequired" = "管理員要求您必須為這個裝置設定通行碼，才能存取這個帳戶。請設定通行碼，然後再試一次。";

/* The text in the error dialog informing user that EMM policy prevented sign-in on the device. */
"EmmGeneralError" = "這部裝置不符合您的管理員所設定的安全性政策規定。";

/* The title in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectTitle" = "要連結 Device Policy 應用程式嗎？";

/* The text in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectText" = "為了保護貴機構的資料，您必須在登入前連結 Device Policy 應用程式。";

/* The action button label in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectLabel" = "連結";
