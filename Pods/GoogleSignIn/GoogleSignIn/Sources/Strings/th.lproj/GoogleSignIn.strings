/* Sign-in button text */
"Sign in" = "ลงชื่อเข้าใช้";

/* Long form sign-in button text */
"Sign in with Google" = "ลงชื่อเข้าใช้ด้วย Google";

/* The text for the button for user to acknowledge and dismiss a dialog. */
"OK" = "ตกลง";

/* The text for the button for user to dismiss a dialog without taking any action. */
"Cancel" = "ยกเลิก";

/* The name of the iOS native "Settings" app. */
"SettingsAppName" = "การตั้งค่า";

/* The title for the error dialog for unable to sign in because of EMM policy. */
"EmmErrorTitle" = "ลงชื่อเข้าใช้บัญชีไม่ได้";

/* The text in the error dialog asking user to set up a passcode for the device due to EMM policy. */
"EmmPasscodeRequired" = "ผู้ดูแลระบบกำหนดให้คุณตั้งรหัสผ่านในอุปกรณ์นี้เพื่อเข้าถึงบัญชีนี้ โปรดตั้งรหัสผ่าน แล้วลองอีกครั้ง";

/* The text in the error dialog informing user that EMM policy prevented sign-in on the device. */
"EmmGeneralError" = "อุปกรณ์ไม่ตรงตามนโยบายความปลอดภัยที่กำหนดโดยผู้ดูแลระบบของคุณ";

/* The title in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectTitle" = "เชื่อมต่อแอป Device Policy ไหม";

/* The text in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectText" = "เพื่อปกป้องข้อมูลขององค์กร คุณต้องเชื่อมต่อแอป Device Policy ก่อนลงชื่อเข้าสู่ระบบ";

/* The action button label in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectLabel" = "เชื่อมต่อ";
