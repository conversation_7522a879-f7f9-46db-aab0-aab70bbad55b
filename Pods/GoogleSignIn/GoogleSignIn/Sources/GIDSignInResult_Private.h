/*
 * Copyright 2022 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#import "GoogleSignIn/Sources/Public/GoogleSignIn/GIDSignInResult.h"

NS_ASSUME_NONNULL_BEGIN

// Private |GIDSignInResult| methods that are used in this SDK.
@interface GIDSignInResult ()

// Private initializer for |GIDSignInResult|.
// @param user The current GIDGoogleUser.
// @param severAuthCode The one-time authorization code for backend to exchange
//     access and refresh tokens.
- (instancetype)initWithGoogleUser:(GIDGoogleUser *)user
                    serverAuthCode:(nullable NSString *)serverAuthCode;

@end

NS_ASSUME_NONNULL_END
