/* Prompts a person to go to the URL listed to enter the confirmation code that is presented to them above the given string. */
"DeviceLogin.LogInPrompt" = "%@ adresini ziyaret edin ve yukarıda görülen kodu girin.";

/* Prompts a person that the next thing they need to do to finish connecting their Smart TV and Facebook application is to navigate to their Facebook application on their mobile device and look through their notifications for a message about the connection being formed */
"DeviceLogin.SmartLogInPrompt" = "Hesabınızı bağlamak için, mobil cihazınızda Facebook uygulamasını açın ve bildirimlerinizi kontrol edin.";

/* Displayed as a separator between two options. First option is on a line above this, and second option is below */
"DeviceLogin.SmartLogInOrLabel" = "- YA DA -";

/* The title of the label to dismiss the alert when presenting user facing error messages */
"ErrorRecovery.Alert.OK" = "Tamam";

/* The title of the label to decline attempting error recovery */
"ErrorRecovery.Cancel" = "İptal";

/* The fallback message to display to recover invalidated tokens */
"ErrorRecovery.Login.Suggestion" = "Facebook hesabına yeniden bağlanmak için lütfen bu uygulamaya tekrar giriş yap.";

/* The title of the label to start attempting error recovery */
"ErrorRecovery.OK" = "Tamam";

/* The fallback message to display to retry transient errors */
"ErrorRecovery.Transient.Suggestion" = "Sunucu geçici olarak meşgul. Lütfen tekrar dene.";

/* The label for the FBSDKLoginButton action sheet to cancel logging out */
"LoginButton.CancelLogout" = "İptal";

/* The label for the FBSDKLoginButton action sheet to confirm logging out */
"LoginButton.ConfirmLogOut" = "Çıkış Yap";

/* The fallback string for the FBSDKLoginButton label when the user name is not available yet */
"LoginButton.LoggedIn" = "Facebook ile giriş yapıldı";

/* The format string for the FBSDKLoginButton label when the user is logged in */
"LoginButton.LoggedInAs" = "%@ olarak giriş yapıldı";

/* The short label for the FBSDKLoginButton when the user is currently logged out */
"LoginButton.LogIn" = "Giriş yap";

/* The long label for the FBSDKLoginButton when the user is currently logged out */
"LoginButton.LogInContinue" = "Facebook ile Devam Et";

/* The long label for the FBSDKLoginButton when the user is currently logged out */
"LoginButton.LogInLong" = "Facebook ile giriş yap";

/* The label for the FBSDKLoginButton when the user is currently logged in */
"LoginButton.LogOut" = "Çıkış yap";

/* The user facing error message when the app slider has been disabled and login fails. */
"LoginError.SystemAccount.Disabled" = "Facebook hesabına erişim izni verilmedi. Cihaz ayarlarını doğrula.";

/* The user facing error message when the Accounts framework encounters a network error. */
"LoginError.SystemAccount.Network" = "Facebook'a bağlanılamıyor. Ağ bağlantını kontrol edip tekrar dene.";

/* The user facing error message when the device Facebook account password is incorrect and login fails. */
"LoginError.SystemAccount.PasswordChange" = "Facebook şifren değiştirildi. Şifreni onaylamak için Ayarlar &gt; Facebook'u aç ve adına dokunun.";

/* The user facing error message when the device Facebook account is unavailable and login fails. */
"LoginError.SystemAccount.Unavailable" = "Facebook hesabı cihaz üzerinde yapılandırılmadı.";

/* The user facing error message when the Facebook account signed in to the Accounts framework becomes unconfirmed. */
"LoginError.SystemAccount.UnconfirmedUser" = "Hesabın onaylanmadı. Lütfen www.facebook.com adresine giriş yap ve verilen talimatları izle.";

/* The user facing error message when the Facebook account signed in to the Accounts framework has been checkpointed. */
"LoginError.SystemAccount.UserCheckpointed" = "Şu an için uygulamalara giriş yapamazsın. Lütfen www.facebook.com adresine giriş yap ve verilen talimatları izle.";

/* The message of the FBSDKLoginTooltipView */
"LoginTooltip.Message" = "Kontrol sende. Uygulamalarla paylaşmak istediğin bilgileri seç.";

/* Title of the web dialog that prompts the user to log in to Facebook. */
"LoginWeb.LogInTitle" = "Giriş Yap";

/* The label for FBSDKSendButton */
"SendButton.Send" = "Gönder";

/* The label for FBSDKShareButton */
"ShareButton.Share" = "Paylaş";

/* Prompts a person if this is their current account */
"SmartLogin.NotYou" = "Sen değil misin?";

/* Text on a button that a person presses to confirm that they are finished with the login experience */
"SmartLogin.ConfirmationTitle" = "Girişi Onayla";

/* Text on a button that lets a person continue with their name linked to a Facebook account (Name = %@) */
"SmartLogin.Continue" = "%@ olarak devam et";
