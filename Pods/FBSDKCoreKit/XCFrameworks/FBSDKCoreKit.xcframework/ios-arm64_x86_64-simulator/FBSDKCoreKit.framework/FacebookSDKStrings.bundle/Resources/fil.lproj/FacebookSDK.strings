/* Prompts a person to go to the URL listed to enter the confirmation code that is presented to them above the given string. */
"DeviceLogin.LogInPrompt" = "Pumunta sa %@ at ilagay ang code na ipinapakita sa itaas.";

/* Prompts a person that the next thing they need to do to finish connecting their Smart TV and Facebook application is to navigate to their Facebook application on their mobile device and look through their notifications for a message about the connection being formed */
"DeviceLogin.SmartLogInPrompt" = "Para ikonekta ang iyong account, buksan ang Facebook app sa iyong cellphone at tumingin ng mga notification.";

/* Displayed as a separator between two options. First option is on a line above this, and second option is below */
"DeviceLogin.SmartLogInOrLabel" = "- O KAYA -";

/* The title of the label to dismiss the alert when presenting user facing error messages */
"ErrorRecovery.Alert.OK" = "OK";

/* The title of the label to decline attempting error recovery */
"ErrorRecovery.Cancel" = "Kanselahin";

/* The fallback message to display to recover invalidated tokens */
"ErrorRecovery.Login.Suggestion" = "Mangyaring mag-log in muli sa app na ito para ikonekta muli ang iyong Facebook account.";

/* The title of the label to start attempting error recovery */
"ErrorRecovery.OK" = "OK";

/* The fallback message to display to retry transient errors */
"ErrorRecovery.Transient.Suggestion" = "Pansamantalang abala ang server, pakisubukan muli.";

/* The label for the FBSDKLoginButton action sheet to cancel logging out */
"LoginButton.CancelLogout" = "Kanselahin";

/* The label for the FBSDKLoginButton action sheet to confirm logging out */
"LoginButton.ConfirmLogOut" = "Mag-log Out";

/* The fallback string for the FBSDKLoginButton label when the user name is not available yet */
"LoginButton.LoggedIn" = "Naka-log in gamit ang Facebook";

/* The format string for the FBSDKLoginButton label when the user is logged in */
"LoginButton.LoggedInAs" = "Naka-log in bilang si %@";

/* The short label for the FBSDKLoginButton when the user is currently logged out */
"LoginButton.LogIn" = "Mag-log in";

/* The long label for the FBSDKLoginButton when the user is currently logged out */
"LoginButton.LogInContinue" = "Magatuloy sa Facebook";

/* The long label for the FBSDKLoginButton when the user is currently logged out */
"LoginButton.LogInLong" = "Mag-log in sa pamamagitan ng Facebook";

/* The label for the FBSDKLoginButton when the user is currently logged in */
"LoginButton.LogOut" = "Mag-log out";

/* The user facing error message when the app slider has been disabled and login fails. */
"LoginError.SystemAccount.Disabled" = "Hindi binigyan ng access ang Facebook account. Beripikahin ang mga setting ng device.";

/* The user facing error message when the Accounts framework encounters a network error. */
"LoginError.SystemAccount.Network" = "Hindi nakakonekta sa Facebook. Tingnan ang iyong koneksyon sa network at subukan muli.";

/* The user facing error message when the device Facebook account password is incorrect and login fails. */
"LoginError.SystemAccount.PasswordChange" = "Nagbago ang iyong password sa Facebook. Para makumpirma ang iyong password, buksan ang Mga Setting &gt; Facebook at i-tap ang iyong pangalan.";

/* The user facing error message when the device Facebook account is unavailable and login fails. */
"LoginError.SystemAccount.Unavailable" = "Hindi na-configure sa device ang Facebook account.";

/* The user facing error message when the Facebook account signed in to the Accounts framework becomes unconfirmed. */
"LoginError.SystemAccount.UnconfirmedUser" = "Hindi nakumpirma ang iyong account. Mangyaring mag-log in sa www.facebook.com at sundin ang mga ibinigay na tagubilin.";

/* The user facing error message when the Facebook account signed in to the Accounts framework has been checkpointed. */
"LoginError.SystemAccount.UserCheckpointed" = "Hindi ka makakapag-log in sa mga app sa oras na ito. Mangyaring mag-log in sa www.facebook.com at sundin ang mga ibinigay na tagubilin.";

/* The message of the FBSDKLoginTooltipView */
"LoginTooltip.Message" = "Ikaw ang may kontrol - piliin kung anong impormasyon ang gusto mong ibahagi sa mga app.";

/* Title of the web dialog that prompts the user to log in to Facebook. */
"LoginWeb.LogInTitle" = "Mag-log In";

/* The label for FBSDKSendButton */
"SendButton.Send" = "Ipadala";

/* The label for FBSDKShareButton */
"ShareButton.Share" = "Ibahagi";

/* Prompts a person if this is their current account */
"SmartLogin.NotYou" = "Hindi ikaw?";

/* Text on a button that a person presses to confirm that they are finished with the login experience */
"SmartLogin.ConfirmationTitle" = "I-confirm ang Pag-log in";

/* Text on a button that lets a person continue with their name linked to a Facebook account (Name = %@) */
"SmartLogin.Continue" = "Magpatuloy bilang %@";
