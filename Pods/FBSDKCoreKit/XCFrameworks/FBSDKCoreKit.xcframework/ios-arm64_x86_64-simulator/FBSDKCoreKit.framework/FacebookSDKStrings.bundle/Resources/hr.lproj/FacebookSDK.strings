/* Prompts a person to go to the URL listed to enter the confirmation code that is presented to them above the given string. */
"DeviceLogin.LogInPrompt" = "Posjetite adresu %@ i unesite gore prikazan kod.";

/* Prompts a person that the next thing they need to do to finish connecting their Smart TV and Facebook application is to navigate to their Facebook application on their mobile device and look through their notifications for a message about the connection being formed */
"DeviceLogin.SmartLogInPrompt" = "Da biste povezali račun, otvorite aplikaciju Facebook na mobilnom uređaju i potražite obavijesti.";

/* Displayed as a separator between two options. First option is on a line above this, and second option is below */
"DeviceLogin.SmartLogInOrLabel" = "- ILI -";

/* The title of the label to dismiss the alert when presenting user facing error messages */
"ErrorRecovery.Alert.OK" = "U redu";

/* The title of the label to decline attempting error recovery */
"ErrorRecovery.Cancel" = "Odustani";

/* The fallback message to display to recover invalidated tokens */
"ErrorRecovery.Login.Suggestion" = "Ponovo se prijavite u ovu aplikaciju kako biste se ponovo povezali s Facebook računom.";

/* The title of the label to start attempting error recovery */
"ErrorRecovery.OK" = "U redu";

/* The fallback message to display to retry transient errors */
"ErrorRecovery.Transient.Suggestion" = "Poslužitelj je privremeno zauzet, pokušajte ponovo.";

/* The label for the FBSDKLoginButton action sheet to cancel logging out */
"LoginButton.CancelLogout" = "Odustani";

/* The label for the FBSDKLoginButton action sheet to confirm logging out */
"LoginButton.ConfirmLogOut" = "Odjavi se";

/* The fallback string for the FBSDKLoginButton label when the user name is not available yet */
"LoginButton.LoggedIn" = "Prijavljen putem Facebooka";

/* The format string for the FBSDKLoginButton label when the user is logged in */
"LoginButton.LoggedInAs" = "Prijavljen kao %@";

/* The short label for the FBSDKLoginButton when the user is currently logged out */
"LoginButton.LogIn" = "Prijavi se";

/* The long label for the FBSDKLoginButton when the user is currently logged out */
"LoginButton.LogInContinue" = "Nastavite s korištenjem Facebooka";

/* The long label for the FBSDKLoginButton when the user is currently logged out */
"LoginButton.LogInLong" = "Prijava putem Facebooka";

/* The label for the FBSDKLoginButton when the user is currently logged in */
"LoginButton.LogOut" = "Odjavi se";

/* The user facing error message when the app slider has been disabled and login fails. */
"LoginError.SystemAccount.Disabled" = "Pristup Facebook računu nije odobren. Provjerite postavke uređaja.";

/* The user facing error message when the Accounts framework encounters a network error. */
"LoginError.SystemAccount.Network" = "Povezivanje s Facebookom nije uspjelo. Provjerite mrežnu vezu i pokušajte ponovo.";

/* The user facing error message when the device Facebook account password is incorrect and login fails. */
"LoginError.SystemAccount.PasswordChange" = "Promijenili ste lozinku za Facebook. Za potvrdu lozinke otvorite Postavke &gt; Facebook i dodirnite svoje ime.";

/* The user facing error message when the device Facebook account is unavailable and login fails. */
"LoginError.SystemAccount.Unavailable" = "Na uređaju nije konfiguriran račun za Facebook.";

/* The user facing error message when the Facebook account signed in to the Accounts framework becomes unconfirmed. */
"LoginError.SystemAccount.UnconfirmedUser" = "Vaš račun nije potvrđen. Prijavite se na www.facebook.com i slijedite upute.";

/* The user facing error message when the Facebook account signed in to the Accounts framework has been checkpointed. */
"LoginError.SystemAccount.UserCheckpointed" = "U ovome trenutku prijava u aplikacije nije moguća. Prijavite se na www.facebook.com i slijedite upute.";

/* The message of the FBSDKLoginTooltipView */
"LoginTooltip.Message" = "Vi odlučujete – odaberite koje podatke želite dijeliti u aplikacijama.";

/* Title of the web dialog that prompts the user to log in to Facebook. */
"LoginWeb.LogInTitle" = "Prijava";

/* The label for FBSDKSendButton */
"SendButton.Send" = "Pošalji";

/* The label for FBSDKShareButton */
"ShareButton.Share" = "Dijeli";

/* Prompts a person if this is their current account */
"SmartLogin.NotYou" = "To niste vi?";

/* Text on a button that a person presses to confirm that they are finished with the login experience */
"SmartLogin.ConfirmationTitle" = "Potvrda prijave";

/* Text on a button that lets a person continue with their name linked to a Facebook account (Name = %@) */
"SmartLogin.Continue" = "Nastavi kao %@";
