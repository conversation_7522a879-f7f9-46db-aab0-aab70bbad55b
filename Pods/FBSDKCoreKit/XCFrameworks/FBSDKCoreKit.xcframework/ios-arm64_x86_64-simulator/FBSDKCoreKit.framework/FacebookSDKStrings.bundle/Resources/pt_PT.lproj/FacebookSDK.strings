/* Prompts a person to go to the URL listed to enter the confirmation code that is presented to them above the given string. */
"DeviceLogin.LogInPrompt" = "Visita %@ e introduz o código mostrado acima.";

/* Prompts a person that the next thing they need to do to finish connecting their Smart TV and Facebook application is to navigate to their Facebook application on their mobile device and look through their notifications for a message about the connection being formed */
"DeviceLogin.SmartLogInPrompt" = "Para ligares a tua conta, abre a app Facebook no teu dispositivo móvel e verifica se tens notificações.";

/* Displayed as a separator between two options. First option is on a line above this, and second option is below */
"DeviceLogin.SmartLogInOrLabel" = "– OU –";

/* The title of the label to dismiss the alert when presenting user facing error messages */
"ErrorRecovery.Alert.OK" = "OK";

/* The title of the label to decline attempting error recovery */
"ErrorRecovery.Cancel" = "Cancelar";

/* The fallback message to display to recover invalidated tokens */
"ErrorRecovery.Login.Suggestion" = "Volta a iniciar sessão nesta aplicação para te ligares novamente à tua conta do Facebook.";

/* The title of the label to start attempting error recovery */
"ErrorRecovery.OK" = "OK";

/* The fallback message to display to retry transient errors */
"ErrorRecovery.Transient.Suggestion" = "O servidor está temporariamente ocupado. Tenta novamente.";

/* The label for the FBSDKLoginButton action sheet to cancel logging out */
"LoginButton.CancelLogout" = "Cancelar";

/* The label for the FBSDKLoginButton action sheet to confirm logging out */
"LoginButton.ConfirmLogOut" = "Terminar sessão";

/* The fallback string for the FBSDKLoginButton label when the user name is not available yet */
"LoginButton.LoggedIn" = "Com sessão iniciada através do Facebook";

/* The format string for the FBSDKLoginButton label when the user is logged in */
"LoginButton.LoggedInAs" = "Com sessão iniciada como %@";

/* The short label for the FBSDKLoginButton when the user is currently logged out */
"LoginButton.LogIn" = "Iniciar sessão";

/* The long label for the FBSDKLoginButton when the user is currently logged out */
"LoginButton.LogInContinue" = "Continuar com o Facebook";

/* The long label for the FBSDKLoginButton when the user is currently logged out */
"LoginButton.LogInLong" = "Iniciar sessão com o Facebook";

/* The label for the FBSDKLoginButton when the user is currently logged in */
"LoginButton.LogOut" = "Terminar a sessão";

/* The user facing error message when the app slider has been disabled and login fails. */
"LoginError.SystemAccount.Disabled" = "O acesso não foi concedido à conta do Facebook. Verificar as definições do dispositivo.";

/* The user facing error message when the Accounts framework encounters a network error. */
"LoginError.SystemAccount.Network" = "Não é possível ligar ao Facebook. Verifica a tua ligação à rede e tenta novamente.";

/* The user facing error message when the device Facebook account password is incorrect and login fails. */
"LoginError.SystemAccount.PasswordChange" = "A tua palavra-passe do Facebook foi alterada. Para confirmares a tua palavra-passe, abre as Definições &gt; Facebook e toca no teu nome.";

/* The user facing error message when the device Facebook account is unavailable and login fails. */
"LoginError.SystemAccount.Unavailable" = "A conta do Facebook não foi configurada no dispositivo.";

/* The user facing error message when the Facebook account signed in to the Accounts framework becomes unconfirmed. */
"LoginError.SystemAccount.UnconfirmedUser" = "A tua conta não foi confirmada. Inicia sessão em www.facebook.com e segue as instruções indicadas.";

/* The user facing error message when the Facebook account signed in to the Accounts framework has been checkpointed. */
"LoginError.SystemAccount.UserCheckpointed" = "Não podes iniciar sessão em aplicações neste momento. Inicia sessão em www.facebook.com e segue as instruções indicadas.";

/* The message of the FBSDKLoginTooltipView */
"LoginTooltip.Message" = "Tens o controlo: escolhe a informação que pretendes partilhar com as apps.";

/* Title of the web dialog that prompts the user to log in to Facebook. */
"LoginWeb.LogInTitle" = "Iniciar sessão";

/* The label for FBSDKSendButton */
"SendButton.Send" = "Enviar";

/* The label for FBSDKShareButton */
"ShareButton.Share" = "Partilhar";

/* Prompts a person if this is their current account */
"SmartLogin.NotYou" = "Não és tu?";

/* Text on a button that a person presses to confirm that they are finished with the login experience */
"SmartLogin.ConfirmationTitle" = "Confirmar Início de Sessão";

/* Text on a button that lets a person continue with their name linked to a Facebook account (Name = %@) */
"SmartLogin.Continue" = "Continuar como %@";
