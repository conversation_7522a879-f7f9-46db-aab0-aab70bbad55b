/* Prompts a person to go to the URL listed to enter the confirmation code that is presented to them above the given string. */
"DeviceLogin.LogInPrompt" = "Gå til %@, og indtast den kode, der er angivet ovenfor.";

/* Prompts a person that the next thing they need to do to finish connecting their Smart TV and Facebook application is to navigate to their Facebook application on their mobile device and look through their notifications for a message about the connection being formed */
"DeviceLogin.SmartLogInPrompt" = "Du kan tilknytte din konto ved at åbne Facebook-appen på din mobilenhed og tjekke notifikationerne.";

/* Displayed as a separator between two options. First option is on a line above this, and second option is below */
"DeviceLogin.SmartLogInOrLabel" = "- ELLER -";

/* The title of the label to dismiss the alert when presenting user facing error messages */
"ErrorRecovery.Alert.OK" = "OK";

/* The title of the label to decline attempting error recovery */
"ErrorRecovery.Cancel" = "Annuller";

/* The fallback message to display to recover invalidated tokens */
"ErrorRecovery.Login.Suggestion" = "Log på denne app igen for at genoprette forbindelsen til din Facebook-konto.";

/* The title of the label to start attempting error recovery */
"ErrorRecovery.OK" = "OK";

/* The fallback message to display to retry transient errors */
"ErrorRecovery.Transient.Suggestion" = "Serveren er optaget i øjeblikket. Prøv igen.";

/* The label for the FBSDKLoginButton action sheet to cancel logging out */
"LoginButton.CancelLogout" = "Annuller";

/* The label for the FBSDKLoginButton action sheet to confirm logging out */
"LoginButton.ConfirmLogOut" = "Log af";

/* The fallback string for the FBSDKLoginButton label when the user name is not available yet */
"LoginButton.LoggedIn" = "Logget på med Facebook";

/* The format string for the FBSDKLoginButton label when the user is logged in */
"LoginButton.LoggedInAs" = "Logget på som %@";

/* The short label for the FBSDKLoginButton when the user is currently logged out */
"LoginButton.LogIn" = "Log på";

/* The long label for the FBSDKLoginButton when the user is currently logged out */
"LoginButton.LogInContinue" = "Fortsæt med Facebook";

/* The long label for the FBSDKLoginButton when the user is currently logged out */
"LoginButton.LogInLong" = "Log på med Facebook";

/* The label for the FBSDKLoginButton when the user is currently logged in */
"LoginButton.LogOut" = "Log af";

/* The user facing error message when the app slider has been disabled and login fails. */
"LoginError.SystemAccount.Disabled" = "Der er ikke blevet givet adgang til Facebook-kontoen. Kontrollér enhedsindstillingerne.";

/* The user facing error message when the Accounts framework encounters a network error. */
"LoginError.SystemAccount.Network" = "Kunne ikke oprette forbindelse til Facebook. Kontrollér din netværksforbindelse, og prøv igen.";

/* The user facing error message when the device Facebook account password is incorrect and login fails. */
"LoginError.SystemAccount.PasswordChange" = "Din Facebook-adgangskode er ændret. For at bekræfte din adgangskode skal du åbne Indstillinger &gt; Facebook og trykke på dit navn.";

/* The user facing error message when the device Facebook account is unavailable and login fails. */
"LoginError.SystemAccount.Unavailable" = "Facebook-kontoen er ikke blevet konfigureret på enheden.";

/* The user facing error message when the Facebook account signed in to the Accounts framework becomes unconfirmed. */
"LoginError.SystemAccount.UnconfirmedUser" = "Din konto er ikke bekræftet. Log på www.facebook.com, og følg instruktionerne.";

/* The user facing error message when the Facebook account signed in to the Accounts framework has been checkpointed. */
"LoginError.SystemAccount.UserCheckpointed" = "Du kan ikke logge på apps på nuværende tidspunkt. Log på www.facebook.com, og følg instruktionerne.";

/* The message of the FBSDKLoginTooltipView */
"LoginTooltip.Message" = "Du har styringen – vælg de oplysninger, som du vil dele med apps.";

/* Title of the web dialog that prompts the user to log in to Facebook. */
"LoginWeb.LogInTitle" = "Log på";

/* The label for FBSDKSendButton */
"SendButton.Send" = "Send";

/* The label for FBSDKShareButton */
"ShareButton.Share" = "Del";

/* Prompts a person if this is their current account */
"SmartLogin.NotYou" = "Ikke dig?";

/* Text on a button that a person presses to confirm that they are finished with the login experience */
"SmartLogin.ConfirmationTitle" = "Bekræft login";

/* Text on a button that lets a person continue with their name linked to a Facebook account (Name = %@) */
"SmartLogin.Continue" = "Fortsæt som %@";
