/* Prompts a person to go to the URL listed to enter the confirmation code that is presented to them above the given string. */
"DeviceLogin.LogInPrompt" = "Keresd fel a %@ címet, és írd be a fent megjelenített kódot.";

/* Prompts a person that the next thing they need to do to finish connecting their Smart TV and Facebook application is to navigate to their Facebook application on their mobile device and look through their notifications for a message about the connection being formed */
"DeviceLogin.SmartLogInPrompt" = "A fiók hozzákapcsolásához nyisd meg a Facebook alkalmazást a mobilkészülékeden, és nézd meg az értesítéseket.";

/* Displayed as a separator between two options. First option is on a line above this, and second option is below */
"DeviceLogin.SmartLogInOrLabel" = "- VAGY -";

/* The title of the label to dismiss the alert when presenting user facing error messages */
"ErrorRecovery.Alert.OK" = "OK";

/* The title of the label to decline attempting error recovery */
"ErrorRecovery.Cancel" = "Mégsem";

/* The fallback message to display to recover invalidated tokens */
"ErrorRecovery.Login.Suggestion" = "<PERSON><PERSON>rj<PERSON>k, jelentkezz be újra ebbe az alkalmazásba, ha szeretnéd ismét összekapcsolni a Facebook-fiókoddal.";

/* The title of the label to start attempting error recovery */
"ErrorRecovery.OK" = "OK";

/* The fallback message to display to retry transient errors */
"ErrorRecovery.Transient.Suggestion" = "A kiszolgáló átmenetileg foglalt, próbáld meg újra.";

/* The label for the FBSDKLoginButton action sheet to cancel logging out */
"LoginButton.CancelLogout" = "Mégsem";

/* The label for the FBSDKLoginButton action sheet to confirm logging out */
"LoginButton.ConfirmLogOut" = "Kijelentkezés";

/* The fallback string for the FBSDKLoginButton label when the user name is not available yet */
"LoginButton.LoggedIn" = "Bejelentkezve a Facebook használatával";

/* The format string for the FBSDKLoginButton label when the user is logged in */
"LoginButton.LoggedInAs" = "Bejelentkezve %@ néven";

/* The short label for the FBSDKLoginButton when the user is currently logged out */
"LoginButton.LogIn" = "Bejelentkezés";

/* The long label for the FBSDKLoginButton when the user is currently logged out */
"LoginButton.LogInContinue" = "Folytatás a Facebookkal";

/* The long label for the FBSDKLoginButton when the user is currently logged out */
"LoginButton.LogInLong" = "Bejelentkezés a Facebook használatával";

/* The label for the FBSDKLoginButton when the user is currently logged in */
"LoginButton.LogOut" = "Kijelentkezés";

/* The user facing error message when the app slider has been disabled and login fails. */
"LoginError.SystemAccount.Disabled" = "A Facebook-fiókhoz nincs megadva a hozzáférés. Ellenőrizd az eszköz beállításait.";

/* The user facing error message when the Accounts framework encounters a network error. */
"LoginError.SystemAccount.Network" = "Nem sikerült kapcsolódni a Facebookhoz. Ellenőrizd a hálózati kapcsolatot, majd próbáld meg újra.";

/* The user facing error message when the device Facebook account password is incorrect and login fails. */
"LoginError.SystemAccount.PasswordChange" = "Facebook-jelszavad megváltozott. A jelszó megerősítéséhez nyisd meg a Settings (Beállítások) &gt; Facebook pontot, és koppints a nevedre.";

/* The user facing error message when the device Facebook account is unavailable and login fails. */
"LoginError.SystemAccount.Unavailable" = "A készüléken nincs beállítva a Facebook-fiók.";

/* The user facing error message when the Facebook account signed in to the Accounts framework becomes unconfirmed. */
"LoginError.SystemAccount.UnconfirmedUser" = "A fiókod nincs megerősítve. Jelentkezz be a www.facebook.com címre, és kövesd az utasításokat.";

/* The user facing error message when the Facebook account signed in to the Accounts framework has been checkpointed. */
"LoginError.SystemAccount.UserCheckpointed" = "Jelenleg nem tudsz alkalmazásokba bejelentkezni. Jelentkezz be a www.facebook.com címre, és kövesd az utasításokat.";

/* The message of the FBSDKLoginTooltipView */
"LoginTooltip.Message" = "A döntés a kezedben van: kiválaszthatod, hogy milyen adatokat osztasz meg az alkalmazásokkal.";

/* Title of the web dialog that prompts the user to log in to Facebook. */
"LoginWeb.LogInTitle" = "Bejelentkezés";

/* The label for FBSDKSendButton */
"SendButton.Send" = "Küldés";

/* The label for FBSDKShareButton */
"ShareButton.Share" = "Megosztás";

/* Prompts a person if this is their current account */
"SmartLogin.NotYou" = "Nem te vagy az?";

/* Text on a button that a person presses to confirm that they are finished with the login experience */
"SmartLogin.ConfirmationTitle" = "Bejelentkezés megerősítése";

/* Text on a button that lets a person continue with their name linked to a Facebook account (Name = %@) */
"SmartLogin.Continue" = "Folytatás mint %@";
