/* Prompts a person to go to the URL listed to enter the confirmation code that is presented to them above the given string. */
"DeviceLogin.LogInPrompt" = "Ga naar %@ en voer de bovenstaande code in.";

/* Prompts a person that the next thing they need to do to finish connecting their Smart TV and Facebook application is to navigate to their Facebook application on their mobile device and look through their notifications for a message about the connection being formed */
"DeviceLogin.SmartLogInPrompt" = "Om je account te verbinden, open je de Facebook-app op je mobiele apparaat en controleer of je meldingen ziet.";

/* Displayed as a separator between two options. First option is on a line above this, and second option is below */
"DeviceLogin.SmartLogInOrLabel" = "- OF -";

/* The title of the label to dismiss the alert when presenting user facing error messages */
"ErrorRecovery.Alert.OK" = "OK";

/* The title of the label to decline attempting error recovery */
"ErrorRecovery.Cancel" = "Annuleren";

/* The fallback message to display to recover invalidated tokens */
"ErrorRecovery.Login.Suggestion" = "Meld je nogmaals aan bij deze app om verbinding te maken met je Facebook-account.";

/* The title of the label to start attempting error recovery */
"ErrorRecovery.OK" = "OK";

/* The fallback message to display to retry transient errors */
"ErrorRecovery.Transient.Suggestion" = "De server is tijdelijk niet beschikbaar. Probeer het opnieuw.";

/* The label for the FBSDKLoginButton action sheet to cancel logging out */
"LoginButton.CancelLogout" = "Annuleren";

/* The label for the FBSDKLoginButton action sheet to confirm logging out */
"LoginButton.ConfirmLogOut" = "Afmelden";

/* The fallback string for the FBSDKLoginButton label when the user name is not available yet */
"LoginButton.LoggedIn" = "Aangemeld via Facebook";

/* The format string for the FBSDKLoginButton label when the user is logged in */
"LoginButton.LoggedInAs" = "Aangemeld als %@";

/* The short label for the FBSDKLoginButton when the user is currently logged out */
"LoginButton.LogIn" = "Aanmelden";

/* The long label for the FBSDKLoginButton when the user is currently logged out */
"LoginButton.LogInContinue" = "Doorgaan met Facebook";

/* The long label for the FBSDKLoginButton when the user is currently logged out */
"LoginButton.LogInLong" = "Aanmelden met Facebook";

/* The label for the FBSDKLoginButton when the user is currently logged in */
"LoginButton.LogOut" = "Afmelden";

/* The user facing error message when the app slider has been disabled and login fails. */
"LoginError.SystemAccount.Disabled" = "Er is geen toegang verleend aan het Facebook-account. Verifieer de apparaatinstellingen.";

/* The user facing error message when the Accounts framework encounters a network error. */
"LoginError.SystemAccount.Network" = "Er kan geen verbinding worden gemaakt met Facebook. Controleer je netwerkverbinding en probeer het opnieuw.";

/* The user facing error message when the device Facebook account password is incorrect and login fails. */
"LoginError.SystemAccount.PasswordChange" = "Je Facebook-wachtwoord is gewijzigd. Open Instellingen &gt; Facebook en tik op je naam om je wachtwoord te bevestigen.";

/* The user facing error message when the device Facebook account is unavailable and login fails. */
"LoginError.SystemAccount.Unavailable" = "Het Facebook-account is niet geconfigureerd op het apparaat.";

/* The user facing error message when the Facebook account signed in to the Accounts framework becomes unconfirmed. */
"LoginError.SystemAccount.UnconfirmedUser" = "Je account is niet bevestigd. Meld je aan bij www.facebook.com en volg de instructies.";

/* The user facing error message when the Facebook account signed in to the Accounts framework has been checkpointed. */
"LoginError.SystemAccount.UserCheckpointed" = "Je kunt je momenteel niet aanmelden bij apps. Meld je aan bij www.facebook.com en volg de instructies.";

/* The message of the FBSDKLoginTooltipView */
"LoginTooltip.Message" = "Jij hebt de controle. Kies welke informatie je met apps wilt delen.";

/* Title of the web dialog that prompts the user to log in to Facebook. */
"LoginWeb.LogInTitle" = "Aanmelden";

/* The label for FBSDKSendButton */
"SendButton.Send" = "Verzenden";

/* The label for FBSDKShareButton */
"ShareButton.Share" = "Delen";

/* Prompts a person if this is their current account */
"SmartLogin.NotYou" = "Ben jij dit niet?";

/* Text on a button that a person presses to confirm that they are finished with the login experience */
"SmartLogin.ConfirmationTitle" = "Aanmelden bevestigen";

/* Text on a button that lets a person continue with their name linked to a Facebook account (Name = %@) */
"SmartLogin.Continue" = "Doorgaan als %@";
