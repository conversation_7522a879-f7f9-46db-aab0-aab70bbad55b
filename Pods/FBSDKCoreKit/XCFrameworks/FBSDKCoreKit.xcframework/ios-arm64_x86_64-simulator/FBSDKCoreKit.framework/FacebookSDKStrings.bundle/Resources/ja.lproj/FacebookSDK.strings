/* Prompts a person to go to the URL listed to enter the confirmation code that is presented to them above the given string. */
"DeviceLogin.LogInPrompt" = "%@にアクセスし、上に表示されているコードを入力してください。";

/* Prompts a person that the next thing they need to do to finish connecting their Smart TV and Facebook application is to navigate to their Facebook application on their mobile device and look through their notifications for a message about the connection being formed */
"DeviceLogin.SmartLogInPrompt" = "アカウントをリンクするには、お使いのモバイル機器でFacebookアプリを開き、お知らせをご確認ください。";

/* Displayed as a separator between two options. First option is on a line above this, and second option is below */
"DeviceLogin.SmartLogInOrLabel" = "- または -";

/* The title of the label to dismiss the alert when presenting user facing error messages */
"ErrorRecovery.Alert.OK" = "OK";

/* The title of the label to decline attempting error recovery */
"ErrorRecovery.Cancel" = "キャンセル";

/* The fallback message to display to recover invalidated tokens */
"ErrorRecovery.Login.Suggestion" = "このアプリにもう一度ログインして、Facebookアカウントを再接続してください。";

/* The title of the label to start attempting error recovery */
"ErrorRecovery.OK" = "OK";

/* The fallback message to display to retry transient errors */
"ErrorRecovery.Transient.Suggestion" = "サーバーが一時的にビジーです。もう一度お試しください。";

/* The label for the FBSDKLoginButton action sheet to cancel logging out */
"LoginButton.CancelLogout" = "キャンセル";

/* The label for the FBSDKLoginButton action sheet to confirm logging out */
"LoginButton.ConfirmLogOut" = "ログアウト";

/* The fallback string for the FBSDKLoginButton label when the user name is not available yet */
"LoginButton.LoggedIn" = "Facebookを使用してログイン中";

/* The format string for the FBSDKLoginButton label when the user is logged in */
"LoginButton.LoggedInAs" = "%@としてログイン中";

/* The short label for the FBSDKLoginButton when the user is currently logged out */
"LoginButton.LogIn" = "ログイン";

/* The long label for the FBSDKLoginButton when the user is currently logged out */
"LoginButton.LogInContinue" = "Facebookで続ける";

/* The long label for the FBSDKLoginButton when the user is currently logged out */
"LoginButton.LogInLong" = "Facebookでログイン";

/* The label for the FBSDKLoginButton when the user is currently logged in */
"LoginButton.LogOut" = "ログアウト";

/* The user facing error message when the app slider has been disabled and login fails. */
"LoginError.SystemAccount.Disabled" = "Facebookアカウントにアクセス権が与えられていません。デバイス設定を確認してください。";

/* The user facing error message when the Accounts framework encounters a network error. */
"LoginError.SystemAccount.Network" = "Facebookに接続できませんでした。ネットワーク接続を確認してもう一度お試しください。";

/* The user facing error message when the device Facebook account password is incorrect and login fails. */
"LoginError.SystemAccount.PasswordChange" = "Facebookパスワードが変更されています。パスワードを確認するには、[設定] &gt; [Facebook]の順に開き、名前をタップしてください。";

/* The user facing error message when the device Facebook account is unavailable and login fails. */
"LoginError.SystemAccount.Unavailable" = "デバイスにFacebookアカウントが構成されていません。";

/* The user facing error message when the Facebook account signed in to the Accounts framework becomes unconfirmed. */
"LoginError.SystemAccount.UnconfirmedUser" = "アカウントが確認されません。www.facebook.comにログインし、表示される説明に従ってください。";

/* The user facing error message when the Facebook account signed in to the Accounts framework has been checkpointed. */
"LoginError.SystemAccount.UserCheckpointed" = "今はアプリにログインできません。www.facebook.comにログインし、表示される説明に従ってください。";

/* The message of the FBSDKLoginTooltipView */
"LoginTooltip.Message" = "アプリと共有する情報をコントロールできます。";

/* Title of the web dialog that prompts the user to log in to Facebook. */
"LoginWeb.LogInTitle" = "ログイン";

/* The label for FBSDKSendButton */
"SendButton.Send" = "送信";

/* The label for FBSDKShareButton */
"ShareButton.Share" = "シェア";

/* Prompts a person if this is their current account */
"SmartLogin.NotYou" = "別のユーザーとしてログイン";

/* Text on a button that a person presses to confirm that they are finished with the login experience */
"SmartLogin.ConfirmationTitle" = "ログインの確認";

/* Text on a button that lets a person continue with their name linked to a Facebook account (Name = %@) */
"SmartLogin.Continue" = "%@として続行";
