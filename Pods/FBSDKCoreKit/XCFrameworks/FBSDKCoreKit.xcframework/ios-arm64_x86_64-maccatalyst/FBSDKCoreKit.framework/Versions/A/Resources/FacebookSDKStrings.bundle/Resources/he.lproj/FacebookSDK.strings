/* Prompts a person to go to the URL listed to enter the confirmation code that is presented to them above the given string. */
"DeviceLogin.LogInPrompt" = "בקר ב-%@ והזן את הקוד המופיע למעלה.";

/* Prompts a person that the next thing they need to do to finish connecting their Smart TV and Facebook application is to navigate to their Facebook application on their mobile device and look through their notifications for a message about the connection being formed */
"DeviceLogin.SmartLogInPrompt" = "כדי לחבר את החשבון שלך, פתח את אפליקציית פייסבוק במכשיר הנייד שלך ובדוק אם יש התראות.";

/* Displayed as a separator between two options. First option is on a line above this, and second option is below */
"DeviceLogin.SmartLogInOrLabel" = "- או -";

/* The title of the label to dismiss the alert when presenting user facing error messages */
"ErrorRecovery.Alert.OK" = "אישור";

/* The title of the label to decline attempting error recovery */
"ErrorRecovery.Cancel" = "ביטול";

/* The fallback message to display to recover invalidated tokens */
"ErrorRecovery.Login.Suggestion" = "התחבר לאפליקציה זו שוב כדי לחבר מחדש את חשבון הפייסבוק שלך.";

/* The title of the label to start attempting error recovery */
"ErrorRecovery.OK" = "אישור";

/* The fallback message to display to retry transient errors */
"ErrorRecovery.Transient.Suggestion" = "השרת עמוס באופן זמני, נסה שוב.";

/* The label for the FBSDKLoginButton action sheet to cancel logging out */
"LoginButton.CancelLogout" = "ביטול";

/* The label for the FBSDKLoginButton action sheet to confirm logging out */
"LoginButton.ConfirmLogOut" = "התנתק";

/* The fallback string for the FBSDKLoginButton label when the user name is not available yet */
"LoginButton.LoggedIn" = "מחובר באמצעות פייסבוק";

/* The format string for the FBSDKLoginButton label when the user is logged in */
"LoginButton.LoggedInAs" = "מחובר בתור %@";

/* The short label for the FBSDKLoginButton when the user is currently logged out */
"LoginButton.LogIn" = "התחבר";

/* The long label for the FBSDKLoginButton when the user is currently logged out */
"LoginButton.LogInContinue" = "המשך עם פייסבוק";

/* The long label for the FBSDKLoginButton when the user is currently logged out */
"LoginButton.LogInLong" = "התחבר באמצעות פייסבוק";

/* The label for the FBSDKLoginButton when the user is currently logged in */
"LoginButton.LogOut" = "התנתק";

/* The user facing error message when the app slider has been disabled and login fails. */
"LoginError.SystemAccount.Disabled" = "לא הוענקה גישה לחשבון הפייסבוק. אמת את הגדרות המכשיר.";

/* The user facing error message when the Accounts framework encounters a network error. */
"LoginError.SystemAccount.Network" = "לא ניתן להתחבר לפייסבוק. בדוק את החיבור שלך לרשת ונסה שוב.";

/* The user facing error message when the device Facebook account password is incorrect and login fails. */
"LoginError.SystemAccount.PasswordChange" = "סיסמת הפייסבוק שלך השתנתה. כדי לאשר את הסיסמה, פתח את 'הגדרות' &gt; 'פייסבוק' והקש על שמך.";

/* The user facing error message when the device Facebook account is unavailable and login fails. */
"LoginError.SystemAccount.Unavailable" = "חשבון הפייסבוק לא הוגדר במכשיר.";

/* The user facing error message when the Facebook account signed in to the Accounts framework becomes unconfirmed. */
"LoginError.SystemAccount.UnconfirmedUser" = "החשבון שלך לא אושר. התחבר ל-www.facebook.com ופעל בהתאם להוראות שיוצגו.";

/* The user facing error message when the Facebook account signed in to the Accounts framework has been checkpointed. */
"LoginError.SystemAccount.UserCheckpointed" = "לא ניתן להתחבר לאפליקציות כעת. התחבר ל-www.facebook.com ופעל בהתאם להוראות שיוצגו.";

/* The message of the FBSDKLoginTooltipView */
"LoginTooltip.Message" = "אתה מחליט - בחר איזה מידע אתה רוצה לשתף עם אפליקציות.";

/* Title of the web dialog that prompts the user to log in to Facebook. */
"LoginWeb.LogInTitle" = "התחבר";

/* The label for FBSDKSendButton */
"SendButton.Send" = "שלח";

/* The label for FBSDKShareButton */
"ShareButton.Share" = "שתף";

/* Prompts a person if this is their current account */
"SmartLogin.NotYou" = "לא אתה?";

/* Text on a button that a person presses to confirm that they are finished with the login experience */
"SmartLogin.ConfirmationTitle" = "אשר התחברות";

/* Text on a button that lets a person continue with their name linked to a Facebook account (Name = %@) */
"SmartLogin.Continue" = "המשך כ-%@";
