/* Prompts a person to go to the URL listed to enter the confirmation code that is presented to them above the given string. */
"DeviceLogin.LogInPrompt" = "Navštivte %@ a zadejte nahoře uvedený kód.";

/* Prompts a person that the next thing they need to do to finish connecting their Smart TV and Facebook application is to navigate to their Facebook application on their mobile device and look through their notifications for a message about the connection being formed */
"DeviceLogin.SmartLogInPrompt" = "Účet si můžete připojit tak, že si na mobilním zařízení spustíte aplikaci Facebook a podíváte se do upozornění.";

/* Displayed as a separator between two options. First option is on a line above this, and second option is below */
"DeviceLogin.SmartLogInOrLabel" = "- NEBO -";

/* The title of the label to dismiss the alert when presenting user facing error messages */
"ErrorRecovery.Alert.OK" = "OK";

/* The title of the label to decline attempting error recovery */
"ErrorRecovery.Cancel" = "Zrušit";

/* The fallback message to display to recover invalidated tokens */
"ErrorRecovery.Login.Suggestion" = "Pokud se chcete ke svému Facebook účtu znovu připojit, přihlaste se k této aplikaci ještě jednou.";

/* The title of the label to start attempting error recovery */
"ErrorRecovery.OK" = "OK";

/* The fallback message to display to retry transient errors */
"ErrorRecovery.Transient.Suggestion" = "Server je dočasně zaneprázdněný, zkuste to znovu.";

/* The label for the FBSDKLoginButton action sheet to cancel logging out */
"LoginButton.CancelLogout" = "Zrušit";

/* The label for the FBSDKLoginButton action sheet to confirm logging out */
"LoginButton.ConfirmLogOut" = "Odhlásit";

/* The fallback string for the FBSDKLoginButton label when the user name is not available yet */
"LoginButton.LoggedIn" = "Přihlášen(a) přes Facebook";

/* The format string for the FBSDKLoginButton label when the user is logged in */
"LoginButton.LoggedInAs" = "Přihlášen(a) jako %@";

/* The short label for the FBSDKLoginButton when the user is currently logged out */
"LoginButton.LogIn" = "Přihlásit";

/* The long label for the FBSDKLoginButton when the user is currently logged out */
"LoginButton.LogInContinue" = "Pokračovat přes Facebook";

/* The long label for the FBSDKLoginButton when the user is currently logged out */
"LoginButton.LogInLong" = "Přihlásit se přes Facebook";

/* The label for the FBSDKLoginButton when the user is currently logged in */
"LoginButton.LogOut" = "Odhlásit";

/* The user facing error message when the app slider has been disabled and login fails. */
"LoginError.SystemAccount.Disabled" = "Facebook účtu nebylo oprávnění uděleno. Ověřte nastavení zařízení.";

/* The user facing error message when the Accounts framework encounters a network error. */
"LoginError.SystemAccount.Network" = "K Facebooku se nedá připojit. Zkontrolujte připojení k síti a zkuste to znovu.";

/* The user facing error message when the device Facebook account password is incorrect and login fails. */
"LoginError.SystemAccount.PasswordChange" = "Vaše heslo na Facebook je změněné. Pokud chcete heslo potvrdit, přejděte do Nastavení &gt; Facebook a klepněte na své jméno.";

/* The user facing error message when the device Facebook account is unavailable and login fails. */
"LoginError.SystemAccount.Unavailable" = "V tomto zařízení není Facebook účet konfigurovaný.";

/* The user facing error message when the Facebook account signed in to the Accounts framework becomes unconfirmed. */
"LoginError.SystemAccount.UnconfirmedUser" = "Váš účet není potvrzený. Přihlaste se na www. facebook.com a postupujte podle uvedených pokynů.";

/* The user facing error message when the Facebook account signed in to the Accounts framework has been checkpointed. */
"LoginError.SystemAccount.UserCheckpointed" = "Do aplikací se v této chvíli přihlásit nemůžete. Přihlaste se na www. facebook.com a postupujte podle uvedených pokynů.";

/* The message of the FBSDKLoginTooltipView */
"LoginTooltip.Message" = "Je jen na vás, které informace chcete s aplikacemi sdílet.";

/* Title of the web dialog that prompts the user to log in to Facebook. */
"LoginWeb.LogInTitle" = "Přihlásit";

/* The label for FBSDKSendButton */
"SendButton.Send" = "Odeslat";

/* The label for FBSDKShareButton */
"ShareButton.Share" = "Sdílet";

/* Prompts a person if this is their current account */
"SmartLogin.NotYou" = "Nejste to vy?";

/* Text on a button that a person presses to confirm that they are finished with the login experience */
"SmartLogin.ConfirmationTitle" = "Potvrdit přihlášení";

/* Text on a button that lets a person continue with their name linked to a Facebook account (Name = %@) */
"SmartLogin.Continue" = "Pokračovat jako %@";
