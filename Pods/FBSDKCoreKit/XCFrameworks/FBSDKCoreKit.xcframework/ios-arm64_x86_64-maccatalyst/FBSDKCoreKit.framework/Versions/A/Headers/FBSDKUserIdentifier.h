/*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 * All rights reserved.
 *
 * This source code is licensed under the license found in the
 * LICENSE file in the root directory of this source tree.
 */

#import <Foundation/Foundation.h>

#if !TARGET_OS_TV

NS_ASSUME_NONNULL_BEGIN

/// A unique identifier for an end user.
typedef NSString *FBSDKUserIdentifier NS_SWIFT_NAME(UserIdentifier) NS_SWIFT_BRIDGED_TYPEDEF;

NS_ASSUME_NONNULL_END

#endif
