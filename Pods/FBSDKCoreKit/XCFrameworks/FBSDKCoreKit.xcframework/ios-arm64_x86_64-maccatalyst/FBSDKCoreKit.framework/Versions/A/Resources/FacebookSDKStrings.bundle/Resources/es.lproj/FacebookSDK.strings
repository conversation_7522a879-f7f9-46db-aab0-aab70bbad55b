/* Prompts a person to go to the URL listed to enter the confirmation code that is presented to them above the given string. */
"DeviceLogin.LogInPrompt" = "Visita %@ e ingresa el código que se muestra arriba.";

/* Prompts a person that the next thing they need to do to finish connecting their Smart TV and Facebook application is to navigate to their Facebook application on their mobile device and look through their notifications for a message about the connection being formed */
"DeviceLogin.SmartLogInPrompt" = "Para conectar tu cuenta, abre la aplicación de Facebook en tu dispositivo móvil y comprueba las notificaciones.";

/* Displayed as a separator between two options. First option is on a line above this, and second option is below */
"DeviceLogin.SmartLogInOrLabel" = "- O -";

/* The title of the label to dismiss the alert when presenting user facing error messages */
"ErrorRecovery.Alert.OK" = "Aceptar";

/* The title of the label to decline attempting error recovery */
"ErrorRecovery.Cancel" = "Cancelar";

/* The fallback message to display to recover invalidated tokens */
"ErrorRecovery.Login.Suggestion" = "Inicia sesión de nuevo en esta aplicación para volver a conectarte a tu cuenta de Facebook.";

/* The title of the label to start attempting error recovery */
"ErrorRecovery.OK" = "Aceptar";

/* The fallback message to display to retry transient errors */
"ErrorRecovery.Transient.Suggestion" = "El servidor está temporalmente ocupado. Vuelve a intentarlo.";

/* The label for the FBSDKLoginButton action sheet to cancel logging out */
"LoginButton.CancelLogout" = "Cancelar";

/* The label for the FBSDKLoginButton action sheet to confirm logging out */
"LoginButton.ConfirmLogOut" = "Cerrar sesión";

/* The fallback string for the FBSDKLoginButton label when the user name is not available yet */
"LoginButton.LoggedIn" = "Sesión iniciada con Facebook";

/* The format string for the FBSDKLoginButton label when the user is logged in */
"LoginButton.LoggedInAs" = "Sesión iniciada como %@";

/* The short label for the FBSDKLoginButton when the user is currently logged out */
"LoginButton.LogIn" = "Inicio de sesión";

/* The long label for the FBSDKLoginButton when the user is currently logged out */
"LoginButton.LogInContinue" = "Continuar con Facebook";

/* The long label for the FBSDKLoginButton when the user is currently logged out */
"LoginButton.LogInLong" = "Iniciar sesión con Facebook";

/* The label for the FBSDKLoginButton when the user is currently logged in */
"LoginButton.LogOut" = "Salir";

/* The user facing error message when the app slider has been disabled and login fails. */
"LoginError.SystemAccount.Disabled" = "No se concedió acceso a la cuenta de Facebook. Verifica la configuración del dispositivo.";

/* The user facing error message when the Accounts framework encounters a network error. */
"LoginError.SystemAccount.Network" = "No es posible conectarse a Facebook. Comprueba tu conexión a internet y vuelve a intentarlo.";

/* The user facing error message when the device Facebook account password is incorrect and login fails. */
"LoginError.SystemAccount.PasswordChange" = "Cambió tu contraseña de Facebook. Para confirmar tu contraseña, abre Configuración &gt; Facebook y toca tu nombre.";

/* The user facing error message when the device Facebook account is unavailable and login fails. */
"LoginError.SystemAccount.Unavailable" = "No se configuró la cuenta de Facebook en el dispositivo.";

/* The user facing error message when the Facebook account signed in to the Accounts framework becomes unconfirmed. */
"LoginError.SystemAccount.UnconfirmedUser" = "Tu cuenta no está confirmada. Inicia sesión en www.facebook.com y sigue las instrucciones.";

/* The user facing error message when the Facebook account signed in to the Accounts framework has been checkpointed. */
"LoginError.SystemAccount.UserCheckpointed" = "No puedes iniciar sesión en las aplicaciones en este momento. Inicia sesión en www.facebook.com y sigue las instrucciones.";

/* The message of the FBSDKLoginTooltipView */
"LoginTooltip.Message" = "Tú decides: elige qué información quieres compartir con las aplicaciones.";

/* Title of the web dialog that prompts the user to log in to Facebook. */
"LoginWeb.LogInTitle" = "Inicio de sesión";

/* The label for FBSDKSendButton */
"SendButton.Send" = "Enviar";

/* The label for FBSDKShareButton */
"ShareButton.Share" = "Compartir";

/* Prompts a person if this is their current account */
"SmartLogin.NotYou" = "¿No eres tú?";

/* Text on a button that a person presses to confirm that they are finished with the login experience */
"SmartLogin.ConfirmationTitle" = "Confirmar inicio de sesión";

/* Text on a button that lets a person continue with their name linked to a Facebook account (Name = %@) */
"SmartLogin.Continue" = "Continuar como %@";
