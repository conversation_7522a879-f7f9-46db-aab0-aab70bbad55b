/* Prompts a person to go to the URL listed to enter the confirmation code that is presented to them above the given string. */
"DeviceLogin.LogInPrompt" = "%@ സന്ദർശിച്ച്‌ മുകളിൽ കാണിച്ച‌ കോഡ്‌ നൽകുക.";

/* Prompts a person that the next thing they need to do to finish connecting their Smart TV and Facebook application is to navigate to their Facebook application on their mobile device and look through their notifications for a message about the connection being formed */
"DeviceLogin.SmartLogInPrompt" = "നിങ്ങളുടെ അക്കൗണ്ട്‌ കണക്‌റ്റുചെയ്യാൻ, നിങ്ങളുടെ മൊബൈൽ ഉപകരണത്തിലെ Facebook ആപ്പ്‌ തുറന്ന്‌ അറിയിപ്പുകൾ പരിശോധിക്കുക.";

/* Displayed as a separator between two options. First option is on a line above this, and second option is below */
"DeviceLogin.SmartLogInOrLabel" = "- അല്ലെങ്കിൽ -";

/* The title of the label to dismiss the alert when presenting user facing error messages */
"ErrorRecovery.Alert.OK" = "ശരി";

/* The title of the label to decline attempting error recovery */
"ErrorRecovery.Cancel" = "റദ്ദാക്കുക";

/* The fallback message to display to recover invalidated tokens */
"ErrorRecovery.Login.Suggestion" = "നിങ്ങളുടെ Facebook അക്കൗണ്ടുമായി വീണ്ടും കണക്‌റ്റുചെയ്യുന്നതിന് ഈ ആപ്ലിക്കേഷനിൽ വീണ്ടും ലോഗിൻ ചെയ്യുക.";

/* The title of the label to start attempting error recovery */
"ErrorRecovery.OK" = "ശരി";

/* The fallback message to display to retry transient errors */
"ErrorRecovery.Transient.Suggestion" = "സെർവർ താൽക്കാലികമായി തിരക്കിലാണ്, വീണ്ടും ശ്രമിക്കുക.";

/* The label for the FBSDKLoginButton action sheet to cancel logging out */
"LoginButton.CancelLogout" = "റദ്ദാക്കുക";

/* The label for the FBSDKLoginButton action sheet to confirm logging out */
"LoginButton.ConfirmLogOut" = "ലോഗ്ഔട്ട് ചെയ്യുക";

/* The fallback string for the FBSDKLoginButton label when the user name is not available yet */
"LoginButton.LoggedIn" = "Facebook ഉപയോഗിച്ച് ലോഗിൻ ചെയ്‌തു";

/* The format string for the FBSDKLoginButton label when the user is logged in */
"LoginButton.LoggedInAs" = "%@ എന്നതായി ലോഗിൻ ചെയ്‌തു";

/* The short label for the FBSDKLoginButton when the user is currently logged out */
"LoginButton.LogIn" = "ലോഗിൻ ചെയ്യുക";

/* The long label for the FBSDKLoginButton when the user is currently logged out */
"LoginButton.LogInContinue" = "Facebook ഉപയോഗിച്ച് തുടരുക";

/* The long label for the FBSDKLoginButton when the user is currently logged out */
"LoginButton.LogInLong" = "Facebook ഉപയോഗിച്ച് ലോഗിൻ ചെയ്യുക";

/* The label for the FBSDKLoginButton when the user is currently logged in */
"LoginButton.LogOut" = "ലോഗ്ഔട്ട് ചെയ്യുക";

/* The user facing error message when the app slider has been disabled and login fails. */
"LoginError.SystemAccount.Disabled" = "Facebook അക്കൗണ്ടിലേക്ക് ആക്‌സസ് നൽകിയിട്ടില്ല. ഉപകരണ ക്രമീകരണങ്ങൾ പരിശോധിച്ചുറപ്പിക്കുക.";

/* The user facing error message when the Accounts framework encounters a network error. */
"LoginError.SystemAccount.Network" = "Facebook-ലേക്ക് കണക്‌റ്റുചെയ്യാനായില്ല. നിങ്ങളുടെ നെറ്റ്‌വർക്ക് കണക്ഷൻ പരിശോധിച്ച് വീണ്ടും ശ്രമിക്കുക.";

/* The user facing error message when the device Facebook account password is incorrect and login fails. */
"LoginError.SystemAccount.PasswordChange" = "നിങ്ങളുടെ Facebook പാസ്‌വേഡ് മാറി. പാ‌സ്‌വേഡ് സ്ഥിരീകരിക്കുന്നതിന് ക്രമീകരണങ്ങൾ &gt; Facebook എന്നത് തുറന്ന് പേര് ടാപ്പുചെയ്യുക.";

/* The user facing error message when the device Facebook account is unavailable and login fails. */
"LoginError.SystemAccount.Unavailable" = "ഉപകരണത്തിൽ Facebook അക്കൗണ്ട് കോൺഫിഗർ ചെയ്‌തിട്ടില്ല.";

/* The user facing error message when the Facebook account signed in to the Accounts framework becomes unconfirmed. */
"LoginError.SystemAccount.UnconfirmedUser" = "അക്കൗണ്ട് സ്ഥിരീകരിച്ചു. www.facebook.com എന്നതിലേക്ക് ലോഗിൻ ചെയ്‌ത് തന്നിരിക്കുന്ന നിർദ്ദേശങ്ങൾ പാലിക്കുക.";

/* The user facing error message when the Facebook account signed in to the Accounts framework has been checkpointed. */
"LoginError.SystemAccount.UserCheckpointed" = "നിങ്ങൾക്ക് ഈ സമയം ആപ്ലിക്കേഷനുകളിലേക്ക് ലോഗിൻ ചെയ്യാനാവില്ല. www.facebook.com എന്നതിലേക്ക് ലോഗിൻ ചെയ്‌ത് തന്നിരിക്കുന്ന നിർദ്ദേശങ്ങൾ പാലിക്കുക.";

/* The message of the FBSDKLoginTooltipView */
"LoginTooltip.Message" = "നിങ്ങളുടെ നിയന്ത്രണത്തിലാണ് - നിങ്ങൾക്ക് ആപ്‌സുമായി ഏതെല്ലാം വിവരങ്ങൾ പങ്കിടണമെന്ന് തിരഞ്ഞെടുക്കുക.";

/* Title of the web dialog that prompts the user to log in to Facebook. */
"LoginWeb.LogInTitle" = "ലോഗിൻ ചെയ്യുക";

/* The label for FBSDKSendButton */
"SendButton.Send" = "അയയ്‌ക്കുക";

/* The label for FBSDKShareButton */
"ShareButton.Share" = "പങ്കിടൂ";

/* Prompts a person if this is their current account */
"SmartLogin.NotYou" = "നിങ്ങൾ അല്ലേ?";

/* Text on a button that a person presses to confirm that they are finished with the login experience */
"SmartLogin.ConfirmationTitle" = "ലോഗിൻ സ്ഥിരീകരിക്കുക";

/* Text on a button that lets a person continue with their name linked to a Facebook account (Name = %@) */
"SmartLogin.Continue" = "%@ ആയി തുടരുക";
