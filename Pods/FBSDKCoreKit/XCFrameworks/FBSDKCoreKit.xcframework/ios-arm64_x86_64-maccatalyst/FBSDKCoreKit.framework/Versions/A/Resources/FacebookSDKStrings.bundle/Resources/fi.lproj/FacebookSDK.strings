/* Prompts a person to go to the URL listed to enter the confirmation code that is presented to them above the given string. */
"DeviceLogin.LogInPrompt" = "<PERSON><PERSON><PERSON> osoitteeseen %@ ja kirjoita oheinen koodi sivulle.";

/* Prompts a person that the next thing they need to do to finish connecting their Smart TV and Facebook application is to navigate to their Facebook application on their mobile device and look through their notifications for a message about the connection being formed */
"DeviceLogin.SmartLogInPrompt" = "Pääset liittämään käyttäjätilisi avaamalla Facebook-sovelluksen mobiililaitteellasi ja tarkistamalla ilmoitukset.";

/* Displayed as a separator between two options. First option is on a line above this, and second option is below */
"DeviceLogin.SmartLogInOrLabel" = "- TAI -";

/* The title of the label to dismiss the alert when presenting user facing error messages */
"ErrorRecovery.Alert.OK" = "OK";

/* The title of the label to decline attempting error recovery */
"ErrorRecovery.Cancel" = "Peruuta";

/* The fallback message to display to recover invalidated tokens */
"ErrorRecovery.Login.Suggestion" = "Kirjaudu tähän sovellukseen uudelleen, jotta voit yhdistää Facebook-tilisi uudelleen.";

/* The title of the label to start attempting error recovery */
"ErrorRecovery.OK" = "OK";

/* The fallback message to display to retry transient errors */
"ErrorRecovery.Transient.Suggestion" = "Palvelin on tilapäisesti varattu, yritä uudelleen.";

/* The label for the FBSDKLoginButton action sheet to cancel logging out */
"LoginButton.CancelLogout" = "Peruuta";

/* The label for the FBSDKLoginButton action sheet to confirm logging out */
"LoginButton.ConfirmLogOut" = "Kirjaudu ulos";

/* The fallback string for the FBSDKLoginButton label when the user name is not available yet */
"LoginButton.LoggedIn" = "Sisäänkirjautunut Facebookin avulla";

/* The format string for the FBSDKLoginButton label when the user is logged in */
"LoginButton.LoggedInAs" = "Sisäänkirjautunut nimellä %@";

/* The short label for the FBSDKLoginButton when the user is currently logged out */
"LoginButton.LogIn" = "Kirjaudu sisään";

/* The long label for the FBSDKLoginButton when the user is currently logged out */
"LoginButton.LogInContinue" = "Jatka Facebook-tunnuksilla";

/* The long label for the FBSDKLoginButton when the user is currently logged out */
"LoginButton.LogInLong" = "Kirjaudu sisään Facebookin avulla";

/* The label for the FBSDKLoginButton when the user is currently logged in */
"LoginButton.LogOut" = "Kirjaudu ulos";

/* The user facing error message when the app slider has been disabled and login fails. */
"LoginError.SystemAccount.Disabled" = "Pääsyä Facebook-tiliin ei ole myönnetty. Tarkista laiteasetukset.";

/* The user facing error message when the Accounts framework encounters a network error. */
"LoginError.SystemAccount.Network" = "Yhteyttä Facebookiin ei voi muodostaa. Tarkista verkkoyhteys ja yritä sitten uudelleen.";

/* The user facing error message when the device Facebook account password is incorrect and login fails. */
"LoginError.SystemAccount.PasswordChange" = "Facebook-salasanasi on vaihdettu. Vahvista salasanasi avaamalla Asetukset &gt; Facebook ja napauttamalla nimeäsi.";

/* The user facing error message when the device Facebook account is unavailable and login fails. */
"LoginError.SystemAccount.Unavailable" = "Facebook-tiliä ei ole määritetty laitteessa.";

/* The user facing error message when the Facebook account signed in to the Accounts framework becomes unconfirmed. */
"LoginError.SystemAccount.UnconfirmedUser" = "Tiliäsi ei ole vahvistettu. Kirjaudu sisään osoitteeseen www.facebook.com ja noudata annettuja ohjeita.";

/* The user facing error message when the Facebook account signed in to the Accounts framework has been checkpointed. */
"LoginError.SystemAccount.UserCheckpointed" = "Et pysty kirjautumaan sovelluksiin tällä hetkellä. Kirjaudu sisään osoitteeseen www.facebook.com ja noudata annettuja ohjeita.";

/* The message of the FBSDKLoginTooltipView */
"LoginTooltip.Message" = "Sinä voit valita, mitä tietoja jaat sovelluksille.";

/* Title of the web dialog that prompts the user to log in to Facebook. */
"LoginWeb.LogInTitle" = "Kirjaudu sisään";

/* The label for FBSDKSendButton */
"SendButton.Send" = "Lähetä";

/* The label for FBSDKShareButton */
"ShareButton.Share" = "Jaa";

/* Prompts a person if this is their current account */
"SmartLogin.NotYou" = "Etkö tämä ole sinä?";

/* Text on a button that a person presses to confirm that they are finished with the login experience */
"SmartLogin.ConfirmationTitle" = "Vahvista sisäänkirjautuminen";

/* Text on a button that lets a person continue with their name linked to a Facebook account (Name = %@) */
"SmartLogin.Continue" = "Jatka nimellä %@";
