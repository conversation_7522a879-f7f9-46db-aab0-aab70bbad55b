# iOS Hormone Life


#### 开发语言
1. 主要编程语言： Swift
2. 部分库可能会使用到Object-C

#### 国际化语言
1. 程序默认使用英文，如需使用其他国语言，使用系统自带接口NSLocalizedString实现国际化语言。

#### 框架
1. UIKit: 用于构建用户界面的基础框架。
2. Alamofire: 用于网络请求。
3. SnapKit: 用于快速实现UI布局框架。
4. DGCharts: 用于实现图表样式的需求。
5. EventKit: 系统自带用于处理日历事件的库。
6. SwiftyJSON： 用于辅助处理数据model的框架。
7. FMDB: 处理部分轻量缓存框架。
8. SDWebImage： 处理图片缓存框架。
9. Photos: 处理本地图片框架。
10. MapKit/CLLocationManager: 系统自带框架，用于可能需要定位的实现。

#### 缓存
1. FMDB: 处理一些轻量级的缓存需求；
2. UserDefault: 处理一些需要保存在活跃周期的少量数据；
3. KeyChain: 缓存一些删掉app也能保留的少量数据。

#### 布局
1. 利用 SnapKit 实现UI 布局；
2. 使用系统自带 NSLayoutAnchor 实现布局；
3. 使用 xib/ UIStoryBoard实现布局。

#### 架构设计
1. MVVM (模型-视图-视图模型)： 结构清晰，层次明了，便于分离，减少冗余。
2. 依赖注入：为了减少冗余代码，复用可重用的代码，将可复用的代码抽离封装成各种组件，在组件之间通过依赖注入来管理依赖关系。
3. 单例： 在全局中使用单例，可方便保证在整个应用生命周期中使用同一个值。
4. 协议：使用协议/委托/代理模式在类与类之前传递消息。

