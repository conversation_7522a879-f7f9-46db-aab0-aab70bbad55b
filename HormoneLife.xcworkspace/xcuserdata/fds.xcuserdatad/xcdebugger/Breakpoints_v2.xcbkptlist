<?xml version="1.0" encoding="UTF-8"?>
<Bucket
   uuid = "F933624C-16CB-4DFC-8F61-29224C0D245E"
   type = "0"
   version = "2.0">
   <Breakpoints>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "5D169E87-3341-43B8-A6B8-E441A6B1BDE8"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "HormoneLife/UserTests/ViewModels/UserTestViewModel.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "33"
            endingLineNumber = "33"
            landmarkName = "uploadTest(imageUrl:pageType:success:failure:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "6E74E2B1-9E47-437A-AFA6-1E1926C2F712"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "HormoneLife/NetworkHelp/NetWorkHandle.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "295"
            endingLineNumber = "295"
            landmarkName = "requestPostForFullUrl(_:parameters:showHub:success:failure:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "B9CAEEE8-521B-4CD7-A24D-57F37354F669"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "HormoneLife/NetworkHelp/NetWorkHandle.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "286"
            endingLineNumber = "286"
            landmarkName = "requestPostForFullUrl(_:parameters:showHub:success:failure:)"
            landmarkType = "7">
            <Locations>
               <Location
                  uuid = "B9CAEEE8-521B-4CD7-A24D-57F37354F669 - 79c67a2d572dee1e"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "closure #1 (Alamofire.DataResponse&lt;Any, Alamofire.AFError&gt;) -&gt; () in static HormoneLife.NetWorkHandle.requestPostForFullUrl(_: Swift.String, parameters: Swift.Optional&lt;Swift.Dictionary&lt;Swift.String, Any&gt;&gt;, showHub: Swift.Bool, success: (Swift.Dictionary&lt;Swift.String, Any&gt;) -&gt; (), failure: (Swift.Optional&lt;Swift.String&gt;) -&gt; ()) -&gt; ()"
                  moduleName = "HormoneLife"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Documents/spoil/%E4%B8%87%E5%AD%9A/HormoneLife/NetworkHelp/NetWorkHandle.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "286"
                  endingLineNumber = "286"
                  offsetFromSymbolStart = "992">
               </Location>
               <Location
                  uuid = "B9CAEEE8-521B-4CD7-A24D-57F37354F669 - 6b9068284d69921f"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "closure #2 @Swift.MainActor () -&gt; () in closure #1 (Alamofire.DataResponse&lt;Any, Alamofire.AFError&gt;) -&gt; () in static HormoneLife.NetWorkHandle.requestPostForFullUrl(_: Swift.String, parameters: Swift.Optional&lt;Swift.Dictionary&lt;Swift.String, Any&gt;&gt;, showHub: Swift.Bool, success: (Swift.Dictionary&lt;Swift.String, Any&gt;) -&gt; (), failure: (Swift.Optional&lt;Swift.String&gt;) -&gt; ()) -&gt; ()"
                  moduleName = "HormoneLife"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Documents/spoil/%E4%B8%87%E5%AD%9A/HormoneLife/NetworkHelp/NetWorkHandle.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "287"
                  endingLineNumber = "287"
                  offsetFromSymbolStart = "60">
               </Location>
            </Locations>
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "01E10AF3-8855-4EDD-B570-4CEB252D540E"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "HormoneLife/NetworkHelp/NetWorkHandle.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "278"
            endingLineNumber = "278"
            landmarkName = "requestPostForFullUrl(_:parameters:showHub:success:failure:)"
            landmarkType = "7">
            <Locations>
               <Location
                  uuid = "01E10AF3-8855-4EDD-B570-4CEB252D540E - 79c67a2d572def16"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "closure #1 (Alamofire.DataResponse&lt;Any, Alamofire.AFError&gt;) -&gt; () in static HormoneLife.NetWorkHandle.requestPostForFullUrl(_: Swift.String, parameters: Swift.Optional&lt;Swift.Dictionary&lt;Swift.String, Any&gt;&gt;, showHub: Swift.Bool, success: (Swift.Dictionary&lt;Swift.String, Any&gt;) -&gt; (), failure: (Swift.Optional&lt;Swift.String&gt;) -&gt; ()) -&gt; ()"
                  moduleName = "HormoneLife"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Documents/spoil/%E4%B8%87%E5%AD%9A/HormoneLife/NetworkHelp/NetWorkHandle.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "278"
                  endingLineNumber = "278"
                  offsetFromSymbolStart = "2848">
               </Location>
               <Location
                  uuid = "01E10AF3-8855-4EDD-B570-4CEB252D540E - 318c32b0df102916"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "closure #1 @Swift.MainActor () -&gt; () in closure #1 (Alamofire.DataResponse&lt;Any, Alamofire.AFError&gt;) -&gt; () in static HormoneLife.NetWorkHandle.requestPostForFullUrl(_: Swift.String, parameters: Swift.Optional&lt;Swift.Dictionary&lt;Swift.String, Any&gt;&gt;, showHub: Swift.Bool, success: (Swift.Dictionary&lt;Swift.String, Any&gt;) -&gt; (), failure: (Swift.Optional&lt;Swift.String&gt;) -&gt; ()) -&gt; ()"
                  moduleName = "HormoneLife"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Documents/spoil/%E4%B8%87%E5%AD%9A/HormoneLife/NetworkHelp/NetWorkHandle.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "279"
                  endingLineNumber = "279"
                  offsetFromSymbolStart = "88">
               </Location>
            </Locations>
         </BreakpointContent>
      </BreakpointProxy>
   </Breakpoints>
</Bucket>
