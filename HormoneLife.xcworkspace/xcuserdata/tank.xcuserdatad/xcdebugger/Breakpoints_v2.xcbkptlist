<?xml version="1.0" encoding="UTF-8"?>
<Bucket
   uuid = "C61F68F9-B42D-4B6C-AEB9-79A54B7580CE"
   type = "0"
   version = "2.0">
   <Breakpoints>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "EC82124B-264A-42E3-BC4C-77DE8B0F18F6"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "HormoneLife/Charts/CircleDemoView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "177"
            endingLineNumber = "177"
            landmarkName = "setupUIWith(_:testDetail:)"
            landmarkType = "7">
            <Locations>
               <Location
                  uuid = "EC82124B-264A-42E3-BC4C-77DE8B0F18F6 - 62fa999f90ca75a"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "HormoneLife.CircleDemoView.initMovingViewPosition(CoreGraphics.CGFloat) -&gt; ()"
                  moduleName = "HormoneLife"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Documents/Swift_Dev/HormoneLife/HormoneLife/Charts/CircleDemoView.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "92"
                  endingLineNumber = "92"
                  offsetFromSymbolStart = "148">
               </Location>
               <Location
                  uuid = "EC82124B-264A-42E3-BC4C-77DE8B0F18F6 - 62fa999f90ca75a"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "HormoneLife.CircleDemoView.initMovingViewPosition(CoreGraphics.CGFloat) -&gt; ()"
                  moduleName = "HormoneLife"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Documents/Swift_Dev/HormoneLife/HormoneLife/Charts/CircleDemoView.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "92"
                  endingLineNumber = "92"
                  offsetFromSymbolStart = "128">
               </Location>
            </Locations>
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "1B2913FC-277B-43EA-926E-DCC8D9AB883B"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "HormoneLife/Charts/CircleDemoView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "180"
            endingLineNumber = "180"
            landmarkName = "setupUIWith(_:testDetail:)"
            landmarkType = "7">
            <Locations>
               <Location
                  uuid = "1B2913FC-277B-43EA-926E-DCC8D9AB883B - 62fa999f90ca737"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "HormoneLife.CircleDemoView.initMovingViewPosition(CoreGraphics.CGFloat) -&gt; ()"
                  moduleName = "HormoneLife"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Documents/Swift_Dev/HormoneLife/HormoneLife/Charts/CircleDemoView.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "95"
                  endingLineNumber = "95"
                  offsetFromSymbolStart = "328">
               </Location>
               <Location
                  uuid = "1B2913FC-277B-43EA-926E-DCC8D9AB883B - 62fa999f90ca737"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "HormoneLife.CircleDemoView.initMovingViewPosition(CoreGraphics.CGFloat) -&gt; ()"
                  moduleName = "HormoneLife"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Documents/Swift_Dev/HormoneLife/HormoneLife/Charts/CircleDemoView.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "95"
                  endingLineNumber = "95"
                  offsetFromSymbolStart = "332">
               </Location>
            </Locations>
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "08B037AB-2B5C-4FE9-B88A-ED446BFDD29B"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "HormoneLife/TabBarVCs/HomeViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "76"
            endingLineNumber = "76"
            landmarkName = "fetchHomeData()"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "EC60ABF6-92B7-4251-BCCD-437E6B3E62C6"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "HormoneLife/TabBarVCs/HomeViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "205"
            endingLineNumber = "205"
            landmarkName = "didTap(_:)"
            landmarkType = "7">
            <Locations>
               <Location
                  uuid = "EC60ABF6-92B7-4251-BCCD-437E6B3E62C6 - bf92818d1300501b"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "HormoneLife.HomeViewController.didTapCell(type: HormoneLife.SamplePaperView.PaperType) -&gt; ()"
                  moduleName = "HormoneLife"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Documents/Swift_Dev/HormoneLife/HormoneLife/TabBarVCs/HomeViewController.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "186"
                  endingLineNumber = "186"
                  offsetFromSymbolStart = "33">
               </Location>
               <Location
                  uuid = "EC60ABF6-92B7-4251-BCCD-437E6B3E62C6 - bf92818d13005038"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "HormoneLife.HomeViewController.didTapCell(type: HormoneLife.SamplePaperView.PaperType) -&gt; ()"
                  moduleName = "HormoneLife"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Documents/Swift_Dev/HormoneLife/HormoneLife/TabBarVCs/HomeViewController.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "185"
                  endingLineNumber = "185"
                  offsetFromSymbolStart = "33">
               </Location>
            </Locations>
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "15BDA6ED-878B-48D4-BBE6-4C29088FC28D"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "HormoneLife/TabBarVCs/HomeCameraPopupView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "63"
            endingLineNumber = "63"
            landmarkName = "didTapClose()"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "C5475042-C93E-4CB3-9175-17E2C4F9FB30"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "HormoneLife/LoginAndSignUp/SignUpViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "303"
            endingLineNumber = "303"
            landmarkName = "textView(_:shouldInteractWith:in:interaction:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "87EDE43A-**************-6663AAD09109"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "HormoneLife/Profile/TagsTableViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "84"
            endingLineNumber = "84"
            landmarkName = "didAddTag(_:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "A5A5564D-55D2-4527-8428-EE20E11185E1"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "HormoneLife/TabBarVCs/CalendarPopupSelectionViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "281"
            endingLineNumber = "281"
            landmarkName = "textField(_:shouldChangeCharactersIn:replacementString:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "8E579E55-E0EB-4D9B-A5F4-3842ABB12DCA"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "HormoneLife/TabBarVCs/CalendarPopupSelectionViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "274"
            endingLineNumber = "274"
            landmarkName = "textField(_:shouldChangeCharactersIn:replacementString:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "121C9C22-B17A-444F-AAB8-A32ED33A6989"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "HormoneLife/Profile/PersonalInfoViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "152"
            endingLineNumber = "152"
            landmarkName = "didTapFirstAvarageIcon(_:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "8C379885-53D6-48A7-B5D2-E1F11EAFA009"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "HormoneLife/Profile/PersonalInfoViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "156"
            endingLineNumber = "156"
            landmarkName = "didTapSecondAvarageIcon(_:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "C4D8EE2A-C285-44FF-A56F-2E4974A94A80"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "HormoneLife/Profile/PersonalInfoViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "160"
            endingLineNumber = "160"
            landmarkName = "didTapOvulationIcon(_:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "6F70A167-466F-47DF-BB78-B13AC07958C6"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "HormoneLife/NetworkHelp/Interactor.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "48"
            endingLineNumber = "48"
            landmarkName = "userPeriodCycleByYear(userId:userName:title:searchStartCycleTime:searchEndCycleTime:completion:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "C463BA13-8B56-48F1-BEB8-006C4EA63AE2"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "HormoneLife/common/Calender/AppCalendarView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "299"
            endingLineNumber = "299"
            landmarkName = "handleCellConfiguration(cell:cellState:indexPath:date:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "3BDB8C0E-9620-4FA6-B5C8-28C5402A3842"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "HormoneLife/common/Calender/AppCalendarView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "296"
            endingLineNumber = "296"
            landmarkName = "handleCellConfiguration(cell:cellState:indexPath:date:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "3FA2A922-00F8-4136-88E9-7F4E107F1BD1"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "HormoneLife/common/Extensions/String+Extension.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "36"
            endingLineNumber = "36"
            landmarkName = "covertDate(with:)"
            landmarkType = "7">
            <Locations>
               <Location
                  uuid = "3FA2A922-00F8-4136-88E9-7F4E107F1BD1 - 92b7bd9a4e85955"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "Swift.String.covertDate(with: Swift.String) -&gt; Foundation.Date"
                  moduleName = "HormoneLife"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Documents/Swift_Dev/HormoneLife/HormoneLife/common/Extensions/String+Extension.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "36"
                  endingLineNumber = "36"
                  offsetFromSymbolStart = "1284">
               </Location>
               <Location
                  uuid = "3FA2A922-00F8-4136-88E9-7F4E107F1BD1 - 92b7bd9a4e85955"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "Swift.String.covertDate(with: Swift.String) -&gt; Foundation.Date"
                  moduleName = "HormoneLife"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Documents/Swift_Dev/HormoneLife/HormoneLife/common/Extensions/String+Extension.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "36"
                  endingLineNumber = "36"
                  offsetFromSymbolStart = "748">
               </Location>
            </Locations>
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "7FDFF727-3A54-4676-ACB7-1E595B4C5FA6"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "HormoneLife/TabBarVCs/CalendarTableViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "327"
            endingLineNumber = "327"
            landmarkName = "didTapAdd(_:)"
            landmarkType = "7">
            <Locations>
               <Location
                  uuid = "7FDFF727-3A54-4676-ACB7-1E595B4C5FA6 - b831563bd712440f"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "HormoneLife.CalendarTableViewController.cellDayMarkLabelType(view: Swift.Optional&lt;HormoneLife.AppCalendarCell&gt;, text: Swift.String, isSelected: Swift.Bool, indexPath: Foundation.IndexPath, date: Foundation.Date) -&gt; Swift.Array&lt;HormoneLife.CalendarLabel&gt;"
                  moduleName = "HormoneLife"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Documents/Swift_Dev/HormoneLife/HormoneLife/TabBarVCs/CalendarTableViewController.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "336"
                  endingLineNumber = "336"
                  offsetFromSymbolStart = "223">
               </Location>
               <Location
                  uuid = "7FDFF727-3A54-4676-ACB7-1E595B4C5FA6 - c45e8c42cbc137b8"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "closure #1 (HormoneLife.UserPeriodCycle) -&gt; () in HormoneLife.CalendarTableViewController.cellDayMarkLabelType(view: Swift.Optional&lt;HormoneLife.AppCalendarCell&gt;, text: Swift.String, isSelected: Swift.Bool, indexPath: Foundation.IndexPath, date: Foundation.Date) -&gt; Swift.Array&lt;HormoneLife.CalendarLabel&gt;"
                  moduleName = "HormoneLife"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Documents/Swift_Dev/HormoneLife/HormoneLife/TabBarVCs/CalendarTableViewController.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "337"
                  endingLineNumber = "337"
                  offsetFromSymbolStart = "1297">
               </Location>
            </Locations>
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "A979C7DA-4B92-4DA8-8CD5-6367EC29F8AE"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "HormoneLife/TabBarVCs/CalendarTableViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "111"
            endingLineNumber = "111"
            landmarkName = "handleRequestSuccessResult(refresh:count:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "DA6F0F0B-719B-46A9-BCB0-8DCF05A92A46"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "HormoneLife/TabBarVCs/CalendarLabelCellTableViewCell.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "310"
            endingLineNumber = "310"
            landmarkName = "dateState(detail:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "62EE4199-D032-4A77-911C-09F7475FDA4D"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "HormoneLife/TabBarVCs/CalendarTableViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "313"
            endingLineNumber = "313"
            landmarkName = "didTapExpandButton(_:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "5A56F797-96A2-4F2E-AB6F-DC217D0E122C"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "HormoneLife/TabBarVCs/CalendarTableViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "305"
            endingLineNumber = "305"
            landmarkName = "tableView(_:didSelectRowAt:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "CD0869EA-9FA2-426C-846D-2F82054BF36D"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "HormoneLife/common/Calender/AppCalendarView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "380"
            endingLineNumber = "380"
            landmarkName = "setupViewsOfCalendar(from:)"
            landmarkType = "7">
            <Locations>
               <Location
                  uuid = "CD0869EA-9FA2-426C-846D-2F82054BF36D - 3f0a815e481d7ada"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "HormoneLife.AppCalendarView.buttonNextHandler(__C.UIButton) -&gt; ()"
                  moduleName = "HormoneLife"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Documents/Swift_Dev/HormoneLife/HormoneLife/common/Calender/AppCalendarView.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "357"
                  endingLineNumber = "357"
                  offsetFromSymbolStart = "106">
               </Location>
               <Location
                  uuid = "CD0869EA-9FA2-426C-846D-2F82054BF36D - 3f0a815e481d7ada"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "HormoneLife.AppCalendarView.buttonNextHandler(__C.UIButton) -&gt; ()"
                  moduleName = "HormoneLife"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Documents/Swift_Dev/HormoneLife/HormoneLife/common/Calender/AppCalendarView.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "357"
                  endingLineNumber = "357"
                  offsetFromSymbolStart = "173">
               </Location>
            </Locations>
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "0EFCD819-13A3-45E8-AEB3-4E4544F2050F"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "HormoneLife/common/Calender/AppCalendarView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "386"
            endingLineNumber = "386"
            landmarkName = "buttonNextHandler(_:)"
            landmarkType = "7">
            <Locations>
               <Location
                  uuid = "0EFCD819-13A3-45E8-AEB3-4E4544F2050F - 9ddfdc507878e016"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "HormoneLife.AppCalendarView.buttonPrevHandler(__C.UIButton) -&gt; ()"
                  moduleName = "HormoneLife"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Documents/Swift_Dev/HormoneLife/HormoneLife/common/Calender/AppCalendarView.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "363"
                  endingLineNumber = "363"
                  offsetFromSymbolStart = "106">
               </Location>
               <Location
                  uuid = "0EFCD819-13A3-45E8-AEB3-4E4544F2050F - 9ddfdc507878e016"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "HormoneLife.AppCalendarView.buttonPrevHandler(__C.UIButton) -&gt; ()"
                  moduleName = "HormoneLife"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Documents/Swift_Dev/HormoneLife/HormoneLife/common/Calender/AppCalendarView.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "363"
                  endingLineNumber = "363"
                  offsetFromSymbolStart = "173">
               </Location>
            </Locations>
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "FBDEFFDF-8A38-4E24-A05D-0026A5E52878"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "HormoneLife/common/Calender/AppCalendarView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "451"
            endingLineNumber = "451"
            landmarkName = "calendarDidScroll(_:)"
            landmarkType = "7">
            <Locations>
               <Location
                  uuid = "FBDEFFDF-8A38-4E24-A05D-0026A5E52878 - efc183ef016412e2"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "HormoneLife.AppCalendarView.calendarDidScroll(JTAppleCalendar.JTACMonthView) -&gt; ()"
                  moduleName = "HormoneLife"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Documents/Swift_Dev/HormoneLife/HormoneLife/common/Calender/AppCalendarView.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "429"
                  endingLineNumber = "429"
                  offsetFromSymbolStart = "722">
               </Location>
               <Location
                  uuid = "FBDEFFDF-8A38-4E24-A05D-0026A5E52878 - efc183ef016412e2"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "HormoneLife.AppCalendarView.calendarDidScroll(JTAppleCalendar.JTACMonthView) -&gt; ()"
                  moduleName = "HormoneLife"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Documents/Swift_Dev/HormoneLife/HormoneLife/common/Calender/AppCalendarView.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "429"
                  endingLineNumber = "429"
                  offsetFromSymbolStart = "906">
               </Location>
            </Locations>
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "8653E9B2-BADE-4857-AE03-2C18F6982EFD"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "HormoneLife/Charts/ChartsTitleView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "357"
            endingLineNumber = "357"
            landmarkName = "setIsSelcted(isSelected:isUseByDefault:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "808B8987-3BA1-41B5-B30B-A28A287726EB"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "HormoneLife/UserTests/RecordTestsViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "70"
            endingLineNumber = "70"
            landmarkName = "presentImagePickerController()"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "D0DD86F0-B39F-468F-BBEC-6C0DEDB48451"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "HormoneLife/UserTests/RecordTestsViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "65"
            endingLineNumber = "65"
            landmarkName = "didTapChooseFromGallery(_:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "C2B47A7F-8404-499C-8A91-D22A2C1B1364"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "HormoneLife/UserTests/RecordTestsViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "59"
            endingLineNumber = "59"
            landmarkName = "didTapCamera(_:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "68C2A8F8-35AE-45EA-81D7-0112BC7F4F20"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "HormoneLife/UserTests/Photos/CustomCameraViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "95"
            endingLineNumber = "95"
            landmarkName = "uploadImage(image:)"
            landmarkType = "7">
            <Locations>
               <Location
                  uuid = "68C2A8F8-35AE-45EA-81D7-0112BC7F4F20 - 5e91842c05a91be6"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "closure #1 (Swift.Optional&lt;HormoneLife.UserTestResultPageModel&gt;) -&gt; () in closure #1 (Swift.String, Swift.String) -&gt; () in HormoneLife.CustomCameraViewController.didFinishEditingWithImage(editedImage: Swift.Optional&lt;__C.UIImage&gt;) -&gt; ()"
                  moduleName = "HormoneLife"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Documents/Swift_Dev/HormoneLife/HormoneLife/UserTests/Photos/CustomCameraViewController.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "96"
                  endingLineNumber = "96"
                  offsetFromSymbolStart = "104">
               </Location>
               <Location
                  uuid = "68C2A8F8-35AE-45EA-81D7-0112BC7F4F20 - 2489521dc4e081c7"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "closure #1 (Swift.String, Swift.String) -&gt; () in HormoneLife.CustomCameraViewController.didFinishEditingWithImage(editedImage: Swift.Optional&lt;__C.UIImage&gt;) -&gt; ()"
                  moduleName = "HormoneLife"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Documents/Swift_Dev/HormoneLife/HormoneLife/UserTests/Photos/CustomCameraViewController.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "96"
                  endingLineNumber = "96"
                  offsetFromSymbolStart = "136">
               </Location>
               <Location
                  uuid = "68C2A8F8-35AE-45EA-81D7-0112BC7F4F20 - 5e91842c05a91c62"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "closure #1 (Swift.Optional&lt;HormoneLife.UserTestResultPageModel&gt;) -&gt; () in closure #1 (Swift.String, Swift.String) -&gt; () in HormoneLife.CustomCameraViewController.didFinishEditingWithImage(editedImage: Swift.Optional&lt;__C.UIImage&gt;) -&gt; ()"
                  moduleName = "HormoneLife"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Documents/Swift_Dev/HormoneLife/HormoneLife/UserTests/Photos/CustomCameraViewController.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "92"
                  endingLineNumber = "92"
                  offsetFromSymbolStart = "68">
               </Location>
               <Location
                  uuid = "68C2A8F8-35AE-45EA-81D7-0112BC7F4F20 - 2489521dc4e08118"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "closure #1 (Swift.String, Swift.String) -&gt; () in HormoneLife.CustomCameraViewController.didFinishEditingWithImage(editedImage: Swift.Optional&lt;__C.UIImage&gt;) -&gt; ()"
                  moduleName = "HormoneLife"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Documents/Swift_Dev/HormoneLife/HormoneLife/UserTests/Photos/CustomCameraViewController.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "97"
                  endingLineNumber = "97"
                  offsetFromSymbolStart = "136">
               </Location>
               <Location
                  uuid = "68C2A8F8-35AE-45EA-81D7-0112BC7F4F20 - 5e91842c05a91b85"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "closure #1 (Swift.Optional&lt;HormoneLife.UserTestResultPageModel&gt;) -&gt; () in closure #1 (Swift.String, Swift.String) -&gt; () in HormoneLife.CustomCameraViewController.didFinishEditingWithImage(editedImage: Swift.Optional&lt;__C.UIImage&gt;) -&gt; ()"
                  moduleName = "HormoneLife"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Documents/Swift_Dev/HormoneLife/HormoneLife/UserTests/Photos/CustomCameraViewController.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "93"
                  endingLineNumber = "93"
                  offsetFromSymbolStart = "96">
               </Location>
            </Locations>
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "99D518BB-C86B-4044-BE06-41E2CD5A621E"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "HormoneLife/UserTests/Photos/CustomCameraViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "113"
            endingLineNumber = "113"
            landmarkName = "uploadImage(image:)"
            landmarkType = "7">
            <Locations>
               <Location
                  uuid = "99D518BB-C86B-4044-BE06-41E2CD5A621E - 5e91842c05a9188a"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "closure #1 (Swift.Optional&lt;HormoneLife.UserTestResultPageModel&gt;) -&gt; () in closure #1 (Swift.String, Swift.String) -&gt; () in HormoneLife.CustomCameraViewController.didFinishEditingWithImage(editedImage: Swift.Optional&lt;__C.UIImage&gt;) -&gt; ()"
                  moduleName = "HormoneLife"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Documents/Swift_Dev/HormoneLife/HormoneLife/UserTests/Photos/CustomCameraViewController.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "116"
                  endingLineNumber = "116"
                  offsetFromSymbolStart = "684">
               </Location>
               <Location
                  uuid = "99D518BB-C86B-4044-BE06-41E2CD5A621E - 5e91842c05a9196b"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "closure #1 (Swift.Optional&lt;HormoneLife.UserTestResultPageModel&gt;) -&gt; () in closure #1 (Swift.String, Swift.String) -&gt; () in HormoneLife.CustomCameraViewController.didFinishEditingWithImage(editedImage: Swift.Optional&lt;__C.UIImage&gt;) -&gt; ()"
                  moduleName = "HormoneLife"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Documents/Swift_Dev/HormoneLife/HormoneLife/UserTests/Photos/CustomCameraViewController.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "115"
                  endingLineNumber = "115"
                  offsetFromSymbolStart = "652">
               </Location>
            </Locations>
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "6C41A510-210E-470D-AAB6-C9B96131C65E"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "HormoneLife/NetworkHelp/NetWorkHandle.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "94"
            endingLineNumber = "94"
            landmarkName = "upload(images:params:success:failure:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "9E1AD38B-E615-4134-88AA-2CE43ED8A5F5"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "HormoneLife/NetworkHelp/NetWorkHandle.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "90"
            endingLineNumber = "90"
            landmarkName = "upload(images:params:success:failure:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "8D02CCD0-E7DD-4DA6-9A93-7AEFF71367CD"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "HormoneLife/NetworkHelp/NetWorkHandle.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "86"
            endingLineNumber = "86"
            landmarkName = "upload(images:params:success:failure:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "CEB1DC70-E366-4F41-B088-543A0D3EDA47"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "HormoneLife/NetworkHelp/NetWorkHandle.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "81"
            endingLineNumber = "81"
            landmarkName = "upload(images:params:success:failure:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "55F9FF7D-724B-4393-95E6-A6462E2D8E7B"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "HormoneLife/Charts/ViewController/HistoryRecordCell.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "60"
            endingLineNumber = "60"
            landmarkName = "configCell(testPaper:delegate:)"
            landmarkType = "7">
            <Locations>
               <Location
                  uuid = "55F9FF7D-724B-4393-95E6-A6462E2D8E7B - c0f9abd39788b512"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "HormoneLife.HistoryRecordCell.configCell(testPaper: HormoneLife.TestPaperList, delegate: Swift.Optional&lt;HormoneLife.HistoryRecordCellDelegate&gt;) -&gt; ()"
                  moduleName = "HormoneLife"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Documents/Swift_Dev/HormoneLife/HormoneLife/Charts/ViewController/HistoryRecordCell.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "60"
                  endingLineNumber = "60"
                  offsetFromSymbolStart = "2492">
               </Location>
               <Location
                  uuid = "55F9FF7D-724B-4393-95E6-A6462E2D8E7B - c0f9abd39788b512"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "HormoneLife.HistoryRecordCell.configCell(testPaper: HormoneLife.TestPaperList, delegate: Swift.Optional&lt;HormoneLife.HistoryRecordCellDelegate&gt;) -&gt; ()"
                  moduleName = "HormoneLife"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Documents/Swift_Dev/HormoneLife/HormoneLife/Charts/ViewController/HistoryRecordCell.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "60"
                  endingLineNumber = "60"
                  offsetFromSymbolStart = "2176">
               </Location>
            </Locations>
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "C56664C2-5CD2-4612-B12E-92BDC337E3CA"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "HormoneLife/Charts/ViewController/HistoryRecordViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "317"
            endingLineNumber = "317"
            landmarkName = "didUpdateResult()"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "824F6D34-FD08-48BF-A35B-5004BF73AEB7"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "HormoneLife/Charts/ViewController/HistoryRecordViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "130"
            endingLineNumber = "130"
            landmarkName = "loadData()"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "DDB32D64-3CB9-4825-84BD-DCE4B31797CC"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "HormoneLife/TabBarVCs/ResultViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "210"
            endingLineNumber = "210"
            landmarkName = "updateValue()"
            landmarkType = "9">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "F862AFE0-4096-4121-947E-84C2205E47FE"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "HormoneLife/TabBarVCs/ResultViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "89"
            endingLineNumber = "89"
            landmarkName = "setupTimeView()"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "6A6C3E40-BBD1-4632-8795-EFF12F8B0540"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "HormoneLife/TabBarVCs/ResultViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "229"
            endingLineNumber = "229"
            landmarkName = "didTapSave(_:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "68EB4FFD-B80A-4723-B4AA-E9632AB99802"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "HormoneLife/Profile/ViewControllers/CycleReportTableViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "41"
            endingLineNumber = "41"
            landmarkName = "getCycleListByYear()"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "15A6D120-8555-497F-AEC3-32FCE1509248"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "HormoneLife/Profile/Views/CycleReportTableViewCell.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "152"
            endingLineNumber = "152"
            landmarkName = "setupCycleDay(data:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "B68DCA4F-9432-4D7E-A5E5-063852200D93"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "HormoneLife/Charts/ViewController/HistoryRecordViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "141"
            endingLineNumber = "141"
            landmarkName = "loadData()"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "905B4E8F-F24F-4461-8769-97B9B99C5140"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "HormoneLife/TabBarVCs/CalendarTableViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "200"
            endingLineNumber = "200"
            landmarkName = "tableView(_:cellForRowAt:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "C05548FF-A94A-4622-90F6-46DE8377B35B"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "HormoneLife/common/Calender/AppCalendarView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "107"
            endingLineNumber = "107"
            landmarkName = "currentYearMonth"
            landmarkType = "24">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "0FFE719C-A735-4C40-AC7F-8C56A8781EBA"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "HormoneLife/TabBarVCs/CalendarTableViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "157"
            endingLineNumber = "157"
            landmarkName = "currentDayToAgoOrAfterDays(days:fromCurrentDate:)"
            landmarkType = "7">
            <Locations>
               <Location
                  uuid = "0FFE719C-A735-4C40-AC7F-8C56A8781EBA - ff1211d65c85f1d7"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "HormoneLife.CalendarTableViewController.fetchCycleData() -&gt; ()"
                  moduleName = "HormoneLife"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Documents/Swift_Dev/HormoneLife/HormoneLife/TabBarVCs/CalendarTableViewController.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "165"
                  endingLineNumber = "165"
                  offsetFromSymbolStart = "132">
               </Location>
               <Location
                  uuid = "0FFE719C-A735-4C40-AC7F-8C56A8781EBA - a90f04ff95696b0f"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "HormoneLife.CalendarTableViewController.currentDayToAgoOrAfterDays(days: Swift.Int, fromCurrentDate: Swift.String) -&gt; Swift.String"
                  moduleName = "HormoneLife"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Documents/Swift_Dev/HormoneLife/HormoneLife/TabBarVCs/CalendarTableViewController.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "153"
                  endingLineNumber = "153"
                  offsetFromSymbolStart = "724">
               </Location>
               <Location
                  uuid = "0FFE719C-A735-4C40-AC7F-8C56A8781EBA - a90f04ff95696b0f"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "HormoneLife.CalendarTableViewController.currentDayToAgoOrAfterDays(days: Swift.Int, fromCurrentDate: Swift.String) -&gt; Swift.String"
                  moduleName = "HormoneLife"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Documents/Swift_Dev/HormoneLife/HormoneLife/TabBarVCs/CalendarTableViewController.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "153"
                  endingLineNumber = "153"
                  offsetFromSymbolStart = "756">
               </Location>
            </Locations>
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "F3600633-3F00-44F6-8820-D0DE433840D6"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "HormoneLife/common/Calender/AppCalendarView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "247"
            endingLineNumber = "247"
            landmarkName = "resetCell(cell:cellState:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "B07DF866-D4E9-4247-A74A-61DA2F5297D3"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "HormoneLife/LoginAndSignUp/TandCPopupViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "65"
            endingLineNumber = "65"
            landmarkName = "textView(_:shouldInteractWith:in:interaction:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "868731DC-9C83-45B0-81F2-4B79CB45F8DF"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "HormoneLife/LoginAndSignUp/TandCPopupViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "72"
            endingLineNumber = "72"
            landmarkName = "textView(_:shouldInteractWith:in:interaction:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
   </Breakpoints>
</Bucket>
