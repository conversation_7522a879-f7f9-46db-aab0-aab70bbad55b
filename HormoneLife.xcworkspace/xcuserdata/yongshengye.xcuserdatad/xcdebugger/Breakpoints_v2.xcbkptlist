<?xml version="1.0" encoding="UTF-8"?>
<Bucket
   uuid = "5CAF6BF9-1B35-4DE3-A321-9519E1AD8730"
   type = "0"
   version = "2.0">
   <Breakpoints>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "BF919BB5-535A-4212-8182-2F0EED7A582C"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "HormoneLife/NetworkHelp/NetWorkHandle.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "93"
            endingLineNumber = "93"
            landmarkName = "upload(images:params:success:failure:)"
            landmarkType = "7">
            <Locations>
               <Location
                  uuid = "BF919BB5-535A-4212-8182-2F0EED7A582C - e8842ca4b98788fe"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "static HormoneLife.NetWorkHandle.upload(images: __C.UIImage, params: Swift.Optional&lt;Swift.Dictionary&lt;Swift.String, Swift.AnyObject&gt;&gt;, success: (Swift.Dictionary&lt;Swift.String, Any&gt;) -&gt; (), failure: (Swift.Optional&lt;Swift.String&gt;) -&gt; ()) -&gt; ()"
                  moduleName = "HormoneLife.debug.dylib"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Desktop/%E5%9B%BE%E7%89%87%E8%B5%84%E6%BA%90%E7%AE%A1%E7%90%86/%E9%A1%B9%E7%9B%AE/hormone-life/HormoneLife/NetworkHelp/NetWorkHandle.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "93"
                  endingLineNumber = "93">
               </Location>
               <Location
                  uuid = "BF919BB5-535A-4212-8182-2F0EED7A582C - 9d11c6d84e4868"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "closure #2 (inout Foundation.URLRequest) -&gt; () in static HormoneLife.NetWorkHandle.upload(images: __C.UIImage, params: Swift.Optional&lt;Swift.Dictionary&lt;Swift.String, Swift.AnyObject&gt;&gt;, success: (Swift.Dictionary&lt;Swift.String, Any&gt;) -&gt; (), failure: (Swift.Optional&lt;Swift.String&gt;) -&gt; ()) -&gt; ()"
                  moduleName = "HormoneLife.debug.dylib"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Desktop/%E5%9B%BE%E7%89%87%E8%B5%84%E6%BA%90%E7%AE%A1%E7%90%86/%E9%A1%B9%E7%9B%AE/hormone-life/HormoneLife/NetworkHelp/NetWorkHandle.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "93"
                  endingLineNumber = "93">
               </Location>
               <Location
                  uuid = "BF919BB5-535A-4212-8182-2F0EED7A582C - 91862e8958c6f45d"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "closure #3 (Alamofire.DataResponse&lt;Any, Alamofire.AFError&gt;) -&gt; () in static HormoneLife.NetWorkHandle.upload(images: __C.UIImage, params: Swift.Optional&lt;Swift.Dictionary&lt;Swift.String, Swift.AnyObject&gt;&gt;, success: (Swift.Dictionary&lt;Swift.String, Any&gt;) -&gt; (), failure: (Swift.Optional&lt;Swift.String&gt;) -&gt; ()) -&gt; ()"
                  moduleName = "HormoneLife.debug.dylib"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Desktop/%E5%9B%BE%E7%89%87%E8%B5%84%E6%BA%90%E7%AE%A1%E7%90%86/%E9%A1%B9%E7%9B%AE/hormone-life/HormoneLife/NetworkHelp/NetWorkHandle.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "94"
                  endingLineNumber = "94">
               </Location>
            </Locations>
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "3D03B7F7-F11A-429B-B9B6-D39B636E510C"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "HormoneLife/NetworkHelp/Interactor.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "133"
            endingLineNumber = "133"
            landmarkName = "userConfigUpdate(_:completion:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
   </Breakpoints>
</Bucket>
