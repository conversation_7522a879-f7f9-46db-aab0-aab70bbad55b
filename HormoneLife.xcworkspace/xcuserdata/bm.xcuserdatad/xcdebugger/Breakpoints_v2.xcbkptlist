<?xml version="1.0" encoding="UTF-8"?>
<Bucket
   uuid = "32FEED02-F80C-4638-B0EE-942DE0200F6E"
   type = "0"
   version = "2.0">
   <Breakpoints>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.ExceptionBreakpoint">
         <BreakpointContent
            uuid = "6805EF6D-12EA-49D9-9712-3491A128EB19"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            breakpointStackSelectionBehavior = "1"
            scope = "1"
            stopOnStyle = "0">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "00E2A57A-CDD1-4512-8AC8-C786CB1B544D"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "HormoneLife/Charts/ChartsTitleView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "169"
            endingLineNumber = "169"
            landmarkName = "resultValue(value:ratio:testDetail:)"
            landmarkType = "7">
            <Locations>
               <Location
                  uuid = "00E2A57A-CDD1-4512-8AC8-C786CB1B544D - 462f72e6a52c0716"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "HormoneLife.TestType.resultValue(value: CoreGraphics.CGFloat, ratio: CoreGraphics.CGFloat, testDetail: Swift.Optional&lt;HormoneLife.TestPaperDetail&gt;) -&gt; Swift.String"
                  moduleName = "HormoneLife"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Documents/spoil/%E4%B8%87%E5%AD%9A/HormoneLife/Charts/ChartsTitleView.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "169"
                  endingLineNumber = "169"
                  offsetFromSymbolStart = "612">
               </Location>
               <Location
                  uuid = "00E2A57A-CDD1-4512-8AC8-C786CB1B544D - 462f72e6a52c0716"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "HormoneLife.TestType.resultValue(value: CoreGraphics.CGFloat, ratio: CoreGraphics.CGFloat, testDetail: Swift.Optional&lt;HormoneLife.TestPaperDetail&gt;) -&gt; Swift.String"
                  moduleName = "HormoneLife"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Documents/spoil/%E4%B8%87%E5%AD%9A/HormoneLife/Charts/ChartsTitleView.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "169"
                  endingLineNumber = "169"
                  offsetFromSymbolStart = "604">
               </Location>
               <Location
                  uuid = "00E2A57A-CDD1-4512-8AC8-C786CB1B544D - 14f48c70baf85267"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "HormoneLife.TestType.isHideMinMaxValue.getter : Swift.Bool"
                  moduleName = "HormoneLife"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Documents/spoil/%E4%B8%87%E5%AD%9A/HormoneLife/Charts/ChartsTitleView.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "170"
                  endingLineNumber = "170"
                  offsetFromSymbolStart = "96">
               </Location>
               <Location
                  uuid = "00E2A57A-CDD1-4512-8AC8-C786CB1B544D - c70d4cb7aa97b6a9"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "HormoneLife.TestType.isHideMinMaxValue.getter : Swift.Bool"
                  moduleName = "HormoneLife"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Desktop/%E4%B8%87%E5%AD%9A/HormoneLife/Charts/ChartsTitleView.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "170"
                  endingLineNumber = "170"
                  offsetFromSymbolStart = "96">
               </Location>
            </Locations>
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "F87E25E6-0B71-424C-B183-6B62AF1B309E"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "HormoneLife/TabBarVCs/CalendarTableViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "574"
            endingLineNumber = "574"
            landmarkName = "CalendarTableViewController"
            landmarkType = "21">
            <Locations>
               <Location
                  uuid = "F87E25E6-0B71-424C-B183-6B62AF1B309E - 13e15f418eb536e8"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "HormoneLife.CalendarTableViewController.cellColorDataType(view: Swift.Optional&lt;HormoneLife.AppCalendarCell&gt;, text: Swift.String, isSelected: Swift.Bool, indexPath: Foundation.IndexPath, date: Foundation.Date) -&gt; Swift.Dictionary&lt;HormoneLife.CalendarDataType, __C.UIColor&gt;"
                  moduleName = "HormoneLife"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Documents/spoil/%E4%B8%87%E5%AD%9A/HormoneLife/TabBarVCs/CalendarTableViewController.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "575"
                  endingLineNumber = "575"
                  offsetFromSymbolStart = "84">
               </Location>
               <Location
                  uuid = "F87E25E6-0B71-424C-B183-6B62AF1B309E - e5ef1338e1bd87b4"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "HormoneLife.CalendarTableViewController.didTapSwitch(_: HormoneLife.CalendarVCGeneralCell.CalendarVCRowType, isOn: Swift.Bool, cell: __C.UITableViewCell) -&gt; ()"
                  moduleName = "HormoneLife"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Documents/spoil/%E4%B8%87%E5%AD%9A/HormoneLife/TabBarVCs/CalendarTableViewController.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "574"
                  endingLineNumber = "574"
                  offsetFromSymbolStart = "552">
               </Location>
               <Location
                  uuid = "F87E25E6-0B71-424C-B183-6B62AF1B309E - f86d76c855acc85c"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "HormoneLife.CalendarTableViewController.didTapArow(HormoneLife.CalendarVCGeneralCell.CalendarVCRowType) -&gt; ()"
                  moduleName = "HormoneLife"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Desktop/%E4%B8%87%E5%AD%9A/HormoneLife/TabBarVCs/CalendarTableViewController.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "577"
                  endingLineNumber = "577"
                  offsetFromSymbolStart = "64">
               </Location>
               <Location
                  uuid = "F87E25E6-0B71-424C-B183-6B62AF1B309E - e2df6f60435477ee"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "HormoneLife.CalendarTableViewController.didTapAdd(HormoneLife.CalendarVCGeneralCell.CalendarVCRowType) -&gt; ()"
                  moduleName = "HormoneLife"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Documents/spoil/%E4%B8%87%E5%AD%9A/HormoneLife/TabBarVCs/CalendarTableViewController.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "576"
                  endingLineNumber = "576"
                  offsetFromSymbolStart = "128">
               </Location>
            </Locations>
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "64CBF6B7-DD8F-4917-84CD-CA45C42FFE29"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "HormoneLife/LoginAndSignUp/BindAccount/BindAccountViewModel.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "29"
            endingLineNumber = "29"
            landmarkName = "bindAccount(account:grantType:idToken:uid:success:failure:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "31D80147-DE2B-4862-AA30-E0ED415FFA92"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "HormoneLife/LoginAndSignUp/BindAccount/BindAccountViewModel.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "31"
            endingLineNumber = "31"
            landmarkName = "bindAccount(account:grantType:idToken:uid:success:failure:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "94051A96-7F11-4F01-9476-7CD2638AA8D6"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "HormoneLife/NetworkHelp/NetWorkHandle.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "171"
            endingLineNumber = "171"
            landmarkName = "requestPost(_:parameters:showHub:success:failure:)"
            landmarkType = "7">
            <Locations>
               <Location
                  uuid = "94051A96-7F11-4F01-9476-7CD2638AA8D6 - 3aee78e9c1f01297"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "closure #1 (Alamofire.DataResponse&lt;Any, Alamofire.AFError&gt;) -&gt; () in static HormoneLife.NetWorkHandle.requestPost(_: Swift.String, parameters: Swift.Optional&lt;Swift.Dictionary&lt;Swift.String, Any&gt;&gt;, showHub: Swift.Bool, success: (Swift.Dictionary&lt;Swift.String, Any&gt;) -&gt; (), failure: (Swift.Optional&lt;Swift.String&gt;) -&gt; ()) -&gt; ()"
                  moduleName = "HormoneLife"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Documents/spoil/%E4%B8%87%E5%AD%9A/HormoneLife/NetworkHelp/NetWorkHandle.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "171"
                  endingLineNumber = "171"
                  offsetFromSymbolStart = "2424">
               </Location>
               <Location
                  uuid = "94051A96-7F11-4F01-9476-7CD2638AA8D6 - 96abc91ea0433ceb"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "closure #5 @Swift.MainActor () -&gt; () in closure #1 (Alamofire.DataResponse&lt;Any, Alamofire.AFError&gt;) -&gt; () in static HormoneLife.NetWorkHandle.requestPost(_: Swift.String, parameters: Swift.Optional&lt;Swift.Dictionary&lt;Swift.String, Any&gt;&gt;, showHub: Swift.Bool, success: (Swift.Dictionary&lt;Swift.String, Any&gt;) -&gt; (), failure: (Swift.Optional&lt;Swift.String&gt;) -&gt; ()) -&gt; ()"
                  moduleName = "HormoneLife"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Documents/spoil/%E4%B8%87%E5%AD%9A/HormoneLife/NetworkHelp/NetWorkHandle.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "172"
                  endingLineNumber = "172"
                  offsetFromSymbolStart = "140">
               </Location>
               <Location
                  uuid = "94051A96-7F11-4F01-9476-7CD2638AA8D6 - 9254134129933d4d"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "closure #1 (Alamofire.DataResponse&lt;Any, Alamofire.AFError&gt;) -&gt; () in static HormoneLife.NetWorkHandle.requestPost(_: Swift.String, parameters: Swift.Optional&lt;Swift.Dictionary&lt;Swift.String, Any&gt;&gt;, showHub: Swift.Bool, success: (Swift.Dictionary&lt;Swift.String, Any&gt;) -&gt; (), failure: (Swift.Optional&lt;Swift.String&gt;) -&gt; ()) -&gt; ()"
                  moduleName = "HormoneLife"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Desktop/%E4%B8%87%E5%AD%9A/HormoneLife/NetworkHelp/NetWorkHandle.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "171"
                  endingLineNumber = "171"
                  offsetFromSymbolStart = "2424">
               </Location>
               <Location
                  uuid = "94051A96-7F11-4F01-9476-7CD2638AA8D6 - 3e11a2b648201335"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "closure #5 @Swift.MainActor () -&gt; () in closure #1 (Alamofire.DataResponse&lt;Any, Alamofire.AFError&gt;) -&gt; () in static HormoneLife.NetWorkHandle.requestPost(_: Swift.String, parameters: Swift.Optional&lt;Swift.Dictionary&lt;Swift.String, Any&gt;&gt;, showHub: Swift.Bool, success: (Swift.Dictionary&lt;Swift.String, Any&gt;) -&gt; (), failure: (Swift.Optional&lt;Swift.String&gt;) -&gt; ()) -&gt; ()"
                  moduleName = "HormoneLife"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Desktop/%E4%B8%87%E5%AD%9A/HormoneLife/NetworkHelp/NetWorkHandle.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "172"
                  endingLineNumber = "172"
                  offsetFromSymbolStart = "140">
               </Location>
            </Locations>
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "534774D9-3E27-421A-88F4-CB65E9ED4F9C"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "HormoneLife/NetworkHelp/NetWorkHandle.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "165"
            endingLineNumber = "165"
            landmarkName = "requestPost(_:parameters:showHub:success:failure:)"
            landmarkType = "7">
            <Locations>
               <Location
                  uuid = "534774D9-3E27-421A-88F4-CB65E9ED4F9C - 3aee78e9c1f0135d"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "closure #1 (Alamofire.DataResponse&lt;Any, Alamofire.AFError&gt;) -&gt; () in static HormoneLife.NetWorkHandle.requestPost(_: Swift.String, parameters: Swift.Optional&lt;Swift.Dictionary&lt;Swift.String, Any&gt;&gt;, showHub: Swift.Bool, success: (Swift.Dictionary&lt;Swift.String, Any&gt;) -&gt; (), failure: (Swift.Optional&lt;Swift.String&gt;) -&gt; ()) -&gt; ()"
                  moduleName = "HormoneLife"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Documents/spoil/%E4%B8%87%E5%AD%9A/HormoneLife/NetworkHelp/NetWorkHandle.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "165"
                  endingLineNumber = "165"
                  offsetFromSymbolStart = "816">
               </Location>
               <Location
                  uuid = "534774D9-3E27-421A-88F4-CB65E9ED4F9C - a4e8944e974bdf26"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "closure #4 @Swift.MainActor () -&gt; () in closure #1 (Alamofire.DataResponse&lt;Any, Alamofire.AFError&gt;) -&gt; () in static HormoneLife.NetWorkHandle.requestPost(_: Swift.String, parameters: Swift.Optional&lt;Swift.Dictionary&lt;Swift.String, Any&gt;&gt;, showHub: Swift.Bool, success: (Swift.Dictionary&lt;Swift.String, Any&gt;) -&gt; (), failure: (Swift.Optional&lt;Swift.String&gt;) -&gt; ()) -&gt; ()"
                  moduleName = "HormoneLife"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Documents/spoil/%E4%B8%87%E5%AD%9A/HormoneLife/NetworkHelp/NetWorkHandle.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "166"
                  endingLineNumber = "166"
                  offsetFromSymbolStart = "72">
               </Location>
               <Location
                  uuid = "534774D9-3E27-421A-88F4-CB65E9ED4F9C - 9254134129933a13"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "closure #1 (Alamofire.DataResponse&lt;Any, Alamofire.AFError&gt;) -&gt; () in static HormoneLife.NetWorkHandle.requestPost(_: Swift.String, parameters: Swift.Optional&lt;Swift.Dictionary&lt;Swift.String, Any&gt;&gt;, showHub: Swift.Bool, success: (Swift.Dictionary&lt;Swift.String, Any&gt;) -&gt; (), failure: (Swift.Optional&lt;Swift.String&gt;) -&gt; ()) -&gt; ()"
                  moduleName = "HormoneLife"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Desktop/%E4%B8%87%E5%AD%9A/HormoneLife/NetworkHelp/NetWorkHandle.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "165"
                  endingLineNumber = "165"
                  offsetFromSymbolStart = "816">
               </Location>
               <Location
                  uuid = "534774D9-3E27-421A-88F4-CB65E9ED4F9C - c52ffe67f28f66c"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "closure #4 @Swift.MainActor () -&gt; () in closure #1 (Alamofire.DataResponse&lt;Any, Alamofire.AFError&gt;) -&gt; () in static HormoneLife.NetWorkHandle.requestPost(_: Swift.String, parameters: Swift.Optional&lt;Swift.Dictionary&lt;Swift.String, Any&gt;&gt;, showHub: Swift.Bool, success: (Swift.Dictionary&lt;Swift.String, Any&gt;) -&gt; (), failure: (Swift.Optional&lt;Swift.String&gt;) -&gt; ()) -&gt; ()"
                  moduleName = "HormoneLife"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Desktop/%E4%B8%87%E5%AD%9A/HormoneLife/NetworkHelp/NetWorkHandle.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "166"
                  endingLineNumber = "166"
                  offsetFromSymbolStart = "72">
               </Location>
            </Locations>
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "56ABA081-2ACD-4E26-8A94-524FE54E4BDC"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "HormoneLife/NetworkHelp/NetWorkHandle.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "159"
            endingLineNumber = "159"
            landmarkName = "requestPost(_:parameters:showHub:success:failure:)"
            landmarkType = "7">
            <Locations>
               <Location
                  uuid = "56ABA081-2ACD-4E26-8A94-524FE54E4BDC - 3aee78e9c1f01063"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "closure #1 (Alamofire.DataResponse&lt;Any, Alamofire.AFError&gt;) -&gt; () in static HormoneLife.NetWorkHandle.requestPost(_: Swift.String, parameters: Swift.Optional&lt;Swift.Dictionary&lt;Swift.String, Any&gt;&gt;, showHub: Swift.Bool, success: (Swift.Dictionary&lt;Swift.String, Any&gt;) -&gt; (), failure: (Swift.Optional&lt;Swift.String&gt;) -&gt; ()) -&gt; ()"
                  moduleName = "HormoneLife"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Documents/spoil/%E4%B8%87%E5%AD%9A/HormoneLife/NetworkHelp/NetWorkHandle.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "159"
                  endingLineNumber = "159"
                  offsetFromSymbolStart = "1400">
               </Location>
               <Location
                  uuid = "56ABA081-2ACD-4E26-8A94-524FE54E4BDC - 4a2673be9a5c7a5d"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "closure #3 @Swift.MainActor () -&gt; () in closure #1 (Alamofire.DataResponse&lt;Any, Alamofire.AFError&gt;) -&gt; () in static HormoneLife.NetWorkHandle.requestPost(_: Swift.String, parameters: Swift.Optional&lt;Swift.Dictionary&lt;Swift.String, Any&gt;&gt;, showHub: Swift.Bool, success: (Swift.Dictionary&lt;Swift.String, Any&gt;) -&gt; (), failure: (Swift.Optional&lt;Swift.String&gt;) -&gt; ()) -&gt; ()"
                  moduleName = "HormoneLife"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Documents/spoil/%E4%B8%87%E5%AD%9A/HormoneLife/NetworkHelp/NetWorkHandle.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "160"
                  endingLineNumber = "160"
                  offsetFromSymbolStart = "80">
               </Location>
               <Location
                  uuid = "56ABA081-2ACD-4E26-8A94-524FE54E4BDC - 9254134129933ad9"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "closure #1 (Alamofire.DataResponse&lt;Any, Alamofire.AFError&gt;) -&gt; () in static HormoneLife.NetWorkHandle.requestPost(_: Swift.String, parameters: Swift.Optional&lt;Swift.Dictionary&lt;Swift.String, Any&gt;&gt;, showHub: Swift.Bool, success: (Swift.Dictionary&lt;Swift.String, Any&gt;) -&gt; (), failure: (Swift.Optional&lt;Swift.String&gt;) -&gt; ()) -&gt; ()"
                  moduleName = "HormoneLife"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Desktop/%E4%B8%87%E5%AD%9A/HormoneLife/NetworkHelp/NetWorkHandle.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "159"
                  endingLineNumber = "159"
                  offsetFromSymbolStart = "1400">
               </Location>
               <Location
                  uuid = "56ABA081-2ACD-4E26-8A94-524FE54E4BDC - e29c1816723f50ab"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "closure #3 @Swift.MainActor () -&gt; () in closure #1 (Alamofire.DataResponse&lt;Any, Alamofire.AFError&gt;) -&gt; () in static HormoneLife.NetWorkHandle.requestPost(_: Swift.String, parameters: Swift.Optional&lt;Swift.Dictionary&lt;Swift.String, Any&gt;&gt;, showHub: Swift.Bool, success: (Swift.Dictionary&lt;Swift.String, Any&gt;) -&gt; (), failure: (Swift.Optional&lt;Swift.String&gt;) -&gt; ()) -&gt; ()"
                  moduleName = "HormoneLife"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Desktop/%E4%B8%87%E5%AD%9A/HormoneLife/NetworkHelp/NetWorkHandle.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "160"
                  endingLineNumber = "160"
                  offsetFromSymbolStart = "80">
               </Location>
            </Locations>
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "5C36E4F5-9DC6-4B55-98FA-DEC257340A2E"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "HormoneLife/NetworkHelp/NetWorkHandle.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "146"
            endingLineNumber = "146"
            landmarkName = "requestPost(_:parameters:showHub:success:failure:)"
            landmarkType = "7">
            <Locations>
               <Location
                  uuid = "5C36E4F5-9DC6-4B55-98FA-DEC257340A2E - 3aee78e9c1f011ce"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "closure #1 (Alamofire.DataResponse&lt;Any, Alamofire.AFError&gt;) -&gt; () in static HormoneLife.NetWorkHandle.requestPost(_: Swift.String, parameters: Swift.Optional&lt;Swift.Dictionary&lt;Swift.String, Any&gt;&gt;, showHub: Swift.Bool, success: (Swift.Dictionary&lt;Swift.String, Any&gt;) -&gt; (), failure: (Swift.Optional&lt;Swift.String&gt;) -&gt; ()) -&gt; ()"
                  moduleName = "HormoneLife"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Documents/spoil/%E4%B8%87%E5%AD%9A/HormoneLife/NetworkHelp/NetWorkHandle.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "146"
                  endingLineNumber = "146"
                  offsetFromSymbolStart = "2048">
               </Location>
               <Location
                  uuid = "5C36E4F5-9DC6-4B55-98FA-DEC257340A2E - 58675eee8965198d"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "closure #2 @Swift.MainActor () -&gt; () in closure #1 (Alamofire.DataResponse&lt;Any, Alamofire.AFError&gt;) -&gt; () in static HormoneLife.NetWorkHandle.requestPost(_: Swift.String, parameters: Swift.Optional&lt;Swift.Dictionary&lt;Swift.String, Any&gt;&gt;, showHub: Swift.Bool, success: (Swift.Dictionary&lt;Swift.String, Any&gt;) -&gt; (), failure: (Swift.Optional&lt;Swift.String&gt;) -&gt; ()) -&gt; ()"
                  moduleName = "HormoneLife"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Documents/spoil/%E4%B8%87%E5%AD%9A/HormoneLife/NetworkHelp/NetWorkHandle.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "147"
                  endingLineNumber = "147"
                  offsetFromSymbolStart = "336">
               </Location>
               <Location
                  uuid = "5C36E4F5-9DC6-4B55-98FA-DEC257340A2E - 9254134129933884"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "closure #1 (Alamofire.DataResponse&lt;Any, Alamofire.AFError&gt;) -&gt; () in static HormoneLife.NetWorkHandle.requestPost(_: Swift.String, parameters: Swift.Optional&lt;Swift.Dictionary&lt;Swift.String, Any&gt;&gt;, showHub: Swift.Bool, success: (Swift.Dictionary&lt;Swift.String, Any&gt;) -&gt; (), failure: (Swift.Optional&lt;Swift.String&gt;) -&gt; ()) -&gt; ()"
                  moduleName = "HormoneLife"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Desktop/%E4%B8%87%E5%AD%9A/HormoneLife/NetworkHelp/NetWorkHandle.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "146"
                  endingLineNumber = "146"
                  offsetFromSymbolStart = "2048">
               </Location>
               <Location
                  uuid = "5C36E4F5-9DC6-4B55-98FA-DEC257340A2E - f0dd3546610630c7"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "closure #2 @Swift.MainActor () -&gt; () in closure #1 (Alamofire.DataResponse&lt;Any, Alamofire.AFError&gt;) -&gt; () in static HormoneLife.NetWorkHandle.requestPost(_: Swift.String, parameters: Swift.Optional&lt;Swift.Dictionary&lt;Swift.String, Any&gt;&gt;, showHub: Swift.Bool, success: (Swift.Dictionary&lt;Swift.String, Any&gt;) -&gt; (), failure: (Swift.Optional&lt;Swift.String&gt;) -&gt; ()) -&gt; ()"
                  moduleName = "HormoneLife"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Desktop/%E4%B8%87%E5%AD%9A/HormoneLife/NetworkHelp/NetWorkHandle.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "147"
                  endingLineNumber = "147"
                  offsetFromSymbolStart = "336">
               </Location>
            </Locations>
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "2F316A95-863C-49D7-AE92-A15AE40111EB"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "HormoneLife/LoginAndSignUp/LoginViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "22"
            endingLineNumber = "22"
            landmarkName = "loginButton(_:didCompleteWith:error:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "23E2B986-50ED-4160-8D2B-0C65D82648F6"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "HormoneLife/LoginAndSignUp/LoginViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "516"
            endingLineNumber = "516"
            landmarkName = "presentationAnchor(for:)"
            landmarkType = "7">
            <Locations>
               <Location
                  uuid = "23E2B986-50ED-4160-8D2B-0C65D82648F6 - 3d8569e3df1be9a1"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "HormoneLife.LoginViewController.presentationAnchor(for: __C.ASAuthorizationController) -&gt; __C.UIWindow"
                  moduleName = "HormoneLife"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Desktop/%E4%B8%87%E5%AD%9A/HormoneLife/LoginAndSignUp/LoginViewController.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "513"
                  endingLineNumber = "513"
                  offsetFromSymbolStart = "96">
               </Location>
               <Location
                  uuid = "23E2B986-50ED-4160-8D2B-0C65D82648F6 - 3d8569e3df1be90c"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "HormoneLife.LoginViewController.presentationAnchor(for: __C.ASAuthorizationController) -&gt; __C.UIWindow"
                  moduleName = "HormoneLife"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Desktop/%E4%B8%87%E5%AD%9A/HormoneLife/LoginAndSignUp/LoginViewController.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "516"
                  endingLineNumber = "516"
                  offsetFromSymbolStart = "96">
               </Location>
               <Location
                  uuid = "23E2B986-50ED-4160-8D2B-0C65D82648F6 - bd05e09e12d1e8c9"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "HormoneLife.LoginViewController.authorizationController(controller: __C.ASAuthorizationController, didCompleteWithAuthorization: __C.ASAuthorization) -&gt; ()"
                  moduleName = "HormoneLife"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Documents/spoil/%E4%B8%87%E5%AD%9A/HormoneLife/LoginAndSignUp/LoginViewController.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "533"
                  endingLineNumber = "533"
                  offsetFromSymbolStart = "2668">
               </Location>
            </Locations>
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "593FE07B-1DAE-4502-9332-E1839029CF1D"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "HormoneLife/LoginAndSignUp/LoginViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "511"
            endingLineNumber = "511"
            landmarkName = "authorizationController(controller:didCompleteWithError:)"
            landmarkType = "7">
            <Locations>
               <Location
                  uuid = "593FE07B-1DAE-4502-9332-E1839029CF1D - 3d8569e3df1be825"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "HormoneLife.LoginViewController.presentationAnchor(for: __C.ASAuthorizationController) -&gt; __C.UIWindow"
                  moduleName = "HormoneLife"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Desktop/%E4%B8%87%E5%AD%9A/HormoneLife/LoginAndSignUp/LoginViewController.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "509"
                  endingLineNumber = "509"
                  offsetFromSymbolStart = "96">
               </Location>
               <Location
                  uuid = "593FE07B-1DAE-4502-9332-E1839029CF1D - 10f1ab9d10503011"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "HormoneLife.LoginViewController.authorizationController(controller: __C.ASAuthorizationController, didCompleteWithError: Swift.Error) -&gt; ()"
                  moduleName = "HormoneLife"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Desktop/%E4%B8%87%E5%AD%9A/HormoneLife/LoginAndSignUp/LoginViewController.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "509"
                  endingLineNumber = "509"
                  offsetFromSymbolStart = "28">
               </Location>
               <Location
                  uuid = "593FE07B-1DAE-4502-9332-E1839029CF1D - 10f1ab9d105031b4"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "HormoneLife.LoginViewController.authorizationController(controller: __C.ASAuthorizationController, didCompleteWithError: Swift.Error) -&gt; ()"
                  moduleName = "HormoneLife"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Desktop/%E4%B8%87%E5%AD%9A/HormoneLife/LoginAndSignUp/LoginViewController.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "512"
                  endingLineNumber = "512"
                  offsetFromSymbolStart = "28">
               </Location>
               <Location
                  uuid = "593FE07B-1DAE-4502-9332-E1839029CF1D - bd05e09e12d1e8c9"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "HormoneLife.LoginViewController.authorizationController(controller: __C.ASAuthorizationController, didCompleteWithAuthorization: __C.ASAuthorization) -&gt; ()"
                  moduleName = "HormoneLife"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Documents/spoil/%E4%B8%87%E5%AD%9A/HormoneLife/LoginAndSignUp/LoginViewController.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "533"
                  endingLineNumber = "533"
                  offsetFromSymbolStart = "2668">
               </Location>
            </Locations>
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "826D7AB1-183F-4FF6-9612-820F01F353CC"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "HormoneLife/LoginAndSignUp/LoginViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "28"
            endingLineNumber = "28"
            landmarkName = "loginButtonDidLogOut(_:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
   </Breakpoints>
</Bucket>
