# Uncomment the next line to define a global platform for your project
#platform :ios, '13.0'
#source  'https://github.com/CocoaPods/Specs.git'

target 'HormoneLife' do
  # Comment the next line if you don't want to use dynamic frameworks
  use_frameworks!

  # Pods for HormoneLife

  pod 'Alamofire'
  pod 'FMDB'
  pod 'SnapKit'
  pod 'SwiftyJSON'
#  pod 'MBProgressHUD'
  pod 'SDWebImage'
  pod 'IQKeyboardManagerSwift'
  pod 'JTAppleCalendar'
  pod 'ObjectMapper'
  pod 'DGCharts'
  pod 'MJRefresh'
#  pod 'SQAutoScrollView'
  pod 'Kingfisher'#  ,'~> 7.10.0'
  
  pod 'GoogleSignIn'
  
  pod 'FBSDKLoginKit'
  
  pod 'Firebase'
  pod 'FirebaseCore'
  pod 'FirebaseMessaging'
  pod 'HandyJSON', '~> 5.0.4-beta'
  pod 'FirebaseAuth'
#  pod 'JXPagingView/Paging'
#  pod 'JXSegmentedView','~> 1.2.6'
  
end

post_install do |installer|
  installer.pods_project.targets.each do |target|
    target.build_configurations.each do |config|
      config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = 13.0;
      config.build_settings['EXPANDED_CODE_SIGN_IDENTITY'] = ""
      config.build_settings['CODE_SIGNING_REQUIRED'] = "NO"
      config.build_settings['CODE_SIGNING_ALLOWED'] = "NO"
      config.build_settings["EXCLUDED_ARCHS[sdk=iphonesimulator*]"] = "arm64"
      config.build_settings['BUILD_LIBRARY_FOR_DISTRIBUTION'] = "YES"
      config.build_settings['ENABLE_USER_SCRIPT_SANDBOXING'] = "NO"
    end
  end
end
