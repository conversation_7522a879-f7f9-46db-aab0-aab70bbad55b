// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		189A14CA2C1AD70F00394477 /* ScanView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 189A14C92C1AD70F00394477 /* ScanView.swift */; };
		189E91CB2C1A7F6A007D04CA /* RecordTestsViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 189E91C92C1A7F6A007D04CA /* RecordTestsViewController.swift */; };
		189E91CC2C1A7F6A007D04CA /* RecordTestsViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 189E91CA2C1A7F6A007D04CA /* RecordTestsViewController.xib */; };
		18A1F5222C1C2324003BE30C /* ImageCropperViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18A1F5212C1C2324003BE30C /* ImageCropperViewController.swift */; };
		18A1F5242C1C27B1003BE30C /* TBImageCropperHelper.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18A1F5232C1C27B1003BE30C /* TBImageCropperHelper.swift */; };
		18A1F5262C1C3FFE003BE30C /* CustomCameraViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18A1F5252C1C3FFE003BE30C /* CustomCameraViewController.swift */; };
		18E5F8BC2C1C187D002CD571 /* OverlayView.xib in Resources */ = {isa = PBXBuildFile; fileRef = 18E5F8B92C1C187C002CD571 /* OverlayView.xib */; };
		18E5F8BD2C1C187D002CD571 /* OverlayView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18E5F8BA2C1C187C002CD571 /* OverlayView.swift */; };
		18E5F8BE2C1C187D002CD571 /* FullScreenCameraViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18E5F8BB2C1C187C002CD571 /* FullScreenCameraViewController.swift */; };
		18E5F8C02C1C19B8002CD571 /* NibLoadable.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18E5F8BF2C1C19B8002CD571 /* NibLoadable.swift */; };
		1B1CB7E92C312E0D00E99055 /* BalloonMarker.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1B1CB7E82C312E0C00E99055 /* BalloonMarker.swift */; };
		1B1D76722C4FF24600134308 /* HistoryRecordBottomView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1B1D76712C4FF24600134308 /* HistoryRecordBottomView.swift */; };
		1B1D76742C50024500134308 /* EditTestResultController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1B1D76732C50024400134308 /* EditTestResultController.swift */; };
		1B1D76762C50083900134308 /* EditTestResultBottomView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1B1D76752C50083900134308 /* EditTestResultBottomView.swift */; };
		1B1D76782C500A3F00134308 /* TestTimeView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1B1D76772C500A3F00134308 /* TestTimeView.swift */; };
		1B381CBE2C5CF7520071D14F /* HistoryRecordFooterView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1B381CBD2C5CF7520071D14F /* HistoryRecordFooterView.swift */; };
		1B49621B2C283B7B00B67BAF /* ChartsHeaderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1B49621A2C283B7B00B67BAF /* ChartsHeaderView.swift */; };
		1B49621D2C3079C000B67BAF /* ChartsCardView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1B49621C2C3079C000B67BAF /* ChartsCardView.swift */; };
		1B49621F2C308A0A00B67BAF /* LineChartFilledView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1B49621E2C308A0900B67BAF /* LineChartFilledView.swift */; };
		1B97A3FA2C4F0BCC00FA5BF0 /* HistoryRecordCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1B97A3F82C4F0BCC00FA5BF0 /* HistoryRecordCell.swift */; };
		1B97A3FB2C4F0BCC00FA5BF0 /* HistoryRecordCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = 1B97A3F92C4F0BCC00FA5BF0 /* HistoryRecordCell.xib */; };
		1B992BD12C4F0333002FCF2D /* HistoryRecordViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1B992BD02C4F0333002FCF2D /* HistoryRecordViewController.swift */; };
		1BE0234C2C1734ED00957955 /* ChartsViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1BE0234B2C1734ED00957955 /* ChartsViewController.swift */; };
		1BE0234E2C17366E00957955 /* ChartsTitleView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1BE0234D2C17366E00957955 /* ChartsTitleView.swift */; };
		2201CD582C5CD18E004E1BE2 /* HomeCameraPopupView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2201CD572C5CD18E004E1BE2 /* HomeCameraPopupView.swift */; };
		2201CD5A2C5FBCDA004E1BE2 /* TagsTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2201CD592C5FBCDA004E1BE2 /* TagsTableViewCell.swift */; };
		2201CD5C2C5FC101004E1BE2 /* TagsTableViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2201CD5B2C5FC101004E1BE2 /* TagsTableViewController.swift */; };
		2201CD5E2C607026004E1BE2 /* SupportAndHelpFeedbackTableViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2201CD5D2C607026004E1BE2 /* SupportAndHelpFeedbackTableViewController.swift */; };
		2201CD602C607263004E1BE2 /* FeedbackTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2201CD5F2C607263004E1BE2 /* FeedbackTableViewCell.swift */; };
		220AA8C72CC7F88400BEEAB3 /* UnbingPopupViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 220AA8C52CC7F88300BEEAB3 /* UnbingPopupViewController.xib */; };
		220AA8C82CC7F88400BEEAB3 /* UnbingPopupViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 220AA8C62CC7F88300BEEAB3 /* UnbingPopupViewController.swift */; };
		220AA8CA2CC8014200BEEAB3 /* EmptyTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 220AA8C92CC8014200BEEAB3 /* EmptyTableViewCell.swift */; };
		22105BEE2CBEDB9600F5785C /* HomeDataSingleton.swift in Sources */ = {isa = PBXBuildFile; fileRef = 22105BED2CBEDB9600F5785C /* HomeDataSingleton.swift */; };
		2212D1D92C17421200A6680E /* NetWorkHandle.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2212D1D72C17421100A6680E /* NetWorkHandle.swift */; };
		2212D1DA2C17421200A6680E /* Interactor.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2212D1D82C17421200A6680E /* Interactor.swift */; };
		2212D1DE2C17451E00A6680E /* Common.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2212D1DD2C17451E00A6680E /* Common.swift */; };
		2212D1E02C17493400A6680E /* Model.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2212D1DF2C17493400A6680E /* Model.swift */; };
		2212D1E22C17536400A6680E /* UserDefaultTool.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2212D1E12C17536400A6680E /* UserDefaultTool.swift */; };
		2212D1E42C1753A700A6680E /* Interface.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2212D1E32C1753A700A6680E /* Interface.swift */; };
		221D649A2D26FDAF000E7A08 /* ConfirmErrorPopupViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 221D64992D26FDAF000E7A08 /* ConfirmErrorPopupViewController.swift */; };
		222CF7CF2C9567B4005F389D /* ChangeConceptionViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 222CF7CE2C9567B4005F389D /* ChangeConceptionViewController.swift */; };
		223065632C02E2D900638708 /* SignUpViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 223065612C02E2D900638708 /* SignUpViewController.swift */; };
		223065642C02E2D900638708 /* SignUpViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 223065622C02E2D900638708 /* SignUpViewController.xib */; };
		223776492C143C0D00A2575F /* RootViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 223776482C143C0D00A2575F /* RootViewController.swift */; };
		2237764C2C14768900A2575F /* ProfileViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2237764A2C14768900A2575F /* ProfileViewController.swift */; };
		2237764D2C14768900A2575F /* ProfileViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 2237764B2C14768900A2575F /* ProfileViewController.xib */; };
		2237764F2C14B17000A2575F /* UIFont+Extension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2237764E2C14B17000A2575F /* UIFont+Extension.swift */; };
		223776512C14C2FE00A2575F /* UIApplecation+Extension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 223776502C14C2FE00A2575F /* UIApplecation+Extension.swift */; };
		223776532C14CA5A00A2575F /* HomeCarouselView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 223776522C14CA5A00A2575F /* HomeCarouselView.swift */; };
		223776572C14CF8800A2575F /* carouselSampleImage.png in Resources */ = {isa = PBXBuildFile; fileRef = 223776562C14CF8800A2575F /* carouselSampleImage.png */; };
		2237765A2C155B8100A2575F /* SupportAndHelpHomeViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 223776582C155B8100A2575F /* SupportAndHelpHomeViewController.swift */; };
		2237765B2C155B8100A2575F /* SupportAndHelpHomeViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 223776592C155B8100A2575F /* SupportAndHelpHomeViewController.xib */; };
		223776622C15B26800A2575F /* AboutHormonelifeViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 223776602C15B26800A2575F /* AboutHormonelifeViewController.swift */; };
		223776632C15B26800A2575F /* AboutHormonelifeViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 223776612C15B26800A2575F /* AboutHormonelifeViewController.xib */; };
		223776662C15B71E00A2575F /* SettingViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 223776642C15B71D00A2575F /* SettingViewController.swift */; };
		223776672C15B71E00A2575F /* SettingViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 223776652C15B71D00A2575F /* SettingViewController.xib */; };
		2237766A2C16003200A2575F /* ChangePasswordViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 223776682C16003200A2575F /* ChangePasswordViewController.swift */; };
		2237766B2C16003200A2575F /* ChangePasswordViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 223776692C16003200A2575F /* ChangePasswordViewController.xib */; };
		224CA4442C01E81A00FAD0CC /* LoginViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 224CA4422C01E81A00FAD0CC /* LoginViewController.swift */; };
		224CA4452C01E81A00FAD0CC /* LoginViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 224CA4432C01E81A00FAD0CC /* LoginViewController.xib */; };
		2252D6802C037DAA00E3672F /* WebViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2252D67E2C037DAA00E3672F /* WebViewController.swift */; };
		2252D6812C037DAA00E3672F /* WebViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 2252D67F2C037DAA00E3672F /* WebViewController.xib */; };
		2252D6842C037E3F00E3672F /* ForgotPassWordViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2252D6822C037E3F00E3672F /* ForgotPassWordViewController.swift */; };
		2252D6852C037E3F00E3672F /* ForgotPassWordViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 2252D6832C037E3F00E3672F /* ForgotPassWordViewController.xib */; };
		2252D6882C0384E200E3672F /* CreateAccountPopupViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2252D6862C0384E200E3672F /* CreateAccountPopupViewController.swift */; };
		2252D6892C0384E200E3672F /* CreateAccountPopupViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 2252D6872C0384E200E3672F /* CreateAccountPopupViewController.xib */; };
		2258803A2C579C5700DCEFC1 /* TandCResultView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 225880392C579C5700DCEFC1 /* TandCResultView.swift */; };
		225922B62C5948DF0053FDC3 /* NotificationsViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 225922B42C5948DE0053FDC3 /* NotificationsViewController.swift */; };
		225922B72C5948DF0053FDC3 /* NotificationsViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 225922B52C5948DF0053FDC3 /* NotificationsViewController.xib */; };
		225D72602C32F6AA005DAAAC /* TestStripsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 225D725F2C32F6AA005DAAAC /* TestStripsView.swift */; };
		226090332BEF93440053F10E /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 226090322BEF93440053F10E /* AppDelegate.swift */; };
		226090372BEF93440053F10E /* ViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 226090362BEF93440053F10E /* ViewController.swift */; };
		2260903A2BEF93440053F10E /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 226090382BEF93440053F10E /* Main.storyboard */; };
		2260903C2BEF93460053F10E /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 2260903B2BEF93460053F10E /* Assets.xcassets */; };
		2260903F2BEF93460053F10E /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 2260903D2BEF93460053F10E /* LaunchScreen.storyboard */; };
		2260904E2BEFC1610053F10E /* BaseViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2260904D2BEFC1610053F10E /* BaseViewController.swift */; };
		226090572BEFC2260053F10E /* UIViewController+Modal.swift in Sources */ = {isa = PBXBuildFile; fileRef = 226090502BEFC2260053F10E /* UIViewController+Modal.swift */; };
		226090582BEFC2270053F10E /* UILabel+Extension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 226090512BEFC2260053F10E /* UILabel+Extension.swift */; };
		226090592BEFC2270053F10E /* UIView+Extension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 226090522BEFC2260053F10E /* UIView+Extension.swift */; };
		2260905B2BEFC2270053F10E /* Day+Extension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 226090542BEFC2260053F10E /* Day+Extension.swift */; };
		2260905C2BEFC2270053F10E /* String+Extension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 226090552BEFC2260053F10E /* String+Extension.swift */; };
		2260905D2BEFC2270053F10E /* UIColor+Extension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 226090562BEFC2260053F10E /* UIColor+Extension.swift */; };
		2260905F2BEFC3000053F10E /* LanguageManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2260905E2BEFC3000053F10E /* LanguageManager.swift */; };
		226090612BEFC40F0053F10E /* TabBarController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 226090602BEFC40F0053F10E /* TabBarController.swift */; };
		22680D902C0A215E00B5DCF2 /* BirthdaySettingViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 22680D8E2C0A215E00B5DCF2 /* BirthdaySettingViewController.swift */; };
		22680D912C0A215E00B5DCF2 /* BirthdaySettingViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 22680D8F2C0A215E00B5DCF2 /* BirthdaySettingViewController.xib */; };
		22680D942C0A2DC700B5DCF2 /* BoundViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 22680D922C0A2DC700B5DCF2 /* BoundViewController.swift */; };
		22680D952C0A2DC700B5DCF2 /* BoundViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 22680D932C0A2DC700B5DCF2 /* BoundViewController.xib */; };
		22680D982C0C1F9700B5DCF2 /* HomeViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 22680D962C0C1F9700B5DCF2 /* HomeViewController.swift */; };
		22680D992C0C1F9700B5DCF2 /* HomeViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 22680D972C0C1F9700B5DCF2 /* HomeViewController.xib */; };
		226D298E2C1EFCB40058E91E /* MessageTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 226D298C2C1EFCB40058E91E /* MessageTableViewCell.swift */; };
		226D298F2C1EFCB40058E91E /* MessageTableViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = 226D298D2C1EFCB40058E91E /* MessageTableViewCell.xib */; };
		226D299A2C21E7A40058E91E /* AppCalendarCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 226D29952C21E7A40058E91E /* AppCalendarCell.swift */; };
		226D299B2C21E7A40058E91E /* AppCalendarView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 226D29962C21E7A40058E91E /* AppCalendarView.swift */; };
		226D299C2C21E7A40058E91E /* AppCalendarDayTypeCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 226D29972C21E7A40058E91E /* AppCalendarDayTypeCell.swift */; };
		226D299D2C21E7A40058E91E /* AppCalendarViewDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 226D29982C21E7A40058E91E /* AppCalendarViewDelegate.swift */; };
		226D299E2C21E7A40058E91E /* AppCalendarConfig.swift in Sources */ = {isa = PBXBuildFile; fileRef = 226D29992C21E7A40058E91E /* AppCalendarConfig.swift */; };
		226D29A02C21EAD80058E91E /* CalendarPopupViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 226D299F2C21EAD80058E91E /* CalendarPopupViewController.swift */; };
		226D29A22C21EF690058E91E /* ExtensionFile.swift in Sources */ = {isa = PBXBuildFile; fileRef = 226D29A12C21EF690058E91E /* ExtensionFile.swift */; };
		226D29A42C21EF810058E91E /* PickPhotoViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 226D29A32C21EF810058E91E /* PickPhotoViewController.swift */; };
		2272A7D32C160B0500FCB0F5 /* DeleteAccountViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2272A7D12C160B0400FCB0F5 /* DeleteAccountViewController.swift */; };
		2272A7D42C160B0500FCB0F5 /* DeleteAccountViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 2272A7D22C160B0500FCB0F5 /* DeleteAccountViewController.xib */; };
		2286B0912C1CA6E700C59403 /* UITextField+Extension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2286B0902C1CA6E700C59403 /* UITextField+Extension.swift */; };
		2286B0942C1ED05B00C59403 /* TagsCollectionViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2286B0922C1ED05B00C59403 /* TagsCollectionViewCell.swift */; };
		2286B0952C1ED05B00C59403 /* TagsCollectionViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = 2286B0932C1ED05B00C59403 /* TagsCollectionViewCell.xib */; };
		2286B0972C1ED73A00C59403 /* LeftAlignedCollectionViewFlowLayout.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2286B0962C1ED73A00C59403 /* LeftAlignedCollectionViewFlowLayout.swift */; };
		2291C56C2C063F46005CC00F /* GoalSettingViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2291C56A2C063F46005CC00F /* GoalSettingViewController.swift */; };
		2291C56D2C063F46005CC00F /* GoalSettingViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 2291C56B2C063F46005CC00F /* GoalSettingViewController.xib */; };
		229C762A2C6523E500CFC07F /* EditTestTimeView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 225922B22C57E30E0053FDC3 /* EditTestTimeView.swift */; };
		22A78B462C168855006C8B33 /* PersonalInfoViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 22A78B442C168854006C8B33 /* PersonalInfoViewController.swift */; };
		22A78B472C168855006C8B33 /* PersonalInfoViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 22A78B452C168855006C8B33 /* PersonalInfoViewController.xib */; };
		22A78B4A2C1691F6006C8B33 /* TagsViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 22A78B482C1691F6006C8B33 /* TagsViewController.swift */; };
		22A78B4B2C1691F6006C8B33 /* TagsViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 22A78B492C1691F6006C8B33 /* TagsViewController.xib */; };
		22A78B4E2C1694DE006C8B33 /* AddTagViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 22A78B4C2C1694DD006C8B33 /* AddTagViewController.xib */; };
		22A78B4F2C1694DE006C8B33 /* AddTagViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 22A78B4D2C1694DE006C8B33 /* AddTagViewController.swift */; };
		22A78B522C169BD2006C8B33 /* MessagesViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 22A78B502C169BD1006C8B33 /* MessagesViewController.swift */; };
		22A78B532C169BD2006C8B33 /* MessagesViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 22A78B512C169BD1006C8B33 /* MessagesViewController.xib */; };
		22A78B562C16A03B006C8B33 /* MessageSettingViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 22A78B542C16A03A006C8B33 /* MessageSettingViewController.xib */; };
		22A78B572C16A03B006C8B33 /* MessageSettingViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 22A78B552C16A03A006C8B33 /* MessageSettingViewController.swift */; };
		22A78D2D2C56469300C3A271 /* CircleDemoView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 22A78D2C2C56469300C3A271 /* CircleDemoView.swift */; };
		22A78D312C56476F00C3A271 /* ResultViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 22A78D302C56476F00C3A271 /* ResultViewController.swift */; };
		22B39A0E2CBF086100E98FEC /* LineChartDownloadViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 22B39A0D2CBF086100E98FEC /* LineChartDownloadViewController.swift */; };
		22B63A002C248FF600C58D7B /* CalendarTableViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 22B639FF2C248FF600C58D7B /* CalendarTableViewController.swift */; };
		22B63A022C2497B800C58D7B /* CalendarLabelCellTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 22B63A012C2497B800C58D7B /* CalendarLabelCellTableViewCell.swift */; };
		22B63A042C2657F800C58D7B /* CalendarVCSectionCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 22B63A032C2657F800C58D7B /* CalendarVCSectionCell.swift */; };
		22B63A0C2C267B1D00C58D7B /* CalendarPopupSelectionViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 22B63A0B2C267B1D00C58D7B /* CalendarPopupSelectionViewController.swift */; };
		22B63A0E2C274FDA00C58D7B /* NotesTableTableViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 22B63A0D2C274FDA00C58D7B /* NotesTableTableViewController.swift */; };
		22B63A112C27E76900C58D7B /* SupportVCCustomerSupportCellTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 22B63A0F2C27E76900C58D7B /* SupportVCCustomerSupportCellTableViewCell.swift */; };
		22B63A122C27E76900C58D7B /* SupportVCCustomerSupportCellTableViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = 22B63A102C27E76900C58D7B /* SupportVCCustomerSupportCellTableViewCell.xib */; };
		22B63A152C27E85300C58D7B /* SupportVCDontFindAnswerCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 22B63A132C27E85300C58D7B /* SupportVCDontFindAnswerCell.swift */; };
		22B63A162C27E85300C58D7B /* SupportVCDontFindAnswerCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = 22B63A142C27E85300C58D7B /* SupportVCDontFindAnswerCell.xib */; };
		22B63A192C27E92C00C58D7B /* SupportVCQuestionCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 22B63A172C27E92C00C58D7B /* SupportVCQuestionCell.swift */; };
		22B63A1A2C27E92C00C58D7B /* SupportVCQuestionCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = 22B63A182C27E92C00C58D7B /* SupportVCQuestionCell.xib */; };
		22B63A1C2C28173E00C58D7B /* TimePickerView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 22B63A1B2C28173E00C58D7B /* TimePickerView.swift */; };
		22B63A1E2C284CFC00C58D7B /* EditTestDateAndTimeViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 22B63A1D2C284CFC00C58D7B /* EditTestDateAndTimeViewController.swift */; };
		22B63A222C2851DD00C58D7B /* EditTestDateVCTestTimeCellTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 22B63A212C2851DD00C58D7B /* EditTestDateVCTestTimeCellTableViewCell.swift */; };
		22B63A242C28591200C58D7B /* EditTestDateVCCalendarCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 22B63A232C28591200C58D7B /* EditTestDateVCCalendarCell.swift */; };
		22B63A262C285C5000C58D7B /* EditTestDateCycleCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 22B63A252C285C5000C58D7B /* EditTestDateCycleCell.swift */; };
		22B63A282C29C08600C58D7B /* PickerView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 22B63A272C29C08600C58D7B /* PickerView.swift */; };
		22B778222C8F3B610004F8A3 /* TandCPopupViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 22B778202C8F3B610004F8A3 /* TandCPopupViewController.swift */; };
		22B778232C8F3B610004F8A3 /* TandCPopupViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 22B778212C8F3B610004F8A3 /* TandCPopupViewController.xib */; };
		22D11B702C0CE2C1001AE12D /* NoteTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 22D11B6E2C0CE2C1001AE12D /* NoteTableViewCell.swift */; };
		22D11B712C0CE2C1001AE12D /* NoteTableViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = 22D11B6F2C0CE2C1001AE12D /* NoteTableViewCell.xib */; };
		22D11B742C0CE791001AE12D /* AddNoteViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 22D11B722C0CE791001AE12D /* AddNoteViewController.swift */; };
		22D11B752C0CE791001AE12D /* AddNoteViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 22D11B732C0CE791001AE12D /* AddNoteViewController.xib */; };
		22D11B8C2C0E04C8001AE12D /* Gilroy ExtraBold.otf in Resources */ = {isa = PBXBuildFile; fileRef = 22D11B782C0E04C5001AE12D /* Gilroy ExtraBold.otf */; };
		22D11B8D2C0E04C8001AE12D /* Gilroy ExtraBoldItalic.otf in Resources */ = {isa = PBXBuildFile; fileRef = 22D11B792C0E04C5001AE12D /* Gilroy ExtraBoldItalic.otf */; };
		22D11B8E2C0E04C8001AE12D /* Gilroy RegularItalic.otf in Resources */ = {isa = PBXBuildFile; fileRef = 22D11B7A2C0E04C5001AE12D /* Gilroy RegularItalic.otf */; };
		22D11B8F2C0E04C8001AE12D /* gilroy UltraLight.otf in Resources */ = {isa = PBXBuildFile; fileRef = 22D11B7B2C0E04C5001AE12D /* gilroy UltraLight.otf */; };
		22D11B902C0E04C8001AE12D /* Gilroy BoldItalic.otf in Resources */ = {isa = PBXBuildFile; fileRef = 22D11B7C2C0E04C6001AE12D /* Gilroy BoldItalic.otf */; };
		22D11B912C0E04C8001AE12D /* Gilroy LightItalic.otf in Resources */ = {isa = PBXBuildFile; fileRef = 22D11B7D2C0E04C6001AE12D /* Gilroy LightItalic.otf */; };
		22D11B922C0E04C8001AE12D /* Gilroy Heavy.otf in Resources */ = {isa = PBXBuildFile; fileRef = 22D11B7E2C0E04C6001AE12D /* Gilroy Heavy.otf */; };
		22D11B932C0E04C8001AE12D /* Gilroy HeavyItalic.otf in Resources */ = {isa = PBXBuildFile; fileRef = 22D11B7F2C0E04C6001AE12D /* Gilroy HeavyItalic.otf */; };
		22D11B942C0E04C8001AE12D /* Gilroy SemiBoldItalic.otf in Resources */ = {isa = PBXBuildFile; fileRef = 22D11B802C0E04C6001AE12D /* Gilroy SemiBoldItalic.otf */; };
		22D11B952C0E04C8001AE12D /* gilroy bold.otf in Resources */ = {isa = PBXBuildFile; fileRef = 22D11B812C0E04C7001AE12D /* gilroy bold.otf */; };
		22D11B962C0E04C8001AE12D /* Gilroy MediumItalic.otf in Resources */ = {isa = PBXBuildFile; fileRef = 22D11B822C0E04C7001AE12D /* Gilroy MediumItalic.otf */; };
		22D11B972C0E04C8001AE12D /* gilroy blackItalic.otf in Resources */ = {isa = PBXBuildFile; fileRef = 22D11B832C0E04C7001AE12D /* gilroy blackItalic.otf */; };
		22D11B982C0E04C8001AE12D /* Gilroy Light.otf in Resources */ = {isa = PBXBuildFile; fileRef = 22D11B842C0E04C7001AE12D /* Gilroy Light.otf */; };
		22D11B992C0E04C8001AE12D /* gilroy regular.otf in Resources */ = {isa = PBXBuildFile; fileRef = 22D11B852C0E04C7001AE12D /* gilroy regular.otf */; };
		22D11B9A2C0E04C8001AE12D /* Gilroy Thin.otf in Resources */ = {isa = PBXBuildFile; fileRef = 22D11B862C0E04C7001AE12D /* Gilroy Thin.otf */; };
		22D11B9B2C0E04C8001AE12D /* Gilroy Medium.otf in Resources */ = {isa = PBXBuildFile; fileRef = 22D11B872C0E04C8001AE12D /* Gilroy Medium.otf */; };
		22D11B9C2C0E04C8001AE12D /* gilroy UltraLightitalic.otf in Resources */ = {isa = PBXBuildFile; fileRef = 22D11B882C0E04C8001AE12D /* gilroy UltraLightitalic.otf */; };
		22D11B9D2C0E04C8001AE12D /* gilroy black.otf in Resources */ = {isa = PBXBuildFile; fileRef = 22D11B892C0E04C8001AE12D /* gilroy black.otf */; };
		22D11B9E2C0E04C8001AE12D /* Gilroy ThinItalic.otf in Resources */ = {isa = PBXBuildFile; fileRef = 22D11B8A2C0E04C8001AE12D /* Gilroy ThinItalic.otf */; };
		22D11B9F2C0E04C8001AE12D /* Gilroy SemiBold.otf in Resources */ = {isa = PBXBuildFile; fileRef = 22D11B8B2C0E04C8001AE12D /* Gilroy SemiBold.otf */; };
		22DF42F42C5A7D1900D9DEDE /* NoticeTableViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = 22DF42F22C5A7D1800D9DEDE /* NoticeTableViewCell.xib */; };
		22DF42F52C5A7D1900D9DEDE /* NoticeTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 22DF42F32C5A7D1800D9DEDE /* NoticeTableViewCell.swift */; };
		22DF42F82C5A83B800D9DEDE /* ReminderViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 22DF42F62C5A83B700D9DEDE /* ReminderViewController.xib */; };
		22DF42F92C5A83B800D9DEDE /* ReminderViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 22DF42F72C5A83B800D9DEDE /* ReminderViewController.swift */; };
		22E327CA2CB19F1B001DDC04 /* LineChartViewTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 22E327C92CB19F1B001DDC04 /* LineChartViewTableViewCell.swift */; };
		22EA43C32CD773E4008026F6 /* AttarchmentDownloadButtonView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 22EA43C22CD773E4008026F6 /* AttarchmentDownloadButtonView.swift */; };
		22F6ED182C7A1D42004A27CC /* SampleChartViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 22F6ED172C7A1D42004A27CC /* SampleChartViewController.swift */; };
		22F6ED1A2C7CEAF2004A27CC /* LineChartDemoView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 22F6ED192C7CEAF2004A27CC /* LineChartDemoView.swift */; };
		22F6ED1C2C8204D1004A27CC /* ChartDetailViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 22F6ED1B2C8204D1004A27CC /* ChartDetailViewController.swift */; };
		22F6ED1E2C8209B9004A27CC /* ChartDetailColorLabelView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 22F6ED1D2C8209B9004A27CC /* ChartDetailColorLabelView.swift */; };
		22F6ED202C822553004A27CC /* ChartDetailView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 22F6ED1F2C822553004A27CC /* ChartDetailView.swift */; };
		22F6ED222C838879004A27CC /* CycleReportTableViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 22F6ED212C838879004A27CC /* CycleReportTableViewController.swift */; };
		22F6ED242C838A33004A27CC /* CycleReportHeaderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 22F6ED232C838A33004A27CC /* CycleReportHeaderView.swift */; };
		22F6ED262C83912D004A27CC /* CycleReportTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 22F6ED252C83912D004A27CC /* CycleReportTableViewCell.swift */; };
		22F6ED282C875681004A27CC /* ConfirmResultPopupViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 22F6ED272C875681004A27CC /* ConfirmResultPopupViewController.swift */; };
		22F6ED2A2C89FFCD004A27CC /* DeleteResultPopupViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 22F6ED292C89FFCD004A27CC /* DeleteResultPopupViewController.swift */; };
		2D4C75F92CDDEA340030B544 /* SQAutoScrollView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2D4C75F62CDDEA340030B544 /* SQAutoScrollView.swift */; };
		2D4C75FA2CDDEA340030B544 /* SQDotView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2D4C75F72CDDEA340030B544 /* SQDotView.swift */; };
		2D4C75FB2CDDEA340030B544 /* SQPageControl.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2D4C75F82CDDEA340030B544 /* SQPageControl.swift */; };
		2D4C75FF2CDDEAE50030B544 /* MBProgressHUD.m in Sources */ = {isa = PBXBuildFile; fileRef = 2D4C75FD2CDDEAE50030B544 /* MBProgressHUD.m */; };
		4B1E1BB62C6E53B0001FE006 /* UserTestViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4B1E1BB52C6E53B0001FE006 /* UserTestViewModel.swift */; };
		4B1E1BB82C6E6054001FE006 /* AutoDismissToastView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4B1E1BB72C6E6054001FE006 /* AutoDismissToastView.swift */; };
		4B3CFF432C5773600032F90F /* Documents.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4B3CFF422C5773600032F90F /* Documents.swift */; };
		4B3CFF452C577CAC0032F90F /* UploadFileViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4B3CFF442C577CAC0032F90F /* UploadFileViewModel.swift */; };
		4BF80DAA2C64AF580039F791 /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = 4BF80DA92C64AF580039F791 /* GoogleService-Info.plist */; };
		800E7A492C8B52E400AA9397 /* UserNoteCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 800E7A482C8B52E400AA9397 /* UserNoteCell.swift */; };
		800E7A4B2C8B598300AA9397 /* HomSingleImageCollectionViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 800E7A4A2C8B598300AA9397 /* HomSingleImageCollectionViewCell.swift */; };
		800E7A5C2C8B5E1500AA9397 /* JXPhotoBrowserPageIndicator.swift in Sources */ = {isa = PBXBuildFile; fileRef = 800E7A4E2C8B5E1500AA9397 /* JXPhotoBrowserPageIndicator.swift */; };
		800E7A5D2C8B5E1500AA9397 /* JXPhotoBrowserNoneAnimator.swift in Sources */ = {isa = PBXBuildFile; fileRef = 800E7A4F2C8B5E1500AA9397 /* JXPhotoBrowserNoneAnimator.swift */; };
		800E7A5E2C8B5E1500AA9397 /* JXPhotoBrowserZoomSupportedCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 800E7A502C8B5E1500AA9397 /* JXPhotoBrowserZoomSupportedCell.swift */; };
		800E7A5F2C8B5E1500AA9397 /* JXPhotoBrowserSmoothZoomAnimator.swift in Sources */ = {isa = PBXBuildFile; fileRef = 800E7A512C8B5E1500AA9397 /* JXPhotoBrowserSmoothZoomAnimator.swift */; };
		800E7A602C8B5E1500AA9397 /* JXPhotoBrowserAnimatedTransitioning.swift in Sources */ = {isa = PBXBuildFile; fileRef = 800E7A522C8B5E1500AA9397 /* JXPhotoBrowserAnimatedTransitioning.swift */; };
		800E7A612C8B5E1500AA9397 /* JXPhotoBrowserCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 800E7A532C8B5E1500AA9397 /* JXPhotoBrowserCell.swift */; };
		800E7A622C8B5E1500AA9397 /* JXPhotoBrowserNumberPageIndicator.swift in Sources */ = {isa = PBXBuildFile; fileRef = 800E7A542C8B5E1500AA9397 /* JXPhotoBrowserNumberPageIndicator.swift */; };
		800E7A632C8B5E1500AA9397 /* JXPhotoBrowserZoomAnimator.swift in Sources */ = {isa = PBXBuildFile; fileRef = 800E7A552C8B5E1500AA9397 /* JXPhotoBrowserZoomAnimator.swift */; };
		800E7A642C8B5E1500AA9397 /* JXPhotoBrowserView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 800E7A562C8B5E1500AA9397 /* JXPhotoBrowserView.swift */; };
		800E7A652C8B5E1500AA9397 /* JXPhotoBrowserDefaultPageIndicator.swift in Sources */ = {isa = PBXBuildFile; fileRef = 800E7A572C8B5E1500AA9397 /* JXPhotoBrowserDefaultPageIndicator.swift */; };
		800E7A662C8B5E1500AA9397 /* JXPhotoBrowserImageCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 800E7A582C8B5E1500AA9397 /* JXPhotoBrowserImageCell.swift */; };
		800E7A672C8B5E1500AA9397 /* JXPhotoBrowserFadeAnimator.swift in Sources */ = {isa = PBXBuildFile; fileRef = 800E7A592C8B5E1500AA9397 /* JXPhotoBrowserFadeAnimator.swift */; };
		800E7A682C8B5E1500AA9397 /* JXPhotoBrowserLog.swift in Sources */ = {isa = PBXBuildFile; fileRef = 800E7A5A2C8B5E1500AA9397 /* JXPhotoBrowserLog.swift */; };
		800E7A692C8B5E1500AA9397 /* JXPhotoBrowser.swift in Sources */ = {isa = PBXBuildFile; fileRef = 800E7A5B2C8B5E1500AA9397 /* JXPhotoBrowser.swift */; };
		800E7A6B2C8BFD3F00AA9397 /* NoteDetailViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 800E7A6A2C8BFD3F00AA9397 /* NoteDetailViewController.swift */; };
		800E7A6D2C8BFE0D00AA9397 /* NoteDetailViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 800E7A6C2C8BFE0D00AA9397 /* NoteDetailViewModel.swift */; };
		800E7A722C8C1FAD00AA9397 /* FeedbackAddViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 800E7A712C8C1FAD00AA9397 /* FeedbackAddViewModel.swift */; };
		800E7A762C8C40E300AA9397 /* SupportOperViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 800E7A752C8C40E300AA9397 /* SupportOperViewModel.swift */; };
		800F3F5C2C8C5685000104FF /* SupportOperListViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 800F3F5B2C8C5685000104FF /* SupportOperListViewModel.swift */; };
		800F3F5E2C8C7AF8000104FF /* SuportWebViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 800F3F5D2C8C7AF8000104FF /* SuportWebViewController.swift */; };
		800F3F602C8C8021000104FF /* AboutUsViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 800F3F5F2C8C8021000104FF /* AboutUsViewController.swift */; };
		800F3F622C8C823A000104FF /* CommonViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 800F3F612C8C823A000104FF /* CommonViewModel.swift */; };
		8055D27C2CC25CED001D8EBE /* CycleReportCaptureView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8055D27B2CC25CED001D8EBE /* CycleReportCaptureView.swift */; };
		8078B69F2C9C6357002D7079 /* AuthenticationServices.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8078B69E2C9C6357002D7079 /* AuthenticationServices.framework */; };
		8078B6A22C9C7C6D002D7079 /* ThirdLoginViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8078B6A12C9C7C6D002D7079 /* ThirdLoginViewModel.swift */; };
		80792F462CB42F6300A8770A /* AddImageCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 80792F452CB42F6300A8770A /* AddImageCell.swift */; };
		80792F512CB65F4800A8770A /* RemindViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 80792F502CB65F4800A8770A /* RemindViewModel.swift */; };
		80792F532CB6659300A8770A /* ReminderMessageCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 80792F522CB6659300A8770A /* ReminderMessageCell.swift */; };
		807EDDE72CAFE10F003706CF /* BindAccountViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 807EDDE62CAFE10F003706CF /* BindAccountViewModel.swift */; };
		808571A32C883B4500F1BB9E /* WFNetwokrManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 808571A22C883B4500F1BB9E /* WFNetwokrManager.swift */; };
		808571A52C894D6500F1BB9E /* BaseViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 808571A42C894D6500F1BB9E /* BaseViewModel.swift */; };
		808571AB2C89506700F1BB9E /* NotePageViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 808571AA2C89506700F1BB9E /* NotePageViewModel.swift */; };
		808571AD2C8952E700F1BB9E /* AddNoteViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 808571AC2C8952E700F1BB9E /* AddNoteViewModel.swift */; };
		808571AF2C89923000F1BB9E /* SystemTagViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 808571AE2C89923000F1BB9E /* SystemTagViewModel.swift */; };
		808571B32C89AC4200F1BB9E /* BaseNavigationController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 808571B22C89AC4200F1BB9E /* BaseNavigationController.swift */; };
		80CA05D12C8B4CF900CCBC47 /* BaseTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 80CA05D02C8B4CF900CCBC47 /* BaseTableViewCell.swift */; };
		80CA05E82C8B4DFD00CCBC47 /* RectangleLayout.m in Sources */ = {isa = PBXBuildFile; fileRef = 80CA05E32C8B4DFD00CCBC47 /* RectangleLayout.m */; };
		80CA05E92C8B4DFD00CCBC47 /* WaterFallFlowLayout.swift in Sources */ = {isa = PBXBuildFile; fileRef = 80CA05E52C8B4DFD00CCBC47 /* WaterFallFlowLayout.swift */; };
		80CA05EA2C8B4DFD00CCBC47 /* HorizontalLayout.m in Sources */ = {isa = PBXBuildFile; fileRef = 80CA05E72C8B4DFD00CCBC47 /* HorizontalLayout.m */; };
		80E3481C2C9A61E2000996C5 /* MessageViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 80E3481B2C9A61E2000996C5 /* MessageViewModel.swift */; };
		80FC97712CBFCEA1006BA376 /* LuanchAdView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 80FC97702CBFCEA1006BA376 /* LuanchAdView.swift */; };
		80FC97732CBFE788006BA376 /* CustomHudView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 80FC97722CBFE788006BA376 /* CustomHudView.swift */; };
		80FC97752CBFF490006BA376 /* HistoryCaptureView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 80FC97742CBFF490006BA376 /* HistoryCaptureView.swift */; };
		80FC97AF2CC00815006BA376 /* LBXScanNative.m in Sources */ = {isa = PBXBuildFile; fileRef = 80FC97AE2CC00815006BA376 /* LBXScanNative.m */; };
		94CCB296A71664ACBF6DC902 /* Pods_HormoneLife.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 56B80A67C5911CD03EF0D6D5 /* Pods_HormoneLife.framework */; };
		BD4CA4B02E30D56500E50E79 /* AIChatVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD4CA4AF2E30D56500E50E79 /* AIChatVC.swift */; };
		************************ /* ContactInfoTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD74B3BD2E1907940094929E /* ContactInfoTableViewCell.swift */; };
		************************ /* VerificationCodeViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* VerificationCodeViewController.swift */; };
		BDC213892E1E208C00F4128E /* CaptureStateManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = BDC213882E1E208C00F4128E /* CaptureStateManager.swift */; };
		BDF5E8CD2E165A7D0049236F /* AIWebVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = BDF5E8CC2E165A7D0049236F /* AIWebVC.swift */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		189A14C92C1AD70F00394477 /* ScanView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ScanView.swift; sourceTree = "<group>"; };
		189E91C92C1A7F6A007D04CA /* RecordTestsViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RecordTestsViewController.swift; sourceTree = "<group>"; };
		189E91CA2C1A7F6A007D04CA /* RecordTestsViewController.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = RecordTestsViewController.xib; sourceTree = "<group>"; };
		18A1F5212C1C2324003BE30C /* ImageCropperViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ImageCropperViewController.swift; sourceTree = "<group>"; };
		18A1F5232C1C27B1003BE30C /* TBImageCropperHelper.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TBImageCropperHelper.swift; sourceTree = "<group>"; };
		18A1F5252C1C3FFE003BE30C /* CustomCameraViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CustomCameraViewController.swift; sourceTree = "<group>"; };
		18E5F8B92C1C187C002CD571 /* OverlayView.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = OverlayView.xib; sourceTree = "<group>"; };
		18E5F8BA2C1C187C002CD571 /* OverlayView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = OverlayView.swift; sourceTree = "<group>"; };
		18E5F8BB2C1C187C002CD571 /* FullScreenCameraViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = FullScreenCameraViewController.swift; sourceTree = "<group>"; };
		18E5F8BF2C1C19B8002CD571 /* NibLoadable.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NibLoadable.swift; sourceTree = "<group>"; };
		1B1CB7E82C312E0C00E99055 /* BalloonMarker.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = BalloonMarker.swift; sourceTree = "<group>"; };
		1B1D76712C4FF24600134308 /* HistoryRecordBottomView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HistoryRecordBottomView.swift; sourceTree = "<group>"; };
		1B1D76732C50024400134308 /* EditTestResultController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EditTestResultController.swift; sourceTree = "<group>"; };
		1B1D76752C50083900134308 /* EditTestResultBottomView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EditTestResultBottomView.swift; sourceTree = "<group>"; };
		1B1D76772C500A3F00134308 /* TestTimeView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TestTimeView.swift; sourceTree = "<group>"; };
		1B381CBD2C5CF7520071D14F /* HistoryRecordFooterView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HistoryRecordFooterView.swift; sourceTree = "<group>"; };
		1B49621A2C283B7B00B67BAF /* ChartsHeaderView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ChartsHeaderView.swift; sourceTree = "<group>"; };
		1B49621C2C3079C000B67BAF /* ChartsCardView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ChartsCardView.swift; sourceTree = "<group>"; };
		1B49621E2C308A0900B67BAF /* LineChartFilledView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LineChartFilledView.swift; sourceTree = "<group>"; };
		1B97A3F82C4F0BCC00FA5BF0 /* HistoryRecordCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HistoryRecordCell.swift; sourceTree = "<group>"; };
		1B97A3F92C4F0BCC00FA5BF0 /* HistoryRecordCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = HistoryRecordCell.xib; sourceTree = "<group>"; };
		1B992BD02C4F0333002FCF2D /* HistoryRecordViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HistoryRecordViewController.swift; sourceTree = "<group>"; };
		1BE0234B2C1734ED00957955 /* ChartsViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ChartsViewController.swift; sourceTree = "<group>"; };
		1BE0234D2C17366E00957955 /* ChartsTitleView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ChartsTitleView.swift; sourceTree = "<group>"; };
		2201CD572C5CD18E004E1BE2 /* HomeCameraPopupView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HomeCameraPopupView.swift; sourceTree = "<group>"; };
		2201CD592C5FBCDA004E1BE2 /* TagsTableViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TagsTableViewCell.swift; sourceTree = "<group>"; };
		2201CD5B2C5FC101004E1BE2 /* TagsTableViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TagsTableViewController.swift; sourceTree = "<group>"; };
		2201CD5D2C607026004E1BE2 /* SupportAndHelpFeedbackTableViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SupportAndHelpFeedbackTableViewController.swift; sourceTree = "<group>"; };
		2201CD5F2C607263004E1BE2 /* FeedbackTableViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FeedbackTableViewCell.swift; sourceTree = "<group>"; };
		220AA8C52CC7F88300BEEAB3 /* UnbingPopupViewController.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = UnbingPopupViewController.xib; sourceTree = "<group>"; };
		220AA8C62CC7F88300BEEAB3 /* UnbingPopupViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = UnbingPopupViewController.swift; sourceTree = "<group>"; };
		220AA8C92CC8014200BEEAB3 /* EmptyTableViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EmptyTableViewCell.swift; sourceTree = "<group>"; };
		22105BED2CBEDB9600F5785C /* HomeDataSingleton.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HomeDataSingleton.swift; sourceTree = "<group>"; };
		2212D1D72C17421100A6680E /* NetWorkHandle.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NetWorkHandle.swift; sourceTree = "<group>"; };
		2212D1D82C17421200A6680E /* Interactor.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Interactor.swift; sourceTree = "<group>"; };
		2212D1DD2C17451E00A6680E /* Common.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Common.swift; sourceTree = "<group>"; };
		2212D1DF2C17493400A6680E /* Model.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Model.swift; sourceTree = "<group>"; };
		2212D1E12C17536400A6680E /* UserDefaultTool.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = UserDefaultTool.swift; sourceTree = "<group>"; };
		2212D1E32C1753A700A6680E /* Interface.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Interface.swift; sourceTree = "<group>"; };
		221D64992D26FDAF000E7A08 /* ConfirmErrorPopupViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ConfirmErrorPopupViewController.swift; sourceTree = "<group>"; };
		222CF7CE2C9567B4005F389D /* ChangeConceptionViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ChangeConceptionViewController.swift; sourceTree = "<group>"; };
		223065612C02E2D900638708 /* SignUpViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SignUpViewController.swift; sourceTree = "<group>"; };
		223065622C02E2D900638708 /* SignUpViewController.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = SignUpViewController.xib; sourceTree = "<group>"; };
		223776482C143C0D00A2575F /* RootViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = RootViewController.swift; sourceTree = "<group>"; };
		2237764A2C14768900A2575F /* ProfileViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ProfileViewController.swift; sourceTree = "<group>"; };
		2237764B2C14768900A2575F /* ProfileViewController.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = ProfileViewController.xib; sourceTree = "<group>"; };
		2237764E2C14B17000A2575F /* UIFont+Extension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "UIFont+Extension.swift"; sourceTree = "<group>"; };
		223776502C14C2FE00A2575F /* UIApplecation+Extension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "UIApplecation+Extension.swift"; sourceTree = "<group>"; };
		223776522C14CA5A00A2575F /* HomeCarouselView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = HomeCarouselView.swift; sourceTree = "<group>"; };
		223776562C14CF8800A2575F /* carouselSampleImage.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = carouselSampleImage.png; sourceTree = "<group>"; };
		223776582C155B8100A2575F /* SupportAndHelpHomeViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SupportAndHelpHomeViewController.swift; sourceTree = "<group>"; };
		223776592C155B8100A2575F /* SupportAndHelpHomeViewController.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = SupportAndHelpHomeViewController.xib; sourceTree = "<group>"; };
		223776602C15B26800A2575F /* AboutHormonelifeViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AboutHormonelifeViewController.swift; sourceTree = "<group>"; };
		223776612C15B26800A2575F /* AboutHormonelifeViewController.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = AboutHormonelifeViewController.xib; sourceTree = "<group>"; };
		223776642C15B71D00A2575F /* SettingViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SettingViewController.swift; sourceTree = "<group>"; };
		223776652C15B71D00A2575F /* SettingViewController.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = SettingViewController.xib; sourceTree = "<group>"; };
		223776682C16003200A2575F /* ChangePasswordViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ChangePasswordViewController.swift; sourceTree = "<group>"; };
		223776692C16003200A2575F /* ChangePasswordViewController.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = ChangePasswordViewController.xib; sourceTree = "<group>"; };
		224CA4422C01E81A00FAD0CC /* LoginViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LoginViewController.swift; sourceTree = "<group>"; };
		224CA4432C01E81A00FAD0CC /* LoginViewController.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = LoginViewController.xib; sourceTree = "<group>"; };
		2252D67E2C037DAA00E3672F /* WebViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WebViewController.swift; sourceTree = "<group>"; };
		2252D67F2C037DAA00E3672F /* WebViewController.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = WebViewController.xib; sourceTree = "<group>"; };
		2252D6822C037E3F00E3672F /* ForgotPassWordViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ForgotPassWordViewController.swift; sourceTree = "<group>"; };
		2252D6832C037E3F00E3672F /* ForgotPassWordViewController.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = ForgotPassWordViewController.xib; sourceTree = "<group>"; };
		2252D6862C0384E200E3672F /* CreateAccountPopupViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CreateAccountPopupViewController.swift; sourceTree = "<group>"; };
		2252D6872C0384E200E3672F /* CreateAccountPopupViewController.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = CreateAccountPopupViewController.xib; sourceTree = "<group>"; };
		225880392C579C5700DCEFC1 /* TandCResultView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TandCResultView.swift; sourceTree = "<group>"; };
		225922B22C57E30E0053FDC3 /* EditTestTimeView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EditTestTimeView.swift; sourceTree = "<group>"; };
		225922B42C5948DE0053FDC3 /* NotificationsViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NotificationsViewController.swift; sourceTree = "<group>"; };
		225922B52C5948DF0053FDC3 /* NotificationsViewController.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = NotificationsViewController.xib; sourceTree = "<group>"; };
		225D725F2C32F6AA005DAAAC /* TestStripsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TestStripsView.swift; sourceTree = "<group>"; };
		2260902F2BEF93440053F10E /* HormoneLife.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = HormoneLife.app; sourceTree = BUILT_PRODUCTS_DIR; };
		226090322BEF93440053F10E /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		226090362BEF93440053F10E /* ViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ViewController.swift; sourceTree = "<group>"; };
		226090392BEF93440053F10E /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		2260903B2BEF93460053F10E /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		2260903E2BEF93460053F10E /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		226090402BEF93460053F10E /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		2260904D2BEFC1610053F10E /* BaseViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = BaseViewController.swift; sourceTree = "<group>"; };
		226090502BEFC2260053F10E /* UIViewController+Modal.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = "UIViewController+Modal.swift"; sourceTree = "<group>"; };
		226090512BEFC2260053F10E /* UILabel+Extension.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = "UILabel+Extension.swift"; sourceTree = "<group>"; };
		226090522BEFC2260053F10E /* UIView+Extension.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = "UIView+Extension.swift"; sourceTree = "<group>"; };
		226090542BEFC2260053F10E /* Day+Extension.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = "Day+Extension.swift"; sourceTree = "<group>"; };
		226090552BEFC2260053F10E /* String+Extension.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = "String+Extension.swift"; sourceTree = "<group>"; };
		226090562BEFC2260053F10E /* UIColor+Extension.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = "UIColor+Extension.swift"; sourceTree = "<group>"; };
		2260905E2BEFC3000053F10E /* LanguageManager.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = LanguageManager.swift; sourceTree = "<group>"; };
		226090602BEFC40F0053F10E /* TabBarController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = TabBarController.swift; sourceTree = "<group>"; };
		22680D8E2C0A215E00B5DCF2 /* BirthdaySettingViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BirthdaySettingViewController.swift; sourceTree = "<group>"; };
		22680D8F2C0A215E00B5DCF2 /* BirthdaySettingViewController.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = BirthdaySettingViewController.xib; sourceTree = "<group>"; };
		22680D922C0A2DC700B5DCF2 /* BoundViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BoundViewController.swift; sourceTree = "<group>"; };
		22680D932C0A2DC700B5DCF2 /* BoundViewController.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = BoundViewController.xib; sourceTree = "<group>"; };
		22680D962C0C1F9700B5DCF2 /* HomeViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HomeViewController.swift; sourceTree = "<group>"; };
		22680D972C0C1F9700B5DCF2 /* HomeViewController.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = HomeViewController.xib; sourceTree = "<group>"; };
		226D298C2C1EFCB40058E91E /* MessageTableViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MessageTableViewCell.swift; sourceTree = "<group>"; };
		226D298D2C1EFCB40058E91E /* MessageTableViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = MessageTableViewCell.xib; sourceTree = "<group>"; };
		226D29952C21E7A40058E91E /* AppCalendarCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AppCalendarCell.swift; sourceTree = "<group>"; };
		226D29962C21E7A40058E91E /* AppCalendarView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AppCalendarView.swift; sourceTree = "<group>"; };
		226D29972C21E7A40058E91E /* AppCalendarDayTypeCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AppCalendarDayTypeCell.swift; sourceTree = "<group>"; };
		226D29982C21E7A40058E91E /* AppCalendarViewDelegate.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AppCalendarViewDelegate.swift; sourceTree = "<group>"; };
		226D29992C21E7A40058E91E /* AppCalendarConfig.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AppCalendarConfig.swift; sourceTree = "<group>"; };
		226D299F2C21EAD80058E91E /* CalendarPopupViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CalendarPopupViewController.swift; sourceTree = "<group>"; };
		226D29A12C21EF690058E91E /* ExtensionFile.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ExtensionFile.swift; sourceTree = "<group>"; };
		226D29A32C21EF810058E91E /* PickPhotoViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = PickPhotoViewController.swift; sourceTree = "<group>"; };
		2272A7D12C160B0400FCB0F5 /* DeleteAccountViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = DeleteAccountViewController.swift; sourceTree = "<group>"; };
		2272A7D22C160B0500FCB0F5 /* DeleteAccountViewController.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = DeleteAccountViewController.xib; sourceTree = "<group>"; };
		2286B0902C1CA6E700C59403 /* UITextField+Extension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "UITextField+Extension.swift"; sourceTree = "<group>"; };
		2286B0922C1ED05B00C59403 /* TagsCollectionViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TagsCollectionViewCell.swift; sourceTree = "<group>"; };
		2286B0932C1ED05B00C59403 /* TagsCollectionViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = TagsCollectionViewCell.xib; sourceTree = "<group>"; };
		2286B0962C1ED73A00C59403 /* LeftAlignedCollectionViewFlowLayout.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LeftAlignedCollectionViewFlowLayout.swift; sourceTree = "<group>"; };
		2291C56A2C063F46005CC00F /* GoalSettingViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GoalSettingViewController.swift; sourceTree = "<group>"; };
		2291C56B2C063F46005CC00F /* GoalSettingViewController.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = GoalSettingViewController.xib; sourceTree = "<group>"; };
		22A78B442C168854006C8B33 /* PersonalInfoViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = PersonalInfoViewController.swift; sourceTree = "<group>"; };
		22A78B452C168855006C8B33 /* PersonalInfoViewController.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = PersonalInfoViewController.xib; sourceTree = "<group>"; };
		22A78B482C1691F6006C8B33 /* TagsViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TagsViewController.swift; sourceTree = "<group>"; };
		22A78B492C1691F6006C8B33 /* TagsViewController.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = TagsViewController.xib; sourceTree = "<group>"; };
		22A78B4C2C1694DD006C8B33 /* AddTagViewController.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = AddTagViewController.xib; sourceTree = "<group>"; };
		22A78B4D2C1694DE006C8B33 /* AddTagViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AddTagViewController.swift; sourceTree = "<group>"; };
		22A78B502C169BD1006C8B33 /* MessagesViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MessagesViewController.swift; sourceTree = "<group>"; };
		22A78B512C169BD1006C8B33 /* MessagesViewController.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = MessagesViewController.xib; sourceTree = "<group>"; };
		22A78B542C16A03A006C8B33 /* MessageSettingViewController.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = MessageSettingViewController.xib; sourceTree = "<group>"; };
		22A78B552C16A03A006C8B33 /* MessageSettingViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MessageSettingViewController.swift; sourceTree = "<group>"; };
		22A78D2C2C56469300C3A271 /* CircleDemoView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CircleDemoView.swift; sourceTree = "<group>"; };
		22A78D302C56476F00C3A271 /* ResultViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ResultViewController.swift; sourceTree = "<group>"; };
		22B39A0D2CBF086100E98FEC /* LineChartDownloadViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LineChartDownloadViewController.swift; sourceTree = "<group>"; };
		22B639FF2C248FF600C58D7B /* CalendarTableViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CalendarTableViewController.swift; sourceTree = "<group>"; };
		22B63A012C2497B800C58D7B /* CalendarLabelCellTableViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CalendarLabelCellTableViewCell.swift; sourceTree = "<group>"; };
		22B63A032C2657F800C58D7B /* CalendarVCSectionCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CalendarVCSectionCell.swift; sourceTree = "<group>"; };
		22B63A0B2C267B1D00C58D7B /* CalendarPopupSelectionViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CalendarPopupSelectionViewController.swift; sourceTree = "<group>"; };
		22B63A0D2C274FDA00C58D7B /* NotesTableTableViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NotesTableTableViewController.swift; sourceTree = "<group>"; };
		22B63A0F2C27E76900C58D7B /* SupportVCCustomerSupportCellTableViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SupportVCCustomerSupportCellTableViewCell.swift; sourceTree = "<group>"; };
		22B63A102C27E76900C58D7B /* SupportVCCustomerSupportCellTableViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = SupportVCCustomerSupportCellTableViewCell.xib; sourceTree = "<group>"; };
		22B63A132C27E85300C58D7B /* SupportVCDontFindAnswerCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SupportVCDontFindAnswerCell.swift; sourceTree = "<group>"; };
		22B63A142C27E85300C58D7B /* SupportVCDontFindAnswerCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = SupportVCDontFindAnswerCell.xib; sourceTree = "<group>"; };
		22B63A172C27E92C00C58D7B /* SupportVCQuestionCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SupportVCQuestionCell.swift; sourceTree = "<group>"; };
		22B63A182C27E92C00C58D7B /* SupportVCQuestionCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = SupportVCQuestionCell.xib; sourceTree = "<group>"; };
		22B63A1B2C28173E00C58D7B /* TimePickerView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TimePickerView.swift; sourceTree = "<group>"; };
		22B63A1D2C284CFC00C58D7B /* EditTestDateAndTimeViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EditTestDateAndTimeViewController.swift; sourceTree = "<group>"; };
		22B63A212C2851DD00C58D7B /* EditTestDateVCTestTimeCellTableViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EditTestDateVCTestTimeCellTableViewCell.swift; sourceTree = "<group>"; };
		22B63A232C28591200C58D7B /* EditTestDateVCCalendarCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EditTestDateVCCalendarCell.swift; sourceTree = "<group>"; };
		22B63A252C285C5000C58D7B /* EditTestDateCycleCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EditTestDateCycleCell.swift; sourceTree = "<group>"; };
		22B63A272C29C08600C58D7B /* PickerView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = PickerView.swift; sourceTree = "<group>"; };
		22B778202C8F3B610004F8A3 /* TandCPopupViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TandCPopupViewController.swift; sourceTree = "<group>"; };
		22B778212C8F3B610004F8A3 /* TandCPopupViewController.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = TandCPopupViewController.xib; sourceTree = "<group>"; };
		22D11B6E2C0CE2C1001AE12D /* NoteTableViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NoteTableViewCell.swift; sourceTree = "<group>"; };
		22D11B6F2C0CE2C1001AE12D /* NoteTableViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = NoteTableViewCell.xib; sourceTree = "<group>"; };
		22D11B722C0CE791001AE12D /* AddNoteViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AddNoteViewController.swift; sourceTree = "<group>"; };
		22D11B732C0CE791001AE12D /* AddNoteViewController.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = AddNoteViewController.xib; sourceTree = "<group>"; };
		22D11B782C0E04C5001AE12D /* Gilroy ExtraBold.otf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Gilroy ExtraBold.otf"; sourceTree = "<group>"; };
		22D11B792C0E04C5001AE12D /* Gilroy ExtraBoldItalic.otf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Gilroy ExtraBoldItalic.otf"; sourceTree = "<group>"; };
		22D11B7A2C0E04C5001AE12D /* Gilroy RegularItalic.otf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Gilroy RegularItalic.otf"; sourceTree = "<group>"; };
		22D11B7B2C0E04C5001AE12D /* gilroy UltraLight.otf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "gilroy UltraLight.otf"; sourceTree = "<group>"; };
		22D11B7C2C0E04C6001AE12D /* Gilroy BoldItalic.otf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Gilroy BoldItalic.otf"; sourceTree = "<group>"; };
		22D11B7D2C0E04C6001AE12D /* Gilroy LightItalic.otf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Gilroy LightItalic.otf"; sourceTree = "<group>"; };
		22D11B7E2C0E04C6001AE12D /* Gilroy Heavy.otf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Gilroy Heavy.otf"; sourceTree = "<group>"; };
		22D11B7F2C0E04C6001AE12D /* Gilroy HeavyItalic.otf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Gilroy HeavyItalic.otf"; sourceTree = "<group>"; };
		22D11B802C0E04C6001AE12D /* Gilroy SemiBoldItalic.otf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Gilroy SemiBoldItalic.otf"; sourceTree = "<group>"; };
		22D11B812C0E04C7001AE12D /* gilroy bold.otf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "gilroy bold.otf"; sourceTree = "<group>"; };
		22D11B822C0E04C7001AE12D /* Gilroy MediumItalic.otf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Gilroy MediumItalic.otf"; sourceTree = "<group>"; };
		22D11B832C0E04C7001AE12D /* gilroy blackItalic.otf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "gilroy blackItalic.otf"; sourceTree = "<group>"; };
		22D11B842C0E04C7001AE12D /* Gilroy Light.otf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Gilroy Light.otf"; sourceTree = "<group>"; };
		22D11B852C0E04C7001AE12D /* gilroy regular.otf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "gilroy regular.otf"; sourceTree = "<group>"; };
		22D11B862C0E04C7001AE12D /* Gilroy Thin.otf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Gilroy Thin.otf"; sourceTree = "<group>"; };
		22D11B872C0E04C8001AE12D /* Gilroy Medium.otf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Gilroy Medium.otf"; sourceTree = "<group>"; };
		22D11B882C0E04C8001AE12D /* gilroy UltraLightitalic.otf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "gilroy UltraLightitalic.otf"; sourceTree = "<group>"; };
		22D11B892C0E04C8001AE12D /* gilroy black.otf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "gilroy black.otf"; sourceTree = "<group>"; };
		22D11B8A2C0E04C8001AE12D /* Gilroy ThinItalic.otf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Gilroy ThinItalic.otf"; sourceTree = "<group>"; };
		22D11B8B2C0E04C8001AE12D /* Gilroy SemiBold.otf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Gilroy SemiBold.otf"; sourceTree = "<group>"; };
		22DF42F22C5A7D1800D9DEDE /* NoticeTableViewCell.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = NoticeTableViewCell.xib; sourceTree = "<group>"; };
		22DF42F32C5A7D1800D9DEDE /* NoticeTableViewCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NoticeTableViewCell.swift; sourceTree = "<group>"; };
		22DF42F62C5A83B700D9DEDE /* ReminderViewController.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = ReminderViewController.xib; sourceTree = "<group>"; };
		22DF42F72C5A83B800D9DEDE /* ReminderViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ReminderViewController.swift; sourceTree = "<group>"; };
		22E327C92CB19F1B001DDC04 /* LineChartViewTableViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LineChartViewTableViewCell.swift; sourceTree = "<group>"; };
		22EA43C22CD773E4008026F6 /* AttarchmentDownloadButtonView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AttarchmentDownloadButtonView.swift; sourceTree = "<group>"; };
		22F6ED172C7A1D42004A27CC /* SampleChartViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SampleChartViewController.swift; sourceTree = "<group>"; };
		22F6ED192C7CEAF2004A27CC /* LineChartDemoView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LineChartDemoView.swift; sourceTree = "<group>"; };
		22F6ED1B2C8204D1004A27CC /* ChartDetailViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ChartDetailViewController.swift; sourceTree = "<group>"; };
		22F6ED1D2C8209B9004A27CC /* ChartDetailColorLabelView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ChartDetailColorLabelView.swift; sourceTree = "<group>"; };
		22F6ED1F2C822553004A27CC /* ChartDetailView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ChartDetailView.swift; sourceTree = "<group>"; };
		22F6ED212C838879004A27CC /* CycleReportTableViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CycleReportTableViewController.swift; sourceTree = "<group>"; };
		22F6ED232C838A33004A27CC /* CycleReportHeaderView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CycleReportHeaderView.swift; sourceTree = "<group>"; };
		22F6ED252C83912D004A27CC /* CycleReportTableViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CycleReportTableViewCell.swift; sourceTree = "<group>"; };
		22F6ED272C875681004A27CC /* ConfirmResultPopupViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ConfirmResultPopupViewController.swift; sourceTree = "<group>"; };
		22F6ED292C89FFCD004A27CC /* DeleteResultPopupViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DeleteResultPopupViewController.swift; sourceTree = "<group>"; };
		2D4C75F62CDDEA340030B544 /* SQAutoScrollView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SQAutoScrollView.swift; sourceTree = "<group>"; };
		2D4C75F72CDDEA340030B544 /* SQDotView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SQDotView.swift; sourceTree = "<group>"; };
		2D4C75F82CDDEA340030B544 /* SQPageControl.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SQPageControl.swift; sourceTree = "<group>"; };
		2D4C75FD2CDDEAE50030B544 /* MBProgressHUD.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MBProgressHUD.m; sourceTree = "<group>"; };
		2D4C75FE2CDDEAE50030B544 /* MBProgressHUD.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MBProgressHUD.h; sourceTree = "<group>"; };
		4B1E1BB52C6E53B0001FE006 /* UserTestViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserTestViewModel.swift; sourceTree = "<group>"; };
		4B1E1BB72C6E6054001FE006 /* AutoDismissToastView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AutoDismissToastView.swift; sourceTree = "<group>"; };
		4B3CFF422C5773600032F90F /* Documents.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Documents.swift; sourceTree = "<group>"; };
		4B3CFF442C577CAC0032F90F /* UploadFileViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UploadFileViewModel.swift; sourceTree = "<group>"; };
		4BF80DA92C64AF580039F791 /* GoogleService-Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = "GoogleService-Info.plist"; sourceTree = "<group>"; };
		56B80A67C5911CD03EF0D6D5 /* Pods_HormoneLife.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_HormoneLife.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		800E7A482C8B52E400AA9397 /* UserNoteCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserNoteCell.swift; sourceTree = "<group>"; };
		800E7A4A2C8B598300AA9397 /* HomSingleImageCollectionViewCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = HomSingleImageCollectionViewCell.swift; sourceTree = "<group>"; };
		800E7A4E2C8B5E1500AA9397 /* JXPhotoBrowserPageIndicator.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = JXPhotoBrowserPageIndicator.swift; sourceTree = "<group>"; };
		800E7A4F2C8B5E1500AA9397 /* JXPhotoBrowserNoneAnimator.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = JXPhotoBrowserNoneAnimator.swift; sourceTree = "<group>"; };
		800E7A502C8B5E1500AA9397 /* JXPhotoBrowserZoomSupportedCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = JXPhotoBrowserZoomSupportedCell.swift; sourceTree = "<group>"; };
		800E7A512C8B5E1500AA9397 /* JXPhotoBrowserSmoothZoomAnimator.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = JXPhotoBrowserSmoothZoomAnimator.swift; sourceTree = "<group>"; };
		800E7A522C8B5E1500AA9397 /* JXPhotoBrowserAnimatedTransitioning.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = JXPhotoBrowserAnimatedTransitioning.swift; sourceTree = "<group>"; };
		800E7A532C8B5E1500AA9397 /* JXPhotoBrowserCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = JXPhotoBrowserCell.swift; sourceTree = "<group>"; };
		800E7A542C8B5E1500AA9397 /* JXPhotoBrowserNumberPageIndicator.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = JXPhotoBrowserNumberPageIndicator.swift; sourceTree = "<group>"; };
		800E7A552C8B5E1500AA9397 /* JXPhotoBrowserZoomAnimator.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = JXPhotoBrowserZoomAnimator.swift; sourceTree = "<group>"; };
		800E7A562C8B5E1500AA9397 /* JXPhotoBrowserView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = JXPhotoBrowserView.swift; sourceTree = "<group>"; };
		800E7A572C8B5E1500AA9397 /* JXPhotoBrowserDefaultPageIndicator.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = JXPhotoBrowserDefaultPageIndicator.swift; sourceTree = "<group>"; };
		800E7A582C8B5E1500AA9397 /* JXPhotoBrowserImageCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = JXPhotoBrowserImageCell.swift; sourceTree = "<group>"; };
		800E7A592C8B5E1500AA9397 /* JXPhotoBrowserFadeAnimator.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = JXPhotoBrowserFadeAnimator.swift; sourceTree = "<group>"; };
		800E7A5A2C8B5E1500AA9397 /* JXPhotoBrowserLog.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = JXPhotoBrowserLog.swift; sourceTree = "<group>"; };
		800E7A5B2C8B5E1500AA9397 /* JXPhotoBrowser.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = JXPhotoBrowser.swift; sourceTree = "<group>"; };
		800E7A6A2C8BFD3F00AA9397 /* NoteDetailViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NoteDetailViewController.swift; sourceTree = "<group>"; };
		800E7A6C2C8BFE0D00AA9397 /* NoteDetailViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NoteDetailViewModel.swift; sourceTree = "<group>"; };
		800E7A712C8C1FAD00AA9397 /* FeedbackAddViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FeedbackAddViewModel.swift; sourceTree = "<group>"; };
		800E7A752C8C40E300AA9397 /* SupportOperViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SupportOperViewModel.swift; sourceTree = "<group>"; };
		800F3F5B2C8C5685000104FF /* SupportOperListViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SupportOperListViewModel.swift; sourceTree = "<group>"; };
		800F3F5D2C8C7AF8000104FF /* SuportWebViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SuportWebViewController.swift; sourceTree = "<group>"; };
		800F3F5F2C8C8021000104FF /* AboutUsViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AboutUsViewController.swift; sourceTree = "<group>"; };
		800F3F612C8C823A000104FF /* CommonViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CommonViewModel.swift; sourceTree = "<group>"; };
		8055D27B2CC25CED001D8EBE /* CycleReportCaptureView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CycleReportCaptureView.swift; sourceTree = "<group>"; };
		8078B69E2C9C6357002D7079 /* AuthenticationServices.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AuthenticationServices.framework; path = System/Library/Frameworks/AuthenticationServices.framework; sourceTree = SDKROOT; };
		8078B6A02C9C644D002D7079 /* HormoneLife.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = HormoneLife.entitlements; sourceTree = "<group>"; };
		8078B6A12C9C7C6D002D7079 /* ThirdLoginViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ThirdLoginViewModel.swift; sourceTree = "<group>"; };
		80792F452CB42F6300A8770A /* AddImageCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AddImageCell.swift; sourceTree = "<group>"; };
		80792F502CB65F4800A8770A /* RemindViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RemindViewModel.swift; sourceTree = "<group>"; };
		80792F522CB6659300A8770A /* ReminderMessageCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ReminderMessageCell.swift; sourceTree = "<group>"; };
		807EDDE62CAFE10F003706CF /* BindAccountViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BindAccountViewModel.swift; sourceTree = "<group>"; };
		808571A22C883B4500F1BB9E /* WFNetwokrManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WFNetwokrManager.swift; sourceTree = "<group>"; };
		808571A42C894D6500F1BB9E /* BaseViewModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = BaseViewModel.swift; sourceTree = "<group>"; };
		808571AA2C89506700F1BB9E /* NotePageViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NotePageViewModel.swift; sourceTree = "<group>"; };
		808571AC2C8952E700F1BB9E /* AddNoteViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AddNoteViewModel.swift; sourceTree = "<group>"; };
		808571AE2C89923000F1BB9E /* SystemTagViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SystemTagViewModel.swift; sourceTree = "<group>"; };
		808571B22C89AC4200F1BB9E /* BaseNavigationController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = BaseNavigationController.swift; sourceTree = "<group>"; };
		80CA05D02C8B4CF900CCBC47 /* BaseTableViewCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = BaseTableViewCell.swift; sourceTree = "<group>"; };
		80CA05DE2C8B4DF000CCBC47 /* HormoneLife-Bridging-Header.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "HormoneLife-Bridging-Header.h"; sourceTree = "<group>"; };
		80CA05E32C8B4DFD00CCBC47 /* RectangleLayout.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = RectangleLayout.m; sourceTree = "<group>"; };
		80CA05E42C8B4DFD00CCBC47 /* HorizontalLayout.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HorizontalLayout.h; sourceTree = "<group>"; };
		80CA05E52C8B4DFD00CCBC47 /* WaterFallFlowLayout.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = WaterFallFlowLayout.swift; sourceTree = "<group>"; };
		80CA05E62C8B4DFD00CCBC47 /* RectangleLayout.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RectangleLayout.h; sourceTree = "<group>"; };
		80CA05E72C8B4DFD00CCBC47 /* HorizontalLayout.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HorizontalLayout.m; sourceTree = "<group>"; };
		80E3481B2C9A61E2000996C5 /* MessageViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MessageViewModel.swift; sourceTree = "<group>"; };
		80FC97702CBFCEA1006BA376 /* LuanchAdView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LuanchAdView.swift; sourceTree = "<group>"; };
		80FC97722CBFE788006BA376 /* CustomHudView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CustomHudView.swift; sourceTree = "<group>"; };
		80FC97742CBFF490006BA376 /* HistoryCaptureView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HistoryCaptureView.swift; sourceTree = "<group>"; };
		80FC97AD2CC00815006BA376 /* LBXScanNative.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = LBXScanNative.h; sourceTree = "<group>"; };
		80FC97AE2CC00815006BA376 /* LBXScanNative.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = LBXScanNative.m; sourceTree = "<group>"; };
		8F4A46CEA63C16C345C6D1C2 /* Pods-HormoneLife.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-HormoneLife.debug.xcconfig"; path = "Target Support Files/Pods-HormoneLife/Pods-HormoneLife.debug.xcconfig"; sourceTree = "<group>"; };
		BD4CA4AF2E30D56500E50E79 /* AIChatVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AIChatVC.swift; sourceTree = "<group>"; };
		BD74B3BD2E1907940094929E /* ContactInfoTableViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContactInfoTableViewCell.swift; sourceTree = "<group>"; };
		************************ /* VerificationCodeViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VerificationCodeViewController.swift; sourceTree = "<group>"; };
		BDC213882E1E208C00F4128E /* CaptureStateManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CaptureStateManager.swift; sourceTree = "<group>"; };
		BDF5E8CC2E165A7D0049236F /* AIWebVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AIWebVC.swift; sourceTree = "<group>"; };
		E8159D3155618FEA86255DA6 /* Pods-HormoneLife.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-HormoneLife.release.xcconfig"; path = "Target Support Files/Pods-HormoneLife/Pods-HormoneLife.release.xcconfig"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		2260902C2BEF93440053F10E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				8078B69F2C9C6357002D7079 /* AuthenticationServices.framework in Frameworks */,
				94CCB296A71664ACBF6DC902 /* Pods_HormoneLife.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		189E91C82C1A7F25007D04CA /* UserTests */ = {
			isa = PBXGroup;
			children = (
				4B1E1BB42C6E5384001FE006 /* ViewModels */,
				18E5F8B82C1C17EE002CD571 /* Photos */,
				189E91C92C1A7F6A007D04CA /* RecordTestsViewController.swift */,
				189E91CA2C1A7F6A007D04CA /* RecordTestsViewController.xib */,
				189A14C92C1AD70F00394477 /* ScanView.swift */,
			);
			path = UserTests;
			sourceTree = "<group>";
		};
		18E5F8B82C1C17EE002CD571 /* Photos */ = {
			isa = PBXGroup;
			children = (
				BDC213882E1E208C00F4128E /* CaptureStateManager.swift */,
				18A1F5252C1C3FFE003BE30C /* CustomCameraViewController.swift */,
				18A1F5212C1C2324003BE30C /* ImageCropperViewController.swift */,
				18E5F8BB2C1C187C002CD571 /* FullScreenCameraViewController.swift */,
				18E5F8BA2C1C187C002CD571 /* OverlayView.swift */,
				18E5F8B92C1C187C002CD571 /* OverlayView.xib */,
				225D725F2C32F6AA005DAAAC /* TestStripsView.swift */,
				18A1F5232C1C27B1003BE30C /* TBImageCropperHelper.swift */,
				22F6ED272C875681004A27CC /* ConfirmResultPopupViewController.swift */,
				221D64992D26FDAF000E7A08 /* ConfirmErrorPopupViewController.swift */,
			);
			path = Photos;
			sourceTree = "<group>";
		};
		1B992BCF2C4F031B002FCF2D /* ViewController */ = {
			isa = PBXGroup;
			children = (
				1B992BD02C4F0333002FCF2D /* HistoryRecordViewController.swift */,
				22F6ED292C89FFCD004A27CC /* DeleteResultPopupViewController.swift */,
				220AA8C92CC8014200BEEAB3 /* EmptyTableViewCell.swift */,
				1B97A3F82C4F0BCC00FA5BF0 /* HistoryRecordCell.swift */,
				1B97A3F92C4F0BCC00FA5BF0 /* HistoryRecordCell.xib */,
				1B1D76712C4FF24600134308 /* HistoryRecordBottomView.swift */,
				1B1D76732C50024400134308 /* EditTestResultController.swift */,
				1B1D76772C500A3F00134308 /* TestTimeView.swift */,
				1B1D76752C50083900134308 /* EditTestResultBottomView.swift */,
				1B381CBD2C5CF7520071D14F /* HistoryRecordFooterView.swift */,
				22B39A0D2CBF086100E98FEC /* LineChartDownloadViewController.swift */,
				80FC97742CBFF490006BA376 /* HistoryCaptureView.swift */,
			);
			path = ViewController;
			sourceTree = "<group>";
		};
		1BE0234A2C17349E00957955 /* Charts */ = {
			isa = PBXGroup;
			children = (
				1B992BCF2C4F031B002FCF2D /* ViewController */,
				22A78D2C2C56469300C3A271 /* CircleDemoView.swift */,
				225880392C579C5700DCEFC1 /* TandCResultView.swift */,
				225922B22C57E30E0053FDC3 /* EditTestTimeView.swift */,
				1B1CB7E82C312E0C00E99055 /* BalloonMarker.swift */,
				1BE0234B2C1734ED00957955 /* ChartsViewController.swift */,
				1BE0234D2C17366E00957955 /* ChartsTitleView.swift */,
				1B49621A2C283B7B00B67BAF /* ChartsHeaderView.swift */,
				1B49621E2C308A0900B67BAF /* LineChartFilledView.swift */,
				1B49621C2C3079C000B67BAF /* ChartsCardView.swift */,
				22F6ED192C7CEAF2004A27CC /* LineChartDemoView.swift */,
				22F6ED1D2C8209B9004A27CC /* ChartDetailColorLabelView.swift */,
				22F6ED1F2C822553004A27CC /* ChartDetailView.swift */,
			);
			path = Charts;
			sourceTree = "<group>";
		};
		2212D1D62C17407800A6680E /* NetworkHelp */ = {
			isa = PBXGroup;
			children = (
				2212D1D82C17421200A6680E /* Interactor.swift */,
				808571A22C883B4500F1BB9E /* WFNetwokrManager.swift */,
				2212D1D72C17421100A6680E /* NetWorkHandle.swift */,
				2212D1DD2C17451E00A6680E /* Common.swift */,
				2212D1DF2C17493400A6680E /* Model.swift */,
				2212D1E12C17536400A6680E /* UserDefaultTool.swift */,
				2212D1E32C1753A700A6680E /* Interface.swift */,
				22105BED2CBEDB9600F5785C /* HomeDataSingleton.swift */,
			);
			path = NetworkHelp;
			sourceTree = "<group>";
		};
		223776552C14CF4700A2575F /* Files */ = {
			isa = PBXGroup;
			children = (
				4BF80DA92C64AF580039F791 /* GoogleService-Info.plist */,
				223776562C14CF8800A2575F /* carouselSampleImage.png */,
			);
			path = Files;
			sourceTree = "<group>";
		};
		226090262BEF93440053F10E = {
			isa = PBXGroup;
			children = (
				226090312BEF93440053F10E /* HormoneLife */,
				226090302BEF93440053F10E /* Products */,
				AD2ADCCF06C1DC532A1BB6E5 /* Pods */,
				DE971C70F5A8151B0711E30A /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		226090302BEF93440053F10E /* Products */ = {
			isa = PBXGroup;
			children = (
				2260902F2BEF93440053F10E /* HormoneLife.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		226090312BEF93440053F10E /* HormoneLife */ = {
			isa = PBXGroup;
			children = (
				8078B6A02C9C644D002D7079 /* HormoneLife.entitlements */,
				189E91C82C1A7F25007D04CA /* UserTests */,
				2212D1D62C17407800A6680E /* NetworkHelp */,
				22D11B762C0E0489001AE12D /* Resource */,
				2260904C2BEFC13F0053F10E /* common */,
				226090492BEF98630053F10E /* Profile */,
				226090482BEF97CF0053F10E /* TabBarVCs */,
				226090462BEF96890053F10E /* LoginAndSignUp */,
				1BE0234A2C17349E00957955 /* Charts */,
				226090472BEF97540053F10E /* BasicInfo */,
				226090322BEF93440053F10E /* AppDelegate.swift */,
				BDF5E8CC2E165A7D0049236F /* AIWebVC.swift */,
				BD4CA4AF2E30D56500E50E79 /* AIChatVC.swift */,
				800E7A4C2C8B5E0400AA9397 /* ThirdVenders */,
				226090362BEF93440053F10E /* ViewController.swift */,
				226090382BEF93440053F10E /* Main.storyboard */,
				2260903B2BEF93460053F10E /* Assets.xcassets */,
				2260903D2BEF93460053F10E /* LaunchScreen.storyboard */,
				226090402BEF93460053F10E /* Info.plist */,
			);
			path = HormoneLife;
			sourceTree = "<group>";
		};
		226090462BEF96890053F10E /* LoginAndSignUp */ = {
			isa = PBXGroup;
			children = (
				807EDDE22CAFE0E8003706CF /* BindAccount */,
				224CA4422C01E81A00FAD0CC /* LoginViewController.swift */,
				224CA4432C01E81A00FAD0CC /* LoginViewController.xib */,
				223065612C02E2D900638708 /* SignUpViewController.swift */,
				223065622C02E2D900638708 /* SignUpViewController.xib */,
				2252D6822C037E3F00E3672F /* ForgotPassWordViewController.swift */,
				2252D6832C037E3F00E3672F /* ForgotPassWordViewController.xib */,
				2252D67E2C037DAA00E3672F /* WebViewController.swift */,
				2252D67F2C037DAA00E3672F /* WebViewController.xib */,
				2252D6862C0384E200E3672F /* CreateAccountPopupViewController.swift */,
				2252D6872C0384E200E3672F /* CreateAccountPopupViewController.xib */,
				22B778202C8F3B610004F8A3 /* TandCPopupViewController.swift */,
				22B778212C8F3B610004F8A3 /* TandCPopupViewController.xib */,
				8078B6A12C9C7C6D002D7079 /* ThirdLoginViewModel.swift */,
				80FC97702CBFCEA1006BA376 /* LuanchAdView.swift */,
				************************ /* VerificationCodeViewController.swift */,
			);
			path = LoginAndSignUp;
			sourceTree = "<group>";
		};
		226090472BEF97540053F10E /* BasicInfo */ = {
			isa = PBXGroup;
			children = (
				808571A62C89500C00F1BB9E /* Notes */,
				808571A82C89501D00F1BB9E /* ViewModels */,
				2291C56A2C063F46005CC00F /* GoalSettingViewController.swift */,
				2291C56B2C063F46005CC00F /* GoalSettingViewController.xib */,
				22680D8E2C0A215E00B5DCF2 /* BirthdaySettingViewController.swift */,
				22680D8F2C0A215E00B5DCF2 /* BirthdaySettingViewController.xib */,
				22680D922C0A2DC700B5DCF2 /* BoundViewController.swift */,
				22680D932C0A2DC700B5DCF2 /* BoundViewController.xib */,
			);
			path = BasicInfo;
			sourceTree = "<group>";
		};
		226090482BEF97CF0053F10E /* TabBarVCs */ = {
			isa = PBXGroup;
			children = (
				223776482C143C0D00A2575F /* RootViewController.swift */,
				226090602BEFC40F0053F10E /* TabBarController.swift */,
				22A78D302C56476F00C3A271 /* ResultViewController.swift */,
				22B639FF2C248FF600C58D7B /* CalendarTableViewController.swift */,
				22B63A012C2497B800C58D7B /* CalendarLabelCellTableViewCell.swift */,
				22B63A032C2657F800C58D7B /* CalendarVCSectionCell.swift */,
				22B63A0B2C267B1D00C58D7B /* CalendarPopupSelectionViewController.swift */,
				22680D962C0C1F9700B5DCF2 /* HomeViewController.swift */,
				22680D972C0C1F9700B5DCF2 /* HomeViewController.xib */,
				223776522C14CA5A00A2575F /* HomeCarouselView.swift */,
				2201CD572C5CD18E004E1BE2 /* HomeCameraPopupView.swift */,
				22F6ED172C7A1D42004A27CC /* SampleChartViewController.swift */,
				22F6ED1B2C8204D1004A27CC /* ChartDetailViewController.swift */,
				222CF7CE2C9567B4005F389D /* ChangeConceptionViewController.swift */,
				22E327C82CB19EBD001DDC04 /* Views */,
			);
			path = TabBarVCs;
			sourceTree = "<group>";
		};
		226090492BEF98630053F10E /* Profile */ = {
			isa = PBXGroup;
			children = (
				800E7A6F2C8C1F2800AA9397 /* ViewControllers */,
				800E7A702C8C1F9A00AA9397 /* ViewModels */,
				800E7A6E2C8C1F1C00AA9397 /* Views */,
			);
			path = Profile;
			sourceTree = "<group>";
		};
		2260904C2BEFC13F0053F10E /* common */ = {
			isa = PBXGroup;
			children = (
				80CA05E22C8B4DFD00CCBC47 /* Layouts */,
				80CA05D02C8B4CF900CCBC47 /* BaseTableViewCell.swift */,
				808571B22C89AC4200F1BB9E /* BaseNavigationController.swift */,
				808571A42C894D6500F1BB9E /* BaseViewModel.swift */,
				800F3F612C8C823A000104FF /* CommonViewModel.swift */,
				4B1E1BB72C6E6054001FE006 /* AutoDismissToastView.swift */,
				226D29942C21E7A40058E91E /* Calender */,
				2260904F2BEFC20A0053F10E /* Extensions */,
				2260904D2BEFC1610053F10E /* BaseViewController.swift */,
				2260905E2BEFC3000053F10E /* LanguageManager.swift */,
				18E5F8BF2C1C19B8002CD571 /* NibLoadable.swift */,
				2286B0962C1ED73A00C59403 /* LeftAlignedCollectionViewFlowLayout.swift */,
				226D29A32C21EF810058E91E /* PickPhotoViewController.swift */,
				22B63A272C29C08600C58D7B /* PickerView.swift */,
				4B3CFF442C577CAC0032F90F /* UploadFileViewModel.swift */,
				80CA05DE2C8B4DF000CCBC47 /* HormoneLife-Bridging-Header.h */,
			);
			path = common;
			sourceTree = "<group>";
		};
		2260904F2BEFC20A0053F10E /* Extensions */ = {
			isa = PBXGroup;
			children = (
				226D29A12C21EF690058E91E /* ExtensionFile.swift */,
				226090542BEFC2260053F10E /* Day+Extension.swift */,
				226090552BEFC2260053F10E /* String+Extension.swift */,
				226090562BEFC2260053F10E /* UIColor+Extension.swift */,
				226090512BEFC2260053F10E /* UILabel+Extension.swift */,
				226090522BEFC2260053F10E /* UIView+Extension.swift */,
				226090502BEFC2260053F10E /* UIViewController+Modal.swift */,
				2237764E2C14B17000A2575F /* UIFont+Extension.swift */,
				223776502C14C2FE00A2575F /* UIApplecation+Extension.swift */,
				2286B0902C1CA6E700C59403 /* UITextField+Extension.swift */,
			);
			path = Extensions;
			sourceTree = "<group>";
		};
		226D29942C21E7A40058E91E /* Calender */ = {
			isa = PBXGroup;
			children = (
				226D29952C21E7A40058E91E /* AppCalendarCell.swift */,
				226D29962C21E7A40058E91E /* AppCalendarView.swift */,
				226D29972C21E7A40058E91E /* AppCalendarDayTypeCell.swift */,
				226D29982C21E7A40058E91E /* AppCalendarViewDelegate.swift */,
				226D29992C21E7A40058E91E /* AppCalendarConfig.swift */,
				226D299F2C21EAD80058E91E /* CalendarPopupViewController.swift */,
			);
			path = Calender;
			sourceTree = "<group>";
		};
		22D11B762C0E0489001AE12D /* Resource */ = {
			isa = PBXGroup;
			children = (
				223776552C14CF4700A2575F /* Files */,
				22D11B772C0E0498001AE12D /* Fonts */,
				4B3CFF422C5773600032F90F /* Documents.swift */,
			);
			path = Resource;
			sourceTree = "<group>";
		};
		22D11B772C0E0498001AE12D /* Fonts */ = {
			isa = PBXGroup;
			children = (
				22D11B892C0E04C8001AE12D /* gilroy black.otf */,
				22D11B832C0E04C7001AE12D /* gilroy blackItalic.otf */,
				22D11B812C0E04C7001AE12D /* gilroy bold.otf */,
				22D11B7C2C0E04C6001AE12D /* Gilroy BoldItalic.otf */,
				22D11B782C0E04C5001AE12D /* Gilroy ExtraBold.otf */,
				22D11B792C0E04C5001AE12D /* Gilroy ExtraBoldItalic.otf */,
				22D11B7E2C0E04C6001AE12D /* Gilroy Heavy.otf */,
				22D11B7F2C0E04C6001AE12D /* Gilroy HeavyItalic.otf */,
				22D11B842C0E04C7001AE12D /* Gilroy Light.otf */,
				22D11B7D2C0E04C6001AE12D /* Gilroy LightItalic.otf */,
				22D11B872C0E04C8001AE12D /* Gilroy Medium.otf */,
				22D11B822C0E04C7001AE12D /* Gilroy MediumItalic.otf */,
				22D11B852C0E04C7001AE12D /* gilroy regular.otf */,
				22D11B7A2C0E04C5001AE12D /* Gilroy RegularItalic.otf */,
				22D11B8B2C0E04C8001AE12D /* Gilroy SemiBold.otf */,
				22D11B802C0E04C6001AE12D /* Gilroy SemiBoldItalic.otf */,
				22D11B862C0E04C7001AE12D /* Gilroy Thin.otf */,
				22D11B8A2C0E04C8001AE12D /* Gilroy ThinItalic.otf */,
				22D11B7B2C0E04C5001AE12D /* gilroy UltraLight.otf */,
				22D11B882C0E04C8001AE12D /* gilroy UltraLightitalic.otf */,
			);
			path = Fonts;
			sourceTree = "<group>";
		};
		22E327C82CB19EBD001DDC04 /* Views */ = {
			isa = PBXGroup;
			children = (
				22E327C92CB19F1B001DDC04 /* LineChartViewTableViewCell.swift */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		2D4C75F52CDDEA340030B544 /* SQAutoScrollView */ = {
			isa = PBXGroup;
			children = (
				2D4C75F62CDDEA340030B544 /* SQAutoScrollView.swift */,
				2D4C75F72CDDEA340030B544 /* SQDotView.swift */,
				2D4C75F82CDDEA340030B544 /* SQPageControl.swift */,
			);
			path = SQAutoScrollView;
			sourceTree = "<group>";
		};
		2D4C75FC2CDDEAE50030B544 /* MBProgressHUD */ = {
			isa = PBXGroup;
			children = (
				2D4C75FD2CDDEAE50030B544 /* MBProgressHUD.m */,
				2D4C75FE2CDDEAE50030B544 /* MBProgressHUD.h */,
			);
			path = MBProgressHUD;
			sourceTree = "<group>";
		};
		4B1E1BB42C6E5384001FE006 /* ViewModels */ = {
			isa = PBXGroup;
			children = (
				4B1E1BB52C6E53B0001FE006 /* UserTestViewModel.swift */,
			);
			path = ViewModels;
			sourceTree = "<group>";
		};
		800E7A4C2C8B5E0400AA9397 /* ThirdVenders */ = {
			isa = PBXGroup;
			children = (
				2D4C75FC2CDDEAE50030B544 /* MBProgressHUD */,
				2D4C75F52CDDEA340030B544 /* SQAutoScrollView */,
				80FC97AC2CC00815006BA376 /* LBXNative */,
				800E7A4D2C8B5E1500AA9397 /* JXPhotoBrowser */,
			);
			path = ThirdVenders;
			sourceTree = "<group>";
		};
		800E7A4D2C8B5E1500AA9397 /* JXPhotoBrowser */ = {
			isa = PBXGroup;
			children = (
				800E7A4E2C8B5E1500AA9397 /* JXPhotoBrowserPageIndicator.swift */,
				800E7A4F2C8B5E1500AA9397 /* JXPhotoBrowserNoneAnimator.swift */,
				800E7A502C8B5E1500AA9397 /* JXPhotoBrowserZoomSupportedCell.swift */,
				800E7A512C8B5E1500AA9397 /* JXPhotoBrowserSmoothZoomAnimator.swift */,
				800E7A522C8B5E1500AA9397 /* JXPhotoBrowserAnimatedTransitioning.swift */,
				800E7A532C8B5E1500AA9397 /* JXPhotoBrowserCell.swift */,
				800E7A542C8B5E1500AA9397 /* JXPhotoBrowserNumberPageIndicator.swift */,
				800E7A552C8B5E1500AA9397 /* JXPhotoBrowserZoomAnimator.swift */,
				800E7A562C8B5E1500AA9397 /* JXPhotoBrowserView.swift */,
				800E7A572C8B5E1500AA9397 /* JXPhotoBrowserDefaultPageIndicator.swift */,
				800E7A582C8B5E1500AA9397 /* JXPhotoBrowserImageCell.swift */,
				800E7A592C8B5E1500AA9397 /* JXPhotoBrowserFadeAnimator.swift */,
				800E7A5A2C8B5E1500AA9397 /* JXPhotoBrowserLog.swift */,
				800E7A5B2C8B5E1500AA9397 /* JXPhotoBrowser.swift */,
			);
			path = JXPhotoBrowser;
			sourceTree = "<group>";
		};
		800E7A6E2C8C1F1C00AA9397 /* Views */ = {
			isa = PBXGroup;
			children = (
				BD74B3BD2E1907940094929E /* ContactInfoTableViewCell.swift */,
				2201CD5F2C607263004E1BE2 /* FeedbackTableViewCell.swift */,
				22B63A0F2C27E76900C58D7B /* SupportVCCustomerSupportCellTableViewCell.swift */,
				22B63A102C27E76900C58D7B /* SupportVCCustomerSupportCellTableViewCell.xib */,
				22B63A132C27E85300C58D7B /* SupportVCDontFindAnswerCell.swift */,
				22B63A142C27E85300C58D7B /* SupportVCDontFindAnswerCell.xib */,
				22B63A172C27E92C00C58D7B /* SupportVCQuestionCell.swift */,
				22B63A182C27E92C00C58D7B /* SupportVCQuestionCell.xib */,
				2201CD592C5FBCDA004E1BE2 /* TagsTableViewCell.swift */,
				2286B0922C1ED05B00C59403 /* TagsCollectionViewCell.swift */,
				2286B0932C1ED05B00C59403 /* TagsCollectionViewCell.xib */,
				226D298C2C1EFCB40058E91E /* MessageTableViewCell.swift */,
				226D298D2C1EFCB40058E91E /* MessageTableViewCell.xib */,
				22DF42F32C5A7D1800D9DEDE /* NoticeTableViewCell.swift */,
				22DF42F22C5A7D1800D9DEDE /* NoticeTableViewCell.xib */,
				22B63A1B2C28173E00C58D7B /* TimePickerView.swift */,
				22B63A212C2851DD00C58D7B /* EditTestDateVCTestTimeCellTableViewCell.swift */,
				22B63A232C28591200C58D7B /* EditTestDateVCCalendarCell.swift */,
				22B63A252C285C5000C58D7B /* EditTestDateCycleCell.swift */,
				22F6ED232C838A33004A27CC /* CycleReportHeaderView.swift */,
				22F6ED252C83912D004A27CC /* CycleReportTableViewCell.swift */,
				80792F452CB42F6300A8770A /* AddImageCell.swift */,
				80792F522CB6659300A8770A /* ReminderMessageCell.swift */,
				80FC97722CBFE788006BA376 /* CustomHudView.swift */,
				8055D27B2CC25CED001D8EBE /* CycleReportCaptureView.swift */,
				22EA43C22CD773E4008026F6 /* AttarchmentDownloadButtonView.swift */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		800E7A6F2C8C1F2800AA9397 /* ViewControllers */ = {
			isa = PBXGroup;
			children = (
				223776602C15B26800A2575F /* AboutHormonelifeViewController.swift */,
				223776612C15B26800A2575F /* AboutHormonelifeViewController.xib */,
				223776642C15B71D00A2575F /* SettingViewController.swift */,
				223776652C15B71D00A2575F /* SettingViewController.xib */,
				220AA8C62CC7F88300BEEAB3 /* UnbingPopupViewController.swift */,
				220AA8C52CC7F88300BEEAB3 /* UnbingPopupViewController.xib */,
				223776682C16003200A2575F /* ChangePasswordViewController.swift */,
				223776692C16003200A2575F /* ChangePasswordViewController.xib */,
				2272A7D12C160B0400FCB0F5 /* DeleteAccountViewController.swift */,
				2272A7D22C160B0500FCB0F5 /* DeleteAccountViewController.xib */,
				22A78B442C168854006C8B33 /* PersonalInfoViewController.swift */,
				22A78B452C168855006C8B33 /* PersonalInfoViewController.xib */,
				22A78B482C1691F6006C8B33 /* TagsViewController.swift */,
				22A78B492C1691F6006C8B33 /* TagsViewController.xib */,
				2237764A2C14768900A2575F /* ProfileViewController.swift */,
				2237764B2C14768900A2575F /* ProfileViewController.xib */,
				223776582C155B8100A2575F /* SupportAndHelpHomeViewController.swift */,
				223776592C155B8100A2575F /* SupportAndHelpHomeViewController.xib */,
				2201CD5D2C607026004E1BE2 /* SupportAndHelpFeedbackTableViewController.swift */,
				800F3F5D2C8C7AF8000104FF /* SuportWebViewController.swift */,
				2201CD5B2C5FC101004E1BE2 /* TagsTableViewController.swift */,
				22A78B4D2C1694DE006C8B33 /* AddTagViewController.swift */,
				22A78B4C2C1694DD006C8B33 /* AddTagViewController.xib */,
				225922B42C5948DE0053FDC3 /* NotificationsViewController.swift */,
				225922B52C5948DF0053FDC3 /* NotificationsViewController.xib */,
				22A78B502C169BD1006C8B33 /* MessagesViewController.swift */,
				22A78B512C169BD1006C8B33 /* MessagesViewController.xib */,
				22DF42F72C5A83B800D9DEDE /* ReminderViewController.swift */,
				22DF42F62C5A83B700D9DEDE /* ReminderViewController.xib */,
				22A78B552C16A03A006C8B33 /* MessageSettingViewController.swift */,
				22A78B542C16A03A006C8B33 /* MessageSettingViewController.xib */,
				22B63A1D2C284CFC00C58D7B /* EditTestDateAndTimeViewController.swift */,
				22F6ED212C838879004A27CC /* CycleReportTableViewController.swift */,
				800F3F5F2C8C8021000104FF /* AboutUsViewController.swift */,
			);
			path = ViewControllers;
			sourceTree = "<group>";
		};
		800E7A702C8C1F9A00AA9397 /* ViewModels */ = {
			isa = PBXGroup;
			children = (
				800E7A712C8C1FAD00AA9397 /* FeedbackAddViewModel.swift */,
				800E7A752C8C40E300AA9397 /* SupportOperViewModel.swift */,
				800F3F5B2C8C5685000104FF /* SupportOperListViewModel.swift */,
				80E3481B2C9A61E2000996C5 /* MessageViewModel.swift */,
				80792F502CB65F4800A8770A /* RemindViewModel.swift */,
			);
			path = ViewModels;
			sourceTree = "<group>";
		};
		807EDDE22CAFE0E8003706CF /* BindAccount */ = {
			isa = PBXGroup;
			children = (
				807EDDE62CAFE10F003706CF /* BindAccountViewModel.swift */,
			);
			path = BindAccount;
			sourceTree = "<group>";
		};
		808571A62C89500C00F1BB9E /* Notes */ = {
			isa = PBXGroup;
			children = (
				808571A72C89501400F1BB9E /* ViewControllers */,
				808571A92C89503700F1BB9E /* Views */,
			);
			path = Notes;
			sourceTree = "<group>";
		};
		808571A72C89501400F1BB9E /* ViewControllers */ = {
			isa = PBXGroup;
			children = (
				22B63A0D2C274FDA00C58D7B /* NotesTableTableViewController.swift */,
				800E7A6A2C8BFD3F00AA9397 /* NoteDetailViewController.swift */,
				22D11B722C0CE791001AE12D /* AddNoteViewController.swift */,
				22D11B732C0CE791001AE12D /* AddNoteViewController.xib */,
			);
			path = ViewControllers;
			sourceTree = "<group>";
		};
		808571A82C89501D00F1BB9E /* ViewModels */ = {
			isa = PBXGroup;
			children = (
				808571AA2C89506700F1BB9E /* NotePageViewModel.swift */,
				808571AC2C8952E700F1BB9E /* AddNoteViewModel.swift */,
				808571AE2C89923000F1BB9E /* SystemTagViewModel.swift */,
				800E7A6C2C8BFE0D00AA9397 /* NoteDetailViewModel.swift */,
			);
			path = ViewModels;
			sourceTree = "<group>";
		};
		808571A92C89503700F1BB9E /* Views */ = {
			isa = PBXGroup;
			children = (
				22D11B6E2C0CE2C1001AE12D /* NoteTableViewCell.swift */,
				22D11B6F2C0CE2C1001AE12D /* NoteTableViewCell.xib */,
				800E7A482C8B52E400AA9397 /* UserNoteCell.swift */,
				800E7A4A2C8B598300AA9397 /* HomSingleImageCollectionViewCell.swift */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		80CA05E22C8B4DFD00CCBC47 /* Layouts */ = {
			isa = PBXGroup;
			children = (
				80CA05E32C8B4DFD00CCBC47 /* RectangleLayout.m */,
				80CA05E42C8B4DFD00CCBC47 /* HorizontalLayout.h */,
				80CA05E52C8B4DFD00CCBC47 /* WaterFallFlowLayout.swift */,
				80CA05E62C8B4DFD00CCBC47 /* RectangleLayout.h */,
				80CA05E72C8B4DFD00CCBC47 /* HorizontalLayout.m */,
			);
			path = Layouts;
			sourceTree = "<group>";
		};
		80FC97AC2CC00815006BA376 /* LBXNative */ = {
			isa = PBXGroup;
			children = (
				80FC97AD2CC00815006BA376 /* LBXScanNative.h */,
				80FC97AE2CC00815006BA376 /* LBXScanNative.m */,
			);
			path = LBXNative;
			sourceTree = "<group>";
		};
		AD2ADCCF06C1DC532A1BB6E5 /* Pods */ = {
			isa = PBXGroup;
			children = (
				8F4A46CEA63C16C345C6D1C2 /* Pods-HormoneLife.debug.xcconfig */,
				E8159D3155618FEA86255DA6 /* Pods-HormoneLife.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		DE971C70F5A8151B0711E30A /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				8078B69E2C9C6357002D7079 /* AuthenticationServices.framework */,
				56B80A67C5911CD03EF0D6D5 /* Pods_HormoneLife.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		2260902E2BEF93440053F10E /* HormoneLife */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 226090432BEF93460053F10E /* Build configuration list for PBXNativeTarget "HormoneLife" */;
			buildPhases = (
				2B0F97949D98FCB29B07C752 /* [CP] Check Pods Manifest.lock */,
				2260902B2BEF93440053F10E /* Sources */,
				2260902C2BEF93440053F10E /* Frameworks */,
				2260902D2BEF93440053F10E /* Resources */,
				F68F57BA14813E4E34E05F60 /* [CP] Embed Pods Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = HormoneLife;
			productName = HormoneLife;
			productReference = 2260902F2BEF93440053F10E /* HormoneLife.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		226090272BEF93440053F10E /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1520;
				LastUpgradeCheck = 1520;
				ORGANIZATIONNAME = HormoneLife;
				TargetAttributes = {
					2260902E2BEF93440053F10E = {
						CreatedOnToolsVersion = 15.2;
						LastSwiftMigration = 1520;
					};
				};
			};
			buildConfigurationList = 2260902A2BEF93440053F10E /* Build configuration list for PBXProject "HormoneLife" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 226090262BEF93440053F10E;
			productRefGroup = 226090302BEF93440053F10E /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				2260902E2BEF93440053F10E /* HormoneLife */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		2260902D2BEF93440053F10E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				223065642C02E2D900638708 /* SignUpViewController.xib in Resources */,
				2286B0952C1ED05B00C59403 /* TagsCollectionViewCell.xib in Resources */,
				2252D6892C0384E200E3672F /* CreateAccountPopupViewController.xib in Resources */,
				22D11B922C0E04C8001AE12D /* Gilroy Heavy.otf in Resources */,
				22B778232C8F3B610004F8A3 /* TandCPopupViewController.xib in Resources */,
				22DF42F42C5A7D1900D9DEDE /* NoticeTableViewCell.xib in Resources */,
				22A78B532C169BD2006C8B33 /* MessagesViewController.xib in Resources */,
				22DF42F82C5A83B800D9DEDE /* ReminderViewController.xib in Resources */,
				220AA8C72CC7F88400BEEAB3 /* UnbingPopupViewController.xib in Resources */,
				22D11B8D2C0E04C8001AE12D /* Gilroy ExtraBoldItalic.otf in Resources */,
				22D11B972C0E04C8001AE12D /* gilroy blackItalic.otf in Resources */,
				223776572C14CF8800A2575F /* carouselSampleImage.png in Resources */,
				22D11B9F2C0E04C8001AE12D /* Gilroy SemiBold.otf in Resources */,
				223776632C15B26800A2575F /* AboutHormonelifeViewController.xib in Resources */,
				22D11B992C0E04C8001AE12D /* gilroy regular.otf in Resources */,
				224CA4452C01E81A00FAD0CC /* LoginViewController.xib in Resources */,
				2252D6852C037E3F00E3672F /* ForgotPassWordViewController.xib in Resources */,
				22D11B942C0E04C8001AE12D /* Gilroy SemiBoldItalic.otf in Resources */,
				22D11B9C2C0E04C8001AE12D /* gilroy UltraLightitalic.otf in Resources */,
				22B63A122C27E76900C58D7B /* SupportVCCustomerSupportCellTableViewCell.xib in Resources */,
				2260903F2BEF93460053F10E /* LaunchScreen.storyboard in Resources */,
				2252D6812C037DAA00E3672F /* WebViewController.xib in Resources */,
				2260903C2BEF93460053F10E /* Assets.xcassets in Resources */,
				22D11B9A2C0E04C8001AE12D /* Gilroy Thin.otf in Resources */,
				22D11B982C0E04C8001AE12D /* Gilroy Light.otf in Resources */,
				22A78B472C168855006C8B33 /* PersonalInfoViewController.xib in Resources */,
				4BF80DAA2C64AF580039F791 /* GoogleService-Info.plist in Resources */,
				22D11B902C0E04C8001AE12D /* Gilroy BoldItalic.otf in Resources */,
				22D11B8E2C0E04C8001AE12D /* Gilroy RegularItalic.otf in Resources */,
				2291C56D2C063F46005CC00F /* GoalSettingViewController.xib in Resources */,
				22D11B952C0E04C8001AE12D /* gilroy bold.otf in Resources */,
				22D11B932C0E04C8001AE12D /* Gilroy HeavyItalic.otf in Resources */,
				22D11B752C0CE791001AE12D /* AddNoteViewController.xib in Resources */,
				22D11B9E2C0E04C8001AE12D /* Gilroy ThinItalic.otf in Resources */,
				22D11B962C0E04C8001AE12D /* Gilroy MediumItalic.otf in Resources */,
				22680D952C0A2DC700B5DCF2 /* BoundViewController.xib in Resources */,
				22D11B912C0E04C8001AE12D /* Gilroy LightItalic.otf in Resources */,
				22D11B8F2C0E04C8001AE12D /* gilroy UltraLight.otf in Resources */,
				2237765B2C155B8100A2575F /* SupportAndHelpHomeViewController.xib in Resources */,
				223776672C15B71E00A2575F /* SettingViewController.xib in Resources */,
				22A78B4B2C1691F6006C8B33 /* TagsViewController.xib in Resources */,
				18E5F8BC2C1C187D002CD571 /* OverlayView.xib in Resources */,
				22680D912C0A215E00B5DCF2 /* BirthdaySettingViewController.xib in Resources */,
				22A78B562C16A03B006C8B33 /* MessageSettingViewController.xib in Resources */,
				22B63A162C27E85300C58D7B /* SupportVCDontFindAnswerCell.xib in Resources */,
				22B63A1A2C27E92C00C58D7B /* SupportVCQuestionCell.xib in Resources */,
				2237764D2C14768900A2575F /* ProfileViewController.xib in Resources */,
				22D11B9D2C0E04C8001AE12D /* gilroy black.otf in Resources */,
				22D11B9B2C0E04C8001AE12D /* Gilroy Medium.otf in Resources */,
				22D11B8C2C0E04C8001AE12D /* Gilroy ExtraBold.otf in Resources */,
				2272A7D42C160B0500FCB0F5 /* DeleteAccountViewController.xib in Resources */,
				2237766B2C16003200A2575F /* ChangePasswordViewController.xib in Resources */,
				22A78B4E2C1694DE006C8B33 /* AddTagViewController.xib in Resources */,
				2260903A2BEF93440053F10E /* Main.storyboard in Resources */,
				22D11B712C0CE2C1001AE12D /* NoteTableViewCell.xib in Resources */,
				225922B72C5948DF0053FDC3 /* NotificationsViewController.xib in Resources */,
				226D298F2C1EFCB40058E91E /* MessageTableViewCell.xib in Resources */,
				22680D992C0C1F9700B5DCF2 /* HomeViewController.xib in Resources */,
				1B97A3FB2C4F0BCC00FA5BF0 /* HistoryRecordCell.xib in Resources */,
				189E91CC2C1A7F6A007D04CA /* RecordTestsViewController.xib in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		2B0F97949D98FCB29B07C752 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-HormoneLife-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		F68F57BA14813E4E34E05F60 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-HormoneLife/Pods-HormoneLife-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			inputPaths = (
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-HormoneLife/Pods-HormoneLife-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-HormoneLife/Pods-HormoneLife-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		2260902B2BEF93440053F10E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				800E7A602C8B5E1500AA9397 /* JXPhotoBrowserAnimatedTransitioning.swift in Sources */,
				22B39A0E2CBF086100E98FEC /* LineChartDownloadViewController.swift in Sources */,
				800E7A672C8B5E1500AA9397 /* JXPhotoBrowserFadeAnimator.swift in Sources */,
				1BE0234C2C1734ED00957955 /* ChartsViewController.swift in Sources */,
				22EA43C32CD773E4008026F6 /* AttarchmentDownloadButtonView.swift in Sources */,
				220AA8C82CC7F88400BEEAB3 /* UnbingPopupViewController.swift in Sources */,
				800E7A5E2C8B5E1500AA9397 /* JXPhotoBrowserZoomSupportedCell.swift in Sources */,
				808571A32C883B4500F1BB9E /* WFNetwokrManager.swift in Sources */,
				BD4CA4B02E30D56500E50E79 /* AIChatVC.swift in Sources */,
				22B63A042C2657F800C58D7B /* CalendarVCSectionCell.swift in Sources */,
				2286B0912C1CA6E700C59403 /* UITextField+Extension.swift in Sources */,
				2D4C75FA2CDDEA340030B544 /* SQDotView.swift in Sources */,
				80CA05D12C8B4CF900CCBC47 /* BaseTableViewCell.swift in Sources */,
				4B3CFF452C577CAC0032F90F /* UploadFileViewModel.swift in Sources */,
				2D4C75F92CDDEA340030B544 /* SQAutoScrollView.swift in Sources */,
				226D299A2C21E7A40058E91E /* AppCalendarCell.swift in Sources */,
				2D4C75FF2CDDEAE50030B544 /* MBProgressHUD.m in Sources */,
				80E3481C2C9A61E2000996C5 /* MessageViewModel.swift in Sources */,
				22F6ED282C875681004A27CC /* ConfirmResultPopupViewController.swift in Sources */,
				22D11B742C0CE791001AE12D /* AddNoteViewController.swift in Sources */,
				224CA4442C01E81A00FAD0CC /* LoginViewController.swift in Sources */,
				80FC97732CBFE788006BA376 /* CustomHudView.swift in Sources */,
				22B63A1E2C284CFC00C58D7B /* EditTestDateAndTimeViewController.swift in Sources */,
				226D299D2C21E7A40058E91E /* AppCalendarViewDelegate.swift in Sources */,
				800E7A762C8C40E300AA9397 /* SupportOperViewModel.swift in Sources */,
				221D649A2D26FDAF000E7A08 /* ConfirmErrorPopupViewController.swift in Sources */,
				22105BEE2CBEDB9600F5785C /* HomeDataSingleton.swift in Sources */,
				80CA05E92C8B4DFD00CCBC47 /* WaterFallFlowLayout.swift in Sources */,
				22B63A222C2851DD00C58D7B /* EditTestDateVCTestTimeCellTableViewCell.swift in Sources */,
				226090592BEFC2270053F10E /* UIView+Extension.swift in Sources */,
				22F6ED1A2C7CEAF2004A27CC /* LineChartDemoView.swift in Sources */,
				8055D27C2CC25CED001D8EBE /* CycleReportCaptureView.swift in Sources */,
				800E7A5F2C8B5E1500AA9397 /* JXPhotoBrowserSmoothZoomAnimator.swift in Sources */,
				2260904E2BEFC1610053F10E /* BaseViewController.swift in Sources */,
				2201CD5A2C5FBCDA004E1BE2 /* TagsTableViewCell.swift in Sources */,
				22F6ED1C2C8204D1004A27CC /* ChartDetailViewController.swift in Sources */,
				22B63A1C2C28173E00C58D7B /* TimePickerView.swift in Sources */,
				2212D1E22C17536400A6680E /* UserDefaultTool.swift in Sources */,
				800E7A5D2C8B5E1500AA9397 /* JXPhotoBrowserNoneAnimator.swift in Sources */,
				189A14CA2C1AD70F00394477 /* ScanView.swift in Sources */,
				226D299B2C21E7A40058E91E /* AppCalendarView.swift in Sources */,
				22A78B462C168855006C8B33 /* PersonalInfoViewController.swift in Sources */,
				22F6ED2A2C89FFCD004A27CC /* DeleteResultPopupViewController.swift in Sources */,
				1B381CBE2C5CF7520071D14F /* HistoryRecordFooterView.swift in Sources */,
				807EDDE72CAFE10F003706CF /* BindAccountViewModel.swift in Sources */,
				800E7A612C8B5E1500AA9397 /* JXPhotoBrowserCell.swift in Sources */,
				800E7A6D2C8BFE0D00AA9397 /* NoteDetailViewModel.swift in Sources */,
				80CA05EA2C8B4DFD00CCBC47 /* HorizontalLayout.m in Sources */,
				22A78B4F2C1694DE006C8B33 /* AddTagViewController.swift in Sources */,
				************************ /* VerificationCodeViewController.swift in Sources */,
				800E7A652C8B5E1500AA9397 /* JXPhotoBrowserDefaultPageIndicator.swift in Sources */,
				22B63A152C27E85300C58D7B /* SupportVCDontFindAnswerCell.swift in Sources */,
				226D29A22C21EF690058E91E /* ExtensionFile.swift in Sources */,
				80FC97AF2CC00815006BA376 /* LBXScanNative.m in Sources */,
				808571AB2C89506700F1BB9E /* NotePageViewModel.swift in Sources */,
				226090612BEFC40F0053F10E /* TabBarController.swift in Sources */,
				1B49621F2C308A0A00B67BAF /* LineChartFilledView.swift in Sources */,
				2260905D2BEFC2270053F10E /* UIColor+Extension.swift in Sources */,
				229C762A2C6523E500CFC07F /* EditTestTimeView.swift in Sources */,
				2201CD5C2C5FC101004E1BE2 /* TagsTableViewController.swift in Sources */,
				800E7A692C8B5E1500AA9397 /* JXPhotoBrowser.swift in Sources */,
				18E5F8BE2C1C187D002CD571 /* FullScreenCameraViewController.swift in Sources */,
				22F6ED222C838879004A27CC /* CycleReportTableViewController.swift in Sources */,
				18E5F8BD2C1C187D002CD571 /* OverlayView.swift in Sources */,
				800E7A662C8B5E1500AA9397 /* JXPhotoBrowserImageCell.swift in Sources */,
				2212D1DA2C17421200A6680E /* Interactor.swift in Sources */,
				80792F532CB6659300A8770A /* ReminderMessageCell.swift in Sources */,
				808571AD2C8952E700F1BB9E /* AddNoteViewModel.swift in Sources */,
				2237764C2C14768900A2575F /* ProfileViewController.swift in Sources */,
				800E7A4B2C8B598300AA9397 /* HomSingleImageCollectionViewCell.swift in Sources */,
				223776622C15B26800A2575F /* AboutHormonelifeViewController.swift in Sources */,
				80792F462CB42F6300A8770A /* AddImageCell.swift in Sources */,
				4B1E1BB82C6E6054001FE006 /* AutoDismissToastView.swift in Sources */,
				22B778222C8F3B610004F8A3 /* TandCPopupViewController.swift in Sources */,
				800E7A492C8B52E400AA9397 /* UserNoteCell.swift in Sources */,
				2212D1DE2C17451E00A6680E /* Common.swift in Sources */,
				2201CD602C607263004E1BE2 /* FeedbackTableViewCell.swift in Sources */,
				800F3F5E2C8C7AF8000104FF /* SuportWebViewController.swift in Sources */,
				18A1F5222C1C2324003BE30C /* ImageCropperViewController.swift in Sources */,
				22B63A0E2C274FDA00C58D7B /* NotesTableTableViewController.swift in Sources */,
				2212D1D92C17421200A6680E /* NetWorkHandle.swift in Sources */,
				22D11B702C0CE2C1001AE12D /* NoteTableViewCell.swift in Sources */,
				2237766A2C16003200A2575F /* ChangePasswordViewController.swift in Sources */,
				18A1F5262C1C3FFE003BE30C /* CustomCameraViewController.swift in Sources */,
				22F6ED182C7A1D42004A27CC /* SampleChartViewController.swift in Sources */,
				************************ /* ContactInfoTableViewCell.swift in Sources */,
				800F3F622C8C823A000104FF /* CommonViewModel.swift in Sources */,
				80FC97752CBFF490006BA376 /* HistoryCaptureView.swift in Sources */,
				223776512C14C2FE00A2575F /* UIApplecation+Extension.swift in Sources */,
				1B1CB7E92C312E0D00E99055 /* BalloonMarker.swift in Sources */,
				2252D6882C0384E200E3672F /* CreateAccountPopupViewController.swift in Sources */,
				2286B0942C1ED05B00C59403 /* TagsCollectionViewCell.swift in Sources */,
				808571AF2C89923000F1BB9E /* SystemTagViewModel.swift in Sources */,
				22680D902C0A215E00B5DCF2 /* BirthdaySettingViewController.swift in Sources */,
				2212D1E42C1753A700A6680E /* Interface.swift in Sources */,
				226D29A02C21EAD80058E91E /* CalendarPopupViewController.swift in Sources */,
				226090582BEFC2270053F10E /* UILabel+Extension.swift in Sources */,
				22DF42F92C5A83B800D9DEDE /* ReminderViewController.swift in Sources */,
				22F6ED262C83912D004A27CC /* CycleReportTableViewCell.swift in Sources */,
				22A78D312C56476F00C3A271 /* ResultViewController.swift in Sources */,
				220AA8CA2CC8014200BEEAB3 /* EmptyTableViewCell.swift in Sources */,
				2252D6842C037E3F00E3672F /* ForgotPassWordViewController.swift in Sources */,
				800F3F602C8C8021000104FF /* AboutUsViewController.swift in Sources */,
				22F6ED1E2C8209B9004A27CC /* ChartDetailColorLabelView.swift in Sources */,
				2201CD5E2C607026004E1BE2 /* SupportAndHelpFeedbackTableViewController.swift in Sources */,
				22E327CA2CB19F1B001DDC04 /* LineChartViewTableViewCell.swift in Sources */,
				800E7A5C2C8B5E1500AA9397 /* JXPhotoBrowserPageIndicator.swift in Sources */,
				1BE0234E2C17366E00957955 /* ChartsTitleView.swift in Sources */,
				22A78B572C16A03B006C8B33 /* MessageSettingViewController.swift in Sources */,
				2258803A2C579C5700DCEFC1 /* TandCResultView.swift in Sources */,
				2212D1E02C17493400A6680E /* Model.swift in Sources */,
				18A1F5242C1C27B1003BE30C /* TBImageCropperHelper.swift in Sources */,
				226090572BEFC2260053F10E /* UIViewController+Modal.swift in Sources */,
				2201CD582C5CD18E004E1BE2 /* HomeCameraPopupView.swift in Sources */,
				225D72602C32F6AA005DAAAC /* TestStripsView.swift in Sources */,
				2237765A2C155B8100A2575F /* SupportAndHelpHomeViewController.swift in Sources */,
				18E5F8C02C1C19B8002CD571 /* NibLoadable.swift in Sources */,
				226D29A42C21EF810058E91E /* PickPhotoViewController.swift in Sources */,
				1B992BD12C4F0333002FCF2D /* HistoryRecordViewController.swift in Sources */,
				22B63A192C27E92C00C58D7B /* SupportVCQuestionCell.swift in Sources */,
				8078B6A22C9C7C6D002D7079 /* ThirdLoginViewModel.swift in Sources */,
				226090572BEFC2260053F10E /* UIViewController+Modal.swift in Sources */,
				80FC97712CBFCEA1006BA376 /* LuanchAdView.swift in Sources */,
				800E7A722C8C1FAD00AA9397 /* FeedbackAddViewModel.swift in Sources */,
				2237765A2C155B8100A2575F /* SupportAndHelpHomeViewController.swift in Sources */,
				22B63A262C285C5000C58D7B /* EditTestDateCycleCell.swift in Sources */,
				22F6ED242C838A33004A27CC /* CycleReportHeaderView.swift in Sources */,
				226D298E2C1EFCB40058E91E /* MessageTableViewCell.swift in Sources */,
				800F3F5C2C8C5685000104FF /* SupportOperListViewModel.swift in Sources */,
				223776492C143C0D00A2575F /* RootViewController.swift in Sources */,
				226090372BEF93440053F10E /* ViewController.swift in Sources */,
				800E7A632C8B5E1500AA9397 /* JXPhotoBrowserZoomAnimator.swift in Sources */,
				225922B62C5948DF0053FDC3 /* NotificationsViewController.swift in Sources */,
				22B63A282C29C08600C58D7B /* PickerView.swift in Sources */,
				1B97A3FA2C4F0BCC00FA5BF0 /* HistoryRecordCell.swift in Sources */,
				22B63A022C2497B800C58D7B /* CalendarLabelCellTableViewCell.swift in Sources */,
				2272A7D32C160B0500FCB0F5 /* DeleteAccountViewController.swift in Sources */,
				1B49621D2C3079C000B67BAF /* ChartsCardView.swift in Sources */,
				2D4C75FB2CDDEA340030B544 /* SQPageControl.swift in Sources */,
				226090332BEF93440053F10E /* AppDelegate.swift in Sources */,
				22A78D2D2C56469300C3A271 /* CircleDemoView.swift in Sources */,
				22A78B522C169BD2006C8B33 /* MessagesViewController.swift in Sources */,
				4B1E1BB62C6E53B0001FE006 /* UserTestViewModel.swift in Sources */,
				2260905C2BEFC2270053F10E /* String+Extension.swift in Sources */,
				1B1D76722C4FF24600134308 /* HistoryRecordBottomView.swift in Sources */,
				1B1D76742C50024500134308 /* EditTestResultController.swift in Sources */,
				BDF5E8CD2E165A7D0049236F /* AIWebVC.swift in Sources */,
				2260905B2BEFC2270053F10E /* Day+Extension.swift in Sources */,
				222CF7CF2C9567B4005F389D /* ChangeConceptionViewController.swift in Sources */,
				2237764F2C14B17000A2575F /* UIFont+Extension.swift in Sources */,
				1B1D76762C50083900134308 /* EditTestResultBottomView.swift in Sources */,
				800E7A682C8B5E1500AA9397 /* JXPhotoBrowserLog.swift in Sources */,
				22B63A002C248FF600C58D7B /* CalendarTableViewController.swift in Sources */,
				800E7A6B2C8BFD3F00AA9397 /* NoteDetailViewController.swift in Sources */,
				2286B0972C1ED73A00C59403 /* LeftAlignedCollectionViewFlowLayout.swift in Sources */,
				80CA05E82C8B4DFD00CCBC47 /* RectangleLayout.m in Sources */,
				22B63A0C2C267B1D00C58D7B /* CalendarPopupSelectionViewController.swift in Sources */,
				2252D6802C037DAA00E3672F /* WebViewController.swift in Sources */,
				808571A52C894D6500F1BB9E /* BaseViewModel.swift in Sources */,
				BDC213892E1E208C00F4128E /* CaptureStateManager.swift in Sources */,
				189E91CB2C1A7F6A007D04CA /* RecordTestsViewController.swift in Sources */,
				22680D982C0C1F9700B5DCF2 /* HomeViewController.swift in Sources */,
				80792F512CB65F4800A8770A /* RemindViewModel.swift in Sources */,
				223065632C02E2D900638708 /* SignUpViewController.swift in Sources */,
				1B49621B2C283B7B00B67BAF /* ChartsHeaderView.swift in Sources */,
				22DF42F52C5A7D1900D9DEDE /* NoticeTableViewCell.swift in Sources */,
				223776662C15B71E00A2575F /* SettingViewController.swift in Sources */,
				22B63A112C27E76900C58D7B /* SupportVCCustomerSupportCellTableViewCell.swift in Sources */,
				1B1D76782C500A3F00134308 /* TestTimeView.swift in Sources */,
				22680D942C0A2DC700B5DCF2 /* BoundViewController.swift in Sources */,
				22B63A242C28591200C58D7B /* EditTestDateVCCalendarCell.swift in Sources */,
				226D299C2C21E7A40058E91E /* AppCalendarDayTypeCell.swift in Sources */,
				2291C56C2C063F46005CC00F /* GoalSettingViewController.swift in Sources */,
				22A78B4A2C1691F6006C8B33 /* TagsViewController.swift in Sources */,
				4B3CFF432C5773600032F90F /* Documents.swift in Sources */,
				22F6ED202C822553004A27CC /* ChartDetailView.swift in Sources */,
				800E7A642C8B5E1500AA9397 /* JXPhotoBrowserView.swift in Sources */,
				800E7A622C8B5E1500AA9397 /* JXPhotoBrowserNumberPageIndicator.swift in Sources */,
				808571B32C89AC4200F1BB9E /* BaseNavigationController.swift in Sources */,
				223776532C14CA5A00A2575F /* HomeCarouselView.swift in Sources */,
				226D299E2C21E7A40058E91E /* AppCalendarConfig.swift in Sources */,
				2260905F2BEFC3000053F10E /* LanguageManager.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXVariantGroup section */
		226090382BEF93440053F10E /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				226090392BEF93440053F10E /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
		2260903D2BEF93460053F10E /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				2260903E2BEF93460053F10E /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		226090412BEF93460053F10E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		226090422BEF93460053F10E /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		226090442BEF93460053F10E /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 8F4A46CEA63C16C345C6D1C2 /* Pods-HormoneLife.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				BUILD_LIBRARY_FOR_DISTRIBUTION = NO;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = HormoneLife/HormoneLife.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 7UQU4BC565;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				EXCLUDED_ARCHS = "";
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)",
					"$(PROJECT_DIR)/Frameworks",
				);
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = HormoneLife/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = HORMONElife;
				INFOPLIST_KEY_NSCameraUsageDescription = "Do you allow HORMONElife to access the photo album? So that you can use services such as scanning, image uploading, and image saving normally";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "Do you allow HORMONElife to access the photo album? So that you can use services such as scanning, image uploading, and image saving normally";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.1.3;
				PRODUCT_BUNDLE_IDENTIFIER = com.jlink.wondfo;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OBJC_BRIDGING_HEADER = "HormoneLife/common/HormoneLife-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		226090452BEF93460053F10E /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = E8159D3155618FEA86255DA6 /* Pods-HormoneLife.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				BUILD_LIBRARY_FOR_DISTRIBUTION = NO;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = HormoneLife/HormoneLife.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 7UQU4BC565;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				EXCLUDED_ARCHS = "";
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)",
					"$(PROJECT_DIR)/Frameworks",
				);
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = HormoneLife/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = HORMONElife;
				INFOPLIST_KEY_NSCameraUsageDescription = "Do you allow HORMONElife to access the photo album? So that you can use services such as scanning, image uploading, and image saving normally";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "Do you allow HORMONElife to access the photo album? So that you can use services such as scanning, image uploading, and image saving normally";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.1.3;
				PRODUCT_BUNDLE_IDENTIFIER = com.jlink.wondfo;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OBJC_BRIDGING_HEADER = "HormoneLife/common/HormoneLife-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		2260902A2BEF93440053F10E /* Build configuration list for PBXProject "HormoneLife" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				226090412BEF93460053F10E /* Debug */,
				226090422BEF93460053F10E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		226090432BEF93460053F10E /* Build configuration list for PBXNativeTarget "HormoneLife" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				226090442BEF93460053F10E /* Debug */,
				226090452BEF93460053F10E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 226090272BEF93440053F10E /* Project object */;
}
