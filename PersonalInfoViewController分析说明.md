# PersonalInfoViewController 页面分析说明

## 概述
`PersonalInfoViewController` 是个人信息设置页面，用户可以在此页面修改个人基础信息和月经周期相关设置。当用户修改 "Your average period length"（平均月经周期长度）时，会涉及多个接口调用和数据模型。

## 页面UI组件分析

### 关键输入框
- `firstAverage` (UITextField): 对应 "Your average period length" - 月经持续天数 (1-15天)
- `secondAverage` (UITextField): 对应 "Average cycle length" - 月经周期平均长度 (15-50天)
- `firstNameField`, `lastNameField`, `birthdayField`, `tagsField` 等其他个人信息字段

### 数据选择器
- `periodLengthPickerView`: 用于选择月经持续天数 (1-15天)
- `averageLengthPickerView`: 用于选择月经周期长度 (15-50天)

## 修改 "Your average period length" 的完整流程

### 1. 用户交互触发
当用户点击 `firstAverage` 输入框时：
```swift
// 第340-342行: textFieldShouldBeginEditing 方法
case 54, 55:
    editingTextFieldOriginalValue = textField.text
    editingTextField = textField
```

### 2. 数据选择
- 显示 `periodLengthPickerView` 选择器 (1-15天范围)
- 用户选择新的天数值

### 3. 数据保存触发
当用户完成编辑时，会触发 `textFieldDidEndEditing` 方法：
```swift
// 第349-351行
func textFieldDidEndEditing(_ textField: UITextField) {
    savePersonalInfo()
}
```

### 4. 核心保存方法 `savePersonalInfo()`

#### 4.1 调用的接口
该方法会调用两个主要接口：

**接口1: 更新用户基础信息**
```swift
UserInteractor.updateUserInfo(
    userName: lastNameField.text ?? "", 
    firstName: firstNameField.text ?? "", 
    sex: 0, 
    birthday: birthdayFormat, 
    headImg: self.uploadViewModel.url, 
    label: tagsField.text ?? "", 
    photos: ""
)
```

**接口2: 更新用户配置信息**
```swift
UserInteractor.userConfigUpdate(config)
```

#### 4.2 接口详细信息

**接口1详情:**
- **API路径**: `/app/userInfo/updateUserInfo`
- **请求方法**: POST
- **主要参数**: 
  - userName: 姓氏
  - firstName: 名字
  - sex: 性别 (固定为0)
  - birthday: 生日 (格式: "yyyy-MM-dd HH:mm:ss")
  - headImg: 头像URL
  - label: 标签
  - photos: 照片

**接口2详情:**
- **API路径**: `/app/userBusinessConfig/update`
- **请求方法**: POST
- **关键参数**:
  - `period`: 月经持续天数 (来自 `firstAverage.text`)
  - `menstruationCycleAvg`: 月经周期平均长度 (来自 `secondAverage.text`)
  - `trackMethod`: 排卵追踪方法
  - `otherTrackMethod`: 其他追踪方法
- **注意**: 个人设置页面不传入 `startPeriodDate` 参数，避免后端日期验证错误

### 5. UserConfig 模型构建
```swift
// 个人设置页面不传入startPeriodDate，避免后端日期验证错误
var config = UserConfig()
// config.startPeriodDate = userDay  // 注释掉，不传入startPeriodDate
config.trackMethod = self.trackMethod
config.otherTrackMethod = self.otherTrackMethod
config.period = self.firstAverage.text  // 这里是关键！月经持续天数
config.menstruationCycleAvg = self.secondAverage.text  // 月经周期长度
```

### 6. 数据刷新
接口调用成功后，会调用 `hl_fetchUserInfo()` 刷新本地用户数据：
```swift
UserInteractor.updateUserInfo(...) { result in
    hl_fetchUserInfo()  // 重新获取用户信息
}
```

## 涉及的数据模型

### 1. UserConfig 模型
```swift
struct UserConfig: Codable {
    var period: String?  // 月经持续天数
    var menstruationCycleAvg: String?  // 月经周期平均长度
    var startPeriodDate: String?  // 上次月经开始日期
    var trackMethod: String?  // 排卵追踪方法
    var otherTrackMethod: String?  // 其他追踪方法
    // ... 其他字段
}
```

### 2. UserInfo 模型
包含用户基础信息和 `UserBusinessConfigVO` 配置信息

### 3. UserBusinessConfigVO 模型
```swift
struct UserBusinessConfigVO: Codable {
    let trackMethod, startPeriodDate: String?
    let period, menstruationCycleAvg: Int  // 注意这里是Int类型
    // ... 其他字段
}
```

### 4. CycleAndPeriodAvg 模型
```swift
struct CycleAndPeriodAvg: Codable {
    var avgPeriod: Int = 0  // 平均月经持续天数
    var avgMenstruationCycleAvg: Int = 0  // 平均月经周期长度
}
```

## 数据获取相关接口

### 获取用户基础信息
- **接口**: `UserInteractor.getUserInfo`
- **API路径**: `/app/getInfo`
- **请求方法**: GET
- **返回**: UserInfo 模型

### 获取周期和月经平均数据
- **接口**: `UserInteractor.getCycleAndPeriodAvg`
- **API路径**: `/app/userPeriodCycle/getCycleAndPeriodAvg`
- **请求方法**: GET
- **返回**: CycleAndPeriodAvg 模型

## 数据显示逻辑

页面加载时，从本地缓存的用户信息中读取数据：
```swift
// 第208-215行: 显示周期数据
self.secondAverageDaysLabel.isHidden = !((Interface.shared().loggedInUser?.cycleAndPeriodAvg?.avgMenstruationCycleAvg ?? 0) > 0)
self.firstAverageDaysLabel.isHidden = !((Interface.shared().loggedInUser?.cycleAndPeriodAvg?.avgPeriod ?? 0) > 0)

if (Interface.shared().loggedInUser?.cycleAndPeriodAvg?.avgMenstruationCycleAvg ?? 0) > 0 {
    self.secondAverage.text = "\(Interface.shared().loggedInUser?.cycleAndPeriodAvg?.avgMenstruationCycleAvg ?? 0)"
}
if (Interface.shared().loggedInUser?.cycleAndPeriodAvg?.avgPeriod ?? 0) > 0 {
    self.firstAverage.text = "\(Interface.shared().loggedInUser?.cycleAndPeriodAvg?.avgPeriod ?? 0)"
}
```

## 问题分析和解决方案

### 问题描述
根据你的操作结果，发现了一个关键问题：

**发送的参数**：
```json
{
    "menstruationCycleAvg": "28",
    "startPeriodDate": "2024-10-07 15:00:00",
    "period": "10"
}
```

**返回的错误**：
```json
{
    "code": "U0001",
    "data": null,
    "msg": "the last period day must by less than half year"
}
```

### 问题根源
错误信息 "the last period day must by less than half year" 表明：
- `startPeriodDate` (上次月经开始日期) 必须在半年以内
- 当前发送的日期是 "2024-10-07 15:00:00"，如果当前日期已经超过2024年4月7日，就会触发这个错误

### 解决方案 ✅
根据后端建议，**个人设置页面不传入 `startPeriodDate` 参数**即可正常保存：

**修改后的参数**：
```json
{
    "menstruationCycleAvg": "28",
    "period": "10"
}
```

**成功响应**：
```json
{
    "code": "00000",
    "data": null,
    "msg": "everything ok"
}
```

### 代码修改
已修改 `savePersonalInfo()` 和 `didTapSaveAction()` 方法：
```swift
// 个人设置页面不传入startPeriodDate，避免后端日期验证错误
var config = UserConfig()
// config.startPeriodDate = userDay  // 注释掉，不传入startPeriodDate
config.trackMethod = self.trackMethod
config.otherTrackMethod = self.otherTrackMethod
config.period = self.firstAverage.text
config.menstruationCycleAvg = self.secondAverage.text
```

## 总结

修改 "Your average period length" 的完整流程涉及：
1. **UI交互**: 用户选择新的天数值
2. **数据验证**:
   - 确保输入值在有效范围内 (1-15天)
   - **重要**: 验证startPeriodDate不能超过半年前
3. **接口调用**:
   - 更新用户基础信息 (`/app/userInfo/updateUserInfo`)
   - 更新用户配置信息 (`/app/userBusinessConfig/update`)
4. **错误处理**: 处理日期过期等业务逻辑错误
5. **数据刷新**: 重新获取用户信息更新本地缓存
6. **UI更新**: 刷新页面显示

**关键发现**: 系统要求上次月经开始日期必须在半年以内，这是一个重要的业务规则约束。

## 缓存机制和数据刷新问题修复

### 发现的缓存问题
1. **数据显示不一致**: 修改后返回首页再进入，数据变空
2. **缓存未更新**: 重启app后数据没有更新，显示旧数据
3. **部分更新问题**: 连续修改时会出现数据混乱（如5+22改成8+22，再改22为25时变成5+25）

### 问题根源分析
1. **数据来源分离**:
   - 页面显示数据来自 `Interface.shared().loggedInUser?.cycleAndPeriodAvg`
   - 但保存后只刷新了 `userInfo`，没有刷新 `cycleAndPeriodAvg`

2. **缺少实时刷新**:
   - `setupTextFieldDelegate()` 只在 `viewDidLoad` 时调用
   - `viewWillAppear` 时没有重新加载最新数据

3. **异步更新不完整**:
   - 保存成功后没有立即刷新周期数据
   - 缺少 `getCycleAndPeriodAvg` 调用

### 修复方案

#### 1. 添加统一的数据刷新方法
```swift
/// 刷新用户数据显示
private func refreshUserData() {
    guard let user = Interface.shared().loggedInUser?.userInfo else { return }

    // 更新基础信息
    firstNameField.text = user.firstName
    lastNameField.text = user.userName
    // ... 其他字段

    // 更新周期数据
    let cycleData = Interface.shared().loggedInUser?.cycleAndPeriodAvg
    if (cycleData?.avgMenstruationCycleAvg ?? 0) > 0 {
        self.secondAverage.text = "\(cycleData?.avgMenstruationCycleAvg ?? 0)"
    } else {
        self.secondAverage.text = ""
    }

    if (cycleData?.avgPeriod ?? 0) > 0 {
        self.firstAverage.text = "\(cycleData?.avgPeriod ?? 0)"
    } else {
        self.firstAverage.text = ""
    }
}
```

#### 2. 添加周期数据获取方法
```swift
/// 获取周期和月经平均数据
private func getCycleAndPeriodAvg() {
    UserInteractor.getCycleAndPeriodAvg { [weak self] info in
        guard let userInfo = info else { return }
        Interface.shared().loggedInUser?.cycleAndPeriodAvg = userInfo
        // 更新UI显示
        DispatchQueue.main.async {
            self?.refreshUserData()
        }
    }
}
```

#### 3. 修改页面生命周期
```swift
override func viewWillAppear(_ animated: Bool) {
    super.viewWillAppear(animated)
    setupUI()
    // 每次页面出现时刷新数据显示
    refreshUserData()
}
```

#### 4. 修改保存逻辑
```swift
UserInteractor.userConfigUpdate(config) { success in
    if success {
        // 配置更新成功后，刷新周期数据
        self.getCycleAndPeriodAvg()
    }
}
```

### 修复效果
- ✅ 每次进入页面都会显示最新数据
- ✅ 修改后立即刷新显示
- ✅ 避免了部分更新导致的数据混乱
- ✅ 解决了缓存不一致问题
