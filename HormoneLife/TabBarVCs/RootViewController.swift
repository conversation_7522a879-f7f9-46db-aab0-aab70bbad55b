import UIKit

class RootViewController: UIViewController {

    private var statusBarStyle = UIStatusBarStyle.lightContent
    override var preferredStatusBarStyle: UIStatusBarStyle { return statusBarStyle }

    override func viewDidLoad() {
        super.viewDidLoad()

        let tabBarController = TabBarController()
        addChild(tabBarController)
        view.addSubview(tabBarController.view)
        tabBarController.didMove(toParent: self)
    }

    public func updateStatusBar(style: UIStatusBarStyle) {
        statusBarStyle = style
        setNeedsStatusBarAppearanceUpdate()
    }
}
