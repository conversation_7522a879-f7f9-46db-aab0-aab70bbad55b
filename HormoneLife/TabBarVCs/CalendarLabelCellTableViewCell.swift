//
//  CalendarLabelCellTableViewCell.swift
//  HormoneLife
//
//  Created by Tank on 2024/6/21.
//

import UIKit
import SnapKit

protocol CalendarLabelCellTableViewCellDelegate: AnyObject {
    func didTapExpandButton(_ isExpand: Bool)
    func didTapLabel(_ labelType: CalendarLabel)
}

class CalendarLabelCellTableViewCell: UITableViewCell, CalendarLabelViewDelegate {

    lazy var periodView = CalendarLabelView(.period, delegate: self)
    lazy var fertileView = CalendarLabelView(.fertile, delegate: self)
    lazy var ovulationView = CalendarLabelView(.ovulation, delegate: self)
    lazy var expPeriodView = CalendarLabelView(.exp_period, delegate: self)
    lazy var expFertileView  = CalendarLabelView(.exp_fertile, delegate: self)
    lazy var expOvulationView = CalendarLabelView(.exp_ovulation, delegate: self)
    lazy var sexView = CalendarLabelView(.sex, delegate: self)
    lazy var lhUitraView = CalendarLabelView(.lh_uitra, delegate: self)
    lazy var fshView = CalendarLabelView(.fsh, delegate: self)
    lazy var pdgView = CalendarLabelView(.pdg, delegate: self)
    lazy var lhTestDayView = CalendarLabelView(.lh_testDay, delegate: self)
    lazy var hcgTestDay = CalendarLabelView(.hcg_testDay, delegate: self)
    
    weak var delegate: CalendarLabelCellTableViewCellDelegate?
    
    lazy var expandButton: UIButton = {
        let b = UIButton(type: .custom)
        b.setImage(UIImage(named: "calendarUpArow"), for: .selected)
        b.setImage(UIImage(named: "calendarDownArow"), for: .normal)
        b.addTarget(self, action: #selector(didTapExpand), for: .touchUpInside)
        return b
    }()
    
    let leftLine: UIView = {
        let l = UIView()
        l.backgroundColor = .lightGray.withAlphaComponent(0.2)
        return l
    }()
    
    let rightLine: UIView = {
        let l = UIView()
        l.backgroundColor = .lightGray.withAlphaComponent(0.2)
        return l
    }()
    
    let expandButtonBgView = UIView()
    var expandButtonBgViewTopContraint: Constraint?
    var isExpand: Bool = false {
        didSet {
            expandButton.isSelected = isExpand
            expandButtonBgViewTopContraint?.update(offset: isExpand ? 115 : 0)
            
            checkLabelStatus(isExpand)
        }
    }

    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        selectionStyle = .none
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func checkLabelStatus(_ shouldExpand: Bool) {
        [expPeriodView, expFertileView, expOvulationView, sexView, lhUitraView, fshView, pdgView].forEach {
            $0.isHidden = !shouldExpand
        }
    }
    
    func didTapSelf(_ label: CalendarLabel) {
        delegate?.didTapLabel(label)
    }
    
    @objc func didTapExpand(_ sender: UIButton) {
        print("expand: ", !sender.isSelected)
        expandButton.isSelected = !expandButton.isSelected
        checkLabelStatus(expandButton.isSelected)
        delegate?.didTapExpandButton(expandButton.isSelected)
    }
    
    func setupUI() {
        
        contentView.layer.masksToBounds = true
        
        contentView.backgroundColor = .white
        [periodView, fertileView, ovulationView, expPeriodView, expFertileView, expOvulationView, sexView, lhUitraView, fshView, pdgView, lhTestDayView, hcgTestDay, expandButtonBgView].forEach {
            contentView.addSubview($0)
        }
        lhTestDayView.isHidden = isHideLH
        
        expandButtonBgView.addSubview(expandButton)
        expandButtonBgView.addSubview(leftLine)
        expandButtonBgView.addSubview(rightLine)
        
        periodView.snp.makeConstraints { make in
            make.height.equalTo(16)
            make.top.equalToSuperview()
            make.left.equalToSuperview().inset(24)
        }
        
        fertileView.snp.makeConstraints { make in
            make.height.equalTo(16)
            make.centerY.equalTo(periodView)
            make.leading.equalTo(periodView.snp.trailing).offset(40)
        }
        
        ovulationView.snp.makeConstraints { make in
            make.height.equalTo(16)
            make.centerY.equalTo(fertileView)
            make.leading.equalTo(fertileView.snp.trailing).offset(40)
        }
        
        expPeriodView.snp.makeConstraints { make in
            make.height.equalTo(16)
            make.top.equalTo(periodView.snp.bottom).offset(12)
            make.leading.equalTo(periodView)
        }
        
        expFertileView.snp.makeConstraints { make in
            make.height.equalTo(16)
            make.centerY.equalTo(expPeriodView)
            make.leading.equalTo(expPeriodView.snp.trailing).offset(20)
        }
        
        expOvulationView.snp.makeConstraints { make in
            make.height.equalTo(16)
            make.top.equalTo(expPeriodView.snp.bottom).offset(12)
            make.leading.equalTo(expPeriodView)
        }
        
        sexView.snp.makeConstraints { make in
            make.height.equalTo(16)
            make.centerY.equalTo(expOvulationView)
            make.leading.equalTo(expOvulationView.snp.trailing).offset(20)
        }
        
        lhUitraView.snp.makeConstraints { make in
            make.height.equalTo(16)
            make.top.equalTo(expOvulationView.snp.bottom).offset(12)
            make.leading.equalTo(expOvulationView)
        }
        
        fshView.snp.makeConstraints { make in
            make.height.equalTo(16)
            make.centerY.equalTo(lhUitraView)
            make.leading.equalTo(lhUitraView.snp.trailing).offset(20)
        }
        
        pdgView.snp.makeConstraints { make in
            make.height.equalTo(16)
            make.centerY.equalTo(fshView)
            make.leading.equalTo(fshView.snp.trailing).offset(20)
        }
        
        hcgTestDay.snp.makeConstraints { make in
            make.height.equalTo(16)
            make.top.equalTo(lhUitraView.snp.bottom).offset(12)
            make.leading.equalTo(expOvulationView)
        }
        
        lhTestDayView.snp.makeConstraints { make in
            make.height.equalTo(16)
            make.centerY.equalTo(hcgTestDay)
            make.leading.equalTo(hcgTestDay.snp.trailing).offset(20)
        }
        
        expandButtonBgView.snp.makeConstraints { make in
            make.leading.trailing.bottom.equalToSuperview()
            make.height.equalTo(50)
            expandButtonBgViewTopContraint = make.top.equalTo(periodView.snp.bottom).offset(0).constraint
        }
        
        expandButton.snp.makeConstraints { make in
            make.height.width.equalTo(14)
            make.center.equalToSuperview()
        }
        
        leftLine.snp.makeConstraints { make in
            make.height.equalTo(1)
            make.left.equalToSuperview().inset(24)
            make.right.equalTo(expandButton.snp.left).offset(-20)
            make.centerY.equalToSuperview()
        }
        
        rightLine.snp.makeConstraints { make in
            make.height.equalTo(1)
            make.right.equalToSuperview().inset(24)
            make.left.equalTo(expandButton.snp.right).offset(20)
            make.centerY.equalToSuperview()
        }
    }
}


protocol CalendarLabelViewDelegate: AnyObject {
    func didTapSelf(_ label: CalendarLabel)
}

class CalendarLabelView: UIView {
    var img: UIImageView = {
        let i = UIImageView()
        i.contentMode = .scaleAspectFit
        return i
    }()
    
    var title: UILabel = {
        let t = UILabel()
        t.font = .regularGilroyFont(12)
        t.textColor = .black
        return t
    }()
    
    var calendarLabel: CalendarLabel = .period
    
    weak var delegate: CalendarLabelViewDelegate?
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    convenience init(_ label: CalendarLabel, delegate: CalendarLabelViewDelegate? = nil) {
        self.init(frame: .zero)
        calendarLabel = label
        img.image = UIImage(named: label.imageName)
        title.text = label.title
        self.delegate = delegate
        
        let tap = UITapGestureRecognizer(target: self, action: #selector(didTap))
        self.addGestureRecognizer(tap)
    }
    
    @objc func didTap() {
        delegate?.didTapSelf(calendarLabel)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func setupUI() {
        addSubview(img)
        addSubview(title)
        
        img.snp.makeConstraints { make in
            make.width.height.equalTo(12)
            make.leading.centerY.equalToSuperview()
        }
        
        title.snp.makeConstraints { make in
            make.leading.equalTo(img.snp.trailing).offset(4)
            make.height.equalTo(16)
            make.top.bottom.trailing.equalToSuperview()
        }
    }
}

enum CalendarLabel: String, CaseIterable {
    case period = "periodState"
    case fertile = "fertileWindowState"
    case ovulation = "ovulationDay"
    case exp_period = "expectedPeriodState"
    case exp_fertile = "expectedFertileWindowState"
    case exp_ovulation = "expectedOvulationDayState"
    case sex = "sex"
    case lh_uitra = "lhultraState"
    case fsh = "fshstate"
    case pdg = "pdgstate"
    case lh_testDay = "lhtestdayState"
    case hcg_testDay = "hcgtestdayState"
    
    static func dateState(detail: CalendarDetailVO, isEnableDate: Bool = true) -> [CalendarLabel] {
        var labelData: [CalendarLabel] = []
        
        if detail.expectedPeriodState {
            labelData.append(.exp_period)
        }
        if detail.expectedFertileWindowState {
            labelData.append(.exp_fertile)
        }
        if detail.expectedOvulationDayState {
            labelData.append(.exp_ovulation)
        }
        if detail.sex {
            labelData.append(.sex)
        }
        if detail.lhultraState {
            labelData.append(.lh_uitra)
        }
        if detail.fshstate {
            labelData.append(.fsh)
        }
        if detail.pdgstate {
            labelData.append(.pdg)
        }
        if detail.lhstate && !isHideLH {
            labelData.append(.lh_testDay)
        }
        if detail.hcgstate {
            labelData.append(.hcg_testDay)
        }
        if detail.periodState {
            labelData.append(isEnableDate ? .period : .exp_period)
        }
        if detail.fertileWindowState {
            labelData.append(isEnableDate ? .fertile : .exp_fertile)
        }
        if detail.ovulationDay {
            labelData.append(isEnableDate ? .ovulation : .exp_ovulation)
        }
        return labelData
    }
    
    var imageName: String {
        switch self {
        case .period: return "solidCirclePink"
        case .fertile: return "solidCircleGreen"
        case .ovulation: return  "solidCircleDarkGreen"
        case .exp_period: return "dotCirclePink"
        case .exp_fertile: return "dotCircleGreen"
        case .exp_ovulation: return "OvulationImage"
        case .sex: return "heartPink"
        case .lh_uitra: return "trangleRed"
        case .fsh: return "triangleYellow"
        case .pdg: return "trianglePurple"
        case .lh_testDay: return "trianglePink"
        case .hcg_testDay: return "trangleCyan"
        }
    }
    
    var title: String {
        switch self {
        case .period: return "Period"
        case .fertile: return "Fertile Window"
        case .ovulation: return  "Ovulation Day"
        case .exp_period: return "Expected Period"
        case .exp_fertile: return "Expected Fertile window"
        case .exp_ovulation: return "Expected Ovulation Day"
        case .sex: return "Sex"
        case .lh_uitra: return "LH Ultra Test"
        case .fsh: return "FSH Test"
        case .pdg: return "PdG Test"
        case .lh_testDay: return "LH Test"
        case .hcg_testDay: return "HCG Test"
        }
    }
    
    var calendarCurrentStatusTitle: String {
        switch self {
        case .period: return "Period"
        case .fertile: return "Fertile Window"
        case .ovulation: return  "Ovulation Day"
        default: return ""
        }
    }
    
    var chartTypeColor: UIColor {
        switch self {
        case .period: return UIColor("#FF7777").withAlphaComponent(0.8)
        case .fertile: return UIColor("#4AC5B1").withAlphaComponent(0.8)
        case .ovulation: return  UIColor("#FF6ED8").withAlphaComponent(0.8)
//        case .exp_period: return "dotCirclePink"
//        case .exp_fertile: return "dotCircleGreen"
//        case .exp_ovulation: return "OvulationImage"
//        case .sex: return "heartPink"
//        case .lh_uitra: return "trangleRed"
//        case .fsh: return "triangleYellow"
//        case .pdg: return "trianglePurple"
//        case .lh_testDay: return "trianglePink"
            //case .hcg_testDay: return "trangleCyan"
        default: return .white
        }
    }
    
    var chartTypeImageName: String {
        switch self {
//        case .period: return "solidCirclePink"
//        case .fertile: return "solidCircleGreen"
//        case .ovulation: return  "solidCircleDarkGreen"
//        case .exp_period: return "dotCirclePink"
//        case .exp_fertile: return "dotCircleGreen"
//        case .exp_ovulation: return "OvulationImage"
        case .sex: return "chartHeartIcon"//"heartPink"
//        case .lh_uitra: return "trangleRed"
//        case .fsh: return "triangleYellow"
//        case .pdg: return "trianglePurple"
//        case .lh_testDay: return "trianglePink"
//        case .hcg_testDay: return "trangleCyan"
        default: return "nil"
        }
    }
}
