//
//  LineChartViewTableViewCell.swift
//  HormoneLife
//
//  Created by Tank on 2024/10/6.
//

import UIKit

class LineChartViewTableViewCell: UITableViewCell, LineChartDemoViewDelegate {
    
    weak var delegate: LineChartDemoViewDelegate?
    
    var lineView: LineChartWithTitleView?
    
    let bottomView: UIView = {
        let l = UIView()
        l.backgroundColor = .themeColor
        return l
    }()

    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        selectionStyle = .none
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func prepareForReuse() {
        super.prepareForReuse()
        lineView = nil
        lineView?.lineChartView.cycleData = nil
    }
    
    func configLineViewWith(cycleData: CycleFoldLineStatistic, lineChartDelegate: LineChartDemoViewDelegate? = nil) {
        lineView = LineChartWithTitleView(frame: .zero)
        lineView?.lineChartView.isLineChartScrollEnbled = LineChartConfig.isLineChartScrollEnbled
        lineView?.lineChartView.delegate = lineChartDelegate
        lineView?.lineChartView.preChartBtn.isHidden = true
        lineView?.lineChartView.nextChartBtn.isHidden = true
        
        guard let type = LineChartDemoView.chartTypeByTypeString(typeString: cycleData.pageType) else {
            return
        }
        lineView?.chartType = type // first
        lineView?.titleLabel.text = type.title
        lineView?.lineChartView.cycleData = cycleData // after
        lineView?.lineChartView.lineChartView.isUserInteractionEnabled = false
        guard let v = lineView else { return }
        contentView.backgroundColor = .themeColor
        contentView.addSubview(v)
        
        v.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(10)
            make.top.equalToSuperview()
        }
        
        contentView.addSubview(bottomView)
        bottomView.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(10)
            make.height.equalTo(14)
            make.bottom.equalToSuperview()
            make.top.equalTo(v.snp.bottom)
        }
    }
}
