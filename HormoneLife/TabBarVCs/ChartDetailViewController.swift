//
//  ChartDetailViewController.swift
//  HormoneLife
//
//  Created by Tank on 2024/8/24.
//

import UIKit
import DGCharts

class ChartDetailViewController: BaseViewController, UIScrollViewDelegate, LineChartDemoViewDelegate, CyclePickerViewDelegate {
    let viewWidth = Int(UIScreen.main.bounds.width)
    
    private lazy var collectionBackView: UIView = {
        let v = UIView()
        v.backgroundColor = .white
        return v
    }()
    
    lazy var cycleListCollectionView: UICollectionView = {
        let collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        collectionView.backgroundColor = .white
        collectionView.keyboardDismissMode = .onDrag
        collectionView.register(NormalSingleTextCollectionViewCell.classForCoder(), forCellWithReuseIdentifier: NormalSingleTextCollectionViewCell.description())
        collectionView.dataSource = self
        collectionView.delegate = self
        collectionView.contentInset = UIEdgeInsets(top: 0, left: 0, bottom: 0, right: 0)
        collectionView.showsHorizontalScrollIndicator = false
        collectionView.contentInset = .init(top: 0, left: 10, bottom: 0, right: 10)
        collectionView.reloadData()
        
        return collectionView
    }()
    
    lazy var layout: LeftAlignedCollectionViewFlowLayout = {
        let layout = LeftAlignedCollectionViewFlowLayout()
        layout.scrollDirection = .horizontal
        layout.minimumLineSpacing = 10
        layout.estimatedItemSize = CGSize(width: UIScreen.main.bounds.width - 20, height: 25)
        layout.itemSize = UICollectionViewFlowLayout.automaticSize
        return layout
    }()
    
    var selectIndex = 0
    
    lazy var hScrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.tag = 100
        scrollView.delegate = self
//        scrollView.isPagingEnabled = true
        scrollView.isScrollEnabled = false
        scrollView.showsVerticalScrollIndicator = false
        scrollView.showsHorizontalScrollIndicator = false
        scrollView.alwaysBounceHorizontal = true
        return scrollView
    }()
    let container = UIView()
    
    lazy var cyclePickerView: CyclePickerView = {
        let pickerView = CyclePickerView(frame: CGRect(x: 0, y: 0, width: UIScreen.main.bounds.width, height: 500))
        pickerView.delegate = self
        pickerView.dataSource = cycleDurationDateOptions
        pickerView.tag = 100
        return pickerView
    }()
    
    var cycleDurationDateOptions: [String] = [] {
        didSet {
            self.searchBarView.textField.inputView = cycleDurationDateOptions.isEmpty ? nil : cyclePickerView
            self.searchBarView.isTextFieldCouldBecomeFirstResponse = !cycleDurationDateOptions.isEmpty
            
            guard self.searchBarView.textField.text == "Select Cycle",
                  let firstCycle = cycleDurationDateOptions.first else { return }
            let durationDate = firstCycle.components(separatedBy: "        ")
            self.searchBarView.textField.text = durationDate.first
            self.selectedCycle = cycleList.first
        }
    }
    
    var cycleList: [CycleSimple] = []
    var selectedCycle: CycleSimple? = nil {
        didSet {
            guard self.selectedCycle != oldValue else { return }
            self.loadLineChartData()
        }
    }
    var currentCycleRound: String = ""
    
    lazy var searchBarView = ChartsHeaderView(type: .chartDetail)
    
    var cycleStatistic: CycleFoldLineStatistic?
    
    var chartView: ChartDetailView?
    var chartViews: [ChartDetailView] {
        guard let chart = chartView else { return [] }
        return [chart]
    }
    
    var chartType: LineChartDemoView.ChartType = .lhUltra
    let viewModel = UserTestViewModel()
    
    var temperatureUnit: Int = 1 {
        didSet {
            guard temperatureUnit != oldValue else { return }
            loadLineChartData()
        }
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()

//        navigationItem.leftBarButtonItem = nil
        navigationItem.title = chartType.chartViewTitle
        view.backgroundColor = .themeColor
        searchBarView.downloadButton.addTarget(self, action: #selector(didTapDownloadButton), for: .touchUpInside)
        setupUI()
        setupContentOffset()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        fetchCycleList()
        loadLineChartData()
    }
    
    func fetchCycleList() {
        self.cycleDurationDateOptions = []
        viewModel.getCycleSimpleList(pageType: chartType.description) { cycleSimpleList in
            guard !cycleSimpleList.isEmpty else { return }
            self.cycleList = cycleSimpleList
            
            cycleSimpleList.forEach { cycle in
                if let fromDate = cycle.startCycleTime?.yyyyMMddHHmmss_yyyyMMdd()?.replacingOccurrences(of: "-", with: "/") {
                    
                    var cycleRound = ""
                    
                    if let toDate = cycle.endCycleTime?.yyyyMMddHHmmss_yyyyMMdd()?.replacingOccurrences(of: "-", with: "/") {
                        cycleRound = "\(fromDate)   -   \(toDate)"
                    } else {
                        cycleRound = "Current cycle"
                        
                        if let expectNextStartCycleTime = cycle.expectNextStartCycleTime?.yyyyMMddHHmmss_yyyyMMdd()?.replacingOccurrences(of: "-", with: "/") {
                            self.currentCycleRound = "\(fromDate)   -   \(expectNextStartCycleTime)"
                        }
                    }
                    
                    self.cycleDurationDateOptions.append(cycleRound)
                }
                self.cyclePickerView.dataSource = self.cycleDurationDateOptions
            }
            
            self.cycleListCollectionView.reloadData()
        }
    }
    
    func loadLineChartData() {
        func setupStatisticData(data: CycleFoldLineStatistic?) {
            guard let statistic = data else { return }
            self.cycleStatistic = statistic
            
            let cyclesCount = self.cycleList.count
            var currentCycleIndex = 0
            if let selectCycle = self.selectedCycle {
                currentCycleIndex = self.cycleList.firstIndex(of: selectCycle) ?? 0
            }
            
            let isPrebtnHiddle = currentCycleIndex == 0
            let isNextBtnHiddel = currentCycleIndex == cyclesCount - 1
            
            if self.chartType == .temp {
                self.chartView = ChartDetailView(cycleData: statistic, pageType: self.chartType, delegate: self, lineViewDelegate: self, temperatureUnit: self.temperatureUnit)
            } else {
                self.chartView = ChartDetailView(cycleData: statistic, pageType: self.chartType, lineViewDelegate: self)
            }
            self.updateChartView()
        }
        
        guard let cycleId = selectedCycle?.id else { return }
        self.chartView?.removeFromSuperview()
        self.chartView = nil
        
        switch chartType {
        case .temp:
            viewModel.temperatureFoldLineStatisticBy(cycleId: cycleId, temperatureUnit: self.temperatureUnit) { data in
                setupStatisticData(data: data)
            }
        case .conception:
            viewModel.cycleOvulationFoldLineStatistic(cycleId: cycleId) { data in
                setupStatisticData(data: data)
            }
        default:
            viewModel.cycleFoldLineStatisticDetailBy(cycleId: cycleId, pageType: chartType.description) { data in
                setupStatisticData(data: data)
            }
        }
    }
    
    func setupContentOffset() {
//        guard let type = chartViews.firstIndex(where: { chart in
//            chart.chartView.chartType == chartType
//        }) else { return }
//        let x = (type.rawValue - 1) * viewWidth
//        hScrollView.setContentOffset(CGPoint(x: x, y: 0), animated: true)
    }
    
    func didTapPreChart(chartType: LineChartDemoView.ChartType) {
        var currentCycleIndex = 0
        if let selectCycle = self.selectedCycle {
            currentCycleIndex = self.cycleList.firstIndex(of: selectCycle) ?? 0
        }
        if cycleList.count > currentCycleIndex + 1 {
            let durationDate = cycleDurationDateOptions[currentCycleIndex + 1].components(separatedBy: "        ")
            self.searchBarView.textField.text = durationDate.first
            selectedCycle = cycleList[currentCycleIndex + 1]
        } else {
            showToachMessage(message: "no more cycles")
        }
        
//        guard let type = chartViews.first, chartType != type.lineView?.chartType else { return }
//        let x = (chartType.rawValue - 1) * viewWidth
//        hScrollView.setContentOffset(CGPoint(x: x, y: 0), animated: true)
    }
    
    func didTapNextChart(chartType: LineChartDemoView.ChartType) {
        var currentCycleIndex = 0
        if let selectCycle = self.selectedCycle {
            currentCycleIndex = self.cycleList.firstIndex(of: selectCycle) ?? 0
        }
        if currentCycleIndex > 0, cycleList.count > currentCycleIndex - 1 {
            let durationDate = cycleDurationDateOptions[currentCycleIndex - 1].components(separatedBy: "        ")
            self.searchBarView.textField.text = durationDate.first
            selectedCycle = cycleList[currentCycleIndex - 1]
        } else {
            showToachMessage(message: "no more cycles")
        }
        
//        guard let type = chartViews.last, chartType != type.lineView?.chartType else { return }
//        let x = (chartType.rawValue + 1) * viewWidth
//        hScrollView.setContentOffset(CGPoint(x: x, y: 0), animated: true)
    }
    
    @objc func didTapDownloadButton() {
        let cycleRound = searchBarView.textField.text == "Current cycle" ? self.currentCycleRound : searchBarView.textField.text
        let downloadVC = LineChartDownloadViewController(chartView: self.chartView, cycleStatistic: self.cycleStatistic, chartType: self.chartType, chartTitle: chartType.chartViewTitle, chartCycle: cycleRound)
        self.navigationController?.pushViewController(downloadVC, animated: true)
    }

    private func setupUI() {
        view.addSubview(hScrollView)
        hScrollView.addSubview(container)
        
        //if chartType == .conception || chartType == .temp {
            view.addSubview(searchBarView)
            searchBarView.snp.makeConstraints { make in
                make.left.top.right.equalToSuperview().inset(10)
            }
            
            hScrollView.snp.makeConstraints { make in
                make.left.right.bottom.equalToSuperview()
                make.top.equalTo(searchBarView.snp.bottom).offset(16)
                make.height.equalTo(container)
            }
            
            /*
        } else {
            view.addSubview(collectionBackView)
            collectionBackView.snp.makeConstraints { make in
                make.height.equalTo(60)
                make.left.right.equalToSuperview()
                make.top.equalToSuperview().inset(2)
            }
            
            collectionBackView.addSubview(cycleListCollectionView)
            cycleListCollectionView.snp.makeConstraints { make in
                make.height.equalTo(50)
                make.left.right.equalToSuperview().inset(10)
                make.bottom.equalToSuperview()
            }
            
            hScrollView.snp.makeConstraints { make in
                make.left.right.bottom.equalToSuperview()
                make.top.equalTo(collectionBackView.snp.bottom)
                make.height.equalTo(container)
            }
        }
             */
        
        container.snp.makeConstraints { make in
            make.edges.equalTo(hScrollView)
            make.width.greaterThanOrEqualTo(hScrollView) //
        }
    }
    
    private func updateChartView() {
        for (index, chart) in chartViews.enumerated() {
            container.addSubview(chart)
            
            chart.snp.makeConstraints { make in
                make.bottom.top.equalToSuperview()
                make.width.equalTo(viewWidth) //要
                make.left.equalToSuperview().inset(index * viewWidth)
                if index == chartViews.count - 1 {
                    make.right.equalToSuperview()
                }
            }
        }
    }
    
    func didTapSaveIndex(_ index: Int) {
        didTapCancel()
        if cycleDurationDateOptions.count > index {
            let durationDate = cycleDurationDateOptions[index].components(separatedBy: "        ")
            searchBarView.textField.text = durationDate.first
        }
        
        if cycleList.count > index {
            selectedCycle = cycleList[index]
//            loadLineChartData()
        }
    }
    
    func didTapCancel() {
        searchBarView.textField.resignFirstResponder()
    }
}

extension ChartDetailViewController: LineChartWithTitleViewDelegate {
    func temperatureSwitch(index: Int) {
        self.temperatureUnit = index == 0 ? 2 : 1
    }
}

extension ChartDetailViewController: UICollectionViewDelegate, UICollectionViewDataSource, UICollectionViewDelegateFlowLayout {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        cycleDurationDateOptions.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        guard cycleDurationDateOptions.count > indexPath.item else {
            return UICollectionViewCell()
        }
        
        let model = cycleDurationDateOptions[indexPath.item]
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: NormalSingleTextCollectionViewCell.description(), for: indexPath) as! NormalSingleTextCollectionViewCell
        cell.titleLab.text = model
        cell.containerView.backgroundColor = .clear
        cell.titleLab.font = .mediumGilroyFont(14)
        cell.lineView.isHidden = (self.selectIndex != indexPath.item)
        if self.selectIndex == indexPath.item {
            cell.titleLab.textColor = UIColorFromRGB(rgbValue: 0x360C5E)
        } else {
            cell.titleLab.textColor = UIColorFromRGB(rgbValue: 0x360C5E, alpha: 0.6)
        }
        
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        self.selectIndex = indexPath.item
        
        if cycleList.count > indexPath.item {
            selectedCycle = cycleList[indexPath.item]
            loadLineChartData()
        }

        collectionView.scrollToItem(at: indexPath, at: .centeredHorizontally, animated: true)
        collectionView.reloadData()
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        guard cycleDurationDateOptions.count > indexPath.item else {
            return .zero
        }
        let model = cycleDurationDateOptions[indexPath.item]
        let width = (String.width(text: model, textHeight: 18, font: .mediumGilroyFont(14)) + 20)
        return CGSize(width: width, height: 47)
    }
}
