//
//  HomeViewController.swift
//  HormoneLife
//
//  Created by Tank on 2024/6/2.
//

import UIKit
import SnapKit

class HomeViewController: BaseViewController {
    
    @IBOutlet weak var carouselContainerView: UIView!
    
    @IBOutlet weak var homeTitle: UILabel!
    @IBOutlet weak var homeContent: UILabel!
    @IBOutlet weak var homeRate: UILabel!
    @IBOutlet weak var captureResultsView: UIView!
    @IBOutlet weak var homeRateView: UIView!
    
    @IBOutlet weak var chanceOfConceptionView: UIStackView!
    var guidelineStep: GuidelineStep = .first
    
    var guidelineImage: UIImageView = {
        let g = UIImageView()
        g.isUserInteractionEnabled = true
        g.contentMode = .scaleToFill
        return g
    }()
    
    // AI floating button
    private lazy var aiFloatingButton: UIButton = {
        let button = UIButton(type: .custom)
        button.frame = CGRect(x: 0, y: 0, width: 64, height: 64)

        // 设置圆角 - 需要 masksToBounds = true 来裁剪内容
        button.layer.cornerRadius = 32
        button.layer.masksToBounds = true

        // 设置图标
        button.setImage(UIImage(named: "ai_icon"), for: .normal)
        button.tintColor = .white

        // 为了同时有圆角和阴影，需要添加一个容器视图
        let containerView = UIView()
        containerView.frame = button.frame
        containerView.layer.cornerRadius = 32
        containerView.layer.shadowColor = UIColor.black.cgColor
        containerView.layer.shadowOffset = CGSize(width: 0, height: 3)
        containerView.layer.shadowOpacity = 0.3
        containerView.layer.shadowRadius = 4
        containerView.layer.masksToBounds = false

        // 将按钮添加到容器视图中
        containerView.addSubview(button)
        button.frame = containerView.bounds
        // 直接为内层按钮也添加点击事件，避免事件被内层按钮拦截后外层不触发
        button.addTarget(self, action: #selector(openAIWebView), for: .touchUpInside)

        // 创建一个包装按钮来返回
        let wrapperButton = UIButton(type: .custom)
        wrapperButton.frame = containerView.frame
        wrapperButton.addSubview(containerView)
        wrapperButton.addTarget(self, action: #selector(openAIWebView), for: .touchUpInside)

        return wrapperButton
    }()
    
    var bannerDatas: [Advertisement]?
    
    let homeCarouselView = HomeCarouselView()
    lazy var homePopupView = HomeCameraPopupView(delegate: self)
    var homePopupViewBottomConstraint: Constraint?

    override func viewDidLoad() {
        super.viewDidLoad()

        view.backgroundColor = .themeColor
        print("token: ", UserDefaults.standard.userToken)
        
        setupGuidelinePage()
        setNavigationBar()
        setCarouselView()
        setupPopupView()
        setupAIFloatingButton()// AI floating button
        fetchUserInfo()
        captureResultsView.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(didTapCapture)))
        homeCarouselView.clickIndex = { [weak self] index in
            let model = self!.bannerDatas![index]
            guard let link = model.link else {
                return
            }
            let webview = WebViewController(link)
            webview.title = model.name
            self?.navigationController?.pushViewController(webview, animated: true)
        }
        
        self.chanceOfConceptionView.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(helpAction)))
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        fetchHomeData()
    }
    
    @objc func helpAction() {
        let vc = SupportAndHelpHomeViewController()
        self.navigationController?.pushViewController(vc, animated: true)
    }
    
    @objc func didTapRateView() {
        guard UserDefaults.standard.isLoginAsGuestWithoutAccount == false else {
            let vc = CreateAccountPopupViewController()
            vc.delegate = self
            vc.modalPresentationStyle = .overFullScreen
            self.navigationController?.present(vc, animated: true)
            return
        }
        
        let chartDetailVC = ChartDetailViewController()
        chartDetailVC.chartType = .conception
        pushToNext(chartDetailVC)
    }
    
    
    @IBOutlet weak var helpImageView: UIImageView!
    
    func setupUI() {
        func extractNumbers(from string: String) -> [String] {
            var numbers: String = ""
            for character in string {
                if let number = Int(String(character)) {
                    numbers += "\(number)"
                }
            }
            return [numbers]
        }
        
        homeTitle.highlightWords(words: extractNumbers(from: homeTitle.text ?? ""))
        homeContent.setLineHeight(2)
        homeRateView.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(didTapRateView)))
    }
    
    private func fetchHomeData() {
        HomeInteractor.fetchHomeData { homeData in
            self.homeTitle.text = homeData?.title ?? "Your fertile window starts in 6 days"
            self.homeContent.text = homeData?.content ?? "You can use an LH test strip to determine your ovulation day! Snap a photo of your results."
            self.homeRate.text = "\(homeData?.rate ?? 50)"
            self.setupUI()
            
            HomeDataSingleton.shared().homeData = homeData
        }
        
        HomeInteractor.fetchAdOnPosition(.banner) { [weak self] result in
            self?.bannerDatas = result
            var urls = [String]()
            for item in result {
                if let img = item.img {
                    urls.append(img)
                }
            }
            guard urls.count > 0 else {
                return
            }
            self?.homeCarouselView.refreshCarouseDatas(urls: urls)
            print("")
        }
    }
        
    
    private func fetchUserInfo() {
        UserInteractor.getUserInfo { info in
            guard let userInfo = info else { return }
            
            let user = LoginUser(deviceId: UserDefaults.standard.deviceID, token: UserDefaults.standard.userToken, userInfo: userInfo)
            Interface.shared().loggedInUser = user
            self.getCycleAndPeriodAvg()
        }
    }
    
    private func getCycleAndPeriodAvg() {
        UserInteractor.getCycleAndPeriodAvg { info in
            guard let userInfo = info else { return }
            Interface.shared().loggedInUser?.cycleAndPeriodAvg = userInfo
        }
    }
    
    private func setupPopupView() {
        view.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(didTapClose)))
        
        currentWindow?.addSubview(homePopupView)
        homePopupView.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            homePopupViewBottomConstraint = make.bottom.equalToSuperview().inset(-1000).constraint
        }
    }
    
    private func setCarouselView() {
        carouselContainerView.addSubview(homeCarouselView)
        homeCarouselView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(HomeCarouselView.carouselViewHeight)
        }
    }
    
    private func setNavigationBar() {
        navigationItem.leftBarButtonItem = UIBarButtonItem(image: UIImage(named: "homeNavLeftBtn"), style: .done, target: self, action: #selector(didTapNavLeftButton))
        
        let label = UILabel(frame: CGRect(x: 0, y: 0, width: 75, height: 16))
        label.text = "Switch roles"
        label.font = .regularGilroyFont(13)
        label.textColor = .mainTextColor
        
        let imageView = UIImageView(frame: CGRect(x: 0, y: 0, width: 24, height: 24))
        imageView.image = UIImage(named: "homeNavRightBtn")
        
        let stackview = UIStackView(arrangedSubviews: [label, imageView])
        stackview.distribution = .fill
        stackview.alignment = .center
        stackview.axis = .horizontal
        stackview.spacing = 12
        
        let tapNavRightBtn = UITapGestureRecognizer(target: self, action: #selector(didTapNavRightButton))
        stackview.addGestureRecognizer(tapNavRightBtn)
        
        navigationItem.rightBarButtonItem = UIBarButtonItem(customView: stackview)
    }
    
    @objc func didTapNavRightButton() {
        didTapClose()
        let roleSwitchVC = GoalSettingViewController()
        hidesBottomBarWhenPushed = true
        navigationController?.pushViewController(roleSwitchVC, animated: true)
        hidesBottomBarWhenPushed = false
    }
    
    @objc func didTapNavLeftButton() {
        didTapClose()
        let meVC = ProfileViewController()
        meVC.viewController = self
        meVC.delegate = self
        let configuration = ModalConfiguration.default
        configuration.direction = .left
        configuration.isEnableShadow = false
        configuration.animationDuration = 0.2
        let size = CGSize(width: UIScreen.main.bounds.size.width * 2/3, height: UIScreen.main.bounds.size.height)
        let nav = UINavigationController(rootViewController: meVC)
        let topVC = topMostController()
        topVC?.presentModalViewController(nav, contentSize: size, configuration: configuration, completion: nil)
    }
    
    @objc func didTapCapture() {
        UIView.animate(withDuration: 0.15) {
            self.homePopupViewBottomConstraint?.update(inset: 0)
            currentWindow?.layoutIfNeeded()
        }
    }
    
    private func setupAIFloatingButton() {
        view.addSubview(aiFloatingButton)
        aiFloatingButton.snp.makeConstraints { make in
            make.right.equalToSuperview().inset(16)
            make.bottom.equalToSuperview().inset(60)//距离tabbar底部 35，距离最底部119（包含安全距离）
            make.width.height.equalTo(64)
        }
    }
    
    @objc private func openAIWebView() {
        let aiVC = AIChatVC()
        aiVC.hidesBottomBarWhenPushed = true
        navigationController?.pushViewController(aiVC, animated: true)
        // let aiWebVC = AIWebVC()
        // aiWebVC.hidesBottomBarWhenPushed = true
        // navigationController?.pushViewController(aiWebVC, animated: true)
    }
}

extension HomeViewController: ProfileViewControllerDelegate {
    func didTap(_ profileType: ProfileViewController.ProfileActionType) {
        switch profileType {
        case .cycleReports:
            let cycleReportsVC = CycleReportTableViewController()
            pushToNext(cycleReportsVC)
        case .supportAndHelp:
            let supportVC = SupportAndHelpHomeViewController()
            pushToNext(supportVC)
        case .aboutHormonelife:
            AppInfoInteractor.fetchAppInfo(1) { result in
                if let appInfo = result, (appInfo.url != nil || appInfo.content != nil) {
                    let webView = AboutHormonelifeViewController(appInfo.url, contentString: appInfo.content)
                    webView.title = "Hormonelife"
                    self.pushToNext(webView)
                }
            }
        case .settings:
            let settingPage = SettingViewController()
            pushToNext(settingPage)
        case .avatarEdit:
            let infoVc = PersonalInfoViewController()
            pushToNext(infoVc)
        case .notifications:
            let notiVC = NotificationsViewController()
            pushToNext(notiVC)
        case .memo:
            let noteVC = NotesTableTableViewController()
            pushToNext(noteVC)
        default:
            return
        }
    }
    
    private func pushToNext(_ nextViewController: UIViewController) {
        hidesBottomBarWhenPushed = true
        navigationController?.pushViewController(nextViewController, animated: true)
        hidesBottomBarWhenPushed = false
    }
}

extension HomeViewController: HomeCameraPopupViewDelegate {
    @objc func didTapClose() {
        UIView.animate(withDuration: 0.15) {
            self.homePopupViewBottomConstraint?.update(inset: -(self.homePopupView.frame.height))
            currentWindow?.layoutIfNeeded()
        }
    }
    
    func didTapCell(type: TestType) {
        guard !isRunningOnSimulator() else { return }
        didTapClose()
        let captureVC = CustomCameraViewController()
        
        captureVC.pageType = type.homeTitle
        
        captureVC.paperType = type
        pushToNext(captureVC)
    }
}

extension HomeViewController {
    enum GuidelineStep: Int, CaseIterable {
        case first = 0
        case second
        case third
        case done
    }
    
    private func setupGuidelinePage(_ step: GuidelineStep = .first) {
        guard !UserDefaults.standard.hasSeemGuidelinePage else {
            return
        }
        
        if guidelineImage.superview == nil {
            currentWindow?.addSubview(guidelineImage)
            guidelineImage.snp.makeConstraints { make in
                make.edges.equalToSuperview()
            }
        }
        
        view.isUserInteractionEnabled = false
        if step == .first {
            guidelineImage.image = UIImage(named: "guideline1")
            guidelineStep = .second
        } else if step == .second {
            guidelineImage.image = UIImage(named: "guideline2")
            guidelineStep = .third
        } else {
            guidelineImage.image = UIImage(named: "guideline3")
            guidelineStep = .done
        }
        
        let guesture = UITapGestureRecognizer(target: self, action: #selector(nextStep))
        guidelineImage.addGestureRecognizer(guesture)
        
    }
    
    @objc func nextStep(sender: UITapGestureRecognizer) {
        let location = sender.location(in: guidelineImage)
        if location.x < view.center.x {
            if guidelineStep == .third { guidelineStep = .first }
            if guidelineStep == .done { guidelineStep = .second }
        }
        
        guard guidelineStep == .done else {
            setupGuidelinePage(guidelineStep)
            return
        }
        
        guidelineImage.removeFromSuperview()
        view.isUserInteractionEnabled = true
        UserDefaults.standard.hasSeemGuidelinePage = true
    }
}

extension HomeViewController : CreateAccountPopupViewControllerDelegate {
    func didTapClosePopup() {
        
    }
    
    func didTapCreateAccount() {
        let signUpVC = SignUpViewController()
        navigationController?.pushViewController(signUpVC, animated: true)
    }
    
}
