//
//  HomeCarouselView.swift
//  MilkTime
//
//  Created by Match on 2023/7/15.
//

import UIKit
//import SQAutoScrollView

class HomeCarouselView: UIView {
    
    static let carouselViewHeight: CGFloat = UIScreen.main.bounds.width * 202 / 375
    
    typealias HLBannerCallback = (_ index: Int) -> Void
    
    var clickIndex: HLBannerCallback?
    
    var urls: [String] = []
    private lazy var cycleView: SQAutoScrollView = {
        if urls.count == 0 {
            if let imageURL = Bundle.main.url(forResource: "carouselSampleImage", withExtension: "png") {
                urls = [imageURL.absoluteString, imageURL.absoluteString]
            } else {
                let webUrls = [
                    "https://img2.baidu.com/it/u=2484659533,2504932225&fm=253&fmt=auto&app=120&f=JPEG",
                    "https://img2.baidu.com/it/u=2484659533,2504932225&fm=253&fmt=auto&app=120&f=JPEG",
                    "https://img2.baidu.com/it/u=2484659533,2504932225&fm=253&fmt=auto&app=120&f=JPEG"
                ]
                urls = webUrls
            }
        }
        
        
        let view = SQAutoScrollView(frame: CGRect.init(x: 0, y: 0, width: UIScreen.main.bounds.size.width, height: HomeCarouselView.carouselViewHeight), urls: urls, didItemCallBack: { [weak self] (view, index) in
            //print("view--->\(view), index-->\(index)")
            self?.clickIndex?(index)
        })
        view.pageControl?.currentPageIndicatorTintColor = .white
        view.pageControl?.pageIndicatorTintColor = UIColor.rgba(255, 255, 255, 0.5)
        view.pageControl?.style = .customer
        return view
    }()
    
    private lazy var carouselView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
//        view.layer.cornerRadius = 12
        view.layer.masksToBounds = true
        return view
    }()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupViews()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    public func refreshCarouseDatas(urls: [String]) {
        self.urls = urls
        self.cycleView.imageUrls = urls
    }
    
    func setupViews() {
        backgroundColor = .clear
        addSubview(carouselView)
        carouselView.addSubview(cycleView)

        carouselView.snp.makeConstraints { make in
            make.height.equalTo(HomeCarouselView.carouselViewHeight)
            make.left.right.top.equalToSuperview()
        }
        
        cycleView.snp.makeConstraints { make in
            make.height.equalTo(HomeCarouselView.carouselViewHeight)
            make.top.left.right.equalToSuperview()
        }
    }
}
