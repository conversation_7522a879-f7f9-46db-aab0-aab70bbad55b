//
//  TabBarController.swift
//  MilkTime
//
//  Created by Tank on 2023/6/13.
//

import UIKit
import SnapKit

class TabBarController: UITabBarController {
    
    let unSelectedItemIcons = [
        UIImage(named: "homeTabBar0UnselectedBtn"),
        UII<PERSON>(named: "homeTabBar1Btn"),
        U<PERSON><PERSON>(named: "placeholder"),
        UIImage(named: "homeTabBar3Btn"),
        <PERSON>IImage(named: "homeTabBar4Btn")
    ]
    
    let selectedItemIcons = [
        UIImage(named: "homeTabBar0Btn"),
        UIImage(named: "homeTabBar1SelectedBtn"),
        <PERSON>II<PERSON>(named: "placeholder"),
        <PERSON><PERSON><PERSON>(named: "homeTabBar3SelectedBtn"),
        UIImage(named: "homeTabBar4SelectedBtn")
    ]
    
    let conmonViewModel = CommonViewModel()
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        initMainButton()
        
        self.view.backgroundColor = .white
        let firstViewController = HomeViewController()
        let firstNav = BaseNavigationController(rootViewController: firstViewController)
        
        let secondViewController = HistoryRecordViewController()
        let secondNav = BaseNavigationController(rootViewController: secondViewController)
        
        let thirdViewController = CalendarTableViewController()
        let thirdNav = BaseNavigationController(rootViewController: thirdViewController)
        
        let forthViewController = SampleChartViewController()
        let forthNav = BaseNavigationController(rootViewController: forthViewController)
        
        let fivethViewController = WebViewController(kHomeLiftShopUrlKey)//ChartsViewController()//EditTestResultController()
        fivethViewController.navigationItem.title = "Shop"
        let fivethNav = BaseNavigationController(rootViewController: fivethViewController)
        
        let vcs = [firstNav, secondNav, thirdNav, forthNav, fivethNav]
        let itemTitles = ["Home", "Result", "", "Charts", "Shop"]
        setViewControllers(vcs, animated: false)

        tabBar.tintColor = .mainTextColor
        tabBar.unselectedItemTintColor = .mainTextColor.withAlphaComponent(0.6)//UIColor(red: 110/255, green: 132/255, blue: 132/255, alpha: 1)
        tabBar.isTranslucent = false
        tabBar.backgroundColor = .white
        tabBar.barTintColor = .white
        let font = UIFont(name: "Gilroy", size: 11) ?? UIFont.systemFont(ofSize: 11)

        for i in 0..<vcs.count {
            tabBar.items?[i].image = unSelectedItemIcons[i]
            tabBar.items?[i].title = itemTitles[i]
            tabBar.items?[i].setTitleTextAttributes([NSAttributedString.Key.font: font], for: .normal)
        }
        self.delegate = self
        tabBar(self.tabBar, didSelect: firstNav.tabBarItem)
        
        
        self.conmonViewModel.getSystemDictionary(type: "Download_App_QR_Code") { result in
            
            for item in result {
                if item.remark == "IOS" {
                    UserDefaults.standard.downloadUrl = item.dictValue ?? ""
                }
            }
        } failure: { error in
            
        }
        
    }
    
    override func tabBar(_ tabBar: UITabBar, didSelect item: UITabBarItem) {
        guard let tabItems = tabBar.items else { return }
        
        for (index, tabBarItem) in tabItems.enumerated() {
            if tabBarItem != item {
                tabBarItem.image = unSelectedItemIcons[index]
            } else {
                item.image = selectedItemIcons[index]
            }
        }
    }
    
    private func initMainButton() {
        let centerView = UIView()
        centerView.backgroundColor = .clear
        tabBar.addSubview(centerView)
        centerView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            //make.bottom.equalTo(tabBar.snp.bottom).inset(UIApplication.getSafeAreaInsets().bottom - 3)
            make.top.equalTo(tabBar.snp.top).offset(-8)
            make.width.height.equalTo(68)
        }
        
        let centerViewBg = UIImageView(image: UIImage(named: "homeTabBarCenterBtnBg"))
        centerView.addSubview(centerViewBg)
        centerViewBg.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.height.equalTo(68)
        }
        
        let centerButton = UIButton(type: .custom)
        centerButton.addTarget(self, action: #selector(centerButtonDidTap), for: .touchUpInside)
        centerButton.setImage(UIImage(named: "homeTabBarCenterBtn"), for: .normal)
        centerView.addSubview(centerButton)
        centerButton.snp.makeConstraints { make in
            make.width.height.equalTo(40)
            make.center.equalToSuperview()
        }
    }
    
    @objc func centerButtonDidTap() {
        print(#function)
    }
}

extension TabBarController : UITabBarControllerDelegate, CreateAccountPopupViewControllerDelegate {

    func tabBarController(_ tabBarController: UITabBarController, shouldSelect viewController: UIViewController) -> Bool {
        
        if let nav = viewController as? BaseNavigationController, let vc = nav.viewControllers.first, vc.isKind(of: SampleChartViewController.classForCoder()) && UserDefaults.standard.isLoginAsGuestWithoutAccount == true {
            let vc = CreateAccountPopupViewController()
            vc.delegate = self
            vc.modalPresentationStyle = .overFullScreen
            tabBarController.present(vc, animated: true)
            return false
        }
        return true
    }
    
    func didTapCreateAccount() {
        
        if let nav = self.selectedViewController as? BaseNavigationController {
//            DispatchQueue.main.asyncAfter(deadline: DispatchTime.now() + 0.8) {
                let topVC = self.topMostController()
                let signUpVC = SignUpViewController()
                nav.pushViewController(signUpVC, animated: true)
//            }
        }
    }
    
    func didTapClosePopup() {
        
    }
    
}
