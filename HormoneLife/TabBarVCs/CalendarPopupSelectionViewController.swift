//
//  CalendarPopupSelectionViewController.swift
//  HormoneLife
//
//  Created by Tank on 2024/6/22.
//

import UIKit
import SnapKit

protocol CalendarPopupSelectionViewControllerDelegate: AnyObject {
    func didClose()
    func didSave(value: [String])
    func didSave(type: CalendarVCGeneralCell.CalendarVCRowType, tags: [String])
}

extension CalendarPopupSelectionViewControllerDelegate {
    func didClose() {}
    func didSave(value: [String]) {}
    func didSave(type: CalendarVCGeneralCell.CalendarVCRowType, tags: [String]) {}
}

class CalendarPopupSelectionViewController: UIViewController, UITextFieldDelegate {

    private lazy var pickerView: TimePickerView = {
        let pickerView = TimePickerView()
        pickerView.delegate = self
        return pickerView
    }()
    var pickerViewBottomContrain: Constraint?

    weak var delegate: CalendarPopupSelectionViewControllerDelegate?
    var isMultipleChoice: Bool = false

    let bgView: UIView = {
        let v = UIView()
        v.backgroundColor = .white
        v.layer.cornerRadius = 8
        return v
    }()
    
    lazy var closeButon: UIButton = {
        let b = UIButton(type: .custom)
        b.setImage(UIImage(named: "purpleClose"), for: .normal)
        b.addTarget(self, action: #selector(didTapClose), for: .touchUpInside)
        return b
    }()
    
    let titleLabel: UILabel = {
        let l = UILabel()
        l.font = .heavyGilroyFont(14)
        l.textColor = .mainTextColor
        l.textAlignment = .center
        l.numberOfLines = 0
        l.setLineHeight(8)
        return l
    }()
    
    let buttonStackView: UIStackView = {
        let stackview = UIStackView()
        stackview.distribution = .fill
        stackview.axis = .vertical
        stackview.spacing = 16
        return stackview
    }()
    
    lazy var cancleButton: UIButton = {
        let b = UIButton(type: .custom)
        b.setTitle("Cancle", for: .normal)
        b.titleLabel?.font = .regularGilroyFont(16)
        b.backgroundColor = .white
        b.setTitleColor(.mainTextColor, for: .normal)
        b.layer.cornerRadius = 4
        b.layer.borderColor = UIColor.mainTextColor.cgColor
        b.layer.borderWidth = 1
        b.addTarget(self, action: #selector(didTapClose), for: .touchUpInside)
        return b
    }()
    
    lazy var saveButton: UIButton = {
        let b = UIButton(type: .custom)
        b.setTitle("Save", for: .normal)
        b.titleLabel?.font = .regularGilroyFont(16)
        b.backgroundColor = .mainTextColor
        b.setTitleColor(.white, for: .normal)
        b.layer.cornerRadius = 4
        b.addTarget(self, action: #selector(didPopTapSave), for: .touchUpInside)
        return b
    }()
    
    lazy var otherField: UITextField = {
       let field = UITextField()
        field.font = .regularGilroyFont(14)
        field.textAlignment = .center
        field.contentMode = .scaleAspectFill
        field.tag = 100
        field.delegate = self
        field.backgroundColor = .clear
        field.textColor = .white
        return field
    }()
    
    lazy var segmentControl: UISegmentedControl = {
        let s = UISegmentedControl(items: ["˚F", "˚C"])
        s.backgroundColor = .themeColor
        s.selectedSegmentTintColor = .mainTextColor
        s.setTitleTextAttributes([NSAttributedString.Key.foregroundColor: UIColor.white], for: .selected)
        s.addTarget(self, action: #selector(segmentValueChanged(_:)), for: .valueChanged)
        s.selectedSegmentIndex = 0
        return s
    }()
    
    var rowType: CalendarVCGeneralCell.CalendarVCRowType = .begin
    var buttons: [UIButton] = []
    var buttonsTitle: [String] = []
    var popupTitle: String = ""
    var temperature: String = ""
    
    init(rowType: CalendarVCGeneralCell.CalendarVCRowType, delegate: CalendarPopupSelectionViewControllerDelegate? = nil, isMultipleChoice: Bool = false) {
        super.init(nibName: nil, bundle: nil)
        self.rowType = rowType
        self.buttonsTitle = rowType.popupViewButtonTitles
        self.popupTitle = rowType.popupViewTitle
        self.delegate = delegate
        self.isMultipleChoice = isMultipleChoice
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()

        setupUI()
        titleLabel.text = popupTitle
        view.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(didTapView)))
        
        switch rowType {
        case let .temp(tempValue, unit, markTime):
            temperature = tempValue
            buildFields(buttonsTitle, tempValue: tempValue, tempUnit: unit, markTime: markTime)
        default:
            buildButtons(buttonsTitle)
        }
    }
    
    @objc func segmentValueChanged(_ sender: UISegmentedControl) {
        let selectedIndex = sender.selectedSegmentIndex
        
        if let field = view.viewWithTag(201) as? UITextField,
           let fieldValue = Double(field.text ?? "0"),
           fieldValue > 0 {
            if selectedIndex == 0 { //C -> F
                var transforValue = Int(fieldValue * 1.8 + 32)
                if transforValue > 108 {
                    transforValue = 108
                } else if transforValue < 92 {
                    transforValue = 92
                }
                
                field.text = "\(transforValue)"
                temperature = "\(transforValue)"
            } else { // F -> C
                var transforValue = Int((fieldValue - 32) / 1.8)
                if transforValue > 42 {
                    transforValue = 42
                } else if transforValue < 32 {
                    transforValue = 32
                }
                
                field.text = "\(transforValue)"
                temperature = "\(transforValue)"
            }
        }
    }
    
    func buildFields(_ titles: [String], tempValue: String = "", tempUnit: Int = 1, markTime: String = "") {
        for (index, title) in titles.enumerated() {
            let bg = UIButton(frame: CGRect(x: 0, y: 0, width: UIScreen.main.bounds.width - 48, height: 36))
            bg.backgroundColor = .clear
            
            let tags = title.components(separatedBy: ",")
            
            let field = UITextField()
            field.setAttributedPlaceholer(tags.first)
            field.font = .regularGilroyFont(14)
            field.textAlignment = .center
            field.contentMode = .scaleAspectFill
            field.layer.cornerRadius = 4
            field.layer.borderWidth = 1
            field.layer.borderColor = UIColor.mainTextColor.cgColor
            field.tag = index + 200
            field.textColor = .mainTextColor
            field.keyboardType = .numbersAndPunctuation
            field.delegate = self
            if index == 0 {
//                field.isEnabled = false
                field.text = markTime.isEmpty ? dateFormat(date: Date(), format: "hh:mm a") : markTime
                let btn = UIButton()
                btn.backgroundColor = .clear
                field.addSubview(btn)
                btn.snp.makeConstraints { make in
                    make.left.right.top.bottom.equalToSuperview()
                }
                btn.addTarget(self, action: #selector(selectTimeAction), for: .touchUpInside)
            }
            
            if index == 1,
               !tempValue.isEmpty {
                field.text = tempValue
            }
            
            bg.addSubview(field)
            field.snp.makeConstraints { make in
                make.edges.equalToSuperview()
            }
            
            if index == 1 {
                bg.addSubview(segmentControl)
                segmentControl.snp.makeConstraints { make in
                    make.width.equalTo(100)
                    make.top.right.equalToSuperview().inset(1)
                    make.bottom.equalToSuperview().inset(2)
                }
                segmentControl.selectedSegmentIndex = tempUnit == 2 ? 0 : 1
            }
            
            buttonStackView.addArrangedSubview(bg)
            
            self.view.addSubview(pickerView)
            pickerView.snp.makeConstraints { make in
                make.left.right.equalToSuperview()
                make.height.equalTo(320)
                pickerViewBottomContrain = make.bottom.equalToSuperview().inset(-320).constraint
            }
        }
    }
    
    @objc func selectTimeAction() {
        
        UIView.animate(withDuration: 0.15) {
            self.pickerViewBottomContrain?.update(inset: 0)
            self.view.layoutIfNeeded()
        }
 
        
    }
    
    func buildButtons(_ titles: [String]) {
        for (index, title) in titles.enumerated() {
            
            let tags = title.components(separatedBy: ",")
            
            let button = UIButton(type: .custom)
            button.setTitle(tags.first, for: .normal)
            button.titleLabel?.font = .regularGilroyFont(14)
            button.setTitleColor(.white, for: .selected)
            button.setTitleColor(.mainTextColor, for: .normal)
            button.layer.cornerRadius = 4
            button.layer.borderWidth = 1
            button.layer.borderColor = UIColor.mainTextColor.cgColor
            button.frame = CGRect(x: 0, y: 0, width: UIScreen.main.bounds.width - 48, height: 36)
            button.tag = index
             
            switch rowType {
            case .sex(let current):
                button.isSelected = current == tags.first
            case .flow(let current):
                button.isSelected = current == tags.first
            case .cervicalMucus(let current):
                button.isSelected = current == tags.first
            default:
                button.isSelected = index == 0
            }
            
            button.backgroundColor = button.isSelected ? .mainTextColor : .white
            button.addTarget(self, action: #selector(didSelectButton), for: .touchUpInside)
            
            buttons.append(button)
            
            if rowType == .ovulationTracking && title.hasPrefix("Other") {
                button.setTitle("", for: .normal)
                otherField.setAttributedPlaceholer(tags.first)
                button.addSubview(otherField)
                otherField.snp.makeConstraints { make in
                    make.edges.equalToSuperview()
                }
            }
            
            buttonStackView.addArrangedSubview(button)
        }
    }
    
    func textField(_ textField: UITextField, shouldChangeCharactersIn range: NSRange, replacementString string: String) -> Bool {
        
        guard let button = otherField.superview as? UIButton else { return true }
        let text = textField.text ?? ""
        let newText = (text as NSString).replacingCharacters(in: range, with: string)
        
        guard newText.count > 0 else {
            button.isSelected = false
            button.backgroundColor = .white
            otherField.textColor = .mainTextColor
            return true
        }
        otherField.text = newText
        button.isSelected = true
        button.backgroundColor = .mainTextColor
        otherField.textColor = .white
        
        buttons.forEach {
            if $0.tag == 5 {
                $0.isSelected = !button.isSelected
                $0.backgroundColor = $0.isSelected ? .mainTextColor : .white
            }
        }
        
        return false
    }
    
    func textFieldDidEndEditing(_ textField: UITextField) {
        if textField.tag == 201 {
            if segmentControl.selectedSegmentIndex == 1 {
                if (Int(textField.text ?? "0") ?? 40) < 32 {
                    showToachMessage(message: "Logging is only permitted within the temperature range of 32-42°C.")
                    textField.text = "32"
                    temperature = textField.text ?? ""
                } else if (Int(textField.text ?? "0") ?? 40) > 42 {
                    showToachMessage(message: "Logging is only permitted within the temperature range of 32-42°C.")
                    textField.text = "42"
                    temperature = textField.text ?? ""
                } else {
                    temperature = textField.text ?? ""
                }
            } else {
                if (Int(textField.text ?? "0") ?? 92) < 92 {
                    showToachMessage(message: "Logging is only permitted within the temperature range of 92-108°F.")
                    textField.text = "92"
                    temperature = textField.text ?? ""
                } else if (Int(textField.text ?? "0") ?? 92) > 108 {
                    showToachMessage(message: "Logging is only permitted within the temperature range of 92-108°F.")
                    textField.text = "108"
                    temperature = textField.text ?? ""
                } else {
                    temperature = textField.text ?? ""
                }
            }
        }
    }
    
    @objc func didSelectButton(sender: UIButton) {
        otherField.resignFirstResponder()
        
        guard isMultipleChoice else {
            buttons.forEach {
                $0.isSelected = $0.tag == sender.tag
                $0.backgroundColor = $0.isSelected ? .mainTextColor : .white
            }
            print(sender.tag)
            return
        }
        
        
        
        sender.isSelected = !sender.isSelected
        sender.backgroundColor = sender.isSelected ? .mainTextColor : .white
        
        //sender.tag == 5 ==> none of the above
        if rowType == .ovulationTracking {
            buttons.forEach {
                if $0.tag == 5 {
                    $0.isSelected = false
                    $0.backgroundColor = $0.isSelected ? .mainTextColor : .white
                } else if $0.tag == 4 {
                    otherField.textColor = $0.isSelected ? .white : .mainTextColor
                }
            }
        }
        
        
        
        if rowType == .ovulationTracking && (sender.title(for: .normal) ?? "").contains("None of") {
            buttons.forEach {
                $0.isSelected = false
                $0.backgroundColor = $0.isSelected ? .mainTextColor : .white
            }
            otherField.textColor = .mainTextColor
            
            sender.isSelected = true
            sender.backgroundColor = sender.isSelected ? .mainTextColor : .white
        }
    }
    
    func setupUI() {
        view.backgroundColor = .rgba(0, 0, 0, 0.1)
        view.addSubview(bgView)
        [closeButon, titleLabel, buttonStackView, saveButton].forEach {
            bgView.addSubview($0)
        }
        
        bgView.snp.makeConstraints { make in
            make.leading.trailing.bottom.equalToSuperview()
            make.height.greaterThanOrEqualTo(200)
        }
        
        closeButon.snp.makeConstraints { make in
            make.height.width.equalTo(16)
            make.top.equalToSuperview().inset(12)
            make.trailing.equalToSuperview().inset(24)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(30)
            make.leading.trailing.equalToSuperview().inset(24)
        }
        
        buttonStackView.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview().inset(24)
            make.top.equalTo(titleLabel.snp.bottom).offset(20)
        }
        
        if rowType == .ovulationTracking {
            bgView.addSubview(cancleButton)
            closeButon.isHidden = true
            
            cancleButton.snp.makeConstraints { make in
                make.top.equalTo(buttonStackView.snp.bottom).offset(40)
                make.left.equalToSuperview().inset(24)
                make.height.equalTo(48)
                make.bottom.equalToSuperview().inset(30)
                make.right.equalTo(bgView.snp.centerX).offset(-10)
            }
            
            saveButton.snp.makeConstraints { make in
                make.right.equalToSuperview().inset(24)
                make.top.bottom.height.equalTo(cancleButton)
                make.left.equalTo(cancleButton.snp.right).offset(20)
            }
            
        } else {
            saveButton.snp.makeConstraints { make in
                make.leading.trailing.equalToSuperview().inset(24)
                make.top.equalTo(buttonStackView.snp.bottom).offset(40)
                make.bottom.equalToSuperview().inset(30)
                make.height.equalTo(48)
            }
        }
    }
    
    @objc func didTapView() {
        switch rowType {
        case .ovulationTracking, .temp(_,_,_):
            view.endEditing(true)
        default:
            didTapClose()
        }
    }
    
    @objc func didTapClose() {
        delegate?.didClose()
        dismiss(animated: true)
    }
    
    @objc func didPopTapSave() {
        switch rowType {
        case let .temp(_,_,markTime):
            
            let view = self.view.viewWithTag(200) as? UITextField
            delegate?.didSave(type: .temp(tempValue: temperature, tempUnit: segmentControl.selectedSegmentIndex == 0 ? 2 : 1, markTime: view?.text ?? ""), tags: [])
            didTapClose()
        default:
            let tags = buttons.filter { $0.isSelected }.map { "\($0.tag)" }
            var result = [String]()
            
            if let other = otherField.text, other.count > 0 {
                
                for (_, item) in tags.enumerated() {
                    if buttonsTitle[Int(item)!].contains("Other") || buttonsTitle[Int(item)!].contains("other") {
                        result.append("\(other),other")
                    } else {
                        result.append(item)
                    }
                }
            } else {
                result = tags
            }
            delegate?.didSave(value: result)
            delegate?.didSave(type: self.rowType, tags: result)
            didTapClose()
        }
    }
}

extension CalendarPopupSelectionViewController: TimePickerViewDelegate {
    func didTapCancel() {
        
        UIView.animate(withDuration: 0.15) {
            self.pickerViewBottomContrain?.update(inset: -320)
            self.view.layoutIfNeeded()
        }
    }
    
    func didTapSave(_ time: String) {
        
        let view = self.view.viewWithTag(200) as? UITextField
        view?.text = time
        
        UIView.animate(withDuration: 0.15) {
            self.pickerViewBottomContrain?.update(inset: -320)
            self.view.layoutIfNeeded()
        }

    }
    
    func didSelect(_ time: String) {
        let view = self.view.viewWithTag(200) as? UITextField
        view?.text = time
    }
}
