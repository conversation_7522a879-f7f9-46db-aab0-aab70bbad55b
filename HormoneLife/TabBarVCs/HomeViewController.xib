<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="23727" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="23721"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="Gilroy Medium.otf">
            <string><PERSON><PERSON>-Medium</string>
        </array>
        <array key="gilroy regular.otf">
            <string><PERSON>roy-Regular</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="HomeViewController" customModule="HormoneLife" customModuleProvider="target">
            <connections>
                <outlet property="captureResultsView" destination="B0d-1p-GiV" id="VeE-xc-noZ"/>
                <outlet property="carouselContainerView" destination="Mna-kZ-deD" id="5Dv-1P-oLN"/>
                <outlet property="chanceOfConceptionView" destination="bq3-hq-0gg" id="Rnm-uI-ZRY"/>
                <outlet property="helpImageView" destination="M7Z-wG-nBd" id="VtE-D4-z79"/>
                <outlet property="homeContent" destination="C1e-i5-XdW" id="l2e-FA-fPt"/>
                <outlet property="homeRate" destination="9Nr-tP-qsc" id="10x-Ur-UT2"/>
                <outlet property="homeRateView" destination="2aK-Ho-YRc" id="hxc-dD-7Ta"/>
                <outlet property="homeTitle" destination="btF-ci-YxZ" id="2gY-fT-Aw5"/>
                <outlet property="view" destination="i5M-Pr-FkT" id="sfx-zR-JGt"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="i5M-Pr-FkT">
            <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" contentInsetAdjustmentBehavior="never" translatesAutoresizingMaskIntoConstraints="NO" id="YdT-Ul-VuO">
                    <rect key="frame" x="0.0" y="118" width="393" height="666"/>
                    <subviews>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Paa-mB-VOw">
                            <rect key="frame" x="0.0" y="0.0" width="393" height="666"/>
                            <subviews>
                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Mna-kZ-deD">
                                    <rect key="frame" x="0.0" y="0.0" width="393" height="280"/>
                                    <subviews>
                                        <imageView hidden="YES" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="homeScrollImage1" translatesAutoresizingMaskIntoConstraints="NO" id="olp-QJ-kXt">
                                            <rect key="frame" x="0.0" y="0.0" width="393" height="280"/>
                                            <userDefinedRuntimeAttributes>
                                                <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                    <integer key="value" value="12"/>
                                                </userDefinedRuntimeAttribute>
                                            </userDefinedRuntimeAttributes>
                                        </imageView>
                                        <imageView hidden="YES" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="hormoneLife" translatesAutoresizingMaskIntoConstraints="NO" id="sNU-Ms-RxP">
                                            <rect key="frame" x="48" y="100" width="297" height="32"/>
                                            <constraints>
                                                <constraint firstAttribute="height" constant="32" id="6WG-D3-xgN"/>
                                            </constraints>
                                        </imageView>
                                    </subviews>
                                    <color key="backgroundColor" red="0.94169229269999999" green="0.92880314590000002" blue="0.9607291818" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                    <constraints>
                                        <constraint firstAttribute="trailing" secondItem="sNU-Ms-RxP" secondAttribute="trailing" constant="48" id="LCd-Uw-T3r"/>
                                        <constraint firstItem="sNU-Ms-RxP" firstAttribute="top" secondItem="Mna-kZ-deD" secondAttribute="top" constant="100" id="Onq-dc-8nL"/>
                                        <constraint firstAttribute="trailing" secondItem="olp-QJ-kXt" secondAttribute="trailing" id="Q3l-wg-8gm"/>
                                        <constraint firstItem="olp-QJ-kXt" firstAttribute="top" secondItem="Mna-kZ-deD" secondAttribute="top" id="TkU-VJ-z2U"/>
                                        <constraint firstAttribute="height" constant="280" id="u1n-0A-4ha"/>
                                        <constraint firstAttribute="bottom" secondItem="olp-QJ-kXt" secondAttribute="bottom" id="uBo-Km-ReR"/>
                                        <constraint firstItem="olp-QJ-kXt" firstAttribute="leading" secondItem="Mna-kZ-deD" secondAttribute="leading" id="uKj-Ug-nKf"/>
                                        <constraint firstItem="sNU-Ms-RxP" firstAttribute="leading" secondItem="Mna-kZ-deD" secondAttribute="leading" constant="48" id="xvA-Nh-IRs"/>
                                    </constraints>
                                </view>
                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="JI5-fu-8gt">
                                    <rect key="frame" x="0.0" y="240" width="393" height="426"/>
                                    <subviews>
                                        <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="20" translatesAutoresizingMaskIntoConstraints="NO" id="bsm-KX-Z5w">
                                            <rect key="frame" x="24" y="50" width="345" height="343"/>
                                            <subviews>
                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="wUx-cd-f7F">
                                                    <rect key="frame" x="0.0" y="0.0" width="345" height="225"/>
                                                    <subviews>
                                                        <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="13" translatesAutoresizingMaskIntoConstraints="NO" id="7gj-jn-WXm">
                                                            <rect key="frame" x="20" y="10" width="305" height="192"/>
                                                            <subviews>
                                                                <stackView opaque="NO" contentMode="scaleToFill" spacing="18" translatesAutoresizingMaskIntoConstraints="NO" id="Bhj-uM-zw9">
                                                                    <rect key="frame" x="0.0" y="0.0" width="305" height="115"/>
                                                                    <subviews>
                                                                        <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="3" translatesAutoresizingMaskIntoConstraints="NO" id="t9y-o4-NM3">
                                                                            <rect key="frame" x="0.0" y="0.0" width="195" height="115"/>
                                                                            <subviews>
                                                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="btF-ci-YxZ">
                                                                                    <rect key="frame" x="0.0" y="0.0" width="195" height="52"/>
                                                                                    <fontDescription key="fontDescription" name="Gilroy-Medium" family="Gilroy" pointSize="18"/>
                                                                                    <color key="textColor" red="0.25882357360000002" green="0.25882357360000002" blue="0.25882357360000002" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                                    <nil key="highlightedColor"/>
                                                                                </label>
                                                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="KxF-xd-mH7">
                                                                                    <rect key="frame" x="0.0" y="55" width="195" height="60"/>
                                                                                    <subviews>
                                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="C1e-i5-XdW">
                                                                                            <rect key="frame" x="0.0" y="0.0" width="195" height="60"/>
                                                                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                            <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="14"/>
                                                                                            <color key="textColor" red="0.50876241919999998" green="0.4311534762" blue="0.6075879931" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                                            <nil key="highlightedColor"/>
                                                                                        </label>
                                                                                    </subviews>
                                                                                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                    <constraints>
                                                                                        <constraint firstAttribute="height" constant="60" id="Dli-aw-kNC"/>
                                                                                        <constraint firstItem="C1e-i5-XdW" firstAttribute="top" secondItem="KxF-xd-mH7" secondAttribute="top" id="F8P-Zp-MSr"/>
                                                                                        <constraint firstItem="C1e-i5-XdW" firstAttribute="leading" secondItem="KxF-xd-mH7" secondAttribute="leading" id="SvM-7J-ant"/>
                                                                                        <constraint firstAttribute="trailing" secondItem="C1e-i5-XdW" secondAttribute="trailing" id="rrR-8O-Pb7"/>
                                                                                        <constraint firstAttribute="bottom" secondItem="C1e-i5-XdW" secondAttribute="bottom" id="u5B-FB-z2W"/>
                                                                                    </constraints>
                                                                                </view>
                                                                            </subviews>
                                                                        </stackView>
                                                                        <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="6" translatesAutoresizingMaskIntoConstraints="NO" id="GdM-ZE-ESc">
                                                                            <rect key="frame" x="213" y="0.0" width="92" height="115"/>
                                                                            <subviews>
                                                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="2aK-Ho-YRc">
                                                                                    <rect key="frame" x="0.0" y="0.0" width="92" height="77"/>
                                                                                    <subviews>
                                                                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="fJ0-cJ-o3K">
                                                                                            <rect key="frame" x="-3" y="-21" width="98" height="98"/>
                                                                                            <subviews>
                                                                                                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="percentCircle1" translatesAutoresizingMaskIntoConstraints="NO" id="AfN-Ua-e3G">
                                                                                                    <rect key="frame" x="0.0" y="0.0" width="98" height="98"/>
                                                                                                </imageView>
                                                                                                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="percentCircle2" translatesAutoresizingMaskIntoConstraints="NO" id="94m-Fb-6Kk">
                                                                                                    <rect key="frame" x="14" y="14" width="70" height="70"/>
                                                                                                    <constraints>
                                                                                                        <constraint firstAttribute="height" constant="70" id="LFn-VU-ZWS"/>
                                                                                                        <constraint firstAttribute="width" constant="70" id="Nc4-3v-7iV"/>
                                                                                                    </constraints>
                                                                                                </imageView>
                                                                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="98" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="9Nr-tP-qsc">
                                                                                                    <rect key="frame" x="28.666666666666686" y="34" width="33" height="30"/>
                                                                                                    <constraints>
                                                                                                        <constraint firstAttribute="height" constant="30" id="FbG-wE-dQ3"/>
                                                                                                    </constraints>
                                                                                                    <fontDescription key="fontDescription" name="Gilroy-Medium" family="Gilroy" pointSize="30"/>
                                                                                                    <color key="textColor" red="0.1925059855" green="0.057953603569999998" blue="0.3526558876" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                                                    <nil key="highlightedColor"/>
                                                                                                </label>
                                                                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="%" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="36h-Sa-gOs">
                                                                                                    <rect key="frame" x="61.666666666666693" y="48.333333333333314" width="7.3333333333333357" height="11.666666666666664"/>
                                                                                                    <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="10"/>
                                                                                                    <color key="textColor" red="0.50876241919999998" green="0.4311534762" blue="0.6075879931" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                                                    <nil key="highlightedColor"/>
                                                                                                </label>
                                                                                            </subviews>
                                                                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                            <constraints>
                                                                                                <constraint firstAttribute="trailing" secondItem="AfN-Ua-e3G" secondAttribute="trailing" id="2t3-wP-inK"/>
                                                                                                <constraint firstItem="94m-Fb-6Kk" firstAttribute="centerX" secondItem="AfN-Ua-e3G" secondAttribute="centerX" id="CoX-2u-bUx"/>
                                                                                                <constraint firstItem="AfN-Ua-e3G" firstAttribute="leading" secondItem="fJ0-cJ-o3K" secondAttribute="leading" id="L99-hI-Q3c"/>
                                                                                                <constraint firstAttribute="bottom" secondItem="AfN-Ua-e3G" secondAttribute="bottom" id="OvL-OT-pqn"/>
                                                                                                <constraint firstItem="36h-Sa-gOs" firstAttribute="bottom" secondItem="9Nr-tP-qsc" secondAttribute="bottom" constant="-4" id="PuW-mm-pdm"/>
                                                                                                <constraint firstItem="36h-Sa-gOs" firstAttribute="leading" secondItem="9Nr-tP-qsc" secondAttribute="trailing" id="RYL-KS-8Xu"/>
                                                                                                <constraint firstItem="94m-Fb-6Kk" firstAttribute="centerY" secondItem="AfN-Ua-e3G" secondAttribute="centerY" id="SDk-3i-fwC"/>
                                                                                                <constraint firstAttribute="height" constant="98" id="VHz-R0-onP"/>
                                                                                                <constraint firstItem="9Nr-tP-qsc" firstAttribute="centerY" secondItem="fJ0-cJ-o3K" secondAttribute="centerY" id="YUe-tU-gnp"/>
                                                                                                <constraint firstAttribute="width" constant="98" id="ovW-Dy-DT3"/>
                                                                                                <constraint firstItem="AfN-Ua-e3G" firstAttribute="top" secondItem="fJ0-cJ-o3K" secondAttribute="top" id="sN7-r0-g8m"/>
                                                                                                <constraint firstItem="9Nr-tP-qsc" firstAttribute="centerX" secondItem="fJ0-cJ-o3K" secondAttribute="centerX" constant="-4" id="xWs-K5-ePC"/>
                                                                                            </constraints>
                                                                                        </view>
                                                                                    </subviews>
                                                                                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                    <constraints>
                                                                                        <constraint firstItem="fJ0-cJ-o3K" firstAttribute="centerX" secondItem="2aK-Ho-YRc" secondAttribute="centerX" id="Yy4-si-ZQ1"/>
                                                                                        <constraint firstAttribute="bottom" secondItem="fJ0-cJ-o3K" secondAttribute="bottom" id="mxI-93-GSr"/>
                                                                                    </constraints>
                                                                                </view>
                                                                                <stackView opaque="NO" contentMode="scaleToFill" spacing="4" translatesAutoresizingMaskIntoConstraints="NO" id="bq3-hq-0gg">
                                                                                    <rect key="frame" x="0.0" y="83" width="92" height="32"/>
                                                                                    <subviews>
                                                                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="rkx-0F-WuS">
                                                                                            <rect key="frame" x="0.0" y="0.0" width="12" height="32"/>
                                                                                            <subviews>
                                                                                                <imageView clipsSubviews="YES" contentMode="top" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="quoteMark" translatesAutoresizingMaskIntoConstraints="NO" id="M7Z-wG-nBd">
                                                                                                    <rect key="frame" x="0.0" y="3" width="12" height="12"/>
                                                                                                    <constraints>
                                                                                                        <constraint firstAttribute="height" constant="12" id="w1j-tb-RHz"/>
                                                                                                    </constraints>
                                                                                                </imageView>
                                                                                            </subviews>
                                                                                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                            <constraints>
                                                                                                <constraint firstAttribute="width" constant="12" id="4eX-tV-6xt"/>
                                                                                                <constraint firstItem="M7Z-wG-nBd" firstAttribute="top" secondItem="rkx-0F-WuS" secondAttribute="top" constant="3" id="IH2-GV-F4p"/>
                                                                                                <constraint firstAttribute="trailing" secondItem="M7Z-wG-nBd" secondAttribute="trailing" id="w20-hJ-O3T"/>
                                                                                                <constraint firstItem="M7Z-wG-nBd" firstAttribute="leading" secondItem="rkx-0F-WuS" secondAttribute="leading" id="yDm-hW-PHF"/>
                                                                                            </constraints>
                                                                                        </view>
                                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="TopLeft" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Chance of Conception" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="64i-Hb-nkh">
                                                                                            <rect key="frame" x="16" y="0.0" width="76" height="32"/>
                                                                                            <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                                                                            <color key="textColor" red="0.50876241919999998" green="0.4311534762" blue="0.6075879931" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                                            <nil key="highlightedColor"/>
                                                                                        </label>
                                                                                    </subviews>
                                                                                    <constraints>
                                                                                        <constraint firstAttribute="height" constant="32" id="zWQ-IB-PoA"/>
                                                                                    </constraints>
                                                                                </stackView>
                                                                            </subviews>
                                                                            <constraints>
                                                                                <constraint firstAttribute="width" constant="92" id="S0y-BC-Yl3"/>
                                                                            </constraints>
                                                                        </stackView>
                                                                    </subviews>
                                                                </stackView>
                                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="B0d-1p-GiV">
                                                                    <rect key="frame" x="0.0" y="128" width="305" height="64"/>
                                                                    <subviews>
                                                                        <stackView opaque="NO" contentMode="scaleToFill" spacing="28" translatesAutoresizingMaskIntoConstraints="NO" id="Dyy-Wy-IFB">
                                                                            <rect key="frame" x="64" y="6" width="221" height="52"/>
                                                                            <subviews>
                                                                                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="camera" translatesAutoresizingMaskIntoConstraints="NO" id="cVl-Ui-mS1">
                                                                                    <rect key="frame" x="0.0" y="0.0" width="52" height="52"/>
                                                                                    <constraints>
                                                                                        <constraint firstAttribute="width" constant="52" id="He7-Ck-DPb"/>
                                                                                    </constraints>
                                                                                </imageView>
                                                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Capture Results" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="ije-6C-iiL">
                                                                                    <rect key="frame" x="80" y="0.0" width="141" height="52"/>
                                                                                    <fontDescription key="fontDescription" type="system" pointSize="16"/>
                                                                                    <color key="textColor" red="0.1925059855" green="0.057953603569999998" blue="0.3526558876" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                                    <nil key="highlightedColor"/>
                                                                                </label>
                                                                            </subviews>
                                                                        </stackView>
                                                                    </subviews>
                                                                    <color key="backgroundColor" red="0.94181483980000003" green="0.92480486630000003" blue="0.96072250599999998" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                    <constraints>
                                                                        <constraint firstItem="Dyy-Wy-IFB" firstAttribute="top" secondItem="B0d-1p-GiV" secondAttribute="top" constant="6" id="0Q3-yZ-dlh"/>
                                                                        <constraint firstAttribute="trailing" secondItem="Dyy-Wy-IFB" secondAttribute="trailing" constant="20" id="70P-9Z-EMx"/>
                                                                        <constraint firstItem="Dyy-Wy-IFB" firstAttribute="leading" secondItem="B0d-1p-GiV" secondAttribute="leading" constant="64" id="C33-mw-O8i"/>
                                                                        <constraint firstAttribute="height" constant="64" id="OMo-Es-0Zl"/>
                                                                        <constraint firstAttribute="bottom" secondItem="Dyy-Wy-IFB" secondAttribute="bottom" constant="6" id="c6A-w6-iov"/>
                                                                    </constraints>
                                                                    <userDefinedRuntimeAttributes>
                                                                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                                            <integer key="value" value="4"/>
                                                                        </userDefinedRuntimeAttribute>
                                                                    </userDefinedRuntimeAttributes>
                                                                </view>
                                                            </subviews>
                                                        </stackView>
                                                    </subviews>
                                                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    <constraints>
                                                        <constraint firstAttribute="height" constant="225" id="2oU-Sj-Cuf"/>
                                                        <constraint firstAttribute="bottom" secondItem="7gj-jn-WXm" secondAttribute="bottom" constant="23" id="3b6-Un-8iu"/>
                                                        <constraint firstItem="7gj-jn-WXm" firstAttribute="top" secondItem="wUx-cd-f7F" secondAttribute="top" constant="10" id="VSh-3K-e8B"/>
                                                        <constraint firstAttribute="trailing" secondItem="7gj-jn-WXm" secondAttribute="trailing" constant="20" id="Z44-HX-xBD"/>
                                                        <constraint firstItem="7gj-jn-WXm" firstAttribute="leading" secondItem="wUx-cd-f7F" secondAttribute="leading" constant="20" id="vOY-gK-c2p"/>
                                                    </constraints>
                                                    <userDefinedRuntimeAttributes>
                                                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                            <integer key="value" value="4"/>
                                                        </userDefinedRuntimeAttribute>
                                                    </userDefinedRuntimeAttributes>
                                                </view>
                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="HQ0-HU-uaV">
                                                    <rect key="frame" x="0.0" y="245" width="345" height="98"/>
                                                    <subviews>
                                                        <stackView opaque="NO" contentMode="scaleToFill" spacing="10" translatesAutoresizingMaskIntoConstraints="NO" id="bbd-l9-yac">
                                                            <rect key="frame" x="30" y="17" width="285" height="64"/>
                                                            <subviews>
                                                                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="homeClockIcon2" translatesAutoresizingMaskIntoConstraints="NO" id="Lx7-Yr-5BA">
                                                                    <rect key="frame" x="0.0" y="0.0" width="64" height="64"/>
                                                                    <constraints>
                                                                        <constraint firstAttribute="width" constant="64" id="Fqk-Yf-MyX"/>
                                                                        <constraint firstAttribute="height" constant="64" id="nSz-43-noq"/>
                                                                    </constraints>
                                                                </imageView>
                                                                <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="1y4-T2-04q">
                                                                    <rect key="frame" x="74" y="0.0" width="211" height="64"/>
                                                                    <subviews>
                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="We value your privacy." textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="dlJ-AD-cqu">
                                                                            <rect key="frame" x="0.0" y="0.0" width="211" height="34"/>
                                                                            <constraints>
                                                                                <constraint firstAttribute="height" constant="34" id="Qe7-up-deD"/>
                                                                            </constraints>
                                                                            <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="16"/>
                                                                            <color key="textColor" red="0.20423525570000001" green="0.075626514850000007" blue="0.36441689729999999" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                            <nil key="highlightedColor"/>
                                                                        </label>
                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Your data is secure and never sold" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="fNr-ej-Wn9">
                                                                            <rect key="frame" x="0.0" y="34" width="211" height="30"/>
                                                                            <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="12"/>
                                                                            <color key="textColor" red="0.20423525570000001" green="0.075626514850000007" blue="0.36441689729999999" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                            <nil key="highlightedColor"/>
                                                                        </label>
                                                                    </subviews>
                                                                </stackView>
                                                            </subviews>
                                                        </stackView>
                                                    </subviews>
                                                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    <constraints>
                                                        <constraint firstAttribute="trailing" secondItem="bbd-l9-yac" secondAttribute="trailing" constant="30" id="1yg-1I-b59"/>
                                                        <constraint firstAttribute="bottom" secondItem="bbd-l9-yac" secondAttribute="bottom" constant="17" id="8pc-hY-cMJ"/>
                                                        <constraint firstAttribute="height" constant="98" id="PKc-Iz-nT6"/>
                                                        <constraint firstItem="bbd-l9-yac" firstAttribute="top" secondItem="HQ0-HU-uaV" secondAttribute="top" constant="17" id="aAm-rn-Bav"/>
                                                        <constraint firstItem="bbd-l9-yac" firstAttribute="leading" secondItem="HQ0-HU-uaV" secondAttribute="leading" constant="30" id="cDA-V7-fzN"/>
                                                    </constraints>
                                                    <userDefinedRuntimeAttributes>
                                                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                            <integer key="value" value="4"/>
                                                        </userDefinedRuntimeAttribute>
                                                    </userDefinedRuntimeAttributes>
                                                </view>
                                            </subviews>
                                        </stackView>
                                    </subviews>
                                    <color key="backgroundColor" red="0.94169229269999999" green="0.92880314590000002" blue="0.9607291818" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                    <constraints>
                                        <constraint firstAttribute="trailing" secondItem="bsm-KX-Z5w" secondAttribute="trailing" constant="24" id="QcR-LO-ne6"/>
                                        <constraint firstItem="bsm-KX-Z5w" firstAttribute="top" secondItem="JI5-fu-8gt" secondAttribute="top" constant="50" id="TUU-0K-pLW"/>
                                        <constraint firstItem="bsm-KX-Z5w" firstAttribute="leading" secondItem="JI5-fu-8gt" secondAttribute="leading" constant="24" id="ke2-1F-ods"/>
                                    </constraints>
                                </view>
                            </subviews>
                            <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                            <constraints>
                                <constraint firstAttribute="bottom" secondItem="JI5-fu-8gt" secondAttribute="bottom" id="0y5-bX-adv"/>
                                <constraint firstAttribute="trailing" secondItem="Mna-kZ-deD" secondAttribute="trailing" id="2Wi-KV-ZI8"/>
                                <constraint firstItem="Mna-kZ-deD" firstAttribute="top" secondItem="Paa-mB-VOw" secondAttribute="top" id="3FX-j8-jRH"/>
                                <constraint firstItem="JI5-fu-8gt" firstAttribute="leading" secondItem="Paa-mB-VOw" secondAttribute="leading" id="ZCv-vb-4l9"/>
                                <constraint firstItem="JI5-fu-8gt" firstAttribute="top" secondItem="Mna-kZ-deD" secondAttribute="bottom" constant="-40" id="dn7-U3-VNx"/>
                                <constraint firstAttribute="trailing" secondItem="JI5-fu-8gt" secondAttribute="trailing" id="fWj-SE-twO"/>
                                <constraint firstItem="Mna-kZ-deD" firstAttribute="leading" secondItem="Paa-mB-VOw" secondAttribute="leading" id="s7e-Oi-Ugk"/>
                            </constraints>
                        </view>
                    </subviews>
                    <color key="backgroundColor" red="0.94169229269999999" green="0.92880314590000002" blue="0.9607291818" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                    <constraints>
                        <constraint firstAttribute="trailing" secondItem="Paa-mB-VOw" secondAttribute="trailing" id="A4b-Fi-n91"/>
                        <constraint firstItem="Paa-mB-VOw" firstAttribute="width" secondItem="YdT-Ul-VuO" secondAttribute="width" id="Bv2-lM-x0W"/>
                        <constraint firstItem="Paa-mB-VOw" firstAttribute="top" secondItem="YdT-Ul-VuO" secondAttribute="top" id="Of8-mx-3Wg"/>
                        <constraint firstItem="Paa-mB-VOw" firstAttribute="centerY" secondItem="YdT-Ul-VuO" secondAttribute="centerY" id="SZ2-Zd-NFq"/>
                        <constraint firstItem="Paa-mB-VOw" firstAttribute="leading" secondItem="YdT-Ul-VuO" secondAttribute="leading" id="gqH-Mf-XSr"/>
                        <constraint firstAttribute="bottom" secondItem="Paa-mB-VOw" secondAttribute="bottom" id="tYF-lS-kNK"/>
                    </constraints>
                </scrollView>
            </subviews>
            <viewLayoutGuide key="safeArea" id="fnl-2z-Ty3"/>
            <color key="backgroundColor" systemColor="systemBackgroundColor"/>
            <constraints>
                <constraint firstItem="YdT-Ul-VuO" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" id="K9E-ve-OsG"/>
                <constraint firstItem="YdT-Ul-VuO" firstAttribute="top" secondItem="fnl-2z-Ty3" secondAttribute="top" id="ScJ-iL-0wR"/>
                <constraint firstItem="YdT-Ul-VuO" firstAttribute="trailing" secondItem="fnl-2z-Ty3" secondAttribute="trailing" id="Xjs-fQ-wxv"/>
                <constraint firstItem="YdT-Ul-VuO" firstAttribute="bottom" secondItem="fnl-2z-Ty3" secondAttribute="bottom" id="cwm-nX-FpY"/>
            </constraints>
            <point key="canvasLocation" x="96.946564885496173" y="-11.267605633802818"/>
        </view>
    </objects>
    <resources>
        <image name="camera" width="51.333332061767578" height="51.333332061767578"/>
        <image name="homeClockIcon2" width="341.33334350585938" height="341.33334350585938"/>
        <image name="homeScrollImage1" width="375" height="200"/>
        <image name="hormoneLife" width="140" height="16"/>
        <image name="percentCircle1" width="108" height="108"/>
        <image name="percentCircle2" width="80" height="80"/>
        <image name="quoteMark" width="12" height="12"/>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
