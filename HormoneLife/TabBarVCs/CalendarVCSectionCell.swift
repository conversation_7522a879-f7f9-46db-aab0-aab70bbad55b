//
//  CalendarVCSectionCell.swift
//  HormoneLife
//
//  Created by Tank on 2024/6/22.
//

import UIKit

protocol CalendarVCCycleDayCellDelegate: AnyObject {
    func didTapUnderLine()
}

class CalendarVCCycleDayCell: UITableViewCell, UITextViewDelegate {
    
    weak var delegate: CalendarVCCycleDayCellDelegate?
    
    let bgView: UIView = {
        let b = UIView()
        b.backgroundColor = .white
        return b
    }()
    
    let bgImageView: UIImageView = {
        let i = UIImageView(image: UIImage(named: "cycleDayBg"))
        i.backgroundColor = .white
        i.contentMode = .center
        i.layer.cornerRadius = 4
        i.layer.masksToBounds = true
        i.isUserInteractionEnabled = true
        return i
    }()
    
    let titleLabel: UILabel = {
        let l = UILabel()
        l.font = .heavyGilroyFont(18)
        l.textColor = .mainTextColor
        return l
    }()
    
    lazy var descTextView: UITextView = {
        let l = UITextView()
        l.backgroundColor = .clear
        l.font = .regularGilroyFont(14)
        l.textColor = .mainTextColor
        l.dataDetectorTypes = [.link]
        l.isSelectable = true
        l.isEditable = false
        l.delegate = self
        l.isScrollEnabled = false
        return l
    }()
    
    var underlineString = "Lh UItra Test Day"
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        selectionStyle = .none
        contentView.backgroundColor = .themeColor
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func setupWithTitle(_ title: String, description: String, delegate: CalendarVCCycleDayCellDelegate?) {
        titleLabel.text = title
        descTextView.text = description
        //descTextView.underlineWords(words: [underlineString])
        self.delegate = delegate
    }
    
    func textView(_ textView: UITextView, shouldInteractWith URL: URL, in characterRange: NSRange, interaction: UITextItemInteraction) -> Bool {
        
        let range = (textView.text as NSString).range(of: underlineString)
        if characterRange == range {
            delegate?.didTapUnderLine()
        }
        
        return false
    }
    
    func setupUI() {
        contentView.addSubview(bgView)
        contentView.addSubview(bgImageView)
        bgImageView.addSubview(titleLabel)
        bgImageView.addSubview(descTextView)
        
        bgView.snp.makeConstraints { make in
            make.left.top.right.equalToSuperview()
            make.bottom.equalToSuperview().inset(4)
        }
        
        bgImageView.snp.makeConstraints { make in
            make.top.bottom.equalToSuperview().inset(16)
            make.leading.trailing.equalToSuperview().inset(24)
            make.height.equalTo(88)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(16)
            make.leading.equalToSuperview().inset(20)
        }
        
        descTextView.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview().inset(18)
            make.height.equalTo(30)
            make.bottom.equalToSuperview().inset(12)
        }
    }
}

class CalendarVCSectionCell: UITableViewCell {
    let titleLabel: UILabel = {
        let l = UILabel()
        l.font = .boldGilroyFont(14)
        l.textColor = .mainTextColor
        return l
    }()
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        selectionStyle = .none
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func setupUI() {
        contentView.backgroundColor = .white
        contentView.addSubview(titleLabel)
        
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(16)
            make.bottom.equalToSuperview().inset(8)
            make.height.equalTo(20)
            make.leading.equalToSuperview().inset(24)
        }
    }
}

// MARK: BaseCell

protocol CalendarVCGeneralCellDelegate: AnyObject {
    func didTapAdd(_ rowType: CalendarVCGeneralCell.CalendarVCRowType)
    func didTapArow(_ rowType: CalendarVCGeneralCell.CalendarVCRowType)
    func didTapSwitch(_ rowType: CalendarVCGeneralCell.CalendarVCRowType, isOn: Bool, cell: UITableViewCell)
    func didTapDescLabel(_ rowType: CalendarVCGeneralCell.CalendarVCRowType)
}

extension CalendarVCGeneralCellDelegate {
    func didTapAdd(_ rowType: CalendarVCGeneralCell.CalendarVCRowType) {}
    func didTapArow(_ rowType: CalendarVCGeneralCell.CalendarVCRowType) {}
    func didTapSwitch(_ rowType: CalendarVCGeneralCell.CalendarVCRowType, isOn: Bool, cell: UITableViewCell) {}
    func didTapDescLabel(_ rowType: CalendarVCGeneralCell.CalendarVCRowType) {}
}

class CalendarVCGeneralCell: UITableViewCell {
    
    enum CalendarVCRowType: Equatable {
        case begin
        case flow(current: String)
        case ovulationData
        case ovulationTest
        case pdg
        case temp(tempValue: String, tempUnit: Int, markTime: String)
        case cervicalMucus(current: String)
        case sex(current: String)
        case notes
        case ovulationTracking
        
        var popupViewTitle: String {
            switch self {
            case .cervicalMucus: return "Log Cervical Mucus"
            case .flow:
                return "Log Flow Level"
            case .temp:
                return "Body Temperature"
            case .ovulationTracking:
                return "Which of the following methods have you used to track your ovulation? (Multiple choice)"
            case .sex:
                return "Sex Record"
            default:
                return ""
            }
        }
        
        var popupViewButtonTitles: [String] {
            switch self {
            case .cervicalMucus:
                return ["None", "Dry", "Sticky", "Creamy", "Egg white"]
            case .flow:
                return ["Light", "Medium", "Heavy"]
            case .temp:
                return ["Test time", "Temperature"]
            case .ovulationTracking:
                return ["cervical mucus method", "cervical mucus method", "basal body temperature", "Cycle tracking app", "Other: Write down your own method here", "None of the above"]
            case .sex:
                return ["Unprotected", "Protected", "No sex"]
            default:
                return []
            }
        }
        
        var indexOfItems: Int {
            switch self {
            case .sex(let current):
                return popupViewButtonTitles.firstIndex(of: current) ?? 0
            default:
                return 0
            }
        }
        
        var pageType: String {
            switch self {
            case .ovulationTest:
                return "LH Ultra"
            case .pdg:
                return "PdG"
            default:
                return ""
            }
        }
    }
    
    let titleLabel: UILabel = {
        let l = UILabel()
        l.font = .regularGilroyFont(14)
        l.textColor = .mainTextColor
        return l
    }()
    
    lazy var descLabel: UILabel = {
        let l = UILabel()
        l.isUserInteractionEnabled = true
        l.font = .regularGilroyFont(12)
        l.textColor = .mainTextColor
        l.textAlignment = .right
        l.isHidden = true
        l.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(didTapDescLabel)))
        return l
    }()
    
    lazy var tempLabel: UILabel = {
        let l = UILabel()
        l.isUserInteractionEnabled = true
        l.font = .regularGilroyFont(12)
        l.textColor = .mainTextColor
        l.textAlignment = .right
        l.isHidden = true
        l.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(didTapTempLabel)))
        return l
    }()
    
    lazy var addButon: UIButton = {
        let b = UIButton(type: .custom)
        b.addTarget(self, action: #selector(didTapAdd), for: .touchUpInside)
        return b
    }()
    
    lazy var arowButon: UIButton = {
        let b = UIButton(type: .custom)
        b.isHidden = true
        b.addTarget(self, action: #selector(didTapArow), for: .touchUpInside)
        return b
    }()
    
    lazy var switchButton: UISwitch = {
        let s = UISwitch()
        s.isHidden = true
        s.onTintColor = .labelBackColor
        s.addTarget(self, action: #selector(didTapSwitch), for: .valueChanged)
        return s
    }()
    
    let lineView: UIView = {
        let l = UIView()
        l.backgroundColor = .themeColor
        return l
    }()
    
    weak var delegate: CalendarVCGeneralCellDelegate?
    var rowType: CalendarVCRowType = .begin
        
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        selectionStyle = .none
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    @objc func didTapTempLabel() {
        delegate?.didTapArow(rowType)
    }
    
    @objc func didTapDescLabel() {
        delegate?.didTapDescLabel(rowType)
    }
    
    @objc func didTapAdd(sender: UIButton) {
//        print("add: ", rowType)
        delegate?.didTapAdd(rowType)
    }
    
    @objc func didTapArow(sender: UIButton) {
//        print("arow")
        delegate?.didTapArow(rowType)
    }
    
    @objc func didTapSwitch(sender: UISwitch) {
//        print(sender.isOn)
        delegate?.didTapSwitch(rowType, isOn: sender.isOn, cell: self)
    }
    
    func setupUI() {
        contentView.backgroundColor = .white
        [titleLabel, addButon, arowButon, switchButton, tempLabel, lineView, descLabel].forEach {
            contentView.addSubview($0)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.bottom.equalToSuperview().inset(16)
            make.height.equalTo(24)
            make.leading.equalToSuperview().inset(24)
        }
        
        addButon.snp.makeConstraints { make in
            make.height.width.equalTo(24)
            make.trailing.equalToSuperview().inset(24)
            make.centerY.equalToSuperview()
        }
        
        switchButton.snp.makeConstraints { make in
            make.trailing.equalToSuperview().inset(24)
            make.centerY.equalToSuperview()
        }
        
        arowButon.snp.makeConstraints { make in
            make.height.width.equalTo(16)
            make.trailing.equalTo(addButon.snp.leading).offset(-12)
            make.centerY.equalToSuperview()
        }
        
        tempLabel.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.trailing.equalTo(arowButon.snp.leading).offset(-5)
        }
        
        descLabel.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.trailing.equalTo(addButon.snp.leading).offset(-12)
        }
        
        lineView.snp.makeConstraints { make in
            make.height.equalTo(1)
            make.leading.trailing.equalToSuperview().inset(24)
            make.bottom.equalToSuperview()
        }
    }
}


class CalendarVCAddCell: CalendarVCGeneralCell {
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        descLabel.isHidden = true
        addButon.setImage(UIImage(named: "circleAddIcon"), for: .normal)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
}

class CalendarVCTempCell: CalendarVCGeneralCell {
        
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        
        configUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func configUI() {
        addButon.setImage(UIImage(named: "temperatureIcon"), for: .normal)
        arowButon.setImage(UIImage(named: "tempDownArow"), for: .normal)
        arowButon.isHidden = false
        tempLabel.isHidden = false
        tempLabel.text = "˚F/˚C"
    }
}


class CalendarVCToggleCell: CalendarVCGeneralCell {
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        
        addButon.isHidden = true
        switchButton.isHidden = false
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
}

class NotesSectionHeader: UITableViewHeaderFooterView {
    
    let titleLabel: UILabel = {
        let l = UILabel()
        l.font = .mediumGilroyFont(14)
        l.textColor = .mainTextColor
        return l
    }()
    
    override init(reuseIdentifier: String?) {
        super.init(reuseIdentifier: reuseIdentifier)
        contentView.backgroundColor = .themeColor
        
        contentView.addSubview(titleLabel)
        titleLabel.snp.makeConstraints { make in
            make.left.equalToSuperview().inset(24)
            make.bottom.equalToSuperview()
        }
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
}
