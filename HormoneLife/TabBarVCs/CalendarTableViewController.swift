//
//  CalendarTableViewController.swift
//  HormoneLife
//
//  Created by Tank on 2024/6/21.
//

import UIKit
import JTAppleCalendar
import MJRefresh

class CalendarTableViewController: BaseViewController {
    
    let noteViewModel = NotePageViewModel()
    let userModel = UserTestViewModel()
    
    let tipViewModel = CommonViewModel()
    
    enum Section: Int, CaseIterable {
        case settings
        case notes
    }
    
    var notetitleDayLab = UILabel()
    var selectDate = Date()
    var isComingDays: Bool = false
    
    enum Row {
        case sectionRow(title: String)
        case calendareLabelRow
        case cycleDay(title: String, desc: String)
        case toggleRow(title: String, rowType: CalendarVCGeneralCell.CalendarVCRowType, isSwitchOn: Bool)
        case addRow(title: String, desc: String, rowType: CalendarVCGeneralCell.CalendarVCRowType)
        case temperatureRow(title: String, rowType: CalendarVCGeneralCell.CalendarVCRowType)
        case sexRow(title: String, desc: String, rowType: CalendarVCGeneralCell.CalendarVCRowType)
        case note(rowType: CalendarVCGeneralCell.CalendarVCRowType)
    }
    
    var settingRows: [Row] {
        
//        if isProduce {
//            return [.calendareLabelRow,
//                    .cycleDay(title: "Cycle Day: \(cycleDay)", desc: selectDayStatusString),
//                    .sectionRow(title: "Menstruation"),
//                    .toggleRow(title: menstruationTitle, rowType: .begin, isSwitchOn: isMenstruationBegins),
//                    .addRow(title: "Flow", desc: self.flowDescription, rowType: .flow(current: self.flowDescription)),
//                    .sectionRow(title: "Ovulation Data & Others"),
//                    .addRow(title: "Ovulation Test (LH Ultra)", desc: self.ovulationTestDesc, rowType: .ovulationTest),
//                    .addRow(title: "Progesterone Test (pdg)", desc: self.progesteroneTestDesc, rowType: .pdg),
//                    .temperatureRow(title: "Body Temperature", rowType: .temp(tempValue: self.currentTemperature, tempUnit: currentTemperatureUnit, markTime: temperatureMarkTime)),
//                    .addRow(title: "Cervical Mucus", desc: cervicalMucusDesc, rowType: .cervicalMucus(current: self.cervicalMucusDesc)),
//                    .sexRow(title: "Sex", desc: self.sexDescription, rowType: .sex(current: self.sexDescription)),
//                    .addRow(title: "Notes", desc: "", rowType: .notes)]
//        } else {
            return [.calendareLabelRow,
                    .cycleDay(title: "Cycle Day: \(cycleDay)", desc: selectDayStatusString),
                    .sectionRow(title: "Menstruation"),
                    .toggleRow(title: menstruationTitle, rowType: .begin, isSwitchOn: isMenstruationBegins),
                    .addRow(title: "Flow", desc: self.flowDescription, rowType: .flow(current: self.flowDescription)),
                    .sectionRow(title: "Ovulation Data & Others"),
                    .addRow(title: "Ovulation Test (LH Ultra)", desc: self.ovulationTestDesc, rowType: .ovulationTest),
                    .addRow(title: "Progesterone Test (pdg)", desc: self.progesteroneTestDesc, rowType: .pdg),
                    .temperatureRow(title: "Body Temperature", rowType: .temp(tempValue: self.currentTemperature, tempUnit: currentTemperatureUnit, markTime: temperatureMarkTime)),
                    .addRow(title: "Cervical Mucus", desc: cervicalMucusDesc, rowType: .cervicalMucus(current: self.cervicalMucusDesc)),
                    .sexRow(title: "Sex", desc: self.sexDescription, rowType: .sex(current: self.sexDescription)),
                    .addRow(title: "Notes", desc: "", rowType: .notes)]
//        }
        
       
    }
    
    var cycleDay: Int = 1
//    {
//        Interface.shared().loggedInUser?.userInfo.userBusinessConfigVO.period ?? 0
//    }
    
    var sexDescription: String = ""
    var sexSelectIndex: Int {
        ["No sex", "Protected", "Unprotected"].firstIndex(of: self.sexDescription) ?? -1
//        CalendarVCGeneralCell.CalendarVCRowType.sex(current: self.sexDescription).indexOfItems
    }
    
    var flowDescription: String = ""
    var cervicalMucusDesc: String = ""
    
    var currentTemperature: String = ""
    var currentTemperatureUnit: Int = 1
    var temperatureMarkTime: String = ""
    
    var selectDayStatusString: String = ""
    
    var ovulationTestDesc: String {
        
        guard let _ = self.periodRecordDetail?.lhUltraTestPage else {
            return ""
        }
        
        return testResultDisplayText(
            lastResult: periodRecordDetail?.lhUltraTestPage?.lastResult,
            lastResultLabel: periodRecordDetail?.lhUltraTestPage?.lastResultLabel,
            resultValue: periodRecordDetail?.lhUltraTestPage?.resultValue,
            resultLabel: periodRecordDetail?.lhUltraTestPage?.resultLabel, type: .LhUltra)
    }
    var progesteroneTestDesc: String {
        guard let _ = self.periodRecordDetail?.pdgTestPage else {
            return ""
        }
        return testResultDisplayText(
            lastResult: periodRecordDetail?.pdgTestPage?.lastResult,
            lastResultLabel: periodRecordDetail?.pdgTestPage?.lastResultLabel,
            resultValue: periodRecordDetail?.pdgTestPage?.resultValue,
            resultLabel: periodRecordDetail?.pdgTestPage?.resultLabel, type: .PdG)
    }
    
    var calendarView: AppCalendarView!
    var isCalendarLabelViewExpand: Bool = false
    
    var calendarDateList: [CalendarDatesDetail] = [] {
        didSet {
            guard selectDate.yyyyMMdd() == Date().yyyyMMdd() else {
                return
            }
            self.updateCycleDayRow(dateStr: selectDate.yyyyMMdd())
        }
    }
    var periodRecordDetail: PeriodRecordDetail? = nil {
        didSet {
            guard let period = periodRecordDetail else {
                return
            }
            self.updateCycleDayRow(dateStr: selectDate.yyyyMMdd())
            cycleDay = period.days ?? 0
            flowDescription = period.symptomsFlow ?? ""
            currentTemperature = "\(period.temperature ?? "")"
            if period.temperature == "0" {
                currentTemperature = ""
            }
            currentTemperatureUnit = period.temperatureUnit ?? 2
            cervicalMucusDesc = period.cervicalMucus ?? ""
            if let loveState = period.loveState {
                sexDescription = ["No sex", "Protected", "Unprotected"][loveState]
            } else {
                sexDescription = ""
            }
            
            if period.userCalendarPeriodInfoVO?.beginOrEndStatus == 1 {
                //menstruation begins
                isMenstruationBegins = period.userCalendarPeriodInfoVO?.startPeriodModifyState == 1
            } else if period.userCalendarPeriodInfoVO?.beginOrEndStatus == 2 {
                //menstruation end
                isMenstruationBegins = period.userCalendarPeriodInfoVO?.endPeriodModifyState == 1
            }
        }
    }
    
    var isMenstruationBegins: Bool = false
    var menstruationTitle: String {
        return periodRecordDetail?.userCalendarPeriodInfoVO?.beginOrEndStatus == 2 ? "End of menstruation?" : "Menstruation begins?"
    }
    
    private lazy var tableView: UITableView = {
        let tableView = UITableView(frame: .zero, style: .grouped)
        tableView.backgroundColor = .themeColor
        tableView.showsVerticalScrollIndicator = false
        tableView.separatorStyle = .none
        
        tableView.register(CalendarLabelCellTableViewCell.self, forCellReuseIdentifier: "CalendarLabelCellTableViewCell")
        tableView.register(CalendarVCSectionCell.self, forCellReuseIdentifier: "CalendarVCSectionCell")
        tableView.register(CalendarVCAddCell.self, forCellReuseIdentifier: "CalendarVCAddCell")
        tableView.register(CalendarVCTempCell.self, forCellReuseIdentifier: "CalendarVCTempCell")
        tableView.register(CalendarVCToggleCell.self, forCellReuseIdentifier: "CalendarVCToggleCell")
        tableView.register(CalendarVCCycleDayCell.self, forCellReuseIdentifier: "CalendarVCCycleDayCell")
        tableView.register(NotesSectionHeader.self, forHeaderFooterViewReuseIdentifier: "NotesSectionHeader")
        tableView.register(UserNoteCell.classForCoder(), forCellReuseIdentifier: UserNoteCell.description())

        tableView.dataSource = self
        tableView.delegate = self
        tableView.rowHeight = UITableView.automaticDimension
        return tableView
    }()
    
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        view.backgroundColor = .themeColor
        setupView()
        setNavigationBar()
        setupCalendar()
        self.tipViewModel.getHomeTips { result in
            self.tableView.reloadData()
        } failure: { error in
            
        }
        
//        self.downRefreshDataRequest()
//        self.fetchPeriodDetailByDate(Date().yyyyMMdd())
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        
        // loadData
        let selectYearMonth = dateFormat(date: self.selectDate, format: "yyyy-MM")
        self.fetchCycleDataByMonth(date: selectYearMonth != calendarView.currentYearMonth ? calendarView.currentYearMonth : selectYearMonth)
        self.downRefreshDataRequest()
        self.fetchPeriodDetailByDate(hl_dateFormat(date: Date(), format: "yyyy-MM-dd HH:mm:ss"))
        calendarView.todayButtonHandler(UIButton())
    }
    
    func fetchPeriodDetailByDate(_ date: String) {
        userModel.calendarPeriodRecordDetail(date: date) { result in
            self.periodRecordDetail = result
            self.tableView.reloadData()
        }
    }
    
    func selectCalendarDate(date : Date) {
        
        self.selectDate = date
        self.notetitleDayLab.text = "It's been \(dayForDaysCount(dateFormat(date: self.selectDate, format: "yyyy-MM-dd HH:mm:ss"))) days"
        
        self.downRefreshDataRequest()
        fetchPeriodDetailByDate(hl_dateFormat(date: self.selectDate, format: "yyyy-MM-dd HH:mm:ss"))
        
        //updateCycleDayRow(dateStr: date.yyyyMMdd())
    }
    
    func updateCycleDayRow(dateStr: String) {
        let dateDetail = calendarDateList.first {
            $0.dayStr == dateStr
        }
        guard let detail = dateDetail?.calendarDetailVO else {
            selectDayStatusString = ""
            tableView.reloadData()
            return
        }
        
        var cyclesResult: [String] = []
        let cycles = CalendarLabel.dateState(detail: detail).map {
            $0.calendarCurrentStatusTitle
        }.filter {$0 != ""}
        
        if cycles.count > 0, let last = cycles.last {
            cyclesResult.append(last)
        }
        
        if let tip = self.periodRecordDetail?.calendarRemainTips, tip.count > 0 {
            cyclesResult.append(tip)
        }
        //let cyclesResult = cycles.filter({ cycles.firstIndex(of: $0) == cycles.firstIndex(of: $0) })
        selectDayStatusString = cyclesResult.joined(separator: cyclesResult.count > 1 ? " & " : "")
        tableView.reloadData()
    }
    
    func updateCalendarPeriodWithTemperature(type: CalendarVCGeneralCell.CalendarVCRowType) {
        let id = periodRecordDetail?.id
        var cgTemp: CGFloat?
        if let temp = Float(currentTemperature) {
            cgTemp = CGFloat(temp)
        }
        
        var martTime = dateFormat(date: self.selectDate, format: "yyyy-MM-dd HH:mm:ss")
        if let _ = cgTemp {
            let time = timeFormat(date: self.temperatureMarkTime, format: "hh:mm a", toFormat: "HH:mm:ss")
            
            martTime = "\(martTime.components(separatedBy: " ").first ?? "") \(time)"
        }
        
        switch type {
        case .temp:
            userModel.calendarPeriodUpdate(id: id, cervicalMucus: nil, loveState: nil, markTime: martTime, symptomsFlow: nil, temperature: cgTemp, temperatureUnit: self.currentTemperatureUnit) { result in
                
                self.fetchCycleAndPeriodDataAfterCalenderUpdate()
            }
        case .sex:
            userModel.calendarPeriodUpdate(id: periodRecordDetail?.id, cervicalMucus: nil, loveState: sexSelectIndex, markTime: martTime, symptomsFlow: nil, temperature: nil, temperatureUnit: nil) { result in
                self.fetchCycleAndPeriodDataAfterCalenderUpdate()
            }
        case .flow:
            userModel.calendarPeriodUpdate(id: periodRecordDetail?.id, cervicalMucus: nil, loveState: nil, markTime: martTime, symptomsFlow: flowDescription, temperature: nil, temperatureUnit: nil) { result in
                self.fetchCycleAndPeriodDataAfterCalenderUpdate()
            }
        case .cervicalMucus:
            userModel.calendarPeriodUpdate(id: periodRecordDetail?.id, cervicalMucus: cervicalMucusDesc, loveState: nil, markTime: martTime, symptomsFlow: nil, temperature: nil, temperatureUnit: nil) { result in
                self.fetchCycleAndPeriodDataAfterCalenderUpdate()
            }
        default:
            break
        }
    }
    
    func fetchCycleAndPeriodDataAfterCalenderUpdate() {
        self.fetchCycleDataByMonth(date: dateFormat(date: self.selectDate, format: "yyyy-MM"))
        self.fetchPeriodDetailByDate(dateFormat(date: self.selectDate, format: "yyyy-MM-dd HH:mm:ss"))
    }
    
    func downRefreshDataRequest() {
        self.noteViewModel.getNotesPage(refresh: true, userId: Interface.shared().loggedInUser?.userInfo.id, imageUrls: nil, markTime: dateFormat(date: self.selectDate, format: "yyyy-MM-dd HH:mm:ss")) {[weak self] success in
            self?.handleRequestSuccessResult(refresh: self!.noteViewModel.hasMoreData, count: self!.noteViewModel.dataSource.count)
        } failure: { error in
            self.handleRequestFailureResult()
        }
    }
    
    func upMoreDataRequest() {
        self.noteViewModel.getNotesPage(refresh: false, userId: Interface.shared().loggedInUser?.userInfo.id, imageUrls: nil, markTime: dateFormat(date: self.selectDate, format: "yyyy-MM-dd HH:mm:ss")) {[weak self] success in
            self?.handleRequestSuccessResult(refresh: self!.noteViewModel.hasMoreData, count: self!.noteViewModel.dataSource.count)
        } failure: { error in
            self.handleRequestFailureResult()
        }
    }
    
    func handleRequestSuccessResult(refresh: Bool, count: Int) {
        self.tableView.reloadData()
        if self.tableView.mj_header?.isRefreshing == true {
            self.tableView.mj_header?.endRefreshing()
        }
        if self.tableView.mj_footer?.isRefreshing == true {
            self.tableView.mj_footer?.endRefreshing()
        }
        if refresh {
            self.tableView.mj_footer = MJRefreshAutoNormalFooter(refreshingBlock: {
                self.upMoreDataRequest()
            })
            
        } else {
            self.tableView.mj_footer?.endRefreshingWithNoMoreData()
        }
        
//        if count > 0 {
//            self.noDataView.isHidden = true
//        } else {
//            self.noDataView.isHidden = false
//        }
    }
    
    func handleRequestFailureResult() {
        if self.tableView.mj_header?.isRefreshing == true {
            self.tableView.mj_header?.endRefreshing()
        }
        if self.tableView.mj_footer?.isRefreshing == true {
            self.tableView.mj_footer?.endRefreshing()
        }
    }
    
    // param date: yyyyMM
    private func fetchCycleDataByMonth(date: String) {
        let currentMonthFirstDay = "\(date)-01"
        let currentMonthLastDay = "\(date)-28"
        let sevenDaysAgo = currentDayToAgoOrAfterDays(days: -7, fromCurrentDate: currentMonthFirstDay)
        let tenDaysAfter = currentDayToAgoOrAfterDays(days: 10, fromCurrentDate: currentMonthLastDay)
        
        UserPeriodInteractor.userPeriodCycleByMonth(startDate: sevenDaysAgo, toDate: tenDaysAfter) { dateList in
            // replace data
            self.calendarDateList = dateList
            self.calendarView.reloadData()
        }
    }
    
    func currentDayToAgoOrAfterDays(days: Int, fromCurrentDate: String) -> String {
        let currentDate = fromCurrentDate.covertDate(with: "yyyy-MM-dd")
        let tenDaysAgoComponents = DateComponents(day: days)
        if let tenDaysAgoDate = Calendar.current.date(byAdding: tenDaysAgoComponents, to: currentDate) {
            return tenDaysAgoDate.yyyyMMdd(dateFormat: "yyyy-MM-dd HH:mm:ss")
        } else {
            return "\(fromCurrentDate) 00:00:00"
        }
    }
    
    func setupView() {
        view.addSubview(tableView)
        tableView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
    
    func setupCalendar() {
        calendarView = AppCalendarView(frame: CGRect(x: 0, y: 0, width: UIScreen.main.bounds.size.width, height: 400), delegate: self)
        calendarView.configure(beforeCurrentMonth: 50, afterCurrentMonth: 50, isNextMonthEnable: false, isPreMonthEnable: true)
        tableView.tableHeaderView = calendarView
    }
    
    private func setNavigationBar() {
        navigationItem.leftBarButtonItem = nil
        navigationItem.title = "Calendar"
    }
    
    private func testResultDisplayText(lastResult: Double?, lastResultLabel: String?, resultValue: Double?, resultLabel: String?, type: TestType) -> String {
        var value = 0.0
        var label: String = ""
        if let lastResult = lastResult, lastResult > 0 {
            value = lastResult
            label = lastResultLabel ?? ""
        } else {
            value = resultValue ?? 0.0
            label = resultLabel ?? ""
        }
        
        if value == 0.0 {
            if type == .LhUltra {
                return "0.0(Low)"
            }
            return "Negative"
        }
        if label == "" {
            return "(null)"
        } else {
            if type == .LhUltra {
                return "\(value)(\(label))"
            }
            return "\(label)"
        }
//        if label == "" {
//            return "\(value) (null)"
//        } else {
//            return "\(value) (\(label))"
//        }
    }
}

extension CalendarTableViewController: UITableViewDelegate, UITableViewDataSource {
    func numberOfSections(in tableView: UITableView) -> Int {
        return isComingDays ? 1 : Section.allCases.count
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        guard let section = Section(rawValue: section) else { return 0 }
        switch section {
        case .settings: return isComingDays ? 1 : settingRows.count
        case .notes: return self.noteViewModel.dataSource.count
        }
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        guard let section = Section(rawValue: indexPath.section) else { return UITableViewCell() }
        
        switch section {
        case .settings:
            let row = settingRows[indexPath.row]
            switch row {
            case .calendareLabelRow:
                guard let cell = tableView.dequeueReusableCell(withIdentifier: "CalendarLabelCellTableViewCell", for: indexPath) as? CalendarLabelCellTableViewCell else {
                    return UITableViewCell()
                }
                cell.delegate = self
                cell.isExpand = isComingDays ? true : isCalendarLabelViewExpand
                return cell
            case let .cycleDay(title, desc):
                guard let cell = tableView.dequeueReusableCell(withIdentifier: "CalendarVCCycleDayCell", for: indexPath) as? CalendarVCCycleDayCell else {
                    return UITableViewCell()
                }
                // TODO: need to update text
                cell.setupWithTitle(title, description: desc, delegate: self)
                return cell
            case .sectionRow(let title):
                guard let cell = tableView.dequeueReusableCell(withIdentifier: "CalendarVCSectionCell", for: indexPath) as? CalendarVCSectionCell else {
                    return UITableViewCell()
                }
                cell.titleLabel.text = title
                return cell
            case let .toggleRow(title, rowType, isToggleOn):
                guard let cell = tableView.dequeueReusableCell(withIdentifier: "CalendarVCToggleCell", for: indexPath) as? CalendarVCToggleCell else {
                    return UITableViewCell()
                }
                cell.titleLabel.text = title
                cell.rowType = rowType
                cell.switchButton.isOn = isToggleOn
                cell.delegate = self
                return cell
            case let .addRow(title, desc, rowType):
                guard let cell = tableView.dequeueReusableCell(withIdentifier: "CalendarVCAddCell", for: indexPath) as? CalendarVCAddCell else {
                    return UITableViewCell()
                }
                cell.titleLabel.text = title
                cell.descLabel.text = desc
                cell.descLabel.isHidden = false
                cell.rowType = rowType
                cell.delegate = self
                return cell
            case let .temperatureRow(title, rowType):
                guard let cell = tableView.dequeueReusableCell(withIdentifier: "CalendarVCTempCell", for: indexPath) as? CalendarVCTempCell else {
                    return UITableViewCell()
                }
                cell.titleLabel.text = title
                cell.rowType = rowType
                cell.tempLabel.text = "\(currentTemperature) ˚\(currentTemperatureUnit == 2 ? "F": "C")"
                cell.delegate = self
                return cell
            case let .sexRow(title, desc, rowType):
                                
                guard let cell = tableView.dequeueReusableCell(withIdentifier: "CalendarVCAddCell", for: indexPath) as? CalendarVCAddCell else {
                    return UITableViewCell()
                }
                cell.titleLabel.text = title
                
                if sexSelectIndex >= 0 {
                    cell.descLabel.text = desc
                } else {
                    cell.descLabel.text = ""
                }
                
                cell.descLabel.isHidden = false
                cell.rowType = rowType
                cell.delegate = self
                return cell
            default: return UITableViewCell()
            }
        case .notes:
            let cell = tableView.dequeueReusableCell(withIdentifier: UserNoteCell.description()) as! UserNoteCell
            cell.refresh(model: self.noteViewModel.dataSource[indexPath.row])
            return cell
        }
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return UITableView.automaticDimension
    }
    
    func tableView(_ tableView: UITableView, viewForHeaderInSection section: Int) -> UIView? {
        guard let sectionType = Section(rawValue: section),
              sectionType == .notes,
              noteViewModel.dataSource.count > 0,
            let header = tableView.dequeueReusableHeaderFooterView(withIdentifier: "NotesSectionHeader") as? NotesSectionHeader else {
            return nil
        }
        let user = Interface.shared().loggedInUser?.userInfo
        let days = "\(dayForDaysCount("\(user?.createTime ?? "")", forDate: self.selectDate))"
        let title = (Int(days) ?? 0) <= 0 ? "" : "It's been \(days) day\((Int(days) ?? 0) > 1 ? "s" : "")"
        header.titleLabel.text = title
        
//        header.titleLabel.text = "It's been \(dayForDaysCount(dateFormat(date: self.selectDate, format: "yyyy-MM-dd HH:mm:ss"))) days"
        self.notetitleDayLab = header.titleLabel
        return header
    }
    
    func tableView(_ tableView: UITableView, heightForHeaderInSection section: Int) -> CGFloat {
        guard let sectionType = Section(rawValue: section),
              sectionType == .notes else {
            return 0
        }
        return 40
    }
    
    func tableView(_ tableView: UITableView, viewForFooterInSection section: Int) -> UIView? {
        return nil
    }
    
    func tableView(_ tableView: UITableView, heightForFooterInSection section: Int) -> CGFloat {
        0
    }
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        
        if let sectionType = Section(rawValue: indexPath.section), sectionType == .notes {
            let vc = NoteDetailViewController()
            vc.noteModel = self.noteViewModel.dataSource[indexPath.row]
            self.navigationController?.pushViewController(vc, animated: true)
        }
//        else {
//            let row = settingRows[indexPath.row]
//            switch row {
//            case let .addRow(title, desc, rowType):
//                if let vc = getKeyWindow()?.rootViewController as? TabBarController {
//                    vc.selectedIndex = 1
//                    var pageType = "PdG"
//                    if rowType == .ovulationTest {
//                        pageType = "LH Ultra"
//                    }
//                    let noti = Notification(name: Notification.Name("refreshHistoryRecordData"), userInfo: ["paperType": pageType])
//                    NotificationCenter.default.post(noti)
//                }
//
//            default:
//                print("")
//            }
//        }
        
//    case ovulationTest
//    case pdg
    }
    
}

extension CalendarTableViewController: CalendarLabelCellTableViewCellDelegate {
    func didTapExpandButton(_ isExpand: Bool) {
        isCalendarLabelViewExpand = isExpand
        tableView.reloadRows(at: [IndexPath(row: 0, section: 0)], with: .none)
    }
    
    func didTapLabel(_ labelType: CalendarLabel) {
        print(labelType)
    }
}

extension CalendarTableViewController: CalendarVCGeneralCellDelegate, CreateAccountPopupViewControllerDelegate {
    func didTapCreateAccount() {
        let signUpVC = SignUpViewController()
        navigationController?.pushViewController(signUpVC, animated: true)
    }
    
    func didTapClosePopup() {
        
    }
    
    
    @objc func didTapChartsView(tempUnit: Int = 1) {
        
        guard UserDefaults.standard.isLoginAsGuestWithoutAccount == false else {
            let vc = CreateAccountPopupViewController()
            vc.delegate = self
            vc.modalPresentationStyle = .overFullScreen
            self.navigationController?.present(vc, animated: true)
            return
        }
        
        let chartDetailVC = ChartDetailViewController()
        chartDetailVC.chartType = .temp
        chartDetailVC.temperatureUnit = tempUnit
        hidesBottomBarWhenPushed = true
        navigationController?.pushViewController(chartDetailVC, animated: true)
        hidesBottomBarWhenPushed = false
    }
    
    
    func didTapAdd(_ rowType: CalendarVCGeneralCell.CalendarVCRowType) {
        print(rowType)
        switch rowType {
        case .ovulationTest, .pdg:
            guard !isRunningOnSimulator() else { return }
            let paperType: TestType = rowType == .pdg ? .PdG : .LhUltra
            let captureVC = CustomCameraViewController()
            captureVC.paperType = paperType
            captureVC.pageType = paperType.description
            captureVC.selectDate = self.selectDate
            hidesBottomBarWhenPushed = true
            navigationController?.pushViewController(captureVC, animated: true)
            hidesBottomBarWhenPushed = false
        case .notes:
            let addNoteVc = AddNoteViewController(nibName: "AddNoteViewController", bundle: nil)
            addNoteVc.delegate = self
            navigationController?.pushViewController(addNoteVc, animated: true)
        case .temp(_, let tempUnit, _):
            self.didTapChartsView(tempUnit: tempUnit)
        default:
            let popup = CalendarPopupSelectionViewController(rowType: rowType, delegate: self)
            popup.modalPresentationStyle = .overFullScreen
            navigationController?.present(popup, animated: true)
        }
    }
    
    func didTapDescLabel(_ rowType: CalendarVCGeneralCell.CalendarVCRowType) {
        switch rowType {
        case .ovulationTest, .pdg:
            if let vc = getKeyWindow()?.rootViewController as? TabBarController {
                vc.selectedIndex = 1
                let noti = Notification(name: Notification.Name("refreshHistoryRecordData"), userInfo: ["paperType": rowType.pageType])
                NotificationCenter.default.post(noti)
            }
        default:
            break
        }
    }
    
    func didTapArow(_ rowType: CalendarVCGeneralCell.CalendarVCRowType) {
        switch rowType {
        case .temp(_, _, _):
            let popup = CalendarPopupSelectionViewController(rowType: rowType, delegate: self)
            popup.modalPresentationStyle = .overFullScreen
            navigationController?.present(popup, animated: true)
        default:
            break
        }
    }
    
    func didTapSwitch(_ rowType: CalendarVCGeneralCell.CalendarVCRowType, isOn: Bool, cell: UITableViewCell) {
        print(rowType, isOn)
        guard rowType == .begin else { return }
        
        if let message = periodRecordDetail?.userCalendarPeriodInfoVO?.startReminderMessage, isOn {
            let alert = UIAlertController(title: "", message: message, preferredStyle:.alert)
            let confirmAction = UIAlertAction(title: "Confirm", style: .default) { _ in
                updateMenstruationBegins()
            }
            let cancelAction = UIAlertAction(title: "Cancel", style:.default, handler: nil)
            alert.addAction(cancelAction)
            alert.addAction(confirmAction)
            present(alert, animated: true, completion: nil)
        } else {
            updateMenstruationBegins()
        }
        
        
        func updateMenstruationBegins() {
            //menstruationBeginsUpdate
            let state = isOn ? 1 : 0
            let startOrEnd = periodRecordDetail?.userCalendarPeriodInfoVO?.beginOrEndStatus
            userModel.menstruationBeginsUpdate(id: periodRecordDetail?.id, state: state, selectDate: hl_dateFormat(date: self.selectDate, format: "yyyy-MM-dd HH:mm:ss"), startOrEnd: startOrEnd, startPeriodTime: nil, endPeriodTime: nil) { result in
                self.fetchCycleAndPeriodDataAfterCalenderUpdate()
            } failure: { error in
                self.tableView.reloadData()
            }
        }
    }
}

extension CalendarTableViewController : AppCalendarViewDelegate{
    func cellDataType(view: AppCalendarCell?,  text: String, isSelected : Bool, indexPath: IndexPath, date: Date) -> [CalendarDataType] {
        if Calendar.current.isDateInToday(date) {
            return [.fail,.success, .waitting]
        }
        return []
    }
    func cellColorDataType(view: AppCalendarCell?,  text: String, isSelected : Bool, indexPath: IndexPath, date: Date) -> [CalendarDataType : UIColor] {
        return [.fail : .red, .success : .green, .waitting : .orange, .unknown : .black]
    }

    //calendar is select
    func appCalendarView(_ view : AppCalendarView, dateSelected date: Date) {
        isComingDays = date.removeTimeStamp > Date().removeTimeStamp
        //completion?(date + 72000)
        self.selectCalendarDate(date: date)
        self.dismiss(animated: true)
    }
    
    // set date label
    func cellDayMarkLabelType(view: AppCalendarCell?, text: String, isSelected : Bool, indexPath: IndexPath, date: Date, isEnableDate: Bool) -> [CalendarLabel] {
        let dateStr = date.yyyyMMdd()
        
        let dateDetail = calendarDateList.first {
            $0.dayStr == dateStr
        }
        guard let detail = dateDetail?.calendarDetailVO else {
            return []
        }
        
        return CalendarLabel.dateState(detail: detail, isEnableDate: isEnableDate)
    }
    
    func currentDisplayDate(yyyyMM: String) {
        guard yyyyMM.count > 0 else { return }
        fetchCycleDataByMonth(date: yyyyMM)
    }
}

extension CalendarTableViewController: CalendarVCCycleDayCellDelegate {
    func didTapUnderLine() {
        print(#function)
    }
}

extension CalendarTableViewController: AddNoteViewControllerDelegate {
    func didAddNote() {
        print(#function)
    }
}

extension CalendarTableViewController: CalendarPopupSelectionViewControllerDelegate {
    
    func didSave(type: CalendarVCGeneralCell.CalendarVCRowType, tags: [String]) {
        switch type {
        case .sex:
            guard !tags.isEmpty,
                    let selectedTag = tags.first,
                    let index = Int(selectedTag),
                  type.popupViewButtonTitles.count > index else { return }
            sexDescription = type.popupViewButtonTitles[index]
            tableView.reloadData()
        case .flow:
            guard !tags.isEmpty,
                    let selectedTag = tags.first,
                    let index = Int(selectedTag),
                  type.popupViewButtonTitles.count > index else { return }
            flowDescription = type.popupViewButtonTitles[index]
            tableView.reloadData()
        case .cervicalMucus:
            guard !tags.isEmpty,
                    let selectedTag = tags.first,
                    let index = Int(selectedTag),
                  type.popupViewButtonTitles.count > index else { return }
            cervicalMucusDesc = type.popupViewButtonTitles[index]
            tableView.reloadData()
        case let .temp(tempValue, tempUnit, markTime):
            self.currentTemperature = tempValue
            self.currentTemperatureUnit = tempUnit == 2 ? 2 : 1
            self.temperatureMarkTime = markTime
        default:
            break
        }
        updateCalendarPeriodWithTemperature(type: type)
    }
}


