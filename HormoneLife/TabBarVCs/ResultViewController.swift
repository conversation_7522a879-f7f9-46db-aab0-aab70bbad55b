//
//  ResultViewController.swift
//  HormoneLife
//
//  Created by Tank on 2024/7/28.
//

import UIKit
import SnapKit
import SDWebImage

protocol ResultViewControllerDelegate: AnyObject {
    func didDeleteResult()
    func didUpdateResult()
}
class ResultViewController: BaseViewController {
    
    private lazy var pickerView: TimePickerView = {
        let pickerView = TimePickerView(is24HoursFormat: true)
        pickerView.title.isHidden = true
        pickerView.hourStr = "24"
        pickerView.amAndPm = []
        pickerView.delegate = self
        return pickerView
    }()
    var pickerViewBottomContrain: Constraint?
    
    let tandcView = TandCResultView()
    
    var circleViewType: TestType = .LH
    var circleView = TestResultView()
    
    let testTimeView = EditTestTimeView()
    var yyyyMMdd: String?
    var hhmm: String?
    
    var isCreate = false
    var isEdited = false
    var testResult : UserTestResultPageModel?
    
    lazy var deleteButton: UIButton = {
        let b = UIButton(type: .custom)
        b.backgroundColor = .themeColor
        b.layer.cornerRadius = 4
        b.layer.borderColor = UIColor.mainTextColor.cgColor
        b.layer.borderWidth = 1
        b.setTitle("Delete", for: .normal)
        b.titleLabel?.font = .regularGilroyFont(16)
        b.setTitleColor(.mainTextColor, for: .normal)
        b.addTarget(self, action: #selector(didTapDeleteButton), for: .touchUpInside)
        return b
    }()
    
    lazy var saveButton: UIButton = {
        let b = UIButton(type: .custom)
        b.backgroundColor = .mainTextColor
        b.layer.cornerRadius = 4
        b.setTitle("Save", for: .normal)
        b.titleLabel?.font = .regularGilroyFont(16)
        b.setTitleColor(.white, for: .normal)
        b.addTarget(self, action: #selector(didTapSaveButton), for: .touchUpInside)
        return b
    }()
    
    var testDetail: TestPaperDetail
    weak var delegate: ResultViewControllerDelegate?
    
    init(testDetail: TestPaperDetail, circleViewType: TestType = .LH, delegate: ResultViewControllerDelegate? = nil) {
        self.testDetail = testDetail
        super.init(nibName: nil, bundle: nil)
        self.circleViewType = circleViewType
        tandcView.imageView.sd_setImage(with: URL(string: testDetail.imageUrl ?? ""), placeholderImage: nil)
        self.delegate = delegate
        
        circleView.setupUIWith(circleViewType, testDetail: testDetail)
        
        setupTimeView()
    }
    
    @objc func hcgResultEdited() {
        guard !isEdited else { return }
        isEdited = true
        checkSaveButtonStatus()
    }
    
    private func checkSaveButtonStatus() {
        saveButton.isEnabled = !(isCreate && circleViewType == .HCG && !isEdited)
        
        let disableColor = UIColor(red: 68/255, green: 85/255, blue: 113/255, alpha: 1)
        let enableColor = UIColor.mainTextColor.withAlphaComponent(1)
        saveButton.backgroundColor = saveButton.isEnabled ? enableColor : disableColor
    }
    
    func setupTimeView() {
        let markTime = testDetail.markTime ?? hl_dateFormat(date: Date(), format: "yyyy-MM-dd HH:mm:ss")
        let date = markTime.covertDate()
        
        self.yyyyMMdd = markTime.components(separatedBy: " ").first ?? date.format(with: "yyyy-MM-dd")
        self.hhmm = markTime.components(separatedBy: " ").last ?? date.format(with: "HH:mm:ss")
        
        let displayTime = hhmm?.dropLast(3)
        let day = yyyyMMdd?.components(separatedBy: "-").last
        let month = date.enMonth.prefix(3)
        let year = date.format(with: "yyyy")
        self.testTimeView.year.setTitle(year, for: .normal)
        self.testTimeView.monthAndDay.setTitle("\(month) \(day ?? "")", for: .normal)
        self.testTimeView.time.setTitle(displayTime?.description ?? hhmm, for: .normal)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setNavigationBar()
        setupUI()
        checkSaveButtonStatus()
        
        NotificationCenter.default.addObserver(self, selector: #selector(hcgResultEdited), name: NSNotification.Name("HCGResultEdited"), object: nil)
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
    
    override func didTapBackButton() {
        navigationController?.popToRootViewController(animated: true)
    }
    
    private func setupUI() {
        view.backgroundColor = .themeColor

        [tandcView, circleView, testTimeView, deleteButton, saveButton, pickerView].forEach(view.addSubview)
        
        tandcView.snp.makeConstraints {
            $0.left.right.equalToSuperview().inset(20)
            $0.top.equalToSuperview().inset(30)
        }
        
        circleView.snp.makeConstraints {
            $0.left.right.equalToSuperview().inset(20)
            $0.top.equalTo(tandcView.snp.bottom).offset(16)
            $0.height.equalTo(TestResultView.viewHeight)
        }
        
        testTimeView.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(20)
            make.top.equalTo(circleView.snp.bottom).offset(20)
        }
        
        deleteButton.snp.makeConstraints { make in
            make.left.equalToSuperview().inset(20)
            make.right.equalTo(view.snp.centerX).offset(-10)
            make.height.equalTo(48)
            make.bottom.equalToSuperview().inset(32)
        }
        
        saveButton.snp.makeConstraints { make in
            make.left.equalTo(deleteButton.snp.right).offset(20)
            make.right.equalToSuperview().inset(20)
            make.height.top.bottom.equalTo(deleteButton)
        }
        
        pickerView.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.height.equalTo(320)
            pickerViewBottomContrain = make.bottom.equalToSuperview().inset(-320).constraint
        }
        
        testTimeView.monthAndDay.addTarget(self, action: #selector(didTapMonthDay), for: .touchUpInside)
        testTimeView.year.addTarget(self, action: #selector(didTapMonthDay), for: .touchUpInside)
        testTimeView.time.addTarget(self, action: #selector(didTapTime), for: .touchUpInside)
    }
    
    @objc func didTapMonthDay() {
        popCanlendar { date in
            self.yyyyMMdd = date.format(with: "yyyy-MM-dd")
            let day = date.format(with: "dd")
            let month = date.enMonth.prefix(3)
            let year = date.format(with: "yyyy")
            self.testTimeView.year.setTitle(year, for: .normal)
            self.testTimeView.monthAndDay.setTitle("\(month) \(day)", for: .normal)
        }
    }
    
    @objc func didTapTime() {
        UIView.animate(withDuration: 0.15) {
            self.pickerViewBottomContrain?.update(inset: 0)
            self.view.layoutIfNeeded()
        }
    }
    
    private func setNavigationBar() {
        navigationItem.title = "Edit Test Result"
    }
    
    @objc func didTapDeleteButton() {
        let alert = UIAlertController(title: "Delete Test Result", message: "Are you sure want to delete this test result?", preferredStyle:.alert)
        let confirmAction = UIAlertAction(title: "Delete", style: .default) { _ in
            showActivityHUD()
            if let testId = self.testDetail.id {
                UserTestViewModel().deleteTestResult(resultId: testId) { result in
                    hideActivityHUD()
                    guard let _ = result else {
                        showToachMessage(message: "please try again later")
                        return
                    }
                    self.delegate?.didDeleteResult()
                    self.navigationController?.popViewController(animated: true)
                }
            } else {
                hideActivityHUD()
                self.delegate?.didDeleteResult()
                self.navigationController?.popViewController(animated: true)
            }
        }
        let cancelAction = UIAlertAction(title: "Cancel", style:.default, handler: nil)
        alert.addAction(cancelAction)
        alert.addAction(confirmAction)
        present(alert, animated: true, completion: nil)
    }
    
    @objc func didTapSaveButton() {
        let alert = UIAlertController(title: "Update Test Result", message: "Are you sure want to update this test result?", preferredStyle:.alert)
        let confirmAction = UIAlertAction(title: "Confirm", style: .default) { _ in
            updateValue()
        }
        let cancelAction = UIAlertAction(title: "Cancel", style:.default, handler: nil)
        alert.addAction(cancelAction)
        alert.addAction(confirmAction)
        present(alert, animated: true, completion: nil)
        
        func updateValue() {
            guard let imgUrl = testDetail.imageUrl else { return }
            var adjustedResult = "0"
            if circleViewType == .LH || circleViewType == .PdG || circleViewType == .HCG {
                adjustedResult = "\(circleView.circleView.value)"
            } else {
                adjustedResult = "\(Int(circleView.circleView.value))"
            }
            
            let adjustedTime = "\(yyyyMMdd ?? "") \(hhmm ?? "")"
            
            showActivityHUD()
            if self.isCreate {
                
                showActivityHUD()
                let time = transformTimeStamp(timeStamp: Double(Int(Date().timeIntervalSince1970)), format: "yyyy-MM-dd HH:mm:ss")
                UserTestViewModel().addNewTestPaper(imageUrl: testDetail.imageUrl, lastResult: adjustedResult, ovulation: testResult?.result?.ovulation, pageType: testResult?.type, pregnancy: testResult?.result?.pregnancy, resultLabel: testResult?.resultLabel, resultValue: testResult?.resultValue, markTime: time) { success in
                    hideActivityHUD()
                    self.delegate?.didUpdateResult()
                    self.navigationController?.popToRootViewController(animated: false)
                    if let vc = getKeyWindow()?.rootViewController as? TabBarController {
                        vc.selectedIndex = 1
                        
                        let noti = Notification(name: Notification.Name("refreshHistoryRecordData"), userInfo: ["paperType": self.testResult?.type ?? ""])
                        NotificationCenter.default.post(noti)
                    }
                }
                
//                let time = transformTimeStamp(timeStamp: Double(Int(Date().timeIntervalSince1970)), format: "yyyy-MM-dd HH:mm:ss")
//                UserTestViewModel().addNewTestPaper(imageUrl: self.testDetail.imageUrl, lastResult: adjustedResult, ovulation: testDetail.result?.ovulation, pageType: self.testDetail.pageType, pregnancy: testDetail.result?.pregnancy, resultLabel: testDetail.resultLabel, resultValue: testDetail.resultValue, markTime: time) { success in
//                    
//                    self.view.hideActivityHUD()
//                    self.delegate?.didUpdateResult()
//                    self.navigationController?.popToRootViewController(animated: true)
//                }
            } else {
                UserTestViewModel().updateTest(id: testDetail.id, url: imgUrl, pageType: testDetail.pageType, userId: testDetail.userId, lastResult: adjustedResult, markTime: adjustedTime) { result in
                    hideActivityHUD()
                    self.delegate?.didUpdateResult()
                    self.navigationController?.popViewController(animated: true)
                }
            }
           
        }
    }
}

extension ResultViewController: TimePickerViewDelegate {
    func didTapCancel() {
        UIView.animate(withDuration: 0.15) {
            self.pickerViewBottomContrain?.update(inset: -320)
            self.view.layoutIfNeeded()
        }
    }
    
    func didTapSave(_ time: String) {
        didTapCancel()
        hhmm = "\(time):00"
        self.testTimeView.time.setTitle(time, for: .normal)
    }
    
    func didSelect(_ time: String) {
//        editTimeButton?.setTitle(time, for: .normal)
    }
}
