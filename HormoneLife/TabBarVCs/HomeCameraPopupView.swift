//
//  HomeCameraPopupView.swift
//  HormoneLife
//
//  Created by Tank on 2024/8/2.
//

import UIKit
import SnapKit

protocol HomeCameraPopupViewDelegate: AnyObject {
    func didTapClose()
    func didTapCell(type: TestType)
}

class HomeCameraPopupView: UIView {

    weak var delegate: HomeCameraPopupViewDelegate?
    
    let titleLabel: UILabel = {
        let t = UILabel()
        t.font = .mediumGilroyFont(18)
        t.textColor = .mainTextColor
        t.text = "Choose your test"
        t.textAlignment = .center
        return t
    }()
    
    let subtitleLabel: UILabel = {
        let t = UILabel()
        t.font = .regularGilroyFont(14)
        t.textColor = .mainTextColor
        t.text = "Please select the correct test: The LH Ultra box is labeled \"Ultra Accurate Ovulation Test Kits\", and the LH box is labeled \"Ovulation Test Kits\" . Check before proceeding.\nWhen taking a photo, place the test strip on a plain background (like white paper) to help the camera focus on the strip and avoid misalignment."
        t.boldWords(words: ["\"Ultra Accurate Ovulation Test Kits\"", "\"Ovulation Test Kits\"", "plain background"], point: 0)
        t.numberOfLines = 0
        t.textAlignment = .center
        return t
    }()
    
    lazy var subtitleBgView: UIView = {
        let t = UIView()
        t.layer.masksToBounds = true
        t.layer.cornerRadius = 4
        t.layer.borderWidth = 1
        t.layer.borderColor = UIColor.mainTextColor.cgColor
        return t
    }()
    
    lazy var button: UIButton = {
        let b = UIButton(type: .custom)
        b.setImage(UIImage(named: "purpleClose"), for: .normal)
        b.addTarget(self, action: #selector(didTapClose), for: .touchUpInside)
        return b
    }()
    
    var uitraView: SamplePaperView
    var pdgView: SamplePaperView
    var fshView: SamplePaperView
    var lhView: SamplePaperView
    var hcgView: SamplePaperView
    
    convenience init(delegate: HomeCameraPopupViewDelegate?) {
        self.init()
        self.delegate = delegate
        [uitraView, pdgView, fshView, lhView, hcgView].forEach { $0.delegate = delegate }
        setupViews()
    }
    
    override init(frame: CGRect) {
        uitraView = SamplePaperView(type: .LhUltra, delegate: delegate)
        pdgView = SamplePaperView(type: .PdG, delegate: delegate)
        fshView = SamplePaperView(type: .FSH, delegate: delegate)
        lhView = SamplePaperView(type: .LH, delegate: delegate)
        hcgView = SamplePaperView(type: .HCG, delegate: delegate)
        super.init(frame: frame)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    @objc func didTapClose() {
        delegate?.didTapClose()
    }
    
    func setupViews() {
        backgroundColor = .white
        layer.shadowColor = UIColor.black.cgColor
        layer.shadowOpacity = 0.8
        layer.shadowRadius = 20
        layer.shadowOffset = CGSize(width: 0, height: 20)
        layer.masksToBounds = true
        layer.cornerRadius = 12
        
        subtitleBgView.addSubview(subtitleLabel)
        subtitleLabel.snp.makeConstraints { make in
            make.bottom.top.equalToSuperview().inset(12)
            make.leading.trailing.equalToSuperview().inset(8)
        }
        
        let stackview = UIStackView(arrangedSubviews: [titleLabel, subtitleBgView, uitraView, hcgView, pdgView, fshView])
        if !isHideLH {
            stackview.insertArrangedSubview(lhView, at: 3)
        }
        stackview.distribution = .fill
        stackview.alignment = .fill
        stackview.axis = .vertical
        stackview.spacing = 12
        
        addSubview(stackview)
        addSubview(button)
        stackview.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(24)
            make.top.equalToSuperview().inset(16)
            make.bottom.equalToSuperview().inset(30)
        }
        
        button.snp.makeConstraints { make in
            make.height.width.equalTo(20)
            make.top.equalToSuperview().inset(16)
            make.right.equalToSuperview().inset(16)
        }
    }
}


class SamplePaperView: UIView {
    var type: TestType = .LhUltra
    weak var delegate: HomeCameraPopupViewDelegate?
    
    let titleLabel: UILabel = {
        let t = UILabel()
        t.font = .mediumGilroyFont(16)
        t.textColor = .mainTextColor
        t.textAlignment = .left
        return t
    }()
    
    let typeReadTimeLabel: UILabel = {
        let t = UILabel()
        t.font = .regularGilroyFont(14)
        t.textColor = .mainTextColor
        t.numberOfLines = 0
        t.textAlignment = .center
        return t
    }()
    
    let paperView: UIImageView = {
        let v = UIImageView(frame: CGRect(x: 0, y: 0, width: 215, height: 20))
        v.contentMode = .scaleToFill
        return v
    }()
    
    lazy var button: UIButton = {
        let b = UIButton(type: .custom)
        b.frame = CGRect(x: 0, y: 0, width: 24, height: 24)
        b.setImage(UIImage(named: "purpleRightArow"), for: .normal)
        b.addTarget(self, action: #selector(didTapAction), for: .touchUpInside)
        return b
    }()
    
    let stackView: UIStackView = {
        let stackview = UIStackView()
        stackview.distribution = .fill
        stackview.alignment = .center
        stackview.axis = .horizontal
        stackview.spacing = 12
        return stackview
    }()
    
    @objc func didTapAction() {
        delegate?.didTapCell(type: self.type)
    }
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupViews()
    }
    
    convenience init(type: TestType, delegate: HomeCameraPopupViewDelegate?) {
        self.init()
        self.delegate = delegate
        self.type = type
        titleLabel.text = "\(type.homeTitle)"
        typeReadTimeLabel.text = type.homePaperReadTimeLabel
        paperView.image = UIImage(named: type.homePaperName)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func setupViews() {
        backgroundColor = .lightGrayContentColor
        layer.cornerRadius = 4
        
        button.snp.makeConstraints { make in
            make.height.width.equalTo(24)
        }
        
        stackView.addArrangedSubview(titleLabel)
        stackView.addArrangedSubview(paperView)
        stackView.addArrangedSubview(button)
        addSubview(stackView)
        addSubview(typeReadTimeLabel)
        stackView.snp.makeConstraints { make in
            make.height.equalTo(48)
            make.left.right.equalToSuperview().inset(12)
            make.top.equalToSuperview()
        }
        
        typeReadTimeLabel.snp.makeConstraints { make in
            make.height.equalTo(12)
            make.left.right.equalToSuperview().inset(12)
            make.top.equalTo(stackView.snp.bottom)
            make.bottom.equalToSuperview().inset(12)
        }
        
        self.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(didTapAction)))
    }
}
