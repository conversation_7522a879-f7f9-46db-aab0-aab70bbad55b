//
//  ChangeConceptionViewController.swift
//  HormoneLife
//
//  Created by Tank on 2024/9/14.
//

import UIKit

class ChangeConceptionViewController: BaseViewController, LineChartDemoViewDelegate {

    let viewWidth = Int(UIScreen.main.bounds.width)
    lazy var hScrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.tag = 100
//        scrollView.isPagingEnabled = true
        scrollView.isScrollEnabled = false
        scrollView.showsVerticalScrollIndicator = false
        scrollView.showsHorizontalScrollIndicator = false
        scrollView.alwaysBounceHorizontal = true
        return scrollView
    }()
    let container = UIView()
    
    lazy var searchBarView = ChartsHeaderView(type: .chartDetail)
    
    lazy var conceptionView: ChartDetailView = {
        let chart = ChartDetailView(cycleData: CycleFoldLineStatistic())
        return chart
    }()
    
    var initChartType: LineChartDemoView.ChartType = .conception
    
    var chartViews: [ChartDetailView] {
        [conceptionView]
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()

        navigationItem.title = initChartType.chartViewTitle
        view.backgroundColor = .themeColor
        setupUI()
    }
    
    private func setupUI() {
        view.addSubview(searchBarView)
        searchBarView.snp.makeConstraints { make in
            make.left.top.right.equalToSuperview().inset(20)
        }
        
        view.addSubview(hScrollView)
        hScrollView.addSubview(container)
        hScrollView.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            make.top.equalTo(searchBarView.snp.bottom).offset(16)
            make.height.equalTo(container)
        }
        
        container.snp.makeConstraints { make in
            make.edges.equalTo(hScrollView)
            make.width.greaterThanOrEqualTo(hScrollView) //要
        }
        
        for (index, chart) in chartViews.enumerated() {
            container.addSubview(chart)
            
            chart.snp.makeConstraints { make in
                make.bottom.top.equalToSuperview()
                make.width.equalTo(viewWidth) //要
                make.left.equalToSuperview().inset(index * viewWidth)
                if index == chartViews.count - 1 {
                    make.right.equalToSuperview()
                }
            }
        }
    }
}
