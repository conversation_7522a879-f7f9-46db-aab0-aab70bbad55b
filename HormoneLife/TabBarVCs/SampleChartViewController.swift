//
//  SampleChartViewController.swift
//  HormoneLife
//
//  Created by Tank on 2024/8/24.
//

import UIKit
import DGCharts

class SampleChartViewController: BaseViewController, ChartsTitleViewDelegate, UIScrollViewDelegate, LineChartDemoViewDelegate, UITableViewDelegate, UITableViewDataSource, CyclePickerViewDelegate {
    
    private lazy var titlesView: ChartsTitleView = {
        var types: [TestType] = [.LhUltra, .HCG, .PdG, .FSH]
        if !isHideLH {
            types.insert(.LH, at: 1)
        }
        let view = ChartsTitleView(types: types, useInWhere: .chartList)
        return view
    }()
    
    private var titleIndex: Int = 0 {
        didSet {
            guard titleIndex != oldValue, !stopTitleIndex else { return }
            guard let button = titlesView.viewWithTag(titleIndex + 100) as? ChartsTitleButton else { return }
            titlesView.updateBarViewContraint(sender: button)
        }
    }
    private var stopTitleIndex: Bool = false
    
    
    let searchBarView = ChartsHeaderView()
    
    lazy var cyclePickerView: CyclePickerView = {
        let pickerView = CyclePickerView(frame: CGRect(x: 0, y: 0, width: UIScreen.main.bounds.width, height: 500))
        pickerView.delegate = self
        pickerView.dataSource = cycleDurationDateOptions
        pickerView.tag = 100
        return pickerView
    }()
    
    var cycleDurationDateOptions: [String] = [] {
        didSet {
            self.searchBarView.textField.inputView = cycleDurationDateOptions.isEmpty ? nil : cyclePickerView
            self.searchBarView.isTextFieldCouldBecomeFirstResponse = !cycleDurationDateOptions.isEmpty
            
            guard self.searchBarView.textField.text == "Select Cycle",
                  let firstCycle = cycleDurationDateOptions.first else { return }
            let durationDate = firstCycle.components(separatedBy: "        ")
            self.searchBarView.textField.text = durationDate.first
            self.selectedCycle = cycleList.first
        }
    }
    
    var cycleList: [CycleSimple] = []
    var selectedCycle: CycleSimple? = nil {
        didSet {
            guard self.selectedCycle != oldValue else { return }
            self.loadLineChartData()
        }
    }
   
//    var testTypes: [TestType] = []
    var cycleStatistics: [CycleFoldLineStatistic] = []
//    {
//        didSet {
//            testTypes = cycleStatistics.map { testTypeByTypeString(typeString: $0.pageType) }
//        }
//    }
    
    private lazy var tableView: UITableView = {
        let tableView = UITableView(frame: .zero, style: .plain)
        tableView.backgroundColor = .themeColor
        tableView.separatorStyle = .none
        
        tableView.register(LineChartViewTableViewCell.self, forCellReuseIdentifier: "LineChartViewTableViewCell")

        tableView.keyboardDismissMode = .onDrag
        tableView.dataSource = self
        tableView.delegate = self
        tableView.rowHeight = UITableView.automaticDimension
        return tableView
    }()
    
    private var selectedType: TestType = .LhUltra
    let viewModel = UserTestViewModel()
    
    override func viewDidLoad() {
        super.viewDidLoad()

        navigationItem.leftBarButtonItem = nil
        navigationItem.title = "Charts"
        view.backgroundColor = .themeColor
        setupUI()
        
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        fetchCycleList()
        loadLineChartData()
    }
    
    func fetchCycleList() {
        self.cycleDurationDateOptions = []
        viewModel.getCycleSimpleList(pageType: selectedType.description) { cycleSimpleList in
            guard !cycleSimpleList.isEmpty else { return }
            self.cycleList = cycleSimpleList
            
            cycleSimpleList.forEach { cycle in
                if let fromDate = cycle.startCycleTime?.yyyyMMddHHmmss_yyyyMMdd()?.replacingOccurrences(of: "-", with: "/") {

                    var cycleRound = ""
                    if let toDate = cycle.endCycleTime?.yyyyMMddHHmmss_yyyyMMdd()?.replacingOccurrences(of: "-", with: "/") { // ?? Date().yyyyMMdd(dateFormat: "yyyy/MM/dd")
                        cycleRound = "\(fromDate)   -   \(toDate)"
                    } else {
                        cycleRound = "Current cycle"
                    }
                    
                    self.cycleDurationDateOptions.append(cycleRound)
                }
                self.cyclePickerView.dataSource = self.cycleDurationDateOptions
            }
        }
    }
    
    func loadLineChartData() {
     
        guard let cycleId = selectedCycle?.id else { return }
        self.cycleStatistics = []
        
        viewModel.cycleFoldLineStatisticAll(cycleId: cycleId) { list in
            var cList = list
            if let lhUltra = cList.first(where: { cycle in
                LineChartDemoView.chartTypeByTypeString(typeString: cycle.pageType) == .lhUltra
            }) {
                self.cycleStatistics.append(lhUltra)
            }
            
            if let lh = cList.first(where: { cycle in
                LineChartDemoView.chartTypeByTypeString(typeString: cycle.pageType) == .lh
            }) {
                if !isHideLH {
                    self.cycleStatistics.append(lh)
                }
            }
            
            if let hcg = cList.first(where: { cycle in
                LineChartDemoView.chartTypeByTypeString(typeString: cycle.pageType) == .hcg
            }) {
                self.cycleStatistics.append(hcg)
            }
            
            if let pdg = cList.first(where: { cycle in
                LineChartDemoView.chartTypeByTypeString(typeString: cycle.pageType) == .pdg
            }) {
                self.cycleStatistics.append(pdg)
            }
            
            if let fsh = cList.first(where: { cycle in
                LineChartDemoView.chartTypeByTypeString(typeString: cycle.pageType) == .fsh
            }) {
                self.cycleStatistics.append(fsh)
            }
            
            //self.cycleStatistics = list
            self.tableView.reloadData()
        }
    }
    
    func buttonDidClickWithType(type: TestType) {
        stopTitleIndex = true
        var row = type.rawValue
        if isHideLH && row > 1 {
            row -= 1
        }
        
        // 保护性检查：在尝试滚动之前确保目标行存在，防止因数据未加载完毕导致越界崩溃
        guard row >= 0, row < self.cycleStatistics.count else {
            stopTitleIndex = false
            return
        }
        
        tableView.scrollToRow(at: IndexPath(row: row, section: 0), at: .middle, animated: true)
        DispatchQueue.main.asyncAfter(deadline: DispatchTime.now()+1) {
            self.stopTitleIndex = false
        }
    }
    
    func scrollViewDidScroll(_ scrollView: UIScrollView) {
//        print(scrollView.contentOffset.y)
        if scrollView.contentOffset.y <= 237 {
            titleIndex = 0
        } else if scrollView.contentOffset.y > 237 && scrollView.contentOffset.y <= 590 {
            titleIndex = 1
        } else if scrollView.contentOffset.y > 590 && scrollView.contentOffset.y <= 920 {
            titleIndex = 2
        } else if scrollView.contentOffset.y > 920 && scrollView.contentOffset.y <= 1200 {
            titleIndex = 3
        } else {
            titleIndex = 4
        }
    }
    
    func didTapSelf(chartType: LineChartDemoView.ChartType) {
        let chartDetailVC = ChartDetailViewController()
        chartDetailVC.chartType = chartType
        hidesBottomBarWhenPushed = true
        navigationController?.pushViewController(chartDetailVC, animated: true)
        hidesBottomBarWhenPushed = false
    }

    private func setupUI() {
        titlesView.delegate = self
        view.addSubview(titlesView)
        view.addSubview(searchBarView)
        titlesView.snp.makeConstraints { make in
            make.left.trailingMargin.equalTo(5)
            make.top.equalToSuperview().inset(-10)
        }
        
        searchBarView.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(10)
            make.top.equalTo(titlesView.snp.bottom)
        }
        
        view.addSubview(tableView)
        
        tableView.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            make.top.equalTo(searchBarView.snp.bottom).offset(10)
        }
    }
    
    func didTapSaveIndex(_ index: Int) {
        didTapCancel()
        if cycleDurationDateOptions.count > index {
            let durationDate = cycleDurationDateOptions[index].components(separatedBy: "        ")
            searchBarView.textField.text = durationDate.first
        }
        
        if cycleList.count > index {
            selectedCycle = cycleList[index]
//            loadLineChartData()
        }
    }
    
    func didTapCancel() {
        searchBarView.textField.resignFirstResponder()
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        cycleStatistics.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        
        guard cycleStatistics.count > indexPath.row,
              let cell = tableView.dequeueReusableCell(withIdentifier: "LineChartViewTableViewCell", for: indexPath) as? LineChartViewTableViewCell else {
            return UITableViewCell()
        }
        
        cell.configLineViewWith(cycleData: cycleStatistics[indexPath.row], lineChartDelegate: self)
        return cell
    }
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        
        guard cycleStatistics.count > indexPath.row,
              let type = LineChartDemoView.chartTypeByTypeString(typeString: cycleStatistics[indexPath.row].pageType) else {
            return
        }
        
        didTapSelf(chartType: type)
    }
}
