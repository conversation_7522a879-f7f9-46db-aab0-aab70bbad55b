//
//  ForgotPassWordViewController.swift
//  HormoneLife
//
//  Created by Tank on 2024/5/26.
//

import UIKit

class ForgotPassWordViewController: BaseViewController {
    
    @IBOutlet weak var sendVerificationButton: UIButton!
    @IBOutlet weak var submitButton: UIButton!
    
    @IBOutlet weak var accountNum: UITextField!
    @IBOutlet weak var verificationField: UITextField!
    @IBOutlet weak var newPasswordField: UITextField!
    @IBOutlet weak var confirmPasswordField: UITextField!
    
    var timer: Timer?
    var totalTime = 61
    
    var loginType: LoginType = .phone
    
    override func viewDidLoad() {
        super.viewDidLoad()

        setupFields()
        checkLoginButtonStatus()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        navigationController?.navigationBar.isHidden = false
    }
    
    private func setupFields() {
        [accountNum, verificationField, newPassword<PERSON>ield, confirmPasswordField].forEach {
            $0.delegate = self
            $0.setAttributedPlaceholer($0.placeholder)
        }
    }
    
    private func checkLoginButtonStatus() {
        submitButton.isEnabled = !accountNum.isEmpty && !verificationField.isEmpty && !newPasswordField.isEmpty && !confirmPasswordField.isEmpty
        
        let disableColor = UIColor(red: 68/255, green: 85/255, blue: 113/255, alpha: 1)
        let enableColor = UIColor.mainTextColor.withAlphaComponent(1)
        submitButton.backgroundColor = submitButton.isEnabled ? enableColor : disableColor
    }
    
    override func viewDidDisappear(_ animated: Bool) {
        super.viewDidDisappear(animated)
        removeTimer()
    }

    @IBAction func sendVerificationButtonAction(_ sender: UIButton) {
        guard !accountNum.isEmpty else { return }
        
        func startTimer() {
            guard timer == nil else { return }
            var caculatorTime = totalTime
            timer = Timer.scheduledTimer(withTimeInterval: 1, repeats: true, block: { _ in
                caculatorTime -= 1
                self.sendVerificationButton.setTitle("|    \(caculatorTime)    ", for: .normal)
                if caculatorTime == -1 {
                    self.timer?.invalidate()
                    self.timer = nil
                    self.sendVerificationButton.setTitle("|    Send    ", for: .normal)
                }
            })
            timer?.fire()
        }
        
        loginType = (accountNum.text ?? "").contains("@") ? .email : .phone
        let sendValidation = SendValidate(account: accountNum.text, codeType: loginType, messageCodeType: .FORGET_PASSWORD)
        UserInteractor.sendValidateCode(sendValidation) { success in
            guard let s = success, s else { return }
            startTimer()
        }
    }
    
    @IBAction func submitButtonAction(_ sender: Any) {
        removeTimer()
        sendVerificationButton.setTitle("|    Send    ", for: .normal)
        
        guard let account = accountNum.text,
              let code = verificationField.text,
              let newPwt = newPasswordField.text,
              let confirmPwt = confirmPasswordField.text,
              newPwt == confirmPwt else { return }
        UserInteractor.setNewPassword(account, code: code, codeType: .email, newPassword: newPwt) { result in
            guard let success = result, success else { return }
            // TODO: popup
            self.navigationController?.popViewController(animated: true)
        }
    }
    
    private func removeTimer() {
        if timer != nil {
            timer?.invalidate()
            timer = nil
        }
    }
}

extension ForgotPassWordViewController: UITextFieldDelegate {
    func textFieldShouldReturn(_ textField: UITextField) -> Bool {
        textField.resignFirstResponder()
    }
    
    func textFieldDidChangeSelection(_ textField: UITextField) {
        checkLoginButtonStatus()
    }
}
