//
//  CreateAccountPopupViewController.swift
//  HormoneLife
//
//  Created by Tank on 2024/5/26.
//

import UIKit

protocol CreateAccountPopupViewControllerDelegate: AnyObject {
    func didTapCreateAccount()
    func didTapClosePopup()
}

class CreateAccountPopupViewController: UIViewController {
    
    weak var delegate: CreateAccountPopupViewControllerDelegate?

    override func viewDidLoad() {
        super.viewDidLoad()

        view.backgroundColor = UIColor.black.withAlphaComponent(0.6)
    }
    
    @IBAction func createAccount(_ sender: Any) {
        dismiss(animated: false)
        delegate?.didTapCreateAccount()
    }
    
    @IBAction func closeAction(_ sender: Any) {
        dismiss(animated: true) {
            self.delegate?.didTapClosePopup()
        }
    }
}
