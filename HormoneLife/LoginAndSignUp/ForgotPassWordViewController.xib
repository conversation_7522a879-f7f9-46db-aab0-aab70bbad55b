<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="21701" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21679"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="ForgotPassWordViewController" customModule="HormoneLife" customModuleProvider="target">
            <connections>
                <outlet property="accountNum" destination="x4n-gJ-Owc" id="soN-6c-71D"/>
                <outlet property="confirmPasswordField" destination="Yhn-Ii-E2z" id="BSQ-yX-jus"/>
                <outlet property="newPasswordField" destination="D2O-v8-ZOF" id="dB5-eD-KsW"/>
                <outlet property="sendVerificationButton" destination="UNQ-gF-z8c" id="GLS-3W-5iw"/>
                <outlet property="submitButton" destination="WHS-ZV-e1E" id="NaM-Il-zvL"/>
                <outlet property="verificationField" destination="nld-CX-fVv" id="wxy-fU-cLe"/>
                <outlet property="view" destination="i5M-Pr-FkT" id="sfx-zR-JGt"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="i5M-Pr-FkT">
            <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="gUU-jD-ucA">
                    <rect key="frame" x="30" y="99" width="333" height="360"/>
                    <subviews>
                        <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="equalSpacing" spacing="20" translatesAutoresizingMaskIntoConstraints="NO" id="Kbq-Gc-7Fl">
                            <rect key="frame" x="0.0" y="0.0" width="333" height="360"/>
                            <subviews>
                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="EzW-a7-msY">
                                    <rect key="frame" x="0.0" y="0.0" width="333" height="48"/>
                                    <subviews>
                                        <stackView opaque="NO" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="WZU-Gi-SLV">
                                            <rect key="frame" x="0.0" y="0.0" width="333" height="48"/>
                                            <subviews>
                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="kFZ-qb-GYK">
                                                    <rect key="frame" x="0.0" y="0.0" width="18" height="48"/>
                                                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    <constraints>
                                                        <constraint firstAttribute="width" constant="18" id="sgK-AL-rIb"/>
                                                    </constraints>
                                                    <userDefinedRuntimeAttributes>
                                                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                            <integer key="value" value="4"/>
                                                        </userDefinedRuntimeAttribute>
                                                    </userDefinedRuntimeAttributes>
                                                </view>
                                                <textField opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="248" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="Mobile/Email Number" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="x4n-gJ-Owc">
                                                    <rect key="frame" x="18" y="0.0" width="315" height="48"/>
                                                    <color key="textColor" red="0.50876241919999998" green="0.4311534762" blue="0.6075879931" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits" returnKeyType="done"/>
                                                </textField>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="kFZ-qb-GYK" firstAttribute="leading" secondItem="WZU-Gi-SLV" secondAttribute="leading" id="ALZ-YQ-H5V"/>
                                                <constraint firstAttribute="bottom" secondItem="kFZ-qb-GYK" secondAttribute="bottom" id="Wqa-RR-CLk"/>
                                                <constraint firstItem="kFZ-qb-GYK" firstAttribute="top" secondItem="WZU-Gi-SLV" secondAttribute="top" id="dMx-dz-RNs"/>
                                            </constraints>
                                        </stackView>
                                    </subviews>
                                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <constraints>
                                        <constraint firstAttribute="trailing" secondItem="WZU-Gi-SLV" secondAttribute="trailing" id="Ild-uP-l6O"/>
                                        <constraint firstAttribute="height" constant="48" id="QhC-GM-UQP"/>
                                        <constraint firstItem="WZU-Gi-SLV" firstAttribute="top" secondItem="EzW-a7-msY" secondAttribute="top" id="YeZ-zV-0ia"/>
                                        <constraint firstItem="WZU-Gi-SLV" firstAttribute="leading" secondItem="EzW-a7-msY" secondAttribute="leading" id="aG7-ng-tLg"/>
                                        <constraint firstAttribute="bottom" secondItem="WZU-Gi-SLV" secondAttribute="bottom" id="r0c-UK-Hvo"/>
                                    </constraints>
                                    <userDefinedRuntimeAttributes>
                                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                            <integer key="value" value="4"/>
                                        </userDefinedRuntimeAttribute>
                                    </userDefinedRuntimeAttributes>
                                </view>
                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="m8p-YM-BUB">
                                    <rect key="frame" x="0.0" y="68" width="333" height="48"/>
                                    <subviews>
                                        <stackView opaque="NO" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="h8Y-ch-3cB">
                                            <rect key="frame" x="0.0" y="0.0" width="333" height="48"/>
                                            <subviews>
                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="l67-hZ-3KM">
                                                    <rect key="frame" x="0.0" y="0.0" width="18" height="48"/>
                                                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    <constraints>
                                                        <constraint firstAttribute="width" constant="18" id="jLA-9O-00P"/>
                                                    </constraints>
                                                    <userDefinedRuntimeAttributes>
                                                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                            <integer key="value" value="4"/>
                                                        </userDefinedRuntimeAttribute>
                                                    </userDefinedRuntimeAttributes>
                                                </view>
                                                <textField opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="248" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="Verification code" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="nld-CX-fVv">
                                                    <rect key="frame" x="18" y="0.0" width="232" height="48"/>
                                                    <color key="textColor" red="0.50876241919999998" green="0.4311534762" blue="0.6075879931" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits" returnKeyType="done"/>
                                                </textField>
                                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="UNQ-gF-z8c">
                                                    <rect key="frame" x="250" y="0.0" width="83" height="48"/>
                                                    <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                    <state key="normal" title="|    Send    ">
                                                        <color key="titleColor" red="0.18858519200000001" green="0.057947516439999999" blue="0.3526558876" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                    </state>
                                                    <connections>
                                                        <action selector="sendVerificationButtonAction:" destination="-1" eventType="touchUpInside" id="YGG-xF-MgN"/>
                                                    </connections>
                                                </button>
                                            </subviews>
                                            <constraints>
                                                <constraint firstAttribute="bottom" secondItem="l67-hZ-3KM" secondAttribute="bottom" id="Asi-ha-9Bm"/>
                                                <constraint firstItem="l67-hZ-3KM" firstAttribute="leading" secondItem="h8Y-ch-3cB" secondAttribute="leading" id="sZx-j2-Ial"/>
                                                <constraint firstItem="l67-hZ-3KM" firstAttribute="top" secondItem="h8Y-ch-3cB" secondAttribute="top" id="xMa-0a-xMp"/>
                                            </constraints>
                                        </stackView>
                                    </subviews>
                                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <constraints>
                                        <constraint firstItem="h8Y-ch-3cB" firstAttribute="leading" secondItem="m8p-YM-BUB" secondAttribute="leading" id="8c1-UA-G7o"/>
                                        <constraint firstAttribute="trailing" secondItem="h8Y-ch-3cB" secondAttribute="trailing" id="KTX-GK-fLR"/>
                                        <constraint firstItem="h8Y-ch-3cB" firstAttribute="top" secondItem="m8p-YM-BUB" secondAttribute="top" id="UkD-Dl-tOd"/>
                                        <constraint firstAttribute="height" constant="48" id="eUx-96-6W6"/>
                                        <constraint firstAttribute="bottom" secondItem="h8Y-ch-3cB" secondAttribute="bottom" id="tu0-Eu-MBM"/>
                                    </constraints>
                                    <userDefinedRuntimeAttributes>
                                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                            <integer key="value" value="4"/>
                                        </userDefinedRuntimeAttribute>
                                    </userDefinedRuntimeAttributes>
                                </view>
                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="nxQ-uV-gh4">
                                    <rect key="frame" x="0.0" y="136" width="333" height="48"/>
                                    <subviews>
                                        <stackView opaque="NO" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="57j-hH-VOa">
                                            <rect key="frame" x="0.0" y="0.0" width="333" height="48"/>
                                            <subviews>
                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="sRx-Qi-S76">
                                                    <rect key="frame" x="0.0" y="0.0" width="18" height="48"/>
                                                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    <constraints>
                                                        <constraint firstAttribute="width" constant="18" id="HlH-vx-4S0"/>
                                                    </constraints>
                                                    <userDefinedRuntimeAttributes>
                                                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                            <integer key="value" value="4"/>
                                                        </userDefinedRuntimeAttribute>
                                                    </userDefinedRuntimeAttributes>
                                                </view>
                                                <textField opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="248" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="New Password(At least 6 characters)" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="D2O-v8-ZOF">
                                                    <rect key="frame" x="18" y="0.0" width="315" height="48"/>
                                                    <color key="textColor" red="0.50876241919999998" green="0.4311534762" blue="0.6075879931" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits" returnKeyType="done"/>
                                                </textField>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="sRx-Qi-S76" firstAttribute="top" secondItem="57j-hH-VOa" secondAttribute="top" id="0da-VP-5Wc"/>
                                                <constraint firstAttribute="bottom" secondItem="sRx-Qi-S76" secondAttribute="bottom" id="p5X-XU-8ek"/>
                                                <constraint firstItem="sRx-Qi-S76" firstAttribute="leading" secondItem="57j-hH-VOa" secondAttribute="leading" id="uij-LQ-IyG"/>
                                            </constraints>
                                        </stackView>
                                    </subviews>
                                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <constraints>
                                        <constraint firstAttribute="height" constant="48" id="Cq1-MN-bCf"/>
                                        <constraint firstItem="57j-hH-VOa" firstAttribute="leading" secondItem="nxQ-uV-gh4" secondAttribute="leading" id="cbQ-eB-560"/>
                                        <constraint firstItem="57j-hH-VOa" firstAttribute="top" secondItem="nxQ-uV-gh4" secondAttribute="top" id="e6B-cs-dxe"/>
                                        <constraint firstAttribute="trailing" secondItem="57j-hH-VOa" secondAttribute="trailing" id="feL-6q-6Bg"/>
                                        <constraint firstAttribute="bottom" secondItem="57j-hH-VOa" secondAttribute="bottom" id="wDf-YL-cyJ"/>
                                    </constraints>
                                    <userDefinedRuntimeAttributes>
                                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                            <integer key="value" value="4"/>
                                        </userDefinedRuntimeAttribute>
                                    </userDefinedRuntimeAttributes>
                                </view>
                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="xN0-xg-RpU">
                                    <rect key="frame" x="0.0" y="204" width="333" height="48"/>
                                    <subviews>
                                        <stackView opaque="NO" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="5gc-rI-LUo">
                                            <rect key="frame" x="0.0" y="0.0" width="333" height="48"/>
                                            <subviews>
                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="yj0-CJ-eMf">
                                                    <rect key="frame" x="0.0" y="0.0" width="18" height="48"/>
                                                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    <constraints>
                                                        <constraint firstAttribute="width" constant="18" id="rEn-hz-tkd"/>
                                                    </constraints>
                                                    <userDefinedRuntimeAttributes>
                                                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                            <integer key="value" value="4"/>
                                                        </userDefinedRuntimeAttribute>
                                                    </userDefinedRuntimeAttributes>
                                                </view>
                                                <textField opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="248" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="Confirm Password" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="Yhn-Ii-E2z">
                                                    <rect key="frame" x="18" y="0.0" width="315" height="48"/>
                                                    <color key="textColor" red="0.50876241919999998" green="0.4311534762" blue="0.6075879931" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits" returnKeyType="done"/>
                                                </textField>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="yj0-CJ-eMf" firstAttribute="leading" secondItem="5gc-rI-LUo" secondAttribute="leading" id="Jd9-yM-fLT"/>
                                                <constraint firstAttribute="bottom" secondItem="yj0-CJ-eMf" secondAttribute="bottom" id="K33-nc-XPb"/>
                                                <constraint firstItem="yj0-CJ-eMf" firstAttribute="top" secondItem="5gc-rI-LUo" secondAttribute="top" id="zDr-Sn-VMg"/>
                                            </constraints>
                                        </stackView>
                                    </subviews>
                                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <constraints>
                                        <constraint firstAttribute="height" constant="48" id="03K-a1-U6O"/>
                                        <constraint firstItem="5gc-rI-LUo" firstAttribute="leading" secondItem="xN0-xg-RpU" secondAttribute="leading" id="5Go-Bg-sag"/>
                                        <constraint firstAttribute="bottom" secondItem="5gc-rI-LUo" secondAttribute="bottom" id="9JV-ha-dDF"/>
                                        <constraint firstItem="5gc-rI-LUo" firstAttribute="top" secondItem="xN0-xg-RpU" secondAttribute="top" id="gCt-jR-75v"/>
                                        <constraint firstAttribute="trailing" secondItem="5gc-rI-LUo" secondAttribute="trailing" id="p7o-Jt-gff"/>
                                    </constraints>
                                    <userDefinedRuntimeAttributes>
                                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                            <integer key="value" value="4"/>
                                        </userDefinedRuntimeAttribute>
                                    </userDefinedRuntimeAttributes>
                                </view>
                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="o9d-Mx-u4o">
                                    <rect key="frame" x="0.0" y="272" width="333" height="20"/>
                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <constraints>
                                        <constraint firstAttribute="height" constant="20" id="SUZ-kc-Jza"/>
                                    </constraints>
                                </view>
                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="WHS-ZV-e1E">
                                    <rect key="frame" x="0.0" y="312" width="333" height="48"/>
                                    <color key="backgroundColor" red="0.18858519200000001" green="0.057947516439999999" blue="0.3526558876" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                    <constraints>
                                        <constraint firstAttribute="height" constant="48" id="GR7-NQ-4iI"/>
                                    </constraints>
                                    <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                    <state key="normal" title="Submit"/>
                                    <userDefinedRuntimeAttributes>
                                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                            <integer key="value" value="4"/>
                                        </userDefinedRuntimeAttribute>
                                    </userDefinedRuntimeAttributes>
                                    <connections>
                                        <action selector="submitButtonAction:" destination="-1" eventType="touchUpInside" id="AwD-uS-HrX"/>
                                    </connections>
                                </button>
                            </subviews>
                            <constraints>
                                <constraint firstAttribute="trailing" secondItem="WHS-ZV-e1E" secondAttribute="trailing" id="HV4-yr-gNw"/>
                                <constraint firstItem="WHS-ZV-e1E" firstAttribute="leading" secondItem="Kbq-Gc-7Fl" secondAttribute="leading" id="Ir8-0h-qhT"/>
                                <constraint firstAttribute="trailing" secondItem="EzW-a7-msY" secondAttribute="trailing" id="T6a-ZS-hDv"/>
                                <constraint firstItem="EzW-a7-msY" firstAttribute="top" secondItem="Kbq-Gc-7Fl" secondAttribute="top" id="WWL-iy-CLW"/>
                                <constraint firstItem="EzW-a7-msY" firstAttribute="leading" secondItem="Kbq-Gc-7Fl" secondAttribute="leading" id="dOh-UT-Ghl"/>
                                <constraint firstItem="o9d-Mx-u4o" firstAttribute="leading" secondItem="Kbq-Gc-7Fl" secondAttribute="leading" id="etu-Cu-qAs"/>
                                <constraint firstAttribute="trailing" secondItem="o9d-Mx-u4o" secondAttribute="trailing" id="mDt-sY-x9B"/>
                                <constraint firstAttribute="bottom" secondItem="WHS-ZV-e1E" secondAttribute="bottom" id="tI0-6K-v0K"/>
                            </constraints>
                        </stackView>
                    </subviews>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstAttribute="trailing" secondItem="Kbq-Gc-7Fl" secondAttribute="trailing" id="9Iu-ap-M7w"/>
                        <constraint firstAttribute="bottom" secondItem="Kbq-Gc-7Fl" secondAttribute="bottom" id="KgX-WY-niP"/>
                        <constraint firstItem="Kbq-Gc-7Fl" firstAttribute="leading" secondItem="gUU-jD-ucA" secondAttribute="leading" id="WPL-aY-t0Z"/>
                        <constraint firstItem="Kbq-Gc-7Fl" firstAttribute="top" secondItem="gUU-jD-ucA" secondAttribute="top" id="iBv-7Q-aeF"/>
                    </constraints>
                </view>
            </subviews>
            <viewLayoutGuide key="safeArea" id="fnl-2z-Ty3"/>
            <color key="backgroundColor" red="0.94169229269999999" green="0.92880314590000002" blue="0.9607291818" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
            <constraints>
                <constraint firstItem="gUU-jD-ucA" firstAttribute="top" secondItem="fnl-2z-Ty3" secondAttribute="top" constant="40" id="0gH-jf-jb9"/>
                <constraint firstItem="gUU-jD-ucA" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" constant="30" id="MIQ-4S-ftr"/>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="trailing" secondItem="gUU-jD-ucA" secondAttribute="trailing" constant="30" id="Wjs-jJ-4e7"/>
            </constraints>
            <point key="canvasLocation" x="130.53435114503816" y="-11.267605633802818"/>
        </view>
    </objects>
</document>
