<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="21701" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21679"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="Gilroy Medium.otf">
            <string>Gilroy-Medium</string>
        </array>
        <array key="gilroy bold.otf">
            <string><PERSON><PERSON>-Bold</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="CreateAccountPopupViewController" customModule="HormoneLife" customModuleProvider="target">
            <connections>
                <outlet property="view" destination="i5M-Pr-FkT" id="sfx-zR-JGt"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="i5M-Pr-FkT">
            <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="yqd-x2-E0M">
                    <rect key="frame" x="44" y="174.**************" width="305" height="428"/>
                    <subviews>
                        <imageView hidden="YES" clipsSubviews="YES" userInteractionEnabled="NO" alpha="0.*****************" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="carouselSampleImage.png" translatesAutoresizingMaskIntoConstraints="NO" id="Lpt-fH-7bL">
                            <rect key="frame" x="0.0" y="0.0" width="305" height="428"/>
                        </imageView>
                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="bottom" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="messageViewBg" translatesAutoresizingMaskIntoConstraints="NO" id="hrf-3k-rnO">
                            <rect key="frame" x="0.0" y="0.0" width="305" height="428"/>
                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        </imageView>
                        <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="center" spacing="22" translatesAutoresizingMaskIntoConstraints="NO" id="e69-wM-KnR">
                            <rect key="frame" x="32" y="60" width="241" height="368"/>
                            <subviews>
                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Welcome to" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="t9l-QK-HsN">
                                    <rect key="frame" x="71" y="0.0" width="99.333333333333314" height="30"/>
                                    <constraints>
                                        <constraint firstAttribute="height" constant="30" id="mhw-kd-XoZ"/>
                                    </constraints>
                                    <fontDescription key="fontDescription" name="Gilroy-Medium" family="Gilroy" pointSize="18"/>
                                    <color key="textColor" red="0.*****************" green="0.057947516439999999" blue="0.**********" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                    <nil key="highlightedColor"/>
                                </label>
                                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="hormoneLife" translatesAutoresizingMaskIntoConstraints="NO" id="3mR-tq-HZW">
                                    <rect key="frame" x="50.666666666666671" y="52.000000000000028" width="140" height="14"/>
                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <color key="tintColor" red="0.20423525570000001" green="0.075626514850000007" blue="0.36441689729999999" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                    <constraints>
                                        <constraint firstAttribute="width" constant="140" id="X6P-ih-wvJ"/>
                                        <constraint firstAttribute="height" constant="14" id="lj3-y0-PGu"/>
                                    </constraints>
                                </imageView>
                                <textView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" usesAttributedText="YES" translatesAutoresizingMaskIntoConstraints="NO" id="Ypb-lF-OWH">
                                    <rect key="frame" x="0.0" y="88.000000000000028" width="241" height="150"/>
                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <constraints>
                                        <constraint firstAttribute="height" constant="150" id="Pme-bZ-Kho"/>
                                    </constraints>
                                    <attributedString key="attributedText">
                                        <fragment content="You are currently in guest mode. Create an account to save your data.">
                                            <attributes>
                                                <color key="NSColor" red="0.*****************" green="0.057947516439999999" blue="0.**********" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                <font key="NSFont" size="18" name="Gilroy-Regular"/>
                                                <paragraphStyle key="NSParagraphStyle" alignment="center" lineBreakMode="wordWrapping" baseWritingDirection="natural" lineHeightMultiple="1.5" tighteningFactorForTruncation="0.0"/>
                                            </attributes>
                                        </fragment>
                                    </attributedString>
                                    <textInputTraits key="textInputTraits" autocapitalizationType="sentences"/>
                                </textView>
                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="JIO-0J-2lx">
                                    <rect key="frame" x="0.0" y="260" width="241" height="108"/>
                                    <subviews>
                                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="UX1-Mh-5bt">
                                            <rect key="frame" x="20.666666666666671" y="0.0" width="200" height="48"/>
                                            <color key="backgroundColor" red="0.*****************" green="0.057947516439999999" blue="0.**********" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                            <constraints>
                                                <constraint firstAttribute="width" constant="200" id="51Y-iR-nj4"/>
                                                <constraint firstAttribute="height" constant="48" id="eSj-Ub-daS"/>
                                            </constraints>
                                            <fontDescription key="fontDescription" name="Gilroy-Bold" family="Gilroy" pointSize="16"/>
                                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                            <state key="normal" title="Create Account"/>
                                            <userDefinedRuntimeAttributes>
                                                <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                    <integer key="value" value="8"/>
                                                </userDefinedRuntimeAttribute>
                                            </userDefinedRuntimeAttributes>
                                            <connections>
                                                <action selector="createAccount:" destination="-1" eventType="touchUpInside" id="mJX-qB-wjD"/>
                                            </connections>
                                        </button>
                                    </subviews>
                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <constraints>
                                        <constraint firstItem="UX1-Mh-5bt" firstAttribute="top" secondItem="JIO-0J-2lx" secondAttribute="top" id="HCC-nJ-FES"/>
                                        <constraint firstAttribute="width" constant="241" id="JoV-Jc-38I"/>
                                        <constraint firstItem="UX1-Mh-5bt" firstAttribute="centerX" secondItem="JIO-0J-2lx" secondAttribute="centerX" id="Rvu-61-oqw"/>
                                        <constraint firstAttribute="height" constant="108" id="Zr2-Zm-0NR"/>
                                    </constraints>
                                </view>
                            </subviews>
                            <constraints>
                                <constraint firstAttribute="bottom" secondItem="JIO-0J-2lx" secondAttribute="bottom" id="cw5-Kw-V94"/>
                                <constraint firstItem="Ypb-lF-OWH" firstAttribute="leading" secondItem="e69-wM-KnR" secondAttribute="leading" id="lVs-7y-a5W"/>
                                <constraint firstAttribute="trailing" secondItem="Ypb-lF-OWH" secondAttribute="trailing" id="xc0-n4-iHz"/>
                            </constraints>
                        </stackView>
                    </subviews>
                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstItem="hrf-3k-rnO" firstAttribute="leading" secondItem="yqd-x2-E0M" secondAttribute="leading" id="0gf-DK-yZr"/>
                        <constraint firstItem="hrf-3k-rnO" firstAttribute="top" secondItem="yqd-x2-E0M" secondAttribute="top" id="1Pi-Oj-jxd"/>
                        <constraint firstAttribute="trailing" secondItem="Lpt-fH-7bL" secondAttribute="trailing" id="6KO-GZ-Va1"/>
                        <constraint firstItem="Lpt-fH-7bL" firstAttribute="leading" secondItem="yqd-x2-E0M" secondAttribute="leading" id="9yq-05-mqa"/>
                        <constraint firstAttribute="bottom" secondItem="hrf-3k-rnO" secondAttribute="bottom" id="O0P-tp-b4e"/>
                        <constraint firstItem="e69-wM-KnR" firstAttribute="leading" secondItem="yqd-x2-E0M" secondAttribute="leading" constant="32" id="U7R-6P-MNz"/>
                        <constraint firstAttribute="bottom" secondItem="e69-wM-KnR" secondAttribute="bottom" id="VvO-6U-gGA"/>
                        <constraint firstAttribute="trailing" secondItem="hrf-3k-rnO" secondAttribute="trailing" id="WtD-gA-NWo"/>
                        <constraint firstAttribute="bottom" secondItem="Lpt-fH-7bL" secondAttribute="bottom" id="aqi-dB-Pwr"/>
                        <constraint firstItem="e69-wM-KnR" firstAttribute="top" secondItem="yqd-x2-E0M" secondAttribute="top" constant="60" id="fLK-3T-irm"/>
                        <constraint firstAttribute="trailing" secondItem="e69-wM-KnR" secondAttribute="trailing" constant="32" id="fcg-dc-n4S"/>
                        <constraint firstItem="Lpt-fH-7bL" firstAttribute="top" secondItem="yqd-x2-E0M" secondAttribute="top" id="kx9-jf-DVd"/>
                    </constraints>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                            <integer key="value" value="8"/>
                        </userDefinedRuntimeAttribute>
                    </userDefinedRuntimeAttributes>
                </view>
                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="cIr-Vv-LdS">
                    <rect key="frame" x="183.66666666666666" y="624.**************" width="26" height="26"/>
                    <constraints>
                        <constraint firstAttribute="width" constant="26" id="3t7-Qq-QKC"/>
                        <constraint firstAttribute="height" constant="26" id="64O-v2-Zxs"/>
                    </constraints>
                    <state key="normal" title="Button"/>
                    <buttonConfiguration key="configuration" style="plain" image="closeIcon" title=" "/>
                    <connections>
                        <action selector="closeAction:" destination="-1" eventType="touchUpInside" id="9dg-Aq-Rr0"/>
                    </connections>
                </button>
            </subviews>
            <viewLayoutGuide key="safeArea" id="fnl-2z-Ty3"/>
            <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstItem="yqd-x2-E0M" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" constant="44" id="5V0-11-oyc"/>
                <constraint firstItem="cIr-Vv-LdS" firstAttribute="centerX" secondItem="yqd-x2-E0M" secondAttribute="centerX" id="abd-3c-YK5"/>
                <constraint firstItem="cIr-Vv-LdS" firstAttribute="top" secondItem="yqd-x2-E0M" secondAttribute="bottom" constant="22" id="jF8-pA-3on"/>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="trailing" secondItem="yqd-x2-E0M" secondAttribute="trailing" constant="44" id="kqe-ec-VO6"/>
                <constraint firstItem="yqd-x2-E0M" firstAttribute="centerY" secondItem="fnl-2z-Ty3" secondAttribute="centerY" constant="-50" id="r4q-YN-sr0"/>
            </constraints>
            <point key="canvasLocation" x="130.53435114503816" y="-11.267605633802818"/>
        </view>
    </objects>
    <resources>
        <image name="carouselSampleImage.png" width="1125" height="600"/>
        <image name="closeIcon" width="32" height="32"/>
        <image name="hormoneLife" width="140" height="16"/>
        <image name="messageViewBg" width="514" height="514"/>
    </resources>
</document>
