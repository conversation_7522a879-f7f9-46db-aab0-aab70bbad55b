//
//  LoginViewController.swift
//  HormoneLife
//
//  Created by Tank on 2024/5/25.
//

import UIKit
import AuthenticationServices
import FBSDKCoreKit
import FBSDKLoginKit
import FBSDKCoreKit_Basics
import GoogleSignIn
import FirebaseCore
import GoogleUtilities
import CryptoKit
import FirebaseAuth

class LoginViewController: BaseViewController, LoginButtonDelegate {
    func loginButton(_ loginButton: FBSDKLoginKit.FBLoginButton, didCompleteWith result: FBSDKLoginKit.LoginManagerLoginResult?, error: (Error)?) {
        if let error = error {
            print(error.localizedDescription)
            return
        }
    }
    
    func loginButtonDidLogOut(_ loginButton: FBSDKLoginKit.FBLoginButton) {
        print("loginButtonDidLogOut")
    }
    
    fileprivate var currentNonce: String?
    
    @IBOutlet weak var loginSelectedContainView: UIView!
    @IBOutlet weak var loginSelectedView: UIView!
    @IBOutlet weak var loginSelectedViewLeadingContraint: NSLayoutConstraint!
    
    @IBOutlet weak var countryCodeButton: UIButton!
    @IBOutlet weak var accountTextField: UITextField!
    @IBOutlet weak var pwdTextField: UITextField!
    @IBOutlet weak var rememberAcountBtn: UIButton!
    @IBOutlet weak var errorView: UIView!
    @IBOutlet weak var errorLabel: UILabel!
    @IBOutlet weak var loginButton: UIButton!
    @IBOutlet weak var tandcCheckBoxButton: UIButton!
    @IBOutlet weak var signUpNow: UILabel!
    @IBOutlet weak var privatePoliceLabel: UIButton!
    @IBOutlet weak var termsOfConditionLabel: UIButton!
    
    @IBOutlet weak var facebookBgView: UIView!
    @IBOutlet weak var facebookLoginBtn: UIButton!
    @IBOutlet weak var appleLoginBtn: UIButton!
    
    
    var loginType: LoginType = .phone {
        didSet {
            // 保存当前输入的内容到对应的临时变量
            saveCurrentAccountInput()

            countryCodeButton.isHidden = loginType == .email
            accountTextField.keyboardType = loginType == .email ? .emailAddress : .numberPad
            accountTextField.setAttributedPlaceholer(loginType.accountFieldPlaceholder)

            // 根据新的登录类型加载对应的账号信息
            loadAccountForCurrentType()

            checkLoginButtonStatus()
        }
    }

    // 临时保存当前输入的账号和密码
    private var tempEmailAccount: String = ""
    private var tempEmailPassword: String = ""
    private var tempPhoneAccount: String = ""
    private var tempPhonePassword: String = ""
    
    var grantType: GrantType = .password

    override func viewDidLoad() {
        super.viewDidLoad()

        facebookBgView.isHidden = false
        navigationController?.navigationBar.isHidden = true
        setupUI()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        navigationController?.navigationBar.isHidden = true
    }
    
    private func setupUI() {
        signUpNow.underlineWords()
        privatePoliceLabel.titleLabel?.underlineWords()
        termsOfConditionLabel.titleLabel?.underlineWords()

        accountTextField.delegate = self
        pwdTextField.delegate = self
        accountTextField.setAttributedPlaceholer()
        pwdTextField.setAttributedPlaceholer()

        checkLoginButtonStatus()

        // 加载保存的账号信息
        loadSavedAccountInfo()

        // 根据当前登录类型加载对应的账号信息
        loadAccountForCurrentType()
     
        //
        
        //        // TODO: hardcode account
//        accountTextField.text = "***********"
//        pwdTextField.text = "123456"
////
//        #if DEDUG

//        #endif

    }

    // MARK: - 账号信息管理方法

    /// 加载保存的账号信息到临时变量
    private func loadSavedAccountInfo() {
        // 加载邮箱账号信息
        if let emailAccount = UserDefaults.standard.rememberEmailAccount {
            tempEmailAccount = emailAccount
        }
        if let emailPassword = UserDefaults.standard.rememberEmailPassword {
            tempEmailPassword = emailPassword
        }

        // 加载手机号码账号信息
        if let phoneAccount = UserDefaults.standard.rememberPhoneAccount {
            tempPhoneAccount = phoneAccount
        }
        if let phonePassword = UserDefaults.standard.rememberPhonePassword {
            tempPhonePassword = phonePassword
        }

        // 兼容旧版本的保存方式
        if let oldAccount = UserDefaults.standard.rememberAccount, oldAccount.count > 0 {
            if oldAccount.contains("@") {
                // 是邮箱账号
                if tempEmailAccount.isEmpty {
                    tempEmailAccount = oldAccount
                }
                if let oldPassword = UserDefaults.standard.rememberPassword, tempEmailPassword.isEmpty {
                    tempEmailPassword = oldPassword
                }
                // 自动切换到邮箱模式
                loginType = .email
            } else {
                // 是手机号码
                if tempPhoneAccount.isEmpty {
                    tempPhoneAccount = oldAccount
                }
                if let oldPassword = UserDefaults.standard.rememberPassword, tempPhonePassword.isEmpty {
                    tempPhonePassword = oldPassword
                }
            }
        }

        // 检查是否有保存的账号信息来设置记住密码按钮状态
        let hasEmailInfo = !tempEmailAccount.isEmpty && !tempEmailPassword.isEmpty
        let hasPhoneInfo = !tempPhoneAccount.isEmpty && !tempPhonePassword.isEmpty
        rememberAcountBtn.isSelected = hasEmailInfo || hasPhoneInfo
    }

    /// 根据当前登录类型加载对应的账号信息
    private func loadAccountForCurrentType() {
        if loginType == .email {
            accountTextField.text = tempEmailAccount
            pwdTextField.text = tempEmailPassword
        } else {
            // 手机模式下，检查保存的手机号码是否只包含数字
            let phoneAccount = tempPhoneAccount
            let isValidPhoneNumber = phoneAccount.allSatisfy { $0.isNumber }

            if isValidPhoneNumber && phoneAccount.count <= 10 {
                accountTextField.text = phoneAccount
                pwdTextField.text = tempPhonePassword
            } else {
                // 如果保存的不是有效的手机号码，则清空
                accountTextField.text = ""
                pwdTextField.text = ""
            }
        }
    }

    /// 保存当前输入的账号信息到临时变量
    private func saveCurrentAccountInput() {
        let currentAccount = accountTextField.text ?? ""
        let currentPassword = pwdTextField.text ?? ""

        if loginType == .email {
            tempEmailAccount = currentAccount
            tempEmailPassword = currentPassword
        } else {
            tempPhoneAccount = currentAccount
            tempPhonePassword = currentPassword
        }
    }

    @objc func handleAuthorizationAppleIDButtonPress() {
        let appleIDProvider = ASAuthorizationAppleIDProvider()
        let request = appleIDProvider.createRequest()
        request.requestedScopes = [.fullName, .email]

        let authorizationController = ASAuthorizationController(authorizationRequests: [request])

        authorizationController.delegate = self
        authorizationController.presentationContextProvider = self

        authorizationController.performRequests()
    }
    
    private func checkLoginButtonStatus() {
        if loginType == .phone {
            accountTextField.keyboardType = .numberPad
            loginButton.isEnabled = !accountTextField.isEmpty && !pwdTextField.isEmpty
        } else {
            accountTextField.keyboardType = .emailAddress
            loginButton.isEnabled = !accountTextField.isEmpty && !pwdTextField.isEmpty && (accountTextField.text ?? "").contains("@")
        }
        
        let disableColor = UIColor.mainTextColor.withAlphaComponent(0.3)
        let enableColor = UIColor.mainTextColor.withAlphaComponent(1)
        
        loginButton.backgroundColor = loginButton.isEnabled && tandcCheckBoxButton.isSelected ? enableColor : disableColor
    }
    
    func errorLabelShow(with text: String = "Username or Password Incorrect") {
        errorLabel.text = text
        errorView.isHidden = false
        DispatchQueue.main.asyncAfter(deadline: .now() + 2) { [weak self] in
            self?.errorView.isHidden = true
        }
    }

    @IBAction func mobileButtonAction(_ sender: Any) {
        loginType = .phone
        UIView.animate(withDuration: 0.35) {
            self.loginSelectedViewLeadingContraint.constant = 11
            self.loginSelectedContainView.layoutIfNeeded()
        }
    }
    
    @IBAction func emailButtonAction(_ sender: Any) {
        loginType = .email
        UIView.animate(withDuration: 0.35) {
            self.loginSelectedViewLeadingContraint.constant = 162
            self.loginSelectedContainView.layoutIfNeeded()
        }
    }
    
    @IBAction func countryCodeAction(_ sender: Any) {
    }
    
    @IBAction func pwdVisibleButtonAction(_ sender: UIButton) {
        sender.isSelected = !sender.isSelected
        pwdTextField.isSecureTextEntry = !sender.isSelected
    }
    
    @IBAction func rememberMeCheckBtnAction(_ sender: UIButton) {
        sender.isSelected = !sender.isSelected

        if sender.isSelected && !accountTextField.isEmpty && !pwdTextField.isEmpty {
            // 根据当前登录类型分别保存账号信息
            if loginType == .email {
                UserDefaults.standard.rememberEmailAccount = accountTextField.text
                UserDefaults.standard.rememberEmailPassword = pwdTextField.text
                tempEmailAccount = accountTextField.text ?? ""
                tempEmailPassword = pwdTextField.text ?? ""
            } else {
                UserDefaults.standard.rememberPhoneAccount = accountTextField.text
                UserDefaults.standard.rememberPhonePassword = pwdTextField.text
                tempPhoneAccount = accountTextField.text ?? ""
                tempPhonePassword = pwdTextField.text ?? ""
            }

            // 兼容旧版本，同时保存到旧的key
            UserDefaults.standard.rememberAccount = accountTextField.text
            UserDefaults.standard.rememberPassword = pwdTextField.text
        } else {
            // 清除所有保存的账号信息
            UserDefaults.standard.removeObject(forKey: rememberAccountKey)
            UserDefaults.standard.removeObject(forKey: rememberPasswordKey)
            UserDefaults.standard.rememberEmailAccount = nil
            UserDefaults.standard.rememberEmailPassword = nil
            UserDefaults.standard.rememberPhoneAccount = nil
            UserDefaults.standard.rememberPhonePassword = nil
            UserDefaults.standard.synchronize()

            // 清除临时变量
            tempEmailAccount = ""
            tempEmailPassword = ""
            tempPhoneAccount = ""
            tempPhonePassword = ""
        }
    }
    
    @IBAction func loginButtonAction(_ sender: UIButton) {
        view.endEditing(true)
        
        guard let account = accountTextField.text,
              let password = pwdTextField.text,
              password.isValidPassword() else {
            
            errorLabelShow()
            return
        }
        
        if loginType == .email {
            guard (accountTextField.text ?? "").contains("@") else {
                errorLabelShow(with: "Email incorrect")
                return
            }
        }
        
        guard tandcCheckBoxButton.isSelected else {
            errorLabelShow(with: "Please access the term and condition fisrt")
            return
        }
        
        UserInteractor.userLogin(username: account, password: password, grantType: grantType) { result in
            guard let token = result else {
                self.errorLabelShow()
                return
            }
            UserDefaults.standard.userToken = token
            hl_fetchUserInfo({
                guard let user = Interface.shared().loggedInUser?.userInfo, user.userBusinessConfigVO.menstruationCycleAvg > 0 else {
                    UserDefaults.standard.hasSeemGuidelinePage = true
                    UserDefaults.standard.isLoginAsGuestWithoutAccount = false
                    UserDefaults.standard.isFristTimeChooseGoal = true
                    currentWindow?.rootViewController = BaseNavigationController(rootViewController: GoalSettingViewController())
                    return
                }
                UserDefaults.standard.hasSeemGuidelinePage = true
                UserDefaults.standard.isLoginAsGuestWithoutAccount = false
                UserDefaults.standard.userToken = token
                currentWindow?.rootViewController = TabBarController()
            })
        }
    }
    
    @IBAction func faceIDAction(_ sender: Any) {
    }
    
    @IBAction func forgotPwdAction(_ sender: Any) {
        let forgotPwdVC = ForgotPassWordViewController()
        forgotPwdVC.title = "Forgot Password"
        navigationController?.pushViewController(forgotPwdVC, animated: true)
    }
    
    @IBAction func signUpNowButtonAction(_ sender: Any) {
        let signUpVC = SignUpViewController()
        navigationController?.pushViewController(signUpVC, animated: true)
    }
    
    @IBAction func loginAsGuestButtonAction(_ sender: Any) {
        UserInteractor.registerByTourist() { token in
            guard let token = token else { return }
            UserDefaults.standard.userToken = token
            
//            if DEBUG {
                // TODO: open this line once to debug
                //currentWindow?.rootViewController = TabBarController()
//            } else {
                
                // TODO: open this three lines once to be ready release
                UserDefaults.standard.isFristTimeChooseGoal = true
                UserDefaults.standard.isLoginAsGuestWithoutAccount = true
                let roleSwitchVC = GoalSettingViewController()
                self.navigationController?.pushViewController(roleSwitchVC, animated: true)
//            }
        }
    }
    
    @IBAction func appleLoginAction(_ sender: Any) {
        guard tandcCheckBoxButton.isSelected else {
            errorLabelShow(with: "Please access the term and condition fisrt")
            return
        }
        let nonce = randomNonceString()
          currentNonce = nonce
        let appleIDProvider = ASAuthorizationAppleIDProvider()
        let request = appleIDProvider.createRequest()
        request.requestedScopes = [.fullName, .email]
        request.nonce = sha256(nonce)

        let authorizationController = ASAuthorizationController(authorizationRequests: [request])

        authorizationController.delegate = self
        authorizationController.presentationContextProvider = self
        
        authorizationController.performRequests()
    }
    
    @IBAction func googleLoginAction(_ sender: Any) {
        guard tandcCheckBoxButton.isSelected else {
            errorLabelShow(with: "Please access the term and condition fisrt")
            return
        }
        
        guard let clientID = FirebaseApp.app()?.options.clientID else { return }
        
        let config = GIDConfiguration(clientID: clientID)
        GIDSignIn.sharedInstance.configuration = config
        
        GIDSignIn.sharedInstance.signIn(withPresenting: self) { [unowned self] result, error in
            guard error == nil else {
                return
            }
            
            guard let user = result?.user,
                  let idToken = user.idToken?.tokenString else {
                showToachMessage(message: "Sign in with Google fail")
                return
            }
            
            let credential = GoogleAuthProvider.credential(withIDToken: idToken,
                                                           accessToken: user.accessToken.tokenString)
            Auth.auth().signIn(with: credential) { result, error in
                guard let user = result?.user, let email = user.email else {
                    showToachMessage(message: "Sign in with Google fail")
                    return
                }
                UserInteractor.userLogin(username: email, password: "", grantType: .google,uid: user.uid, googleAccount: email) { result in
                    guard let token = result else {
                        self.errorLabelShow()
                        return
                    }
                    UserDefaults.standard.userToken = token
                    hl_fetchUserInfo({
                        guard let user = Interface.shared().loggedInUser?.userInfo, user.userBusinessConfigVO.menstruationCycleAvg > 0 else {
                            UserDefaults.standard.hasSeemGuidelinePage = true
                            UserDefaults.standard.isLoginAsGuestWithoutAccount = false
                            UserDefaults.standard.isFristTimeChooseGoal = true
                            currentWindow?.rootViewController = BaseNavigationController(rootViewController: GoalSettingViewController())
                            return
                        }
                        UserDefaults.standard.hasSeemGuidelinePage = true
                        UserDefaults.standard.isLoginAsGuestWithoutAccount = false
                        UserDefaults.standard.userToken = token
                        currentWindow?.rootViewController = TabBarController()
                    })
                }
              // At this point, our user is signed in
            }


//            let credential = GoogleAuthProvider.credential(withIDToken: idToken, accessToken: user.accessToken.tokenString)
            print("")
          // ...
        }
        
//        GIDSignIn.sharedInstance.signIn(withPresenting: self)
    }
    
    @IBAction func facebookLoginAction(_ sender: UIButton) {
        guard tandcCheckBoxButton.isSelected else {
            errorLabelShow(with: "Please access the term and condition fisrt")
            return
        }
        
        let login = LoginManager.init()
        login.logIn(permissions: ["public_profile", "email"], from: self) { (result, error) in
            print("result = \(result), error = \(error)")
            if let res = result, res.isCancelled {
                print("取消登录")
            } else {
                showActivityHUD()
                let credential = FacebookAuthProvider
                  .credential(withAccessToken: AccessToken.current!.tokenString)
                Auth.auth().signIn(with: credential) { (authResult, error) in
                    if (error != nil) {
                        // Error. If error.code == .MissingOrInvalidNonce, make sure
                        // you're sending the SHA256-hashed nonce as a hex string with
                        // your request to Apple.
                        print(error?.localizedDescription ?? "")
                        hideActivityHUD()
                        showToachMessage(message: "Sign in with Facebook fail")
                        return
                    }
                    guard let user = authResult?.user, let email = user.email else {
                        hideActivityHUD()
                        showToachMessage(message: "Sign in with Facebook fail")
                        return
                    }
                    UserInteractor.userLogin(username: email, password: "", grantType: .facebook, uid: user.uid, appleAccount: email) { result in
                        guard let token = result else {
                            hideActivityHUD()
                            self.errorLabelShow()
                            return
                        }
                        hideActivityHUD()
                        UserDefaults.standard.userToken = token
                        hl_fetchUserInfo({
                            guard let user = Interface.shared().loggedInUser?.userInfo, user.userBusinessConfigVO.menstruationCycleAvg > 0 else {
                                UserDefaults.standard.hasSeemGuidelinePage = true
                                UserDefaults.standard.isLoginAsGuestWithoutAccount = false
                                UserDefaults.standard.isFristTimeChooseGoal = true
                                currentWindow?.rootViewController = BaseNavigationController(rootViewController: GoalSettingViewController())
                                return
                            }
                            UserDefaults.standard.hasSeemGuidelinePage = true
                            UserDefaults.standard.isLoginAsGuestWithoutAccount = false
                            UserDefaults.standard.userToken = token
                            currentWindow?.rootViewController = TabBarController()
                        })
                    }
                }
            }
        }
    }
    
    @IBAction func tAndcCheckBoxAction(_ sender: UIButton) {
        let popup = TandCPopupViewController()
        popup.delegate = self
        popup.modalPresentationStyle = .overFullScreen
        self.navigationController?.pushViewController(popup, animated: false)
    }
    
    @IBAction func privatePolicyAction(_ sender: Any) {
        AppInfoInteractor.fetchAppInfo(2) { result in
            guard let appInfo = result else { return }
            let webView = WebViewController(appInfo.url, contentString: appInfo.content)
            webView.title = "Privacy Policy"
            webView.isTransparent = true
            self.navigationController?.pushViewController(webView, animated: true)
        }
    }
    
    @IBAction func termsAction(_ sender: Any) {
        AppInfoInteractor.fetchAppInfo(3) { result in
            guard let appInfo = result else { return }
            let webView = WebViewController(appInfo.url, contentString: appInfo.content)
            webView.title = "Terms of Conditions"
            webView.isTransparent = true
            self.navigationController?.pushViewController(webView, animated: true)
        }
    }
    
    private func randomNonceString(length: Int = 32) -> String {
      precondition(length > 0)
      var randomBytes = [UInt8](repeating: 0, count: length)
      let errorCode = SecRandomCopyBytes(kSecRandomDefault, randomBytes.count, &randomBytes)
      if errorCode != errSecSuccess {
        fatalError(
          "Unable to generate nonce. SecRandomCopyBytes failed with OSStatus \(errorCode)"
        )
      }

      let charset: [Character] =
        Array("0123456789ABCDEFGHIJKLMNOPQRSTUVXYZabcdefghijklmnopqrstuvwxyz-._")

      let nonce = randomBytes.map { byte in
        // Pick a random character from the set, wrapping around if needed.
        charset[Int(byte) % charset.count]
      }

      return String(nonce)
    }

    private func sha256(_ input: String) -> String {
        let inputData = Data(input.utf8)
        let hashedData = SHA256.hash(data: inputData)
        let hashString = hashedData.compactMap {
            String(format: "%02x", $0)
        }.joined()
        
        return hashString
    }

        
    
//    func loginButton(_ loginButton: FBLoginButton, didCompleteWith result: LoginManagerLoginResult?, error: Error?) {
//        
//        if result!.isCancelled {
//            print("取消登录")
//        } else {
//            print("loginButton")
//        }
//    }
//    
//    func loginButtonDidLogOut(_ loginButton: FBLoginButton) {
//        print("loginButtonDidLogOut")
//    }
    
    //custom button
//    let login = LoginManager.init()
//    login.logIn(permissions: ["public_profile", "email"], from: self) { (result, error) in
//
//    }
}

extension LoginViewController: UITextFieldDelegate {
    func textFieldShouldReturn(_ textField: UITextField) -> Bool {
        textField.resignFirstResponder()
    }
    
    func textFieldDidChangeSelection(_ textField: UITextField) {
        checkLoginButtonStatus()
    }
    
    func textField(_ textField: UITextField, shouldChangeCharactersIn range: NSRange, replacementString string: String) -> Bool {
        // 只对手机号码登录模式下的账号输入框进行限制
        guard loginType == .phone && textField == accountTextField else { return true }

        let currentText = textField.text ?? ""
        let prospectiveText = (currentText as NSString).replacingCharacters(in: range, with: string)

        // 长度限制：不超过10位
        guard prospectiveText.count <= 10 else { return false }

        // 字符限制：只允许数字
        let allowedCharacters = CharacterSet.decimalDigits
        let characterSet = CharacterSet(charactersIn: string)
        return allowedCharacters.isSuperset(of: characterSet)
    }
}

extension LoginViewController: TandCPopupViewControllerDelegate {
    func didTapDisagree() {
        tandcCheckBoxButton.isSelected = false
        checkLoginButtonStatus()
    }
    
    func didTapAgree() {
        tandcCheckBoxButton.isSelected = true
        checkLoginButtonStatus()
    }
}

extension LoginViewController:ASAuthorizationControllerDelegate, ASAuthorizationControllerPresentationContextProviding {
    /// MARK: ASAuthorizationControllerDelegate
    func authorizationController(controller: ASAuthorizationController, didCompleteWithAuthorization authorization: ASAuthorization) {
        
        if let appleIDCredential = authorization.credential as? ASAuthorizationAppleIDCredential {
            guard let nonce = currentNonce else {
                fatalError("Invalid state: A login callback was received, but no login request was sent.")
            }
            guard let appleIDToken = appleIDCredential.identityToken else {
                print("Unable to fetch identity token")
                return
            }
            guard let idTokenString = String(data: appleIDToken, encoding: .utf8) else {
                print("Unable to serialize token string from data: \(appleIDToken.debugDescription)")
                return
            }
            // Initialize a Firebase credential, including the user's full name.
            let credential = OAuthProvider.appleCredential(withIDToken: idTokenString,
                                                           rawNonce: nonce,
                                                           fullName: appleIDCredential.fullName)
            // Sign in with Firebase.
            Auth.auth().signIn(with: credential) { (authResult, error) in
                if (error != nil) {
                    // Error. If error.code == .MissingOrInvalidNonce, make sure
                    // you're sending the SHA256-hashed nonce as a hex string with
                    // your request to Apple.
                    print(error?.localizedDescription ?? "")
                    showToachMessage(message: "Sign in with Apple fail")
                    return
                }
                guard let user = authResult?.user, let email = user.email else {
                    showToachMessage(message: "Sign in with Apple fail")
                    return
                }
                UserInteractor.userLogin(username: email, password: "", grantType: .apple, uid: user.uid, appleAccount: email) { result in
                    guard let token = result else {
                        self.errorLabelShow()
                        return
                    }
                    UserDefaults.standard.userToken = token
                    hl_fetchUserInfo({
                        guard let user = Interface.shared().loggedInUser?.userInfo, user.userBusinessConfigVO.menstruationCycleAvg > 0 else {
                            UserDefaults.standard.hasSeemGuidelinePage = true
                            UserDefaults.standard.isLoginAsGuestWithoutAccount = false
                            UserDefaults.standard.isFristTimeChooseGoal = true
                            currentWindow?.rootViewController = BaseNavigationController(rootViewController: GoalSettingViewController())
                            return
                        }
                        UserDefaults.standard.hasSeemGuidelinePage = true
                        UserDefaults.standard.isLoginAsGuestWithoutAccount = false
                        UserDefaults.standard.userToken = token
                        currentWindow?.rootViewController = TabBarController()
                    })
                }
            }
        }
    }

    func authorizationController(controller: ASAuthorizationController, didCompleteWithError error: Error) {
        // Handle error.
    }

    /// MARK: ASAuthorizationControllerPresentationContextProviding
    func presentationAnchor(for controller: ASAuthorizationController) -> ASPresentationAnchor {
        return self.view.window!
    }
}

