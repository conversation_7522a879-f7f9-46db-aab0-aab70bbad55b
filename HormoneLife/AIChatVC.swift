//
//  AIChatVC.swift
//  HormoneLife
//
//  Created by yongsheng ye on 2025/7/23.
//  Copyright © 2025 HormoneLife. All rights reserved.
//

import UIKit
// 若使用CocoaPods，添加下面这行导入:
// import IQKeyboardManagerSwift

class AIChatVC: UIViewController {
    
    // MARK: - Properties
    private let tableView = UITableView()
    private let inputContainerView = UIView()
    private let messageInputField = UITextField()
    private let sendButton = UIButton()
    private var messages: [(sender: String, content: String, timestamp: Date)] = []
    private let apiURL = "https://w7uwzkoiuf.execute-api.us-west-2.amazonaws.com/prod/hormonelife-chat"
    
    // MARK: - Cell Identifiers
    private let userCellIdentifier = "UserMessageCell"
    private let aiCellIdentifier = "AIMessageCell"
    
    // MARK: - Lifecycle Methods
    override func viewDidLoad() {
        super.viewDidLoad()
        setupNavigationBar()
        setupUI()
        setupConstraints()
        setupTableView()
        setupKeyboardHandling()
        addInitialGreeting()
    }
    
    // MARK: - Navigation Setup
    private func setupNavigationBar() {
        title = "Ask a question"
        // 如果需要自定义导航栏外观，可以在这里添加
    }
    
    // MARK: - UI Setup
    private func setupUI() {
        view.backgroundColor = .systemBackground
        
        // Input container setup
        inputContainerView.backgroundColor = .white
        inputContainerView.layer.shadowColor = UIColor.black.cgColor
        inputContainerView.layer.shadowOffset = CGSize(width: 0, height: -2)
        inputContainerView.layer.shadowOpacity = 0.05
        inputContainerView.layer.shadowRadius = 3
        
        // Input field setup
        messageInputField.placeholder = "Type a message..." // 改为英文提示
        messageInputField.borderStyle = .roundedRect
        messageInputField.backgroundColor = UIColor(white: 0.95, alpha: 1.0)
        messageInputField.returnKeyType = .send
        messageInputField.delegate = self
        
        // Send button setup
        sendButton.setImage(UIImage(named: "sendIcon"), for: .normal) // 使用系统自带的sendIcon图标
        sendButton.addTarget(self, action: #selector(sendMessage), for: .touchUpInside)
        
        // Add subviews
        view.addSubview(tableView)
        view.addSubview(inputContainerView)
        inputContainerView.addSubview(messageInputField)
        inputContainerView.addSubview(sendButton)
    }
    
    // MARK: - Keyboard Handling
    private func setupKeyboardHandling() {
        // 方案1: 如果已集成IQKeyboardManagerSwift，可以在AppDelegate或应用启动时全局配置
        /*
        IQKeyboardManager.shared.enable = true
        IQKeyboardManager.shared.enableAutoToolbar = true
        IQKeyboardManager.shared.shouldResignOnTouchOutside = true
        */
        
        // 方案2: 原生键盘管理 (不依赖第三方库)
        NotificationCenter.default.addObserver(self, selector: #selector(keyboardWillShow), name: UIResponder.keyboardWillShowNotification, object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(keyboardWillHide), name: UIResponder.keyboardWillHideNotification, object: nil)
    }
    
    @objc private func keyboardWillShow(notification: NSNotification) {
        if let keyboardSize = (notification.userInfo?[UIResponder.keyboardFrameEndUserInfoKey] as? NSValue)?.cgRectValue {
            let keyboardHeight = keyboardSize.height
            let animationDuration = notification.userInfo?[UIResponder.keyboardAnimationDurationUserInfoKey] as? Double ?? 0.3

            // 更新输入容器底部约束，使其位于键盘上方
            UIView.animate(withDuration: animationDuration) {
                self.inputContainerView.transform = CGAffineTransform(translationX: 0, y: -keyboardHeight)

                // 调整tableView的contentInset，确保可以正常滚动
                let contentInset = UIEdgeInsets(top: 0, left: 0, bottom: keyboardHeight, right: 0)
                self.tableView.contentInset = contentInset
                self.tableView.scrollIndicatorInsets = contentInset

                // 确保滚动到最后一条消息
                if !self.messages.isEmpty {
                    let lastIndexPath = IndexPath(row: self.messages.count - 1, section: 0)
                    self.tableView.scrollToRow(at: lastIndexPath, at: .bottom, animated: false)
                }
            }
        }
    }
    
    @objc private func keyboardWillHide(notification: NSNotification) {
        let animationDuration = notification.userInfo?[UIResponder.keyboardAnimationDurationUserInfoKey] as? Double ?? 0.3

        // 恢复输入容器位置和tableView的contentInset
        UIView.animate(withDuration: animationDuration) {
            self.inputContainerView.transform = .identity
            self.tableView.contentInset = .zero
            self.tableView.scrollIndicatorInsets = .zero
        }
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }

    // MARK: - Initial Setup
    private func addInitialGreeting() {
        let greetingMessage = "Hi! Welcome to our chat. How can I help you today?"
        messages.append((sender: "ai", content: greetingMessage, timestamp: Date()))
        tableView.reloadData()
    }
    
    // MARK: - Constraints Setup
    private func setupConstraints() {
        tableView.translatesAutoresizingMaskIntoConstraints = false
        inputContainerView.translatesAutoresizingMaskIntoConstraints = false
        messageInputField.translatesAutoresizingMaskIntoConstraints = false
        sendButton.translatesAutoresizingMaskIntoConstraints = false
        
        NSLayoutConstraint.activate([
            // Input container constraints
            inputContainerView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            inputContainerView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            inputContainerView.bottomAnchor.constraint(equalTo: view.safeAreaLayoutGuide.bottomAnchor),
            inputContainerView.heightAnchor.constraint(equalToConstant: 60),
            
            // Message input field constraints
            messageInputField.leadingAnchor.constraint(equalTo: inputContainerView.leadingAnchor, constant: 16),
            messageInputField.centerYAnchor.constraint(equalTo: inputContainerView.centerYAnchor),
            messageInputField.trailingAnchor.constraint(equalTo: sendButton.leadingAnchor, constant: -8),
            messageInputField.heightAnchor.constraint(equalToConstant: 36),
            
            // Send button constraints
            sendButton.trailingAnchor.constraint(equalTo: inputContainerView.trailingAnchor, constant: -16),
            sendButton.centerYAnchor.constraint(equalTo: inputContainerView.centerYAnchor),
            sendButton.widthAnchor.constraint(equalToConstant: 36),
            sendButton.heightAnchor.constraint(equalToConstant: 36),
            
            // TableView constraints
            tableView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor),
            tableView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            tableView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            tableView.bottomAnchor.constraint(equalTo: inputContainerView.topAnchor)
        ])
    }
    
    private func setupTableView() {
        tableView.delegate = self
        tableView.dataSource = self
        tableView.separatorStyle = .none
        tableView.backgroundColor = UIColor(white: 0.97, alpha: 1.0)
        tableView.keyboardDismissMode = .interactive
        tableView.register(UserMessageCell.self, forCellReuseIdentifier: userCellIdentifier)
        tableView.register(AIMessageCell.self, forCellReuseIdentifier: aiCellIdentifier)
        
        // Add tap gesture to dismiss keyboard
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(dismissKeyboard))
        tapGesture.cancelsTouchesInView = false
        tableView.addGestureRecognizer(tapGesture)
    }
    
    // MARK: - Actions
    @objc private func sendMessage() {
        guard let messageText = messageInputField.text, !messageText.isEmpty else { return }
        
        // Add user message to array
        let currentTime = Date()
        messages.append((sender: "user", content: messageText, timestamp: currentTime))
        
        // Update table view
        let newIndexPath = IndexPath(row: messages.count - 1, section: 0)
        tableView.insertRows(at: [newIndexPath], with: .automatic)
        tableView.scrollToRow(at: newIndexPath, at: .bottom, animated: true)
        
        // Clear input field
        messageInputField.text = ""
        
        // Call API for AI response
        requestAIResponse(for: messageText)
    }
    
    @objc private func dismissKeyboard() {
        view.endEditing(true)
    }
    
    // MARK: - API Request
    private func requestAIResponse(for message: String) {
        guard let url = URL(string: apiURL) else { return }
        let userID = Interface.shared().loggedInUser?.userInfo.phone ?? Interface.shared().loggedInUser?.userInfo.email
        let requestBody: [String: Any] = [
            "messages": [
                ["role": "user","session_id" : userID, "content": message]
            ]
        ]
        
        guard let jsonData = try? JSONSerialization.data(withJSONObject: requestBody) else { return }
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.httpBody = jsonData
        
        // Show loading indicator in AI message
        messages.append((sender: "ai", content: "Thinking...", timestamp: Date()))
        let loadingIndexPath = IndexPath(row: messages.count - 1, section: 0)
        tableView.insertRows(at: [loadingIndexPath], with: .automatic)
        tableView.scrollToRow(at: loadingIndexPath, at: .bottom, animated: true)
        
        let task = URLSession.shared.dataTask(with: request) { [weak self] data, response, error in
            guard let self = self else { return }
            
            DispatchQueue.main.async {
                // Remove the loading message
                if self.messages.last?.sender == "ai" && self.messages.last?.content == "Thinking..." {
                    self.messages.removeLast()
                }
                
                if let error = error {
                    print("Error: \(error.localizedDescription)")
                    self.messages.append((sender: "ai", content: "Sorry, an error occurred. Please try again later.", timestamp: Date()))
                } else if let data = data {
                    do {
                        if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
                           let content = json["content"] as? String {
                            self.messages.append((sender: "ai", content: content, timestamp: Date()))
                        } else {
                            self.messages.append((sender: "ai", content: "Sorry, I couldn't understand your question.", timestamp: Date()))
                        }
                    } catch {
                        print("JSON parsing error: \(error.localizedDescription)")
                        self.messages.append((sender: "ai", content: "Sorry, there was an error processing the response.", timestamp: Date()))
                    }
                }
                
                self.tableView.reloadData()
                if !self.messages.isEmpty {
                    let lastIndexPath = IndexPath(row: self.messages.count - 1, section: 0)
                    self.tableView.scrollToRow(at: lastIndexPath, at: .bottom, animated: true)
                }
            }
        }
        
        task.resume()
    }
}

// MARK: - UITableViewDelegate, UITableViewDataSource
extension AIChatVC: UITableViewDelegate, UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return messages.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let message = messages[indexPath.row]
        
        if message.sender == "user" {
            let cell = tableView.dequeueReusableCell(withIdentifier: userCellIdentifier, for: indexPath) as! UserMessageCell
            cell.configure(with: message.content, timestamp: message.timestamp)
            return cell
        } else {
            let cell = tableView.dequeueReusableCell(withIdentifier: aiCellIdentifier, for: indexPath) as! AIMessageCell
            cell.configure(with: message.content)
            return cell
        }
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return UITableView.automaticDimension
    }
    
    func tableView(_ tableView: UITableView, estimatedHeightForRowAt indexPath: IndexPath) -> CGFloat {
        return 80
    }
}

// MARK: - UITextFieldDelegate
extension AIChatVC: UITextFieldDelegate {
    func textFieldShouldReturn(_ textField: UITextField) -> Bool {
        sendMessage()
        return true
    }
}

// MARK: - Custom Cells
class UserMessageCell: UITableViewCell {
    // MARK: - Properties
    private let timestampLabel = UILabel()
    private let messageContentView = UIView()
    private let messageLabel = UILabel()
    private let avatarImageView = UIImageView() // 添加用户头像
    
    // MARK: - Initialization
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupCell()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Setup
    private func setupCell() {
        selectionStyle = .none
        backgroundColor = .clear
        
        // Avatar image view setup
        avatarImageView.backgroundColor = .white
        avatarImageView.layer.cornerRadius = 16
        avatarImageView.clipsToBounds = true
        avatarImageView.contentMode = .scaleAspectFill
        avatarImageView.image = UIImage(named: "peopleCircle") // 使用peopleCircle图标
        avatarImageView.tintColor = .white
        
        // Timestamp label setup
        timestampLabel.font = UIFont.systemFont(ofSize: 14) // 字号+2
        timestampLabel.textColor = .gray
        timestampLabel.textAlignment = .center // 居中对齐
        
        // Message content view setup
        messageContentView.backgroundColor = .systemBlue
        messageContentView.layer.cornerRadius = 16
        messageContentView.clipsToBounds = true
        
        // Message label setup
        messageLabel.font = UIFont.systemFont(ofSize: 16)
        messageLabel.textColor = .white
        messageLabel.numberOfLines = 0
        
        // Add subviews
        contentView.addSubview(avatarImageView)
        contentView.addSubview(timestampLabel)
        contentView.addSubview(messageContentView)
        messageContentView.addSubview(messageLabel)
        
        // Setup constraints
        timestampLabel.translatesAutoresizingMaskIntoConstraints = false
        messageContentView.translatesAutoresizingMaskIntoConstraints = false
        messageLabel.translatesAutoresizingMaskIntoConstraints = false
        avatarImageView.translatesAutoresizingMaskIntoConstraints = false
        
        NSLayoutConstraint.activate([
            // Avatar image view constraints
            avatarImageView.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 8),
            avatarImageView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -16),
            avatarImageView.widthAnchor.constraint(equalToConstant: 32),
            avatarImageView.heightAnchor.constraint(equalToConstant: 32),
            
            // Timestamp label constraints
            timestampLabel.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 4),
            timestampLabel.centerXAnchor.constraint(equalTo: contentView.centerXAnchor), // 放置在屏幕中央
            timestampLabel.widthAnchor.constraint(equalToConstant: 100), // 给时间标签一个固定宽度
            
            // Message content view constraints
            messageContentView.topAnchor.constraint(equalTo: timestampLabel.bottomAnchor, constant: 4),
            messageContentView.trailingAnchor.constraint(equalTo: avatarImageView.leadingAnchor, constant: -8),
            messageContentView.widthAnchor.constraint(lessThanOrEqualTo: contentView.widthAnchor, multiplier: 0.7),
            messageContentView.bottomAnchor.constraint(equalTo: contentView.bottomAnchor, constant: -8),
            
            // Message label constraints
            messageLabel.topAnchor.constraint(equalTo: messageContentView.topAnchor, constant: 8),
            messageLabel.leadingAnchor.constraint(equalTo: messageContentView.leadingAnchor, constant: 12),
            messageLabel.trailingAnchor.constraint(equalTo: messageContentView.trailingAnchor, constant: -12),
            messageLabel.bottomAnchor.constraint(equalTo: messageContentView.bottomAnchor, constant: -8)
        ])
    }
    
    // MARK: - Configuration
    func configure(with message: String, timestamp: Date) {
        messageLabel.text = message
        
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm"
        timestampLabel.text = formatter.string(from: timestamp)
    }
}

class AIMessageCell: UITableViewCell {
    // MARK: - Properties
    private let avatarImageView = UIImageView()
    private let messageContentView = UIView()
    private let messageLabel = UILabel()
    
    // MARK: - Initialization
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupCell()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Setup
    private func setupCell() {
        selectionStyle = .none
        backgroundColor = .clear
        
        // Avatar image view setup
        avatarImageView.backgroundColor = .lightGray
        avatarImageView.layer.cornerRadius = 16
        avatarImageView.clipsToBounds = true
        avatarImageView.contentMode = .scaleAspectFill
        
        // 使用应用图标作为AI头像
        if let appIconImage = getAppIcon() {
            avatarImageView.image = appIconImage
        } else {
            // 如果无法获取应用图标，使用备用图标
            avatarImageView.image = UIImage(systemName: "app.fill")
            avatarImageView.tintColor = .white
        }
        
        // Message content view setup
        messageContentView.backgroundColor = .white
        messageContentView.layer.cornerRadius = 16
        messageContentView.clipsToBounds = true
        messageContentView.layer.shadowColor = UIColor.black.cgColor
        messageContentView.layer.shadowOffset = CGSize(width: 0, height: 1)
        messageContentView.layer.shadowOpacity = 0.1
        messageContentView.layer.shadowRadius = 2
        
        // Message label setup
        messageLabel.font = UIFont.systemFont(ofSize: 16)
        messageLabel.textColor = .black
        messageLabel.numberOfLines = 0
        
        // Add subviews
        contentView.addSubview(avatarImageView)
        contentView.addSubview(messageContentView)
        messageContentView.addSubview(messageLabel)
        
        // Setup constraints
        avatarImageView.translatesAutoresizingMaskIntoConstraints = false
        messageContentView.translatesAutoresizingMaskIntoConstraints = false
        messageLabel.translatesAutoresizingMaskIntoConstraints = false
        
        NSLayoutConstraint.activate([
            // Avatar image view constraints
            avatarImageView.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 8),
            avatarImageView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 16),
            avatarImageView.widthAnchor.constraint(equalToConstant: 32),
            avatarImageView.heightAnchor.constraint(equalToConstant: 32),
            
            // Message content view constraints
            messageContentView.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 8),
            messageContentView.leadingAnchor.constraint(equalTo: avatarImageView.trailingAnchor, constant: 8),
            messageContentView.widthAnchor.constraint(lessThanOrEqualTo: contentView.widthAnchor, multiplier: 0.75),
            messageContentView.bottomAnchor.constraint(equalTo: contentView.bottomAnchor, constant: -8),
            
            // Message label constraints
            messageLabel.topAnchor.constraint(equalTo: messageContentView.topAnchor, constant: 8),
            messageLabel.leadingAnchor.constraint(equalTo: messageContentView.leadingAnchor, constant: 12),
            messageLabel.trailingAnchor.constraint(equalTo: messageContentView.trailingAnchor, constant: -12),
            messageLabel.bottomAnchor.constraint(equalTo: messageContentView.bottomAnchor, constant: -8)
        ])
    }
    
    // 获取应用图标的辅助方法
    private func getAppIcon() -> UIImage? {
        if let iconsDictionary = Bundle.main.infoDictionary?["CFBundleIcons"] as? [String: Any],
           let primaryIconsDictionary = iconsDictionary["CFBundlePrimaryIcon"] as? [String: Any],
           let iconFiles = primaryIconsDictionary["CFBundleIconFiles"] as? [String],
           let lastIcon = iconFiles.last {
            return UIImage(named: lastIcon)
        }
        return nil
    }
    
    // MARK: - Configuration
    func configure(with message: String) {
        messageLabel.text = message
    }
}
