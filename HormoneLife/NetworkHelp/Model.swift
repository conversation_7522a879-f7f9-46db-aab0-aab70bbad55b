//
//  Model.swift
//  HormoneLife
//
//  Created by Tank on 2024/6/10.
//

import Foundation
import UIKit
import Handy<PERSON><PERSON>N

// MARK: - AppInfo
struct AppInfo: Codable {
    let id: String?
    var createBy, createTime, updateBy, updateTime: String?
    let delFlag: Int?
    let title, content: String?
    var url: String?
    let type: String?
}

struct AppInfoList: Codable {
    var list: [AppInfo]
}

struct SendValidate: Codable {
    var account: String?
    var codeType: LoginType?
    var messageCodeType: MessageCodeType?
}

enum MessageCodeType: String, Codable {
    case FORGET_PASSWORD
    case LOGIN
    case REGISTER
    case REMOVE_ACCOUNT
    case REPLACE_EMAIL
    case REPLACE_PHONE
}

// MARK: - FeedbackContent, feedbackStatus    反馈状态 1：待处理，2：已处理
struct FeedbackContent: Codable {
    let contactInfo, createTime, eventStatusID, eventStatusName: String?
    let eventTime, feedbackCode, feedbackContent, feedbackImg: String?
    let feedbackStatus: Int?
    let feedbackUserID, feedbackUserName, feedbackUserNo, feedbackUserRegisterTime: String?
    let id, replyContent, replyTime, replyUserID: String?
    let replyUserName, version: String?
    let createBy, updateBy, updateTime: String?
    let delFlag: Int?

    enum CodingKeys: String, CodingKey {
        case contactInfo, createTime, createBy, delFlag, updateBy, updateTime
        case eventStatusID = "eventStatusId"
        case eventStatusName, eventTime, feedbackCode, feedbackContent, feedbackImg, feedbackStatus
        case feedbackUserID = "feedbackUserId"
        case feedbackUserName, feedbackUserNo, feedbackUserRegisterTime, id, replyContent, replyTime
        case replyUserID = "replyUserId"
        case replyUserName, version
    }
}

enum LoginType: String, Codable {
    case phone = "PHONE"
    case email = "EMAIL"
    
    var accountFieldPlaceholder: String {
        self == .email ? "Email address" : "Mobile number"
    }
}

enum GrantType: String, Codable {
    case password = "password"
    case google
    case facebook
    case sms_code
    case visitor
    case apple
}

// MARK: - UserInfo
struct UserInfo: Codable {
    let userCode: String?
    let phone, firstName: String?
    let userName: String?
    let email, headImg: String?
    let sex: Int
    let birthday: String?
    let roleType, enableStatus: Int
    let remark, label, photos, lastLoginTime: String?
    let appleUnionID, appleAccount, facebookUnionID, facebookAccount: String?
    let googleUnionID, googleAccount: String?
    let deviceID, id, createBy, createTime: String?
    let updateBy, updateTime: String?
    let userBusinessConfigVO: UserBusinessConfigVO
    let period, menstruationCycleAvg, area: String?
    
    enum CodingKeys: String, CodingKey {
        case userCode, phone, firstName, userName, email, headImg, sex, birthday, roleType, enableStatus, remark, label, photos, lastLoginTime
        case appleUnionID = "appleUnionId"
        case appleAccount
        case facebookUnionID = "facebookUnionId"
        case facebookAccount
        case googleUnionID = "googleUnionId"
        case googleAccount
        case deviceID = "deviceId"
        case id, createBy, createTime, updateBy, updateTime, userBusinessConfigVO, period, menstruationCycleAvg, area
    }
}

struct CycleAndPeriodAvg: Codable {
    var avgPeriod: Int = 0
    var avgMenstruationCycleAvg: Int = 0
}

// MARK: - UserBusinessConfigVO
struct UserBusinessConfigVO: Codable {
    let userID: String?
    let trackMethod, startPeriodDate, lhTime, fshTime, pdgTime: String?
    let period, menstruationCycleAvg, msgNoticeOn, fertilityReminderOn, testReminderOn: Int
    let msgContentOn: Int?
    let id, createBy, createTime, updateBy: String?
    let updateTime: String?
    
    enum CodingKeys: String, CodingKey {
        case userID = "userId"
        case trackMethod, startPeriodDate, period, menstruationCycleAvg, msgNoticeOn, msgContentOn, id, createBy, createTime, updateBy, updateTime, fertilityReminderOn, testReminderOn, lhTime, fshTime, pdgTime
    }
}

struct HomeTipModel : HandyJSON {
    var calendarRemainTestLabel : String?
    var homeCycleTipLabel : String?
    var homeRemainTestLabel : String?
    var periodDelayRemainTestLabel : String?
}

// MARK: - UserInfo
struct HomeData: Codable {
    var title, content: String?
    var rate, pdgStartCriticalValue, pdgEndCriticalValue, pdgCriticalValue: Int?
    var hcgCriticalValue, hcgStartCriticalValue, hcgEndCriticalValue: Int?
    var lhUltraTipList: [HomeTipList] = []
    var fshTipList: [HomeTipList] = []
    var lhTipList: [HomeTipList] = []
    var hcgTipList: [HomeTipList] = []
    var pdgTipList: [HomeTipList] = []
}

struct HomeTipList: Codable {
    var id: String?
    var createBy: String?
    var createTime: String?
    var updateBy: String?
    var updateTime: String?
    var delFlag: Int?
    var title: String?
    var type: String?
    var tips: String?
    var url: String?
    var timeType: Int?
    var days: String?
    var rulesId: String?
    var rate, rateEnd: Double?
    var label, templateType: String?
}

// MARK: - Advertisement
struct Advertisement: Codable {
    var clickNum: Int?
    var content, createBy, createTime: String?
    var delFlag: Int?
    var endTime: String?
    var id: String?
    var img, link, linkType, name: String?
    var position, startTime: String?
    var status: Int?
    var updateBy, updateTime: String?
}

struct AdList: Codable {
    var data: [Advertisement]
}

enum AdvertisementType: String {
    case startup = "startup%20page"
    case page = "page"
    case banner = "banner"
}

// MARK: - UserConfig
struct UserConfig: Codable {
    var createBy, createTime: String?
    var id, menstruationCycleAvg, msgContentOn, msgNoticeOn, fertilityReminderOn, testReminderOn, fshTime, lhTime, pdgTime: String?
    var period: String?
    var startPeriodDate, trackMethod, updateBy, updateTime, otherTrackMethod: String?
    var userID: String?

    enum CodingKeys: String, CodingKey {
        case createBy, createTime, id, menstruationCycleAvg, msgContentOn, msgNoticeOn, period, startPeriodDate, trackMethod, updateBy, updateTime, otherTrackMethod, fertilityReminderOn, testReminderOn, pdgTime, fshTime, lhTime
        case userID = "userId"
    }
}

// Role type
enum RoleType: Int {
    case getPregnant = 1
    case cycleTracking = 2
}

// MARK: - Note
struct Note: Codable {
    let id, userID: Int?
    let content, imageUrls, markTime, medicationHistory, vitaminHistory: String

    enum CodingKeys: String, CodingKey {
        case content, id, imageUrls, markTime, medicationHistory
        case userID = "userId"
        case vitaminHistory
    }
}

// MARK: - Message
struct MessageList: Codable {
    let totalCount, pageSize, totalPage, page: String
    let list: [Message]
}

// MARK: - List
struct Message: Codable {
    let id: String
    var title: String?
    let type: String
    var content: String?
    let url: String
    let createTime, createBy: String
    var isRead: Int?
    var sendTime, endTime, startTime: String?
    var isSend: Int?
}

struct RemindMessage: HandyJSON {
    var id, title, type, message: String?
    var url: String?
    var createTime, createBy: String?
    var isRead: Int?
}

enum MessageType: Int {
    case system = 0
    case board = 1
    case contentBoard = 2
}

// MARK: - UserPeriodCycle
struct UserPeriodCycleList: Codable {
    let totalCount, pageSize, totalPage, page: String
    let list: [UserPeriodCycle]
}

// MARK: - List
struct UserPeriodCycle: Codable {
    
    var cervicalMucusDaysCount: Int?
    var createTime: String?
    var cycleDays: Int?
    var endCycleTime: String?
    var endOvulateTime: String?
    var endPeriodModifyState: Int?
    var endPeriodTime: String?
    var expectNextStartCycleTime: String?
    var id: String?
    var menstrualFlowDaysCount: Int?
    var ovulateTime: String?
    var ovulationRateJson: String?
    var periodDays: Int?
    var pregnancyRate: String?
    var sexRecordDaysDaysCount: Int?
    var startCycleTime : String?
    var startOvulateTime : String?
    var startPeriodModifyState: Int?
    var startPeriodTime: String?
    var symptomsFlow: String?
    
    var sysPregnancyRateJson: String?
    
    var temperatureRecordDaysCount : Int?
    var title: String?
    
    var userId: String?
    var userName: String?
}

struct UserPeriodCycleListByYear: Codable {
    var cycleList: [UserPeriodCycle] = []
    var year: String = ""
}

import HandyJSON
struct UserTestResultPageModel: Codable, HandyJSON {
    
    var error: Int? = -1
    var resultLabel: String?
    var resultValue: Double?
    var shift: Int? = 0
    var type: String? //试纸类型,LH Ultra, PdG,FSH,LH,HCG, 字典值：test_page_type
    var result: UserTestResult?
    var id: String?
}

struct UserTestResult : Codable , HandyJSON {
    var ovulation : String?
    var pregnancy : String?
    var value : Int? = 0
}

// MARK: - CalendarDetailVO
struct CalendarDatesDetail: Codable {
    let dayStr: String
    let calendarDetailVO: CalendarDetailVO
}

// MARK: - CalendarDetailVOClass
struct CalendarDetailVO: Codable, HandyJSON, Equatable {
    var periodState: Bool = false
    var fertileWindowState: Bool = false
    var ovulationDay: Bool = false
    var expectedPeriodState: Bool = false
    var expectedFertileWindowState: Bool = false
    var expectedOvulationDayState: Bool = false
    var sex: Bool = false
    var pdgstate: Bool = false
    var fshstate: Bool = false
    var lhultraState: Bool = false
    var hcgstate: Bool = false
    var lhstate: Bool = false
}


// MARK: - NewTestPaperModel
struct NewTestPaperModel: Codable {
    let imageURL: String
    let lastResult: Int?
    let markTime, pageType: String
    let resultLabel, ovulation, pregnancy: String?
    let resultValue: Double?

    enum CodingKeys: String, CodingKey {
        case imageURL = "imageUrl"
        case lastResult, markTime, ovulation, pageType, pregnancy, resultLabel, resultValue
    }
}

// MARK: - CalendarDetailVO
struct TestPapersModel: Codable {
    let list: [TestPaperList]
    let page, pageSize, totalCount, totalPage: String?
}

// MARK: - List
struct TestPaperList: Codable {
    let days: Int?
    let endCycleTime, fileName: String?
    let id: String?
    let imageUrl: String?
    let lastResultLabel, markTime: String?
    let modifyResultLabel, pageType, resultLabel: String?
    let startCycleTime: String?
    let userId: String?
    let userName: String?
    let modifyResult, resultValue, lastResult: Double?
}

// MARK: - TestPaperDetail
struct TestPaperDetail: Codable, HandyJSON {
    var markTime, pageType: String?
    var resultValue: Double?
    var resultLabel: String?
    var imageUrl: String?
    var id: String?
    var userName, startCycleTime, endCycleTime: String?
    var fileName: String?
    var userId: String?
    var lastResult: Double?
    var lastResultLabel: String?
    var days: Int?
    var modifyResult: Double?
    var modifyResultLabel: String?
    var error: Int? = -1
    var result: UserTestResult?
    var isCreate: Bool? = false
}

struct CycleSimple: Codable, Equatable {
    let id, userID, startCycleTime: String?
    let endCycleTime: String?
    let expectNextStartCycleTime: String?
    var num: Int = 0

    enum CodingKeys: String, CodingKey {
        case id
        case userID = "userId"
        case startCycleTime, endCycleTime, expectNextStartCycleTime, num
    }
}

// MARK: - User Calendar Management - PeriodRecordDetail
import HandyJSON
struct PeriodRecordDetail: Codable, HandyJSON {
    
    var cervicalMucus, createTime: String?
    var days: Int?
    var endCycleTime: String?
    var id: Int?
    var lhUltraTestPage: TestPageResultList?
    var loveState: Int?
    var markTime: String?
    var pdgTestPage: CalendarTestPage?
    var sexRecord, startCycleTime, symptomsColour, symptomsFlow: String?
    var symptomsPain: String?
    var temperatureUnit, userId: Int?
    var userName: String?
    var temperature: String?
    var calendarRemainTips: String?
    var userCalendarPeriodInfoVO: UserCalendarPeriodInfoVO?
    var expectNextStartCycleTime: String?
    var loveRecordTime: String?
    var temperatureRecordTime: String?
    var mucusRecordTime: String?
    var temperatureTime: String?
    var flowRecordTime: String?
    var startPeriodModifyState: Int?
    var endPeriodModifyState: Int?
}

// MARK: - UserCalendarPeriodInfoVO
struct UserCalendarPeriodInfoVO: Codable, HandyJSON {
    var startPeriodModifyState, endPeriodModifyState: Int?
    var currentCycleID, preCycleID, nextCycleID: String?
    var startPeriodTime, endPeriodTime: Int?
    var preStartPeriodTime, preEndPeriodTime: Int?
    var preStartPeriodModifyState, preEndPeriodModifyState: Int?
    var nextStartPeriodTime, nextEndPeriodTime: Int?
    var nextStartPeriodModifyState, nextEndPeriodModifyState: Int?
    var startReminderMessage: String?
    var beginOrEndStatus: Int?
}

// MARK: - CalendarTestPage
struct CalendarTestPage: Codable, HandyJSON {
    var days: Int?
    var endCycleTime, fileName: String?
    var id: Int?
    var imageUrl: String?
    var lastResult: Double?
    var lastResultLabel, markTime: String?
    var modifyResult: Double?
    var modifyResultLabel, pageType, resultLabel: String?
    var resultValue: Double?
    var startCycleTime: String?
    var userId: Int?
    var userName: String?
    var expectNextStartCycleTime, firstName: String?
    var tips: String?
}

// MARK: - CycleStatistics
struct CycleStatistics: Codable, HandyJSON {
    var data: [CycleFoldLineStatistic] = []
}

// MARK: - CycleFoldLineStatistic
struct CycleFoldLineStatistic: Codable, HandyJSON, Equatable {
    var cdList: [Int] = []
    var dateList: [String] = []
    var resultList: [Double] = []
    var testPageResultList: [TestPageResultList] = []
    var calendarDetailVOList: [CalendarDetailVO] = []
    var pageType: String?
    var topResult: Double?
    var topDate: Int?
}

// MARK: - TestPageResultList
struct TestPageResultList: Codable, HandyJSON, Equatable {
    var markTime: String?
    var pageType: String?
    var resultValue: Double?
    var resultLabel: String?
    var imageUrl: String?
    var id: String?
    var userName, startCycleTime, endCycleTime, days: String?
    var userId: String?
    var fileName: String?
    var lastResult: Double?
    var lastResultLabel: String?
    var modifyResult: Int?
    var modifyResultLabel: String?
    var expectNextStartCycleTime, firstName: String?
    var tips: String?
}

struct TestImageReqBody: Codable, HandyJSON {
    var type: String?
    var imageUrl: String?
    var age: Double?
    var ovulation: [Double] = []
    var history: [TestImageHistory] = []
    var imgBase64: String?
}

// MARK: - History
struct TestImageHistory: Codable, HandyJSON {
    var m: Int?
    var data: [Datum] = []
}

// MARK: - Datum
struct Datum: Codable, HandyJSON {
    var d: String?
    var v: Double?
}

struct PhotoToAmazon: Codable, HandyJSON {
    var type: String?
    var shift: Int? = 0
    var error: Int? = -1
    var result: PhotoToAmazonResult?
    var id: String?
}

struct PhotoToAmazonResult: Codable, HandyJSON {
    var value: Double?
    var ovulation: String = ""
    var pregnancy: String = ""
    
//    var ovulation: [Double] = []
//    var pregnancy: [Double] = []
}

struct ResultLabelModel: Codable, HandyJSON {
    var resultValue: Double?
    var resultLabel: String?
    var type: String?
}
