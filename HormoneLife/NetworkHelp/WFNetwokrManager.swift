
import UIKit

let noteListUri = "/app/userNotes/page"
let addNoteUri = "/app/userNotes/save"
let updateNoteUri = "/app/userNotes/update"
let getUserNotesUri = "/app/userNotes/"

class NoteInteractor: NSObject {

    static func addNote(params: [String : Any]?, success: @escaping (_ result: [String : Any]?) -> Void,failure: @escaping (_ error: String?) -> Void) {
        
        NetWorkHandle.requestPost(addNoteUri, parameters: params) { result in
            
            success(result)
        } failure: { error in
            failure(error)
        }
    }
    
    static func updateNote(params: [String : Any]?, success: @escaping (_ result: [String : Any]?) -> Void,failure: @escaping (_ error: String?) -> Void) {
        
        NetWorkHandle.requestPost(updateNoteUri, parameters: params) { result in
            
            success(result)
        } failure: { error in
            failure(error)
        }
    }
    
    static func getUserNotesList(params: [String : Any]?, success: @escaping (_ result: NotePageModel?) -> Void,failure: @escaping (_ error: String?) -> Void) {
        
        NetWorkHandle.requestPost(noteListUri, parameters: params) { result in
            let model = NotePageModel.deserialize(from: result)
            success(model)
        } failure: { error in
            failure(error)
        }
    }
    
    static func getUserNotesDetail(id: String, success: @escaping (_ result: NotePageListModel?) -> Void,failure: @escaping (_ error: String?) -> Void) {
        
        NetWorkHandle.requestGet(getUserNotesUri + id, parameters: nil) { result in
            let model = NotePageListModel.deserialize(from: result)
            success(model)
        } failure: { error in
            failure(error)
        }
    }
    
}

let sysTagsUserUri = "/app/sysTagsUser/list"
let createUserTagUri = "/app/sysTagsUser/createUserTag"
let selectTagsUri = "/app/sysTagsUser/selectTags"
let listDictItemUri = "/app/dictionary/listDictItem/"

let allDictItemUri = "/app/dictionary/listAllDict"
let saveFeedbackInfoUri = "/app/feedbackInfo/save"
let sysOperationListUri = "/app/sysOperation/queryCategoryList"
let getSysOperListUri = "/app/sysOperation/getOperList"
let getSysOperInfoUri = "/app/sysOperation/"
let coldStartUri = "/app/userTestPage/coldStart" //cold start

class SystemInteractor: NSObject {
    static func getSystemTags(params: [String : Any]?, success: @escaping (_ result: [SystemTagsModel]) -> Void,failure: @escaping (_ error: String?) -> Void) {
        
        NetWorkHandle.requestGet(sysTagsUserUri, parameters: params) { result in
            if let datas = result["data"] as? [Any], let results = [SystemTagsModel].deserialize(from: datas) as? [SystemTagsModel] {
                success(results)
            } else {
                success([SystemTagsModel]())
            }
        } failure: { error in
            failure(error)
        }
    }
    
    static func addUserTags(params: [String : Any]?, success: @escaping (_ result: [String : Any]?) -> Void,failure: @escaping (_ error: String?) -> Void) {
        NetWorkHandle.requestPost(createUserTagUri, parameters: params) { result in
            success(result)
        } failure: { error in
            failure(error)
        }
    }
    
    static func saveUserTags(params: [String : Any]?, success: @escaping (_ result: [String : Any]?) -> Void,failure: @escaping (_ error: String?) -> Void) {
        NetWorkHandle.requestPost(selectTagsUri, parameters: params) { result in
            success(result)
        } failure: { error in
            failure(error)
        }
    }
    
    static func getSystemDictionary(type: String, success: @escaping (_ result: [SystemDictionaryModel]) -> Void,failure: @escaping (_ error: String?) -> Void) {
        
        NetWorkHandle.requestGet(listDictItemUri + type, parameters: nil, showHub: false) { result in
            if let datas = result["data"] as? [Any], let results = [SystemDictionaryModel].deserialize(from: datas) as? [SystemDictionaryModel] {
                success(results)
            } else {
                success([SystemDictionaryModel]())
            }
        } failure: { error in
            failure(error)
        }
    }
    
    static func getSystemAllDictionary(success: @escaping (_ result: [SystemDictionaryModel]) -> Void,failure: @escaping (_ error: String?) -> Void) {
        
        NetWorkHandle.requestGet(allDictItemUri, parameters: nil, showHub: false) { result in
            if let datas = result["data"] as? [Any], let results = [SystemDictionaryModel].deserialize(from: datas) as? [SystemDictionaryModel] {
                success(results)
            } else {
                success([SystemDictionaryModel]())
            }
        } failure: { error in
            failure(error)
        }
    }
    
    
    static func saveFeedbackInfo(params: [String : Any]?, success: @escaping (_ result: [String : Any]?) -> Void,failure: @escaping (_ error: String?) -> Void) {
        NetWorkHandle.requestPost(saveFeedbackInfoUri, parameters: params) { result in
            success(result)
        } failure: { error in
            failure(error)
        }
    }
    
    static func getSysOperationList(params: [String : Any]?, success: @escaping (_ result: [String]) -> Void,failure: @escaping (_ error: String?) -> Void) {
        
        NetWorkHandle.requestGet(sysOperationListUri, parameters: params) { result in
            if let datas = result["data"] as? [String] {
                success(datas)
            } else {
                success([String]())
            }
        } failure: { error in
            failure(error)
        }
    }
    
    static func getSysOperList(params: [String : Any]?, success: @escaping (_ result: [SystemOperListModel]) -> Void,failure: @escaping (_ error: String?) -> Void) {
        
        NetWorkHandle.requestPost(getSysOperListUri, parameters: params) { result in
            if let datas = result["data"] as? [Any], let results = [SystemOperListModel].deserialize(from: datas) as? [SystemOperListModel] {
                success(results)
            } else {
                success([SystemOperListModel]())
            }
        } failure: { error in
            failure(error)
        }
    }
    
    static func coldStart(params: [String : Any]?, success: @escaping (_ result: [SystemOperListModel]) -> Void,failure: @escaping (_ error: String?) -> Void) {
        
        NetWorkHandle.requestPost(coldStartUri, parameters: params, showHub: false) { result in
           
        } failure: { error in

        }
    }
    
    static func getSysOperationInfo(id: String, success: @escaping (_ result: SystemOperInfo?) -> Void,failure: @escaping (_ error: String?) -> Void) {
        
        NetWorkHandle.requestGet("\(getSysOperInfoUri)\(id)", parameters: nil) { result in
            if let data = SystemOperInfo.deserialize(from: result) {
                success(data)
            } else {
                success(nil)
            }
        } failure: { error in
            failure(error)
        }
    }
}

let unReadCountUri = "/app/sysMessageUser/unReadCount"
let allReadUri = "/app/sysMessageUser/allRead"
let getUserReminderMsgUri = "/app/userReminderMsg/page"

class MessageInteractor: NSObject {
    static func unReadCount(params: [String : Any]?, success: @escaping (_ result: MeesageUnreadCountModel) -> Void,failure: @escaping (_ error: String?) -> Void) {
        
        NetWorkHandle.requestGet(unReadCountUri, parameters: params) { result in
            if let model = MeesageUnreadCountModel.deserialize(from: result) {
                success(model)
            } else {
                success(MeesageUnreadCountModel())
            }
        } failure: { error in
            failure(error)
        }
    }
    
    static func allRead(params: [String : Any]?, success: @escaping (_ result: [String : Any]) -> Void,failure: @escaping (_ error: String?) -> Void) {
        
        NetWorkHandle.requestPost(allReadUri, parameters: params) { result in
            
            success(result)
        } failure: { error in
            failure(error)
        }
    }
    
    static func getRemindMessages(params: [String : Any]?, success: @escaping (_ result: [RemindMessage]) -> Void,failure: @escaping (_ error: String?) -> Void) {
        
        NetWorkHandle.requestPost(getUserReminderMsgUri, parameters: params, showHub: false) { result in
            if let datas = result["list"] as? [Any], let results = [RemindMessage].deserialize(from: datas) as? [RemindMessage] {
                success(results)
            } else {
                success([RemindMessage]())
            }
        } failure: { error in
            failure(error)
        }
    }
}


//static func feedback(params: [String : Any]?, success: @escaping (_ result: [String : Any]?) -> Void,failure: @escaping (_ error: String?) -> Void) {
//    
//    NetWorkHandle.requestPost(feedbackUri, parameters: params) { result in
//        success(result)
//    } failure: { error in
//        failure(error)
//    }
//}
