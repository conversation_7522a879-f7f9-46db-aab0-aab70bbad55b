//
//  Common.swift
//  HormoneLife
//
//  Created by Tank on 2024/6/10.
//

import Foundation
import UIKit

//public let baseURL = "http://175.27.240.142:8087"
//public let baseImageURL = "http://175.27.240.142:8087/"

public let baseURL = "https://welllifeusa.com"
public let baseImageURL = "https://welllifeusa.com/"

var isProduce = true
var isHideLH = false


public let Bearer = "Bearer "


//-------------Key-------------------
public let refreshTokenKey = "refreshToken"
public let userTokenKey = "userTokenKey"
public let userValidKey = "userValidKey"
public let rememberAccountKey = "rememberAccountKey"
public let rememberPasswordKey = "rememberPasswordKey"

let kHomeLiftShopUrlKey = "https://hormonelife.com/"

public var currentWindow: UIWindow? {
    if #available(iOS 14.0, *) {
        return UIApplication.shared.connectedScenes.compactMap({$0 as? UIWindowScene}).first?.windows.first
    } else {
        return UIApplication.shared.keyWindow
    }
}

typealias HLGetUserInfoCallback = () -> Void
func hl_fetchUserInfo(_ callback: (HLGetUserInfoCallback)? = nil) {
    UserInteractor.getUserInfo { info in
        guard let userInfo = info else { 
            callback?()
            return
        }
        
        let user = LoginUser(deviceId: UserDefaults.standard.deviceID, token: UserDefaults.standard.userToken, userInfo: userInfo)
        Interface.shared().loggedInUser = user
        callback?()
    }
}

func hl_timeFormat(date:String, format: String, toFormat: String) -> String {
    //格式器
    let dateFormatter = DateFormatter()
    dateFormatter.dateFormat = format
    let dateNew = dateFormatter.date(from: date) ?? Date()
    
    dateFormatter.dateFormat = toFormat

    return dateFormatter.string(from: dateNew)
}

func hl_dateFormat(date:Date?, format: String) -> String {
    //格式器
    let dateFormatter = DateFormatter()
    dateFormatter.dateFormat = format
    let dateNew = date ?? Date()
    
    return dateFormatter.string(from: dateNew)
}

func isRunningOnSimulator() -> Bool {
    #if targetEnvironment(simulator)
        print("Simulator does not support this module!")
        return true
    #else
        return false
    #endif
}


func transformTimeStamp(timeStamp:Double, format: String) -> String {
    //格式器
    let dateFormatter = DateFormatter()
    dateFormatter.dateFormat = format
    
    var end = 1
    if timeStamp > 100000000000 {
        end = 1000
    }
    let date = Date(timeIntervalSince1970: timeStamp/Double(end))
    return dateFormatter.string(from: date)
}

func getKeyWindow() -> UIWindow? {
    if #available(iOS 13.0, *) {
        return UIApplication.shared.connectedScenes
            .filter({$0 is UIWindowScene})
            .map({($0 as! UIWindowScene).windows})
            .first?.filter({$0.isKeyWindow}).first
    } else {
        return UIApplication.shared.keyWindow
    }
}

func  showToachMessage(message: String) {
    if let topVC = UIApplication.shared.topViewController {
        hideActivityHUD()
    }
    let view = AutoDismissToastView(message: message)
    view.show()
}

let kScreenBounds = UIScreen.main.bounds
let kScreenSize   = kScreenBounds.size
let kScreenWidth  = kScreenSize.width > kScreenSize.height ? kScreenSize.height : kScreenSize.width
let kScreenHeight = kScreenSize.width > kScreenSize.height ? kScreenSize.width :  kScreenSize.height

let UIScale = kScreenHeight/1334

let safeAreScreenDownSpace = isIphoneX() ? CGFloat(34) : 0
let screenStatusBarHeight = UIApplication.shared.statusBarFrame.size.height
let screenNavigationbarHeight:CGFloat = 44.0
let screenStatusBarAndNavigationbarHeight = (screenStatusBarHeight+screenNavigationbarHeight)
let screenTabBarHeight = safeAreScreenDownSpace+49.0


func isIphoneX() -> Bool {
    if #available(iOS 11.0, *) {
        let safeAreaInsetsBottom = UIApplication.shared.windows.first?.safeAreaInsets.bottom ?? 0
        return safeAreaInsetsBottom > 0
    } else {
        return false
    }
}

/**
 RGB 16进制值颜色
 
 - parameter rgbValue: 16进制色值
 
 - returns: 返回颜色
 */
func UIColorFromRGB(rgbValue: UInt, alpha: CGFloat? = nil) -> UIColor {
    return UIColor(
        red: CGFloat((rgbValue & 0xFF0000) >> 16) / 255.0,
        green: CGFloat((rgbValue & 0x00FF00) >> 8) / 255.0,
        blue: CGFloat(rgbValue & 0x0000FF) / 255.0,
        alpha: CGFloat(alpha ?? 1.0)
    )
}

func isToday(_ dateString: String, format: String = "yyyy-MM-dd HH:mm:ss") -> Bool {
    
    let dateFormatter = DateFormatter()
    dateFormatter.dateFormat = format
    let date = dateFormatter.date(from: dateString) ?? Date()
    
    let calendar = Calendar.current
    let now = Date()
    let nowComponents = calendar.dateComponents([.year, .month, .day], from: now)
    let dateComponents = calendar.dateComponents([.year, .month, .day], from: date)
    
    // 检查是否是同一天
    if let nowYear = nowComponents.year,
       let nowMonth = nowComponents.month,
       let nowDay = nowComponents.day,
       let dateYear = dateComponents.year,
       let dateMonth = dateComponents.month,
       let dateDay = dateComponents.day {
        if nowYear == dateYear && nowMonth == dateMonth && nowDay == dateDay {
            return true
        }
    }
    return false
}

func isYesterday(_ dateString: String, format: String = "yyyy-MM-dd HH:mm:ss") -> Bool {
    
    let dateFormatter = DateFormatter()
    dateFormatter.dateFormat = format
    let date = dateFormatter.date(from: dateString) ?? Date()
    
    let calendar = Calendar.current
    let now = Date()
    let nowComponents = calendar.dateComponents([.year, .month, .day], from: now)
    let dateComponents = calendar.dateComponents([.year, .month, .day], from: date)
    
    if let nowYear = nowComponents.year,
       let nowMonth = nowComponents.month,
       let nowDay = nowComponents.day,
       let dateYear = dateComponents.year,
       let dateMonth = dateComponents.month,
       let dateDay = dateComponents.day {
        if nowYear == dateYear && nowMonth == dateMonth && nowDay - 1 == dateDay {
            return true
        }
    }
    return false
}

func dayForDaysCount(_ dateString: String, format: String = "yyyy-MM-dd HH:mm:ss", forDate: Date = Date()) -> Int {
    let dateFormatter = DateFormatter()
    dateFormatter.dateFormat = "yyyy-MM-dd"
    
    let date = dateFormatter.date(from: timeFormat(date: dateString, format: "yyyy-MM-dd HH:mm:ss", toFormat: "yyyy-MM-dd")) ?? Date()
    
    let currentDate = forDate
    let calendar = Calendar.current
    let components = calendar.dateComponents([.day], from: date, to: currentDate)
    
    if let days = components.day {
        return (days + 1)
    }
    return 0
}

func timeFormat(date:String, format: String, toFormat: String) -> String {
    let dateFormatter = DateFormatter()
    dateFormatter.dateFormat = format
    let dateNew = dateFormatter.date(from: date) ?? Date()
    
    dateFormatter.dateFormat = toFormat

    return dateFormatter.string(from: dateNew)
}

func dateFormat(date: Date, format: String) -> String {
    let dateFormatter = DateFormatter()
    
    dateFormatter.dateFormat = format

    return dateFormatter.string(from: date)
}


func takeScreenshotOfTableView(_ tableView: UITableView) -> UIImage? {
    UIGraphicsBeginImageContextWithOptions(tableView.bounds.size, false, UIScreen.main.scale)
    guard let context = UIGraphicsGetCurrentContext() else { return nil }
    tableView.layer.render(in: context)
    let image = UIGraphicsGetImageFromCurrentImageContext()
    UIGraphicsEndImageContext()
    return image
}


func takeScreenshotOfView(_ view: UIView) -> UIImage? {
    UIGraphicsBeginImageContextWithOptions(view.bounds.size, false, 0.0)
    guard let context = UIGraphicsGetCurrentContext() else { return nil }
    view.layer.render(in: context)
    let image = UIGraphicsGetImageFromCurrentImageContext()
    UIGraphicsEndImageContext()
    return image
}


func hl_getCustomHudTag()  -> Int {
    return 12232
}

func resizeImage(_ image: UIImage, newWidth: CGFloat) -> UIImage? {
    let scale = newWidth / image.size.width
    let newHeight = image.size.height * scale
    UIGraphicsBeginImageContext(CGSize(width: newWidth, height: newHeight))
    image.draw(in: CGRect(x: 0, y: 0, width: newWidth, height: newHeight))
    let newImage = UIGraphicsGetImageFromCurrentImageContext()
    UIGraphicsEndImageContext()
    return newImage
}
