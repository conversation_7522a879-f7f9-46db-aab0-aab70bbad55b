//
//  UserDefaultTool.swift
//  MilkTime
//
//  Created by Tank on 2023/6/22.
//

import Foundation

extension UserDefaults {
    public func removeKeysForLogout() {
            removeObject(forKey: "hasRememberLogingUser")
    }
    
    public var hasSeemGuidelinePage: Bool {
        get {
            return UserDefaults.standard.bool(forKey: "hasSeemGuidelinePage")
        }
        set {
            UserDefaults.standard.setValue(newValue, forKey: "hasSeemGuidelinePage")
            UserDefaults.standard.synchronize()
        }
    }
    
    public var hasRememberLogingUser: Bool {
        get {
            guard Interface.shared().isLoggedInUser() else { return false }
            return UserDefaults.standard.bool(forKey: "hasRememberLogingUser")
        }
        set {
            UserDefaults.standard.setValue(newValue, forKey: "hasRememberLogingUser")
            UserDefaults.standard.synchronize()
        }
    }
    
    public var deviceID: String? {
        get {
            return UserDefaults.standard.string(forKey: "hormoneliftDeviceId")
        }
        set {
            UserDefaults.standard.setValue(newValue, forKey: "hormoneliftDeviceId")
            UserDefaults.standard.synchronize()
        }
    }
    
    public func getDeviceId() -> String {
        guard let deviceId = deviceID else {
            let newId = UUID().uuidString
            deviceID = newId
            return newId
        }
        return deviceId
    }
    
    public var userToken: String? {
        get {
            return UserDefaults.standard.string(forKey: userTokenKey)
        }
        set {
            UserDefaults.standard.setValue(newValue, forKey: userTokenKey)
            UserDefaults.standard.synchronize()
        }
    }
    
    public var isLoginAsGuestWithoutAccount: Bool {
        get {
            return UserDefaults.standard.bool(forKey: "isLoginAsGuestWithoutAccount")
        }
        set {
            UserDefaults.standard.setValue(newValue, forKey: "isLoginAsGuestWithoutAccount")
            UserDefaults.standard.synchronize()
        }
    }
    
    
    public var isFristTimeChooseGoal: Bool {
        get {
            return UserDefaults.standard.bool(forKey: "isFristTimeChooseGoal")
        }
        set {
            UserDefaults.standard.setValue(newValue, forKey: "isFristTimeChooseGoal")
            UserDefaults.standard.synchronize()
        }
    }
    
    public var rememberAccount: String? {
        get {
            return UserDefaults.standard.string(forKey: rememberAccountKey)
        }
        set {
            UserDefaults.standard.setValue(newValue, forKey: rememberAccountKey)
            UserDefaults.standard.synchronize()
        }
    }
    
    public var rememberPassword: String? {
        get {
            return UserDefaults.standard.string(forKey: rememberPasswordKey)
        }
        set {
            UserDefaults.standard.setValue(newValue, forKey: rememberPasswordKey)
            UserDefaults.standard.synchronize()
        }
    }

    // 分别保存邮箱和手机号码账号
    public var rememberEmailAccount: String? {
        get {
            return UserDefaults.standard.string(forKey: "rememberEmailAccountKey")
        }
        set {
            UserDefaults.standard.setValue(newValue, forKey: "rememberEmailAccountKey")
            UserDefaults.standard.synchronize()
        }
    }

    public var rememberPhoneAccount: String? {
        get {
            return UserDefaults.standard.string(forKey: "rememberPhoneAccountKey")
        }
        set {
            UserDefaults.standard.setValue(newValue, forKey: "rememberPhoneAccountKey")
            UserDefaults.standard.synchronize()
        }
    }

    // 分别保存邮箱和手机号码对应的密码
    public var rememberEmailPassword: String? {
        get {
            return UserDefaults.standard.string(forKey: "rememberEmailPasswordKey")
        }
        set {
            UserDefaults.standard.setValue(newValue, forKey: "rememberEmailPasswordKey")
            UserDefaults.standard.synchronize()
        }
    }

    public var rememberPhonePassword: String? {
        get {
            return UserDefaults.standard.string(forKey: "rememberPhonePasswordKey")
        }
        set {
            UserDefaults.standard.setValue(newValue, forKey: "rememberPhonePasswordKey")
            UserDefaults.standard.synchronize()
        }
    }
    
    public var downloadUrl: String {
        get {
            return UserDefaults.standard.string(forKey: "k_home_life_download_url_key") ?? ""
        }
        set {
            UserDefaults.standard.setValue(newValue, forKey: "k_home_life_download_url_key")
            UserDefaults.standard.synchronize()
        }
    }
    
    public var isHideLHTestPaper: Bool {
        get {
            return UserDefaults.standard.bool(forKey: "isHideLHTestPaper")
        }
        set {
            UserDefaults.standard.setValue(newValue, forKey: "isHideLHTestPaper")
            UserDefaults.standard.synchronize()
        }
    }
}
