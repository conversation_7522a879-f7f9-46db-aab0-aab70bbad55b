//
//  HomeDataSingleton.swift
//  HormoneLife
//
//  Created by Tank on 2024/10/16.
//

import Foundation

class HomeDataSingleton {
    private static var sharedInstance: HomeDataSingleton!
    
    static func shared() -> HomeDataSingleton {
        if sharedInstance == nil {
            sharedInstance = HomeDataSingleton()
        }
        return sharedInstance!
    }
    
    var homeData: HomeData?
}
