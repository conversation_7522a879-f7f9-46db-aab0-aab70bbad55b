//
//  Interface.swift
//  MilkTime
//
//  Created by Tank on 2023/6/22.
//

import Foundation

// MARK: - Public-facing interface API
public final class Interface {
    public private(set) static var sharedInstance: Interface!
    
    //以下一定要调用一次
    static func shared() -> Interface {
        if sharedInstance == nil {
            sharedInstance = Interface()
        }
        return sharedInstance!
    }
    
    private var _loggedInUser: LoginUser?
    
    public var loggedInUser: LoginUser? {
        get {
            return _loggedInUser ?? unarchiveUser()
        }

        set {
            _loggedInUser = newValue
            
            // 登录时设置newValue并缓存。 登出时设置nil,并删除缓存
            if let newUser = newValue {
                archive(user: newUser)
            } else {
                UserDefaults.standard.removeObject(forKey: kSavedUserDataKey)
            }
        }
    }
    
    private func archive(user: LoginUser) {
        do {
            let userData = try JSONEncoder().encode(user)
            print(userData, NSHomeDirectory())
            UserDefaults.standard.set(userData, forKey: kSavedUserDataKey)
            UserDefaults.standard.synchronize()
        } catch {
            print("Error archiving person: \(error)")
        }
    }

    private func unarchiveUser() -> LoginUser? {
        guard let userData = UserDefaults.standard.object(forKey: kSavedUserDataKey) as? Data,
            let user = try? JSONDecoder().decode(LoginUser.self, from: userData) else {
            return nil
        }
        return user
    }

    public func isLoggedInUser() -> Bool {
        return loggedInUser != nil
    }
    
    public func login(with user: LoginUser?) {
        guard let u = user else { return }
        loggedInUser = u
        UserDefaults.standard.hasRememberLogingUser = true
    }
    
    public func logout() {
        loggedInUser = nil
        UserDefaults.standard.removeKeysForLogout()
    }
}

public struct LoginUser: Codable {
    var deviceId, token: String?
    let userInfo: UserInfo
    var cycleAndPeriodAvg: CycleAndPeriodAvg?
    
    init(deviceId: String?, token: String?, userInfo: UserInfo) {
        self.deviceId = deviceId
        self.token = token
        self.userInfo = userInfo
    }
}

internal let kSavedUserDataKey = "Saved_User_Data"
