//
//  NetWorkHandle.swift
//
//  Created by Tank on 2024/6/10.
//

import Foundation
import Alamofire

class NetWorkHandle {

    static func requestAPI<T: Codable>(_ api: HormonelifeEndpoint, showHub: Bool = true, completion: @escaping (_ result: T?) -> Void, callback: ((_ data: Data?) -> Void)? = nil, failure: ((_ error: String?) -> Void)? = nil) {
        //print("api.parameters = \(api.parameters)")
        AF.request(api.url, method: api.method, parameters: api.parameters, encoding: api.encoding, headers: api.header).response { resp in
            switch resp.result {
            case .success(let response):
                guard let data = response,
                      let result = try? JSONDecoder().decode(ResponseData<T>.self, from: data) else {
                    //print("JSONDecoder error api.url  = \(api.url)")
                    if let callback = callback {
                        callback(response)
                    } else {
                        failure?("A0212")
                        if showHub {
                            showToachMessage(message: "network error, please try again later")
                        }
                    }

                    return
                }
                let jsonString = String(data: data, encoding: .utf8)
                //print("jsonString = url = \(api.url) \(jsonString)")
                if result.code == "00000" {
                    //print(result)
                    completion(result.data)
                } else {
                    if result.code == "A0212" {
                        //UserDefaults.standard.userToken = nil
                        currentWindow?.rootViewController = UINavigationController(rootViewController: LoginViewController())
                        failure?("A0212")
                    } else {
                        if result.code == "A0240" {
                            failure?(result.code)
                        } else {
                            completion(result.data)
                            if showHub {
                                showToachMessage(message: result.msg)
                            }
                        }
                    }
                }
            
            case .failure(let error):
                failure?(error.localizedDescription)
                if showHub {
                    showToachMessage(message: error.localizedDescription)
                }
            }
        }
    }
    
    static func upload(images: UIImage, params:[String: AnyObject]?,success: @escaping (_ result: [String: Any]) -> Void,failure: @escaping (_ error: String?) -> Void){
        
        let fullStr = baseURL + "/common/upload"
        
        let userToken = UserDefaults.standard.userToken!
        
        let httpHeader: HTTPHeaders = [
            "authorization": "\(Bearer)\(userToken)",
            "Content-type": "multipart/form-data"
        ]
        
       AF.upload(multipartFormData: { (multipartFormData) in
            if params != nil {
                for (key, value) in params! {
                    let data_ = "\(value)".data(using: String.Encoding.utf8)!
                    multipartFormData.append(data_, withName: key)
                }
            }
            
           let timestamp = self.hf_getTimestamp()
           
           if var imgData = images.jpegData(compressionQuality: 0.8) {
               if imgData.count > (1 * 1000 * 1000) {
                   let newImage = resizeImage(images, newWidth: 1500) ?? images
                   imgData = newImage.jpegData(compressionQuality: 0.9)!
               }
               multipartFormData.append(imgData, withName: "file",fileName: "\(timestamp)\(index).png", mimeType: "image/png")
           }
        
        }, to: fullStr ,method: .post, headers: httpHeader) { (response) in
            //print("")
        }.responseJSON { response in
            DispatchQueue.main.async {
                switch response.result{
                case .success(let value):
                    if let resp = value as?[String:AnyObject] {
                        if let result = resp["code"] as? String {
                            if result == "00000" {
                                if let data = resp["data"] as? [String: Any] {
                                    success(data)
                                } else {
                                    success(resp)
                                }
                            } else {
                                
                                if result == "A0212" {
                                    DispatchQueue.main.async {
                                        //UserDefaults.standard.userToken = nil
                                        currentWindow?.rootViewController = UINavigationController(rootViewController: LoginViewController())
                                    }
                                } else {
                                    //showToachMessage(message: "network error!")
                                }
                                failure(resp["msg"] as? String ?? "")
                            }
                        } else {
                            //showToachMessage(message: "network error!")
                            failure("network error!")
                        }
                    } else {
                        //showToachMessage(message: "network error!")
                        failure("nework error!")
                    }
                case .failure(let error):
                    showToachMessage(message: error.localizedDescription)
                    failure(error.localizedDescription)
                }
            }
        }
    }
    
    static func requestPost(_ url: String, parameters: [String: Any]?, showHub: Bool = true, success: @escaping (_ result: [String: Any]) -> Void,failure: @escaping (_ error: String?) -> Void) {
        
        AF.request(baseURL + url, method: .post, parameters: parameters, encoding: JSONEncoding.default, headers: self.header).responseJSON(queue: DispatchQueue.global(), options: .allowFragments) { (response) in
            switch response.result{
            case .success(let value):
                if let resp = value as?[String:AnyObject] {
                    if let result = resp["code"] as? String {
                        if result == "00000" {
                            DispatchQueue.main.async {
                                if let data = resp["data"] as? [String: Any] {
                                    success(data)
                                } else {
                                    success(resp)
                                }
                            }
                        } else {
                            DispatchQueue.main.async {
                                if result == "A0212" {
                                    DispatchQueue.main.async {
                                        //UserDefaults.standard.userToken = nil
                                        currentWindow?.rootViewController = UINavigationController(rootViewController: LoginViewController())
                                    }
                                } else {
                                    if showHub == true {
                                        showToachMessage(message: "network error!")
                                    }
                                }
                                failure(resp["msg"] as? String ?? "")
                            }
                        }
                    } else {
                        DispatchQueue.main.async {
                            if showHub == true {
                                showToachMessage(message: "network error!")
                            }
                            failure("network error!")
                        }
                    }
                } else {
                    DispatchQueue.main.async {
                        if showHub == true {
                            showToachMessage(message: "network error!")
                        }
                        
                        failure("nework error!")
                    }
                }
            case .failure(let error):
                DispatchQueue.main.async {
                    if showHub == true {
                        showToachMessage(message: error.localizedDescription)
                    }
                   
                    failure(error.localizedDescription)
                }
            }
        }
    }
    
    static var header: HTTPHeaders? {
        switch self {
        default:
//            guard let loginUser = Interface.shared().loggedInUser else { return nil }
            guard let userToken = UserDefaults.standard.userToken else { return nil }
            return ["authorization": "\(Bearer)\(userToken)"]
        }
    }
    
    static func requestGet(_ url: String, parameters: [String: Any]?, showHub: Bool = true , success: @escaping (_ result: [String:Any]) -> Void,failure: @escaping (_ error: String?) -> Void) {

        AF.request(baseURL + url, method: .get, parameters: parameters, encoding: URLEncoding.default, headers: self.header).responseJSON(queue: DispatchQueue.global(), options: .allowFragments) { (response) in
            switch response.result{
            case .success(let value):
                if let resp = value as?[String:AnyObject] {
                    if let result = resp["code"] as? String {
                        if result == "00000" {
                            DispatchQueue.main.async {
                                if let data = resp["data"] as? [String: Any] {
                                    success(data)
                                } else {
                                    success(resp)
                                }
                            }
                        } else {
                            
                            if result == "A0212" {
                                DispatchQueue.main.async {
                                    //UserDefaults.standard.userToken = nil
                                    currentWindow?.rootViewController = UINavigationController(rootViewController: LoginViewController())
                                }
                            } else {
                                showToachMessage(message: "network error!")
                            }
                            DispatchQueue.main.async {
                                failure(resp["msg"] as? String ?? "")
                            }
                            
                        }
                    } else {
                        DispatchQueue.main.async {
                            showToachMessage(message: "network error!")
                            failure("network error!")
                        }
                     
                    }
                } else {
                    DispatchQueue.main.async {
                        showToachMessage(message: "network error!")
                        failure("network error!")
                    }
                }
            case .failure(let error):
                DispatchQueue.main.async {
                    showToachMessage(message: error.localizedDescription)
                    
                    failure(error.localizedDescription)
                }
                
            }
        }
    }
    
    static func hf_getTimestamp() -> Int {
        return Int(Date().timeIntervalSince1970)
    }
    
    static func requestPostForFullUrl(_ fullUrl: String, parameters: [String: Any]?, showHub: Bool = true, success: @escaping (_ result: [String: Any]) -> Void,failure: @escaping (_ error: String?) -> Void) {
        
        AF.request(fullUrl, method: .post, parameters: parameters, encoding: JSONEncoding.default, headers: self.header).responseJSON(queue: DispatchQueue.global(), options: .allowFragments) { (response) in
            switch response.result{
            case .success(let value):
                if let resp = value as?[String:AnyObject] {
                    if let statusCode = resp["statusCode"] as? Int,
                       statusCode == 200,
                       let body = resp["body"] as? String,
                       let jsonData = body.data(using: .utf8) {
                        do {
                            if let dictionary = try JSONSerialization.jsonObject(with: jsonData, options: []) as? [String: Any],
                               let resul = dictionary["data"] as? [String : Any] {
                                success(resul)
                            }
                        } catch {
                            failure(error.localizedDescription)
                        }
                    } else if let statusCode = resp["statusCode"] as? Int,
                              statusCode == 500,
                              let body = resp["body"] as? String,
                              let jsonData = body.data(using: .utf8) {
                        
                        do {
                            if let dictionary = try JSONSerialization.jsonObject(with: jsonData, options: []) as? [String: Any],
                            let errorCode = dictionary["code"] as? Int {
                                print("photo error code: ", dictionary["code"])
                                failure("\(errorCode)")
                            }
                        } catch {
                            failure(error.localizedDescription)
                        }
                    } else {
                        DispatchQueue.main.async {
                            if showHub == true {
                                showToachMessage(message: resp["msg"] as? String ?? "network error!")
                            }
                            failure(resp["msg"] as? String ?? "")
                        }
                    }
                } else {
                    DispatchQueue.main.async {
                        if showHub == true {
                            showToachMessage(message: "network error!")
                        }
                        failure("network error!")
                    }
                }
            case .failure(let error):
                DispatchQueue.main.async {
                    if showHub == true {
                        showToachMessage(message: error.localizedDescription)
                    }
                   
                    failure(error.localizedDescription)
                }
            }
        }
    }
}

struct ResponseData<T: Codable>: Codable {
    let code, msg: String
    let pages, total: Int?
    let data: T?

    enum CodingKeys: String, CodingKey {
        case code, msg
        case pages, total
        case data
    }
}

enum HormonelifeEndpoint {
    case appInfoList  //
    case appInfo(type: Int) 
    case sendValidate(type: SendValidate)
    case feedback(content: FeedbackContent)
    case login(deviceId: String, password: String, username: String, grantType: GrantType, code: String, uid: String?, appleAccount: String? = nil, facebookAccount: String? = nil, googleAccount: String? = nil)
    case logout
    case registerByCode(account: String, area: String, code: String, codeType: LoginType, deviceId: String, email: String, phone: String, firstName: String, userName: String, password: String, inviteCode: String = "")
    case registerByTourist(deviceId: String)
    case getUserInfo
    case findPassword(account: String, code: String, codeType: LoginType, newPassword: String)
    case homeData
    case ad(position: AdvertisementType)
    case configUpdate(config: UserConfig)
    case bindEmail(email: String, code: String)
    case bindPhone(phone: String, code: String)
    case unBindThirdPartAccount(grantType: String)
    case logOff(account: String, code: String, codeType: LoginType)
    case roleUpdate(roleType: RoleType)
    case updateUserInfo(userName: String, firstName: String, sex: Int, birthday: String, headImg: String, label: String, photos: String)
    case addNote(newNote: Note)
    case updateNote(newNote: Note)
    case noteDetail(id: Int)
    case deleteNote(id: Int)
    case messages(page: Int, pageSize: Int, type: MessageType, isRead: Int)
    case message(messageId: String)
    case readMessage(messageId: String)
    case deleteMessage(messageId: String)
    
    case babyList(page: Int, pageSize: Int)
    case oss
    case userPeriodCycle(page: Int, pageSize: Int,userId: String?, userName: String?, title: String?, searchEndCycleTime: String?, searchStartCycleTime: String?)
    case userPeriodCycleByYear(userId: String?, userName: String?, title: String?, searchEndCycleTime: String?, searchStartCycleTime: String?)
    case updateTestPage(id: String?, imageUrl: String?, pageType: String?, userId: String?, lastResult: String?, markTime: String?)
    case getCalculateResult(imageUrl: String?, pageType: String?)
    case getDetailByMonth(startDate: String, endDate: String)
    case addTestPage(imageUrl: String?, lastResult: String?, ovulation: String?, pageType: String?, pregnancy: String?, resultLabel: String?, resultValue: Double?, markTime: String?)
    case getTestPageList(pageType: String?, fromDate: String?, toDate: String?)
    case deleteTestResult(id: String)
    case getTestPageDetail(id: String)
    case simpleList(pageType: String)
    case calendarInsertTestDetail(id: Int?, cervicalMucus: String?, loveState: Int?, markTime: String?, symptomsFlow: String?, temperature: CGFloat?, temperatureUnit: Int?)
    case menstruationBeginsUpdate(id: Int?, state: Int, selectDate: String?, startOrEnd: Int?, startPeriodTime: String?, endPeriodTime: String?)
    case getCycleAndPeriodAvg
    
    var path: String {
        switch self {
        case .appInfoList: return "/app/information/listAll"
        case .appInfo(let type): return "/app/information/\(type)"
        case .sendValidate: return "/validateCode/sendCode"
        case .feedback: return "/app/feedbackInfo/save"
        case .login: return "/app/login"
        case .logout: return "/app/logout"
        case .registerByCode: return "/app/registerByCode"
        case .registerByTourist: return "/app/registerOrLoginByTourist"
        case .getUserInfo: return "/app/getInfo"
        case .findPassword: return "/app/forgetPassword"
        case .homeData: return "/app/home"
        case .ad(let position): return "/app/advertisement/\(position.rawValue)"
        case .configUpdate: return "/app/userBusinessConfig/update"
        case .bindEmail: return "/app/userInfo/bindEmail"
        case .bindPhone: return "/app/userInfo/bindPhone"
        case .unBindThirdPartAccount: return "/app/unBindAccount"
        case .logOff: return "/app/userInfo/logOff"
        case .roleUpdate(let roleType): return "/app/userInfo/updateRoleType/\(roleType.rawValue)"
        case .updateUserInfo: return "/app/userInfo/updateUserInfo"
        case .addNote: return "/app/userNotes/save"
        case .updateNote: return "/app/userNotes/update"
        case .noteDetail(let id): return "/app/userNotes/\(id)"
        case .deleteNote(let id): return "/app/userNotes/\(id)"
        case .messages: return "/app/sysMessageUser/page"
        case .message(let id): return "/app/sysMessageUser/\(id)"
        case .readMessage(let id): return "/app/sysMessageUser/\(id)"
        case .deleteMessage(let id): return "/app/sysMessageUser/\(id)"
        
        case .babyList: return "/api/breast/baby/list"
        case .oss: return "/api/base/oss/queryOssStsCert"
            
        case .userPeriodCycle: return "/app/userPeriodCycle/page"
        case .userPeriodCycleByYear: return "/app/userPeriodCycle/listByYear"
        case .addTestPage: return "/app/userTestPage/createTestPage"
        case .updateTestPage: return "/app/userTestPage/updateTestPage"
        case .getCalculateResult: return "/app/userTestPage/getCalculateResult"
        case .getTestPageList: return "/app/userTestPage/page"
        case .getDetailByMonth: return "/app/userPeriodCycle/getDetailByMonth"
        case .deleteTestResult(let id): return "/app/userTestPage/delete/\(id)"
        case .getTestPageDetail(let id): return "/app/userTestPage/\(id)"
        case .simpleList: return "/app/userPeriodCycle/simpleList"
        case .calendarInsertTestDetail: return "/app/userCalendar/insertOrUpdate"
        case .menstruationBeginsUpdate: return "/app/userPeriodCycle/updatePeriod"
        case .getCycleAndPeriodAvg: return "/app/userPeriodCycle/getCycleAndPeriodAvg"
        }
    }
    
    var url: String {
        return baseURL + path
    }
    
    var method: HTTPMethod {
        switch self {
        case .appInfoList, .appInfo, .getUserInfo, .homeData, .ad, .noteDetail, .message, .babyList, .oss, .getTestPageDetail, .getCycleAndPeriodAvg: return .get
        case .sendValidate, .feedback, .login, .logout, .registerByCode, .registerByTourist, .findPassword, .configUpdate, .bindEmail, .bindPhone, .unBindThirdPartAccount, .logOff, .roleUpdate, .updateUserInfo, .addNote, .updateNote, .messages, .readMessage, .userPeriodCycle, .userPeriodCycleByYear, .updateTestPage, .getCalculateResult, .getDetailByMonth, .addTestPage, .getTestPageList, .simpleList, .calendarInsertTestDetail, .menstruationBeginsUpdate: return .post
        case .deleteMessage, .deleteNote, .deleteTestResult: return .delete
        }
    }
    
    //Requests with GET method cannot have body data
    var parameters: Parameters? {
        switch self {
        case let .babyList(page, pageSize):
            return ["pageNum": page, "pageSize": pageSize]
        case let .sendValidate(type):
            return ["account": type.account ?? "", "codeType": type.codeType?.rawValue ?? "", "messageCodeType": type.messageCodeType?.rawValue ?? "", "areaCode": "1"]
        case let .feedback(content):
            return ["contactInfo": content.contactInfo ?? "",
                    "createTime": content.createTime ?? "",
                    "eventStatusId": content.eventStatusID ?? "",
                    "eventStatusName": content.eventStatusName ?? "",
                    "eventTime": content.eventTime ?? "",
                    "feedbackCode": content.feedbackCode ?? "",
                    "feedbackContent": content.feedbackContent ?? "",
                    "feedbackImg": content.feedbackImg ?? "",
                    "feedbackStatus": content.feedbackStatus ?? 0,
                    "feedbackUserId": content.feedbackUserID ?? "",
                    "feedbackUserName": content.feedbackUserName ?? "",
                    "feedbackUserNo": content.feedbackUserNo ?? "",
                    "feedbackUserRegisterTime": content.feedbackUserRegisterTime ?? "",
                    "id": content.id ?? "",
                    "replyContent": content.replyContent ?? "",
                    "replyTime": content.replyTime ?? "",
                    "replyUserId": content.replyUserID ?? "",
                    "replyUserName": content.replyUserName ?? "",
                    "version": content.version ?? ""]
        case let .login(deviceId, password, username, grantType, code, uid, appleAccount, facebookAccount, googleAccount):
            
            var params = ["clientType": "ios", "grantType" : grantType.rawValue]
            if deviceId.count > 0 {
                params["deviceId"] = deviceId
            }
            if password.count > 0 {
                params["password"] = password
            }
            if username.count > 0 {
                params["username"] = username
            }
            
            if let appleAccount = appleAccount {
                params["appleAccount"] = appleAccount
            }
            if let facebookAccount = facebookAccount {
                params["facebookAccount"] = facebookAccount
            }
            if let googleAccount = googleAccount {
                params["googleAccount"] = googleAccount
            }
            
            if code.count > 0 {
                params["code"] = code
            }
            if let uid = uid, uid.count > 0 {
                params["uid"] = uid
            }
            return params
        case let .registerByCode(account, area, code, codeType, deviceId, email, phone, firstName, userName, password, inviteCode):
            
            var params = ["account": account,
                    "code": code,
                    "codeType": codeType.rawValue,
                    "deviceId": deviceId,
                    "email": email,
                    "firstName": firstName,
                    "password": password,
                    "phone": phone,
                    "userName": userName,
                    "inviteCode": inviteCode]
            if area.count > 0 {
                params["area"] = area
            }
            return params
        case let .registerByTourist(deviceId):
            return ["deviceId": deviceId]
        case let .findPassword(account, code, codeType, newPassword):
            return ["account": account,
                    "code": code,
                    "codeType": codeType.rawValue,
                    "newPassword": newPassword]
        case let .configUpdate(config):
            var params = [String : String]()
            
            if let createBy = config.createBy, createBy.count > 0 {
                params["createBy"] = createBy
            }
            if let createTime = config.createTime, createTime.count > 0 {
                params["createTime"] = createTime
            }
            if let id = config.id, id.count > 0 {
                params["id"] = id
            }
            if let menstruationCycleAvg = config.menstruationCycleAvg, menstruationCycleAvg.count > 0 {
                params["menstruationCycleAvg"] = menstruationCycleAvg
            }
            if let msgContentOn = config.msgContentOn, msgContentOn.count > 0 {
                params["msgContentOn"] = msgContentOn
            }
            if let msgNoticeOn = config.msgNoticeOn, msgNoticeOn.count > 0 {
                params["msgNoticeOn"] = msgNoticeOn
            }
            if let period = config.period, period.count > 0 {
                params["period"] = period
            }
            if let startPeriodDate = config.startPeriodDate, startPeriodDate.count > 0 {
                params["startPeriodDate"] = startPeriodDate
            }
            if let trackMethod = config.trackMethod, trackMethod.count > 0 {
                params["trackMethod"] = trackMethod
            }
            if let updateBy = config.updateBy, updateBy.count > 0 {
                params["updateBy"] = updateBy
            }
            if let updateTime = config.updateTime, updateTime.count > 0 {
                params["updateTime"] = updateTime
            }
            if let userId = config.userID, userId.count > 0 {
                params["userId"] = userId
            }
            if let otherTrackMethod = config.otherTrackMethod {
                params["otherTrackMethod"] = otherTrackMethod
            }
            if let testReminderOn = config.testReminderOn {
                params["testReminderOn"] = testReminderOn
            }
            if let fertilityReminderOn = config.fertilityReminderOn {
                params["fertilityReminderOn"] = fertilityReminderOn
            }
            if let fshTime = config.fshTime {
                params["fshTime"] = fshTime
            }
            if let lhTime = config.lhTime {
                params["lhTime"] = lhTime
            }
            if let pdgTime = config.pdgTime {
                params["pdgTime"] = pdgTime
            }
            
            return params
            
        case let .bindEmail(email, code):
            return ["code": code, "email": email]
        case let .bindPhone(phone, code):
            return ["code": code, "phone": phone]
        case .unBindThirdPartAccount(let grantType):
            return ["grantType": grantType]
        case let .logOff(account, code, codeType):
            return ["account": account, "code": code, "codeType": codeType.rawValue]
        case let .updateUserInfo(userName, firstName, sex, birthday, headImg, label, photos):
            var params = ["sex" : sex] as [String : Any]
            if birthday.count > 0 {
                params["birthday"] = birthday
            }
            if firstName.count > 0 {
                params["firstName"] = firstName
            }
            if headImg.count > 0 {
                params["headImg"] = headImg
            }
            if label.count > 0 {
                params["label"] = label
            }
            if photos.count > 0 {
                params["photos"] = photos
            }
            if userName.count > 0 {
                params["userName"] = userName
            }
            return params
        case let .addNote(newNote):
            return ["content": newNote.content,
                    "imageUrls": newNote.imageUrls,
                    "markTime": newNote.markTime,
                    "medicationHistory": newNote.medicationHistory,
                    "vitaminHistory": newNote.vitaminHistory]
        case let .updateNote(newNote):
            return ["content": newNote.content,
                    "imageUrls": newNote.imageUrls,
                    "markTime": newNote.markTime,
                    "medicationHistory": newNote.medicationHistory,
                    "vitaminHistory": newNote.vitaminHistory,
                    "id": newNote.id ?? 0,
                    "userId": newNote.userID ?? 0]
        case let .messages(page, pageSize, type, isRead):
            var param = [String: Any]()
//            if let type = type {
//                param["type"] = type
//            }
            if isRead <= 1 { param["isRead"] = isRead }
            return ["page": page,
                    "pageSize": pageSize,
                    "param": param]
        case .readMessage:
            return nil //["isRead": 1]
        case let .userPeriodCycle(page, pageSize,userId, userName, title, searchEndCycleTime, searchStartCycleTime):
            var params = [String : Any]()
            if let userId = userId {
                params["userId"] = userId
            }
            if let userName = userName {
                params["userName"] = userName
            }
            if let title = title {
                params["title"] = title
            }
            if let searchEndCycleTime = searchEndCycleTime {
                params["searchEndCycleTime"] = searchEndCycleTime
            }
            if let searchStartCycleTime = searchStartCycleTime {
                params["searchStartCycleTime"] = searchStartCycleTime
            }
            return ["page": page,
                    "pageSize": pageSize,
                    "param": params]
        case let .userPeriodCycleByYear(userId, userName, title, searchEndCycleTime, searchStartCycleTime):
            return [
                "userId": userId ?? "",
                "userName": userName ?? "",
                "title": title ?? "",
                "searchStartCycleTime": searchStartCycleTime ?? "",
                "searchEndCycleTime": searchEndCycleTime ?? ""
            ]

        case let .updateTestPage(id, imageUrl, pageType, userId, lastResult, markTime):
            var params = [String : Any]()
            if let userId = userId {
                params["userId"] = userId
            }
            if let pageType = pageType {
                params["pageType"] = pageType
            }
            if let markTime = markTime {
                params["markTime"] = markTime
            }
            if let lastResult = lastResult {
                params["lastResult"] = lastResult
            }
            if let imageUrl = imageUrl {
                params["imageUrl"] = imageUrl
            }
            if let id = id {
                params["id"] = id
            }
            return params
        case let .addTestPage(imageUrl, lastResult, ovulation, pageType, pregnancy, resultLabel, resultValue, markTime):
            var params = [String : Any]()
            if let imageUrl = imageUrl {
                params["imageUrl"] = imageUrl
            }
            if let pageType = pageType {
                params["pageType"] = pageType
            }
            if let markTime = markTime {
                params["markTime"] = markTime
            }
            if let lastResult = lastResult {
                params["lastResult"] = lastResult
            }
            if let ovulation = ovulation {
                params["ovulation"] = ovulation
            }
            if let pregnancy = pregnancy {
                params["pregnancy"] = pregnancy
            }
            if let resultLabel = resultLabel {
                params["resultLabel"] = resultLabel
            }
            if let resultValue = resultValue {
                params["resultValue"] = resultValue
            }
            return params
        case let .getTestPageList(pageType, fromDate, toDate):
            var params = [String : Any]()
            if let pageType = pageType {
                params["pageType"] = pageType
            }
            if let fromDate = fromDate {
                params["markTimeStartTime"] = fromDate
            }
            if let toDate = toDate {
                params["markTimeEndTime"] = toDate
            }
            return ["param": params, "page": 1, "pageSize": 1000]
        case let .getCalculateResult(imageUrl, pageType):
            var params = [String : Any]()
           
            if let pageType = pageType {
                params["type"] = pageType
            }
            
            if let imageUrl = imageUrl {
                params["imageUrl"] = imageUrl
            }
            
            return params
        case let .getDetailByMonth(startDate, toDate):
            return [
                "searchStartCycleTime": startDate,
                "searchEndCycleTime": toDate
            ]
        case .simpleList(let pageType):
            return ["pageType": pageType]
        case let .calendarInsertTestDetail(id, cervicalMucus, loveState, markTime, symptomsFlow, temperature, temperatureUnit):
            
            var params = [String : Any]()
            if let id = id {
                params["id"] = id
            }
            
            if let cervicalMucus = cervicalMucus, cervicalMucus.count > 0 {
                params["cervicalMucus"] = cervicalMucus
            }
            
            if let loveState = loveState,loveState >= 0  {
                params["loveState"] = loveState
            }
            
            if let markTime = markTime, markTime.count > 0 {
                params["markTime"] = markTime
            }
            
            if let symptomsFlow = symptomsFlow, symptomsFlow.count > 0 {
                params["symptomsFlow"] = symptomsFlow
            }
            
            if let temperature = temperature, temperature > 0 {
                params["temperature"] = temperature
                if let temperatureUnit = temperatureUnit {
                    params["temperatureUnit"] = temperatureUnit
                }
            }
            return params
        case let .menstruationBeginsUpdate(id, state, selectDate, startOrEnd, startPeriodTime, endPeriodTime):
            var params = [String : Any]()
            params["state"] = state
            if let id = id {
                params["id"] = id
            }
            
            if let startPeriodTime = startPeriodTime {
                params["startPeriodTime"] = startPeriodTime
            }

            if let endPeriodTime = endPeriodTime {
                params["endPeriodTime"] = endPeriodTime
            }
            if let selectDate = selectDate {
                params["date"] = selectDate
            }
            
            if let startOrEnd = startOrEnd {
                params["startOrEnd"] = startOrEnd
            }
            return params
        default: return nil
        }
    }
    
    var header: HTTPHeaders? {
        switch self {
        default: 
//            guard let loginUser = Interface.shared().loggedInUser else { return nil }
            guard let userToken = UserDefaults.standard.userToken else { return nil }
            return ["authorization": "\(Bearer)\(userToken)"]
        }
    }
    
    var encoding: ParameterEncoding {
        switch self.method {
        case .post: return JSONEncoding.default
        default: return URLEncoding.default
        }
    }
}
