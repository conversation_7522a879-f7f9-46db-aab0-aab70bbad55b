//
//  AppDelegate.swift
//  HormoneLife
//
//  Created by Tank on 2024/5/11.
//

import UIKit
import FirebaseCore
import FirebaseMessaging
import IQ<PERSON>eyboardManagerSwift
import FBSDKCoreKit
import FBSDKLoginKit
import GoogleSignIn

@main
class AppDelegate: UIResponder, UIApplicationDelegate {
    var window: UIWindow?
    
    func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?) -> Bool {
        
        getDeviceId()
        
        window = UIWindow(frame: UIScreen.main.bounds)
        window?.backgroundColor = .white
        window?.rootViewController = getRootViewController()
        window?.makeKeyAndVisible()
        FirebaseApp.configure()
        
        //第一步：获取推送通知中心
        let center = UNUserNotificationCenter.current()
        center.delegate = self
        center.requestAuthorization(options: [.alert, .sound, .badge]) { granted, error in
            if error == nil {
                DispatchQueue.main.async {
                    UIApplication.shared.registerForRemoteNotifications()
                }
            }
        }
        IQKeyboardManager.shared.enable = true
        
        ApplicationDelegate.shared.application(application, didFinishLaunchingWithOptions: launchOptions)
        self.getStartUp()
        return true
    }
    
    func application(_ application: UIApplication, didRegisterForRemoteNotificationsWithDeviceToken deviceToken: Data) {
        Messaging.messaging().token(completion: {_,_ in
            
        })
        Messaging.messaging().apnsToken = deviceToken
    }

    func application( _ app:UIApplication, open url:URL, options: [UIApplication.OpenURLOptionsKey :Any] = [:] ) -> Bool {
        if
            ApplicationDelegate.shared.application( app, open: url, sourceApplication: options[UIApplication.OpenURLOptionsKey.sourceApplication] as? String, annotation: options[UIApplication.OpenURLOptionsKey.annotation]) == true {
            return true
        } else {
            return GIDSignIn.sharedInstance.handle(url)
        }
    }
 
    private func getRootViewController() -> UIViewController {
        guard UserDefaults.standard.userToken != nil,
              !UserDefaults.standard.isLoginAsGuestWithoutAccount
        else {
            return UINavigationController(rootViewController: LoginViewController())
        }
        
        guard let user = Interface.shared().loggedInUser?.userInfo, user.userBusinessConfigVO.menstruationCycleAvg > 0 else {
            UserDefaults.standard.hasSeemGuidelinePage = false
            UserDefaults.standard.isLoginAsGuestWithoutAccount = false
            UserDefaults.standard.isFristTimeChooseGoal = true
            return BaseNavigationController(rootViewController: GoalSettingViewController())
        }
        
        return TabBarController()
    }
    
    func configThird() {
        
    }
    
    private func getDeviceId() {
        guard let deviceID = UserDefaults.standard.deviceID else {
            UserDefaults.standard.deviceID = UUID().uuidString
            return
        }
        print("deviceID: ", deviceID)
    }
    
    func getStartUp() {
        let view = LuanchAdView(frame: .zero, img: "")
        getKeyWindow()?.addSubview(view)
        HomeInteractor.fetchAdOnPosition(.startup) { result in
            if let res = result.first, let img = res.img {
//                let view = LuanchAdView(frame: .zero, img: img)
//                getKeyWindow()?.addSubview(view)
                view.refresh(img: img)
            } else {
                
            }
        }
    }
}

extension AppDelegate : UNUserNotificationCenterDelegate {
    
}
