//
//  BaseViewController.swift
//  MilkTime
//
//  Created by Match on 2023/6/17.
//

import UIKit
import EventKit

class BaseViewController: UIViewController {
    
    //public var leftButton: UIBarButtonItem?
    
    override func viewDidLoad() {
        super.viewDidLoad()
//        view.backgroundColor = .white
        //navigationController?.navigationBar.barStyle = .default
        //navigationController?.navigationBar.setBackgroundImage(UIImage(), for: .default)
        navigationController?.navigationBar.backgroundColor = .white
        navigationController?.navigationBar.tintColor = .mainTextColor
        navigationController?.navigationBar.isTranslucent = false

        let appearance = UINavigationBarAppearance()
        appearance.configureWithOpaqueBackground()
        appearance.backgroundColor = .white//UIColor(red: 194/255, green: 171/255, blue: 152/255, alpha: 1)
        appearance.shadowColor = .white
        appearance.shadowImage = UIImage()
        appearance.titleTextAttributes = [NSAttributedString.Key.foregroundColor: UIColor.mainTextColor, NSAttributedString.Key.font: UIFont.mediumGilroyFont(22)]
        navigationController?.navigationBar.standardAppearance = appearance
        navigationController?.navigationBar.scrollEdgeAppearance = navigationController?.navigationBar.standardAppearance
        
        setNavigationBar()
        
        /*
        if presentationController != nil && self == navigationController?.viewControllers.first {
            print("left")
        } else if self != navigationController?.viewControllers.first {
            let button = UIButton(type: .custom)
            button.setBackgroundImage(UIImage(named: ""), for: .normal)
            button.setTitle("Back", for: .normal)
            button.tintColor = .black
            button.setTitleColor(.black, for: .normal)
            button.addTarget(self, action: #selector(backButtonlock), for: .touchUpInside)
            
            if #available(iOS 13.0, *) {
                button.showsLargeContentViewer = true
            }
            
            leftButton = UIBarButtonItem(customView: button)
            navigationItem.leftBarButtonItem = leftButton
        } else {
            navigationItem.leftBarButtonItem = nil
        }
        */
    }
    
    private func setNavigationBar() {
        navigationItem.leftBarButtonItem = UIBarButtonItem(image: UIImage(named: "backIcon"), style: .done, target: self, action: #selector(didTapBackButton))
    }

    @objc func didTapBackButton() {
        navigationController?.popViewController(animated: true)
    }
    
//    @objc func backButtonlock() {
//        self.navigationController?.popViewController(animated: true)
//    }
    
    override func touchesEnded(_ touches: Set<UITouch>, with event: UIEvent?) {
        view.endEditing(false)
    }
    
    func checkToken() {
        
    }
    
    func popCanlendar(_ completion: @escaping (Date) -> Void) {
        let vc = CalendarPopupViewController(beforeCurrentMonth: 600, afterCurrentMonth: 50, isNextMonthEnable: true, isPreMonthEnable: true, completion: completion)
        let configuration = ModalConfiguration.default
        configuration.direction = .bottom
        configuration.isEnableShadow = false
        configuration.animationDuration = 0.2
        let size = CGSize(width: UIScreen.main.bounds.size.width, height: 400)

        let topVC = topMostController()
        topVC?.presentModalViewController(vc, contentSize: size, configuration: configuration, completion: nil)
    }
    
    //同步事件到日历
    func syncToSystemEvent(_ title: String, startDate: Date, endDate: Date, alarmTime: TimeInterval = -300) {
        //获取日历权限
        let eventStore = EKEventStore()
        eventStore.requestAccess(to: .event) { isAllow, error in
            if isAllow {
                
                let event = EKEvent(eventStore: eventStore)
                event.title = title
                //event.notes = "this is notes"

                event.calendar = eventStore.defaultCalendarForNewEvents
                event.calendar.cgColor = UIColor.systemPink.cgColor
                event.calendar.title = "suck"

                event.startDate = startDate
                event.endDate = endDate
                print(event.calendar.calendarIdentifier)

                event.addAlarm(EKAlarm(relativeOffset: alarmTime)) //提前通知

                do {

                    try eventStore.save(event, span: .thisEvent, commit: true)

                    DispatchQueue.main.async {
                        //新增成功后的处理
                        //DFUITools.showText("已同步到系统事件", on: currentWindow, delay: 2.0)
                    }
                } catch  {
                    //DFUITools.showText(error.localizedDescription, on: currentWindow, delay: 2.0)
                }
            } else {
//                print(error?.localizedDescription)
//                DFUITools.showText(error?.localizedDescription, on: currentWindow, delay: 2.0)
            }
        }
    }
    
    //同步提醒事件到日历
    func syncToSystemReminder(_ title: String, reminderDate: Date) {
        //获取日历权限
        let eventStore = EKEventStore()
        eventStore.requestAccess(to: .reminder) { isAllow, error in
            if isAllow {
                let reminder = EKReminder(eventStore: eventStore)
                
                reminder.title = title
                reminder.calendar = eventStore.defaultCalendarForNewReminders()
                let date = reminderDate
                reminder.addAlarm(EKAlarm(absoluteDate: date)) //不提前

                do {

                    try eventStore.save(reminder, commit: true)

                    DispatchQueue.main.async {
                        //新增成功后的处理
                        //DFUITools.showText("已同步到系统提醒", on: currentWindow, delay: 2.0)
                    }
                } catch  {
                    //DFUITools.showText(error.localizedDescription, on: currentWindow, delay: 2.0)
                }
            } else {
//                print(error?.localizedDescription)
//                DFUITools.showText(error?.localizedDescription, on: currentWindow, delay: 2.0)
            }
        }
    }
}
