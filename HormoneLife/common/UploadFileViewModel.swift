//
//  UploadFileViewModel.swift
//  HormoneLife
//
//  Created by loct on 7/29/24.
//

import UIKit

class UploadFileViewModel: NSObject {
    
    var url : String = ""
    var fileName: String = ""
    
    func uploadImages(images: UIImage, _ success: @escaping (_ url: String, _ fileName: String) -> Void,failure: @escaping (_ error: String?) -> Void) {
        
        //url , fileName
        NetWorkHandle.upload(images: images, params: nil) { result in
            self.url = result["url"] as? String ?? ""
            self.fileName = result["url"] as? String ?? ""
            success(result["url"] as? String ?? "", result["url"] as? String ?? "")
        } failure: { error in
            failure(error)
        }
    }
}
