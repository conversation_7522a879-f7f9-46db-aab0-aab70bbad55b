
import UIKit
import SnapKit

class AutoDismissToastView: UIControl {
    
    var message: String = ""
    @objc init(message:String) {
        super.init(frame: UIScreen.main.bounds)
        self.message = message
        createSubviews()
        DispatchQueue.main.asyncAfter(deadline: DispatchTime.now()+2) {
            self.disMiss()
        }
    }
    
    func createSubviews() {
        
        self.tag = 2323432
        
        if let _ = UIApplication.shared.keyWindow?.viewWithTag(2323432) {
            
        } else {
            self.backgroundColor = UIColor(red: 0, green: 0, blue: 0, alpha: 0.2)
        }
        
        let bgView = UIView()
        bgView.layer.cornerRadius = 5
        bgView.layer.masksToBounds = true
        bgView.backgroundColor = UIColor(red: 58/255.0, green: 58/255.0, blue: 58/255.0, alpha: 0.7)
        self.addSubview(bgView)
        
        
        let messageLab = UILabel()
        messageLab.text = self.message
        messageLab.textColor = .white
        messageLab.numberOfLines = 0
        messageLab.font = .regularGilroyFont(18)
        messageLab.layer.cornerRadius = 5
        messageLab.layer.masksToBounds = true
        //messageLab.backgroundColor = UIColor(red: 58/255.0, green: 58/255.0, blue: 58/255.0, alpha: 0.7)
        addSubview(messageLab)
//        let height = _stringHeight(width: width-40, font: UIFont.systemFont(ofSize: 20))
        var width = String.width(text: self.message, textHeight: 16, font: messageLab.font) + 20
        if width > kScreenWidth - 100 {
            width = kScreenWidth - 100
        }
        messageLab.snp.makeConstraints { (make) in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview().offset(-30)
            make.height.greaterThanOrEqualTo(14)
            make.width.equalTo(width)
        }
        
        bgView.snp.makeConstraints { make in
            make.top.equalTo(messageLab).offset(-5)
            make.left.equalTo(messageLab.snp.left).offset(-10)
            make.bottom.equalTo(messageLab).offset(5)
            make.right.equalTo(messageLab.snp.right).offset(10)
        }
        
        let style = NSMutableParagraphStyle()
        style.alignment = .center
//        style.firstLineHeadIndent = 10
//        style.headIndent = 10
//        style.tailIndent = -10
        messageLab.attributedText = NSAttributedString(string: self.message, attributes: [NSAttributedString.Key.paragraphStyle:style])
        
        self.addTarget(self, action: #selector(disMiss), for: .touchUpInside)
    }
    
    @objc func show() {
        DispatchQueue.main.async {
            getKeyWindow()!.addSubview(self)
        }
    }
    
    @objc func disMiss() {
        self.removeFromSuperview()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func _stringHeight(text: String? = nil, width: CGFloat, font: UIFont) -> CGFloat {
        let rect = ((text ?? "") as NSString).boundingRect(with: CGSize(width: width, height: CGFloat(MAXFLOAT)), options: .usesLineFragmentOrigin, attributes: [NSAttributedString.Key.font: font], context: nil)
        return rect.size.height
    }
    
    override func hitTest(_ point: CGPoint, with event: UIEvent?) -> UIView? {
        let hitView = super.hitTest(point, with: event)
        if hitView == self {
            return nil
        } else {
            return hitView
        }
    }
}
