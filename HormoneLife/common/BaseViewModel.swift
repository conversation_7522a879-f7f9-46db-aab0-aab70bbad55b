
import UIKit

class BaseViewModel: NSObject {
    typealias JPViewModelCallbackBlock = (_ index: Int, _ resultJson: Any) -> Void
    
    typealias JPViewModelSuccessHandleCallback = (_ index:Int, _ result: [String: Any]?)->Void
    
    @objc var successCallback: JPViewModelSuccessHandleCallback?
    
    var hasMoreData = false
    var begingIndex = 1
    var pageSize = 10
    
    var failureCallbackBlock: JPViewModelCallbackBlock?
    
    override init() {
        super.init()
        init_viewModel()
    }
    
    func init_viewModel() {
        
    }
}
