//
//  UIFont+Extension.swift
//  HormoneLife
//
//  Created by Tank on 2024/6/8.
//

import UIKit

public extension UIFont {
    class func  regularGilroyFont(_ fontSize: CGFloat) -> UIFont {
        return UIFont(name: "<PERSON><PERSON>-Regular", size: fontSize) ?? .systemFont(ofSize: fontSize)
    }
    
    class func boldGilroyFont(_ fontSize: CGFloat) -> UIFont {
        return UIFont(name: "Gilroy-Bold", size: fontSize) ?? .boldSystemFont(ofSize: fontSize)
    }
    
    class func  heavyGilroyFont(_ fontSize: CGFloat) -> UIFont {
        return UIFont(name: "<PERSON>roy-Heavy", size: fontSize) ?? .systemFont(ofSize: fontSize)
    }
    
    class func lightGilroyFont(_ fontSize: CGFloat) -> UIFont {
        return UIFont(name: "Gilroy-Light", size: fontSize) ?? .systemFont(ofSize: fontSize)
    }
    
    class func  mediumGilroyFont(_ fontSize: CGFloat) -> UIFont {
        return UIFont(name: "<PERSON><PERSON>-Medium", size: fontSize) ?? .systemFont(ofSize: fontSize)
    }
    
    class func semiBoldGilroyFont(_ fontSize: CGFloat) -> UIFont {
        return UIFont(name: "Gilroy-SemiBold", size: fontSize) ?? .systemFont(ofSize: fontSize)
    }
    
    class func  thinGilroyFont(_ fontSize: CGFloat) -> UIFont {
        return UIFont(name: "Gilroy-Thin", size: fontSize) ?? .systemFont(ofSize: fontSize)
    }
}
