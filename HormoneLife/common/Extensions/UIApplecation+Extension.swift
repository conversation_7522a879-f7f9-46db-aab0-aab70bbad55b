//
//  UIApplecation+Extension.swift
//  HormoneLife
//
//  Created by Tank on 2024/6/9.
//

import Foundation
import UIKit

extension UIApplication {
    class func getSafeAreaInsets() -> UIEdgeInsets {
        if let safeAreaInsets = UIApplication.shared.windows.first?.safeAreaInsets {
            return safeAreaInsets
        } else {
            return .zero
        }
    }
}

extension UIApplication {
    var topViewController: UIViewController? {
        return keyWindow?.rootViewController?.topViewController()
    }
}
 
extension UIViewController {
    func topViewController() -> UIViewController {
        if let presented = self.presentedViewController {
            return presented.topViewController()
        } else if let navigation = self as? UINavigationController {
            return navigation.visibleViewController?.topViewController() ?? self
        } else if let tab = self as? UITabBarController {
            return tab.selectedViewController?.topViewController() ?? self
        } else {
            return self
        }
    }
}
