//
//  UITextField+Extension.swift
//  HormoneLife
//
//  Created by Tank on 2024/6/15.
//

import UIKit

extension UITextField {
    var isEmpty: Bool {
        return text == nil || text == ""
    }
    
    func setAttributedPlaceholer(_ placeholer: String? = nil) {
        let palceholerText = placeholer ?? (self.placeholder ?? "")
        let attributedText = NSMutableAttributedString(string: palceholerText)
        attributedText.setAttributes([
            .foregroundColor: UIColor.mainTextColor.withAlphaComponent(0.6),
            .font: UIFont.regularGilroyFont(14)
        ], range: NSMakeRange(0, attributedText.length))
        attributedPlaceholder = attributedText
    }
}

extension UITextView {
    func underlineWords(words: [String] = []) {
        guard let textString = text else {
            return
        }
        let attributedText = NSMutableAttributedString(string: textString)
        let paragraphStyle = NSMutableParagraphStyle()
        paragraphStyle.lineSpacing = 10
        paragraphStyle.alignment = textAlignment
        
        attributedText.setAttributes([
            .foregroundColor: UIColor.mainTextColor,
            .font: UIFont.regularGilroyFont(14),
            .paragraphStyle: paragraphStyle
        ], range: NSMakeRange(0, attributedText.length))
        
        var underLineWords = words
        if underLineWords.isEmpty {
            underLineWords = [textString]
        }
        
        for word in underLineWords {
            let rangeToUnderline = (textString as NSString).range(of: word)
            
            attributedText.addAttribute(NSAttributedString.Key.link,
                                        value: "scheme",
                                        range: rangeToUnderline)
        }
//        isUserInteractionEnabled = true
        self.attributedText = attributedText
        
        let linkAttributes: [NSAttributedString.Key: Any] = [
           .foregroundColor: UIColor.mainTextColor,
           .underlineStyle: NSUnderlineStyle.single.rawValue
        ]

        self.linkTextAttributes = linkAttributes
    }
}
