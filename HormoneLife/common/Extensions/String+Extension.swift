//
//  String+Extension.swift
//  MilkTime
//
//  Created by Tank on 2024/6/2.
//

import UIKit

extension String {
    
    // for calendar period
    func yyyyMMddHHmmssTOyyyyMMdd() -> String? {
        let dateAry = self.components(separatedBy: " ")
        if !dateAry.isEmpty {
            return dateAry.first
        }
        return nil
    }
    
    static func strWithKey(_ key: String, arguments:[String]? = nil) -> String? {
        var t = LanguageManager.shared.localizedString(key: key)
        if let arguments = arguments, arguments.count > 0 {
            t = String(format: t, arguments: arguments)
        }
        return t
    }
    
    func covertDate(with formatterString: String = "yyyy-MM-dd HH:mm:ss") -> Date {
        let  formatter =  DateFormatter()
        formatter.dateFormat = formatterString
        formatter.timeZone = .current//TimeZone(identifier: "UTC")
        formatter.locale = .current//Locale(identifier: "UTC")
        
        guard let date = formatter.date(from: self) else {
            return Date()
        }
        return date
    }
    
    func isMatching(regex regexString: String) -> Bool {
        do {
            let regex = try NSRegularExpression(pattern: regexString, options: .caseInsensitive)
            let option = NSRegularExpression.MatchingOptions(rawValue: 0)
            let range = NSRange(location: 0, length: count)
            return regex.firstMatch(in: self, options: option, range: range) != nil
        } catch {
            return false
        }
    }
    
    func isValidEmai() -> Bool {
        return isMatching(regex: "^([a-zA-Z0-9._%-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,6}(\\.[a-zA-Z]{2,6})?)$")
    }
    
    func isValidPassword() -> Bool {
        return count > 5
        //return isMatching(regex: "^(?=.*[A-Za-z])(?=.*\\d)[A-Za-z\\d]{6,}$")
    }
    
    static func height(text: String, textWidth: CGFloat, font: UIFont) -> CGFloat {
        var dict: NSDictionary = NSDictionary()
        dict = NSDictionary(object:font,forKey: NSAttributedString.Key.font as NSCopying)
        let rect: CGRect = (text as NSString).boundingRect(with: CGSize(width: textWidth,height: CGFloat(MAXFLOAT)), options: [NSStringDrawingOptions.truncatesLastVisibleLine, NSStringDrawingOptions.usesFontLeading,NSStringDrawingOptions.usesLineFragmentOrigin],attributes: dict as? [NSAttributedString.Key : Any] ,context: nil)
        return rect.size.height
    }

    static func width(text: String, textHeight: CGFloat, font: UIFont) -> CGFloat {
        var dict: NSDictionary = NSDictionary()
        dict = NSDictionary(object:font,forKey: NSAttributedString.Key.font as NSCopying)
        let rect: CGRect = (text as NSString).boundingRect(with: CGSize(width: CGFloat(MAXFLOAT),height: textHeight), options: [NSStringDrawingOptions.truncatesLastVisibleLine, NSStringDrawingOptions.usesFontLeading,NSStringDrawingOptions.usesLineFragmentOrigin],attributes: dict as? [NSAttributedString.Key : Any] ,context: nil)
        return rect.size.width
    }
    
    func yyyyMMddHHmmss_yyyyMMdd() -> String? {
        let dayAndHour = self.components(separatedBy: " ")
        return dayAndHour.first
    }
    
    func yyyyMMddHHmmss_MMdd() -> String? {
        let yyyyMMdd = self.yyyyMMddHHmmss_yyyyMMdd()
        return yyyyMMdd?.dropFirst(5).description
    }
    
    func yyyyMMddHHmmss_MM() -> String? {
        let mmdd = self.yyyyMMddHHmmss_MMdd()
        return mmdd?.components(separatedBy: "-").first
    }
    
    func yyyyMMddHHmmss_enMM() -> String {
        guard let mm = self.yyyyMMddHHmmss_MM() else { return "" }
        return mm.enMonth
    }
    
    func yyyyMMddHHmmss_dd() -> String {
        let mmdd = self.yyyyMMddHHmmss_MMdd()
        return mmdd?.components(separatedBy: "-").last ?? ""
    }
    
    func yyyyMMddHHmmss_HHmmss() -> String? {
        let dayAndHour = self.components(separatedBy: " ")
        return dayAndHour.last
    }
    
    func yyyyMMddHHmmss_HHmm() -> String? {
        let hhmmss = self.yyyyMMddHHmmss_HHmmss()
        return hhmmss?.dropLast(3).description
    }
    
    func yyyyMMddHHmmss_ddHHmm() -> String {
        let dataStr = self
        let day = dataStr.yyyyMMddHHmmss_dd()
        guard let hhmm = dataStr.yyyyMMddHHmmss_HHmm() else {
            return "\(day) 00:00"
        }
        return "\(day) \(hhmm)"
    }
    
    static func diff(from yyyyMMdd: String, toyyyyMMdd: String) -> CGFloat {
        //start day also as 1 day.
        let startTime = yyyyMMdd.covertDate(with: "yyyy-MM-dd")
        let endTime = toyyyyMMdd.covertDate(with: "yyyy-MM-dd")
        let calendar = Calendar.current
        let components = calendar.dateComponents([.day], from: startTime, to: endTime)
        if let dayDiff = components.day {
            return CGFloat(dayDiff)
        } else {
            return 0
        }
    }
    
    static func durationDays(from yyyyMMdd: String, toyyyyMMdd: String) -> CGFloat {
        let startTime = yyyyMMdd.covertDate(with: "yyyy-MM-dd")
        let endTime = toyyyyMMdd.covertDate(with: "yyyy-MM-dd")
        let calendar = Calendar.current
        let components = calendar.dateComponents([.day], from: startTime, to: endTime)
        if let dayDiff = components.day {
            return CGFloat(dayDiff + 1)
        } else {
            return 0
        }
    }
    
    var enMonth: String
    {
        switch Int(self) {
        case 1: return "January"
        case 2: return "February"
        case 3: return "March"
        case 4: return "April"
        case 5: return "May"
        case 6: return "June"
        case 7: return "July"
        case 8: return "August"
        case 9: return "September"
        case 10: return "October"
        case 11: return "November"
        default: return "December"
        }
    }
}
