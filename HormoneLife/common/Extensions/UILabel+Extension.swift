//
//  UILabel+Extension.swift
//  MilkTime
//
//  Created by Tank on 2024/6/2.
//

import UIKit

extension UILabel {
    func underlineWords(words: [String] = []) {
        guard let textString = text else {
            return
        }
        let attributedText = NSMutableAttributedString(string: text ?? "")
        
        var underLineWords = words
        if underLineWords.isEmpty {
            underLineWords = [textString]
        }
        
        for word in underLineWords {
            let rangeToUnderline = (textString as NSString).range(of: word)
            attributedText.addAttribute(NSAttributedString.Key.underlineStyle,
                                        value: NSUnderlineStyle.single.rawValue,
                                        range: rangeToUnderline)
            attributedText.addAttribute(NSAttributedString.Key.foregroundColor,
                                        value: textColor ?? .mainTextColor,
                                        range: rangeToUnderline)
            
        }
        self.attributedText = attributedText
    }
    
    // need to call after set text
    func setLineHeight(_ height: CGFloat) {
        let paragraphStyle = NSMutableParagraphStyle()
        paragraphStyle.lineSpacing = height
        paragraphStyle.alignment = textAlignment

        let attributedString = NSAttributedString(string: text ?? "", attributes: [NSAttributedString.Key.paragraphStyle: paragraphStyle])
        attributedText = attributedString
    }
    
    func highlightWords(words: [String] = []) {
        guard let textString = text else {
            return
        }
        let attributedText = NSMutableAttributedString(string: text ?? "")
        
        var highlightWords = words
        if highlightWords.isEmpty {
            highlightWords = [textString]
        }
        
        for word in highlightWords {
            let rangeTohighLight = (textString as NSString).range(of: word)
            attributedText.addAttribute(NSAttributedString.Key.font,
                                        value: UIFont.mediumGilroyFont(font.pointSize + 8),
                                        range: rangeTohighLight)
            attributedText.addAttribute(NSAttributedString.Key.foregroundColor,
                                        value: textColor ?? .mainTextColor,
                                        range: rangeTohighLight)
            
        }
        self.attributedText = attributedText
    }
    
    func boldWords(words: [String] = [], point: CGFloat = 0) {
        guard let textString = text else {
            return
        }
        let attributedText = NSMutableAttributedString(string: text ?? "")
        
        var highlightWords = words
        if highlightWords.isEmpty {
            highlightWords = [textString]
        }
        
        for word in highlightWords {
            let rangeTohighLight = (textString as NSString).range(of: word)
            attributedText.addAttribute(NSAttributedString.Key.font,
                                        value: UIFont.boldGilroyFont(font.pointSize + point),
                                        range: rangeTohighLight)
            attributedText.addAttribute(NSAttributedString.Key.foregroundColor,
                                        value: textColor ?? .mainTextColor,
                                        range: rangeTohighLight)
            
        }
        self.attributedText = attributedText
    }
}
