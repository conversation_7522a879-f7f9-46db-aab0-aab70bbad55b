//
//  CommonViewModel.swift
//  HormoneLife
//
//  Created by bm on 2024/9/7.
//

import UIKit
import HandyJSON

class CommonViewModel: BaseViewModel {

    var dataSource = [SystemDictionaryModel]()
    var tagsInfos = [String]()
    var homeTipModel : HomeTipModel?
    
    func getSystemDictionary(type: String, success: @escaping (_ result: [SystemDictionaryModel]) -> Void,failure: @escaping (_ error: String?) -> Void) {
        SystemInteractor.getSystemDictionary(type: type) { result in
            self.dataSource = result
            self.handleDicValue()
            success(result)
        } failure: { error in
            failure(error)
        }
    }
    
    func getSystemAllDictionary(success: @escaping (_ result: [SystemDictionaryModel]) -> Void,failure: @escaping (_ error: String?) -> Void) {
        SystemInteractor.getSystemAllDictionary() { result in
            self.dataSource = result
            self.handleDicValue()
            success(result)
        } failure: { error in
            failure(error)
        }
    }
    
    func getHomeTips(success: @escaping (_ result: HomeTipModel?) -> Void,failure: @escaping (_ error: String?) -> Void) {
        HomeInteractor.getTip { result in
            self.homeTipModel = result
            success(result)
        } failure: { error in
            failure(error)
        }
    }
    
    
    func handleDicValue() {
        for item in self.dataSource {
            self.tagsInfos.append("\(item.dictLabel ?? ""),\(item.dictValue ?? ""),0")
        }
    }
}

class SystemDictionaryModel : HandyJSON {
    var after : String?
    var before: String?
    var createTime: String?
    var dictId: String?
    var dictLabel : String?
    var dictName: String?
    var dictValue: String?
    var enableStatus: String?
    var id: String?
    var isSys: String?
    var remark: String?
    var run: String?
    var sort :Int = 0
    var isSelect = false
    
    required init() {
        
    }
}
