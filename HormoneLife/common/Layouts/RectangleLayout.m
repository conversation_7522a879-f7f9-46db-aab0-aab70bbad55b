

#import "RectangleLayout.h"

@interface RectangleLayout ()
{
    
}
@property(nonatomic,strong)NSMutableDictionary* headerSizesDic;
@property(nonatomic,strong)NSMutableDictionary* footerSizesDic;
@property(nonatomic,strong)NSMutableDictionary* layoutInfoDic;
@end


@implementation RectangleLayout
-(id)init
{
    self = [super init];
    if (self) {
        [self initData];
    }
    return self;
}

-(void)initData
{
    self.numbersOfCellInFirstRow = 2;
    self.maxCellWidth = (int)(CGRectGetWidth(self.collectionView.frame)/self.numbersOfCellInFirstRow);
    self.numbersOfCellInRow = 2;
  
    
}

-(void)initMaxWidth
{
    self.maxCellWidth = (int)(CGRectGetWidth(self.collectionView.frame)/self.numbersOfCellInFirstRow);

}
-(void)prepareLayout
{
    self.headerSizesDic = [NSMutableDictionary dictionary];
    self.footerSizesDic = [NSMutableDictionary dictionary];
    self.layoutInfoDic = [NSMutableDictionary dictionary];
    NSMutableDictionary *headerLayoutDictionary = [NSMutableDictionary dictionary];
    NSMutableDictionary *footerLayoutDictionary = [NSMutableDictionary dictionary];
    NSMutableDictionary *cellLayoutDictionary = [NSMutableDictionary dictionary];
    
    
    [self initMaxWidth];
    
   
    
    for (int section = 0; section<self.collectionView.numberOfSections; section++) {
        NSInteger itemsCount = [self.collectionView numberOfItemsInSection:section];
        for (int index = 0; index<itemsCount; index++) {
            NSIndexPath *indexPath = [NSIndexPath indexPathForItem:index inSection:section];
            if (index==0) {
                if (self.delegate&&[self.delegate respondsToSelector:@selector(collectionView:layout:headerSizeforSection:)]) {
                    
                    CGSize headSize = [self.delegate collectionView:self.collectionView layout:self headerSizeforSection:section];
                    self.headerSizesDic[[NSNumber numberWithInteger:section]] = [NSValue valueWithCGSize:headSize];
                    UICollectionViewLayoutAttributes *headerAttributes = [UICollectionViewLayoutAttributes
                                                                          layoutAttributesForSupplementaryViewOfKind:RectangleLayoutHeaderKind
                                                                          withIndexPath:indexPath];
                    headerAttributes.frame = CGRectMake(0, 0, headSize.width, headSize.height);
                    headerLayoutDictionary[indexPath] = headerAttributes;
                }
            }
            if (index==(itemsCount-1)) {
                if (self.delegate&&[self.delegate respondsToSelector:@selector(collectionView:layout:footerSizeforSection:)]) {
                    
                    CGSize footSize = [self.delegate collectionView:self.collectionView layout:self footerSizeforSection:section];
                    self.footerSizesDic[[NSNumber numberWithInteger:section]] = [NSValue valueWithCGSize:footSize];
                    UICollectionViewLayoutAttributes *footerAttributes = [UICollectionViewLayoutAttributes
                                                                          layoutAttributesForSupplementaryViewOfKind:RectangleLayoutFooterKind
                                                                          withIndexPath:indexPath];
                    
                    
                    CGRect lastFrame = [self frameForCellAtIndexPath:indexPath];
                    
                    footerAttributes.frame = CGRectMake(0, lastFrame.origin.y+lastFrame.size.height, footSize.width, footSize.height);
                    footerLayoutDictionary[indexPath] = footerAttributes;
                }
            }
            UICollectionViewLayoutAttributes * cellAttributes = [UICollectionViewLayoutAttributes layoutAttributesForCellWithIndexPath:indexPath];
            cellAttributes.frame = [self frameForCellAtIndexPath:indexPath];
            cellLayoutDictionary[indexPath] = cellAttributes;
            
        }
    }
    
    
    self.layoutInfoDic[RectangleLayoutHeaderKind] = headerLayoutDictionary;
    self.layoutInfoDic[RectangleLayoutFooterKind] = footerLayoutDictionary;
    self.layoutInfoDic[RectangleLayoutCellKind] = cellLayoutDictionary;
    
    
    
    
}
- (CGRect)frameForCellAtIndexPath:(NSIndexPath *)indexPath
{
    CGRect frame = CGRectZero;
    CGSize headSize = [[self.headerSizesDic objectForKey:[NSNumber numberWithInteger:[indexPath section]]] CGSizeValue];
    
    if ([indexPath row]<self.numbersOfCellInFirstRow) {
        frame = CGRectMake([indexPath row]*self.maxCellWidth, headSize.height, self.maxCellWidth, self.maxHeight);
        
    }else
    {
        
        float oringY = headSize.height + self.maxHeight + (([indexPath row]-self.numbersOfCellInFirstRow)/self.numbersOfCellInRow)*self.normalCellHeight;
        frame = CGRectMake(([indexPath row]-self.numbersOfCellInFirstRow)%self.numbersOfCellInRow*(int)(CGRectGetWidth(self.collectionView.frame)/self.numbersOfCellInRow), oringY, (int)(CGRectGetWidth(self.collectionView.frame)/self.numbersOfCellInRow), self.normalCellHeight);
    }
    
   
    return frame;
    
    
}
-(CGSize)collectionViewContentSize
{
    
    if([self.collectionView numberOfItemsInSection:0]==0){
        return CGSizeZero;
    }
    
    NSInteger allRows = 0;
    for (NSInteger section = 0; section<self.collectionView.numberOfSections; section++) {
        
        allRows = [self.collectionView numberOfItemsInSection:section] + allRows;
    }
    
    float height = self.maxHeight;
    
    if (allRows>self.numbersOfCellInFirstRow) {
        height = self.maxHeight+(((allRows-self.numbersOfCellInFirstRow)-1)/self.numbersOfCellInRow+1)*self.normalCellHeight;
    }
    
    
    
    CGSize size = CGSizeMake(self.collectionView.bounds.size.width, [self.headerSizesDic[[NSNumber numberWithInteger:0]] CGSizeValue].height+[self.footerSizesDic[[NSNumber numberWithInteger:0]] CGSizeValue].height+height);
    
    

    
    return size;
    
}
- (NSArray *)layoutAttributesForElementsInRect:(CGRect)rect
{
    NSMutableArray *allAttributes = [NSMutableArray arrayWithCapacity:self.layoutInfoDic.count];
    
    [self.layoutInfoDic enumerateKeysAndObjectsUsingBlock:^(NSString *elementIdentifier,
                                                            NSDictionary *elementsInfo,
                                                            BOOL *stop) {
        [elementsInfo enumerateKeysAndObjectsUsingBlock:^(NSIndexPath *indexPath,
                                                          UICollectionViewLayoutAttributes *attributes,
                                                          BOOL *innerStop) {
            if (CGRectIntersectsRect(rect, attributes.frame)) {
                [allAttributes addObject:attributes];
            }
        }];
    }];
    
    return allAttributes;
}
- (UICollectionViewLayoutAttributes *)layoutAttributesForItemAtIndexPath:(NSIndexPath *)indexPath
{
    return self.layoutInfoDic[RectangleLayoutCellKind][indexPath];
    
}

- (UICollectionViewLayoutAttributes *)layoutAttributesForSupplementaryViewOfKind:(NSString *)kind atIndexPath:(NSIndexPath *)indexPath
{
    return self.layoutInfoDic[kind][indexPath];
}

@end
