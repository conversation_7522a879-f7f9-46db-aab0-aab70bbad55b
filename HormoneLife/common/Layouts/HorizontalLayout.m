
#import "HorizontalLayout.h"

@interface HorizontalLayout ()
{

}
@property(nonatomic,strong)NSMutableDictionary* headerSizesDic;
@property(nonatomic,strong)NSMutableDictionary* footerSizesDic;
@property(nonatomic,strong)NSMutableDictionary* layoutInfoDic;
@end


@implementation HorizontalLayout

-(id)init
{
    self = [super init];
    if (self) {
        [self initData];
    }
    return self;
}

-(void)initData
{
    self.numbersOfCellInRow = 4;
}
-(void)prepareLayout
{
    self.headerSizesDic = [NSMutableDictionary dictionary];
    self.footerSizesDic = [NSMutableDictionary dictionary];
    self.layoutInfoDic = [NSMutableDictionary dictionary];
    NSMutableDictionary *headerLayoutDictionary = [NSMutableDictionary dictionary];
    NSMutableDictionary *footerLayoutDictionary = [NSMutableDictionary dictionary];
    NSMutableDictionary *cellLayoutDictionary = [NSMutableDictionary dictionary];
    
    if (self.cellWidth < 10) {
        self.cellWidth = CGRectGetWidth(self.collectionView.frame)/self.numbersOfCellInRow;
    }
    
    self.cellHeight = CGRectGetHeight(self.collectionView.frame);
    
    
    for (int section = 0; section<self.collectionView.numberOfSections; section++) {
        NSInteger itemsCount = [self.collectionView numberOfItemsInSection:section];
        for (int index = 0; index<itemsCount; index++) {
            NSIndexPath *indexPath = [NSIndexPath indexPathForItem:index inSection:section];
            if (index==0) {
                if (self.delegate&&[self.delegate respondsToSelector:@selector(collectionView:layout:headerSizeforSection:)]) {
                    
                    CGSize headSize = [self.delegate collectionView:self.collectionView layout:self headerSizeforSection:section];
                    self.headerSizesDic[[NSNumber numberWithInteger:section]] = [NSValue valueWithCGSize:headSize];
                    UICollectionViewLayoutAttributes *headerAttributes = [UICollectionViewLayoutAttributes
                                                                          layoutAttributesForSupplementaryViewOfKind:HorizontalLayoutHeaderKind
                                                                          withIndexPath:indexPath];
                    headerAttributes.frame = CGRectMake(0, 0, headSize.width, headSize.height);
                    headerLayoutDictionary[indexPath] = headerAttributes;
                }
            }
            if (index==(itemsCount-1)) {
                if (self.delegate&&[self.delegate respondsToSelector:@selector(collectionView:layout:footerSizeforSection:)]) {
                    
                    CGSize footSize = [self.delegate collectionView:self.collectionView layout:self footerSizeforSection:section];
                    self.footerSizesDic[[NSNumber numberWithInteger:section]] = [NSValue valueWithCGSize:footSize];
                    UICollectionViewLayoutAttributes *footerAttributes = [UICollectionViewLayoutAttributes
                                                                          layoutAttributesForSupplementaryViewOfKind:HorizontalLayoutFooterKind
                                                                          withIndexPath:indexPath];
                    
                    CGRect lastFrame = [self frameForCellAtIndexPath:indexPath];
                    footerAttributes.frame = CGRectMake(0, lastFrame.origin.y+lastFrame.size.height, footSize.width, footSize.height);
                    footerLayoutDictionary[indexPath] = footerAttributes;
                }
            }
            UICollectionViewLayoutAttributes * cellAttributes = [UICollectionViewLayoutAttributes layoutAttributesForCellWithIndexPath:indexPath];
            cellAttributes.frame = [self frameForCellAtIndexPath:indexPath];
            cellLayoutDictionary[indexPath] = cellAttributes;
            
        }
    }
    
    
    self.layoutInfoDic[HorizontalLayoutHeaderKind] = headerLayoutDictionary;
    self.layoutInfoDic[HorizontalLayoutFooterKind] = footerLayoutDictionary;
    self.layoutInfoDic[HorizontalLayoutCellKind] = cellLayoutDictionary;
    

    
}
- (CGRect)frameForCellAtIndexPath:(NSIndexPath *)indexPath
{
    CGRect frame = CGRectZero;
    
    CGSize headSize = [[self.headerSizesDic objectForKey:[NSNumber numberWithInteger:[indexPath section]]] CGSizeValue];
    
    frame = CGRectMake([indexPath row]*self.cellWidth, headSize.height, self.cellWidth, self.cellHeight);
    
    
    
    return frame;
    
    
}
-(CGSize)collectionViewContentSize
{
    
    if([self.collectionView numberOfItemsInSection:0]==0){
        return CGSizeZero;
    }
    NSInteger allRows = 0;
    for (NSInteger section = 0; section<self.collectionView.numberOfSections; section++) {
        allRows = [self.collectionView numberOfItemsInSection:section] + allRows;
    }
    CGSize  size = CGSizeMake(self.cellWidth*allRows, self.collectionView.bounds.size.height);
    
    
    
       
    return size;
    
}
- (NSArray *)layoutAttributesForElementsInRect:(CGRect)rect
{
    NSMutableArray *allAttributes = [NSMutableArray arrayWithCapacity:self.layoutInfoDic.count];
    
    [self.layoutInfoDic enumerateKeysAndObjectsUsingBlock:^(NSString *elementIdentifier,
                                                            NSDictionary *elementsInfo,
                                                            BOOL *stop) {
        [elementsInfo enumerateKeysAndObjectsUsingBlock:^(NSIndexPath *indexPath,
                                                          UICollectionViewLayoutAttributes *attributes,
                                                          BOOL *innerStop) {
            if (CGRectIntersectsRect(rect, attributes.frame)) {
                [allAttributes addObject:attributes];
            }
        }];
    }];
    
    return allAttributes;
}
- (UICollectionViewLayoutAttributes *)layoutAttributesForItemAtIndexPath:(NSIndexPath *)indexPath
{
    return self.layoutInfoDic[HorizontalLayoutCellKind][indexPath];
}

- (UICollectionViewLayoutAttributes *)layoutAttributesForSupplementaryViewOfKind:(NSString *)kind atIndexPath:(NSIndexPath *)indexPath
{
    return self.layoutInfoDic[kind][indexPath];
}
@end
