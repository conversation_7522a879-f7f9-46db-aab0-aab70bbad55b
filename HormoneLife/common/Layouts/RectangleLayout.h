
#import <UIKit/UIKit.h>


static  NSString *const RectangleLayoutHeaderKind = @"RectangleLayoutHeaderKind";
static  NSString *const RectangleLayoutFooterKind = @"RectangleLayoutFooterKind";
static  NSString *const RectangleLayoutCellKind   = @"RectangleLayoutCellKind";


@protocol RectangleLayoutDelegate <NSObject>

@optional
-(CGSize)collectionView:(UICollectionView*)collectionView layout:(UICollectionViewFlowLayout *)layout headerSizeforSection:(NSInteger)section;
-(CGSize)collectionView:(UICollectionView*)collectionView layout:(UICollectionViewFlowLayout *)layout footerSizeforSection:(NSInteger)section;

@end


@interface RectangleLayout : UICollectionViewFlowLayout
{

}
@property(nonatomic,strong)id<RectangleLayoutDelegate> delegate;
@property(nonatomic,assign)float maxCellWidth;
@property(nonatomic,assign)NSInteger numbersOfCellInFirstRow;
@property(nonatomic,assign)float maxHeight;
@property(nonatomic,assign)float normalCellHeight;
@property(nonatomic,assign)NSInteger numbersOfCellInRow;     
-(void)initMaxWidth;
@end
