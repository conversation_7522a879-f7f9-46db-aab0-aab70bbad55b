
#import <UIKit/UIKit.h>


static  NSString *const HorizontalLayoutHeaderKind = @"HorizontalLayoutHeaderKind";
static  NSString *const HorizontalLayoutFooterKind = @"HorizontalLayoutFooterKind";
static  NSString *const HorizontalLayoutCellKind   = @"HorizontalLayoutCellKind";


@protocol HorizontalLayoutDelegate <NSObject>

@optional
-(CGSize)collectionView:(UICollectionView*)collectionView layout:(UICollectionViewFlowLayout *)layout headerSizeforSection:(NSInteger)section;
-(CGSize)collectionView:(UICollectionView*)collectionView layout:(UICollectionViewFlowLayout *)layout footerSizeforSection:(NSInteger)section;

@end


@interface HorizontalLayout : UICollectionViewFlowLayout
{

}
@property(nonatomic,strong)id<HorizontalLayoutDelegate> delegate;
@property(nonatomic,assign)float cellWidth;              
@property(nonatomic,assign)float numbersOfCellInRow;
@property(nonatomic,assign)float cellHeight;
@end
