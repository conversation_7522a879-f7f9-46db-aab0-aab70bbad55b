

import UIKit

class BaseNavigationController: UINavigationController, UIGestureRecognizerDelegate {

    override func viewDidLoad() {
        super.viewDidLoad()

        self.navigationBar.isTranslucent = true
        self.interactivePopGestureRecognizer?.delegate = self
    }
    
    
    func gestureRecognizerShouldBegin(_ gestureRecognizer: UIGestureRecognizer) -> Bo<PERSON> {
        guard let topViewController = self.topViewController else {
            return false
        }
        
        if self.children.count <= 1 {
            return false
        }
        
        if let scrollView = topViewController.view.subviews.compactMap({ $0 as? UIScrollView }).first {
            let velocity = scrollView.panGestureRecognizer.velocity(in: scrollView)
            if scrollView.contentOffset.x <= 0 && velocity.x > 0 {
                return true
            }
        }
        return true
    }

    func gestureRecognizer(_ gestureRecognizer: UIGestureRecognizer, shouldRecognizeSimultaneouslyWith otherGestureRecognizer: UIGestureRecognizer) -> Bool {

        if let otherGestureView = otherGestureRecognizer.view, otherGestureView is UICollectionView {
            return true
        }
        return false
    }

    func gestureRecognizer(_ gestureRecognizer: UIGestureRecognizer, shouldRequireFailureOf otherGestureRecognizer: UIGestureRecognizer) -> Bool {
        if let otherGestureView = otherGestureRecognizer.view, otherGestureView is UICollectionView {

            if let collectionView = otherGestureView as? UICollectionView {
                if collectionView.contentOffset.x <= 0 {
                    return false
                }
            }
        }
        return true
    }

    override func pushViewController(_ viewController: UIViewController, animated: Bool) {
        if self.viewControllers.count > 0 {
            viewController.hidesBottomBarWhenPushed = true
        } else {
            viewController.hidesBottomBarWhenPushed = false
        }
        super.pushViewController(viewController, animated: animated)
    }
    
    override func setViewControllers(_ viewControllers: [UIViewController], animated: Bool) {
        if viewControllers.count >= 2 {
            let vc = viewControllers[1]
            vc.hidesBottomBarWhenPushed = true
        }
        super.setViewControllers(viewControllers, animated: animated)
    }

    override var preferredStatusBarStyle: UIStatusBarStyle {
        if #available(iOS 13.0, *) {
            return .darkContent
        } else {
            return .default
        }
    }
}
