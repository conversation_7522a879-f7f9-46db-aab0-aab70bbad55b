//
//  PickPhotoViewController.swift
//  MilkTime
//
//  Created by Tank on 2023/6/18.
//
// reference: https://www.cnblogs.com/Free-Thinker/p/7118024.html
// reference: https://www.jianshu.com/p/1f5896edf605

import UIKit
import Photos

protocol PickPhotoDelegate: AnyObject {
    func callBackImage(photo: UIImage)
    func callBackMediaURL(mediaURL: NSURL)
}

extension PickPhotoDelegate {
    func callBackImage(photo: UIImage) {}
    func callBackMediaURL(mediaURL: NSURL) {}
}

class PickPhotoViewController: BaseViewController, UINavigationControllerDelegate {
    
    let takingPicture =  UIImagePickerController()
    weak var pickPhotoDelegate: PickPhotoDelegate?
    lazy var uploadViewModel = UploadFileViewModel()
    
    @objc func pickPhoto() {
        checkPhotoAccess()
    }
    
    private func checkPhotoAccess() {
        
        let status = PHPhotoLibrary.authorizationStatus()
        switch status {
        case .authorized:
            self.showAlertSheet()
        case .denied, .restricted:
            //self.view.showHUD(withText: "denied, restricted")
            DispatchQueue.main.async(execute: {
                let alertController = UIAlertController(title: "Limited access to photos",
                                                        message: "Click \"Settings\" to allow access to your photos",
                                                        preferredStyle: .alert)
                
                let cancelAction = UIAlertAction(title:"Cancel", style: .cancel, handler:nil)
                
                let settingsAction = UIAlertAction(title:"Setting", style: .default, handler: { _ in
                    if let url = URL(string: UIApplication.openSettingsURLString),
                        UIApplication.shared.canOpenURL(url) {
                        UIApplication.shared.open(url)
                    }
                })
                
                alertController.addAction(cancelAction)
                alertController.addAction(settingsAction)
                
                self.present(alertController, animated: true, completion: nil)
            })
        case .notDetermined:
            PHPhotoLibrary.requestAuthorization({ (s) -> Void in
                DispatchQueue.main.async(execute: { () -> Void in
                    self.checkPhotoAccess()
                })
            })
        default:
            fatalError("Unexpected authorization status")
        }
    }
    
    func showAlertSheet() {
        let actionSheetController = UIAlertController()
        let cancelAction = UIAlertAction(title: "Cancel", style: UIAlertAction.Style.cancel) { _ in
            print("Tap cancel Button")
        }
        
        let takingPicturesAction = UIAlertAction(title: "Camera", style: UIAlertAction.Style.destructive) { _ in
            self.pickPhotosFromLibrary(type: .camera)
            self.takingPicture.showsCameraControls = true
        }
        
        let photoAlbumAction = UIAlertAction(title: "Gallery", style: UIAlertAction.Style.default) { _ in
            self.pickPhotosFromLibrary(type: .photoLibrary)
        }
        
        actionSheetController.addAction(cancelAction)
#if !(arch(i386) || arch(x86_64))
        actionSheetController.addAction(takingPicturesAction)
#endif
        actionSheetController.addAction(photoAlbumAction)
        present(actionSheetController, animated: true, completion: nil)
    }
    
    func pickPhotosFromLibrary(type: UIImagePickerController.SourceType) {
        takingPicture.sourceType = type
        takingPicture.allowsEditing = false
        takingPicture.delegate = self
        present(takingPicture, animated: true, completion: nil)
    }
}
//iOS 获取图片（拍照、相册、图库）
extension PickPhotoViewController: UIImagePickerControllerDelegate {
    func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
        picker.dismiss(animated: false) {
            DispatchQueue.main.async {
                guard let image = info[.originalImage] as? UIImage else { return }
                self.pickPhotoDelegate?.callBackImage(photo: image)
            }
        }
//        picker.dismiss(animated: true, completion: nil)

        
        /*
        if #available(iOS 11.0, *) {
            if takingPicture.sourceType == .photoLibrary {
                if let imageURL = info[UIImagePickerController.InfoKey.imageURL] as? NSURL {
                    print("imageURL =imageURL= \(imageURL)")
                    self.pickPhotoDelegate?.callBackMediaURL(mediaURL: imageURL as NSURL)
                }
            } else if takingPicture.sourceType == .camera {
                if let image = info[UIImagePickerController.InfoKey.originalImage] as? UIImage {
                    let homeDoc = NSHomeDirectory()
                    let imagePath = "\(homeDoc)/tmp/tempImage\(arc4random()).jpeg"
                    print(imagePath)
                    do {
                        try image.jpegData(compressionQuality: 0.6)?.write(to: URL(fileURLWithPath: imagePath))
                    } catch {
                        print(error)
                    }
                    self.pickPhotoDelegate?.callBackMediaURL(mediaURL: NSURL(fileURLWithPath: "/private\(imagePath)"))
                }
            }
        } else {
            if let imageURL = info[UIImagePickerController.InfoKey.mediaURL] as? NSURL {
                print("imageURL =mediaURL= \(imageURL)")
                self.pickPhotoDelegate?.callBackMediaURL(mediaURL: imageURL as NSURL)
            }
        }*/ //originalImage
        

        
        ///mediaURL
//        guard let mediaURL = info[UIImagePickerController.InfoKey.mediaURL] as?  NSURL else { return }
//        self.pickPhotoDelegate?.callBackMediaURL(mediaURL: imageURL)

    }
    
}
