//
//  LanguageManager.swift
//  MilkTime
//
//  Created by Match on 2023/8/14.
//

import Foundation

public let languageKey = "Language"

class LanguageManager {
    
    static let shared = LanguageManager()
    
    private(set) var currentLanguage: String
    
    private init() {
        self.currentLanguage = UserDefaults.standard.string(forKey: languageKey) ?? "en"
    }
    
    func setCurrentLanguage(_ language: String) {
        self.currentLanguage = language
        UserDefaults.standard.set(language, forKey: languageKey)
    }
    
    func localizedString(key: String) -> String {
        let path = Bundle.main.path(forResource: currentLanguage, ofType: "lproj")
        let bundle = Bundle(path: path!)
        return NSLocalizedString(key, tableName: nil, bundle: bundle!, value: "", comment: "")
    }
}
