//
//  SupportOperViewModel.swift
//  HormoneLife
//
//  Created by bm on 2024/9/7.
//

import UIKit

class SupportOperViewModel: BaseViewModel {

    var dataSource = [String]()
    
    func getSysOperation(success: @escaping (_ success: [String]) -> Void,failure: @escaping (_ error: String?) -> Void) {
        SystemInteractor.getSysOperationList(params: nil) { result in
            self.dataSource = result
            success(result)
        } failure: { error in
            failure(error)
        }
    }
    
}
