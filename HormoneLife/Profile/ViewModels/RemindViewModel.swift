//
//  RemindViewModel.swift
//  HormoneLife
//
//  Created by bm on 2024/10/9.
//  Copyright © 2024 HormoneLife. All rights reserved.
//

import UIKit

class RemindViewModel: BaseViewModel {

    var dataSource = [RemindMessage]()
    
    func getReminderPage(refresh: Bool, success: @escaping (_ success: [RemindMessage]) -> Void,failure: @escaping (_ error: String?) -> Void) {
        if refresh == true {
            begingIndex = 1
        } else {
            begingIndex += 1
        }
        var temParams = [String : String]()
        if let userId = Interface.shared().loggedInUser?.userInfo.id {
            temParams["userId"] = userId
        }
//        if let imageUrls = imageUrls {
//            temParams["imageUrls"] = imageUrls
//        }
//        if let markTime = markTime {
//            temParams["markTime"] = markTime
//        }
//        
        let params = ["page": self.begingIndex,
                      "pageSize": self.pageSize,
                      "param": temParams] as [String : Any]
        
        MessageInteractor.getRemindMessages(params: params) { result in
            
            if refresh == true {
                self.dataSource.removeAll()
            }
            if result.count < self.pageSize {
                self.hasMoreData = false
            } else {
                self.hasMoreData = true
            }
            if refresh == true {
                self.dataSource = result
            } else {
                self.dataSource.append(contentsOf: result)
            }
            success(result)
        } failure: { error in
            if self.begingIndex > 1 {
                self.begingIndex -= 1
            }
            failure(error)
        }
    }
}
