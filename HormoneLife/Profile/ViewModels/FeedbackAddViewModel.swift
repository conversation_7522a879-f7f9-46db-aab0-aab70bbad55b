//
//  FeedbackAddViewModel.swift
//  HormoneLife
//
//  Created by bm on 2024/9/7.
//

import UIKit


class FeedbackAddViewModel: BaseViewModel {
    
    var dataSource = [SystemDictionaryModel]()

//    type 反馈类型 0：其他 1：功能bug 2:意见/建议
    func addFeedback(contactInfo: String?, contactPhone: String?, eventStatusId: String?, eventStatusName: String?, feedbackCode: String = "feedback_type", feedbackContent: String?, feedbackImg: String?, feedbackStatus: String?, feedbackUserId: String?, feedbackUserName: String?, feedbackUserNo: String?, id: String?, type: String?,version: String?, success: @escaping (_ result: [String : Any]?) -> Void,failure: @escaping (_ error: String?) -> Void) {
        
        var params = [String : String]()
        
        params["feedbackCode"] = feedbackCode
        
        if let contactInfo = contactInfo {
            params["contactInfo"] = contactInfo
        }
        if let contactPhone = contactPhone {
            params["contactPhone"] = contactPhone
        }
        if let eventStatusId = eventStatusId {
            params["eventStatusId"] = eventStatusId
        }
        if let eventStatusName = eventStatusName {
            params["eventStatusName"] = eventStatusName
        }
        if let feedbackContent = feedbackContent {
            params["feedbackContent"] = feedbackContent
        }
        if let feedbackImg = feedbackImg {
            params["feedbackImg"] = feedbackImg
        }
        if let feedbackStatus = feedbackStatus {
            params["feedbackStatus"] = feedbackStatus
        }
        if let feedbackUserId = feedbackUserId {
            params["feedbackUserId"] = feedbackUserId
        }
        if let feedbackUserName = feedbackUserName {
            params["feedbackUserName"] = feedbackUserName
        }
        if let feedbackUserNo = feedbackUserNo {
            params["feedbackUserNo"] = feedbackUserNo
        }
        if let version = version {
            params["version"] = version
        }
        if let id = id {
            params["id"] = id
        }
        if let type = type {
            params["type"] = type
        }
        SystemInteractor.saveFeedbackInfo(params: params) { result in
            
            success(result)
        } failure: { error in
            failure(error)
        }
    }

}
