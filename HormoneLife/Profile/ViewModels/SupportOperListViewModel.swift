//
//  SupportOperListViewModel.swift
//  HormoneLife
//
//  Created by bm on 2024/9/7.
//

import UIKit
import HandyJSON

class SupportOperListViewModel: BaseViewModel {
    
    var dataSource = [SystemOperListModel]()
    
    func getSysOperation(category: String, title: String?, success: @escaping (_ success: [SystemOperListModel]) -> Void,failure: @escaping (_ error: String?) -> Void) {
        var params = ["category" : category]
        if let title = title {
            params["title"] = title
        }
        SystemInteractor.getSysOperList(params: params) { result in
            self.dataSource = result
            success(result)
        } failure: { error in
            failure(error)
        }
    }
}

struct SystemOperListModel : HandyJSON {
    
    var id: String?
    var accessory: String?
    var category: String?
    var clicks = 0
    var content: String?
    var createBy : String?
    var endTime: String?
    var platform: String?
    
    var startTime: String?
    var status = 0
    var tipsModel : String?
    var title: String?
    //var size = 0
    var fileName: String?
}

class SupportOperInfoViewModel: BaseViewModel {
    
    var dataSource: SystemOperInfo?
    
    func getSysOperationInfo(id: String, success: @escaping (_ success: SystemOperInfo?) -> Void,failure: @escaping (_ error: String?) -> Void) {
        SystemInteractor.getSysOperationInfo(id: id) { result in
            self.dataSource = result
            success(result)
        } failure: { error in
            failure(error)
        }
    }
}

struct SystemOperInfo: HandyJSON {
    var id, createBy, createTime, updateBy: String?
    var updateTime: String?
    var delFlag: Int = 0
    var title, category, content: String?
    var accessory: String?
    var platform, tipsModel, clicks: Int?
    var sysCategoryList: [SysCategoryList]?
    var fileName: String?
    var status: Int = 0
    var size, fileType: String?
}

// MARK: - SysCategoryList
struct SysCategoryList: HandyJSON {
    var id, createBy, createTime: String?
    var updateBy: String?
    var updateTime: String?
    var delFlag: Int = 0
    var name: String?
    var sort: Int = 0
}
