//
//  MessageViewModel.swift
//  HormoneLife
//
//  Created by bm on 2024/9/18.
//

import UIKit
import HandyJSON

class MessageViewModel: BaseViewModel {
    
    var countModel = MeesageUnreadCountModel()
    
    func getUnreadCount(success: @escaping (_ success: MeesageUnreadCountModel) -> Void,failure: @escaping (_ error: String?) -> Void) {
        MessageInteractor.unReadCount(params: nil) { result in
            self.countModel = result
            success(result)
        } failure: { error in
            
        }
    }
    
    func allRead(success: @escaping (_ success: [String: Any]) -> Void,failure: @escaping (_ error: String?) -> Void) {
        MessageInteractor.allRead(params: nil) { result in
            success(result)
        } failure: { error in
            
        }

    }
}

struct MeesageUnreadCountModel: HandyJSON {
    var messageCount: Int = 0
    var reminderCount: Int = 0
}
