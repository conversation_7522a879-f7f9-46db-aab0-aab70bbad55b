<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="21701" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21679"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="gilroy regular.otf">
            <string>Gilroy-Regular</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="PersonalInfoViewController">
            <connections>
                <outlet property="avatarImg" destination="f7O-fD-8Nb" id="DWy-5X-bwl"/>
                <outlet property="birthdayField" destination="Ykd-6U-TTP" id="Omg-SO-jnE"/>
                <outlet property="firstAverage" destination="Gwp-A7-msB" id="8as-l9-19Q"/>
                <outlet property="firstAverageDaysLabel" destination="eNW-gj-cqp" id="DkD-BR-tgC"/>
                <outlet property="firstNameField" destination="Mye-jl-iUT" id="6Cs-nE-FGF"/>
                <outlet property="lastNameField" destination="BVb-ul-Xl2" id="aS2-yA-apm"/>
                <outlet property="saveButton" destination="zwg-25-pZa" id="IM8-2g-QrA"/>
                <outlet property="secondAverage" destination="XQ5-vG-ZaJ" id="2jg-uR-PTx"/>
                <outlet property="secondAverageDaysLabel" destination="BuE-In-ITZ" id="iQD-dr-NoT"/>
                <outlet property="tagsField" destination="VoC-ep-qxt" id="Psd-if-LAo"/>
                <outlet property="trackingOvulation" destination="tCd-pg-OSa" id="0PU-37-kdg"/>
                <outlet property="view" destination="i5M-Pr-FkT" id="sfx-zR-JGt"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="i5M-Pr-FkT">
            <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="pgh-30-Wdu">
                    <rect key="frame" x="0.0" y="59" width="393" height="793"/>
                    <subviews>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="k2o-7T-wGT">
                            <rect key="frame" x="0.0" y="0.0" width="393" height="804"/>
                            <subviews>
                                <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="IOC-Cr-9AQ">
                                    <rect key="frame" x="20" y="20" width="353" height="704"/>
                                    <subviews>
                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="wue-22-f5s">
                                            <rect key="frame" x="0.0" y="0.0" width="353" height="404"/>
                                            <subviews>
                                                <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="20" translatesAutoresizingMaskIntoConstraints="NO" id="VOG-Le-aXw">
                                                    <rect key="frame" x="20" y="40" width="313" height="334"/>
                                                    <subviews>
                                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="vmT-c9-jVI">
                                                            <rect key="frame" x="0.0" y="0.0" width="313" height="64"/>
                                                            <subviews>
                                                                <imageView clipsSubviews="YES" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="peopleCircle" translatesAutoresizingMaskIntoConstraints="NO" id="f7O-fD-8Nb">
                                                                    <rect key="frame" x="124.66666666666666" y="0.0" width="64" height="64"/>
                                                                    <constraints>
                                                                        <constraint firstAttribute="height" constant="64" id="Cbb-F1-bLq"/>
                                                                        <constraint firstAttribute="width" constant="64" id="V1c-RT-ujQ"/>
                                                                    </constraints>
                                                                    <userDefinedRuntimeAttributes>
                                                                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                                            <integer key="value" value="32"/>
                                                                        </userDefinedRuntimeAttribute>
                                                                    </userDefinedRuntimeAttributes>
                                                                </imageView>
                                                            </subviews>
                                                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                            <constraints>
                                                                <constraint firstAttribute="height" constant="64" id="4km-Kd-Y5f"/>
                                                                <constraint firstItem="f7O-fD-8Nb" firstAttribute="centerY" secondItem="vmT-c9-jVI" secondAttribute="centerY" id="5cT-vs-lz6"/>
                                                                <constraint firstItem="f7O-fD-8Nb" firstAttribute="centerX" secondItem="vmT-c9-jVI" secondAttribute="centerX" id="qnq-7G-ftj"/>
                                                            </constraints>
                                                        </view>
                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Profile Photo" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="0Kg-Bi-faq">
                                                            <rect key="frame" x="0.0" y="84" width="313" height="26"/>
                                                            <fontDescription key="fontDescription" name="HelveticaNeue" family="Helvetica Neue" pointSize="14"/>
                                                            <color key="textColor" red="0.18858519200000001" green="0.057947516439999999" blue="0.3526558876" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                            <nil key="highlightedColor"/>
                                                        </label>
                                                        <textField opaque="NO" tag="50" contentMode="scaleToFill" horizontalHuggingPriority="248" contentHorizontalAlignment="left" contentVerticalAlignment="center" borderStyle="roundedRect" placeholder="First Name" textAlignment="center" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="Mye-jl-iUT">
                                                            <rect key="frame" x="0.0" y="130" width="313" height="36"/>
                                                            <color key="backgroundColor" red="0.95682305099999998" green="0.96063107250000002" blue="0.96861356499999995" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                            <constraints>
                                                                <constraint firstAttribute="height" constant="36" id="Ud5-dR-a0K"/>
                                                            </constraints>
                                                            <color key="textColor" red="0.18858519200000001" green="0.057947516439999999" blue="0.3526558876" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                            <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="14"/>
                                                            <textInputTraits key="textInputTraits"/>
                                                        </textField>
                                                        <textField opaque="NO" tag="51" contentMode="scaleToFill" horizontalHuggingPriority="248" contentHorizontalAlignment="left" contentVerticalAlignment="center" borderStyle="roundedRect" placeholder="Last Name" textAlignment="center" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="BVb-ul-Xl2">
                                                            <rect key="frame" x="0.0" y="186" width="313" height="36"/>
                                                            <color key="backgroundColor" red="0.95682305099999998" green="0.96063107250000002" blue="0.96861356499999995" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                            <constraints>
                                                                <constraint firstAttribute="height" constant="36" id="8du-pz-dsG"/>
                                                            </constraints>
                                                            <color key="textColor" red="0.18858519200000001" green="0.057947516439999999" blue="0.3526558876" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                            <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="14"/>
                                                            <textInputTraits key="textInputTraits"/>
                                                        </textField>
                                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="MCJ-68-TFc">
                                                            <rect key="frame" x="0.0" y="242" width="313" height="36"/>
                                                            <subviews>
                                                                <textField opaque="NO" tag="52" contentMode="scaleToFill" horizontalHuggingPriority="248" contentHorizontalAlignment="left" contentVerticalAlignment="center" borderStyle="roundedRect" placeholder="Birthday" textAlignment="center" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="Ykd-6U-TTP">
                                                                    <rect key="frame" x="0.0" y="0.0" width="313" height="36"/>
                                                                    <color key="backgroundColor" red="0.95682305099999998" green="0.96063107250000002" blue="0.96861356499999995" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                    <constraints>
                                                                        <constraint firstAttribute="height" constant="36" id="e4w-9U-rmt"/>
                                                                    </constraints>
                                                                    <color key="textColor" red="0.18858519200000001" green="0.057947516439999999" blue="0.3526558876" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                    <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="14"/>
                                                                    <textInputTraits key="textInputTraits"/>
                                                                </textField>
                                                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="HuV-Sg-C11">
                                                                    <rect key="frame" x="0.0" y="0.0" width="313" height="36"/>
                                                                    <state key="normal" title="Button"/>
                                                                    <buttonConfiguration key="configuration" style="plain" title=" "/>
                                                                    <connections>
                                                                        <action selector="didTapBirthdayField:" destination="-1" eventType="touchUpInside" id="QJG-GA-6ca"/>
                                                                    </connections>
                                                                </button>
                                                            </subviews>
                                                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                            <constraints>
                                                                <constraint firstItem="HuV-Sg-C11" firstAttribute="leading" secondItem="MCJ-68-TFc" secondAttribute="leading" id="7To-EM-QcF"/>
                                                                <constraint firstAttribute="trailing" secondItem="HuV-Sg-C11" secondAttribute="trailing" id="F1D-mo-l4i"/>
                                                                <constraint firstItem="Ykd-6U-TTP" firstAttribute="top" secondItem="MCJ-68-TFc" secondAttribute="top" id="iug-9W-zKw"/>
                                                                <constraint firstAttribute="bottom" secondItem="Ykd-6U-TTP" secondAttribute="bottom" id="nE0-r9-pEY"/>
                                                                <constraint firstAttribute="trailing" secondItem="Ykd-6U-TTP" secondAttribute="trailing" id="nTa-uA-SWk"/>
                                                                <constraint firstItem="Ykd-6U-TTP" firstAttribute="leading" secondItem="MCJ-68-TFc" secondAttribute="leading" id="rDM-rR-JfZ"/>
                                                                <constraint firstItem="HuV-Sg-C11" firstAttribute="top" secondItem="MCJ-68-TFc" secondAttribute="top" id="rcr-86-huB"/>
                                                                <constraint firstAttribute="height" constant="36" id="scN-3K-1Z5"/>
                                                                <constraint firstAttribute="bottom" secondItem="HuV-Sg-C11" secondAttribute="bottom" id="v1d-U3-cUg"/>
                                                            </constraints>
                                                        </view>
                                                        <textField opaque="NO" tag="53" contentMode="scaleToFill" horizontalHuggingPriority="248" contentHorizontalAlignment="left" contentVerticalAlignment="center" borderStyle="roundedRect" placeholder="Tags" textAlignment="center" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="VoC-ep-qxt">
                                                            <rect key="frame" x="0.0" y="298" width="313" height="36"/>
                                                            <color key="backgroundColor" red="0.95682305099999998" green="0.96063107250000002" blue="0.96861356499999995" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                            <constraints>
                                                                <constraint firstAttribute="height" constant="36" id="noa-1o-mhb"/>
                                                            </constraints>
                                                            <color key="textColor" red="0.18858519200000001" green="0.057947516439999999" blue="0.3526558876" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                            <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="14"/>
                                                            <textInputTraits key="textInputTraits"/>
                                                        </textField>
                                                    </subviews>
                                                </stackView>
                                            </subviews>
                                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <constraints>
                                                <constraint firstAttribute="height" constant="404" id="8hC-Yb-ELT"/>
                                                <constraint firstItem="VOG-Le-aXw" firstAttribute="top" secondItem="wue-22-f5s" secondAttribute="top" constant="40" id="Gu7-zf-bd8"/>
                                                <constraint firstItem="VOG-Le-aXw" firstAttribute="leading" secondItem="wue-22-f5s" secondAttribute="leading" constant="20" id="Phj-bg-Buk"/>
                                                <constraint firstAttribute="bottom" secondItem="VOG-Le-aXw" secondAttribute="bottom" constant="30" id="gk9-dQ-x0F"/>
                                                <constraint firstAttribute="trailing" secondItem="VOG-Le-aXw" secondAttribute="trailing" constant="20" id="xr4-3m-N8A"/>
                                            </constraints>
                                            <userDefinedRuntimeAttributes>
                                                <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                    <integer key="value" value="4"/>
                                                </userDefinedRuntimeAttribute>
                                            </userDefinedRuntimeAttributes>
                                        </view>
                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="xf8-Am-iP3">
                                            <rect key="frame" x="0.0" y="416" width="353" height="288"/>
                                            <subviews>
                                                <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="DY5-gf-tV3">
                                                    <rect key="frame" x="20" y="20" width="313" height="248"/>
                                                    <subviews>
                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Your average period length" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="wzD-mT-HGy">
                                                            <rect key="frame" x="0.0" y="0.0" width="313" height="20"/>
                                                            <constraints>
                                                                <constraint firstAttribute="height" constant="20" id="v4h-OS-au0"/>
                                                            </constraints>
                                                            <fontDescription key="fontDescription" name="HelveticaNeue" family="Helvetica Neue" pointSize="14"/>
                                                            <color key="textColor" red="0.18858519200000001" green="0.057947516439999999" blue="0.3526558876" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                            <nil key="highlightedColor"/>
                                                        </label>
                                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="jVI-sq-lpD">
                                                            <rect key="frame" x="0.0" y="36" width="313" height="36"/>
                                                            <subviews>
                                                                <textField opaque="NO" tag="54" contentMode="scaleToFill" horizontalHuggingPriority="248" contentHorizontalAlignment="left" contentVerticalAlignment="center" borderStyle="roundedRect" placeholder="Please select" textAlignment="center" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="Gwp-A7-msB">
                                                                    <rect key="frame" x="0.0" y="0.0" width="313" height="36"/>
                                                                    <color key="backgroundColor" red="0.95682305099999998" green="0.96063107250000002" blue="0.96861356499999995" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                    <constraints>
                                                                        <constraint firstAttribute="height" constant="36" id="Q2j-bV-tug"/>
                                                                    </constraints>
                                                                    <color key="textColor" red="0.1925059855" green="0.057953603569999998" blue="0.3526558876" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                    <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="14"/>
                                                                    <textInputTraits key="textInputTraits"/>
                                                                </textField>
                                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Days" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="eNW-gj-cqp">
                                                                    <rect key="frame" x="209.33333333333334" y="9.9999999999999982" width="31.666666666666657" height="16.333333333333329"/>
                                                                    <fontDescription key="fontDescription" name="HelveticaNeue" family="Helvetica Neue" pointSize="14"/>
                                                                    <color key="textColor" red="0.20423525570000001" green="0.075626514850000007" blue="0.36441689729999999" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                    <nil key="highlightedColor"/>
                                                                </label>
                                                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="fwv-K2-Xti">
                                                                    <rect key="frame" x="281" y="0.0" width="16" height="36"/>
                                                                    <constraints>
                                                                        <constraint firstAttribute="width" constant="16" id="4Gv-Lr-ggg"/>
                                                                    </constraints>
                                                                    <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                                    <state key="normal" image="forwarIcon"/>
                                                                    <connections>
                                                                        <action selector="didTapFirstAvarageIcon:" destination="-1" eventType="touchUpInside" id="M8s-Fb-oP0"/>
                                                                    </connections>
                                                                </button>
                                                            </subviews>
                                                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                            <constraints>
                                                                <constraint firstItem="Gwp-A7-msB" firstAttribute="top" secondItem="jVI-sq-lpD" secondAttribute="top" id="Cka-nS-YsY"/>
                                                                <constraint firstAttribute="bottom" secondItem="Gwp-A7-msB" secondAttribute="bottom" id="KPp-Y1-COa"/>
                                                                <constraint firstItem="eNW-gj-cqp" firstAttribute="centerY" secondItem="jVI-sq-lpD" secondAttribute="centerY" id="PIu-I9-dp4"/>
                                                                <constraint firstAttribute="trailing" secondItem="fwv-K2-Xti" secondAttribute="trailing" constant="16" id="QkQ-PB-naT"/>
                                                                <constraint firstItem="Gwp-A7-msB" firstAttribute="leading" secondItem="jVI-sq-lpD" secondAttribute="leading" id="U8O-aS-LXR"/>
                                                                <constraint firstItem="fwv-K2-Xti" firstAttribute="leading" secondItem="eNW-gj-cqp" secondAttribute="trailing" constant="40" id="UZZ-78-869"/>
                                                                <constraint firstAttribute="trailing" secondItem="Gwp-A7-msB" secondAttribute="trailing" id="dBE-CU-Pfh"/>
                                                                <constraint firstAttribute="bottom" secondItem="fwv-K2-Xti" secondAttribute="bottom" id="paL-5l-09n"/>
                                                                <constraint firstItem="fwv-K2-Xti" firstAttribute="top" secondItem="jVI-sq-lpD" secondAttribute="top" id="rRF-ay-G1M"/>
                                                                <constraint firstAttribute="height" constant="36" id="tOb-hx-BM7"/>
                                                            </constraints>
                                                        </view>
                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Your average cycle length" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="WM7-fW-w30">
                                                            <rect key="frame" x="0.0" y="88" width="313" height="20"/>
                                                            <constraints>
                                                                <constraint firstAttribute="height" constant="20" id="dUz-1E-1k9"/>
                                                            </constraints>
                                                            <fontDescription key="fontDescription" name="HelveticaNeue" family="Helvetica Neue" pointSize="14"/>
                                                            <color key="textColor" red="0.18858519200000001" green="0.057947516439999999" blue="0.3526558876" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                            <nil key="highlightedColor"/>
                                                        </label>
                                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="gWP-i2-Wm5">
                                                            <rect key="frame" x="0.0" y="124" width="313" height="36"/>
                                                            <subviews>
                                                                <textField opaque="NO" tag="55" contentMode="scaleToFill" horizontalHuggingPriority="248" contentHorizontalAlignment="left" contentVerticalAlignment="center" borderStyle="roundedRect" placeholder="Please select" textAlignment="center" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="XQ5-vG-ZaJ">
                                                                    <rect key="frame" x="0.0" y="0.0" width="313" height="36"/>
                                                                    <color key="backgroundColor" red="0.95682305099999998" green="0.96063107250000002" blue="0.96861356499999995" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                    <constraints>
                                                                        <constraint firstAttribute="height" constant="36" id="gLv-1l-Rmw"/>
                                                                    </constraints>
                                                                    <color key="textColor" red="0.1925059855" green="0.057953603569999998" blue="0.3526558876" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                    <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="14"/>
                                                                    <textInputTraits key="textInputTraits"/>
                                                                </textField>
                                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Days" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="BuE-In-ITZ">
                                                                    <rect key="frame" x="209" y="9.6666666666666288" width="32" height="17"/>
                                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                    <color key="textColor" red="0.20423525570000001" green="0.075626514850000007" blue="0.36441689729999999" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                    <nil key="highlightedColor"/>
                                                                </label>
                                                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="qzp-6m-XLZ">
                                                                    <rect key="frame" x="281" y="0.0" width="16" height="36"/>
                                                                    <constraints>
                                                                        <constraint firstAttribute="width" constant="16" id="yZB-gN-Iho"/>
                                                                    </constraints>
                                                                    <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                                    <state key="normal" image="forwarIcon"/>
                                                                    <connections>
                                                                        <action selector="didTapSecondAvarageIcon:" destination="-1" eventType="touchUpInside" id="Hiy-Sh-PY5"/>
                                                                    </connections>
                                                                </button>
                                                            </subviews>
                                                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                            <constraints>
                                                                <constraint firstItem="XQ5-vG-ZaJ" firstAttribute="top" secondItem="gWP-i2-Wm5" secondAttribute="top" id="1Lw-8f-0if"/>
                                                                <constraint firstAttribute="bottom" secondItem="XQ5-vG-ZaJ" secondAttribute="bottom" id="3MH-2D-8JX"/>
                                                                <constraint firstAttribute="bottom" secondItem="qzp-6m-XLZ" secondAttribute="bottom" id="BGc-S2-Xt0"/>
                                                                <constraint firstAttribute="trailing" secondItem="qzp-6m-XLZ" secondAttribute="trailing" constant="16" id="BLZ-ox-JVq"/>
                                                                <constraint firstItem="BuE-In-ITZ" firstAttribute="centerY" secondItem="gWP-i2-Wm5" secondAttribute="centerY" id="JVB-MB-hrh"/>
                                                                <constraint firstItem="qzp-6m-XLZ" firstAttribute="leading" secondItem="BuE-In-ITZ" secondAttribute="trailing" constant="40" id="Mlj-YN-9Xo"/>
                                                                <constraint firstAttribute="height" constant="36" id="Xel-Hs-fsQ"/>
                                                                <constraint firstAttribute="trailing" secondItem="XQ5-vG-ZaJ" secondAttribute="trailing" id="Zyh-YO-OEc"/>
                                                                <constraint firstItem="qzp-6m-XLZ" firstAttribute="top" secondItem="gWP-i2-Wm5" secondAttribute="top" id="cPk-Zb-MMS"/>
                                                                <constraint firstItem="XQ5-vG-ZaJ" firstAttribute="leading" secondItem="gWP-i2-Wm5" secondAttribute="leading" id="nyy-Ig-NDG"/>
                                                            </constraints>
                                                        </view>
                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Method of tracking ovulation" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="WIa-T7-1P2">
                                                            <rect key="frame" x="0.0" y="176" width="313" height="20"/>
                                                            <constraints>
                                                                <constraint firstAttribute="height" constant="20" id="klq-Ma-kac"/>
                                                            </constraints>
                                                            <fontDescription key="fontDescription" name="HelveticaNeue" family="Helvetica Neue" pointSize="14"/>
                                                            <color key="textColor" red="0.18858519200000001" green="0.057947516439999999" blue="0.3526558876" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                            <nil key="highlightedColor"/>
                                                        </label>
                                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="jgH-Ac-7G8">
                                                            <rect key="frame" x="0.0" y="212" width="313" height="36"/>
                                                            <subviews>
                                                                <textField opaque="NO" tag="56" contentMode="scaleToFill" horizontalHuggingPriority="248" contentHorizontalAlignment="left" contentVerticalAlignment="center" borderStyle="roundedRect" placeholder="Other" textAlignment="center" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="tCd-pg-OSa">
                                                                    <rect key="frame" x="0.0" y="0.0" width="313" height="36"/>
                                                                    <color key="backgroundColor" red="0.95682305099999998" green="0.96063107250000002" blue="0.96861356499999995" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                    <constraints>
                                                                        <constraint firstAttribute="height" constant="36" id="Lxw-48-VEh"/>
                                                                    </constraints>
                                                                    <color key="textColor" red="0.1925059855" green="0.057953603569999998" blue="0.3526558876" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                    <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="14"/>
                                                                    <textInputTraits key="textInputTraits"/>
                                                                </textField>
                                                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="dyv-C0-bjd">
                                                                    <rect key="frame" x="281" y="0.0" width="16" height="36"/>
                                                                    <constraints>
                                                                        <constraint firstAttribute="width" constant="16" id="qRy-h9-dSs"/>
                                                                    </constraints>
                                                                    <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                                    <state key="normal" image="forwarIcon"/>
                                                                    <connections>
                                                                        <action selector="didTapOvulationIcon:" destination="-1" eventType="touchUpInside" id="4nj-dQ-R7i"/>
                                                                    </connections>
                                                                </button>
                                                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="cSI-zD-Pwo">
                                                                    <rect key="frame" x="0.0" y="0.0" width="281" height="36"/>
                                                                    <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                                    <state key="normal" title="  "/>
                                                                    <connections>
                                                                        <action selector="didTapOvulationIcon:" destination="-1" eventType="touchUpInside" id="GaB-MS-8Bm"/>
                                                                    </connections>
                                                                </button>
                                                            </subviews>
                                                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                            <constraints>
                                                                <constraint firstAttribute="bottom" secondItem="tCd-pg-OSa" secondAttribute="bottom" id="2RZ-4Z-ekf"/>
                                                                <constraint firstItem="cSI-zD-Pwo" firstAttribute="leading" secondItem="jgH-Ac-7G8" secondAttribute="leading" id="9Em-bv-M9Q"/>
                                                                <constraint firstItem="tCd-pg-OSa" firstAttribute="top" secondItem="jgH-Ac-7G8" secondAttribute="top" id="AG0-1J-Gs0"/>
                                                                <constraint firstItem="dyv-C0-bjd" firstAttribute="top" secondItem="jgH-Ac-7G8" secondAttribute="top" id="GSl-wW-9Ny"/>
                                                                <constraint firstAttribute="height" constant="36" id="HZj-T2-xB9"/>
                                                                <constraint firstAttribute="trailing" secondItem="dyv-C0-bjd" secondAttribute="trailing" constant="16" id="HgJ-sE-LCV"/>
                                                                <constraint firstItem="cSI-zD-Pwo" firstAttribute="top" secondItem="jgH-Ac-7G8" secondAttribute="top" id="SKY-VK-OuC"/>
                                                                <constraint firstAttribute="bottom" secondItem="dyv-C0-bjd" secondAttribute="bottom" id="ar9-Ws-ShE"/>
                                                                <constraint firstAttribute="trailing" secondItem="tCd-pg-OSa" secondAttribute="trailing" id="d5o-UK-tdV"/>
                                                                <constraint firstItem="dyv-C0-bjd" firstAttribute="leading" secondItem="cSI-zD-Pwo" secondAttribute="trailing" id="lPZ-4T-Snw"/>
                                                                <constraint firstAttribute="bottom" secondItem="cSI-zD-Pwo" secondAttribute="bottom" id="nSa-i2-M4b"/>
                                                                <constraint firstItem="tCd-pg-OSa" firstAttribute="leading" secondItem="jgH-Ac-7G8" secondAttribute="leading" id="ryj-z8-fBz"/>
                                                            </constraints>
                                                        </view>
                                                    </subviews>
                                                </stackView>
                                            </subviews>
                                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <constraints>
                                                <constraint firstAttribute="trailing" secondItem="DY5-gf-tV3" secondAttribute="trailing" constant="20" id="4ad-29-oOA"/>
                                                <constraint firstAttribute="bottom" secondItem="DY5-gf-tV3" secondAttribute="bottom" constant="20" id="9bY-H7-Uzh"/>
                                                <constraint firstItem="DY5-gf-tV3" firstAttribute="top" secondItem="xf8-Am-iP3" secondAttribute="top" constant="20" id="SYe-ef-X0q"/>
                                                <constraint firstAttribute="height" constant="288" id="TV4-jE-ZaQ"/>
                                                <constraint firstItem="DY5-gf-tV3" firstAttribute="leading" secondItem="xf8-Am-iP3" secondAttribute="leading" constant="20" id="mte-1K-J4b"/>
                                            </constraints>
                                            <userDefinedRuntimeAttributes>
                                                <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                    <integer key="value" value="4"/>
                                                </userDefinedRuntimeAttribute>
                                            </userDefinedRuntimeAttributes>
                                        </view>
                                    </subviews>
                                    <constraints>
                                        <constraint firstAttribute="bottom" secondItem="xf8-Am-iP3" secondAttribute="bottom" id="paK-6d-Ije"/>
                                    </constraints>
                                    <userDefinedRuntimeAttributes>
                                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                            <integer key="value" value="4"/>
                                        </userDefinedRuntimeAttribute>
                                    </userDefinedRuntimeAttributes>
                                </stackView>
                            </subviews>
                            <color key="backgroundColor" red="0.94169229269999999" green="0.92880314590000002" blue="0.9607291818" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                            <constraints>
                                <constraint firstAttribute="trailing" secondItem="IOC-Cr-9AQ" secondAttribute="trailing" constant="20" id="0Wg-Kn-KAD"/>
                                <constraint firstAttribute="bottom" secondItem="IOC-Cr-9AQ" secondAttribute="bottom" constant="80" id="2Ul-MQ-bE2"/>
                                <constraint firstItem="IOC-Cr-9AQ" firstAttribute="top" secondItem="k2o-7T-wGT" secondAttribute="top" constant="20" id="eCY-qU-yHr"/>
                                <constraint firstItem="IOC-Cr-9AQ" firstAttribute="leading" secondItem="k2o-7T-wGT" secondAttribute="leading" constant="20" id="oOU-jV-NF5"/>
                            </constraints>
                        </view>
                    </subviews>
                    <constraints>
                        <constraint firstItem="k2o-7T-wGT" firstAttribute="width" secondItem="pgh-30-Wdu" secondAttribute="width" id="QDd-ig-EKd"/>
                        <constraint firstItem="k2o-7T-wGT" firstAttribute="leading" secondItem="pgh-30-Wdu" secondAttribute="leading" id="S43-bb-uva"/>
                        <constraint firstAttribute="bottom" secondItem="k2o-7T-wGT" secondAttribute="bottom" id="V5G-p4-XuJ"/>
                        <constraint firstItem="k2o-7T-wGT" firstAttribute="centerY" secondItem="pgh-30-Wdu" secondAttribute="centerY" id="Y5h-12-bo7"/>
                        <constraint firstItem="k2o-7T-wGT" firstAttribute="top" secondItem="pgh-30-Wdu" secondAttribute="top" id="YHa-8c-ZEW"/>
                        <constraint firstAttribute="trailing" secondItem="k2o-7T-wGT" secondAttribute="trailing" id="dpO-Ww-bxC"/>
                    </constraints>
                    <viewLayoutGuide key="contentLayoutGuide" id="iXO-eY-rew"/>
                    <viewLayoutGuide key="frameLayoutGuide" id="gE5-Ra-tJH"/>
                </scrollView>
                <button hidden="YES" opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="zwg-25-pZa">
                    <rect key="frame" x="24" y="750" width="345" height="48"/>
                    <color key="backgroundColor" red="0.18858519200000001" green="0.057947516439999999" blue="0.3526558876" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="48" id="b9o-eK-7aE"/>
                    </constraints>
                    <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="16"/>
                    <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                    <state key="normal" title="Save"/>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                            <integer key="value" value="4"/>
                        </userDefinedRuntimeAttribute>
                    </userDefinedRuntimeAttributes>
                    <connections>
                        <action selector="didTapSaveAction:" destination="-1" eventType="touchUpInside" id="ieb-y4-ce3"/>
                    </connections>
                </button>
            </subviews>
            <viewLayoutGuide key="safeArea" id="fnl-2z-Ty3"/>
            <color key="backgroundColor" red="0.94169229269999999" green="0.92880314590000002" blue="0.9607291818" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
            <constraints>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="trailing" secondItem="zwg-25-pZa" secondAttribute="trailing" constant="24" id="NfD-lE-spq"/>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="bottom" secondItem="zwg-25-pZa" secondAttribute="bottom" constant="20" id="TPz-LS-5ZN"/>
                <constraint firstItem="pgh-30-Wdu" firstAttribute="top" secondItem="fnl-2z-Ty3" secondAttribute="top" id="p7N-bq-qMi"/>
                <constraint firstAttribute="bottom" secondItem="pgh-30-Wdu" secondAttribute="bottom" id="vdX-Se-PAU"/>
                <constraint firstItem="pgh-30-Wdu" firstAttribute="trailing" secondItem="fnl-2z-Ty3" secondAttribute="trailing" id="vhG-2J-ycn"/>
                <constraint firstItem="zwg-25-pZa" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" constant="24" id="y1k-fl-IHD"/>
                <constraint firstItem="pgh-30-Wdu" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" id="zDL-fI-VWq"/>
            </constraints>
            <point key="canvasLocation" x="130.53435114503816" y="-12.67605633802817"/>
        </view>
    </objects>
    <resources>
        <image name="forwarIcon" width="16" height="16"/>
        <image name="peopleCircle" width="48" height="48"/>
    </resources>
</document>
