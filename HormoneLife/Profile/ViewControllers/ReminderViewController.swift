//
//  ReminderViewController.swift
//  HormoneLife
//
//  Created by Tank on 2024/6/9.
//

import UIKit
import MJRefresh

class ReminderViewController: BaseViewController {

    let viewModel = RemindViewModel()
    
    @IBOutlet weak var tableView: UITableView!
    
    var timeTitle: String = "Wednesday, 07/17/2024, 3:30 PM"
    var reminderMessages: [String] = ["uiweif", "dhsajlkd"]
    
    override func viewDidLoad() {
        super.viewDidLoad()

        setupNavigationBar()
        setupTableView()
        downRefreshDataRequest()
    }
    
    func downRefreshDataRequest() {
        self.viewModel.getReminderPage(refresh: true) {[weak self] success in
            self?.handleRequestSuccessResult(refresh: self!.viewModel.hasMoreData, count: self!.viewModel.dataSource.count)
        } failure: { error in
            self.handleRequestFailureResult()
        }

    }
    
    func upMoreDataRequest() {
        self.viewModel.getReminderPage(refresh: true) {[weak self] success in
            self?.handleRequestSuccessResult(refresh: self!.viewModel.hasMoreData, count: self!.viewModel.dataSource.count)
        } failure: { error in
            self.handleRequestFailureResult()
        }
    }
    
    private func setupTableView() {
        tableView.separatorStyle = .none
        tableView.keyboardDismissMode = .onDrag
        tableView.rowHeight = UITableView.automaticDimension
        tableView.delegate = self
        tableView.dataSource = self
        tableView.mj_header = MJRefreshNormalHeader(refreshingBlock: {
            self.downRefreshDataRequest()
        })
        tableView.register(ReminderMessageCell.classForCoder(), forCellReuseIdentifier: ReminderMessageCell.description())
//        tableView.register(UINib(nibName: "NoticeTableViewCell", bundle: nil), forCellReuseIdentifier: "NoticeTableViewCell")
        tableView.register(RemindSectionHeaderView.self, forHeaderFooterViewReuseIdentifier: "RemindSectionHeaderView")
        tableView.register(EmptyTableViewCell.self, forCellReuseIdentifier: "EmptyTableViewCell")
    }
    
    private func setupNavigationBar() {
        navigationItem.title = "Reminder Message"
        //navigationItem.rightBarButtonItem = UIBarButtonItem(image: UIImage(named: "messageSettingIcon"), style: .done, target: self, action: #selector(didTapNavRightButton))

    }

    @objc func didTapNavRightButton() {
        let messageSetVC = MessageSettingViewController()
        hidesBottomBarWhenPushed = true
        navigationController?.pushViewController(messageSetVC, animated: true)
        hidesBottomBarWhenPushed = true
    }
}

extension ReminderViewController: UITableViewDelegate, UITableViewDataSource {
    func numberOfSections(in tableView: UITableView) -> Int {
        return 1
    }
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return self.viewModel.dataSource.count > 0 ? self.viewModel.dataSource.count : 1
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        if self.viewModel.dataSource.count > 0 {
            guard self.viewModel.dataSource.count > 0,
                  let cell = tableView.dequeueReusableCell(withIdentifier: ReminderMessageCell.description()) as? ReminderMessageCell else {
                return UITableViewCell()
            }
            
            let preIndex = max(0, indexPath.row - 1)
            cell.refresh(currentModel: self.viewModel.dataSource[indexPath.row], preModel: self.viewModel.dataSource[preIndex], isFirstIndexPath: indexPath.row == 0)
            return cell
        } else {
            guard let cell = tableView.dequeueReusableCell(withIdentifier: "EmptyTableViewCell") as? EmptyTableViewCell else {
                return UITableViewCell()
            }
            cell.setupCell(height: tableView.height())
            return cell
        }
    }

    
    func tableView(_ tableView: UITableView, viewForFooterInSection section: Int) -> UIView? {
        return UIView()
    }
    
    func tableView(_ tableView: UITableView, heightForFooterInSection section: Int) -> CGFloat {
        return CGFloat.leastNormalMagnitude
    }
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        func markIsReadById(_ messageId: String) {
            UserMessageInteractor.markAsReadById(messageId) { result in
                print("read")
            }
        }
    }
    
    func handleRequestSuccessResult(refresh: Bool, count: Int) {
        self.tableView.reloadData()
        if self.tableView.mj_header?.isRefreshing == true {
            self.tableView.mj_header?.endRefreshing()
        }
        if self.tableView.mj_footer?.isRefreshing == true {
            self.tableView.mj_footer?.endRefreshing()
        }
        if refresh {
            self.tableView.mj_footer = MJRefreshAutoNormalFooter(refreshingBlock: {
                self.upMoreDataRequest()
            })
            
        } else {
            self.tableView.mj_footer?.endRefreshingWithNoMoreData()
        }
        
//        if count > 0 {
//            self.noDataView.isHidden = true
//        } else {
//            self.noDataView.isHidden = false
//        }
    }
    
    func handleRequestFailureResult() {
        if self.tableView.mj_header?.isRefreshing == true {
            self.tableView.mj_header?.endRefreshing()
        }
        if self.tableView.mj_footer?.isRefreshing == true {
            self.tableView.mj_footer?.endRefreshing()
        }
    }
}
