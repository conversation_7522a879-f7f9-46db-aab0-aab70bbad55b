//
//  ProfileViewController.swift
//  HormoneLife
//
//  Created by Tank on 2024/6/8.
//

import UIKit
import SDWebImage

protocol ProfileViewControllerDelegate: AnyObject {
    func didTap(_ profileType: ProfileViewController.ProfileActionType)
}

class ProfileViewController: UIViewController {
    
    weak var delegate: ProfileViewControllerDelegate?
    let tagViewModel = SystemTagViewModel()
    
    
    var viewController: UIViewController!
    
    @IBOutlet weak var avatarImage: UIImageView!
    @IBOutlet weak var NameLabel: UILabel!
    @IBOutlet weak var personalLabel: UILabel!
    @IBOutlet weak var notificationCountLabel: UILabel!
    @IBOutlet weak var logoutButton: UIButton!
    
    let messageViewModel = MessageViewModel()
    
    override func viewDidLoad() {
        super.viewDidLoad()

        setupUI()
        setupGuesture()
        getTagsDatas()
    }
    
    private func setupUI() {
        logoutButton.layer.cornerRadius = 4
        logoutButton.layer.borderWidth = 1
        logoutButton.layer.borderColor = UIColor.mainTextColor.cgColor
        
        personalLabel.layer.cornerRadius = 4
        personalLabel.numberOfLines = 0
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        fetchMessagesCount()
    }
    
    private func fetchMessagesCount() {
        self.messageViewModel.getUnreadCount { success in
//            self.tableView.reloadData()
            self.notificationCountLabel.text = success.messageCount > 0 ? "\(success.messageCount)" : ""
        } failure: { error in
            
        }
        
//        UserMessageInteractor.fetchMessages { list in
//            guard let messageList = list else { return }
//            self.notificationCountLabel.text = messageList.totalCount
//        }
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        navigationController?.navigationBar.isHidden = true
    }
    
    private func setupGuesture() {
        let tapAvatar = UITapGestureRecognizer(target: self, action: #selector(didTapAvatar))
        let tapName = UITapGestureRecognizer(target: self, action: #selector(didTapAvatar))
        avatarImage.addGestureRecognizer(tapAvatar)
        NameLabel.addGestureRecognizer(tapName)
        self.avatarImage.layer.cornerRadius = 24
        if let avatarImageStr = Interface.shared().loggedInUser?.userInfo.headImg {
            avatarImage.sd_setImage(with: URL(string: avatarImageStr), placeholderImage: UIImage(named: "avatarSample"), context: nil)
        }
        
        NameLabel.text = "\(Interface.shared().loggedInUser?.userInfo.firstName ?? "") \(Interface.shared().loggedInUser?.userInfo.userName ?? "")"
        //personalLabel.text = Interface.shared().loggedInUser?.userInfo.label
    }
    
    func getTagsDatas() {
        
        // base role
//        self.personalLabel.text = Interface.shared().loggedInUser?.userInfo.roleType == 1 ? "get pregnant" : "cycle tracking"
        self.personalLabel.text = Interface.shared().loggedInUser?.userInfo.roleType == 1 ? "Conception" : "Cycle Tracking"
        
//        tagViewModel.getNotesPage { [weak self] success in
//            self?.personalLabel.text = self?.tagViewModel.selectDataSource.map { $0.components(separatedBy: ",").first ?? "" }.joined(separator: ", ")
//        } failure: { error in
//
//        }
    }
    
    @objc func didTapAvatar() {
        delegate?.didTap(.avatarEdit)
        dismiss(animated: true)
    }
    
    @IBAction func profileEditAction(_ sender: Any) {
        didTapAvatar()
    }

    @IBAction func didTapSupportAndHelpBtn(_ sender: Any) {
        delegate?.didTap(.supportAndHelp)
        dismiss(animated: true)
    }
    
    @IBAction func didTapMemoAction(_ sender: Any) {
        delegate?.didTap(.memo)
        dismiss(animated: true)
    }
    
    @IBAction func aboutHormonelifeAction(_ sender: Any) {
        delegate?.didTap(.aboutHormonelife)
        dismiss(animated: true)
    }
    
    @IBAction func settingAction(_ sender: Any) {
        delegate?.didTap(.settings)
        dismiss(animated: true)
    }
    
    @IBAction func notificationsAction(_ sender: Any) {
        delegate?.didTap(.notifications)
        dismiss(animated: true)
    }
    
    @IBAction func cycleReportsAction(_ sender: Any) {
        delegate?.didTap(.cycleReports)
        dismiss(animated: true)
    }
    
    @IBAction func logouAction(_ sender: Any) {
        UserInteractor.userLogout { result in
            guard let isLogout = result else { return }
            UserDefaults.standard.removeObject(forKey: rememberAccountKey)
            UserDefaults.standard.synchronize()
            UserDefaults.standard.userToken = nil
            currentWindow?.rootViewController = UINavigationController(rootViewController: LoginViewController())
        }
    }
}

extension ProfileViewController {
    enum ProfileActionType {
        case avatarEdit
        case cycleReports
        case memo
        case supportAndHelp
        case notifications
        case aboutHormonelife
        case settings
        case logout
    }
}
