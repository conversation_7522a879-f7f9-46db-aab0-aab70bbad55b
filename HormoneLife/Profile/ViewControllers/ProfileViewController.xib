<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="21701" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21679"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="gilroy regular.otf">
            <string>Gilroy-Regular</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="ProfileViewController" customModule="HormoneLife" customModuleProvider="target">
            <connections>
                <outlet property="NameLabel" destination="O9S-KS-xH8" id="d2H-KV-e7B"/>
                <outlet property="avatarImage" destination="7pM-g6-52W" id="Zqs-Oh-ncy"/>
                <outlet property="logoutButton" destination="fQP-1a-Ow3" id="cgl-om-qfU"/>
                <outlet property="notificationCountLabel" destination="bFn-kX-nr4" id="HDT-Mv-9np"/>
                <outlet property="personalLabel" destination="cby-ts-Yvz" id="roB-aO-Phw"/>
                <outlet property="view" destination="i5M-Pr-FkT" id="sfx-zR-JGt"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="i5M-Pr-FkT">
            <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="cl5-lu-g72">
                    <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                    <subviews>
                        <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" contentInsetAdjustmentBehavior="never" translatesAutoresizingMaskIntoConstraints="NO" id="FFG-IX-Hez">
                            <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                            <subviews>
                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="7iy-1Q-Uh8">
                                    <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                                    <subviews>
                                        <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="40" translatesAutoresizingMaskIntoConstraints="NO" id="53B-C2-XdI">
                                            <rect key="frame" x="32" y="145" width="329" height="587"/>
                                            <subviews>
                                                <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="NCK-8R-jAO">
                                                    <rect key="frame" x="0.0" y="0.0" width="329" height="88"/>
                                                    <subviews>
                                                        <stackView opaque="NO" contentMode="scaleToFill" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="hGm-we-UPW">
                                                            <rect key="frame" x="0.0" y="0.0" width="329" height="48"/>
                                                            <subviews>
                                                                <imageView clipsSubviews="YES" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="avatarSample" translatesAutoresizingMaskIntoConstraints="NO" id="7pM-g6-52W">
                                                                    <rect key="frame" x="0.0" y="0.0" width="48" height="48"/>
                                                                    <constraints>
                                                                        <constraint firstAttribute="width" constant="48" id="U5J-pC-GlK"/>
                                                                    </constraints>
                                                                </imageView>
                                                                <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="pWZ-P0-Z61">
                                                                    <rect key="frame" x="64" y="0.0" width="265" height="48"/>
                                                                    <subviews>
                                                                        <stackView opaque="NO" contentMode="scaleToFill" spacing="4" translatesAutoresizingMaskIntoConstraints="NO" id="PrO-Oa-8fv">
                                                                            <rect key="frame" x="0.0" y="0.0" width="265" height="20"/>
                                                                            <subviews>
                                                                                <label opaque="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="LILIS" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="O9S-KS-xH8">
                                                                                    <rect key="frame" x="0.0" y="0.0" width="245" height="20"/>
                                                                                    <fontDescription key="fontDescription" type="system" pointSize="20"/>
                                                                                    <color key="textColor" red="0.1925059855" green="0.057953603569999998" blue="0.3526558876" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                                    <nil key="highlightedColor"/>
                                                                                </label>
                                                                                <button opaque="NO" contentMode="scaleAspectFit" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="cWY-2t-qiX">
                                                                                    <rect key="frame" x="249" y="0.0" width="16" height="20"/>
                                                                                    <constraints>
                                                                                        <constraint firstAttribute="width" constant="16" id="yTl-03-Dj7"/>
                                                                                    </constraints>
                                                                                    <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                                                    <state key="normal" title="Button" image="editPan"/>
                                                                                    <connections>
                                                                                        <action selector="profileEditAction:" destination="-1" eventType="touchUpInside" id="hrk-yq-kEm"/>
                                                                                    </connections>
                                                                                </button>
                                                                            </subviews>
                                                                        </stackView>
                                                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="W42-7q-GHZ">
                                                                            <rect key="frame" x="0.0" y="28" width="265" height="20"/>
                                                                            <subviews>
                                                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="  Love life  " textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="cby-ts-Yvz">
                                                                                    <rect key="frame" x="0.0" y="0.0" width="56" height="20"/>
                                                                                    <color key="backgroundColor" red="0.38800710440000002" green="0.13929319379999999" blue="0.90519392489999995" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                                    <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="12"/>
                                                                                    <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                    <nil key="highlightedColor"/>
                                                                                </label>
                                                                            </subviews>
                                                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                            <constraints>
                                                                                <constraint firstAttribute="bottom" secondItem="cby-ts-Yvz" secondAttribute="bottom" id="MLN-yn-AGo"/>
                                                                                <constraint firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="cby-ts-Yvz" secondAttribute="trailing" id="Q5e-Nz-ByW"/>
                                                                                <constraint firstItem="cby-ts-Yvz" firstAttribute="leading" secondItem="W42-7q-GHZ" secondAttribute="leading" id="bQj-ng-ufi"/>
                                                                                <constraint firstAttribute="height" relation="greaterThanOrEqual" constant="20" id="pWu-Oc-mtZ"/>
                                                                                <constraint firstItem="cby-ts-Yvz" firstAttribute="top" secondItem="W42-7q-GHZ" secondAttribute="top" id="qPl-KO-FQt"/>
                                                                            </constraints>
                                                                            <userDefinedRuntimeAttributes>
                                                                                <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                                                    <integer key="value" value="4"/>
                                                                                </userDefinedRuntimeAttribute>
                                                                            </userDefinedRuntimeAttributes>
                                                                        </view>
                                                                    </subviews>
                                                                    <constraints>
                                                                        <constraint firstAttribute="trailing" secondItem="W42-7q-GHZ" secondAttribute="trailing" id="XZ1-tg-zfi"/>
                                                                        <constraint firstItem="W42-7q-GHZ" firstAttribute="leading" secondItem="pWZ-P0-Z61" secondAttribute="leading" id="f4T-SI-cb4"/>
                                                                    </constraints>
                                                                </stackView>
                                                            </subviews>
                                                        </stackView>
                                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="dwh-fl-8xC">
                                                            <rect key="frame" x="0.0" y="48" width="329" height="40"/>
                                                            <subviews>
                                                                <view alpha="0.59999999999999998" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="O0y-ky-I9n">
                                                                    <rect key="frame" x="0.0" y="39" width="329" height="1"/>
                                                                    <color key="backgroundColor" red="0.50876241919999998" green="0.4311534762" blue="0.6075879931" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                    <constraints>
                                                                        <constraint firstAttribute="height" constant="1" id="WFI-6A-4L7"/>
                                                                    </constraints>
                                                                </view>
                                                            </subviews>
                                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                            <constraints>
                                                                <constraint firstAttribute="trailing" secondItem="O0y-ky-I9n" secondAttribute="trailing" id="26m-RH-vjo"/>
                                                                <constraint firstAttribute="height" constant="40" id="JsY-gm-jYF"/>
                                                                <constraint firstItem="O0y-ky-I9n" firstAttribute="leading" secondItem="dwh-fl-8xC" secondAttribute="leading" id="Tfw-mt-EMo"/>
                                                                <constraint firstAttribute="bottom" secondItem="O0y-ky-I9n" secondAttribute="bottom" id="ZU2-jd-klO"/>
                                                            </constraints>
                                                        </view>
                                                    </subviews>
                                                    <constraints>
                                                        <constraint firstAttribute="height" constant="88" id="wsS-4W-5Tm"/>
                                                    </constraints>
                                                </stackView>
                                                <stackView opaque="NO" contentMode="scaleToFill" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="ubV-e1-0oF">
                                                    <rect key="frame" x="0.0" y="128" width="329" height="24"/>
                                                    <subviews>
                                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="cycleReports" translatesAutoresizingMaskIntoConstraints="NO" id="jlm-Qb-r4q">
                                                            <rect key="frame" x="0.0" y="0.0" width="16" height="24"/>
                                                            <constraints>
                                                                <constraint firstAttribute="width" constant="16" id="6Zv-wG-xGD"/>
                                                            </constraints>
                                                        </imageView>
                                                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="leading" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="mGI-5s-2bt">
                                                            <rect key="frame" x="28" y="0.0" width="301" height="24"/>
                                                            <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="14"/>
                                                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                            <state key="normal" title="Cycle Reports">
                                                                <color key="titleColor" red="0.1925059855" green="0.057953603569999998" blue="0.3526558876" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                            </state>
                                                            <connections>
                                                                <action selector="cycleReportsAction:" destination="-1" eventType="touchUpInside" id="b8O-ee-YYd"/>
                                                            </connections>
                                                        </button>
                                                    </subviews>
                                                    <constraints>
                                                        <constraint firstAttribute="height" constant="24" id="eYb-xJ-5bL"/>
                                                    </constraints>
                                                </stackView>
                                                <stackView opaque="NO" contentMode="scaleToFill" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="BlL-GY-Ylg">
                                                    <rect key="frame" x="0.0" y="192" width="329" height="24"/>
                                                    <subviews>
                                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" alpha="0.80000000000000004" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="memoIcon" translatesAutoresizingMaskIntoConstraints="NO" id="vvO-rB-hlL">
                                                            <rect key="frame" x="0.0" y="0.0" width="18" height="24"/>
                                                            <constraints>
                                                                <constraint firstAttribute="width" constant="18" id="pvY-Hg-ELC"/>
                                                            </constraints>
                                                        </imageView>
                                                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="leading" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="xIT-vS-Mo2">
                                                            <rect key="frame" x="30" y="0.0" width="299" height="24"/>
                                                            <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="14"/>
                                                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                            <state key="normal" title="Memo">
                                                                <color key="titleColor" red="0.1925059855" green="0.057953603569999998" blue="0.3526558876" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                            </state>
                                                            <connections>
                                                                <action selector="didTapMemoAction:" destination="-1" eventType="touchUpInside" id="Jef-3S-nQJ"/>
                                                            </connections>
                                                        </button>
                                                    </subviews>
                                                    <constraints>
                                                        <constraint firstAttribute="height" constant="24" id="Yj8-4q-7S5"/>
                                                    </constraints>
                                                </stackView>
                                                <stackView opaque="NO" contentMode="scaleToFill" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="6Ja-jc-JjA">
                                                    <rect key="frame" x="0.0" y="256" width="329" height="24"/>
                                                    <subviews>
                                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="notifications" translatesAutoresizingMaskIntoConstraints="NO" id="vdR-he-RSI">
                                                            <rect key="frame" x="0.0" y="0.0" width="16" height="24"/>
                                                            <constraints>
                                                                <constraint firstAttribute="width" constant="16" id="Vgr-8q-Rl4"/>
                                                            </constraints>
                                                        </imageView>
                                                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="leading" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="0Bv-1i-ofE">
                                                            <rect key="frame" x="28" y="0.0" width="259" height="24"/>
                                                            <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="14"/>
                                                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                            <state key="normal" title="Notifications">
                                                                <color key="titleColor" red="0.1925059855" green="0.057953603569999998" blue="0.3526558876" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                            </state>
                                                            <connections>
                                                                <action selector="notificationsAction:" destination="-1" eventType="touchUpInside" id="UdP-VT-jx9"/>
                                                            </connections>
                                                        </button>
                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="bFn-kX-nr4">
                                                            <rect key="frame" x="299" y="0.0" width="30" height="24"/>
                                                            <constraints>
                                                                <constraint firstAttribute="width" constant="30" id="tiz-bh-7ag"/>
                                                            </constraints>
                                                            <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                            <color key="textColor" red="0.1925059855" green="0.057953603569999998" blue="0.3526558876" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                            <nil key="highlightedColor"/>
                                                        </label>
                                                    </subviews>
                                                    <constraints>
                                                        <constraint firstAttribute="height" constant="24" id="5gC-Oz-rTL"/>
                                                    </constraints>
                                                </stackView>
                                                <stackView opaque="NO" contentMode="scaleToFill" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="UdV-pr-dnT">
                                                    <rect key="frame" x="0.0" y="320" width="329" height="24"/>
                                                    <subviews>
                                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="supportHelp" translatesAutoresizingMaskIntoConstraints="NO" id="FGv-As-yDW">
                                                            <rect key="frame" x="0.0" y="0.0" width="16" height="24"/>
                                                            <constraints>
                                                                <constraint firstAttribute="width" constant="16" id="gqc-5v-7bE"/>
                                                            </constraints>
                                                        </imageView>
                                                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="leading" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="cgM-QK-tYx">
                                                            <rect key="frame" x="28" y="0.0" width="264" height="24"/>
                                                            <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="14"/>
                                                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                            <state key="normal" title="Support &amp; Help">
                                                                <color key="titleColor" red="0.1925059855" green="0.057953603569999998" blue="0.3526558876" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                            </state>
                                                            <connections>
                                                                <action selector="didTapSupportAndHelpBtn:" destination="-1" eventType="touchUpInside" id="zyf-NA-nDu"/>
                                                            </connections>
                                                        </button>
                                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="kqU-P1-1BT">
                                                            <rect key="frame" x="304" y="0.0" width="25" height="24"/>
                                                            <subviews>
                                                                <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="NEW" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="25B-W2-Kj8">
                                                                    <rect key="frame" x="0.0" y="5" width="25" height="14"/>
                                                                    <color key="backgroundColor" red="0.38800710440000002" green="0.13929319379999999" blue="0.90519392489999995" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                    <constraints>
                                                                        <constraint firstAttribute="height" constant="14" id="UpH-G1-WJK"/>
                                                                    </constraints>
                                                                    <fontDescription key="fontDescription" type="system" pointSize="10"/>
                                                                    <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                    <nil key="highlightedColor"/>
                                                                </label>
                                                            </subviews>
                                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                            <constraints>
                                                                <constraint firstItem="25B-W2-Kj8" firstAttribute="centerY" secondItem="kqU-P1-1BT" secondAttribute="centerY" id="N0w-5a-bsb"/>
                                                                <constraint firstAttribute="width" constant="25" id="l4h-2v-Q0x"/>
                                                                <constraint firstAttribute="trailing" secondItem="25B-W2-Kj8" secondAttribute="trailing" id="n0B-Iu-SPq"/>
                                                                <constraint firstItem="25B-W2-Kj8" firstAttribute="leading" secondItem="kqU-P1-1BT" secondAttribute="leading" id="qJn-xq-ddy"/>
                                                            </constraints>
                                                        </view>
                                                    </subviews>
                                                    <constraints>
                                                        <constraint firstAttribute="height" constant="24" id="lAM-32-Ulg"/>
                                                    </constraints>
                                                </stackView>
                                                <stackView opaque="NO" contentMode="scaleToFill" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="H6e-ti-Te7">
                                                    <rect key="frame" x="0.0" y="384" width="329" height="24"/>
                                                    <subviews>
                                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="sboutHormonelife" translatesAutoresizingMaskIntoConstraints="NO" id="LNL-mb-fVm">
                                                            <rect key="frame" x="0.0" y="0.0" width="16" height="24"/>
                                                            <constraints>
                                                                <constraint firstAttribute="width" constant="16" id="hXN-he-nFO"/>
                                                            </constraints>
                                                        </imageView>
                                                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="leading" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="fVx-z5-gke">
                                                            <rect key="frame" x="28" y="0.0" width="301" height="24"/>
                                                            <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="14"/>
                                                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                            <state key="normal" title="About HORMONELIFE">
                                                                <color key="titleColor" red="0.1925059855" green="0.057953603569999998" blue="0.3526558876" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                            </state>
                                                            <connections>
                                                                <action selector="aboutHormonelifeAction:" destination="-1" eventType="touchUpInside" id="tYz-uH-PJZ"/>
                                                            </connections>
                                                        </button>
                                                    </subviews>
                                                    <constraints>
                                                        <constraint firstAttribute="height" constant="24" id="mXs-gg-KNR"/>
                                                    </constraints>
                                                </stackView>
                                                <stackView opaque="NO" contentMode="scaleToFill" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="lpz-KA-j4m">
                                                    <rect key="frame" x="0.0" y="448" width="329" height="24"/>
                                                    <subviews>
                                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="settings" translatesAutoresizingMaskIntoConstraints="NO" id="kpj-xt-U0j">
                                                            <rect key="frame" x="0.0" y="0.0" width="16" height="24"/>
                                                            <constraints>
                                                                <constraint firstAttribute="width" constant="16" id="eeM-aD-W1d"/>
                                                            </constraints>
                                                        </imageView>
                                                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="leading" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Cnc-NZ-ahF">
                                                            <rect key="frame" x="28" y="0.0" width="301" height="24"/>
                                                            <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="14"/>
                                                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                            <state key="normal" title="Settings">
                                                                <color key="titleColor" red="0.1925059855" green="0.057953603569999998" blue="0.3526558876" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                            </state>
                                                            <connections>
                                                                <action selector="settingAction:" destination="-1" eventType="touchUpInside" id="tFk-JL-vkw"/>
                                                            </connections>
                                                        </button>
                                                    </subviews>
                                                    <constraints>
                                                        <constraint firstAttribute="height" constant="24" id="b7O-J4-A25"/>
                                                    </constraints>
                                                </stackView>
                                                <view alpha="0.59999999999999998" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="aPa-Wq-77X">
                                                    <rect key="frame" x="0.0" y="512" width="329" height="1"/>
                                                    <color key="backgroundColor" red="0.50876241919999998" green="0.4311534762" blue="0.6075879931" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                    <constraints>
                                                        <constraint firstAttribute="height" constant="1" id="xmU-qS-Anb"/>
                                                    </constraints>
                                                </view>
                                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="fQP-1a-Ow3">
                                                    <rect key="frame" x="0.0" y="553" width="329" height="34"/>
                                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                    <state key="normal" title="Logout">
                                                        <color key="titleColor" red="0.1925059855" green="0.057953603569999998" blue="0.3526558876" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                    </state>
                                                    <userDefinedRuntimeAttributes>
                                                        <userDefinedRuntimeAttribute type="number" keyPath="layer.borderWidth">
                                                            <integer key="value" value="2"/>
                                                        </userDefinedRuntimeAttribute>
                                                        <userDefinedRuntimeAttribute type="color" keyPath="layer.borderColor">
                                                            <color key="value" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        </userDefinedRuntimeAttribute>
                                                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                            <integer key="value" value="4"/>
                                                        </userDefinedRuntimeAttribute>
                                                    </userDefinedRuntimeAttributes>
                                                    <connections>
                                                        <action selector="logouAction:" destination="-1" eventType="touchUpInside" id="N53-vI-NBv"/>
                                                    </connections>
                                                </button>
                                            </subviews>
                                        </stackView>
                                    </subviews>
                                    <color key="backgroundColor" red="0.99999994039999995" green="1" blue="0.99999994039999995" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                    <constraints>
                                        <constraint firstItem="53B-C2-XdI" firstAttribute="top" secondItem="7iy-1Q-Uh8" secondAttribute="top" constant="145" id="8ad-KC-au5"/>
                                        <constraint firstAttribute="trailing" secondItem="53B-C2-XdI" secondAttribute="trailing" constant="32" id="HxQ-Ja-oNK"/>
                                        <constraint firstItem="53B-C2-XdI" firstAttribute="leading" secondItem="7iy-1Q-Uh8" secondAttribute="leading" constant="32" id="Sq6-fg-j4F"/>
                                    </constraints>
                                </view>
                            </subviews>
                            <constraints>
                                <constraint firstItem="7iy-1Q-Uh8" firstAttribute="top" secondItem="FFG-IX-Hez" secondAttribute="top" id="0bw-wQ-zKi"/>
                                <constraint firstItem="7iy-1Q-Uh8" firstAttribute="leading" secondItem="FFG-IX-Hez" secondAttribute="leading" id="AVa-3T-dem"/>
                                <constraint firstItem="7iy-1Q-Uh8" firstAttribute="width" secondItem="FFG-IX-Hez" secondAttribute="width" id="NJo-H8-zb9"/>
                                <constraint firstAttribute="trailing" secondItem="7iy-1Q-Uh8" secondAttribute="trailing" id="Xo3-3A-i1W"/>
                                <constraint firstItem="7iy-1Q-Uh8" firstAttribute="centerY" secondItem="FFG-IX-Hez" secondAttribute="centerY" id="fgt-m5-mdu"/>
                                <constraint firstAttribute="bottom" secondItem="7iy-1Q-Uh8" secondAttribute="bottom" id="hqQ-mw-8n8"/>
                            </constraints>
                        </scrollView>
                    </subviews>
                    <viewLayoutGuide key="safeArea" id="vcb-ij-EBK"/>
                    <color key="backgroundColor" red="0.99999994039999995" green="1" blue="0.99999994039999995" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                    <constraints>
                        <constraint firstItem="FFG-IX-Hez" firstAttribute="top" secondItem="cl5-lu-g72" secondAttribute="top" id="19X-OB-bzx"/>
                        <constraint firstItem="FFG-IX-Hez" firstAttribute="leading" secondItem="cl5-lu-g72" secondAttribute="leading" id="SLc-cu-lCq"/>
                        <constraint firstAttribute="width" constant="280" id="f18-8K-hv2"/>
                        <constraint firstAttribute="trailing" secondItem="FFG-IX-Hez" secondAttribute="trailing" id="fM1-yU-igP"/>
                        <constraint firstAttribute="bottom" secondItem="FFG-IX-Hez" secondAttribute="bottom" id="y4p-6t-CCu"/>
                    </constraints>
                </view>
            </subviews>
            <viewLayoutGuide key="safeArea" id="fnl-2z-Ty3"/>
            <color key="backgroundColor" red="0.99999994039999995" green="1" blue="0.99999994039999995" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
            <constraints>
                <constraint firstItem="cl5-lu-g72" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" id="071-yB-KQa"/>
                <constraint firstAttribute="bottom" secondItem="cl5-lu-g72" secondAttribute="bottom" id="1Kn-pK-ViR"/>
                <constraint firstItem="cl5-lu-g72" firstAttribute="top" secondItem="i5M-Pr-FkT" secondAttribute="top" id="7ri-Ie-bjL"/>
                <constraint firstItem="cl5-lu-g72" firstAttribute="trailing" secondItem="fnl-2z-Ty3" secondAttribute="trailing" id="frs-gy-zPE"/>
            </constraints>
            <point key="canvasLocation" x="64.885496183206101" y="-11.267605633802818"/>
        </view>
    </objects>
    <resources>
        <image name="avatarSample" width="48" height="48"/>
        <image name="cycleReports" width="16" height="16"/>
        <image name="editPan" width="16" height="16"/>
        <image name="memoIcon" width="16" height="16"/>
        <image name="notifications" width="16" height="16"/>
        <image name="sboutHormonelife" width="16" height="16"/>
        <image name="settings" width="16" height="16"/>
        <image name="supportHelp" width="16" height="16"/>
    </resources>
</document>
