//
//  UnbingPopupViewController.swift
//  HormoneLife
//
//  Created by Tank on 2024/9/9.
//

import UIKit

class UnbingPopupViewController: UIViewController {

    @IBOutlet weak var untieButton: UIButton!
    @IBOutlet weak var titleLabel: UILabel!
    @IBOutlet weak var accountLabel: UILabel!
    
    let boundType: BoundViewController.BoundType
    init(_ boundType: BoundViewController.BoundType = .mobile) {
        self.boundType = boundType
        super.init(nibName: "UnbingPopupViewController", bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()

        view.backgroundColor = .black.withAlphaComponent(0.5)
        
        untieButton.layer.borderWidth = 1
        untieButton.layer.borderColor = UIColor.mainTextColor.cgColor
        untieButton.layer.cornerRadius = 4
        
        titleLabel.text = self.boundType.vcTitle
        switch self.boundType {
        case .unBoundGoogle(let google):
            accountLabel.text = google
        case .unBoundFacebook(let facebook):
            accountLabel.text = facebook
        case .unBoundAppleId(let appleId):
            accountLabel.text = appleId
        default:
            return
        }
    }
    
    @IBAction func untieButtonClicked(_ sender: Any) {
        let alert = UIAlertController(title: "Untie Your Account", message: "Are you sure want to \(boundType.vcTitle)?", preferredStyle:.alert)
        let confirmAction = UIAlertAction(title: "Confirm", style: .default) { _ in
            UserInteractor.unBindThirdPartAccount(self.boundType.grantType) { success in
                self.success()
            }
        }
        let cancelAction = UIAlertAction(title: "Cancel", style:.default, handler: nil)
        alert.addAction(cancelAction)
        alert.addAction(confirmAction)
        present(alert, animated: true, completion: nil)
    }
    
    
    @IBAction func backButtonClicked(_ sender: Any) {
        navigationController?.popViewController(animated: false)
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        navigationController?.navigationBar.isHidden = true
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        navigationController?.navigationBar.isHidden = false
    }
    
    func success() {
        hl_fetchUserInfo({
            self.navigationController?.popToRootViewController(animated: false)
        })
    }
}
