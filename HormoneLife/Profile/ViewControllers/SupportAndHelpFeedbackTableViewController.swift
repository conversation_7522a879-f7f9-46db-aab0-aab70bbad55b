//
//  SupportAndHelpFeedbackTableViewController.swift
//  HormoneLife
//
//  Created by Tank on 2024/8/5.
//

import UIKit

class SupportAndHelpFeedbackTableViewController: PickPhotoViewController, UITableViewDelegate, UITableViewDataSource, FeedbackTableViewCellDelegate, PickPhotoDelegate {

    let viewModel = FeedbackAddViewModel()
    
    let systemViewModel = CommonViewModel()
    
    var selectImageIndex = 0
    
    var imageUrls = [String]()
        
    var selectTagValue: String?
    var feedbackContent: String?
    var contactInfoInput: String?
//    var tags: [String] = ["dsafes", "afeew", "dtwewfw", "ghjiwfhj", "fjdioweflsj", "dsafes", "afeew", "dtwewfw", "ghjiwfhj", "fjdioweflsj", "dsafes", "afeew", "dtwewfw", "ghjiwfhj", "fjdioweflsj", "dsafes", "afeew", "dtwewfw", "ghjiwfhj", "fjdioweflsj"]
    
    private lazy var tableView: UITableView = {
        let tableView = UITableView(frame: .zero, style: .plain)
        tableView.backgroundColor = .themeColor
        tableView.showsVerticalScrollIndicator = false
        tableView.separatorStyle = .none
        
        tableView.register(TagsTableViewCell.self, forCellReuseIdentifier: "TagsTableViewCell")
        tableView.register(TagsTableViewFooterView.self, forHeaderFooterViewReuseIdentifier: "TagsTableViewFooterView")
        tableView.register(FeedbackTableViewCell.self, forCellReuseIdentifier: "FeedbackTableViewCell")
        tableView.register(ContactInfoTableViewCell.self, forCellReuseIdentifier: "ContactInfoTableViewCell")
        tableView.register(AddImageCell.classForCoder(), forCellReuseIdentifier: AddImageCell.description())
        tableView.keyboardDismissMode = .onDrag
        tableView.dataSource = self
        tableView.delegate = self
        tableView.rowHeight = UITableView.automaticDimension
        return tableView
    }()
    
    lazy var sendMessageButton: UIButton = {
        let b = UIButton(type: .custom)
        b.backgroundColor = .mainTextColor
        b.layer.cornerRadius = 4
        b.setTitle("Send Message", for: .normal)
        b.titleLabel?.font = .regularGilroyFont(16)
        b.setTitleColor(.white, for: .normal)
        b.addTarget(self, action: #selector(didTapSend), for: .touchUpInside)
        return b
    }()
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        pickPhotoDelegate = self
        navigationItem.title = "Support & Help"
        view.backgroundColor = .themeColor
//        view.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(didTapView)))
        setupUI()
        self.requestTags()
    }
    
    func requestTags() {
        self.systemViewModel.getSystemDictionary(type: "feedback_type") { result in
            self.tableView.reloadData()
        } failure: { error in
            
        }
    }
    
    private func setupUI() {
        view.addSubview(tableView)
        view.addSubview(sendMessageButton)
        sendMessageButton.snp.makeConstraints { make in
            make.height.equalTo(48)
            make.left.right.equalToSuperview().inset(24)
            make.bottom.equalToSuperview().inset(34)
        }
        
        tableView.snp.makeConstraints { make in
            make.left.top.right.equalToSuperview()
            make.bottom.equalTo(sendMessageButton.snp.top)
        }

        // 增加滚动内容区域底部间距，让最后一个 cell 与按钮保持间距 20pt
        tableView.contentInset = UIEdgeInsets(top: 0, left: 0, bottom: 20, right: 0)
    }
    
    @objc func didTapSend() {
        // TODO: call API
        
        self.view.endEditing(true)
        guard let tags = self.selectTagValue else {
            showToachMessage(message: "Select tag!")
            return
        }
        guard let text = self.feedbackContent else {
            showToachMessage(message: "Input content!")
            return
        }
        let lebels = tags.components(separatedBy: ",")
        
        let imgs = self.imageUrls.joined(separator: ",")
        
        let userInfo = Interface.shared().loggedInUser?.userInfo
        let versionStr = Bundle.main.infoDictionary!["CFBundleShortVersionString"] as! String
        let contactPhoneValue = self.contactInfoInput?.trimmingCharacters(in: .whitespacesAndNewlines)
        let contactInfoValue = (userInfo?.phone ?? userInfo?.email)
        self.viewModel.addFeedback(contactInfo: contactInfoValue, contactPhone: contactPhoneValue, eventStatusId: lebels.first, eventStatusName: lebels[1], feedbackContent: text, feedbackImg: imgs, feedbackStatus: nil, feedbackUserId: userInfo?.id, feedbackUserName: userInfo?.userName, feedbackUserNo: nil, id: nil, type: "2", version: versionStr) { result in
            self.navigationController?.popViewController(animated: true)
        } failure: { error in
            
        }

        
        // and then
       
    }
    
//    @objc func didTapView() {
//        view.endEditing(true)
//    }
    
    func didSelected(_ buttonTag: Int) {
        print(buttonTag)
        pickPhoto()
    }
    
    func textFiledDidChange(_ text: String?) {
        self.feedbackContent = text
    }
    
    func callBackImage(photo: UIImage) {
        //addPhotoBtn?.setImage(photo, for: .normal)
        MBProgressHUD.showAdded(to: self.view, animated: true)
        self.uploadViewModel.uploadImages(images: photo) { [weak self] (url, fileName)  in
            MBProgressHUD.hide(for: self!.view, animated: true)
            if self!.selectImageIndex < self!.imageUrls.count {
                // replay
                for (index, _) in self!.imageUrls.enumerated() {
                    if index == self!.selectImageIndex {
                        self!.imageUrls[self!.selectImageIndex] = url
                        break
                    }
                }
            } else {
                //add
                self!.imageUrls.append(url)
            }
            self!.tableView.reloadData()
        } failure: { error in
            MBProgressHUD.hide(for: self.view, animated: true)
        }
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        4
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        
        switch indexPath.row {
        case 0:
            guard let cell = tableView.dequeueReusableCell(withIdentifier: "TagsTableViewCell", for: indexPath) as? TagsTableViewCell else {
                return UITableViewCell()
            }
            cell.setupCell(title: "Type", dataSource: self.systemViewModel.tagsInfos, delegate: self)
            cell.isFeedback = true
            return cell
        case 1:
            guard let cell = tableView.dequeueReusableCell(withIdentifier: "FeedbackTableViewCell", for: indexPath) as? FeedbackTableViewCell else {
                return UITableViewCell()
            }
            cell.delegate = self
            return cell
        case 2:
            let cell = tableView.dequeueReusableCell(withIdentifier: AddImageCell.description()) as! AddImageCell
            cell.refresh(datas: self.imageUrls)
            cell.clickIndex = { [weak self] index in
                self?.selectImageIndex = index
                self?.selectPhoto()
            }
            cell.closeIndex = {[weak self] index in
                self?.imageUrls.remove(at: index)
                tableView.reloadData()
            }
            return cell
        case 3:
            guard let cell = tableView.dequeueReusableCell(withIdentifier: "ContactInfoTableViewCell", for: indexPath) as? ContactInfoTableViewCell else {
                return UITableViewCell()
            }
            cell.textChanged = { [weak self] text in
                self?.contactInfoInput = text
            }
            return cell
        default:
            return UITableViewCell()
        }
    }
    
    func selectPhoto() {
        self.pickPhotosFromLibrary(type: .photoLibrary)
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        switch indexPath.row {
        case 0:
            return 200
        case 1:
            return 288 + 8
        case 2:
            let height = (kScreenWidth - 30 - 30) / 3.0
            if self.imageUrls.count < 3 {
                return height + 15
            } else if self.imageUrls.count < 6 {
                return height * 2 + 15
            } else {
                return height * 3 + 15
            }
        case 3:
            return 120 // contact info card height plus inset
        default:
            return 0
        }
    }
}

extension SupportAndHelpFeedbackTableViewController : AddTagViewControllerDelegate {
    
    func didSelectTag(_ newTag: String) {
        self.selectTagValue = newTag
    }
    
    func didAddTag(_ newTag: String) {
        
    }
}
