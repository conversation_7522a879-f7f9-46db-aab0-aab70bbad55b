//
//  SettingViewController.swift
//  HormoneLife
//
//  Created by Tank on 2024/6/9.
//

import UIKit
import AuthenticationServices
import FBSDKCoreKit
import FBSDKLoginKit
import FBSDKCoreKit_Basics
import GoogleSignIn
import FirebaseCore
import GoogleUtilities
import CryptoKit
import FirebaseAuth

class SettingViewController: BaseViewController {
    
    fileprivate var currentNonce: String?
    
    @IBOutlet weak var checkVersionView: UIView!
    @IBOutlet weak var checkVersionViewLabel: UILabel!
    @IBOutlet weak var logoutButton: UIButton!
    @IBOutlet weak var emailAddress: UIButton!
    @IBOutlet weak var mobileNum: UIButton!
    @IBOutlet weak var appleIDNum: UIButton!
    @IBOutlet weak var facebookNum: UIButton!
    @IBOutlet weak var googleIDNum: UIButton!
    @IBOutlet weak var cacheNum: UIButton!
    @IBOutlet weak var versionLabel: UIButton!
    
    let bindAccountViewModel = BindAccountViewModel()
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        navigationItem.title = "Setting"
        //        setupUI()
        setupLogoutButton()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        navigationController?.navigationBar.isHidden = false
        self.setupUI()
    }
    
    private func setupUI() {
        let user = Interface.shared().loggedInUser?.userInfo
        
        if let email = user?.email, email != "" {
            emailAddress.setTitle(email, for: .normal)
            emailAddress.alpha = 1
        } else {
            emailAddress.alpha = 0.4
        }
        
        if let phone = user?.phone, phone != "" {
            mobileNum.setTitle(phone, for: .normal)
            mobileNum.alpha = 1
        } else {
            mobileNum.alpha = 0.4
        }
        
        
        if let appleA = user?.appleAccount, appleA != "***", !appleA.isEmpty {
            appleIDNum.setTitle(appleA, for: .normal)
            appleIDNum.alpha = 1
        } else {
            appleIDNum.setTitle("Not connected", for: .normal)
            appleIDNum.alpha = 0.4
        }
        
        if let appleA = user?.facebookAccount, appleA != "***", !appleA.isEmpty {
            facebookNum.setTitle(appleA, for: .normal)
            facebookNum.alpha = 1
        } else {
            facebookNum.setTitle("Not connected", for: .normal)
            facebookNum.alpha = 0.4
        }
        if let appleA = user?.googleAccount, appleA != "***", !appleA.isEmpty {
            googleIDNum.setTitle(appleA, for: .normal)
            googleIDNum.alpha = 1
        } else {
            googleIDNum.setTitle("Not connected", for: .normal)
            googleIDNum.alpha = 0.4
        }
        
        cacheNum.setTitle(getCache(), for: .normal)
        versionLabel.setTitle(getVersionNum(), for: .normal)
        
        [emailAddress, mobileNum, appleIDNum, facebookNum, googleIDNum, cacheNum, versionLabel, logoutButton].forEach {
            $0?.setTitleColor(.mainTextColor, for: .normal)
        }
    }
    
    private func getVersionNum() -> String? {
        if let infoDictionary = Bundle.main.infoDictionary {
            if let version = infoDictionary["CFBundleShortVersionString"] as? String {
                return version
            }
        }
        return nil
    }
    
    func getCache() -> String {
        func sizeOfCachesFolder() -> UInt64 {
            let fileManager = FileManager.default
            guard let cachesURL = fileManager.urls(for:.cachesDirectory, in:.userDomainMask).first else {
                return 0
            }
            do {
                let enumerator = fileManager.enumerator(at: cachesURL, includingPropertiesForKeys: nil, options: [.skipsHiddenFiles,.skipsPackageDescendants])
                var totalSize: UInt64 = 0
                while let url = enumerator?.nextObject() as? URL {
                    do {
                        let resourceValues = try url.resourceValues(forKeys: [.fileSizeKey])
                        if let fileSize = resourceValues.fileSize {
                            totalSize += UInt64(fileSize)
                        }
                    } catch {
                        
                    }
                }
                return totalSize
            } catch {
                return 0
            }
        }
        
        let cachesSize = sizeOfCachesFolder()
        if cachesSize == 0 {
            return "0 KB"
        }
        
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useKB,.useMB]
        formatter.countStyle = .file
        let fileSizeNumber = NSNumber(value: Int64(cachesSize))
        let fileSizeMeasurement = Measurement<UnitInformationStorage>(value: Double(fileSizeNumber.intValue), unit:.bytes)
        let sizeString = formatter.string(from: fileSizeMeasurement)
        return sizeString
    }
    
    private func setupLogoutButton() {
        logoutButton.layer.borderColor = UIColor.mainTextColor.cgColor
        logoutButton.layer.borderWidth = 1
        logoutButton.layer.cornerRadius = 4
    }
    
    @IBAction func editEmailAction(_ sender: Any) {
        
        guard UserDefaults.standard.isLoginAsGuestWithoutAccount == false else {
            let vc = CreateAccountPopupViewController()
            vc.delegate = self
            vc.modalPresentationStyle = .overFullScreen
            self.navigationController?.present(vc, animated: true)
            return
        }
        
        let boundVC = BoundViewController(.email)
        hidesBottomBarWhenPushed = true
        navigationController?.pushViewController(boundVC, animated: true)
        hidesBottomBarWhenPushed = true
    }
    
    @IBAction func editMobileAction(_ sender: Any) {
        
        guard UserDefaults.standard.isLoginAsGuestWithoutAccount == false else {
            let vc = CreateAccountPopupViewController()
            vc.delegate = self
            vc.modalPresentationStyle = .overFullScreen
            self.navigationController?.present(vc, animated: true)
            return
        }
        
        let boundVC = BoundViewController(.mobile)
        hidesBottomBarWhenPushed = true
        navigationController?.pushViewController(boundVC, animated: true)
        hidesBottomBarWhenPushed = true
    }
    
    @IBAction func contectAppleIDAction(_ sender: Any) {
        
        guard UserDefaults.standard.isLoginAsGuestWithoutAccount == false else {
            let vc = CreateAccountPopupViewController()
            vc.delegate = self
            vc.modalPresentationStyle = .overFullScreen
            self.navigationController?.present(vc, animated: true)
            return
        }
        
        if let appleA = Interface.shared().loggedInUser?.userInfo.appleAccount, appleA != "***", !appleA.isEmpty {
            let popup = UnbingPopupViewController(.unBoundAppleId(appleId: appleA))
            popup.modalPresentationStyle = .overFullScreen
            self.navigationController?.pushViewController(popup, animated: false)
        } else {
            
            let nonce = randomNonceString()
            currentNonce = nonce
            let appleIDProvider = ASAuthorizationAppleIDProvider()
            let request = appleIDProvider.createRequest()
            request.requestedScopes = [.fullName, .email]
            request.nonce = sha256(nonce)
            
            let authorizationController = ASAuthorizationController(authorizationRequests: [request])
            
            authorizationController.delegate = self
            authorizationController.presentationContextProvider = self
            
            authorizationController.performRequests()
        }
    }
    
    @IBAction func connectFacebookAction(_ sender: Any) {
        
        guard UserDefaults.standard.isLoginAsGuestWithoutAccount == false else {
            let vc = CreateAccountPopupViewController()
            vc.delegate = self
            vc.modalPresentationStyle = .overFullScreen
            self.navigationController?.present(vc, animated: true)
            return
        }
        
        if let facebook = Interface.shared().loggedInUser?.userInfo.facebookAccount, facebook != "" {
            let popup = UnbingPopupViewController(.unBoundFacebook(facebook: facebook))
            popup.modalPresentationStyle = .overFullScreen
            self.navigationController?.pushViewController(popup, animated: false)
        } else {
            // MARK: TODO: bind facebook
        }
    }
    
    @IBAction func connectGoogleAction(_ sender: Any) {
        
        guard UserDefaults.standard.isLoginAsGuestWithoutAccount == false else {
            let vc = CreateAccountPopupViewController()
            vc.delegate = self
            vc.modalPresentationStyle = .overFullScreen
            self.navigationController?.present(vc, animated: true)
            return
        }
        
        if let googleA = Interface.shared().loggedInUser?.userInfo.googleAccount, googleA != "" {
            let popup = UnbingPopupViewController(.unBoundGoogle(google: googleA))
            popup.modalPresentationStyle = .overFullScreen
            self.navigationController?.pushViewController(popup, animated: false)
        } else {
            
            guard let clientID = FirebaseApp.app()?.options.clientID else { return }
            
            let config = GIDConfiguration(clientID: clientID)
            GIDSignIn.sharedInstance.configuration = config
            
            GIDSignIn.sharedInstance.signIn(withPresenting: self) { [unowned self] result, error in
                guard error == nil else {
                    return
                }
                guard let user = result?.user,
                      let idToken = user.idToken?.tokenString else {
                    showToachMessage(message: "Bind with Google fail")
                    return
                }
                let credential = GoogleAuthProvider.credential(withIDToken: idToken,
                                                               accessToken: user.accessToken.tokenString)
                Auth.auth().signIn(with: credential) { result, error in
                    guard let user = result?.user, let email = user.email else {
                        showToachMessage(message: "Bind with Google fail")
                        return
                    }
                    self.bindAccountViewModel.bindAccount(account: email, grantType: "google", idToken: UserDefaults.standard.userToken, uid: user.uid) { result in
                        guard let token = result else {
                            return
                        }
                        hl_fetchUserInfo({
                            self.setupUI()
                        })
                    } failure: { error in
                        
                    }
                }
            }
        }
    }
    
    @IBAction func termsOfConditionsAction(_ sender: Any) {
        AppInfoInteractor.fetchAppInfo(3) { result in
            let webView = WebViewController(contentString: result?.content)
            webView.title = "Terms of Conditions"
            webView.isTransparent = true
            self.navigationController?.pushViewController(webView, animated: true)
        }
    }
    
    
    @IBAction func privatePolicyAction(_ sender: Any) {
        AppInfoInteractor.fetchAppInfo(2) { result in
            let webView = WebViewController(contentString: result?.content)
            webView.title = "Privacy Policy"
            webView.isTransparent = true
            self.navigationController?.pushViewController(webView, animated: true)
        }
    }
    
    @IBAction func clearCacheAction(_ sender: Any) {
        func clearCaches() {
            let fileManager = FileManager.default
            guard let cachesURL = fileManager.urls(for:.cachesDirectory, in:.userDomainMask).first else {
                return
            }
            do {
                let contents = try fileManager.contentsOfDirectory(at: cachesURL, includingPropertiesForKeys: nil, options: [])
                for file in contents {
                    try fileManager.removeItem(at: file)
                }
            } catch {
                
            }
        }
        
        clearCaches()
        
        showToachMessage(message: "Clear Success")
        cacheNum.setTitle("0 KB", for: .normal)
    }
    
    
    @IBAction func changePasswordAction(_ sender: Any) {
        let changePasswordVC = ForgotPassWordViewController()//ChangePasswordViewController()
        changePasswordVC.title = "Change Password"
        hidesBottomBarWhenPushed = true
        navigationController?.pushViewController(changePasswordVC, animated: true)
        hidesBottomBarWhenPushed = true
    }
    
    @IBAction func checkVersionAction(_ sender: Any) {
        
        //        checkVersionViewLabel.text = "The current version is the latest version V\(getVersionNum() ?? "")"
        //        checkVersionViewLabel.setLineHeight(8)
        //        checkVersionView.isHidden = !checkVersionView.isHidden
        //
        //        if !checkVersionView.isHidden {
        //            DispatchQueue.main.asyncAfter(deadline: .now() + 2, execute: {
        //                self.checkVersionView.isHidden = true
        //            })
        //        }
        
        
        let alert = UIAlertController(title: "Current Version", message: getVersionNum(), preferredStyle:.alert)
        let confirmAction = UIAlertAction(title: "OK", style: .default) { _ in
        }
        alert.addAction(confirmAction)
        present(alert, animated: true, completion: nil)
    }
    
    @IBAction func deleteAccountAction(_ sender: Any) {
        let changePasswordVC = DeleteAccountViewController()
        changePasswordVC.modalPresentationStyle = .overFullScreen
        present(changePasswordVC, animated: true)
    }
    
    @IBAction func logoutAction(_ sender: Any) {
        UserInteractor.userLogout { result in
            guard let _ = result else { return }
            
            UserDefaults.standard.userToken = nil
            currentWindow?.rootViewController = UINavigationController(rootViewController: LoginViewController())
        }
    }
    
    private func randomNonceString(length: Int = 32) -> String {
        precondition(length > 0)
        var randomBytes = [UInt8](repeating: 0, count: length)
        let errorCode = SecRandomCopyBytes(kSecRandomDefault, randomBytes.count, &randomBytes)
        if errorCode != errSecSuccess {
            fatalError(
                "Unable to generate nonce. SecRandomCopyBytes failed with OSStatus \(errorCode)"
            )
        }
        
        let charset: [Character] =
        Array("0123456789ABCDEFGHIJKLMNOPQRSTUVXYZabcdefghijklmnopqrstuvwxyz-._")
        
        let nonce = randomBytes.map { byte in
            // Pick a random character from the set, wrapping around if needed.
            charset[Int(byte) % charset.count]
        }
        
        return String(nonce)
    }
    
    private func sha256(_ input: String) -> String {
        let inputData = Data(input.utf8)
        let hashedData = SHA256.hash(data: inputData)
        let hashString = hashedData.compactMap {
            String(format: "%02x", $0)
        }.joined()
        
        return hashString
    }
}

extension SettingViewController:ASAuthorizationControllerDelegate, ASAuthorizationControllerPresentationContextProviding {
    
    func authorizationController(controller: ASAuthorizationController, didCompleteWithAuthorization authorization: ASAuthorization) {
        
        if let appleIDCredential = authorization.credential as? ASAuthorizationAppleIDCredential {
            guard let nonce = currentNonce else {
                fatalError("Invalid state: A login callback was received, but no login request was sent.")
            }
            guard let appleIDToken = appleIDCredential.identityToken else {
                print("Unable to fetch identity token")
                return
            }
            guard let idTokenString = String(data: appleIDToken, encoding: .utf8) else {
                print("Unable to serialize token string from data: \(appleIDToken.debugDescription)")
                return
            }
            // Initialize a Firebase credential, including the user's full name.
            let credential = OAuthProvider.appleCredential(withIDToken: idTokenString,
                                                           rawNonce: nonce,
                                                           fullName: appleIDCredential.fullName)
            // Sign in with Firebase.
            Auth.auth().signIn(with: credential) { (authResult, error) in
                if (error != nil) {
                    // Error. If error.code == .MissingOrInvalidNonce, make sure
                    // you're sending the SHA256-hashed nonce as a hex string with
                    // your request to Apple.
                    print(error?.localizedDescription ?? "")
                    showToachMessage(message: "Bind Apple account fail")
                    return
                }
                guard let user = authResult?.user, let email = user.email else {
                    showToachMessage(message: "Bind Apple account fail")
                    return
                }
                
                self.bindAccountViewModel.bindAccount(account: email, grantType: "apple", idToken: UserDefaults.standard.userToken, uid: user.uid) { result in
                    guard let token = result else {
                        //                        self.errorLabelShow()
                        return
                    }
                    hl_fetchUserInfo({
                        self.setupUI()
                    })
                } failure: { error in
                    print("")
                }
                
            }
        }
        
        //        switch authorization.credential {
        //        case let appleIDCredential as ASAuthorizationAppleIDCredential:
        //            let identityToken = appleIDCredential.identityToken!
        //            if let identifierStr = String.init(data: identityToken, encoding: .utf8) {
        //                let userM = Interface.shared().loggedInUser?.userInfo
        //                self.bindAccountViewModel.bindAccount(account: nil, grantType: "apple", idToken: UserDefaults.standard.userToken, uid: identifierStr) { result in
        //
        //                } failure: { error in
        //
        //                }
        //            }
        //        default:
        //            break
        //        }
        
    }
    
    func authorizationController(controller: ASAuthorizationController, didCompleteWithError error: Error) {
        // Handle error.
    }
    
    /// MARK: ASAuthorizationControllerPresentationContextProviding
    func presentationAnchor(for controller: ASAuthorizationController) -> ASPresentationAnchor {
        return self.view.window!
    }
}

extension SettingViewController : CreateAccountPopupViewControllerDelegate {
    func didTapClosePopup() {
        
    }
    
    func didTapCreateAccount() {
        let signUpVC = SignUpViewController()
        navigationController?.pushViewController(signUpVC, animated: true)
    }
}
