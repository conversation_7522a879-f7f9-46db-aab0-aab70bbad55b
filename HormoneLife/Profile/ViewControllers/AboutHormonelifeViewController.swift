//
//  AboutHormonelifeViewController.swift
//  HormoneLife
//
//  Created by Tank on 2024/6/9.
//

import UIKit
import WebKit

class AboutHormonelifeViewController: BaseViewController {

    @IBOutlet weak var webView: WKWebView!
    var urlString: String?
    
    let headerString = "<header><meta name='viewport' content='width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0,background-color: transparent;,user-scalable=no'><style>img{max-width:100%}</style></header>";
    var content: String?
    
    init(_ urlString: String? = nil, contentString: String? = nil) {
        self.urlString = urlString
        self.content = contentString
        super.init(nibName: "AboutHormonelifeViewController", bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        view.backgroundColor = .themeColor
        self.webView.isOpaque = false
        
        self.webView.scrollView.showsVerticalScrollIndicator = false
//        self.webView.scrollView.contentInset = .init(top: 100, left: 0, bottom: 0, right: 0)
        
        self.webView.backgroundColor = .clear
        guard let urlStr = urlString, let url = URL(string: urlStr) else {
            refresh(content: content)
            return
        }
        webView.load(URLRequest(url: url))
    }

    func refresh(content: String?) {
        if let content = content {
            self.webView.loadHTMLString("\(headerString)\(content)", baseURL: nil)
        }
    }

}
