<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="21701" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21679"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="gilroy regular.otf">
            <string>Gilroy-Regular</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="SettingViewController" customModule="HormoneLife" customModuleProvider="target">
            <connections>
                <outlet property="appleIDNum" destination="6Az-RR-NLZ" id="DCZ-E8-bg1"/>
                <outlet property="cacheNum" destination="0kF-bW-ld7" id="QVb-aB-ecC"/>
                <outlet property="checkVersionView" destination="I43-nQ-trQ" id="OWt-6E-aIJ"/>
                <outlet property="checkVersionViewLabel" destination="rD9-Xl-MqU" id="5Jv-5U-bXg"/>
                <outlet property="emailAddress" destination="hJc-Lf-oPS" id="K1a-xJ-m4s"/>
                <outlet property="facebookNum" destination="6sG-tz-anF" id="LQh-sj-t0X"/>
                <outlet property="googleIDNum" destination="Zgb-Yw-nC0" id="YtR-0L-PwH"/>
                <outlet property="logoutButton" destination="f1S-FB-v0N" id="665-eK-xhN"/>
                <outlet property="mobileNum" destination="pFI-Is-itI" id="Sxi-IU-LJ4"/>
                <outlet property="versionLabel" destination="fVw-fh-jFe" id="qT2-gu-77T"/>
                <outlet property="view" destination="i5M-Pr-FkT" id="u4P-Dg-lmu"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" ambiguous="YES" id="i5M-Pr-FkT">
            <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" ambiguous="YES" translatesAutoresizingMaskIntoConstraints="NO" id="pgh-30-Wdu">
                    <rect key="frame" x="0.0" y="59" width="393" height="793"/>
                    <subviews>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="k2o-7T-wGT">
                            <rect key="frame" x="0.0" y="0.0" width="393" height="680"/>
                            <subviews>
                                <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="Kwy-DA-EnW">
                                    <rect key="frame" x="0.0" y="0.0" width="393" height="680"/>
                                    <subviews>
                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="tRx-Lb-weM">
                                            <rect key="frame" x="0.0" y="0.0" width="393" height="250"/>
                                            <subviews>
                                                <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="fillEqually" translatesAutoresizingMaskIntoConstraints="NO" id="VGG-CP-Zay">
                                                    <rect key="frame" x="24" y="0.0" width="345" height="250"/>
                                                    <subviews>
                                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Ive-05-MUy">
                                                            <rect key="frame" x="0.0" y="0.0" width="345" height="50"/>
                                                            <subviews>
                                                                <stackView opaque="NO" contentMode="scaleToFill" spacing="6" translatesAutoresizingMaskIntoConstraints="NO" id="atj-RE-waj">
                                                                    <rect key="frame" x="0.0" y="0.0" width="345" height="49"/>
                                                                    <subviews>
                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Email" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="3ZX-mQ-B7F">
                                                                            <rect key="frame" x="0.0" y="0.0" width="39" height="49"/>
                                                                            <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="16"/>
                                                                            <color key="textColor" red="0.*****************" green="0.075626514850000007" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                            <nil key="highlightedColor"/>
                                                                        </label>
                                                                        <button opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="249" contentHorizontalAlignment="right" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="hJc-Lf-oPS">
                                                                            <rect key="frame" x="45" y="0.0" width="278" height="49"/>
                                                                            <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="16"/>
                                                                            <state key="normal" title="Not connected">
                                                                                <color key="titleColor" red="0.21396151229999999" green="0.2160799431" blue="0.2160799431" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                            </state>
                                                                            <connections>
                                                                                <action selector="editEmailAction:" destination="-1" eventType="touchUpInside" id="IT9-38-cpK"/>
                                                                            </connections>
                                                                        </button>
                                                                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="right" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="W5k-mO-wHI">
                                                                            <rect key="frame" x="329" y="0.0" width="16" height="49"/>
                                                                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                                            <state key="normal" image="forwarIcon"/>
                                                                            <connections>
                                                                                <action selector="editEmailAction:" destination="-1" eventType="touchUpInside" id="5f2-4L-neh"/>
                                                                            </connections>
                                                                        </button>
                                                                    </subviews>
                                                                </stackView>
                                                                <view alpha="0.65000000000000002" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="jbf-m9-Edb">
                                                                    <rect key="frame" x="0.0" y="49" width="345" height="1"/>
                                                                    <color key="backgroundColor" red="0.*****************" green="0.**********" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                    <constraints>
                                                                        <constraint firstAttribute="height" constant="1" id="g3c-Ru-iA8"/>
                                                                        <constraint firstAttribute="height" relation="greaterThanOrEqual" constant="1" id="slj-wS-fXo"/>
                                                                    </constraints>
                                                                </view>
                                                            </subviews>
                                                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                            <constraints>
                                                                <constraint firstAttribute="bottom" secondItem="atj-RE-waj" secondAttribute="bottom" constant="1" id="0f9-94-bdc"/>
                                                                <constraint firstItem="atj-RE-waj" firstAttribute="leading" secondItem="Ive-05-MUy" secondAttribute="leading" id="P6P-Le-tco"/>
                                                                <constraint firstAttribute="trailing" secondItem="jbf-m9-Edb" secondAttribute="trailing" id="StC-Bf-uTO"/>
                                                                <constraint firstItem="jbf-m9-Edb" firstAttribute="top" secondItem="atj-RE-waj" secondAttribute="bottom" id="bhC-dl-tUa"/>
                                                                <constraint firstAttribute="trailing" secondItem="atj-RE-waj" secondAttribute="trailing" id="bzT-T6-tmI"/>
                                                                <constraint firstItem="atj-RE-waj" firstAttribute="top" secondItem="Ive-05-MUy" secondAttribute="top" id="ipH-jq-8V8"/>
                                                                <constraint firstItem="jbf-m9-Edb" firstAttribute="leading" secondItem="Ive-05-MUy" secondAttribute="leading" id="mqt-oi-Krb"/>
                                                                <constraint firstAttribute="bottom" secondItem="jbf-m9-Edb" secondAttribute="bottom" id="vnN-fI-mjk"/>
                                                            </constraints>
                                                        </view>
                                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="kpB-Dt-vAn">
                                                            <rect key="frame" x="0.0" y="50" width="345" height="50"/>
                                                            <subviews>
                                                                <stackView opaque="NO" contentMode="scaleToFill" spacing="6" translatesAutoresizingMaskIntoConstraints="NO" id="xrm-7l-sDf">
                                                                    <rect key="frame" x="0.0" y="0.0" width="345" height="49"/>
                                                                    <subviews>
                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Mobile" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="eSt-bh-Ozd">
                                                                            <rect key="frame" x="0.0" y="0.0" width="48.666666666666664" height="49"/>
                                                                            <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="16"/>
                                                                            <color key="textColor" red="0.*****************" green="0.075626514850000007" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                            <nil key="highlightedColor"/>
                                                                        </label>
                                                                        <button opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="249" contentHorizontalAlignment="right" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="pFI-Is-itI">
                                                                            <rect key="frame" x="54.666666666666657" y="0.0" width="268.************37" height="49"/>
                                                                            <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="16"/>
                                                                            <state key="normal" title="Not connected">
                                                                                <color key="titleColor" red="0.21396151229999999" green="0.2160799431" blue="0.2160799431" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                            </state>
                                                                            <connections>
                                                                                <action selector="editMobileAction:" destination="-1" eventType="touchUpInside" id="LU1-L7-IoX"/>
                                                                            </connections>
                                                                        </button>
                                                                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="right" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="uEF-tT-ORc">
                                                                            <rect key="frame" x="329" y="0.0" width="16" height="49"/>
                                                                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                                            <state key="normal" image="forwarIcon"/>
                                                                            <connections>
                                                                                <action selector="editMobileAction:" destination="-1" eventType="touchUpInside" id="82x-tz-aIj"/>
                                                                            </connections>
                                                                        </button>
                                                                    </subviews>
                                                                </stackView>
                                                                <view alpha="0.*****************" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="5WW-jO-6Lo">
                                                                    <rect key="frame" x="0.0" y="49" width="345" height="1"/>
                                                                    <color key="backgroundColor" red="0.*****************" green="0.**********" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                    <constraints>
                                                                        <constraint firstAttribute="height" relation="greaterThanOrEqual" constant="1" id="5Kx-MN-QZY"/>
                                                                        <constraint firstAttribute="height" constant="1" id="kbT-hb-x9O"/>
                                                                    </constraints>
                                                                </view>
                                                            </subviews>
                                                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                            <constraints>
                                                                <constraint firstItem="5WW-jO-6Lo" firstAttribute="leading" secondItem="kpB-Dt-vAn" secondAttribute="leading" id="BL5-3I-Q9S"/>
                                                                <constraint firstItem="xrm-7l-sDf" firstAttribute="top" secondItem="kpB-Dt-vAn" secondAttribute="top" id="DNx-cj-sa1"/>
                                                                <constraint firstAttribute="bottom" secondItem="xrm-7l-sDf" secondAttribute="bottom" constant="1" id="IZv-eF-qN9"/>
                                                                <constraint firstAttribute="trailing" secondItem="5WW-jO-6Lo" secondAttribute="trailing" id="SAF-cC-EvJ"/>
                                                                <constraint firstAttribute="bottom" secondItem="5WW-jO-6Lo" secondAttribute="bottom" id="VBv-cO-7nE"/>
                                                                <constraint firstItem="xrm-7l-sDf" firstAttribute="leading" secondItem="kpB-Dt-vAn" secondAttribute="leading" id="lyn-A8-qOO"/>
                                                                <constraint firstItem="5WW-jO-6Lo" firstAttribute="top" secondItem="xrm-7l-sDf" secondAttribute="bottom" id="pfp-tl-6x3"/>
                                                                <constraint firstAttribute="trailing" secondItem="xrm-7l-sDf" secondAttribute="trailing" id="uaK-zT-qNO"/>
                                                            </constraints>
                                                        </view>
                                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="nB6-VT-1xu">
                                                            <rect key="frame" x="0.0" y="100" width="345" height="50"/>
                                                            <subviews>
                                                                <stackView opaque="NO" contentMode="scaleToFill" spacing="6" translatesAutoresizingMaskIntoConstraints="NO" id="0q9-rN-s8R">
                                                                    <rect key="frame" x="0.0" y="0.0" width="345" height="49"/>
                                                                    <subviews>
                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Apple ID" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Dqn-Nx-L5A">
                                                                            <rect key="frame" x="0.0" y="0.0" width="62.************336" height="49"/>
                                                                            <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="16"/>
                                                                            <color key="textColor" red="0.*****************" green="0.075626514850000007" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                            <nil key="highlightedColor"/>
                                                                        </label>
                                                                        <button opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="249" contentHorizontalAlignment="right" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="6Az-RR-NLZ">
                                                                            <rect key="frame" x="68.************329" y="0.0" width="254.66666666666669" height="49"/>
                                                                            <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="16"/>
                                                                            <state key="normal" title="Not connected">
                                                                                <color key="titleColor" red="0.21396151229999999" green="0.2160799431" blue="0.2160799431" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                            </state>
                                                                            <connections>
                                                                                <action selector="contectAppleIDAction:" destination="-1" eventType="touchUpInside" id="dHd-4I-Gnb"/>
                                                                            </connections>
                                                                        </button>
                                                                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="right" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Kaq-1B-AwO">
                                                                            <rect key="frame" x="329" y="0.0" width="16" height="49"/>
                                                                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                                            <state key="normal" image="forwarIcon"/>
                                                                            <connections>
                                                                                <action selector="contectAppleIDAction:" destination="-1" eventType="touchUpInside" id="msD-Mq-JHR"/>
                                                                            </connections>
                                                                        </button>
                                                                    </subviews>
                                                                </stackView>
                                                                <view alpha="0.*****************" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="xFN-Us-ZuS">
                                                                    <rect key="frame" x="0.0" y="49" width="345" height="1"/>
                                                                    <color key="backgroundColor" red="0.*****************" green="0.**********" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                    <constraints>
                                                                        <constraint firstAttribute="height" relation="greaterThanOrEqual" constant="1" id="185-pt-R5n"/>
                                                                        <constraint firstAttribute="height" constant="1" id="saF-nh-DY5"/>
                                                                    </constraints>
                                                                </view>
                                                            </subviews>
                                                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                            <constraints>
                                                                <constraint firstAttribute="trailing" secondItem="xFN-Us-ZuS" secondAttribute="trailing" id="EZC-6w-eNx"/>
                                                                <constraint firstItem="0q9-rN-s8R" firstAttribute="top" secondItem="nB6-VT-1xu" secondAttribute="top" id="Grb-QN-Kc0"/>
                                                                <constraint firstAttribute="trailing" secondItem="0q9-rN-s8R" secondAttribute="trailing" id="LzJ-nw-hMM"/>
                                                                <constraint firstAttribute="bottom" secondItem="xFN-Us-ZuS" secondAttribute="bottom" id="OmK-L0-Jld"/>
                                                                <constraint firstItem="0q9-rN-s8R" firstAttribute="leading" secondItem="nB6-VT-1xu" secondAttribute="leading" id="Ufw-Ms-EKJ"/>
                                                                <constraint firstItem="xFN-Us-ZuS" firstAttribute="top" secondItem="0q9-rN-s8R" secondAttribute="bottom" id="dvo-1R-arn"/>
                                                                <constraint firstItem="xFN-Us-ZuS" firstAttribute="leading" secondItem="nB6-VT-1xu" secondAttribute="leading" id="jT9-B8-f23"/>
                                                                <constraint firstAttribute="bottom" secondItem="0q9-rN-s8R" secondAttribute="bottom" constant="1" id="zHd-pi-vy1"/>
                                                            </constraints>
                                                        </view>
                                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="AD8-aJ-qDh">
                                                            <rect key="frame" x="0.0" y="150" width="345" height="50"/>
                                                            <subviews>
                                                                <stackView opaque="NO" contentMode="scaleToFill" spacing="6" translatesAutoresizingMaskIntoConstraints="NO" id="J2G-u3-Xaz">
                                                                    <rect key="frame" x="0.0" y="0.0" width="345" height="49"/>
                                                                    <subviews>
                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Facebook" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="IZv-qZ-aP3">
                                                                            <rect key="frame" x="0.0" y="0.0" width="74" height="49"/>
                                                                            <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="16"/>
                                                                            <color key="textColor" red="0.*****************" green="0.075626514850000007" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                            <nil key="highlightedColor"/>
                                                                        </label>
                                                                        <button opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="249" contentHorizontalAlignment="right" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="6sG-tz-anF">
                                                                            <rect key="frame" x="80" y="0.0" width="243" height="49"/>
                                                                            <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="16"/>
                                                                            <state key="normal" title="Not connected">
                                                                                <color key="titleColor" red="0.21396151229999999" green="0.2160799431" blue="0.2160799431" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                            </state>
                                                                            <connections>
                                                                                <action selector="connectFacebookAction:" destination="-1" eventType="touchUpInside" id="2b8-IS-3cg"/>
                                                                            </connections>
                                                                        </button>
                                                                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="right" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Snb-cY-RBo">
                                                                            <rect key="frame" x="329" y="0.0" width="16" height="49"/>
                                                                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                                            <state key="normal" image="forwarIcon"/>
                                                                            <connections>
                                                                                <action selector="connectFacebookAction:" destination="-1" eventType="touchUpInside" id="ooc-Op-Jkc"/>
                                                                            </connections>
                                                                        </button>
                                                                    </subviews>
                                                                </stackView>
                                                                <view alpha="0.*****************" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="hRN-WX-RIT">
                                                                    <rect key="frame" x="0.0" y="49" width="345" height="1"/>
                                                                    <color key="backgroundColor" red="0.*****************" green="0.**********" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                    <constraints>
                                                                        <constraint firstAttribute="height" constant="1" id="63O-9Q-Xy0"/>
                                                                        <constraint firstAttribute="height" relation="greaterThanOrEqual" constant="1" id="Cnj-4J-2gj"/>
                                                                    </constraints>
                                                                </view>
                                                            </subviews>
                                                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                            <constraints>
                                                                <constraint firstAttribute="trailing" secondItem="J2G-u3-Xaz" secondAttribute="trailing" id="8jE-zV-FkL"/>
                                                                <constraint firstAttribute="trailing" secondItem="hRN-WX-RIT" secondAttribute="trailing" id="KQ0-qo-rUn"/>
                                                                <constraint firstItem="hRN-WX-RIT" firstAttribute="leading" secondItem="AD8-aJ-qDh" secondAttribute="leading" id="PCb-dA-aFb"/>
                                                                <constraint firstAttribute="bottom" secondItem="hRN-WX-RIT" secondAttribute="bottom" id="XwR-Ej-kZn"/>
                                                                <constraint firstItem="hRN-WX-RIT" firstAttribute="top" secondItem="J2G-u3-Xaz" secondAttribute="bottom" id="Y9b-Xp-fut"/>
                                                                <constraint firstItem="J2G-u3-Xaz" firstAttribute="top" secondItem="AD8-aJ-qDh" secondAttribute="top" id="grb-f2-71W"/>
                                                                <constraint firstItem="J2G-u3-Xaz" firstAttribute="leading" secondItem="AD8-aJ-qDh" secondAttribute="leading" id="kN4-RU-7jz"/>
                                                                <constraint firstAttribute="bottom" secondItem="J2G-u3-Xaz" secondAttribute="bottom" constant="1" id="tm7-dj-Trf"/>
                                                            </constraints>
                                                        </view>
                                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="HYp-s1-wOQ">
                                                            <rect key="frame" x="0.0" y="200" width="345" height="50"/>
                                                            <subviews>
                                                                <stackView opaque="NO" contentMode="scaleToFill" spacing="6" translatesAutoresizingMaskIntoConstraints="NO" id="c7E-gF-ouj">
                                                                    <rect key="frame" x="0.0" y="0.0" width="345" height="49"/>
                                                                    <subviews>
                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Google" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="dIi-Ii-ZyU">
                                                                            <rect key="frame" x="0.0" y="0.0" width="55" height="49"/>
                                                                            <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="16"/>
                                                                            <color key="textColor" red="0.*****************" green="0.075626514850000007" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                            <nil key="highlightedColor"/>
                                                                        </label>
                                                                        <button opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="249" contentHorizontalAlignment="right" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Zgb-Yw-nC0">
                                                                            <rect key="frame" x="61" y="0.0" width="262" height="49"/>
                                                                            <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="16"/>
                                                                            <state key="normal" title="Not connected">
                                                                                <color key="titleColor" red="0.21396151229999999" green="0.2160799431" blue="0.2160799431" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                            </state>
                                                                            <connections>
                                                                                <action selector="connectGoogleAction:" destination="-1" eventType="touchUpInside" id="Uk9-zI-SsQ"/>
                                                                            </connections>
                                                                        </button>
                                                                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="right" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="wpr-zt-dy1">
                                                                            <rect key="frame" x="329" y="0.0" width="16" height="49"/>
                                                                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                                            <state key="normal" image="forwarIcon"/>
                                                                            <connections>
                                                                                <action selector="connectGoogleAction:" destination="-1" eventType="touchUpInside" id="wc6-cE-cNN"/>
                                                                            </connections>
                                                                        </button>
                                                                    </subviews>
                                                                </stackView>
                                                                <view alpha="0.*****************" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="mex-cz-AT8">
                                                                    <rect key="frame" x="0.0" y="49" width="345" height="1"/>
                                                                    <color key="backgroundColor" red="0.*****************" green="0.**********" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                    <constraints>
                                                                        <constraint firstAttribute="height" constant="1" id="pRv-hh-5I5"/>
                                                                        <constraint firstAttribute="height" relation="greaterThanOrEqual" constant="1" id="yH7-eD-ADM"/>
                                                                    </constraints>
                                                                </view>
                                                            </subviews>
                                                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                            <constraints>
                                                                <constraint firstItem="c7E-gF-ouj" firstAttribute="top" secondItem="HYp-s1-wOQ" secondAttribute="top" id="Bs1-0V-aFp"/>
                                                                <constraint firstAttribute="bottom" secondItem="mex-cz-AT8" secondAttribute="bottom" id="U8N-wU-IB5"/>
                                                                <constraint firstAttribute="trailing" secondItem="mex-cz-AT8" secondAttribute="trailing" id="YMH-L1-Blf"/>
                                                                <constraint firstAttribute="trailing" secondItem="c7E-gF-ouj" secondAttribute="trailing" id="cm9-pn-5iZ"/>
                                                                <constraint firstItem="c7E-gF-ouj" firstAttribute="leading" secondItem="HYp-s1-wOQ" secondAttribute="leading" id="hBl-LN-iRz"/>
                                                                <constraint firstItem="mex-cz-AT8" firstAttribute="top" secondItem="c7E-gF-ouj" secondAttribute="bottom" id="rbh-eU-zs8"/>
                                                                <constraint firstAttribute="bottom" secondItem="c7E-gF-ouj" secondAttribute="bottom" constant="1" id="tRb-Gh-z4g"/>
                                                                <constraint firstItem="mex-cz-AT8" firstAttribute="leading" secondItem="HYp-s1-wOQ" secondAttribute="leading" id="tcP-MI-ca5"/>
                                                            </constraints>
                                                        </view>
                                                    </subviews>
                                                </stackView>
                                            </subviews>
                                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <constraints>
                                                <constraint firstAttribute="height" constant="250" id="4ww-km-oR2"/>
                                                <constraint firstItem="VGG-CP-Zay" firstAttribute="leading" secondItem="tRx-Lb-weM" secondAttribute="leading" constant="24" id="CWA-cv-2CJ"/>
                                                <constraint firstAttribute="bottom" secondItem="VGG-CP-Zay" secondAttribute="bottom" id="Zt8-QQ-09p"/>
                                                <constraint firstItem="VGG-CP-Zay" firstAttribute="top" secondItem="tRx-Lb-weM" secondAttribute="top" id="p93-mU-D8K"/>
                                                <constraint firstAttribute="trailing" secondItem="VGG-CP-Zay" secondAttribute="trailing" constant="24" id="vAs-3P-wXc"/>
                                            </constraints>
                                        </view>
                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="kFV-UE-3Up">
                                            <rect key="frame" x="0.0" y="262" width="393" height="232"/>
                                            <subviews>
                                                <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="fillEqually" translatesAutoresizingMaskIntoConstraints="NO" id="VTo-97-pkm">
                                                    <rect key="frame" x="24" y="0.0" width="345" height="232"/>
                                                    <subviews>
                                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="lKe-Fq-Zb6">
                                                            <rect key="frame" x="0.0" y="0.0" width="345" height="58"/>
                                                            <subviews>
                                                                <stackView opaque="NO" contentMode="scaleToFill" spacing="6" translatesAutoresizingMaskIntoConstraints="NO" id="Zfz-fi-AqX">
                                                                    <rect key="frame" x="0.0" y="0.0" width="345" height="57"/>
                                                                    <subviews>
                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Terms of Conditions" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="HWe-R7-UYF">
                                                                            <rect key="frame" x="0.0" y="0.0" width="144.************34" height="57"/>
                                                                            <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="16"/>
                                                                            <color key="textColor" red="0.*****************" green="0.075626514850000007" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                            <nil key="highlightedColor"/>
                                                                        </label>
                                                                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="right" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="qd4-9p-5zM">
                                                                            <rect key="frame" x="150.************37" y="0.0" width="194.**************" height="57"/>
                                                                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                                            <state key="normal" image="forwarIcon"/>
                                                                            <connections>
                                                                                <action selector="termsOfConditionsAction:" destination="-1" eventType="touchUpInside" id="Mrx-rf-7ds"/>
                                                                            </connections>
                                                                        </button>
                                                                    </subviews>
                                                                </stackView>
                                                                <view alpha="0.64999997615814209" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="JZ5-wD-Aqe">
                                                                    <rect key="frame" x="0.0" y="57" width="345" height="1"/>
                                                                    <color key="backgroundColor" red="0.94169229269999999" green="0.92880314590000002" blue="0.9607291818" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                    <constraints>
                                                                        <constraint firstAttribute="height" relation="greaterThanOrEqual" constant="1" id="MRX-Sd-RFd"/>
                                                                        <constraint firstAttribute="height" constant="1" id="eRz-0C-NST"/>
                                                                    </constraints>
                                                                </view>
                                                            </subviews>
                                                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                            <constraints>
                                                                <constraint firstItem="Zfz-fi-AqX" firstAttribute="top" secondItem="lKe-Fq-Zb6" secondAttribute="top" id="6fp-C2-VQB"/>
                                                                <constraint firstAttribute="trailing" secondItem="Zfz-fi-AqX" secondAttribute="trailing" id="IfP-kI-A5a"/>
                                                                <constraint firstAttribute="trailing" secondItem="JZ5-wD-Aqe" secondAttribute="trailing" id="Zof-cu-O5Q"/>
                                                                <constraint firstAttribute="bottom" secondItem="Zfz-fi-AqX" secondAttribute="bottom" constant="1" id="kn9-a4-lJC"/>
                                                                <constraint firstItem="JZ5-wD-Aqe" firstAttribute="top" secondItem="Zfz-fi-AqX" secondAttribute="bottom" id="kwF-DQ-5ye"/>
                                                                <constraint firstItem="Zfz-fi-AqX" firstAttribute="leading" secondItem="lKe-Fq-Zb6" secondAttribute="leading" id="lpC-Hp-LNS"/>
                                                                <constraint firstItem="JZ5-wD-Aqe" firstAttribute="leading" secondItem="lKe-Fq-Zb6" secondAttribute="leading" id="qS6-GW-XZs"/>
                                                                <constraint firstAttribute="bottom" secondItem="JZ5-wD-Aqe" secondAttribute="bottom" id="t6s-2R-ndx"/>
                                                            </constraints>
                                                        </view>
                                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="22n-nb-8Be">
                                                            <rect key="frame" x="0.0" y="58" width="345" height="58"/>
                                                            <subviews>
                                                                <stackView opaque="NO" contentMode="scaleToFill" spacing="6" translatesAutoresizingMaskIntoConstraints="NO" id="14x-f7-WwF">
                                                                    <rect key="frame" x="0.0" y="0.0" width="345" height="57"/>
                                                                    <subviews>
                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Privacy Policy" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="whu-W8-aKQ">
                                                                            <rect key="frame" x="0.0" y="0.0" width="99" height="57"/>
                                                                            <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="16"/>
                                                                            <color key="textColor" red="0.*****************" green="0.075626514850000007" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                            <nil key="highlightedColor"/>
                                                                        </label>
                                                                        <button opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="249" contentHorizontalAlignment="right" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="PkZ-q9-59w">
                                                                            <rect key="frame" x="105" y="0.0" width="218" height="57"/>
                                                                            <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="16"/>
                                                                            <state key="normal">
                                                                                <color key="titleColor" red="0.21396151229999999" green="0.2160799431" blue="0.2160799431" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                            </state>
                                                                            <connections>
                                                                                <action selector="privatePolicyAction:" destination="-1" eventType="touchUpInside" id="hAk-N4-glV"/>
                                                                            </connections>
                                                                        </button>
                                                                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="right" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="OgC-ek-Zmg">
                                                                            <rect key="frame" x="329" y="0.0" width="16" height="57"/>
                                                                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                                            <state key="normal" image="forwarIcon"/>
                                                                            <connections>
                                                                                <action selector="privatePolicyAction:" destination="-1" eventType="touchUpInside" id="l7a-I8-aKx"/>
                                                                            </connections>
                                                                        </button>
                                                                    </subviews>
                                                                </stackView>
                                                                <view alpha="0.*****************" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="685-jF-7nH">
                                                                    <rect key="frame" x="0.0" y="57" width="345" height="1"/>
                                                                    <color key="backgroundColor" red="0.*****************" green="0.**********" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                    <constraints>
                                                                        <constraint firstAttribute="height" constant="1" id="C8a-V3-JrQ"/>
                                                                        <constraint firstAttribute="height" relation="greaterThanOrEqual" constant="1" id="K7h-mQ-rOl"/>
                                                                    </constraints>
                                                                </view>
                                                            </subviews>
                                                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                            <constraints>
                                                                <constraint firstItem="685-jF-7nH" firstAttribute="top" secondItem="14x-f7-WwF" secondAttribute="bottom" id="6gM-Xj-wgY"/>
                                                                <constraint firstAttribute="trailing" secondItem="685-jF-7nH" secondAttribute="trailing" id="Bqu-vz-V5k"/>
                                                                <constraint firstItem="685-jF-7nH" firstAttribute="leading" secondItem="22n-nb-8Be" secondAttribute="leading" id="QjM-sz-Gna"/>
                                                                <constraint firstItem="14x-f7-WwF" firstAttribute="leading" secondItem="22n-nb-8Be" secondAttribute="leading" id="Rr8-1h-mHr"/>
                                                                <constraint firstAttribute="bottom" secondItem="14x-f7-WwF" secondAttribute="bottom" constant="1" id="Tzf-K0-Koo"/>
                                                                <constraint firstAttribute="bottom" secondItem="685-jF-7nH" secondAttribute="bottom" id="k0p-YW-qRu"/>
                                                                <constraint firstItem="14x-f7-WwF" firstAttribute="top" secondItem="22n-nb-8Be" secondAttribute="top" id="p7j-gj-Oc0"/>
                                                                <constraint firstAttribute="trailing" secondItem="14x-f7-WwF" secondAttribute="trailing" id="xpd-jg-BM6"/>
                                                            </constraints>
                                                        </view>
                                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="laE-40-caK">
                                                            <rect key="frame" x="0.0" y="116" width="345" height="58"/>
                                                            <subviews>
                                                                <stackView opaque="NO" contentMode="scaleToFill" spacing="6" translatesAutoresizingMaskIntoConstraints="NO" id="cpr-bJ-lbi">
                                                                    <rect key="frame" x="0.0" y="0.0" width="345" height="57"/>
                                                                    <subviews>
                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Clear Cache" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="8oa-RJ-2IA">
                                                                            <rect key="frame" x="0.0" y="0.0" width="93" height="57"/>
                                                                            <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="16"/>
                                                                            <color key="textColor" red="0.*****************" green="0.075626514850000007" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                            <nil key="highlightedColor"/>
                                                                        </label>
                                                                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="right" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="0kF-bW-ld7">
                                                                            <rect key="frame" x="99" y="0.0" width="246" height="57"/>
                                                                            <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="16"/>
                                                                            <state key="normal" title="208 M">
                                                                                <color key="titleColor" red="0.21396151229999999" green="0.2160799431" blue="0.2160799431" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                            </state>
                                                                            <connections>
                                                                                <action selector="clearCacheAction:" destination="-1" eventType="touchUpInside" id="R3e-Bu-Jps"/>
                                                                            </connections>
                                                                        </button>
                                                                    </subviews>
                                                                </stackView>
                                                                <view alpha="0.*****************" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="O6E-2A-DiB">
                                                                    <rect key="frame" x="0.0" y="57" width="345" height="1"/>
                                                                    <color key="backgroundColor" red="0.*****************" green="0.**********" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                    <constraints>
                                                                        <constraint firstAttribute="height" relation="greaterThanOrEqual" constant="1" id="mTw-JJ-EK2"/>
                                                                        <constraint firstAttribute="height" constant="1" id="y3h-BL-K6K"/>
                                                                    </constraints>
                                                                </view>
                                                            </subviews>
                                                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                            <constraints>
                                                                <constraint firstAttribute="bottom" secondItem="O6E-2A-DiB" secondAttribute="bottom" id="GEa-tu-Eom"/>
                                                                <constraint firstAttribute="trailing" secondItem="cpr-bJ-lbi" secondAttribute="trailing" id="LbP-QR-cya"/>
                                                                <constraint firstItem="O6E-2A-DiB" firstAttribute="leading" secondItem="laE-40-caK" secondAttribute="leading" id="OWw-C7-Ur4"/>
                                                                <constraint firstItem="cpr-bJ-lbi" firstAttribute="leading" secondItem="laE-40-caK" secondAttribute="leading" id="R5C-8a-Zry"/>
                                                                <constraint firstAttribute="trailing" secondItem="O6E-2A-DiB" secondAttribute="trailing" id="Wuv-WW-z05"/>
                                                                <constraint firstItem="cpr-bJ-lbi" firstAttribute="top" secondItem="laE-40-caK" secondAttribute="top" id="nWM-Of-Lbx"/>
                                                                <constraint firstAttribute="bottom" secondItem="cpr-bJ-lbi" secondAttribute="bottom" constant="1" id="p70-gt-IY5"/>
                                                                <constraint firstItem="O6E-2A-DiB" firstAttribute="top" secondItem="cpr-bJ-lbi" secondAttribute="bottom" id="yDE-Ht-Gk8"/>
                                                            </constraints>
                                                        </view>
                                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Xck-wc-aSH">
                                                            <rect key="frame" x="0.0" y="174" width="345" height="58"/>
                                                            <subviews>
                                                                <stackView opaque="NO" contentMode="scaleToFill" spacing="6" translatesAutoresizingMaskIntoConstraints="NO" id="uMe-bU-Pen">
                                                                    <rect key="frame" x="0.0" y="0.0" width="345" height="57"/>
                                                                    <subviews>
                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Check New Version" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="ubK-8s-ggv">
                                                                            <rect key="frame" x="0.0" y="0.0" width="139.************34" height="57"/>
                                                                            <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="16"/>
                                                                            <color key="textColor" red="0.*****************" green="0.075626514850000007" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                            <nil key="highlightedColor"/>
                                                                        </label>
                                                                        <button opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="249" contentHorizontalAlignment="right" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="fVw-fh-jFe">
                                                                            <rect key="frame" x="145.************37" y="0.0" width="199.**************" height="57"/>
                                                                            <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="16"/>
                                                                            <state key="normal" title="V3.0">
                                                                                <color key="titleColor" red="0.*****************" green="0.075626514850000007" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                            </state>
                                                                            <connections>
                                                                                <action selector="checkVersionAction:" destination="-1" eventType="touchUpInside" id="gXk-ab-oyM"/>
                                                                            </connections>
                                                                        </button>
                                                                        <button hidden="YES" opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="right" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="iHr-rm-z1V">
                                                                            <rect key="frame" x="345" y="0.0" width="0.0" height="57"/>
                                                                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                                            <state key="normal" image="forwarIcon"/>
                                                                            <connections>
                                                                                <action selector="checkVersionAction:" destination="-1" eventType="touchUpInside" id="bq1-wW-Az0"/>
                                                                            </connections>
                                                                        </button>
                                                                    </subviews>
                                                                </stackView>
                                                                <view alpha="0.*****************" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Elc-ze-CmL">
                                                                    <rect key="frame" x="0.0" y="57" width="345" height="1"/>
                                                                    <color key="backgroundColor" red="0.*****************" green="0.**********" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                    <constraints>
                                                                        <constraint firstAttribute="height" constant="1" id="0eJ-C4-RBB"/>
                                                                        <constraint firstAttribute="height" relation="greaterThanOrEqual" constant="1" id="Nja-nb-FkD"/>
                                                                    </constraints>
                                                                </view>
                                                            </subviews>
                                                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                            <constraints>
                                                                <constraint firstAttribute="trailing" secondItem="Elc-ze-CmL" secondAttribute="trailing" id="3db-0L-HDR"/>
                                                                <constraint firstItem="Elc-ze-CmL" firstAttribute="top" secondItem="uMe-bU-Pen" secondAttribute="bottom" id="Dmn-1H-CA2"/>
                                                                <constraint firstItem="Elc-ze-CmL" firstAttribute="leading" secondItem="Xck-wc-aSH" secondAttribute="leading" id="HLc-n1-Kjf"/>
                                                                <constraint firstAttribute="bottom" secondItem="Elc-ze-CmL" secondAttribute="bottom" id="U0K-ZZ-SWe"/>
                                                                <constraint firstAttribute="bottom" secondItem="uMe-bU-Pen" secondAttribute="bottom" constant="1" id="Uff-Eq-8WH"/>
                                                                <constraint firstItem="uMe-bU-Pen" firstAttribute="top" secondItem="Xck-wc-aSH" secondAttribute="top" id="kgr-Ou-gcN"/>
                                                                <constraint firstAttribute="trailing" secondItem="uMe-bU-Pen" secondAttribute="trailing" id="x9V-ji-QZP"/>
                                                                <constraint firstItem="uMe-bU-Pen" firstAttribute="leading" secondItem="Xck-wc-aSH" secondAttribute="leading" id="yVM-8d-cmi"/>
                                                            </constraints>
                                                        </view>
                                                    </subviews>
                                                </stackView>
                                            </subviews>
                                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <constraints>
                                                <constraint firstAttribute="bottom" secondItem="VTo-97-pkm" secondAttribute="bottom" id="W25-qM-Ev0"/>
                                                <constraint firstAttribute="trailing" secondItem="VTo-97-pkm" secondAttribute="trailing" constant="24" id="h9m-UJ-f49"/>
                                                <constraint firstAttribute="height" constant="232" id="pdT-x4-aEp"/>
                                                <constraint firstItem="VTo-97-pkm" firstAttribute="leading" secondItem="kFV-UE-3Up" secondAttribute="leading" constant="24" id="t0p-Zw-VtI"/>
                                                <constraint firstItem="VTo-97-pkm" firstAttribute="top" secondItem="kFV-UE-3Up" secondAttribute="top" id="wSw-vL-6FR"/>
                                            </constraints>
                                        </view>
                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Cdu-T2-E01">
                                            <rect key="frame" x="0.0" y="506" width="393" height="114"/>
                                            <subviews>
                                                <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="fillEqually" translatesAutoresizingMaskIntoConstraints="NO" id="o4x-PI-cHd">
                                                    <rect key="frame" x="24" y="0.0" width="345" height="114"/>
                                                    <subviews>
                                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="PYL-B0-MrP">
                                                            <rect key="frame" x="0.0" y="0.0" width="345" height="57"/>
                                                            <subviews>
                                                                <stackView opaque="NO" contentMode="scaleToFill" spacing="6" translatesAutoresizingMaskIntoConstraints="NO" id="ppu-nf-wZW">
                                                                    <rect key="frame" x="0.0" y="0.0" width="345" height="56"/>
                                                                    <subviews>
                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Change Password" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="ekn-xB-sqg">
                                                                            <rect key="frame" x="0.0" y="0.0" width="132.66666666666666" height="56"/>
                                                                            <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="16"/>
                                                                            <color key="textColor" red="0.*****************" green="0.075626514850000007" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                            <nil key="highlightedColor"/>
                                                                        </label>
                                                                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="right" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="jxW-d6-AeA">
                                                                            <rect key="frame" x="138.**************" y="0.0" width="206.************37" height="56"/>
                                                                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                                            <state key="normal" image="forwarIcon"/>
                                                                            <connections>
                                                                                <action selector="changePasswordAction:" destination="-1" eventType="touchUpInside" id="5UX-co-7kO"/>
                                                                            </connections>
                                                                        </button>
                                                                    </subviews>
                                                                </stackView>
                                                                <view alpha="0.64999997615814209" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="zVc-TH-OMM">
                                                                    <rect key="frame" x="0.0" y="56" width="345" height="1"/>
                                                                    <color key="backgroundColor" red="0.*****************" green="0.**********" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                    <constraints>
                                                                        <constraint firstAttribute="height" constant="1" id="AB9-ec-rqM"/>
                                                                        <constraint firstAttribute="height" relation="greaterThanOrEqual" constant="1" id="NO3-Tq-tCZ"/>
                                                                    </constraints>
                                                                </view>
                                                            </subviews>
                                                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                            <constraints>
                                                                <constraint firstAttribute="trailing" secondItem="ppu-nf-wZW" secondAttribute="trailing" id="ExH-x3-HRv"/>
                                                                <constraint firstItem="ppu-nf-wZW" firstAttribute="top" secondItem="PYL-B0-MrP" secondAttribute="top" id="H6z-X8-myV"/>
                                                                <constraint firstItem="zVc-TH-OMM" firstAttribute="top" secondItem="ppu-nf-wZW" secondAttribute="bottom" id="cKD-WS-qDg"/>
                                                                <constraint firstAttribute="bottom" secondItem="ppu-nf-wZW" secondAttribute="bottom" constant="1" id="mmb-B6-qe7"/>
                                                                <constraint firstItem="zVc-TH-OMM" firstAttribute="leading" secondItem="PYL-B0-MrP" secondAttribute="leading" id="pwo-Jj-FVd"/>
                                                                <constraint firstAttribute="trailing" secondItem="zVc-TH-OMM" secondAttribute="trailing" id="tv0-On-REs"/>
                                                                <constraint firstItem="ppu-nf-wZW" firstAttribute="leading" secondItem="PYL-B0-MrP" secondAttribute="leading" id="u53-bu-ETJ"/>
                                                                <constraint firstAttribute="bottom" secondItem="zVc-TH-OMM" secondAttribute="bottom" id="y2m-oC-qsI"/>
                                                            </constraints>
                                                        </view>
                                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="AMs-mh-Hg2">
                                                            <rect key="frame" x="0.0" y="57" width="345" height="57"/>
                                                            <subviews>
                                                                <stackView opaque="NO" contentMode="scaleToFill" spacing="6" translatesAutoresizingMaskIntoConstraints="NO" id="8e5-Jz-uEz">
                                                                    <rect key="frame" x="0.0" y="0.0" width="345" height="56"/>
                                                                    <subviews>
                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Delete Account" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="XXg-Ch-nGO">
                                                                            <rect key="frame" x="0.0" y="0.0" width="113.************33" height="56"/>
                                                                            <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="16"/>
                                                                            <color key="textColor" red="0.*****************" green="0.075626514850000007" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                            <nil key="highlightedColor"/>
                                                                        </label>
                                                                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="right" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="f1V-po-LiX">
                                                                            <rect key="frame" x="119.************36" y="0.0" width="225.**************" height="56"/>
                                                                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                                            <state key="normal" image="forwarIcon"/>
                                                                            <connections>
                                                                                <action selector="deleteAccountAction:" destination="-1" eventType="touchUpInside" id="BqL-4F-cNq"/>
                                                                            </connections>
                                                                        </button>
                                                                    </subviews>
                                                                </stackView>
                                                                <view alpha="0.*****************" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="iaj-dE-PXj">
                                                                    <rect key="frame" x="0.0" y="56" width="345" height="1"/>
                                                                    <color key="backgroundColor" red="0.*****************" green="0.**********" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                    <constraints>
                                                                        <constraint firstAttribute="height" relation="greaterThanOrEqual" constant="1" id="6iR-Fq-EH8"/>
                                                                        <constraint firstAttribute="height" constant="1" id="Dm0-YL-mHo"/>
                                                                    </constraints>
                                                                </view>
                                                            </subviews>
                                                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                            <constraints>
                                                                <constraint firstItem="iaj-dE-PXj" firstAttribute="top" secondItem="8e5-Jz-uEz" secondAttribute="bottom" id="0OS-zD-04p"/>
                                                                <constraint firstAttribute="bottom" secondItem="iaj-dE-PXj" secondAttribute="bottom" id="5mx-Qp-8Oh"/>
                                                                <constraint firstAttribute="bottom" secondItem="8e5-Jz-uEz" secondAttribute="bottom" constant="1" id="Ekf-QI-K8c"/>
                                                                <constraint firstItem="iaj-dE-PXj" firstAttribute="leading" secondItem="AMs-mh-Hg2" secondAttribute="leading" id="XFC-vz-786"/>
                                                                <constraint firstItem="8e5-Jz-uEz" firstAttribute="leading" secondItem="AMs-mh-Hg2" secondAttribute="leading" id="cqG-pC-tIf"/>
                                                                <constraint firstItem="8e5-Jz-uEz" firstAttribute="top" secondItem="AMs-mh-Hg2" secondAttribute="top" id="eoB-D7-hp4"/>
                                                                <constraint firstAttribute="trailing" secondItem="iaj-dE-PXj" secondAttribute="trailing" id="hic-lG-9A9"/>
                                                                <constraint firstAttribute="trailing" secondItem="8e5-Jz-uEz" secondAttribute="trailing" id="wi2-Sl-J8k"/>
                                                            </constraints>
                                                        </view>
                                                    </subviews>
                                                </stackView>
                                            </subviews>
                                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <constraints>
                                                <constraint firstAttribute="trailing" secondItem="o4x-PI-cHd" secondAttribute="trailing" constant="24" id="9nD-iR-lkM"/>
                                                <constraint firstAttribute="height" constant="114" id="Peg-Xi-0ow"/>
                                                <constraint firstAttribute="bottom" secondItem="o4x-PI-cHd" secondAttribute="bottom" id="h5y-8e-XKb"/>
                                                <constraint firstItem="o4x-PI-cHd" firstAttribute="leading" secondItem="Cdu-T2-E01" secondAttribute="leading" constant="24" id="jjl-mw-ofk"/>
                                                <constraint firstItem="o4x-PI-cHd" firstAttribute="top" secondItem="Cdu-T2-E01" secondAttribute="top" id="rmj-AA-Xjq"/>
                                            </constraints>
                                        </view>
                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="oFm-2r-2MF">
                                            <rect key="frame" x="0.0" y="632" width="393" height="48"/>
                                            <subviews>
                                                <button opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="f1S-FB-v0N">
                                                    <rect key="frame" x="20" y="0.0" width="353" height="48"/>
                                                    <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                    <state key="normal" title="Logout">
                                                        <color key="titleColor" red="0.*****************" green="0.075626514850000007" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                    </state>
                                                    <userDefinedRuntimeAttributes>
                                                        <userDefinedRuntimeAttribute type="number" keyPath="layer.borderWidth">
                                                            <integer key="value" value="1"/>
                                                        </userDefinedRuntimeAttribute>
                                                        <userDefinedRuntimeAttribute type="color" keyPath="layer.borderColor">
                                                            <color key="value" red="0.*****************" green="0.075626514850000007" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                        </userDefinedRuntimeAttribute>
                                                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                            <integer key="value" value="4"/>
                                                        </userDefinedRuntimeAttribute>
                                                    </userDefinedRuntimeAttributes>
                                                    <connections>
                                                        <action selector="logoutAction:" destination="-1" eventType="touchUpInside" id="UZh-VY-4mK"/>
                                                    </connections>
                                                </button>
                                            </subviews>
                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <constraints>
                                                <constraint firstItem="f1S-FB-v0N" firstAttribute="leading" secondItem="oFm-2r-2MF" secondAttribute="leading" constant="20" id="1Fk-SS-mxg"/>
                                                <constraint firstAttribute="bottom" secondItem="f1S-FB-v0N" secondAttribute="bottom" id="3GV-Qq-1Qc"/>
                                                <constraint firstAttribute="height" constant="48" id="IO1-4b-NVJ"/>
                                                <constraint firstItem="f1S-FB-v0N" firstAttribute="top" secondItem="oFm-2r-2MF" secondAttribute="top" id="hPx-LL-igZ"/>
                                                <constraint firstAttribute="trailing" secondItem="f1S-FB-v0N" secondAttribute="trailing" constant="20" id="mTk-Vk-0PR"/>
                                            </constraints>
                                        </view>
                                    </subviews>
                                </stackView>
                            </subviews>
                            <color key="backgroundColor" red="0.94169229269999999" green="0.92880314590000002" blue="0.9607291818" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                            <constraints>
                                <constraint firstItem="Kwy-DA-EnW" firstAttribute="top" secondItem="k2o-7T-wGT" secondAttribute="top" id="3a3-oc-lyU"/>
                                <constraint firstAttribute="bottom" secondItem="Kwy-DA-EnW" secondAttribute="bottom" id="Arq-C5-Idn"/>
                                <constraint firstItem="Kwy-DA-EnW" firstAttribute="leading" secondItem="k2o-7T-wGT" secondAttribute="leading" id="JcE-JX-OAc"/>
                                <constraint firstAttribute="trailing" secondItem="Kwy-DA-EnW" secondAttribute="trailing" id="RgY-po-d8j"/>
                            </constraints>
                        </view>
                    </subviews>
                    <constraints>
                        <constraint firstItem="k2o-7T-wGT" firstAttribute="width" secondItem="pgh-30-Wdu" secondAttribute="width" id="QDd-ig-EKd"/>
                        <constraint firstItem="k2o-7T-wGT" firstAttribute="leading" secondItem="pgh-30-Wdu" secondAttribute="leading" id="S43-bb-uva"/>
                        <constraint firstAttribute="bottom" secondItem="k2o-7T-wGT" secondAttribute="bottom" id="V5G-p4-XuJ"/>
                        <constraint firstItem="k2o-7T-wGT" firstAttribute="centerY" secondItem="pgh-30-Wdu" secondAttribute="centerY" id="Y5h-12-bo7"/>
                        <constraint firstItem="k2o-7T-wGT" firstAttribute="top" secondItem="pgh-30-Wdu" secondAttribute="top" id="YHa-8c-ZEW"/>
                        <constraint firstAttribute="trailing" secondItem="k2o-7T-wGT" secondAttribute="trailing" id="dpO-Ww-bxC"/>
                    </constraints>
                    <viewLayoutGuide key="contentLayoutGuide" id="iXO-eY-rew"/>
                    <viewLayoutGuide key="frameLayoutGuide" id="gE5-Ra-tJH"/>
                </scrollView>
                <view hidden="YES" contentMode="scaleToFill" ambiguous="YES" translatesAutoresizingMaskIntoConstraints="NO" id="I43-nQ-trQ">
                    <rect key="frame" x="51.666666666666657" y="381" width="290" height="90"/>
                    <subviews>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="The current version is the latest version V1.0" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="rD9-Xl-MqU">
                            <rect key="frame" x="20" y="20.************329" width="250" height="50"/>
                            <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="13"/>
                            <color key="textColor" red="0.9898208508788553" green="1" blue="0.97918684587441485" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                            <color key="highlightedColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        </label>
                    </subviews>
                    <color key="backgroundColor" red="0.18858519200000001" green="0.057947516439999999" blue="0.3526558876" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                    <constraints>
                        <constraint firstItem="rD9-Xl-MqU" firstAttribute="top" secondItem="I43-nQ-trQ" secondAttribute="top" constant="20" id="49A-pd-WEz"/>
                        <constraint firstAttribute="height" constant="90" id="9Bc-53-60X"/>
                        <constraint firstAttribute="width" constant="290" id="Iyl-wK-hps"/>
                        <constraint firstAttribute="bottom" secondItem="rD9-Xl-MqU" secondAttribute="bottom" constant="20" id="KuT-Ta-S3u"/>
                        <constraint firstAttribute="trailing" secondItem="rD9-Xl-MqU" secondAttribute="trailing" constant="20" id="iQc-eS-Y1K"/>
                        <constraint firstItem="rD9-Xl-MqU" firstAttribute="leading" secondItem="I43-nQ-trQ" secondAttribute="leading" constant="20" id="mx4-Vn-45s"/>
                    </constraints>
                </view>
            </subviews>
            <viewLayoutGuide key="safeArea" id="fnl-2z-Ty3"/>
            <color key="backgroundColor" red="0.94169229269999999" green="0.92880314590000002" blue="0.9607291818" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
            <constraints>
                <constraint firstItem="I43-nQ-trQ" firstAttribute="centerX" secondItem="i5M-Pr-FkT" secondAttribute="centerX" id="Nfk-VL-2ja"/>
                <constraint firstItem="pgh-30-Wdu" firstAttribute="top" secondItem="fnl-2z-Ty3" secondAttribute="top" id="p7N-bq-qMi"/>
                <constraint firstItem="I43-nQ-trQ" firstAttribute="centerY" secondItem="i5M-Pr-FkT" secondAttribute="centerY" id="qrw-dq-Utm"/>
                <constraint firstAttribute="bottom" secondItem="pgh-30-Wdu" secondAttribute="bottom" id="vdX-Se-PAU"/>
                <constraint firstItem="pgh-30-Wdu" firstAttribute="trailing" secondItem="fnl-2z-Ty3" secondAttribute="trailing" id="vhG-2J-ycn"/>
                <constraint firstItem="pgh-30-Wdu" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" id="zDL-fI-VWq"/>
            </constraints>
            <point key="canvasLocation" x="130.53435114503816" y="-12.67605633802817"/>
        </view>
    </objects>
    <resources>
        <image name="forwarIcon" width="16" height="16"/>
    </resources>
</document>
