//
//  CycleReportTableViewController.swift
//  HormoneLife
//
//  Created by Tank on 2024/9/1.
//

import UIKit
import Photos

class CycleReportTableViewController: BaseViewController, UITableViewDelegate, UITableViewDataSource {
    
    let headerView = CycleReportHeaderView()
    
    private lazy var tableView: UITableView = {
        let tableView = UITableView(frame: .zero, style: .plain)
        tableView.backgroundColor = .themeColor
        tableView.showsVerticalScrollIndicator = false
        tableView.separatorStyle = .none
        
        tableView.register(CycleReportTableViewCell.self, forCellReuseIdentifier: "CycleReportTableViewCell")
        
        tableView.dataSource = self
        tableView.delegate = self
        tableView.rowHeight = UITableView.automaticDimension
        return tableView
    }()
    
    var yearsPeriodData: [UserPeriodCycleListByYear] = []

    override func viewDidLoad() {
        super.viewDidLoad()

        setupNavigationBar()
        setupUI()
        fetchUserInfo()
        getCycleListByYear()
    }
    
    private func fetchUserInfo() {
//        UserInteractor.getCycleAndPeriodAvg { info in
//            guard let userInfo = info else { return }
//
//            let user = LoginUser(deviceId: UserDefaults.standard.deviceID, token: UserDefaults.standard.userToken, userInfo: userInfo)
//            Interface.shared().loggedInUser = user
        
        guard let user = Interface.shared().loggedInUser,
            let cycleAndPeriodAvg = user.cycleAndPeriodAvg else { return }
        self.headerView.subView1.title.text = "\(cycleAndPeriodAvg.avgMenstruationCycleAvg)"
        self.headerView.subView2.title.text = "\(cycleAndPeriodAvg.avgPeriod)"
//        }
    }
    
    private func getCycleListByYear() {
        UserPeriodInteractor.userPeriodCycleByYear() { dataList in
            self.yearsPeriodData = dataList
            self.tableView.reloadData()
        }
    }
    
    @objc func didDownloadCapture() {
        showActivityHUD()
        let tbHeight = (CGFloat(self.yearsPeriodData.count) * 65 + 30)

        let user = Interface.shared().loggedInUser
        
        let captureView = CycleReportCaptureView(frame: CGRect(x: -kScreenWidth, y: 0, width: kScreenWidth, height: 500 + tbHeight), title: "\((user?.cycleAndPeriodAvg?.avgMenstruationCycleAvg ?? 0))", cycle: "\(user?.cycleAndPeriodAvg?.avgPeriod ?? 0)", rowHeight: tbHeight, dataSource: self.yearsPeriodData)
        self.view.addSubview(captureView)
        DispatchQueue.main.asyncAfter(deadline: DispatchTime.now() + 1) {
            if let screenshotd = takeScreenshotOfView(captureView) {
                self.saveImageToPhotosAlbum(screenshotd)
            }
            hideActivityHUD()
        }
    }
    
    func saveImageToPhotosAlbum(_ image: UIImage) {
        PHPhotoLibrary.shared().performChanges({
            let assetChangeRequest = PHAssetChangeRequest.creationRequestForAsset(from: image)
        }) { completed, error in
            if completed {
                showToachMessage(message: "Save success")
            } else {
                
            }
        }
    }
    
    
    private func setupUI() {
        view.addSubview(tableView)
        tableView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        tableView.tableHeaderView = headerView
    }
    
    private func setupNavigationBar() {
        navigationItem.title = "Cycle Reports"
        navigationItem.rightBarButtonItem = UIBarButtonItem(image: UIImage(named: "downloadIcon"), style: .done, target: self, action: #selector(didDownloadCapture))
    }
    
    // MARK: - Table view data source

    func numberOfSections(in tableView: UITableView) -> Int {
        return yearsPeriodData.count
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        guard yearsPeriodData.count > section else { return 0 }
        return yearsPeriodData[section].cycleList.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        
        guard yearsPeriodData.count > indexPath.section,
              yearsPeriodData[indexPath.section].cycleList.count > indexPath.row,
              let cell = tableView.dequeueReusableCell(withIdentifier: "CycleReportTableViewCell", for: indexPath) as? CycleReportTableViewCell else {
            return UITableViewCell()
        }
        
        let periodData = yearsPeriodData[indexPath.section].cycleList[indexPath.row]
        cell.configCell(data: periodData, indexPath: indexPath, sectionCount: yearsPeriodData[indexPath.section].cycleList.count)
        return cell
    }
    
    func tableView(_ tableView: UITableView, viewForHeaderInSection section: Int) -> UIView? {
        guard yearsPeriodData.count > section else { return nil }
        let title = "    ・ \(yearsPeriodData[section].year)"
        let label = UILabel()
        label.text = title
        label.textColor = .mainTextColor
        label.font = .mediumGilroyFont(14)
        return label
    }
}
