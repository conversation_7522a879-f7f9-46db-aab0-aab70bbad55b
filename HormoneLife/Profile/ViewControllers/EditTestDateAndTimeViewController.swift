//
//  EditTestDateAndTimeViewController.swift
//  HormoneLife
//
//  Created by Tank on 2024/6/23.
//

import UIKit

class EditTestDateAndTimeViewController: BaseViewController, UITableViewDelegate, UITableViewDataSource, EditTestDateVCTestTimeCellTableViewCellDelegate, EditTestDateCycleCellDelegate {

    enum Row {
        case time(title: String)
        case date(title: String)
        case cycle(title: String)
    }
    
    var rows: [Row] {
        [.time(title: "Modify Test Time"),
         .date(title: "Modify Test Time"),
         .cycle(title: "Modify Your Cycle")]
    }
    
    private lazy var tableView: UITableView = {
        let tableView = UITableView(frame: .zero, style: .plain)
        tableView.backgroundColor = .themeColor
        tableView.showsVerticalScrollIndicator = false
        tableView.separatorStyle = .none
        
        tableView.register(EditTestDateVCTestTimeCellTableViewCell.self, forCellReuseIdentifier: "EditTestDateVCTestTimeCellTableViewCell")
        tableView.register(EditTestDateVCCalendarCell.self, forCellReuseIdentifier: "EditTestDateVCCalendarCell")
        tableView.register(EditTestDateCycleCell.self, forCellReuseIdentifier: "EditTestDateCycleCell")
        
        tableView.dataSource = self
        tableView.delegate = self
        tableView.rowHeight = UITableView.automaticDimension
        return tableView
    }()
    
    lazy var saveButton: UIButton = {
        let b = UIButton(type: .custom)
        b.backgroundColor = .mainTextColor
        b.layer.cornerRadius = 4
        b.setTitle("Save", for: .normal)
        b.titleLabel?.font = .regularGilroyFont(16)
        b.setTitleColor(.white, for: .normal)
        b.addTarget(self, action: #selector(didTapSave), for: .touchUpInside)
        return b
    }()
    
    override func viewDidLoad() {
        super.viewDidLoad()

        navigationItem.title = "Edit test date & time"
        setupUI()
    }
    
    private func setupUI() {
        view.backgroundColor = .themeColor
        view.addSubview(tableView)
        view.addSubview(saveButton)
        saveButton.snp.makeConstraints { make in
            make.height.equalTo(48)
            make.left.right.equalToSuperview().inset(24)
            make.bottom.equalToSuperview().inset(34)
        }
        
        tableView.snp.makeConstraints { make in
            make.left.top.right.equalToSuperview()
            make.bottom.equalTo(saveButton.snp.top)
        }
    }
    
    @objc func didTapSave() {
        
    }
    
    func didSeletValue(_ value: String) {
        print(value)
    }
    
    func didSeletCycle(_ value: String) {
        print(value)
    }
    
    func numberOfSections(in tableView: UITableView) -> Int {
        1
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        rows.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let row = rows[indexPath.row]
        switch row {
        case .time(let title):
            guard let cell = tableView.dequeueReusableCell(withIdentifier: "EditTestDateVCTestTimeCellTableViewCell", for: indexPath) as? EditTestDateVCTestTimeCellTableViewCell else {
                return UITableViewCell()
            }
            cell.title.text = title
            cell.delegate = self
            return cell
        case .date(let title):
            guard let cell = tableView.dequeueReusableCell(withIdentifier: "EditTestDateVCCalendarCell", for: indexPath) as? EditTestDateVCCalendarCell else {
                return UITableViewCell()
            }
            cell.title.text = title
            return cell
        case .cycle(let title):
            guard let cell = tableView.dequeueReusableCell(withIdentifier: "EditTestDateCycleCell", for: indexPath) as? EditTestDateCycleCell else {
                return UITableViewCell()
            }
            cell.title.text = title
            cell.delegate = self
            return cell
        }
    }

}
