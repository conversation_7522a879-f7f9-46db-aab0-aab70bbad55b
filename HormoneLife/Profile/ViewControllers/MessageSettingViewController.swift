//
//  MessageSettingViewController.swift
//  HormoneLife
//
//  Created by Tank on 2024/6/9.
//

import UIKit
import SnapKit

class MessageSettingViewController: BaseViewController {

    @IBOutlet weak var announcementSwitch: UISwitch!
    @IBOutlet weak var reminder1Switch: UISwitch!
    @IBOutlet weak var reminder2Switch: UISwitch!
    @IBOutlet weak var testReminderSwitch: UISwitch!
    
    @IBOutlet weak var lhUItraTestTime: UIButton!
    @IBOutlet weak var pdgTestTime: UIButton!
    @IBOutlet weak var fshTestTime: UIButton!
    var editTimeButton: UIButton?
    
    @IBOutlet weak var lhUItraTestView: UIView!
    @IBOutlet weak var pdgTestView: UIView!
    @IBOutlet weak var fshTestView: UIView!
    
    private lazy var pickerView: TimePickerView = {
        let pickerView = TimePickerView()
        pickerView.delegate = self
        return pickerView
    }()
    var pickerViewBottomContrain: Constraint?
    
    var originalTime: String?
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        view.backgroundColor = .themeColor
        setupNavigationBar()
        setupUI()
        
        self.refreshDatas()
    }
    
    func refreshDatas() {
        let userM = Interface.shared().loggedInUser?.userInfo.userBusinessConfigVO
        self.reminder2Switch.isOn = (userM?.fertilityReminderOn == 1)
        self.testReminderSwitch.isOn = (userM?.testReminderOn == 1)
        self.reminder1Switch.isOn = (self.reminder2Switch.isOn && self.testReminderSwitch.isOn)
        
        self.lhUItraTestTime.setTitle(userM?.lhTime ?? "8:00 AM", for: .normal)
        self.pdgTestTime.setTitle(userM?.pdgTime ?? "8:00 AM", for: .normal)
        self.fshTestTime.setTitle(userM?.fshTime ?? "8:00 AM", for: .normal)
    }
    
    func updateConfig(config: UserConfig) {
        UserInteractor.userConfigUpdate(config) { success in
            hl_fetchUserInfo()
        }
    }
    
    private func setupUI() {
        view.addSubview(pickerView)
        
        [reminder1Switch, reminder2Switch, testReminderSwitch].forEach {
            $0.onTintColor = .labelBackColor
        }
        
        pickerView.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.height.equalTo(320)
            pickerViewBottomContrain = make.bottom.equalToSuperview().inset(-320).constraint
        }
        
        //view.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(didTapView)))
        lhUItraTestView.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(editLhUItraTimeAction)))
        pdgTestView.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(editPdgTimeAction)))
        fshTestView.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(editFshTimeAction)))
    }

    private func setupNavigationBar() {
        navigationItem.title = "Setting"
    }
    
    @IBAction func announcementMessageAction(_ sender: Any) {
        
    }
    
    @IBAction func reminderMessageAction(_ sender: Any) {
        self.reminder2Switch.isOn = self.reminder1Switch.isOn
        self.testReminderSwitch.isOn = self.reminder1Switch.isOn
        
        var config = UserConfig()
        
        if self.reminder1Switch.isOn {
            config.testReminderOn = "1"
            config.fertilityReminderOn = "1"
        } else {
            config.testReminderOn = "0"
            config.fertilityReminderOn = "0"
        }
        self.updateConfig(config: config)
    }
    
    @IBAction func ReminderMessageSecondAction(_ sender: Any) {
        self.reminder1Switch.isOn = (self.reminder2Switch.isOn && self.testReminderSwitch.isOn)
        var config = UserConfig()
        if self.reminder2Switch.isOn {
            config.fertilityReminderOn = "1"
        } else {
            config.fertilityReminderOn = "0"
        }
        self.updateConfig(config: config)
    }
    
    @IBAction func testReminderAction(_ sender: Any) {
        self.reminder1Switch.isOn = (self.reminder2Switch.isOn && self.testReminderSwitch.isOn)
        var config = UserConfig()
        if self.testReminderSwitch.isOn {
            config.testReminderOn = "1"
        } else {
            config.testReminderOn = "0"
        }
        self.updateConfig(config: config)
    }
    
    @objc func editLhUItraTimeAction() {
        originalTime = lhUItraTestTime.titleLabel?.text
        editTimeButton = lhUItraTestTime
        UIView.animate(withDuration: 0.15) {
            self.pickerViewBottomContrain?.update(inset: 0)
            self.view.layoutIfNeeded()
        }
    }
    
    @objc func editPdgTimeAction() {
        originalTime = pdgTestTime.titleLabel?.text
        editTimeButton = pdgTestTime
        UIView.animate(withDuration: 0.15) {
            self.pickerViewBottomContrain?.update(inset: 0)
            self.view.layoutIfNeeded()
        }
    }
    
    @objc func editFshTimeAction() {
        originalTime = fshTestTime.titleLabel?.text
        editTimeButton = fshTestTime
        UIView.animate(withDuration: 0.15) {
            self.pickerViewBottomContrain?.update(inset: 0)
            self.view.layoutIfNeeded()
        }
    }
    
    @objc func didTapView(_ save: Bool = true) {
        if !save {
            editTimeButton?.setTitle(originalTime, for: .normal)
        }
        
        UIView.animate(withDuration: 0.15) {
            self.pickerViewBottomContrain?.update(inset: -320)
            self.view.layoutIfNeeded()
        }
    }
}

extension MessageSettingViewController: TimePickerViewDelegate {
    func didTapCancel() {
        didTapView(false)
    }
    
    func didTapSave(_ time: String) {
        didTapView()
        editTimeButton?.setTitle(time, for: .normal)
        if editTimeButton == lhUItraTestTime {
            var config = UserConfig()
            config.lhTime = time
            self.updateConfig(config: config)
        } else if editTimeButton == fshTestTime {
            var config = UserConfig()
            config.fshTime = time
            self.updateConfig(config: config)
        } else if editTimeButton == pdgTestTime {
            var config = UserConfig()
            config.pdgTime = time
            self.updateConfig(config: config)
        }
    }
    
    func didSelect(_ time: String) {
        editTimeButton?.setTitle(time, for: .normal)
    }
}
