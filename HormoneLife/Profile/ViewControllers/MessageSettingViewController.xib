<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="22505" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22504"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="MessageSettingViewController" customModule="HormoneLife" customModuleProvider="target">
            <connections>
                <outlet property="announcementSwitch" destination="mEG-3e-8VL" id="1uY-yY-BIP"/>
                <outlet property="fshTestTime" destination="RVY-MG-Tbk" id="ajf-Vu-dAp"/>
                <outlet property="fshTestView" destination="kd6-vw-Rqn" id="pw7-S8-d4T"/>
                <outlet property="lhUItraTestTime" destination="qrF-p8-IaK" id="Knf-4p-cWL"/>
                <outlet property="lhUItraTestView" destination="pbG-cS-Tb4" id="eTs-Ky-QF9"/>
                <outlet property="pdgTestTime" destination="MjR-iO-Rm7" id="GW2-BD-hCp"/>
                <outlet property="pdgTestView" destination="WsC-1E-xYv" id="O15-Cf-rnr"/>
                <outlet property="reminder1Switch" destination="uft-qd-0Pg" id="0Ax-YS-tq1"/>
                <outlet property="reminder2Switch" destination="7nB-nT-3fP" id="izw-4P-Wmc"/>
                <outlet property="testReminderSwitch" destination="vZR-L9-6fb" id="4Nt-FW-Aw0"/>
                <outlet property="view" destination="i5M-Pr-FkT" id="sfx-zR-JGt"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="i5M-Pr-FkT">
            <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="pgh-30-Wdu">
                    <rect key="frame" x="0.0" y="59" width="393" height="793"/>
                    <subviews>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="k2o-7T-wGT">
                            <rect key="frame" x="0.0" y="0.0" width="393" height="793"/>
                            <subviews>
                                <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="Iw9-uP-WgF">
                                    <rect key="frame" x="0.0" y="0.0" width="393" height="396"/>
                                    <subviews>
                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="trx-LW-nEF">
                                            <rect key="frame" x="0.0" y="0.0" width="393" height="396"/>
                                            <subviews>
                                                <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="SfM-w2-aUM">
                                                    <rect key="frame" x="24" y="0.0" width="345" height="366"/>
                                                    <subviews>
                                                        <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="3wz-o3-jSl">
                                                            <rect key="frame" x="0.0" y="-80" width="345" height="80"/>
                                                            <subviews>
                                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="System Message" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="gLe-yT-atb">
                                                                    <rect key="frame" x="0.0" y="31.666666666666664" width="107.66666666666667" height="17"/>
                                                                    <constraints>
                                                                        <constraint firstAttribute="height" constant="17" id="bUX-8o-iR3"/>
                                                                    </constraints>
                                                                    <fontDescription key="fontDescription" name="HelveticaNeue" family="Helvetica Neue" pointSize="14"/>
                                                                    <nil key="textColor"/>
                                                                    <nil key="highlightedColor"/>
                                                                </label>
                                                                <switch hidden="YES" opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="750" verticalHuggingPriority="750" contentHorizontalAlignment="center" contentVerticalAlignment="center" on="YES" translatesAutoresizingMaskIntoConstraints="NO" id="seu-tJ-xxA">
                                                                    <rect key="frame" x="284" y="24.666666666666671" width="51" height="31"/>
                                                                </switch>
                                                                <view alpha="0.5" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="SlK-uW-OBB">
                                                                    <rect key="frame" x="0.0" y="79" width="345" height="1"/>
                                                                    <color key="backgroundColor" systemColor="systemGray4Color"/>
                                                                    <constraints>
                                                                        <constraint firstAttribute="height" constant="1" id="y6I-nz-4lq"/>
                                                                    </constraints>
                                                                </view>
                                                            </subviews>
                                                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                            <constraints>
                                                                <constraint firstItem="SlK-uW-OBB" firstAttribute="leading" secondItem="3wz-o3-jSl" secondAttribute="leading" id="2q5-Jb-dKF"/>
                                                                <constraint firstAttribute="trailing" secondItem="SlK-uW-OBB" secondAttribute="trailing" id="J1J-Tr-tZ0"/>
                                                                <constraint firstAttribute="trailing" secondItem="seu-tJ-xxA" secondAttribute="trailing" constant="12" id="N64-j9-hdj"/>
                                                                <constraint firstAttribute="height" constant="80" id="NSk-MM-QpB"/>
                                                                <constraint firstItem="gLe-yT-atb" firstAttribute="centerY" secondItem="3wz-o3-jSl" secondAttribute="centerY" id="RIh-kd-nuI"/>
                                                                <constraint firstItem="gLe-yT-atb" firstAttribute="leading" secondItem="3wz-o3-jSl" secondAttribute="leading" id="aX4-WC-STg"/>
                                                                <constraint firstAttribute="bottom" secondItem="SlK-uW-OBB" secondAttribute="bottom" id="dkI-NL-2Vf"/>
                                                                <constraint firstItem="seu-tJ-xxA" firstAttribute="centerY" secondItem="3wz-o3-jSl" secondAttribute="centerY" id="wh0-ML-Xcr"/>
                                                            </constraints>
                                                        </view>
                                                        <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="V63-r1-eI4">
                                                            <rect key="frame" x="0.0" y="-80" width="345" height="80"/>
                                                            <subviews>
                                                                <switch opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="750" verticalHuggingPriority="750" contentHorizontalAlignment="center" contentVerticalAlignment="center" on="YES" translatesAutoresizingMaskIntoConstraints="NO" id="mEG-3e-8VL">
                                                                    <rect key="frame" x="284" y="24.666666666666671" width="51" height="31"/>
                                                                    <connections>
                                                                        <action selector="announcementMessageAction:" destination="-1" eventType="valueChanged" id="cXH-3V-mUZ"/>
                                                                    </connections>
                                                                </switch>
                                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Announcement Message" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="nTm-AT-03n">
                                                                    <rect key="frame" x="0.0" y="31.666666666666664" width="155.66666666666666" height="17"/>
                                                                    <constraints>
                                                                        <constraint firstAttribute="height" constant="17" id="e1Y-xk-pwn"/>
                                                                    </constraints>
                                                                    <fontDescription key="fontDescription" name="HelveticaNeue" family="Helvetica Neue" pointSize="14"/>
                                                                    <nil key="textColor"/>
                                                                    <nil key="highlightedColor"/>
                                                                </label>
                                                                <view alpha="0.5" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="MPK-q8-UeT">
                                                                    <rect key="frame" x="0.0" y="79" width="345" height="1"/>
                                                                    <color key="backgroundColor" systemColor="systemGray4Color"/>
                                                                    <constraints>
                                                                        <constraint firstAttribute="height" constant="1" id="cDd-EK-8Zk"/>
                                                                    </constraints>
                                                                </view>
                                                            </subviews>
                                                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                            <constraints>
                                                                <constraint firstAttribute="bottom" secondItem="MPK-q8-UeT" secondAttribute="bottom" id="CkF-Xg-iwn"/>
                                                                <constraint firstItem="MPK-q8-UeT" firstAttribute="leading" secondItem="V63-r1-eI4" secondAttribute="leading" id="Ha3-UL-rBi"/>
                                                                <constraint firstAttribute="height" constant="80" id="HyU-MU-Sa2"/>
                                                                <constraint firstItem="nTm-AT-03n" firstAttribute="leading" secondItem="V63-r1-eI4" secondAttribute="leading" id="KJC-lK-PJL"/>
                                                                <constraint firstItem="mEG-3e-8VL" firstAttribute="centerY" secondItem="V63-r1-eI4" secondAttribute="centerY" id="gUC-AA-fQm"/>
                                                                <constraint firstAttribute="trailing" secondItem="MPK-q8-UeT" secondAttribute="trailing" id="kBI-os-SA4"/>
                                                                <constraint firstAttribute="trailing" secondItem="mEG-3e-8VL" secondAttribute="trailing" constant="12" id="rRd-VR-UiB"/>
                                                                <constraint firstItem="nTm-AT-03n" firstAttribute="centerY" secondItem="V63-r1-eI4" secondAttribute="centerY" id="sTl-q2-556"/>
                                                            </constraints>
                                                        </view>
                                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Rs9-kW-OPk">
                                                            <rect key="frame" x="0.0" y="0.0" width="345" height="80"/>
                                                            <subviews>
                                                                <switch opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="750" verticalHuggingPriority="750" contentHorizontalAlignment="center" contentVerticalAlignment="center" on="YES" translatesAutoresizingMaskIntoConstraints="NO" id="uft-qd-0Pg">
                                                                    <rect key="frame" x="284" y="24.666666666666671" width="51" height="31"/>
                                                                    <connections>
                                                                        <action selector="reminderMessageAction:" destination="-1" eventType="valueChanged" id="eNE-8W-fa7"/>
                                                                    </connections>
                                                                </switch>
                                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Reminder Message" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="VfL-dJ-ppb">
                                                                    <rect key="frame" x="0.0" y="31.666666666666671" width="121.33333333333333" height="17"/>
                                                                    <constraints>
                                                                        <constraint firstAttribute="height" constant="17" id="CKT-Bd-ZU5"/>
                                                                    </constraints>
                                                                    <fontDescription key="fontDescription" name="HelveticaNeue" family="Helvetica Neue" pointSize="14"/>
                                                                    <color key="textColor" red="0.20423525570000001" green="0.075626514850000007" blue="0.36441689729999999" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                    <nil key="highlightedColor"/>
                                                                </label>
                                                            </subviews>
                                                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                            <constraints>
                                                                <constraint firstItem="uft-qd-0Pg" firstAttribute="centerY" secondItem="Rs9-kW-OPk" secondAttribute="centerY" id="N3A-5d-Q9d"/>
                                                                <constraint firstAttribute="trailing" secondItem="uft-qd-0Pg" secondAttribute="trailing" constant="12" id="Tg5-iE-AkM"/>
                                                                <constraint firstItem="VfL-dJ-ppb" firstAttribute="centerY" secondItem="Rs9-kW-OPk" secondAttribute="centerY" id="ZRH-vK-ezf"/>
                                                                <constraint firstItem="VfL-dJ-ppb" firstAttribute="leading" secondItem="Rs9-kW-OPk" secondAttribute="leading" id="sRd-yz-48p"/>
                                                                <constraint firstAttribute="height" constant="80" id="ym8-TP-Ghc"/>
                                                            </constraints>
                                                        </view>
                                                        <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="G0N-nn-Age">
                                                            <rect key="frame" x="0.0" y="80" width="345" height="286"/>
                                                            <subviews>
                                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="OYf-hZ-gsv">
                                                                    <rect key="frame" x="0.0" y="0.0" width="345" height="110"/>
                                                                    <subviews>
                                                                        <switch opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="750" verticalHuggingPriority="750" contentHorizontalAlignment="center" contentVerticalAlignment="center" on="YES" translatesAutoresizingMaskIntoConstraints="NO" id="7nB-nT-3fP">
                                                                            <rect key="frame" x="284" y="12" width="51" height="31"/>
                                                                            <connections>
                                                                                <action selector="ReminderMessageSecondAction:" destination="-1" eventType="valueChanged" id="2u6-oX-YNg"/>
                                                                            </connections>
                                                                        </switch>
                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Fertility Notifications" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="mHi-cQ-83N">
                                                                            <rect key="frame" x="12.000000000000007" y="19" width="127.66666666666669" height="17"/>
                                                                            <constraints>
                                                                                <constraint firstAttribute="height" constant="17" id="J61-mS-v7G"/>
                                                                            </constraints>
                                                                            <fontDescription key="fontDescription" name="HelveticaNeue" family="Helvetica Neue" pointSize="14"/>
                                                                            <color key="textColor" red="0.18858519200000001" green="0.057947516439999999" blue="0.3526558876" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                            <nil key="highlightedColor"/>
                                                                        </label>
                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" usesAttributedText="YES" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="lky-EF-TBK">
                                                                            <rect key="frame" x="12" y="44" width="273" height="49"/>
                                                                            <attributedString key="attributedText">
                                                                                <fragment content="Notify you when your fertility window arrives. Notifications are sent at 8 PM.">
                                                                                    <attributes>
                                                                                        <color key="NSColor" red="0.20423525570000001" green="0.075626514850000007" blue="0.36441689729999999" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                                        <font key="NSFont" size="14" name="HelveticaNeue"/>
                                                                                        <paragraphStyle key="NSParagraphStyle" alignment="left" lineBreakMode="wordWrapping" baseWritingDirection="natural" lineHeightMultiple="1.5" tighteningFactorForTruncation="0.0"/>
                                                                                    </attributes>
                                                                                </fragment>
                                                                            </attributedString>
                                                                            <nil key="highlightedColor"/>
                                                                        </label>
                                                                    </subviews>
                                                                    <color key="backgroundColor" red="0.89406855419999998" green="0.89406855419999998" blue="0.89406855419999998" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                    <constraints>
                                                                        <constraint firstAttribute="trailing" secondItem="7nB-nT-3fP" secondAttribute="trailing" constant="12" id="D1u-IM-Q92"/>
                                                                        <constraint firstAttribute="trailing" secondItem="lky-EF-TBK" secondAttribute="trailing" constant="60" id="Pii-oD-4iD"/>
                                                                        <constraint firstItem="7nB-nT-3fP" firstAttribute="top" secondItem="OYf-hZ-gsv" secondAttribute="top" constant="12" id="V2Z-3z-RWh"/>
                                                                        <constraint firstItem="mHi-cQ-83N" firstAttribute="leading" secondItem="OYf-hZ-gsv" secondAttribute="leading" constant="12" id="dar-wu-kob"/>
                                                                        <constraint firstItem="lky-EF-TBK" firstAttribute="top" secondItem="mHi-cQ-83N" secondAttribute="bottom" constant="8" id="dm2-qO-m2O"/>
                                                                        <constraint firstItem="7nB-nT-3fP" firstAttribute="centerY" secondItem="mHi-cQ-83N" secondAttribute="centerY" id="ebI-RN-mCF"/>
                                                                        <constraint firstAttribute="height" constant="110" id="gBC-vJ-Icc"/>
                                                                        <constraint firstItem="lky-EF-TBK" firstAttribute="leading" secondItem="mHi-cQ-83N" secondAttribute="leading" id="vpc-p2-M9a"/>
                                                                    </constraints>
                                                                    <userDefinedRuntimeAttributes>
                                                                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                                            <integer key="value" value="4"/>
                                                                        </userDefinedRuntimeAttribute>
                                                                    </userDefinedRuntimeAttributes>
                                                                </view>
                                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="vXr-At-kaP">
                                                                    <rect key="frame" x="0.0" y="122" width="345" height="164"/>
                                                                    <subviews>
                                                                        <switch opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="750" verticalHuggingPriority="750" contentHorizontalAlignment="center" contentVerticalAlignment="center" on="YES" translatesAutoresizingMaskIntoConstraints="NO" id="vZR-L9-6fb">
                                                                            <rect key="frame" x="284" y="12" width="51" height="31"/>
                                                                            <connections>
                                                                                <action selector="testReminderAction:" destination="-1" eventType="valueChanged" id="MxH-GS-5BV"/>
                                                                            </connections>
                                                                        </switch>
                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Test Reminder" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="g4V-DU-7Oz">
                                                                            <rect key="frame" x="12" y="19" width="90" height="17"/>
                                                                            <constraints>
                                                                                <constraint firstAttribute="height" constant="17" id="Vjp-Sz-n6D"/>
                                                                            </constraints>
                                                                            <fontDescription key="fontDescription" name="HelveticaNeue" family="Helvetica Neue" pointSize="14"/>
                                                                            <color key="textColor" red="0.18858519200000001" green="0.057947516439999999" blue="0.3526558876" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                            <nil key="highlightedColor"/>
                                                                        </label>
                                                                        <view tag="100" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="pbG-cS-Tb4">
                                                                            <rect key="frame" x="12" y="52" width="321" height="24"/>
                                                                            <subviews>
                                                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="LH UItra Test" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Yh2-rF-efU">
                                                                                    <rect key="frame" x="0.0" y="0.0" width="81.666666666666671" height="24"/>
                                                                                    <fontDescription key="fontDescription" name="HelveticaNeue" family="Helvetica Neue" pointSize="14"/>
                                                                                    <color key="textColor" red="0.50876241919999998" green="0.4311534762" blue="0.6075879931" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                                    <nil key="highlightedColor"/>
                                                                                </label>
                                                                                <button opaque="NO" userInteractionEnabled="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="ofa-3Z-CUs">
                                                                                    <rect key="frame" x="305" y="0.0" width="16" height="24"/>
                                                                                    <constraints>
                                                                                        <constraint firstAttribute="width" constant="16" id="q2H-oB-4Yf"/>
                                                                                    </constraints>
                                                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                                    <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                                                    <state key="normal" image="editPan">
                                                                                        <color key="titleColor" white="0.66666666669999997" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                    </state>
                                                                                </button>
                                                                                <button opaque="NO" userInteractionEnabled="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="qrF-p8-IaK">
                                                                                    <rect key="frame" x="243" y="0.0" width="56" height="24"/>
                                                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                                    <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                                                    <state key="normal" title="8:00 Am">
                                                                                        <color key="titleColor" white="0.66666666666666663" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                    </state>
                                                                                </button>
                                                                            </subviews>
                                                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                            <constraints>
                                                                                <constraint firstItem="ofa-3Z-CUs" firstAttribute="leading" secondItem="qrF-p8-IaK" secondAttribute="trailing" constant="6" id="AST-HL-JOo"/>
                                                                                <constraint firstAttribute="height" constant="24" id="BjV-80-nia"/>
                                                                                <constraint firstAttribute="trailing" secondItem="ofa-3Z-CUs" secondAttribute="trailing" id="GXF-Yr-jZV"/>
                                                                                <constraint firstAttribute="bottom" secondItem="Yh2-rF-efU" secondAttribute="bottom" id="OTw-Qq-wLm"/>
                                                                                <constraint firstItem="qrF-p8-IaK" firstAttribute="top" secondItem="pbG-cS-Tb4" secondAttribute="top" id="UXN-rl-dJx"/>
                                                                                <constraint firstItem="Yh2-rF-efU" firstAttribute="top" secondItem="pbG-cS-Tb4" secondAttribute="top" id="UfG-1x-ejm"/>
                                                                                <constraint firstAttribute="bottom" secondItem="qrF-p8-IaK" secondAttribute="bottom" id="hpN-Za-F0b"/>
                                                                                <constraint firstAttribute="bottom" secondItem="ofa-3Z-CUs" secondAttribute="bottom" id="qus-Ts-jLQ"/>
                                                                                <constraint firstItem="ofa-3Z-CUs" firstAttribute="top" secondItem="pbG-cS-Tb4" secondAttribute="top" id="xZU-jt-zef"/>
                                                                                <constraint firstItem="Yh2-rF-efU" firstAttribute="leading" secondItem="pbG-cS-Tb4" secondAttribute="leading" id="xfw-G5-Jah"/>
                                                                            </constraints>
                                                                        </view>
                                                                        <view tag="101" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="WsC-1E-xYv">
                                                                            <rect key="frame" x="12" y="86" width="321" height="24"/>
                                                                            <subviews>
                                                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="PdG Test" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="tAZ-LK-IYg">
                                                                                    <rect key="frame" x="0.0" y="0.0" width="57.333333333333336" height="24"/>
                                                                                    <fontDescription key="fontDescription" name="HelveticaNeue" family="Helvetica Neue" pointSize="14"/>
                                                                                    <color key="textColor" red="0.50876241919999998" green="0.4311534762" blue="0.6075879931" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                                    <nil key="highlightedColor"/>
                                                                                </label>
                                                                                <button opaque="NO" userInteractionEnabled="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="tnj-fm-wTK">
                                                                                    <rect key="frame" x="305" y="0.0" width="16" height="24"/>
                                                                                    <constraints>
                                                                                        <constraint firstAttribute="width" constant="16" id="WHf-Bh-Trq"/>
                                                                                    </constraints>
                                                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                                    <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                                                    <state key="normal" image="editPan">
                                                                                        <color key="titleColor" white="0.66666666669999997" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                    </state>
                                                                                </button>
                                                                                <button opaque="NO" userInteractionEnabled="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="MjR-iO-Rm7">
                                                                                    <rect key="frame" x="243" y="0.0" width="56" height="24"/>
                                                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                                    <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                                                    <state key="normal" title="8:00 Am">
                                                                                        <color key="titleColor" white="0.66666666669999997" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                    </state>
                                                                                </button>
                                                                            </subviews>
                                                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                            <constraints>
                                                                                <constraint firstItem="MjR-iO-Rm7" firstAttribute="top" secondItem="WsC-1E-xYv" secondAttribute="top" id="10K-Q9-dg3"/>
                                                                                <constraint firstAttribute="bottom" secondItem="MjR-iO-Rm7" secondAttribute="bottom" id="1ei-mQ-8aG"/>
                                                                                <constraint firstAttribute="bottom" secondItem="tnj-fm-wTK" secondAttribute="bottom" id="FEE-xJ-wD2"/>
                                                                                <constraint firstAttribute="height" constant="24" id="JUq-Y9-0pB"/>
                                                                                <constraint firstItem="tAZ-LK-IYg" firstAttribute="top" secondItem="WsC-1E-xYv" secondAttribute="top" id="OKx-In-DW4"/>
                                                                                <constraint firstItem="tnj-fm-wTK" firstAttribute="leading" secondItem="MjR-iO-Rm7" secondAttribute="trailing" constant="6" id="Qhh-TE-98t"/>
                                                                                <constraint firstAttribute="bottom" secondItem="tAZ-LK-IYg" secondAttribute="bottom" id="W4C-nY-sPa"/>
                                                                                <constraint firstItem="tnj-fm-wTK" firstAttribute="top" secondItem="WsC-1E-xYv" secondAttribute="top" id="oOO-aF-uuW"/>
                                                                                <constraint firstAttribute="trailing" secondItem="tnj-fm-wTK" secondAttribute="trailing" id="ytb-ux-yfA"/>
                                                                                <constraint firstItem="tAZ-LK-IYg" firstAttribute="leading" secondItem="WsC-1E-xYv" secondAttribute="leading" id="zj2-ff-Z7u"/>
                                                                            </constraints>
                                                                        </view>
                                                                        <view tag="102" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="kd6-vw-Rqn">
                                                                            <rect key="frame" x="12" y="120" width="321" height="24"/>
                                                                            <subviews>
                                                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="FSH Test" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="myA-po-ZGI">
                                                                                    <rect key="frame" x="0.0" y="0.0" width="56.666666666666664" height="24"/>
                                                                                    <fontDescription key="fontDescription" name="HelveticaNeue" family="Helvetica Neue" pointSize="14"/>
                                                                                    <color key="textColor" red="0.50876241919999998" green="0.4311534762" blue="0.6075879931" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                                    <nil key="highlightedColor"/>
                                                                                </label>
                                                                                <button opaque="NO" userInteractionEnabled="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Qfv-nN-taj">
                                                                                    <rect key="frame" x="305" y="0.0" width="16" height="24"/>
                                                                                    <constraints>
                                                                                        <constraint firstAttribute="width" constant="16" id="90p-g5-Udm"/>
                                                                                    </constraints>
                                                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                                    <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                                                    <state key="normal" image="editPan">
                                                                                        <color key="titleColor" white="0.66666666669999997" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                    </state>
                                                                                </button>
                                                                                <button opaque="NO" userInteractionEnabled="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="RVY-MG-Tbk">
                                                                                    <rect key="frame" x="243" y="0.0" width="56" height="24"/>
                                                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                                    <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                                                    <state key="normal" title="8:00 Am">
                                                                                        <color key="titleColor" white="0.66666666669999997" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                    </state>
                                                                                </button>
                                                                            </subviews>
                                                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                            <constraints>
                                                                                <constraint firstAttribute="height" relation="greaterThanOrEqual" constant="24" id="46c-kb-7Kg"/>
                                                                                <constraint firstItem="Qfv-nN-taj" firstAttribute="top" secondItem="kd6-vw-Rqn" secondAttribute="top" id="7lJ-ld-ogR"/>
                                                                                <constraint firstAttribute="bottom" secondItem="RVY-MG-Tbk" secondAttribute="bottom" id="9cU-wV-Ely"/>
                                                                                <constraint firstAttribute="height" constant="24" id="FvD-nS-jGv"/>
                                                                                <constraint firstItem="myA-po-ZGI" firstAttribute="top" secondItem="kd6-vw-Rqn" secondAttribute="top" id="HTu-1H-SAb"/>
                                                                                <constraint firstAttribute="bottom" secondItem="Qfv-nN-taj" secondAttribute="bottom" id="OoN-qV-1rm"/>
                                                                                <constraint firstItem="RVY-MG-Tbk" firstAttribute="top" secondItem="kd6-vw-Rqn" secondAttribute="top" id="RTd-tV-B7T"/>
                                                                                <constraint firstItem="myA-po-ZGI" firstAttribute="leading" secondItem="kd6-vw-Rqn" secondAttribute="leading" id="Smu-sn-c0K"/>
                                                                                <constraint firstAttribute="bottom" secondItem="myA-po-ZGI" secondAttribute="bottom" id="WN5-hH-1cx"/>
                                                                                <constraint firstItem="Qfv-nN-taj" firstAttribute="leading" secondItem="RVY-MG-Tbk" secondAttribute="trailing" constant="6" id="ghk-vf-oV1"/>
                                                                                <constraint firstAttribute="trailing" secondItem="Qfv-nN-taj" secondAttribute="trailing" id="tTd-Np-pVF"/>
                                                                            </constraints>
                                                                        </view>
                                                                    </subviews>
                                                                    <color key="backgroundColor" red="0.89406855419999998" green="0.89406855419999998" blue="0.89406855419999998" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                                    <constraints>
                                                                        <constraint firstAttribute="height" constant="164" id="10E-jy-qXH"/>
                                                                        <constraint firstAttribute="trailing" secondItem="pbG-cS-Tb4" secondAttribute="trailing" constant="12" id="7CU-xG-nHY"/>
                                                                        <constraint firstAttribute="trailing" secondItem="WsC-1E-xYv" secondAttribute="trailing" constant="12" id="8Rq-V4-xj1"/>
                                                                        <constraint firstAttribute="trailing" secondItem="kd6-vw-Rqn" secondAttribute="trailing" constant="12" id="CNP-P4-K3X"/>
                                                                        <constraint firstItem="g4V-DU-7Oz" firstAttribute="centerY" secondItem="vZR-L9-6fb" secondAttribute="centerY" id="Mau-RI-dTJ"/>
                                                                        <constraint firstItem="WsC-1E-xYv" firstAttribute="top" secondItem="pbG-cS-Tb4" secondAttribute="bottom" constant="10" id="Q0u-3y-5BJ"/>
                                                                        <constraint firstItem="kd6-vw-Rqn" firstAttribute="leading" secondItem="vXr-At-kaP" secondAttribute="leading" constant="12" id="a6K-Oe-BLO"/>
                                                                        <constraint firstItem="WsC-1E-xYv" firstAttribute="leading" secondItem="vXr-At-kaP" secondAttribute="leading" constant="12" id="eVC-cS-MSQ"/>
                                                                        <constraint firstItem="pbG-cS-Tb4" firstAttribute="leading" secondItem="vXr-At-kaP" secondAttribute="leading" constant="12" id="mbi-is-JJ4"/>
                                                                        <constraint firstItem="pbG-cS-Tb4" firstAttribute="top" secondItem="g4V-DU-7Oz" secondAttribute="bottom" constant="16" id="oFz-zW-RJ1"/>
                                                                        <constraint firstAttribute="trailing" secondItem="vZR-L9-6fb" secondAttribute="trailing" constant="12" id="qZB-6I-JH4"/>
                                                                        <constraint firstItem="kd6-vw-Rqn" firstAttribute="top" secondItem="WsC-1E-xYv" secondAttribute="bottom" constant="10" id="tXY-iI-LxC"/>
                                                                        <constraint firstItem="vZR-L9-6fb" firstAttribute="top" secondItem="vXr-At-kaP" secondAttribute="top" constant="12" id="x1b-ru-f66"/>
                                                                        <constraint firstItem="g4V-DU-7Oz" firstAttribute="leading" secondItem="vXr-At-kaP" secondAttribute="leading" constant="12" id="xYz-76-Loc"/>
                                                                    </constraints>
                                                                    <userDefinedRuntimeAttributes>
                                                                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                                            <integer key="value" value="4"/>
                                                                        </userDefinedRuntimeAttribute>
                                                                    </userDefinedRuntimeAttributes>
                                                                </view>
                                                            </subviews>
                                                        </stackView>
                                                    </subviews>
                                                </stackView>
                                            </subviews>
                                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <constraints>
                                                <constraint firstItem="SfM-w2-aUM" firstAttribute="top" secondItem="trx-LW-nEF" secondAttribute="top" id="Lrx-6J-YXM"/>
                                                <constraint firstItem="SfM-w2-aUM" firstAttribute="leading" secondItem="trx-LW-nEF" secondAttribute="leading" constant="24" id="YGH-KY-lzA"/>
                                                <constraint firstAttribute="bottom" secondItem="SfM-w2-aUM" secondAttribute="bottom" constant="30" id="ozJ-yW-8wM"/>
                                                <constraint firstAttribute="trailing" secondItem="SfM-w2-aUM" secondAttribute="trailing" constant="24" id="pN4-xQ-8uG"/>
                                            </constraints>
                                        </view>
                                    </subviews>
                                </stackView>
                            </subviews>
                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <constraints>
                                <constraint firstAttribute="trailing" secondItem="Iw9-uP-WgF" secondAttribute="trailing" id="2w4-Gx-QQ2"/>
                                <constraint firstItem="Iw9-uP-WgF" firstAttribute="leading" secondItem="k2o-7T-wGT" secondAttribute="leading" id="shz-rf-pK3"/>
                                <constraint firstItem="Iw9-uP-WgF" firstAttribute="top" secondItem="k2o-7T-wGT" secondAttribute="top" id="uyc-ic-baR"/>
                            </constraints>
                        </view>
                    </subviews>
                    <constraints>
                        <constraint firstItem="k2o-7T-wGT" firstAttribute="width" secondItem="pgh-30-Wdu" secondAttribute="width" id="QDd-ig-EKd"/>
                        <constraint firstItem="k2o-7T-wGT" firstAttribute="leading" secondItem="pgh-30-Wdu" secondAttribute="leading" id="S43-bb-uva"/>
                        <constraint firstAttribute="bottom" secondItem="k2o-7T-wGT" secondAttribute="bottom" id="V5G-p4-XuJ"/>
                        <constraint firstItem="k2o-7T-wGT" firstAttribute="centerY" secondItem="pgh-30-Wdu" secondAttribute="centerY" id="Y5h-12-bo7"/>
                        <constraint firstItem="k2o-7T-wGT" firstAttribute="top" secondItem="pgh-30-Wdu" secondAttribute="top" id="YHa-8c-ZEW"/>
                        <constraint firstAttribute="trailing" secondItem="k2o-7T-wGT" secondAttribute="trailing" id="dpO-Ww-bxC"/>
                    </constraints>
                    <viewLayoutGuide key="contentLayoutGuide" id="iXO-eY-rew"/>
                    <viewLayoutGuide key="frameLayoutGuide" id="gE5-Ra-tJH"/>
                </scrollView>
            </subviews>
            <viewLayoutGuide key="safeArea" id="fnl-2z-Ty3"/>
            <color key="backgroundColor" red="0.9101603627" green="0.88687437769999999" blue="0.85495901110000005" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
            <constraints>
                <constraint firstItem="pgh-30-Wdu" firstAttribute="top" secondItem="fnl-2z-Ty3" secondAttribute="top" id="p7N-bq-qMi"/>
                <constraint firstAttribute="bottom" secondItem="pgh-30-Wdu" secondAttribute="bottom" id="vdX-Se-PAU"/>
                <constraint firstItem="pgh-30-Wdu" firstAttribute="trailing" secondItem="fnl-2z-Ty3" secondAttribute="trailing" id="vhG-2J-ycn"/>
                <constraint firstItem="pgh-30-Wdu" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" id="zDL-fI-VWq"/>
            </constraints>
            <point key="canvasLocation" x="130.53435114503816" y="-12.67605633802817"/>
        </view>
    </objects>
    <resources>
        <image name="editPan" width="16" height="16"/>
        <systemColor name="systemGray4Color">
            <color red="0.81960784310000001" green="0.81960784310000001" blue="0.83921568629999999" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
    </resources>
</document>
