<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="23727" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="23721"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="Gilroy Medium.otf">
            <string><PERSON><PERSON>-Medium</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="SupportAndHelpHomeViewController" customModule="HormoneLife" customModuleProvider="target">
            <connections>
                <outlet property="bottomDonFindAnswerView" destination="pVT-c6-oAV" id="YE5-K1-IwB"/>
                <outlet property="searchBar" destination="QQt-eN-Twy" id="49d-Iw-5Eo"/>
                <outlet property="tableView" destination="1Og-DH-a8K" id="1gG-qw-UL9"/>
                <outlet property="titleCollectionView" destination="Kee-2g-gGG" id="s1l-cd-naD"/>
                <outlet property="view" destination="i5M-Pr-FkT" id="sfx-zR-JGt"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="i5M-Pr-FkT">
            <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="zJL-s1-gIX">
                    <rect key="frame" x="0.0" y="118" width="393" height="112"/>
                    <subviews>
                        <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="GYK-L3-HSg">
                            <rect key="frame" x="0.0" y="0.0" width="393" height="112"/>
                            <subviews>
                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="eo7-4N-qBh">
                                    <rect key="frame" x="0.0" y="0.0" width="393" height="64"/>
                                    <subviews>
                                        <searchBar contentMode="redraw" searchBarStyle="minimal" translatesAutoresizingMaskIntoConstraints="NO" id="QQt-eN-Twy">
                                            <rect key="frame" x="16" y="16" width="361" height="40"/>
                                            <constraints>
                                                <constraint firstAttribute="height" constant="40" id="4Jw-xl-11A"/>
                                                <constraint firstAttribute="height" relation="greaterThanOrEqual" constant="40" id="TqQ-AH-qOZ"/>
                                            </constraints>
                                            <textInputTraits key="textInputTraits"/>
                                            <connections>
                                                <outlet property="delegate" destination="-1" id="lJM-mW-gLL"/>
                                            </connections>
                                        </searchBar>
                                    </subviews>
                                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <constraints>
                                        <constraint firstItem="QQt-eN-Twy" firstAttribute="top" secondItem="eo7-4N-qBh" secondAttribute="top" constant="16" id="52S-k3-GR4"/>
                                        <constraint firstItem="QQt-eN-Twy" firstAttribute="leading" secondItem="eo7-4N-qBh" secondAttribute="leading" constant="16" id="Fw8-Rv-6cS"/>
                                        <constraint firstAttribute="trailing" secondItem="QQt-eN-Twy" secondAttribute="trailing" constant="16" id="nMR-Eo-1yl"/>
                                        <constraint firstAttribute="height" constant="64" id="npK-x7-tI6"/>
                                    </constraints>
                                </view>
                                <collectionView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" dataMode="none" translatesAutoresizingMaskIntoConstraints="NO" id="Kee-2g-gGG">
                                    <rect key="frame" x="0.0" y="64" width="393" height="47"/>
                                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                    <collectionViewFlowLayout key="collectionViewLayout" scrollDirection="horizontal" minimumLineSpacing="10" minimumInteritemSpacing="10" id="1mB-f0-pZM">
                                        <size key="itemSize" width="128" height="128"/>
                                        <size key="headerReferenceSize" width="0.0" height="0.0"/>
                                        <size key="footerReferenceSize" width="0.0" height="0.0"/>
                                        <inset key="sectionInset" minX="0.0" minY="0.0" maxX="0.0" maxY="0.0"/>
                                    </collectionViewFlowLayout>
                                </collectionView>
                                <view alpha="0.29999999999999999" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Tto-g8-Etd">
                                    <rect key="frame" x="0.0" y="111" width="393" height="1"/>
                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <constraints>
                                        <constraint firstAttribute="height" constant="1" id="d0H-vB-EsH"/>
                                    </constraints>
                                </view>
                            </subviews>
                            <constraints>
                                <constraint firstItem="eo7-4N-qBh" firstAttribute="top" secondItem="GYK-L3-HSg" secondAttribute="top" id="CjQ-zv-FbY"/>
                                <constraint firstAttribute="trailing" secondItem="eo7-4N-qBh" secondAttribute="trailing" id="LCZ-Vk-m6I"/>
                                <constraint firstItem="eo7-4N-qBh" firstAttribute="leading" secondItem="GYK-L3-HSg" secondAttribute="leading" id="q2O-w0-3hq"/>
                            </constraints>
                        </stackView>
                    </subviews>
                    <color key="backgroundColor" red="0.94169229269999999" green="0.92880314590000002" blue="0.9607291818" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                    <constraints>
                        <constraint firstItem="GYK-L3-HSg" firstAttribute="leading" secondItem="zJL-s1-gIX" secondAttribute="leading" id="6HD-hV-lhs"/>
                        <constraint firstItem="GYK-L3-HSg" firstAttribute="top" secondItem="zJL-s1-gIX" secondAttribute="top" id="MM5-Qf-gES"/>
                        <constraint firstAttribute="height" constant="112" id="bO4-OR-bIX"/>
                        <constraint firstAttribute="bottom" secondItem="GYK-L3-HSg" secondAttribute="bottom" id="nRC-1t-dOC"/>
                        <constraint firstAttribute="trailing" secondItem="GYK-L3-HSg" secondAttribute="trailing" id="oRu-OG-ZEj"/>
                    </constraints>
                </view>
                <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" showsVerticalScrollIndicator="NO" style="plain" separatorStyle="none" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="28" estimatedSectionHeaderHeight="-1" sectionFooterHeight="28" estimatedSectionFooterHeight="-1" translatesAutoresizingMaskIntoConstraints="NO" id="1Og-DH-a8K">
                    <rect key="frame" x="0.0" y="230" width="393" height="368"/>
                    <color key="backgroundColor" red="0.99999994039999995" green="1" blue="0.99999994039999995" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                    <color key="sectionIndexBackgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <connections>
                        <outlet property="dataSource" destination="-1" id="p4f-mq-I9T"/>
                        <outlet property="delegate" destination="-1" id="bJl-Ma-aAC"/>
                    </connections>
                </tableView>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="pVT-c6-oAV">
                    <rect key="frame" x="20" y="606" width="353" height="148"/>
                    <subviews>
                        <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="hfl-fR-gfb">
                            <rect key="frame" x="24" y="20" width="305" height="88"/>
                            <subviews>
                                <stackView opaque="NO" contentMode="scaleToFill" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="bdF-aV-R5c">
                                    <rect key="frame" x="0.0" y="0.0" width="305" height="40"/>
                                    <subviews>
                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="messageIcon" translatesAutoresizingMaskIntoConstraints="NO" id="QPU-vE-xJu">
                                            <rect key="frame" x="0.0" y="0.0" width="40" height="40"/>
                                            <constraints>
                                                <constraint firstAttribute="height" constant="40" id="CYY-No-V69"/>
                                                <constraint firstAttribute="width" constant="40" id="dIN-3j-uog"/>
                                            </constraints>
                                        </imageView>
                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Can't find the answer?" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="rYU-TF-oCv">
                                            <rect key="frame" x="52" y="0.0" width="201" height="40"/>
                                            <fontDescription key="fontDescription" name="Gilroy-Medium" family="Gilroy" pointSize="14"/>
                                            <color key="textColor" red="0.20423525570000001" green="0.075626514850000007" blue="0.36441689729999999" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                            <nil key="highlightedColor"/>
                                        </label>
                                        <button opaque="NO" userInteractionEnabled="NO" contentMode="scaleToFill" contentHorizontalAlignment="right" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="d9W-Qz-KSK">
                                            <rect key="frame" x="265" y="0.0" width="40" height="40"/>
                                            <constraints>
                                                <constraint firstAttribute="width" constant="40" id="H6P-6v-NI5"/>
                                            </constraints>
                                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                            <state key="normal" image="circleForwardIcon"/>
                                        </button>
                                    </subviews>
                                </stackView>
                                <stackView opaque="NO" contentMode="scaleToFill" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="f7P-w5-1yd">
                                    <rect key="frame" x="0.0" y="40" width="305" height="48"/>
                                    <subviews>
                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Mfs-XF-ANG">
                                            <rect key="frame" x="0.0" y="0.0" width="40" height="48"/>
                                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <constraints>
                                                <constraint firstAttribute="width" constant="40" id="Dgh-Pk-pRe"/>
                                            </constraints>
                                        </view>
                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" usesAttributedText="YES" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="pn5-sx-FDg">
                                            <rect key="frame" x="52" y="0.0" width="253" height="48"/>
                                            <attributedString key="attributedText">
                                                <fragment content="Contact us: App message, <EMAIL>, or call Toll-Free 1-888-444-3657 (9:00 a.m. - 5:30 p.m. CDT, M-F).">
                                                    <attributes>
                                                        <color key="NSColor" red="0.269670099" green="0.1610363424" blue="0.37322980169999997" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                        <font key="NSFont" size="12" name="Times-Roman"/>
                                                        <real key="NSKern" value="0.0"/>
                                                        <paragraphStyle key="NSParagraphStyle" alignment="left" lineBreakMode="wordWrapping" baseWritingDirection="leftToRight" paragraphSpacing="12" defaultTabInterval="36" tighteningFactorForTruncation="0.0">
                                                            <tabStops/>
                                                        </paragraphStyle>
                                                        <color key="NSStrokeColor" red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                        <real key="NSStrokeWidth" value="0.0"/>
                                                    </attributes>
                                                </fragment>
                                            </attributedString>
                                            <nil key="highlightedColor"/>
                                        </label>
                                    </subviews>
                                </stackView>
                            </subviews>
                        </stackView>
                    </subviews>
                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstAttribute="trailing" secondItem="hfl-fR-gfb" secondAttribute="trailing" constant="24" id="7J5-Jr-g6u"/>
                        <constraint firstAttribute="bottom" secondItem="hfl-fR-gfb" secondAttribute="bottom" constant="40" id="C2N-Ge-UCd"/>
                        <constraint firstItem="hfl-fR-gfb" firstAttribute="leading" secondItem="pVT-c6-oAV" secondAttribute="leading" constant="24" id="N2v-7o-gJf"/>
                        <constraint firstAttribute="height" relation="greaterThanOrEqual" constant="148" id="aWc-9u-CmC"/>
                        <constraint firstAttribute="height" constant="148" id="nlV-bd-IJD"/>
                        <constraint firstItem="hfl-fR-gfb" firstAttribute="top" secondItem="pVT-c6-oAV" secondAttribute="top" constant="20" id="ztr-AW-99O"/>
                    </constraints>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                            <integer key="value" value="4"/>
                        </userDefinedRuntimeAttribute>
                    </userDefinedRuntimeAttributes>
                </view>
            </subviews>
            <viewLayoutGuide key="safeArea" id="fnl-2z-Ty3"/>
            <color key="backgroundColor" red="0.94169229269999999" green="0.92880314590000002" blue="0.9607291818" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
            <constraints>
                <constraint firstItem="zJL-s1-gIX" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" id="1mw-n8-S87"/>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="trailing" secondItem="pVT-c6-oAV" secondAttribute="trailing" constant="20" id="4cL-pB-jSM"/>
                <constraint firstItem="pVT-c6-oAV" firstAttribute="top" secondItem="1Og-DH-a8K" secondAttribute="bottom" constant="8" id="6oL-5L-Xec"/>
                <constraint firstItem="pVT-c6-oAV" firstAttribute="bottom" secondItem="fnl-2z-Ty3" secondAttribute="bottom" constant="-30" id="7EK-U6-ZKe"/>
                <constraint firstItem="1Og-DH-a8K" firstAttribute="top" secondItem="zJL-s1-gIX" secondAttribute="bottom" id="CMz-iM-O8h"/>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="trailing" secondItem="1Og-DH-a8K" secondAttribute="trailing" id="Hzi-MD-ehi"/>
                <constraint firstItem="1Og-DH-a8K" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" id="K9R-su-OPA"/>
                <constraint firstItem="zJL-s1-gIX" firstAttribute="top" secondItem="fnl-2z-Ty3" secondAttribute="top" id="PpK-BU-ptG"/>
                <constraint firstItem="pVT-c6-oAV" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" constant="20" id="mLG-Iq-Ll7"/>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="trailing" secondItem="zJL-s1-gIX" secondAttribute="trailing" id="zac-LS-Nr1"/>
            </constraints>
            <point key="canvasLocation" x="130.53435114503816" y="-12.67605633802817"/>
        </view>
    </objects>
    <resources>
        <image name="circleForwardIcon" width="16" height="16"/>
        <image name="messageIcon" width="40" height="40"/>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
