//
//  SupportAndHelpHomeViewController.swift
//  HormoneLife
//
//  Created by Tank on 2024/6/9.
//

import UIKit

class SupportAndHelpHomeViewController: BaseViewController, UITableViewDelegate, UITableViewDataSource {
    
    lazy var bottomButton: UIButton = {
        let button = UIButton(type: .custom)
        //button.backgroundColor = .red
        button.setImage(UIImage(named: "downAron"), for: .normal)
        button.addTarget(self, action: #selector(didTapBottomButton), for: .touchUpInside)
        button.isHidden = true
        return button
    }()
    
    enum SectionType: Int, CaseIterable {
        case question
        case staticCell
    }
    
    let viewModel = SupportOperViewModel()
    let listViewModel = SupportOperListViewModel()
    
    var selectIndex = 0
    
    var searchText: String?
    
    @IBOutlet weak var bottomDonFindAnswerView: UIView!
    @IBOutlet weak var searchBar: UISearchBar!
    //    var collectionView : UICollectionView!
    @IBOutlet weak var titleCollectionView: UICollectionView!
    
    @IBOutlet weak var tableView: UITableView!
            
    override func viewDidLoad() {
        super.viewDidLoad()
        
        navigationItem.title = "Support & Help"
        view.backgroundColor = .themeColor
        setupTableView()
        setupBottomButton()
        self.requestTypeList()
    }
    
    func setupBottomButton() {
        view.addSubview(bottomButton)
        
        bottomButton.snp.makeConstraints { make in
            make.width.height.equalTo(48)
            make.top.equalToSuperview().inset(110)
            make.trailing.equalToSuperview()
        }
        
        bottomButton.isHidden = false
    }
    
    @objc func didTapBottomButton() {
//        print(#function)
        if self.listViewModel.dataSource.count > 4 {
            self.tableView.scrollToRow(at: IndexPath(row: self.listViewModel.dataSource.count - 1, section: 0), at: .bottom, animated: true)
        }
    }
    
    func requestList(category: String) {
        self.listViewModel.getSysOperation(category: category, title: self.searchText) { success in
            self.tableView.reloadData()
        } failure: { error in
            
        }
    }
    
    func requestTypeList() {
        self.viewModel.getSysOperation { [weak self] success in
            
            self!.requestList(category: success.first ?? "")
            
            self?.titleCollectionView.reloadData()
        } failure: { error in
            
        }
    }
    
    private func setupTableView() {
        self.searchBar.delegate = self
        self.searchBar.returnKeyType = .search
        self.searchBar.placeholder = "Enter"
        
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .horizontal
        layout.minimumLineSpacing = 0
        layout.minimumInteritemSpacing = 0
//        self.titleCollectionView.setCollectionViewLayout(layout, animated: false)
//        self.collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        self.titleCollectionView.backgroundColor = .white
        self.titleCollectionView.keyboardDismissMode = .onDrag
        self.titleCollectionView.register(NormalSingleTextCollectionViewCell.classForCoder(), forCellWithReuseIdentifier: NormalSingleTextCollectionViewCell.description())
        self.titleCollectionView.delegate = self
        self.titleCollectionView.dataSource = self
        self.titleCollectionView.showsHorizontalScrollIndicator = false
        self.titleCollectionView.contentInset = .init(top: 0, left: 10, bottom: 0, right: 10)
//        self.titleCollectionView.addSubview(self.collectionView)
//        self.collectionView.snp.makeConstraints { make in
//            make.left.equalTo(0)
//            make.right.equalTo(0)
//            make.top.bottom.equalToSuperview()
//        }
        self.tableView.keyboardDismissMode = .onDrag
        tableView.register(UINib(nibName: "SupportVCQuestionCell", bundle: nil), forCellReuseIdentifier: "SupportVCQuestionCell")
        tableView.register(UINib(nibName: "SupportVCCustomerSupportCellTableViewCell", bundle: nil), forCellReuseIdentifier: "SupportVCCustomerSupportCellTableViewCell")
        tableView.register(UINib(nibName: "SupportVCDontFindAnswerCell", bundle: nil), forCellReuseIdentifier: "SupportVCDontFindAnswerCell")
        tableView.register(EmptyTableViewCell.self, forCellReuseIdentifier: "EmptyTableViewCell")
        
        bottomDonFindAnswerView.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(didTapBottomView)))
    }
    
    @objc func didTapBottomView() {
        let helpVC = SupportAndHelpFeedbackTableViewController()
        navigationController?.pushViewController(helpVC, animated: true)
    }
    
    func numberOfSections(in tableView: UITableView) -> Int {
        1
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        guard let sectionType = SectionType(rawValue: section) else { return 0 }
        if sectionType == .question {
            return self.listViewModel.dataSource.count > 0 ? self.listViewModel.dataSource.count : 1
        } else { return 1 }
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        guard let sectionType = SectionType(rawValue: indexPath.section) else { return UITableViewCell() }
        switch sectionType {
        case .question:
            if self.listViewModel.dataSource.count > 0 {
                let cell = tableView.dequeueReusableCell(withIdentifier: "SupportVCQuestionCell", for: indexPath) as! SupportVCQuestionCell
                let model = self.listViewModel.dataSource[indexPath.row]
                cell.questionTitle.setTitle("\(indexPath.row + 1) · \(model.title ?? "")", for: .normal)
                return cell
            } else {
                guard let cell = tableView.dequeueReusableCell(withIdentifier: "EmptyTableViewCell") as? EmptyTableViewCell else {
                    return UITableViewCell()
                }
                cell.setupCellForSupportAndHelpVC(height: tableView.height())
                return cell
            }
        case .staticCell:
            if indexPath.row == 1 {
                guard let cell = tableView.dequeueReusableCell(withIdentifier: "SupportVCCustomerSupportCellTableViewCell", for: indexPath) as? SupportVCCustomerSupportCellTableViewCell else {
                    return UITableViewCell()
                }
                cell.delegate = self
                return cell
            } else {
                guard let cell = tableView.dequeueReusableCell(withIdentifier: "SupportVCDontFindAnswerCell", for: indexPath) as? SupportVCDontFindAnswerCell else { return UITableViewCell() }
                return cell
            }
        }
    }
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        if indexPath.section == 1 {
            let helpVC = SupportAndHelpFeedbackTableViewController()
            navigationController?.pushViewController(helpVC, animated: true)
        } else {
            let model = self.listViewModel.dataSource[indexPath.row]
            let webView = SuportWebViewController(contentString: model.content)
            webView.titleString = model.title
            webView.operId = model.id
//            webView.refresh(content: model.content)
            navigationController?.pushViewController(webView, animated: true)
        }
    }
}

extension SupportAndHelpHomeViewController : UICollectionViewDelegate, UICollectionViewDataSource , UICollectionViewDelegateFlowLayout {
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let model = self.viewModel.dataSource[indexPath.item]
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: NormalSingleTextCollectionViewCell.description(), for: indexPath) as! NormalSingleTextCollectionViewCell
        cell.titleLab.text = model
        cell.containerView.backgroundColor = .clear
        cell.titleLab.font = .mediumGilroyFont(14)
        cell.lineView.isHidden = (self.selectIndex != indexPath.item)
        if self.selectIndex == indexPath.item {
            
            cell.titleLab.textColor = UIColorFromRGB(rgbValue: 0x360C5E)
        } else {
            cell.titleLab.textColor = UIColorFromRGB(rgbValue: 0x360C5E, alpha: 0.6)
        }
        
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        self.selectIndex = indexPath.item
        self.requestList(category: self.viewModel.dataSource[indexPath.item])
        collectionView.scrollToItem(at: indexPath, at: .centeredHorizontally, animated: true)
        collectionView.reloadData()
    }
    
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return self.viewModel.dataSource.count
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        let model = self.viewModel.dataSource[indexPath.item]
        let width = (String.width(text: model, textHeight: 18, font: .mediumGilroyFont(14)) + 20)
        return CGSize(width: width, height: 47)
    }
}

extension SupportAndHelpHomeViewController: SupportVCCustomerSupportCellTableViewCellDelegate {
    func didTapIns() {
        print(#function)
    }
    
    func didTapFacebook() {
        print(#function)
    }
}

extension SupportAndHelpHomeViewController : UISearchBarDelegate, UITextFieldDelegate {
    func searchBarSearchButtonClicked(_ searchBar: UISearchBar) {
//        print("")
        self.searchText = searchBar.text
        self.view.endEditing(true)
        self.requestList(category: self.viewModel.dataSource[self.selectIndex])
        
    }
}

class NormalSingleTextCollectionViewCell: UICollectionViewCell {
    
    let containerView = UIView()
    let titleLab = UILabel()
    
    let lineView = UIView()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        
        createSubviews()
    }
    
    func createSubviews() {
        self.contentView.layer.masksToBounds = true
        
        containerView.backgroundColor = .clear
        containerView.layer.cornerRadius = 3
        containerView.layer.masksToBounds = true
        contentView.addSubview(containerView)
        containerView.snp.makeConstraints { make in
            make.left.equalTo(5)
            make.right.equalTo(-5)
            make.centerY.equalToSuperview().offset(-4)
            make.height.equalTo(26)
        }
        
        titleLab.textAlignment = .center
        titleLab.text = ""
        titleLab.numberOfLines = 0
        titleLab.textColor = UIColorFromRGB(rgbValue: 0x360C5E)
        titleLab.font = .mediumGilroyFont(12)
        containerView.addSubview(titleLab)
        titleLab.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.height.equalTo(26)
        }
        
        self.lineView.backgroundColor = UIColorFromRGB(rgbValue: 0x360C5E)
        self.contentView.addSubview(self.lineView)
        self.lineView.isHidden = true
        self.lineView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.bottom.equalToSuperview()
//            make.width.equalTo(60)
            make.width.equalTo(self.contentView).multipliedBy(0.6)
            make.height.equalTo(2)
        }
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
}
