<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="21701" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21679"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="Gilroy SemiBold.otf">
            <string><PERSON><PERSON>-SemiBold</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="UnbingPopupViewController">
            <connections>
                <outlet property="accountLabel" destination="ojS-KN-Vh6" id="Owu-Ic-tpd"/>
                <outlet property="titleLabel" destination="ras-eF-ZMM" id="KA8-Yb-e0s"/>
                <outlet property="untieButton" destination="J8k-of-miG" id="OEN-H3-XXF"/>
                <outlet property="view" destination="i5M-Pr-FkT" id="sfx-zR-JGt"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="i5M-Pr-FkT">
            <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="7Uu-rb-cDo">
                    <rect key="frame" x="40" y="220" width="313" height="292"/>
                    <subviews>
                        <imageView hidden="YES" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="bottom" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="messageViewBg" translatesAutoresizingMaskIntoConstraints="NO" id="qYC-1p-G7g">
                            <rect key="frame" x="0.0" y="0.0" width="313" height="292"/>
                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        </imageView>
                        <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="yio-3R-6Kn">
                            <rect key="frame" x="30" y="45" width="253" height="207"/>
                            <subviews>
                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Apple ID" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="ras-eF-ZMM">
                                    <rect key="frame" x="0.0" y="0.0" width="253" height="18.666666666666668"/>
                                    <fontDescription key="fontDescription" name="Gilroy-SemiBold" family="Gilroy" pointSize="16"/>
                                    <color key="textColor" red="0.1925059855" green="0.057953603569999998" blue="0.3526558876" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                    <nil key="highlightedColor"/>
                                </label>
                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="3ce-JX-awF">
                                    <rect key="frame" x="0.0" y="34.***************" width="253" height="30"/>
                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <constraints>
                                        <constraint firstAttribute="height" constant="30" id="w8D-IS-wDR"/>
                                    </constraints>
                                </view>
                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="account label" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="ojS-KN-Vh6">
                                    <rect key="frame" x="0.0" y="80.***************" width="253" height="20.***************"/>
                                    <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                    <color key="textColor" red="0.*****************" green="0.075626514850000007" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                    <nil key="highlightedColor"/>
                                </label>
                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="4iX-l2-UDO">
                                    <rect key="frame" x="0.0" y="117" width="253" height="26"/>
                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                </view>
                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="b2N-q8-dkw">
                                    <rect key="frame" x="0.0" y="159" width="253" height="48"/>
                                    <subviews>
                                        <stackView opaque="NO" contentMode="scaleToFill" distribution="fillProportionally" spacing="18" translatesAutoresizingMaskIntoConstraints="NO" id="2Pz-59-mO1">
                                            <rect key="frame" x="0.0" y="0.0" width="253" height="48"/>
                                            <subviews>
                                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="J8k-of-miG">
                                                    <rect key="frame" x="0.0" y="0.0" width="106" height="48"/>
                                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="16"/>
                                                    <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                    <state key="normal" title="Untie">
                                                        <color key="titleColor" red="0.1925059855" green="0.057953603569999998" blue="0.3526558876" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                    </state>
                                                    <userDefinedRuntimeAttributes>
                                                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                            <integer key="value" value="4"/>
                                                        </userDefinedRuntimeAttribute>
                                                    </userDefinedRuntimeAttributes>
                                                    <connections>
                                                        <action selector="untieButtonClicked:" destination="-1" eventType="touchUpInside" id="HeL-ez-tQ4"/>
                                                    </connections>
                                                </button>
                                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="KTK-4C-So0">
                                                    <rect key="frame" x="124" y="0.0" width="129" height="48"/>
                                                    <color key="backgroundColor" red="0.1925059855" green="0.057953603569999998" blue="0.3526558876" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="16"/>
                                                    <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                    <state key="normal" title="Back">
                                                        <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    </state>
                                                    <userDefinedRuntimeAttributes>
                                                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                            <integer key="value" value="4"/>
                                                        </userDefinedRuntimeAttribute>
                                                    </userDefinedRuntimeAttributes>
                                                    <connections>
                                                        <action selector="backButtonClicked:" destination="-1" eventType="touchUpInside" id="Byy-hL-qPK"/>
                                                    </connections>
                                                </button>
                                            </subviews>
                                        </stackView>
                                    </subviews>
                                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <constraints>
                                        <constraint firstAttribute="trailing" secondItem="2Pz-59-mO1" secondAttribute="trailing" id="72V-MT-NNG"/>
                                        <constraint firstAttribute="bottom" secondItem="2Pz-59-mO1" secondAttribute="bottom" id="9VA-MV-Fzi"/>
                                        <constraint firstItem="2Pz-59-mO1" firstAttribute="leading" secondItem="b2N-q8-dkw" secondAttribute="leading" id="Iqc-bD-GaE"/>
                                        <constraint firstItem="2Pz-59-mO1" firstAttribute="top" secondItem="b2N-q8-dkw" secondAttribute="top" id="S3o-KL-Off"/>
                                        <constraint firstAttribute="height" constant="48" id="wer-wS-MRN"/>
                                    </constraints>
                                </view>
                            </subviews>
                            <constraints>
                                <constraint firstAttribute="bottom" secondItem="b2N-q8-dkw" secondAttribute="bottom" id="DZa-Tc-I7H"/>
                            </constraints>
                        </stackView>
                    </subviews>
                    <viewLayoutGuide key="safeArea" id="gwM-0m-eoo"/>
                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="292" id="28P-3q-CqQ"/>
                        <constraint firstItem="yio-3R-6Kn" firstAttribute="leading" secondItem="7Uu-rb-cDo" secondAttribute="leading" constant="30" id="3nL-lN-G8J"/>
                        <constraint firstAttribute="bottom" secondItem="qYC-1p-G7g" secondAttribute="bottom" id="9CD-CZ-YDz"/>
                        <constraint firstAttribute="trailing" secondItem="yio-3R-6Kn" secondAttribute="trailing" constant="30" id="BpA-75-5G7"/>
                        <constraint firstAttribute="height" relation="greaterThanOrEqual" constant="292" id="Fc4-G7-WAz"/>
                        <constraint firstAttribute="trailing" secondItem="qYC-1p-G7g" secondAttribute="trailing" id="Vri-sQ-HVs"/>
                        <constraint firstItem="yio-3R-6Kn" firstAttribute="top" secondItem="7Uu-rb-cDo" secondAttribute="top" constant="45" id="WwD-Gs-XhH"/>
                        <constraint firstItem="qYC-1p-G7g" firstAttribute="leading" secondItem="7Uu-rb-cDo" secondAttribute="leading" id="XI2-Bp-1MA"/>
                        <constraint firstItem="qYC-1p-G7g" firstAttribute="top" secondItem="7Uu-rb-cDo" secondAttribute="top" id="ihC-kJ-ANM"/>
                        <constraint firstAttribute="bottom" secondItem="yio-3R-6Kn" secondAttribute="bottom" constant="40" id="yY1-tT-hTA"/>
                    </constraints>
                </view>
            </subviews>
            <viewLayoutGuide key="safeArea" id="fnl-2z-Ty3"/>
            <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstItem="7Uu-rb-cDo" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" constant="40" id="Pxa-3N-Ngh"/>
                <constraint firstItem="7Uu-rb-cDo" firstAttribute="centerY" secondItem="i5M-Pr-FkT" secondAttribute="centerY" constant="-60" id="i63-aa-iiZ"/>
                <constraint firstItem="7Uu-rb-cDo" firstAttribute="centerX" secondItem="i5M-Pr-FkT" secondAttribute="centerX" id="vKC-tx-w7F"/>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="trailing" secondItem="7Uu-rb-cDo" secondAttribute="trailing" constant="40" id="xDU-Fp-oux"/>
            </constraints>
            <point key="canvasLocation" x="-68" y="-12"/>
        </view>
    </objects>
    <resources>
        <image name="messageViewBg" width="514" height="514"/>
    </resources>
</document>
