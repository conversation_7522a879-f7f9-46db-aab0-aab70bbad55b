//
//  MessagesViewController.swift
//  HormoneLife
//
//  Created by Tank on 2024/6/9.
//

import UIKit

class MessagesViewController: BaseViewController {

    @IBOutlet weak var tableView: UITableView!
    
    let viewModel = MessageViewModel()
    
    var messages: [Message] = []
    
    override func viewDidLoad() {
        super.viewDidLoad()

        setupNavigationBar()
        setupTableView()
        fetchSystemMessageList()
    }
    
    private func fetchSystemMessageList() {
        UserMessageInteractor.fetchMessages { list in
            guard let messageList = list?.list else { return }
            self.messages = messageList
            self.tableView.reloadData()
        }
    }
    
    private func setupTableView() {
        tableView.separatorStyle = .none
        tableView.keyboardDismissMode = .onDrag
        tableView.rowHeight = UITableView.automaticDimension
        tableView.delegate = self
        tableView.dataSource = self
        
        tableView.register(UINib(nibName: "MessageTableViewCell", bundle: nil), forCellReuseIdentifier: "MessageTableViewCell")
        tableView.register(EmptyTableViewCell.self, forCellReuseIdentifier: "EmptyTableViewCell")
    }
    
    private func setupNavigationBar() {
        navigationItem.title = "Notices"
        
        let button = UIButton(frame: CGRect(x: 0, y: 0, width: 75, height: 16))
        button.setTitle("All read", for: .normal)
        button.titleLabel?.font = .regularGilroyFont(13)
        button.setTitleColor(.labelBackColor, for: .normal)
        button.addTarget(self, action: #selector(didTapNavRightButton), for: .touchUpInside)
        navigationItem.rightBarButtonItem = UIBarButtonItem(customView: button)
    }

    @objc func didTapNavRightButton() {
        
        self.viewModel.allRead { success in
            self.fetchSystemMessageList()
        } failure: { error in
            
        }

//        let messageSetVC = MessageSettingViewController()
//        hidesBottomBarWhenPushed = true
//        navigationController?.pushViewController(messageSetVC, animated: true)
//        hidesBottomBarWhenPushed = true
    }
}

extension MessagesViewController: UITableViewDelegate, UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return messages.count > 0 ? messages.count : 1
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        if messages.count > 0 {
            guard let cell = tableView.dequeueReusableCell(withIdentifier: "MessageTableViewCell", for: indexPath) as? MessageTableViewCell else { return UITableViewCell() }
            
            let message = messages[indexPath.row]
            cell.setupWith(message)
            return cell
        } else {
            guard let cell = tableView.dequeueReusableCell(withIdentifier: "EmptyTableViewCell") as? EmptyTableViewCell else {
                return UITableViewCell()
            }
            cell.setupCell(height: tableView.height())
            return cell
        }
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return UITableView.automaticDimension
    }
    
    func tableView(_ tableView: UITableView, viewForFooterInSection section: Int) -> UIView? {
        return UIView()
    }
    
    func tableView(_ tableView: UITableView, heightForFooterInSection section: Int) -> CGFloat {
        return 20
    }
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        func markIsReadById(_ messageId: String) {
            UserMessageInteractor.markAsReadById(messageId) { result in
                print("read")
            }
        }
        
        UserMessageInteractor.readMessageDetailById(messages[indexPath.row].id) { message in
            guard let message = message else { return }
            let webView = SuportWebViewController(message.url, contentString: message.content)
            webView.titleString = message.title
            webView.operId = message.id
            
            var createTime = ""
            if let sendTime = message.sendTime, sendTime.count > 3 {
                createTime = String(sendTime.dropLast(3))
            } else if message.createTime.count > 3 {
                createTime = String(message.createTime.dropLast(3))
            }
            webView.timeString = createTime.replacingOccurrences(of: "-", with: "/")
            
            webView.vcTitle = "Message Detail"
            markIsReadById(self.messages[indexPath.row].id)
            
            self.hidesBottomBarWhenPushed = true
            self.navigationController?.pushViewController(webView, animated: true)
            self.hidesBottomBarWhenPushed = true
        }
    }
}
