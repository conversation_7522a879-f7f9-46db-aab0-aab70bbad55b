//
//  ChangePasswordViewController.swift
//  HormoneLife
//
//  Created by Tank on 2024/6/9.
//

import UIKit

class ChangePasswordViewController: UIViewController {

    @IBOutlet weak var bgView: UIView!
    @IBOutlet weak var cancelButton: UIButton!
    @IBOutlet weak var saveButton: UIButton!
    @IBOutlet weak var currentPwt: UITextField!
    @IBOutlet weak var newPwt: UITextField!
    @IBOutlet weak var confirmPwt: UITextField!
    
    override func viewDidLoad() {
        super.viewDidLoad()

        bgView.backgroundColor = UIColor(white: 0, alpha: 0.5)
        setupLogoutButton()
        setupUI()
        checkSaveButtonStatus()
    }
    
    private func checkSaveButtonStatus() {
        saveButton.isEnabled = !currentPwt.isEmpty && !newPwt.isEmpty && !confirmPwt.isEmpty
    }
    
    private func setupUI() {
        [currentPwt, newPwt, confirmPwt].forEach {
            $0?.delegate = self
        }
    }

    private func setupLogoutButton() {
        cancelButton.layer.borderColor = UIColor.mainTextColor.cgColor
        cancelButton.layer.borderWidth = 1
        cancelButton.layer.cornerRadius = 4
    }
    
    @IBAction func cancelAction(_ sender: Any) {
        dismiss(animated: true)
    }
    
    @IBAction func saveAction(_ sender: Any) {
        
    }
}

extension ChangePasswordViewController: UITextFieldDelegate {
    func textFieldShouldReturn(_ textField: UITextField) -> Bool {
        textField.resignFirstResponder()
    }
    
    func textFieldDidChangeSelection(_ textField: UITextField) {
        checkSaveButtonStatus()
    }
}
