<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="21701" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21679"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="<PERSON>roy Medium.otf">
            <string><PERSON><PERSON>-Medium</string>
        </array>
        <array key="gilroy regular.otf">
            <string>Gilroy-Regular</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="TagsViewController" customModule="HormoneLife" customModuleProvider="target">
            <connections>
                <outlet property="saveButton" destination="yYB-nN-mJl" id="F0V-uU-T6e"/>
                <outlet property="tagsCollectionView" destination="1RT-Dp-8sN" id="btW-Kh-x8k"/>
                <outlet property="view" destination="i5M-Pr-FkT" id="sfx-zR-JGt"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="i5M-Pr-FkT">
            <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="yYB-nN-mJl">
                    <rect key="frame" x="24" y="750" width="345" height="48"/>
                    <color key="backgroundColor" red="0.18858519200000001" green="0.057947516439999999" blue="0.3526558876" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="48" id="aX3-O2-rm7"/>
                    </constraints>
                    <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="16"/>
                    <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                    <state key="normal" title="Save"/>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                            <integer key="value" value="4"/>
                        </userDefinedRuntimeAttribute>
                    </userDefinedRuntimeAttributes>
                    <connections>
                        <action selector="didTapSaveButtonAction:" destination="-1" eventType="touchUpInside" id="Hik-SK-uDQ"/>
                    </connections>
                </button>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="pR4-Se-pEq">
                    <rect key="frame" x="20" y="79" width="353" height="623"/>
                    <subviews>
                        <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="10" translatesAutoresizingMaskIntoConstraints="NO" id="Pyk-24-MJb">
                            <rect key="frame" x="16" y="20" width="321" height="279"/>
                            <subviews>
                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Share some details about yourself below." lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="pcc-Fw-eto">
                                    <rect key="frame" x="0.0" y="0.0" width="321" height="19"/>
                                    <fontDescription key="fontDescription" name="Gilroy-Medium" family="Gilroy" pointSize="16"/>
                                    <color key="textColor" red="0.18858519200000001" green="0.057947516439999999" blue="0.3526558876" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                    <nil key="highlightedColor"/>
                                </label>
                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="QZ0-JJ-eYY">
                                    <rect key="frame" x="0.0" y="29" width="321" height="200"/>
                                    <subviews>
                                        <collectionView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" dataMode="none" translatesAutoresizingMaskIntoConstraints="NO" id="1RT-Dp-8sN">
                                            <rect key="frame" x="0.0" y="0.0" width="321" height="200"/>
                                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <collectionViewFlowLayout key="collectionViewLayout" minimumLineSpacing="10" minimumInteritemSpacing="10" id="Sb9-BC-FOc">
                                                <size key="itemSize" width="128" height="128"/>
                                                <size key="headerReferenceSize" width="0.0" height="0.0"/>
                                                <size key="footerReferenceSize" width="0.0" height="0.0"/>
                                                <inset key="sectionInset" minX="0.0" minY="0.0" maxX="0.0" maxY="0.0"/>
                                            </collectionViewFlowLayout>
                                        </collectionView>
                                    </subviews>
                                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                    <constraints>
                                        <constraint firstAttribute="trailing" secondItem="1RT-Dp-8sN" secondAttribute="trailing" id="DX1-c5-1xs"/>
                                        <constraint firstAttribute="bottom" secondItem="1RT-Dp-8sN" secondAttribute="bottom" id="TvZ-NU-Rof"/>
                                        <constraint firstItem="1RT-Dp-8sN" firstAttribute="leading" secondItem="QZ0-JJ-eYY" secondAttribute="leading" id="Vgw-GM-Fbb"/>
                                        <constraint firstAttribute="height" constant="200" id="qcq-RO-hFl"/>
                                        <constraint firstItem="1RT-Dp-8sN" firstAttribute="top" secondItem="QZ0-JJ-eYY" secondAttribute="top" id="qzq-Hl-JaY"/>
                                    </constraints>
                                </view>
                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Zkz-XC-EnP">
                                    <rect key="frame" x="0.0" y="239" width="321" height="40"/>
                                    <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                    <state key="normal" image="addTagsButtonPurple"/>
                                    <connections>
                                        <action selector="addTagAction:" destination="-1" eventType="touchUpInside" id="xim-7b-35g"/>
                                    </connections>
                                </button>
                            </subviews>
                        </stackView>
                    </subviews>
                    <color key="backgroundColor" red="0.99999994039999995" green="1" blue="0.99999994039999995" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                    <constraints>
                        <constraint firstItem="Pyk-24-MJb" firstAttribute="top" secondItem="pR4-Se-pEq" secondAttribute="top" constant="20" id="Z9U-tq-QhL"/>
                        <constraint firstAttribute="trailing" secondItem="Pyk-24-MJb" secondAttribute="trailing" constant="16" id="dkg-vv-Q2K"/>
                        <constraint firstItem="Pyk-24-MJb" firstAttribute="leading" secondItem="pR4-Se-pEq" secondAttribute="leading" constant="16" id="vpz-yp-ZBB"/>
                    </constraints>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                            <integer key="value" value="4"/>
                        </userDefinedRuntimeAttribute>
                    </userDefinedRuntimeAttributes>
                </view>
            </subviews>
            <viewLayoutGuide key="safeArea" id="fnl-2z-Ty3"/>
            <color key="backgroundColor" red="0.94169229269999999" green="0.92880314590000002" blue="0.9607291818" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
            <constraints>
                <constraint firstItem="yYB-nN-mJl" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" constant="24" id="FjX-tO-PAV"/>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="bottom" secondItem="yYB-nN-mJl" secondAttribute="bottom" constant="20" id="ceO-8f-jQ9"/>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="trailing" secondItem="pR4-Se-pEq" secondAttribute="trailing" constant="20" id="m9W-y2-K0Z"/>
                <constraint firstItem="yYB-nN-mJl" firstAttribute="top" secondItem="pR4-Se-pEq" secondAttribute="bottom" constant="48" id="oAr-Ok-Jls"/>
                <constraint firstItem="pR4-Se-pEq" firstAttribute="top" secondItem="fnl-2z-Ty3" secondAttribute="top" constant="20" id="p7h-oq-hd6"/>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="trailing" secondItem="yYB-nN-mJl" secondAttribute="trailing" constant="24" id="t8E-kB-uEy"/>
                <constraint firstItem="pR4-Se-pEq" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" constant="20" id="xhd-tB-a5K"/>
            </constraints>
            <point key="canvasLocation" x="-163" y="-12"/>
        </view>
    </objects>
    <resources>
        <image name="addTagsButtonPurple" width="120" height="40"/>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
