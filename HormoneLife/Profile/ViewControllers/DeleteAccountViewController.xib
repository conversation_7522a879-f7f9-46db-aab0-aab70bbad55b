<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="22505" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22504"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="Gilroy Medium.otf">
            <string>Gilroy-Medium</string>
        </array>
        <array key="gilroy regular.otf">
            <string><PERSON><PERSON>-Regular</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="DeleteAccountViewController">
            <connections>
                <outlet property="bgView" destination="2Sj-Qn-nAE" id="Jzu-Zr-yTb"/>
                <outlet property="cancelButton" destination="29v-iy-pE6" id="WB9-Qb-FfH"/>
                <outlet property="view" destination="i5M-Pr-FkT" id="sfx-zR-JGt"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="i5M-Pr-FkT">
            <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="2Sj-Qn-nAE">
                    <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                    <subviews>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="QNd-7n-PiH">
                            <rect key="frame" x="44" y="296" width="305" height="260"/>
                            <subviews>
                                <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="20" translatesAutoresizingMaskIntoConstraints="NO" id="Nti-QO-eta">
                                    <rect key="frame" x="28" y="40" width="249" height="180"/>
                                    <subviews>
                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Delete your account?" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="4Ji-na-vS1">
                                            <rect key="frame" x="0.0" y="0.0" width="249" height="28"/>
                                            <constraints>
                                                <constraint firstAttribute="height" constant="28" id="zfd-dY-3PU"/>
                                            </constraints>
                                            <fontDescription key="fontDescription" name="Gilroy-Medium" family="Gilroy" pointSize="17"/>
                                            <color key="textColor" red="0.*****************" green="0.057947516439999999" blue="0.**********" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                            <nil key="highlightedColor"/>
                                        </label>
                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" usesAttributedText="YES" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="YuG-kJ-g0r">
                                            <rect key="frame" x="0.0" y="48" width="249" height="70"/>
                                            <attributedString key="attributedText">
                                                <fragment content="This will delete your account and all associated with it. Hormonelife will no longer process your personal data.">
                                                    <attributes>
                                                        <color key="NSColor" red="0.*****************" green="0.057947516439999999" blue="0.**********" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                        <font key="NSFont" metaFont="system"/>
                                                        <paragraphStyle key="NSParagraphStyle" alignment="center" lineBreakMode="wordWrapping" baseWritingDirection="natural" lineHeightMultiple="1.5" tighteningFactorForTruncation="0.0"/>
                                                    </attributes>
                                                </fragment>
                                            </attributedString>
                                            <nil key="highlightedColor"/>
                                        </label>
                                        <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="20" translatesAutoresizingMaskIntoConstraints="NO" id="Tnw-zB-iCL">
                                            <rect key="frame" x="0.0" y="138" width="249" height="42"/>
                                            <subviews>
                                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="29v-iy-pE6">
                                                    <rect key="frame" x="0.0" y="0.0" width="114.66666666666667" height="42"/>
                                                    <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="16"/>
                                                    <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                    <state key="normal" title="Cancel">
                                                        <color key="titleColor" red="0.*****************" green="0.057947516439999999" blue="0.**********" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                    </state>
                                                    <connections>
                                                        <action selector="cancelAction:" destination="-1" eventType="touchUpInside" id="Mab-TV-CxG"/>
                                                    </connections>
                                                </button>
                                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Vxh-In-92p">
                                                    <rect key="frame" x="134.66666666666666" y="0.0" width="114.33333333333334" height="42"/>
                                                    <color key="backgroundColor" red="0.*****************" green="0.057947516439999999" blue="0.**********" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                    <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="16"/>
                                                    <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                    <state key="normal" title="Delete"/>
                                                    <userDefinedRuntimeAttributes>
                                                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                            <integer key="value" value="4"/>
                                                        </userDefinedRuntimeAttribute>
                                                    </userDefinedRuntimeAttributes>
                                                    <connections>
                                                        <action selector="saveAction:" destination="-1" eventType="touchUpInside" id="QsJ-c9-fLA"/>
                                                    </connections>
                                                </button>
                                            </subviews>
                                        </stackView>
                                    </subviews>
                                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                </stackView>
                            </subviews>
                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <constraints>
                                <constraint firstAttribute="trailing" secondItem="Nti-QO-eta" secondAttribute="trailing" constant="28" id="bes-gg-mK6"/>
                                <constraint firstAttribute="height" constant="260" id="eAE-5d-Age"/>
                                <constraint firstItem="Nti-QO-eta" firstAttribute="top" secondItem="QNd-7n-PiH" secondAttribute="top" constant="40" id="jZC-P0-wdx"/>
                                <constraint firstItem="Nti-QO-eta" firstAttribute="leading" secondItem="QNd-7n-PiH" secondAttribute="leading" constant="28" id="o62-J6-2gm"/>
                                <constraint firstAttribute="bottom" secondItem="Nti-QO-eta" secondAttribute="bottom" constant="40" id="xNg-Od-CMs"/>
                            </constraints>
                        </view>
                    </subviews>
                    <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstAttribute="trailing" secondItem="QNd-7n-PiH" secondAttribute="trailing" constant="44" id="CHO-2C-Hml"/>
                        <constraint firstItem="QNd-7n-PiH" firstAttribute="leading" secondItem="2Sj-Qn-nAE" secondAttribute="leading" constant="44" id="kjd-DS-H7g"/>
                        <constraint firstItem="QNd-7n-PiH" firstAttribute="centerY" secondItem="2Sj-Qn-nAE" secondAttribute="centerY" id="wCx-hE-2d6"/>
                    </constraints>
                </view>
            </subviews>
            <viewLayoutGuide key="safeArea" id="fnl-2z-Ty3"/>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstItem="2Sj-Qn-nAE" firstAttribute="leading" secondItem="i5M-Pr-FkT" secondAttribute="leading" id="5Ug-14-hUD"/>
                <constraint firstAttribute="trailing" secondItem="2Sj-Qn-nAE" secondAttribute="trailing" id="7Tp-md-8Zn"/>
                <constraint firstAttribute="bottom" secondItem="2Sj-Qn-nAE" secondAttribute="bottom" id="jwT-yH-98N"/>
                <constraint firstItem="2Sj-Qn-nAE" firstAttribute="top" secondItem="i5M-Pr-FkT" secondAttribute="top" id="zwx-Pc-Bj1"/>
            </constraints>
            <point key="canvasLocation" x="-24" y="-12"/>
        </view>
    </objects>
</document>
