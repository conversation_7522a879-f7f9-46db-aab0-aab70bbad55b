//
//  TagsTableViewController.swift
//  HormoneLife
//
//  Created by Tank on 2024/8/4.
//

import UIKit

class TagsTableViewController: BaseViewController, UITableViewDelegate, UITableViewDataSource, TagsTableViewFooterViewDelegate, AddTagViewControllerDelegate {
    
    weak var delegate: PersonalInfoViewControllerDelegate?
//    var tags: [String] = []
    
    let viewModel = SystemTagViewModel()

    private lazy var tableView: UITableView = {
        let tableView = UITableView(frame: .zero, style: .plain)
        tableView.backgroundColor = .themeColor
        tableView.showsVerticalScrollIndicator = false
        tableView.separatorStyle = .none
        
        tableView.register(TagsTableViewCell.self, forCellReuseIdentifier: "TagsTableViewCell")
        tableView.register(TagsTableViewFooterView.self, forHeaderFooterViewReuseIdentifier: "TagsTableViewFooterView")
        
        tableView.dataSource = self
        tableView.delegate = self
        tableView.rowHeight = UITableView.automaticDimension
        return tableView
    }()
    
    lazy var saveButton: UIButton = {
        let b = UIButton(type: .custom)
        b.backgroundColor = .mainTextColor
        b.layer.cornerRadius = 4
        b.setTitle("Save", for: .normal)
        b.titleLabel?.font = .regularGilroyFont(16)
        b.setTitleColor(.white, for: .normal)
        b.addTarget(self, action: #selector(didTapSave), for: .touchUpInside)
        return b
    }()
    
//    var tagsToBeAdded: [String] = ["dsafes", "afeew", "dtwewfw", "ghjiwfhj", "fjdioweflsj", "dsafes", "afeew", "dtwewfw", "ghjiwfhj", "fjdioweflsj", "dsafes", "afeew", "dtwewfw", "ghjiwfhj", "fjdioweflsj", "dsafes", "afeew", "dtwewfw", "ghjiwfhj", "fjdioweflsj"]
    
    var rows: [[String]] {
        return [self.viewModel.selectDataSource, self.viewModel.unselectDataSource]
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        navigationItem.title = "Your Tags"
        setupUI()
        self.getTagsDatas()
    }
    
    func getTagsDatas() {
        self.viewModel.getNotesPage { [weak self] success in
            self?.tableView.reloadData()
        } failure: { error in
            
        }
    }
    
    private func setupUI() {
        view.addSubview(tableView)
        view.addSubview(saveButton)
        saveButton.snp.makeConstraints { make in
            make.height.equalTo(48)
            make.left.right.equalToSuperview().inset(24)
            make.bottom.equalToSuperview().inset(34)
        }
        
        tableView.snp.makeConstraints { make in
            make.left.top.right.equalToSuperview()
            make.bottom.equalTo(saveButton.snp.top)
        }
    }
    
    @objc func didTapSave() {
//        let tagsString = self.viewModel.selectDataSource.joined(separator: ",")
        var selectTags = [String]()
        for item in self.viewModel.selectDataSource {
            let tags = item.components(separatedBy: ",")
            if let last = tags.last {
                selectTags.append(last)
            }
        }
        self.viewModel.selectTags(tagIdList: selectTags) { result in
            self.delegate?.didAddTags(selectTags.joined(separator: ", "))
            self.navigationController?.popViewController(animated: true)
        } failure: { error in
            
        }

//        delegate?.didAddTags(tagsString)

    }
    
    func didTapAdd() {
        let addTagVC = AddTagViewController()
        addTagVC.delegate = self
        addTagVC.modalPresentationStyle = .overFullScreen
        present(addTagVC, animated: true)
    }
    
    func didSelectTag(_ newTag: String) {
//        tags.append(newTag)
//        if self.viewModel.unselectDataSource.contains(newTag) {
//            let leftTags = self.viewModel.unselectDataSource.filter { $0 != newTag }
//            self.viewModel.unselectDataSource = leftTags
//        }
        
        let tags = newTag.components(separatedBy: ",")
        for item in self.viewModel.dataSource {
            if item.tagId == tags.last {
                item.selected = true
            }
        }
        self.viewModel.handleSelectDatas()
        tableView.reloadData()
    }
    
    func didRemoveTag(_ tag: String) {
        let tags = tag.components(separatedBy: ",")
        for item in self.viewModel.dataSource {
            if item.tagId == tags.last {
                item.selected = false
            }
        }
        self.viewModel.handleSelectDatas()
        tableView.reloadData()
    }
    
    func didAddTag(_ newTag: String) {
        self.viewModel.addUserTags(name: newTag, createUserId: Interface.shared().loggedInUser?.userInfo.id ?? "") { [weak self] result in
            self?.getTagsDatas()
        } failure: { error in
            
        }
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        rows.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let row = rows[indexPath.row]
        guard let cell = tableView.dequeueReusableCell(withIdentifier: "TagsTableViewCell", for: indexPath) as? TagsTableViewCell else {
            return UITableViewCell()
        }
        switch indexPath.row {
        case 0:
            cell.setupCell(title: "Tags that have been added", dataSource: row, delegate: self)
            return cell
        default:
            cell.setupCell(title: "Share some details about yourself below", dataSource: row, isAllCornerRaduis: false, delegate: self)
            return cell
        }
    }
    
    func tableView(_ tableView: UITableView, viewForFooterInSection section: Int) -> UIView? {
        guard let view = tableView.dequeueReusableHeaderFooterView(withIdentifier: "TagsTableViewFooterView") as? TagsTableViewFooterView else {
            return nil
        }
        view.delegate = self
        return view
    }
    
    func tableView(_ tableView: UITableView, heightForFooterInSection section: Int) -> CGFloat {
        return 80
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        200
    }
}
