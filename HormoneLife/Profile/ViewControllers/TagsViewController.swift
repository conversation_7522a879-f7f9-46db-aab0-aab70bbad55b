//
//  TagsViewController.swift
//  HormoneLife
//
//  Created by Tank on 2024/6/10.
//
//  No need to used this file

import UIKit

protocol TagsViewControllerDelegate: AnyObject {
    func didAddTag(_ newTag: String)
}

class TagsViewController: BaseViewController {

    @IBOutlet weak var tagsCollectionView: UICollectionView!
    @IBOutlet weak var saveButton: UIButton!
    
    weak var delegate: PersonalInfoViewControllerDelegate?
    var tags: [String] = []
    
    var selectedTagIndex = 0
    
    lazy var layout: LeftAlignedCollectionViewFlowLayout = {
        let layout = LeftAlignedCollectionViewFlowLayout()
        layout.scrollDirection = .vertical
        layout.minimumLineSpacing = 10
        layout.estimatedItemSize = CGSize(width: UIScreen.main.bounds.width - 110, height: 25)
        layout.itemSize = UICollectionViewFlowLayout.automaticSize
        return layout
    }()
    
    override func viewDidLoad() {
        super.viewDidLoad()

        navigationItem.title = "Your Tags"
        setupCollectionView()
        checkLoginButtonStatus()
    }
    
    private func checkLoginButtonStatus() {
        saveButton.isEnabled = !tags.isEmpty
        
        let disableColor = UIColor(red: 68/255, green: 85/255, blue: 113/255, alpha: 1)
        let enableColor = UIColor.mainTextColor
        saveButton.backgroundColor = saveButton.isEnabled ? enableColor : disableColor
    }
    
    private func setupCollectionView() {
        tagsCollectionView.collectionViewLayout = layout
        tagsCollectionView.dataSource = self
        tagsCollectionView.delegate = self
        tagsCollectionView.contentInset = UIEdgeInsets(top: 0, left: 0, bottom: 0, right: 0)
        tagsCollectionView.register(UINib(nibName: "TagsCollectionViewCell", bundle: nil), forCellWithReuseIdentifier: "TagsCollectionViewCell")
        tagsCollectionView.reloadData()
        
    }

    @IBAction func addTagAction(_ sender: UIButton) {
        let addTagVC = AddTagViewController()
        //addTagVC.delegate = self
        addTagVC.modalPresentationStyle = .overFullScreen
        present(addTagVC, animated: true)
    }
    
    @IBAction func didTapSaveButtonAction(_ sender: Any) {
        let tagsString = tags.joined(separator: ",")
        delegate?.didAddTags(tagsString)
        navigationController?.popViewController(animated: true)
    }
}

extension TagsViewController: UICollectionViewDelegate, UICollectionViewDataSource {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return tags.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let tag = tags[indexPath.item]
        guard let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "TagsCollectionViewCell", for: indexPath) as? TagsCollectionViewCell else { return UICollectionViewCell() }
        
        cell.tagsButton.setTitle(tag, for: .normal)
//        cell.itemSelected = false
//        if indexPath.item == selectedTagIndex {
//            cell.itemSelected = true
//        }
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, canEditItemAt indexPath: IndexPath) -> Bool {
        return true
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
//        selectedTagIndex = indexPath.item
//        collectionView.reloadItems(at: [indexPath])
    }
}

extension TagsViewController: TagsViewControllerDelegate {
    func didAddTag(_ newTag: String) {
        tags.append(newTag)
        tagsCollectionView.reloadData()
        checkLoginButtonStatus()
    }
}
