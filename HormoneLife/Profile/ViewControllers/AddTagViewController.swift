//
//  AddTagViewController.swift
//  HormoneLife
//
//  Created by Tank on 2024/6/9.
//

import UIKit

protocol AddTagViewControllerDelegate: AnyObject {
    func didSelectTag(_ newTag: String)
    func didAddTag(_ newTag: String)
    func didRemoveTag(_ tag: String)
}

extension AddTagViewControllerDelegate {
    func didRemoveTag(_ tag: String) {}
}

class AddTagViewController: UIViewController {

    @IBOutlet weak var bgView: UIView!
    @IBOutlet weak var cancelButton: UIButton!
    @IBOutlet weak var tagField: UITextField!
    @IBOutlet weak var saveButton: UIButton!
    
    weak var delegate: AddTagViewControllerDelegate?
    
    override func viewDidLoad() {
        super.viewDidLoad()

        tagField.setAttributedPlaceholer(tagField.placeholder)
        bgView.backgroundColor = UIColor(white: 0, alpha: 0.5)
        setupLogoutButton()
        checkLoginButtonStatus()
        tagField.delegate = self
    }
    
    private func checkLoginButtonStatus() {
        saveButton.isEnabled = !tagField.isEmpty
        
        let disableColor = UIColor(red: 68/255, green: 85/255, blue: 113/255, alpha: 1)
        let enableColor = UIColor.mainTextColor
        saveButton.backgroundColor = saveButton.isEnabled ? enableColor : disableColor
    }

    private func setupLogoutButton() {
        cancelButton.layer.borderColor = UIColor.mainTextColor.cgColor
        cancelButton.layer.borderWidth = 1
        cancelButton.layer.cornerRadius = 4
    }
    
    @IBAction func cancelAction(_ sender: Any) {
        dismiss(animated: true)
    }
    
    @IBAction func saveAction(_ sender: Any) {
        tagField.endEditing(true)
        guard let newTag = tagField.text else { return }
        delegate?.didAddTag(newTag)
        dismiss(animated: true)
    }
}

extension AddTagViewController: UITextFieldDelegate {
    func textFieldShouldReturn(_ textField: UITextField) -> Bool {
        textField.resignFirstResponder()
    }
    
    func textFieldDidChangeSelection(_ textField: UITextField) {
        checkLoginButtonStatus()
    }
}
