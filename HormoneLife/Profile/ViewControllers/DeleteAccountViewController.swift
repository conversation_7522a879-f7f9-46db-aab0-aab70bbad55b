//
//  DeleteAccountViewController.swift
//  HormoneLife
//
//  Created by Tank on 2024/6/9.
//

import UIKit

class DeleteAccountViewController: UIViewController {

    @IBOutlet weak var bgView: UIView!
    @IBOutlet weak var cancelButton: UIButton!
    
    override func viewDidLoad() {
        super.viewDidLoad()

        bgView.backgroundColor = UIColor(white: 0, alpha: 0.5)
        setupLogoutButton()
    }

    private func setupLogoutButton() {
        cancelButton.layer.borderColor = UIColor.mainTextColor.cgColor
        cancelButton.layer.borderWidth = 1
        cancelButton.layer.cornerRadius = 4
    }
    
    @IBAction func cancelAction(_ sender: Any) {
        dismiss(animated: true)
    }
    
    @IBAction func saveAction(_ sender: Any) {
        
        let userInfo = Interface.shared().loggedInUser?.userInfo.phone
        
        UserInteractor.logOffAcount("", code: "", codeType: .phone) { result in
            UserDefaults.standard.userToken = nil
            currentWindow?.rootViewController = UINavigationController(rootViewController: LoginViewController())
        }
    }
}
