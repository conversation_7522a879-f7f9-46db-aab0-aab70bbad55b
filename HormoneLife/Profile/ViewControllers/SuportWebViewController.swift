//
//  SuportWebViewController.swift
//  HormoneLife
//
//  Created by bm on 2024/9/7.
//

import UIKit
import SnapKit
import WebKit

class SuportWebViewController: WebViewController, UIScrollViewDelegate, URLSessionDownloadDelegate {
    
    var titleString : String?
    var timeString: String?
    var operId: String?
    var vcTitle: String? = "Using Hormonelife"
    
    lazy var attarchmentDownloadButton: AttarchmentDownloadButtonView = {
        let v = AttarchmentDownloadButtonView()
        v.isHidden = true
        v.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(didTapAttarchDownload)))
        return v
    }()

    var scrollToBottomCount = -1
    var lastContentOffsetY: CGFloat = 0
    
    let infoViewModel = SupportOperInfoViewModel()
    var downloadFileLocation: URL?
    var downloadFileName: String?
    var hasAttartchment: Bool = false
    
    override func viewDidLoad() {
        super.viewDidLoad()

        self.view.backgroundColor = .white
        webView.backgroundColor = .white
        webView.scrollView.showsVerticalScrollIndicator = false
        webView.navigationDelegate = self
        self.navigationItem.title = vcTitle
        
        let titleLab = UILabel()
        titleLab.textColor = UIColorFromRGB(rgbValue: 0x360C5E)
        titleLab.numberOfLines = 0
        titleLab.font = UIFont(name: "Gilroy-Medium-Medium", size: 16)
        self.view.addSubview(titleLab)
        titleLab.text = self.titleString
        titleLab.snp.makeConstraints { make in
            make.top.equalTo(20)
            make.left.equalTo(26)
            make.right.equalTo(-26)
        }
        
        let timeLab = UILabel()
        timeLab.textColor = .mainTextColor.withAlphaComponent(0.6)
        timeLab.numberOfLines = 0
        timeLab.font = .regularGilroyFont(12)
        self.view.addSubview(timeLab)
        timeLab.text = self.timeString
        timeLab.snp.makeConstraints { make in
            make.top.equalTo(titleLab.snp.bottom).offset(8)
            make.left.equalTo(26)
            make.right.equalTo(-26)
        }
        
        self.webView.snp.remakeConstraints { make in
            make.top.equalTo(timeLab.snp.bottom).offset(16)
            make.left.equalTo(20)
            make.right.equalTo(-20)
            make.bottom.equalToSuperview()
        }
        
        setupAttarchmentDownloadButton()
        fetchAttarchmentInfo()
    }
    
    override func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
        super.webView(webView, didFinish: navigation)
        webView.scrollView.delegate = self
    }
    
    private func setupAttarchmentDownloadButton() {
        view.addSubview(attarchmentDownloadButton)
        attarchmentDownloadButton.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(26)
            make.bottom.equalToSuperview().inset(26)
            make.height.greaterThanOrEqualTo(48)
        }
    }
    
    func openAtActivity() {
        if let file = self.downloadFileLocation {
            DispatchQueue.main.async {
                let activityViewController = UIActivityViewController(activityItems: [file], applicationActivities: nil)
                self.present(activityViewController, animated: true, completion: nil)
            }
        }
    }
    
    @objc func didTapAttarchDownload() {
        if let file = self.downloadFileLocation {
            openAtActivity()
        } else {
            guard let infoData = infoViewModel.dataSource,
                  let attarchmentLink = infoData.accessory,
                  !attarchmentLink.isEmpty else {
                return
            }
            
            showActivityHUD()
            let url = URL(string: attarchmentLink)!
            let session = URLSession(configuration:.default, delegate: self, delegateQueue: nil)
            let task = session.downloadTask(with: url)
            task.resume()
        }
    }
    
    func urlSession(_ session: URLSession, downloadTask: URLSessionDownloadTask, didWriteData bytesWritten: Int64, totalBytesWritten: Int64, totalBytesExpectedToWrite: Int64) {
        
        //let progress = (Float(totalBytesWritten) / Float(totalBytesExpectedToWrite)) * 100
        
        showActivityHUD("downloading...")
    }
    
    func urlSession(_ session: URLSession, task: URLSessionTask, didCompleteWithError error: Error?) {
        if let error = error {
            showToachMessage(message: error.localizedDescription)
            hideActivityHUD()
        } else {
            hideActivityHUD()
        }
    }
    
    func urlSession(_ session: URLSession, downloadTask: URLSessionDownloadTask, didFinishDownloadingTo location: URL) {
        
        if let fileName = self.downloadFileName {
            do {
                let documentsURL = try FileManager.default.url(for:.documentDirectory, in:.userDomainMask, appropriateFor: nil, create: true)
                let destinationURL = documentsURL.appendingPathComponent(fileName)
                
                if !FileManager.default.fileExists(atPath: destinationURL.path) {
                    try FileManager.default.moveItem(at: location, to: destinationURL)
                }
                self.downloadFileLocation = destinationURL
                showToachMessage(message: "download complete")
                self.openAtActivity()
            } catch {
                showToachMessage(message: "\(error)")
            }
        }
    }

    func scrollViewDidScroll(_ scrollView: UIScrollView) {
        let contentHeight = scrollView.contentSize.height
        let visibleHeight = scrollView.bounds.height
        let offsetY = scrollView.contentOffset.y
        if offsetY >= contentHeight - visibleHeight {
            scrollToBottomCount += 1

            if scrollToBottomCount > 0 {
                if hasAttartchment {
                    //attarchmentDownloadButton.isHidden = false
                }
            }
        } else {
            //attarchmentDownloadButton.isHidden = true
        }
    }
    
    func fetchAttarchmentInfo() {
        guard let id = self.operId else { return }
        infoViewModel.getSysOperationInfo(id: id) { info in
            guard let infoData = info,
                  let attarchment = infoData.accessory,
                  !attarchment.isEmpty else {
                self.hasAttartchment = false
                self.attarchmentDownloadButton.isHidden = true
                return
            }
            
            self.hasAttartchment = true
            self.attarchmentDownloadButton.isHidden = false
            self.attarchmentDownloadButton.fileNameLabel.text = infoData.fileName
            
            self.webViewBottomConstraint.constant = 26 + self.attarchmentDownloadButton.height()
            self.view.setNeedsLayout()
            self.view.layoutIfNeeded()
            
            if let fileName = infoData.fileName,
               let type = infoData.fileType {
                if fileName.contains(type) {
                    self.attarchmentDownloadButton.fileNameLabel.text = fileName
                } else {
                    self.attarchmentDownloadButton.fileNameLabel.text = "\(fileName).\(type)"
                }
            } else if let type = attarchment.components(separatedBy: "/").last {
                self.attarchmentDownloadButton.fileNameLabel.text = type
            }
            
            self.downloadFileName = self.attarchmentDownloadButton.fileNameLabel.text
            
            self.attarchmentDownloadButton.cabilityLabel.text = "\(infoData.size ?? "0") B"
            if let size = infoData.size,
               let intSize = Float(size),
                intSize >= 1024 {
                let kbSize = intSize / 1024
                self.attarchmentDownloadButton.cabilityLabel.text = "\(String(format: "%.2f", kbSize)) KB"
                
                if kbSize >= 1024 {
                    let mbSize = kbSize / 1024
                    self.attarchmentDownloadButton.cabilityLabel.text = "\(String(format: "%.2f", mbSize)) MB"
                    
                    if mbSize >= 1024 {
                        let gbSize = mbSize / 1024
                        self.attarchmentDownloadButton.cabilityLabel.text = "\(String(format: "%.2f", gbSize)) GB"
                    }
                }
            }
            
        } failure: { error in
            
        }

    }
}
