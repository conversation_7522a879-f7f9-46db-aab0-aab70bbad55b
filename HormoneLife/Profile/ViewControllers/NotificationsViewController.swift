//
//  NotificationsViewController.swift
//  HormoneLife
//
//  Created by Tank on 2024/6/9.
//

import UIKit

class NotificationsViewController: BaseViewController {

    @IBOutlet weak var tableView: UITableView!
    let messageViewModel = MessageViewModel()
    let reminderViewModel = RemindViewModel()
    
    var message = Message(id: "", title: "Reminder Message", type: "", content: "--", url: "", createTime: "", createBy: "", isRead: 0)
    
    override func viewDidLoad() {
        super.viewDidLoad()

        setupNavigationBar()
        setupTableView()
        
         self.messageViewModel.getUnreadCount { success in
//            self.message = Message(id: "", title: "Reminder Message", type: "", content: "No message", url: "", createTime: "", createBy: "", isRead: 0)
//            self.tableView.reloadData()
        } failure: { error in
            
        }
        
        DispatchQueue.main.asyncAfter(deadline: DispatchTime.now() + 0.1) {
            self.fetchSystemMessageList()
        }
    }
    
    private func fetchSystemMessageList() {
        self.reminderViewModel.pageSize = 4
        self.reminderViewModel.getReminderPage(refresh: true) {[weak self] success in
            let model = success.first
            let time = timeFormat(date: model?.createTime ?? "", format: "yyyy-MM-dd HH:mm:ss", toFormat: "MM/dd yyyy HH:mm")
            self!.message = Message(id: "", title: "Reminder Message", type: "", content: time + " " + (model?.message ?? "No message"), url: "", createTime: "", createBy: "", isRead: 0)
            self!.tableView.reloadData()
        } failure: { error in
            
        }
        
//        UserMessageInteractor.fetchMessages { list in
//            guard let messageList = list?.list else { return }
//            
//            let model = messageList.first
//            
//            let title = model?.title
//            self.message = Message(id: "", title: "Reminder Message", type: "", content: model?.title ?? "", url: "", createTime: "", createBy: "", isRead: 0)
//            self.tableView.reloadData()
//        }
    }
    
    private func setupTableView() {
        tableView.separatorStyle = .none
        tableView.keyboardDismissMode = .onDrag
        tableView.rowHeight = UITableView.automaticDimension
        tableView.delegate = self
        tableView.dataSource = self
        
        tableView.register(UINib(nibName: "MessageTableViewCell", bundle: nil), forCellReuseIdentifier: "MessageTableViewCell")
        tableView.register(UINib(nibName: "NoticeTableViewCell", bundle: nil), forCellReuseIdentifier: "NoticeTableViewCell")
        
    }

    private func setupNavigationBar() {
        navigationItem.title = "Notifications"
        navigationItem.rightBarButtonItem = UIBarButtonItem(image: UIImage(named: "messageSettingIcon"), style: .done, target: self, action: #selector(didTapNavRightButton))
    }

    @objc func didTapNavRightButton() {
        let messageSetVC = MessageSettingViewController()
        hidesBottomBarWhenPushed = true
        navigationController?.pushViewController(messageSetVC, animated: true)
        hidesBottomBarWhenPushed = true
    }
}

extension NotificationsViewController: UITableViewDelegate, UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return 2
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        
        if indexPath.row == 0 {
            guard let cell = tableView.dequeueReusableCell(withIdentifier: "NoticeTableViewCell", for: indexPath) as? NoticeTableViewCell else { return UITableViewCell() }
            cell.setupWith("Notices", numberText: "\(self.messageViewModel.countModel.messageCount)", isButtonHidden: false, isNumberLabelHidden: self.messageViewModel.countModel.messageCount <= 0)
            return cell
        } else {
            guard let cell = tableView.dequeueReusableCell(withIdentifier: "MessageTableViewCell", for: indexPath) as? MessageTableViewCell else { return UITableViewCell() }
            cell.setupWith(message, cellType: .reminder, isNumberLabelHidden: self.messageViewModel.countModel.reminderCount <= 0)
            
            return cell
        }
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        
        if indexPath.row == 0 {
            return 72
        } else {
            return UITableView.automaticDimension
        }
    }
    
    func tableView(_ tableView: UITableView, viewForFooterInSection section: Int) -> UIView? {
        return UIView()
    }
    
    func tableView(_ tableView: UITableView, heightForFooterInSection section: Int) -> CGFloat {
        return 20
    }
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        if indexPath.row == 0 {
            let notiVC = MessagesViewController()
            navigationController?.pushViewController(notiVC, animated: true)
        } else {
            let reminderVC = ReminderViewController()
            navigationController?.pushViewController(reminderVC, animated: true)
        }
    }
}
