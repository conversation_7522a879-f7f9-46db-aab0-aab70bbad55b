//
//  PersonalInfoViewController.swift
//  HormoneLife
//
//  Created by Tank on 2024/6/9.
//

import UIKit

protocol PersonalInfoViewControllerDelegate: AnyObject {
    func didAddTags(_ newTags: String)
}

class PersonalInfoViewController: PickPhotoViewController, PickPhotoDelegate, PeriodPickerViewDelegate {
    // textField.tag = 50 - 56
    
    let systemViewModel = CommonViewModel()
    let tagViewModel = SystemTagViewModel()
    
    @IBOutlet weak var firstNameField: UITextField!
    @IBOutlet weak var lastNameField: UITextField!
    @IBOutlet weak var birthdayField: UITextField!
    @IBOutlet weak var tagsField: UITextField!
    @IBOutlet weak var firstAverage: UITextField!
    @IBOutlet weak var secondAverage: UITextField!
    @IBOutlet weak var trackingOvulation: UITextField!
    var editingTextField: UITextField?
    var editingTextFieldOriginalValue: String?
    
    @IBOutlet weak var avatarImg: UIImageView!
    @IBOutlet weak var saveButton: UIButton!
    
    @IBOutlet weak var firstAverageDaysLabel: UILabel!
    @IBOutlet weak var secondAverageDaysLabel: UILabel!
    
    var trackMethod : String?
    var otherTrackMethod : String?
    //lazy var uploadViewModel = UploadFileViewModel()

    // 用于跟踪当前选中的值，但不立即更新UI
    var selectedPeriodValue: String?
    var selectedCycleValue: String?
    
    lazy var periodLengthPickerView: PeriodPickerView = {
        let pickerView = PeriodPickerView(frame: CGRect(x: 0, y: 0, width: UIScreen.main.bounds.width, height: 372))
        pickerView.delegate = self
        pickerView.dataSource = periodLengthOptions
        pickerView.tag = 100
        return pickerView
    }()
    
    lazy var averageLengthPickerView: PeriodPickerView = {
        let pickerView = PeriodPickerView(frame: CGRect(x: 0, y: 0, width: UIScreen.main.bounds.width, height: 372))
        pickerView.delegate = self
        pickerView.dataSource = averageLengthOptions
        pickerView.tag = 101
        return pickerView
    }()
    
    var periodLengthOptions: [String] = {
        let minute = 1...15
        let array = minute.map { String(format: "%02d", $0) }
        return array
    }()
    
    var averageLengthOptions: [String] = {
        let minute = 15...50
        let array = minute.map { String(format: "%02d", $0) }
        return array
    }()
    
    lazy var datePicker: UIDatePicker = {
        let d = UIDatePicker(frame: CGRect(x: 0, y: 0, width: UIScreen.main.bounds.width, height: 372))
        d.datePickerMode = .date
        d.date = Date(timeIntervalSince1970: 946684800)
        d.preferredDatePickerStyle = .wheels
        d.maximumDate = Date()//before16YearsDate
        d.addTarget(self, action: #selector(dateChanged(_:)), for: .valueChanged)
        return d
    }()
    
    var before16YearsDate: Date {
        let currentDate = Date()
        let calendar = Calendar.current
        let components = DateComponents(year: -16)
        if let newDate = calendar.date(byAdding: components, to: currentDate) {
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "yyyy - MM - dd"
            print(dateFormatter.string(from: newDate))
            return newDate
        } else {
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "yyyy - MM - dd"
            print(dateFormatter.string(from: Date()))
            return Date()
        }
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        pickPhotoDelegate = self
        setupNavigationBar()
        setupTextFieldDelegate()
        self.requestSysDicDatas { result in
            self.setupOptionValueForTrackMethod()
        }
        
        getTagsDatas()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        setupUI()
        // 每次页面出现时刷新数据显示
        refreshUserData()
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        didTapView()
    }
    
    private func setupUI() {
        avatarImg.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(pickPhoto)))
        
        firstAverageDaysLabel.font = .regularGilroyFont(14)
        secondAverageDaysLabel.font = .regularGilroyFont(14)
        
        [firstNameField, lastNameField, birthdayField, tagsField, firstAverage, secondAverage, trackingOvulation].forEach {
            $0.setAttributedPlaceholer($0.placeholder)
        }
        firstAverage.inputView = periodLengthPickerView
        secondAverage.inputView = averageLengthPickerView
        birthdayField.inputView = datePicker
        
        view.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(didTapView)))
    }
    
    @objc func dateChanged(_ sender: UIDatePicker) {
        let selectedDate = sender.date
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "MM/dd yyyy"
        let dateString = dateFormatter.string(from: selectedDate)
        birthdayField.text = dateString
    }
    
    func requestSysDicDatas(success: @escaping (_ result: [SystemDictionaryModel]) -> Void) {
        self.systemViewModel.getSystemDictionary(type: "ovulation_methods") { result in
            success(result)
        } failure: { error in
            success([SystemDictionaryModel]())
        }
    }
    
    private func setupOptionValueForTrackMethod() {
        guard let user = Interface.shared().loggedInUser?.userInfo else { return }
        if let method = user.userBusinessConfigVO.trackMethod {
            self.trackingOvulation.text = method
            
            func indexOfMethod(option: String) -> Int {
                switch option {
                case "B": return 1
                case "C": return 2
                case "D": return 3
                default:
                    return 0
                }
            }
            
            let index = indexOfMethod(option: method)
            guard self.systemViewModel.dataSource.count > index else { return }
            self.trackingOvulation.text = self.systemViewModel.dataSource[index].dictLabel
        }
    }
    
    @objc func didTapView() {
        view.endEditing(true)
    }
    
    func didSelectValue(_ value: String) {
        // 只保存选中的值，不立即更新UI
        if editingTextField == firstAverage {
            selectedPeriodValue = value
        } else if editingTextField == secondAverage {
            selectedCycleValue = value
        }
    }

    func didTapSave(_ value: String) {
        // 点击保存按钮时才更新UI和调用API
        if editingTextField == firstAverage, let selectedValue = selectedPeriodValue {
            editingTextField?.text = selectedValue
            savePeriodConfig(period: selectedValue)
        } else if editingTextField == secondAverage, let selectedValue = selectedCycleValue {
            editingTextField?.text = selectedValue
            saveCycleConfig(menstruationCycleAvg: selectedValue)
        }
        didTapView()
    }

    func didTapCancel() {
        editingTextField?.text = editingTextFieldOriginalValue
        // 清除临时选中的值
        selectedPeriodValue = nil
        selectedCycleValue = nil
        didTapView()
    }
    
    private func setupNavigationBar() {
        navigationItem.title = "Personal Info"
    }
    
    private func setupTextFieldDelegate() {
        let fields = [firstNameField, lastNameField, birthdayField, tagsField, firstAverage, secondAverage, trackingOvulation]
        fields.forEach {
            $0?.delegate = self
        }

        // 使用统一的数据刷新方法
        refreshUserData()
    }
    
    func savePersonalInfo() {
        let birthdayFormat = hl_timeFormat(date: birthdayField.text ?? "", format: "MM/dd yyyy", toFormat: "yyyy-MM-dd HH:mm:ss")
        UserInteractor.updateUserInfo(userName: lastNameField.text ?? "", firstName: firstNameField.text ?? "", sex: 0, birthday: birthdayFormat, headImg: self.uploadViewModel.url, label: tagsField.text ?? "", photos: "") { result in
            hl_fetchUserInfo {
                // 用户信息更新后，刷新页面数据显示
                self.refreshUserData()
            }
        }

        // 个人设置页面不传入startPeriodDate，避免后端日期验证错误
        var config = UserConfig()
        // config.startPeriodDate = userDay  // 注释掉，不传入startPeriodDate
        config.trackMethod = self.trackMethod
        config.otherTrackMethod = self.otherTrackMethod
        config.period = self.firstAverage.text
        config.menstruationCycleAvg = self.secondAverage.text
        UserInteractor.userConfigUpdate(config) { success in
            if success {
                // 配置更新成功后，刷新周期数据
                self.getCycleAndPeriodAvg()
            }
        }
    }
    
    @IBAction func didTapSaveAction(_ sender: UIButton) {
        guard let lastName = lastNameField.text,
              let firstName = firstNameField.text,
              let birthday = birthdayField.text,
              let tag = tagsField.text else { return }
        
        let birthdayFormat = hl_timeFormat(date: birthday, format: "MM/dd yyyy", toFormat: "yyyy-MM-dd HH:mm:ss")
        UserInteractor.updateUserInfo(userName: lastName, firstName: firstName, sex: 0, birthday: birthdayFormat, headImg: self.uploadViewModel.url, label: tag, photos: "") { result in
            hl_fetchUserInfo {
                // 用户信息更新后，刷新页面数据显示
                self.refreshUserData()
                self.navigationController?.popViewController(animated: true)
            }
        }
        // 个人设置页面不传入startPeriodDate，避免后端日期验证错误
        var config = UserConfig()
        // config.startPeriodDate = userDay  // 注释掉，不传入startPeriodDate
        config.trackMethod = self.trackMethod
        config.otherTrackMethod = self.otherTrackMethod
        config.period = self.firstAverage.text
        config.menstruationCycleAvg = self.secondAverage.text
        UserInteractor.userConfigUpdate(config) { success in
            if success {
                // 配置更新成功后，刷新周期数据
                self.getCycleAndPeriodAvg()
            }
        }
    }
    
    func callBackMediaURL(mediaURL: NSURL) {
        print("mediaURL: ", mediaURL)
    }
    
    func callBackImage(photo: UIImage) {
        avatarImg.image = photo
        self.uploadViewModel.uploadImages(images: photo) { (url, fileName)  in
            self.savePersonalInfo()
        } failure: { error in
            
        }
    }
    
    @IBAction func didTapFirstAvarageIcon(_ sender: Any) {
        firstAverage.becomeFirstResponder()
    }
    
    @IBAction func didTapSecondAvarageIcon(_ sender: Any) {
        secondAverage.becomeFirstResponder()
    }
    
    @IBAction func didTapOvulationIcon(_ sender: Any) {
        
        if self.systemViewModel.dataSource.count > 0 {
            
            let popup = CalendarPopupSelectionViewController(rowType: .ovulationTracking, delegate: self, isMultipleChoice: true)
            popup.modalPresentationStyle = .overFullScreen
            popup.buttonsTitle = self.systemViewModel.tagsInfos
            navigationController?.present(popup, animated: true)
        } else {
            self.requestSysDicDatas { result in
                let popup = CalendarPopupSelectionViewController(rowType: .ovulationTracking, delegate: self, isMultipleChoice: true)
                popup.modalPresentationStyle = .overFullScreen
                popup.buttonsTitle = self.systemViewModel.tagsInfos
                self.navigationController?.present(popup, animated: true)
            }
        }
    }
    
    @IBAction func didTapBirthdayField(_ sender: Any) {
//        popCanlendar { date in
//            self.birthdayField.text = date.format(with: "MM/dd yyyy")
//        }
        
        if let birth = birthdayField.text, !birth.isEmpty {
            let birthN = "\(birth) 00:00:00"
            let birthD = birthN.covertDate(with: "MM/dd yyyy HH:mm:ss")
            datePicker.date = birthD
        } else {
            datePicker.date = Date(timeIntervalSince1970: 946684800)
        }
        birthdayField.becomeFirstResponder()
    }
    
    func getTagsDatas() {
        tagViewModel.getNotesPage { [weak self] success in
            self?.tagsField.text = self?.tagViewModel.selectDataSource.map { $0.components(separatedBy: ",").first ?? "" }.joined(separator: ", ")
        } failure: { error in

        }
    }

    // MARK: - 数据刷新方法

    /// 获取周期和月经平均数据 (已废弃，直接使用userBusinessConfigVO数据)
    private func getCycleAndPeriodAvg() {
        // 不再需要单独获取cycleAndPeriodAvg，直接使用userBusinessConfigVO中的数据
        // UserInteractor.getCycleAndPeriodAvg { [weak self] info in
        //     guard let userInfo = info else { return }
        //     Interface.shared().loggedInUser?.cycleAndPeriodAvg = userInfo
        //     // 更新UI显示
        //     DispatchQueue.main.async {
        //         self?.refreshUserData()
        //     }
        // }
    }

    /// 刷新用户数据显示
    private func refreshUserData() {
        guard let user = Interface.shared().loggedInUser?.userInfo else { return }

        // 更新基础信息
        avatarImg.sd_setImage(with: URL(string: user.headImg ?? ""), placeholderImage: UIImage(named: "peopleCircle"), context: nil)
        firstNameField.text = user.firstName
        lastNameField.text = user.userName
        birthdayField.text = hl_timeFormat(date: user.birthday ?? "", format: "yyyy-MM-dd HH:mm:ss", toFormat: "MM/dd yyyy")
        tagsField.text = user.label

        // 更新周期数据 - 直接使用userBusinessConfigVO中的最新数据
        let businessConfig = user.userBusinessConfigVO
        self.secondAverageDaysLabel.isHidden = !(businessConfig.menstruationCycleAvg > 0)
        self.firstAverageDaysLabel.isHidden = !(businessConfig.period > 0)

        if businessConfig.menstruationCycleAvg > 0 {
            self.secondAverage.text = "\(businessConfig.menstruationCycleAvg)"
        } else {
            self.secondAverage.text = ""
        }

        if businessConfig.period > 0 {
            self.firstAverage.text = "\(businessConfig.period)"
        } else {
            self.firstAverage.text = ""
        }

        // 更新追踪方法 - 使用映射后的文案
        updateTrackingOvulationDisplay()
    }

    /// 更新tracking ovulation显示，将字母代码映射为文案
    private func updateTrackingOvulationDisplay() {
        guard let user = Interface.shared().loggedInUser?.userInfo,
              let method = user.userBusinessConfigVO.trackMethod else { return }

        // 如果系统字典数据还没有加载，先加载再更新显示
        if self.systemViewModel.dataSource.isEmpty {
            self.requestSysDicDatas { [weak self] result in
                self?.updateTrackingOvulationDisplayWithData(method: method)
            }
        } else {
            updateTrackingOvulationDisplayWithData(method: method)
        }
    }

    /// 使用已加载的系统字典数据更新显示
    private func updateTrackingOvulationDisplayWithData(method: String) {
        // 直接在系统字典数据中查找对应的dictValue
        if let matchedItem = self.systemViewModel.dataSource.first(where: { $0.dictValue == method }) {
            self.trackingOvulation.text = matchedItem.dictLabel
        } else {
            // 如果没有找到对应的映射，使用原来的索引映射方式作为备选
            func indexOfMethod(option: String) -> Int {
                switch option {
                case "B": return 1
                case "C": return 2
                case "D": return 3
                case "E": return 4
                default:
                    return 0
                }
            }

            let index = indexOfMethod(option: method)
            if self.systemViewModel.dataSource.count > index {
                self.trackingOvulation.text = self.systemViewModel.dataSource[index].dictLabel
            } else {
                // 最后的备选方案：显示原始值
                self.trackingOvulation.text = method
            }
        }
    }

    // MARK: - 单独保存配置方法

    /// 保存月经持续天数配置
    private func savePeriodConfig(period: String) {
        var config = UserConfig()
        config.period = period

        UserInteractor.userConfigUpdate(config) { [weak self] success in
            if success {
                print("Period配置保存成功: \(period)")
                // 保存成功后刷新用户信息，获取最新的userBusinessConfigVO数据
                hl_fetchUserInfo {
                    DispatchQueue.main.async {
                        self?.refreshUserData()
                    }
                }
            } else {
                print("Period配置保存失败")
                // 保存失败，恢复原值
                DispatchQueue.main.async {
                    self?.editingTextField?.text = self?.editingTextFieldOriginalValue
                }
            }
        }
    }

    /// 保存月经周期长度配置
    private func saveCycleConfig(menstruationCycleAvg: String) {
        var config = UserConfig()
        config.menstruationCycleAvg = menstruationCycleAvg

        UserInteractor.userConfigUpdate(config) { [weak self] success in
            if success {
                print("MenstruationCycleAvg配置保存成功: \(menstruationCycleAvg)")
                // 保存成功后刷新用户信息，获取最新的userBusinessConfigVO数据
                hl_fetchUserInfo {
                    DispatchQueue.main.async {
                        self?.refreshUserData()
                    }
                }
            } else {
                print("MenstruationCycleAvg配置保存失败")
                // 保存失败，恢复原值
                DispatchQueue.main.async {
                    self?.editingTextField?.text = self?.editingTextFieldOriginalValue
                }
            }
        }
    }
}

extension PersonalInfoViewController: UITextFieldDelegate {
    func textFieldShouldBeginEditing(_ textField: UITextField) -> Bool {
        switch textField.tag {
        case 53:
            textField.resignFirstResponder()
            let tagsVC = TagsTableViewController()
            tagsVC.delegate = self
            if !tagsField.isEmpty, let tagString = tagsField.text {
                let existTags = tagString.components(separatedBy: ",")
//                tagsVC.self.viewModel.selectDataSource = existTags
            }
            hidesBottomBarWhenPushed = true
            navigationController?.pushViewController(tagsVC, animated: true)
            hidesBottomBarWhenPushed = true
        case 54, 55:
            editingTextFieldOriginalValue = textField.text
            editingTextField = textField

            // 设置picker的初始值
            if textField == firstAverage {
                if let currentValue = textField.text, !currentValue.isEmpty {
                    periodLengthPickerView.currentSelectedValue = currentValue
                    selectedPeriodValue = currentValue
                }
            } else if textField == secondAverage {
                if let currentValue = textField.text, !currentValue.isEmpty {
                    averageLengthPickerView.currentSelectedValue = currentValue
                    selectedCycleValue = currentValue
                }
            }
        default:
            return true
        }
        return true
    }
    
    func textFieldDidEndEditing(_ textField: UITextField) {
        // 只有非周期相关的字段才自动保存
        if textField != firstAverage && textField != secondAverage {
            savePersonalInfo()
        }
    }
}

extension PersonalInfoViewController: PersonalInfoViewControllerDelegate {
    func didAddTags(_ newTags: String) {
        getTagsDatas()
        //tagsField.text = newTags
//        UserInteractor.updateUserInfo(userName: "", firstName: "", sex: 0, birthday: "", headImg: "", label: newTags, photos: "") { result in
//            hl_fetchUserInfo()
//        }
    }
}

extension PersonalInfoViewController: CalendarPopupSelectionViewControllerDelegate {
    func didClose() {
        
    }
    
    func didSave(value: [String]) {
        var tags = [String]()
        var codes = [String]()
        for item in value {
            if let index = Int(item) {
                
                if item.hasSuffix(",other") {
                    tags.append("F")
                    codes.append(item.replacingOccurrences(of: ",other", with: ""))
                    self.otherTrackMethod = item.replacingOccurrences(of: ",other", with: "")
                } else {
                    let model = self.systemViewModel.dataSource[index]
                    tags.append(model.dictLabel ?? "")
                    codes.append(model.dictValue ?? "")
                }
            }
        }
        self.trackMethod = codes.joined(separator: ",")
        trackingOvulation.text = tags.joined(separator: ", ")
        savePersonalInfo()
    }
}
