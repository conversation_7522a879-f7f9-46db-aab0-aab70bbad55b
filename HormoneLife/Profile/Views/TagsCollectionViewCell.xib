<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="21701" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21679"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" id="gTV-IL-0wX" customClass="TagsCollectionViewCell" customModule="HormoneLife" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="335" height="88"/>
            <autoresizingMask key="autoresizingMask"/>
            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center">
                <rect key="frame" x="0.0" y="0.0" width="335" height="88"/>
                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ubD-ku-ces">
                        <rect key="frame" x="0.0" y="0.0" width="335" height="88"/>
                        <subviews>
                            <button opaque="NO" userInteractionEnabled="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="bzz-1R-C54">
                                <rect key="frame" x="5" y="0.0" width="325" height="88"/>
                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                            </button>
                        </subviews>
                        <color key="backgroundColor" red="0.38800710440000002" green="0.13929319379999999" blue="0.90519392489999995" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                        <constraints>
                            <constraint firstAttribute="trailing" secondItem="bzz-1R-C54" secondAttribute="trailing" constant="5" id="8Uq-AL-Vh3"/>
                            <constraint firstItem="bzz-1R-C54" firstAttribute="leading" secondItem="ubD-ku-ces" secondAttribute="leading" constant="5" id="B2N-Vi-fDn"/>
                            <constraint firstAttribute="bottom" secondItem="bzz-1R-C54" secondAttribute="bottom" id="KoX-s7-LWi"/>
                            <constraint firstItem="bzz-1R-C54" firstAttribute="top" secondItem="ubD-ku-ces" secondAttribute="top" id="QXJ-c2-K2Y"/>
                        </constraints>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                <integer key="value" value="4"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </view>
                </subviews>
            </view>
            <viewLayoutGuide key="safeArea" id="ZTg-uK-7eu"/>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstItem="ubD-ku-ces" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" id="0An-o2-L2R"/>
                <constraint firstAttribute="bottom" secondItem="ubD-ku-ces" secondAttribute="bottom" id="LGw-jg-Tqn"/>
                <constraint firstAttribute="trailing" secondItem="ubD-ku-ces" secondAttribute="trailing" id="Tea-A0-fsR"/>
                <constraint firstItem="ubD-ku-ces" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" id="YJD-25-1Lx"/>
            </constraints>
            <size key="customSize" width="335" height="88"/>
            <connections>
                <outlet property="cellBgView" destination="ubD-ku-ces" id="PYZ-0F-jvx"/>
                <outlet property="tagsButton" destination="bzz-1R-C54" id="Oxl-KQ-amC"/>
            </connections>
            <point key="canvasLocation" x="132.06106870229007" y="0.70422535211267612"/>
        </collectionViewCell>
    </objects>
</document>
