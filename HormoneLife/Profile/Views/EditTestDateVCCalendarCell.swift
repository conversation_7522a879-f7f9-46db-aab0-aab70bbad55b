//
//  EditTestDateVCCalendarCell.swift
//  HormoneLife
//
//  Created by Tank on 2024/6/23.
//

import UIKit

class EditTestDateVCCalendarCell: UITableViewCell {

    let titleView = UIView()
    var title: UILabel = {
        let t = UILabel()
        t.font = .mediumGilroyFont(12)
        t.textColor = .mainTextColor
        t.textAlignment = .left
        return t
    }()
    
    lazy var calendarView: AppCalendarView = {
        let calendarView = AppCalendarView()
        calendarView.configure(beforeCurrentMonth: 50, afterCurrentMonth: 50, isNextMonthEnable: true, isPreMonthEnable: true)
        calendarView.delegate = self
        return calendarView
    }()
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        
        selectionStyle = .none
        contentView.backgroundColor = .themeColor
        contentView.addSubview(titleView)
        titleView.addSubview(title)
        contentView.addSubview(calendarView)
        
        titleView.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(12)
            make.height.equalTo(44)
            make.left.right.equalToSuperview()
        }
        titleView.backgroundColor = .white
        
        title.snp.makeConstraints { make in
            make.left.equalToSuperview().inset(20)
            make.bottom.equalToSuperview()
        }
        
        calendarView.snp.makeConstraints { make in
            make.top.equalTo(titleView.snp.bottom)
            make.left.right.equalToSuperview()
            make.bottom.equalToSuperview().inset(12)
            make.height.equalTo(318)
        }
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
}

extension EditTestDateVCCalendarCell : AppCalendarViewDelegate{
    func cellDataType(view: AppCalendarCell?,  text: String, isSelected : Bool, indexPath: IndexPath, date: Date) -> [CalendarDataType] {
        if Calendar.current.isDateInToday(date) {
            return [.fail,.success, .waitting]
        }
        return []
    }
    func cellColorDataType(view: AppCalendarCell?,  text: String, isSelected : Bool, indexPath: IndexPath, date: Date) -> [CalendarDataType : UIColor] {
        return [.fail : .red, .success : .green, .waitting : .orange, .unknown : .black]
    }

    func appCalendarView(_ view : AppCalendarView, dateSelected date: Date) {
        //completion?(date + 72000)
    }
}
