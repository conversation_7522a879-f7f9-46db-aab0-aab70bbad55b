//
//  AddImageCell.swift
//  HormoneLife
//
//  Created by bm on 2024/10/7.
//

import UIKit

class AddImageCell: BaseTableViewCell {

    typealias HLAddImageCalllback = (_ index: Int) -> Void
    
    var clickIndex: HLAddImageCalllback?
    var closeIndex: HLAddImageCalllback?
    
    var imageCount = 3
    let layout = RectangleLayout()
    var collectionView : UICollectionView! {
        didSet {
            collectionView.register(SingleImageCollectionViewCell.classForCoder(), forCellWithReuseIdentifier: SingleImageCollectionViewCell.description())
        }
    }
    var dataArr = [String]()
    
    override func createSubviews() {
        
        self.backgroundColor = .clear
        self.contentView.backgroundColor = .clear
        
        let containerView = UIView()
        containerView.backgroundColor = .white
        containerView.layer.cornerRadius = 8
        containerView.layer.maskedCorners = [.layerMaxXMaxYCorner, .layerMinXMaxYCorner]
        self.contentView.addSubview(containerView)
        containerView.snp.makeConstraints { make in
            make.left.equalTo(20)
            make.right.equalTo(-20)
            make.top.equalTo(0)
            make.bottom.equalTo(0)
        }
        
        let cellHeight = (kScreenWidth - 30 - 30) / 3.0
        layout.numbersOfCellInRow = 3
        layout.normalCellHeight = Float(cellHeight)
        layout.numbersOfCellInFirstRow = 3
        layout.maxHeight = Float(cellHeight)
        
        self.collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        self.collectionView.isScrollEnabled = false
        self.collectionView.backgroundColor = .white
        self.collectionView.delegate = self
        self.collectionView.dataSource = self
        self.collectionView.showsHorizontalScrollIndicator = false
        containerView.addSubview(self.collectionView)
        self.collectionView.snp.makeConstraints { make in
            make.right.equalTo(-15)
            make.left.equalTo(15)
            make.top.equalTo(0)
            make.bottom.equalTo(-15)
        }
    }
    
    func refresh(datas: [String]) {
        self.dataArr = datas
        self.collectionView.reloadData()
    }
}

extension AddImageCell : UICollectionViewDelegate, UICollectionViewDataSource {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        if self.dataArr.count < imageCount {
            return (self.dataArr.count + 1)
        }
        return imageCount
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: SingleImageCollectionViewCell.description(), for: indexPath) as! SingleImageCollectionViewCell
        if self.dataArr.count < imageCount && self.dataArr.count == indexPath.item {
            cell.iconImgView.image = UIImage(named: "addPhotoButton")
            cell.iconImgView.contentMode = .scaleAspectFit
            cell.closeBtn.isHidden = true
        } else {
            cell.refresh(imageUrl: self.dataArr[indexPath.item])
            cell.iconImgView.contentMode = .scaleAspectFill
        }
        cell.closeBlock = { [weak self] in
            self?.closeIndex?(indexPath.item)
        }
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        self.clickIndex?(indexPath.item)
    }
}

class SingleImageCollectionViewCell: UICollectionViewCell {
    
    typealias HLAddImageCloseBlock = () -> Void
    
    var closeBlock: HLAddImageCloseBlock?
    
    let iconImgView = UIImageView()
    let closeBtn = UIButton()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        self.contentView.backgroundColor = .white
        createSubviews()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func createSubviews() {
        
        iconImgView.image = UIImage(named: "")
        iconImgView.layer.cornerRadius = 3
        iconImgView.layer.masksToBounds = true
        iconImgView.backgroundColor = .white
        iconImgView.contentMode = .scaleAspectFill
        contentView.addSubview(iconImgView)
        iconImgView.snp.makeConstraints { make in
            make.left.top.equalTo(3)
            make.right.bottom.equalTo(-3)
        }
        
        closeBtn.addTarget(self, action: #selector(closeAction), for: .touchUpInside)
        closeBtn.setImage(UIImage(named: "purpleClose"), for: .normal)
        self.contentView.addSubview(closeBtn)
        closeBtn.snp.makeConstraints { make in
            make.right.equalTo(-4)
            make.top.equalTo(4)
            make.width.height.equalTo(20)
        }
    }
    
    @objc func closeAction() {
        self.closeBlock?()
    }
    
    func refresh(imageUrl: String) {
        self.closeBtn.isHidden = false
        if imageUrl.hasPrefix("http") {
            iconImgView.kf.setImage(with: URL(string: imageUrl))
        } else {
            iconImgView.kf.setImage(with: URL(string: baseImageURL + imageUrl))
        }
    }

}
