//
//  CycleReportTableViewCell.swift
//  HormoneLife
//
//  Created by Tank on 2024/9/1.
//

import UIKit
import SnapKit

class CycleReportTableViewCell: UITableViewCell {
    
   
    
    let containView: UIView = {
        let v = UIView()
        v.backgroundColor = .white
        v.layer.cornerRadius = 0
        return v
    }()
    
    let cycleLabel: UILabel = {
        let t = UILabel()
        t.font = .regularGilroyFont(14)
        t.textColor = .mainTextColor
        t.textAlignment = .left
        t.text = "Cycle 4"
        return t
    }()
    
    var cycleLabelTopConstraint: Constraint?
    
    let cycleDaysLabel: UILabel = {
        let t = UILabel()
        t.font = .mediumGilroyFont(14)
        t.textColor = .pinkTextColor
        t.textAlignment = .left
        t.text = "(28 Days)"
        return t
    }()
    
    let cycleDateLabel: UILabel = {
        let t = UILabel()
        t.font = .regularGilroyFont(14)
        t.textColor = .mainTextColor
        t.textAlignment = .right
        t.text = ""
        return t
    }()
    
    let periodLabel: UILabel = {
        let t = UILabel()
        t.font = .regularGilroyFont(12)
        t.textColor = .mainTextColor.withAlphaComponent(0.6)
        t.textAlignment = .left
        t.text = "Period: 6 Days"
        return t
    }()
    
    let ovulationDayLabel: UILabel = {
        let t = UILabel()
        t.font = .regularGilroyFont(12)
        t.textColor = .mainTextColor.withAlphaComponent(0.6)
        t.textAlignment = .right
        t.text = "Ovulation Day: Apr 15"
        return t
    }()
    
    let barContainView: UIView = {
        let v = UIView()
        v.backgroundColor = UIColor("#D0BFDF")
        v.layer.cornerRadius = 4
        v.layer.masksToBounds = true
        return v
    }()
    
    let periodView: UIView = {
        let v = UIView()
        v.backgroundColor = .pinkTextColor
        v.layer.cornerRadius = 4
        return v
    }()
    
    let greenView: UIView = {
        let v = UIView()
        v.backgroundColor = UIColor("#4AC5B1")//.rgba(202, 228, 227)
        v.layer.cornerRadius = 4
        return v
    }()
    
    let purpleView: UIView = {
        let v = UIView()
        v.backgroundColor = UIColor("#FF6ED8")
        v.layer.cornerRadius = 4
        return v
    }()
    
    var periodCycleData: UserPeriodCycle?

    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        selectionStyle = .none
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func configCell(data: UserPeriodCycle, indexPath: IndexPath, sectionCount: Int = 0) {
        periodCycleData = data
        
        cycleLabel.text = "Cycle \(sectionCount - indexPath.row)"
        
        containView.layer.cornerRadius = 0
        cycleLabelTopConstraint?.update(inset: indexPath.row == 0 ? 20 : 0)
        containView.layoutIfNeeded()
        
        if indexPath.row == 0 {
            containView.layer.maskedCorners = [.layerMinXMinYCorner, .layerMaxXMinYCorner]
            containView.layer.cornerRadius = 8
        }
        
        if indexPath.row == sectionCount - 1 {
            containView.layer.maskedCorners = [.layerMinXMaxYCorner, .layerMaxXMaxYCorner]
            containView.layer.cornerRadius = 8
        }
        
        if sectionCount == 1 {
            containView.layer.cornerRadius = 8
        }
        
        setupCycleDay(data: data)
        
        if let periodDays = data.periodDays {
            periodLabel.text = "Period: \(periodDays) Days"
        }
        
        if let ovulateTime = data.ovulateTime {
            let day = ovulateTime.yyyyMMddHHmmss_dd() ?? ""
            let mon = ovulateTime.yyyyMMddHHmmss_enMM().prefix(3)
            ovulationDayLabel.text = "Ovulation Day: \(mon) \(day)"
        }
        
        setupCycleBarView(data: data)
        
        print("cell height = \(self.contentView.frame.size.height)")
        print("cell height = \(self.frame.size.height)")
    }
    
    private func setupCycleDay(data: UserPeriodCycle) {
        
        cycleDaysLabel.text = "(\(data.cycleDays ?? 0) Days)"
        
        if let cycleStartDay = data.startCycleTime,
           let cycleEndDay = data.endCycleTime {
            
            let startMon = cycleStartDay.yyyyMMddHHmmss_enMM().prefix(3)
            let startDay = cycleStartDay.yyyyMMddHHmmss_dd() ?? ""
            
            let endMon = cycleEndDay.yyyyMMddHHmmss_enMM().prefix(3)
            let endDay = cycleEndDay.yyyyMMddHHmmss_dd() ?? ""
            
            cycleDateLabel.text = "\(startMon) \(startDay) - \(endMon) \(endDay)"
        }
    }
    
    private func setupCycleBarView(data: UserPeriodCycle) {
        let cycleViewWidth = UIScreen.main.bounds.width - 64
        
        let cycleStartDayYyyyMMdd = data.startCycleTime?.yyyyMMddHHmmss_yyyyMMdd() ?? ""
        let cycleEndDayYyyyMMdd = data.endCycleTime?.yyyyMMddHHmmss_yyyyMMdd() ?? ""
        let cycleDurationDays = String.durationDays(from: cycleStartDayYyyyMMdd, toyyyyMMdd: cycleEndDayYyyyMMdd)
        
        let eachDayWidth = cycleViewWidth / CGFloat(cycleDurationDays)
        
        let periodStartDayYyyyMMdd = data.startPeriodTime?.yyyyMMddHHmmss_yyyyMMdd() ?? ""
        let periodEndDayYyyyMMdd = data.endPeriodTime?.yyyyMMddHHmmss_yyyyMMdd() ?? ""
        let durationPeriod = String.durationDays(from: periodStartDayYyyyMMdd, toyyyyMMdd: periodEndDayYyyyMMdd)
        
        let startOvulateDayYyyyMMdd = data.startOvulateTime?.yyyyMMddHHmmss_yyyyMMdd() ?? ""
        let endOvulateDayYyyyyMMdd = data.endOvulateTime?.yyyyMMddHHmmss_yyyyMMdd() ?? ""
        let durationOvulate = String.durationDays(from: startOvulateDayYyyyMMdd, toyyyyMMdd: endOvulateDayYyyyyMMdd)
        
        let ovulateThatDayYyyyMMdd = data.ovulateTime?.yyyyMMddHHmmss_yyyyMMdd() ?? ""
        
        
        let diffPeriodToCycleStart = String.diff(from: cycleStartDayYyyyMMdd, toyyyyMMdd: periodStartDayYyyyMMdd)
        let diffOvulateToCycleStart = String.diff(from: cycleStartDayYyyyMMdd, toyyyyMMdd: startOvulateDayYyyyMMdd)
        let diffOvulateThatDayToCycleStart = String.diff(from: cycleStartDayYyyyMMdd, toyyyyMMdd: ovulateThatDayYyyyMMdd)
        
        [periodView, greenView, purpleView].forEach(barContainView.addSubview)
        periodView.frame = CGRectMake(diffPeriodToCycleStart * eachDayWidth, 0, durationPeriod * eachDayWidth, 16)
        greenView.frame = CGRectMake(diffOvulateToCycleStart * eachDayWidth, 0, durationOvulate * eachDayWidth, 16)
        purpleView.frame = CGRectMake(diffOvulateThatDayToCycleStart * eachDayWidth, 0, eachDayWidth, 16)
    }
    
    private func setupUI() {
        contentView.backgroundColor = .themeColor
        contentView.addSubview(containView)
        [cycleLabel, cycleDaysLabel, cycleDateLabel, periodLabel, ovulationDayLabel, barContainView].forEach(containView.addSubview)
        
        containView.snp.makeConstraints { make in
            make.top.bottom.equalToSuperview()
            make.left.right.equalToSuperview().inset(20)
        }
        
        cycleLabel.snp.makeConstraints { make in
            make.left.equalToSuperview().inset(12)
            cycleLabelTopConstraint = make.top.equalToSuperview().constraint
        }
        
        cycleDaysLabel.snp.makeConstraints { make in
            make.left.equalTo(cycleLabel.snp.right).offset(4)
            make.top.equalTo(cycleLabel)
        }
        
        cycleDateLabel.snp.makeConstraints { make in
            make.right.equalToSuperview().inset(12)
            make.top.equalTo(cycleLabel)
        }
        
        periodLabel.snp.makeConstraints { make in
            make.left.equalTo(cycleLabel)
            make.top.equalTo(cycleLabel.snp.bottom).offset(8)
        }
        
        ovulationDayLabel.snp.makeConstraints { make in
            make.right.equalTo(cycleDateLabel)
            make.top.equalTo(cycleDateLabel.snp.bottom).offset(8)
        }
        
        barContainView.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(12)
            make.bottom.equalToSuperview().inset(20)
            make.height.equalTo(16)
            make.top.equalTo(periodLabel.snp.bottom).offset(12)
        }
    }
}
