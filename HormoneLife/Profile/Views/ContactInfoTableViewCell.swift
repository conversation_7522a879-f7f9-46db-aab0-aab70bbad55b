import UIKit
import SnapKit

class ContactInfoTableViewCell: UITableViewCell, UITextFieldDelegate {
    
    // 回调，在文本变化时返回最新内容
    var textChanged: ((String?) -> Void)?
    
    private let backView: UIView = {
        let v = UIView()
        v.backgroundColor = .white
        v.layer.cornerRadius = 8
        return v
    }()
    
    private let titleLabel: UILabel = {
        let l = UILabel()
        l.text = "Email address or phone (optional)"
        l.font = .regularGilroyFont(13)
        l.textColor = .mainTextColor
        l.numberOfLines = 0
        l.textAlignment = .left
        return l
    }()
    
    private let inputBackView: UIView = {
        let v = UIView()
        v.backgroundColor = .themeColor
        v.layer.cornerRadius = 4
        return v
    }()
    
    private lazy var textField: UITextField = {
        let tf = UITextField()
        tf.placeholder = "Email address or phone (optional)"
        tf.font = .regularGilroyFont(14)
        tf.textColor = .mainTextColor
        tf.delegate = self
        tf.addTarget(self, action: #selector(textFieldEditingChanged(_:)), for: .editingChanged)
        return tf
    }()
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        selectionStyle = .none
        contentView.backgroundColor = .themeColor
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        contentView.addSubview(backView)
        backView.addSubview(titleLabel)
        backView.addSubview(inputBackView)
        inputBackView.addSubview(textField)
        
        backView.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(16)
            make.left.right.equalToSuperview().inset(20)
            make.height.equalTo(120)
            make.bottom.equalToSuperview()
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(20)
            make.left.right.equalToSuperview().inset(16)
        }
        
        inputBackView.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(36)
            make.bottom.equalToSuperview().inset(16)
        }
        
        textField.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(UIEdgeInsets(top: 0, left: 20, bottom: 0, right: 20))
        }
    }
    
    // MARK: - UITextFieldDelegate
    @objc private func textFieldEditingChanged(_ sender: UITextField) {
        textChanged?(sender.text)
    }
} 