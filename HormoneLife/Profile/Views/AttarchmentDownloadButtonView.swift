//
//  AttarchmentDownloadButtonView.swift
//  HormoneLife
//
//  Created by Tank on 2024/11/3.
//  Copyright © 2024 HormoneLife. All rights reserved.
//

import UIKit
import SnapKit

class AttarchmentDownloadButtonView: UIView {

    private lazy var attarchButton: UIButton = {
        let b = UIButton().makelayoutable()
        b.setImage(UIImage(named: "attarchmentIcon"), for: .normal)
        b.setTitleColor(.black, for: .normal)
        b.backgroundColor = .white
        b.layer.cornerRadius = 8
        b.layer.masksToBounds = true
        //b.addTarget(self, action: #selector(self.buttonPrevHandler(_:)), for: .touchUpInside)
        return b
    }()
    
    private lazy var arowButton: UIButton = {
        let b = UIButton().makelayoutable()
        b.setImage(UIImage(named: "circleForwardIcon"), for: .normal)
        b.setTitleColor(.black, for: .normal)
        //b.addTarget(self, action: #selector(self.buttonNextHandler(_:)), for: .touchUpInside)
        return b
    }()

    lazy var fileNameLabel : UILabel = {
        let v = UILabel()
        v.font = UIFont.regularGilroyFont(12)
        v.textColor = .mainTextColor
        v.textAlignment = .left
        v.text = "Test documentation.zip"
        v.numberOfLines = 0
        return v
    }()
    
    lazy var cabilityLabel : UILabel = {
        let v = UILabel()
        v.font = UIFont.regularGilroyFont(12)
        v.textColor = .mainTextColor
        v.textAlignment = .left
        v.text = "6.32KB"
        v.numberOfLines = 0
        return v
    }()
    
    init(frame: CGRect, delegate: AppCalendarViewDelegate?) {
        super.init(frame: frame)
        setupUI()
    }

    public override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    private func setupUI() {
        backgroundColor = .themeColor
        layer.cornerRadius = 8
        layer.masksToBounds = true
        setupViews()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupViews() {
        addSubview(attarchButton)
        addSubview(arowButton)
        addSubview(fileNameLabel)
        addSubview(cabilityLabel)
        
        attarchButton.snp.makeConstraints { make in
            make.width.height.equalTo(40)
            make.left.equalToSuperview().inset(4)
            make.centerY.equalToSuperview()
        }
        
        arowButton.snp.makeConstraints { make in
            make.width.height.equalTo(16)
            make.right.equalToSuperview().inset(16)
            make.centerY.equalToSuperview()
        }
        
        fileNameLabel.snp.makeConstraints { make in
            make.left.equalTo(attarchButton.snp.right).offset(12)
            make.top.equalToSuperview().inset(8)
            make.right.equalTo(arowButton.snp.left).offset(-12)
        }
        
        cabilityLabel.snp.makeConstraints { make in
            make.left.equalTo(attarchButton.snp.right).offset(12)
            make.top.equalTo(fileNameLabel.snp.bottom).offset(4)
            make.right.equalTo(arowButton.snp.left).offset(-12)
            make.bottom.equalToSuperview().inset(8)
        }
    }
}
