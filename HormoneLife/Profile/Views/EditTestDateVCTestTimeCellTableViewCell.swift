//
//  EditTestDateVCTestTimeCellTableViewCell.swift
//  HormoneLife
//
//  Created by Tank on 2024/6/23.
//

import UIKit

protocol EditTestDateVCTestTimeCellTableViewCellDelegate: AnyObject {
    func didSeletValue(_ value: String)
}

class EditTestDateVCTestTimeCellTableViewCell: UITableViewCell {

    lazy var hourPickerView: PickerView = {
        let pickerView = PickerView()
        pickerView.tag = 0
        pickerView.backgroundColor = .clear
        pickerView.scrollingStyle = .infinite
        pickerView.delegate = self
        pickerView.dataSource = self
        return pickerView
    }()

    lazy var minPickerView: PickerView = {
        let pickerView = PickerView()
        pickerView.tag = 1
        pickerView.backgroundColor = .clear
        pickerView.scrollingStyle = .infinite
        pickerView.delegate = self
        pickerView.dataSource = self
        return pickerView
    }()
    
    let pickerSelectedView = UIView()
    
    var title: UILabel = {
        let t = UILabel()
        t.text = "Reminder Time"
        t.font = .mediumGilroyFont(12)
        t.textColor = .mainTextColor
        t.textAlignment = .left
        return t
    }()
    
    let hourOption: [String] = {
        let hour = 1...24
        let array = hour.map { "\($0)" }
        return array
    }()
    
    let minOption: [String] = {
        let minute = 0...59
        let array = minute.map { String(format: "%02d", $0) }
        return array
    }()
    
    var hourStr = "0"
    var minStr = "00"
    
    weak var delegate: EditTestDateVCTestTimeCellTableViewCellDelegate?
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        
        selectionStyle = .none
        contentView.backgroundColor = .white
        addSubview(title)
        addSubview(pickerSelectedView)
        addSubview(hourPickerView)
        addSubview(minPickerView)
        
        title.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(20)
            make.left.right.equalToSuperview().inset(20)
        }
        
        hourPickerView.snp.makeConstraints { make in
            make.left.equalToSuperview().inset(100)
            make.height.equalTo(120)
            make.width.equalTo(40)
            make.top.equalTo(title.snp.bottom).offset(6)
            make.bottom.equalToSuperview().inset(10)
        }
        
        minPickerView.snp.makeConstraints { make in
            make.right.equalToSuperview().inset(100)
            make.height.width.centerY.equalTo(hourPickerView)
        }
        
        pickerSelectedView.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(20)
            make.height.equalTo(40)
            make.centerY.equalTo(hourPickerView)
        }
        pickerSelectedView.backgroundColor = .lightGrayContentColor
        pickerSelectedView.layer.cornerRadius = 4
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
}

extension EditTestDateVCTestTimeCellTableViewCell: PickerViewDelegate, PickerViewDataSource {
    
    func pickerViewNumberOfRows(_ pickerView: PickerView) -> Int {
        if pickerView.tag == 0 {
            return hourOption.count
        } else {
            return minOption.count
        }
    }
    
    func pickerViewHeightForRows(_ pickerView: PickerView) -> CGFloat {
        40
    }
    
    func pickerView(_ pickerView: PickerView, titleForRow row: Int) -> String {
        if pickerView.tag == 0 {
            return hourOption[row]
        } else {
            return minOption[row]
        }
    }
    
    func pickerView(_ pickerView: PickerView, didSelectRow row: Int) {
        if pickerView.tag == 0 {
            hourStr = hourOption[row]
        } else {
            minStr = minOption[row]
        }
        delegate?.didSeletValue("\(hourStr): \(minStr)")
    }
    
    func pickerView(_ pickerView: PickerView, styleForLabel label: UILabel, highlighted: Bool) {
        label.textAlignment = .center
        
        if highlighted {
            label.font = .mediumGilroyFont(16.0)
            label.textColor = .mainTextColor
        } else {
            label.font = .mediumGilroyFont(16.0)
            label.textColor = .mainTextColor.withAlphaComponent(0.3)
        }
    }
}
