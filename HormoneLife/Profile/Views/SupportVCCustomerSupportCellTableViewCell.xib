<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="21701" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21679"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="Gilroy Medium.otf">
            <string>Gilroy-Medium</string>
        </array>
        <array key="gilroy regular.otf">
            <string><PERSON><PERSON>-Regular</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" rowHeight="282" id="KGk-i7-Jjw" customClass="SupportVCCustomerSupportCellTableViewCell" customModule="HormoneLife" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="320" height="282"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" ambiguous="YES" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="320" height="282"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="6mX-aD-ROs">
                        <rect key="frame" x="20" y="16" width="280" height="230"/>
                        <subviews>
                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="uXd-YM-YGx">
                                <rect key="frame" x="16" y="20" width="248" height="190"/>
                                <subviews>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Customer Support" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="euV-sL-7zt">
                                        <rect key="frame" x="0.0" y="0.0" width="248" height="16"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="16" id="LwY-0j-XRw"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" name="Gilroy-Medium" family="Gilroy" pointSize="16"/>
                                        <nil key="textColor"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="HhQ-ek-6eN">
                                        <rect key="frame" x="0.0" y="32" width="248" height="78"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="78" id="x9E-ok-vqV"/>
                                        </constraints>
                                        <string key="text">Toll-Free Number:1-888-444-3657
(9:30 a.m.to5:00 p.m. CDT M-F) or reach out to us on social media：</string>
                                        <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="14"/>
                                        <color key="textColor" red="0.077407650650000007" green="0.19995591039999999" blue="0.20000121000000001" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="leading" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="g2k-bV-EpP">
                                        <rect key="frame" x="0.0" y="126" width="248" height="24"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="24" id="O0e-6J-Zs3"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="14"/>
                                        <color key="tintColor" red="0.077407650650000007" green="0.19995591039999999" blue="0.20000121000000001" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="10" maxY="0.0"/>
                                        <state key="normal" title="instagram.com/wondfo chicago" image="cameraIcon">
                                            <color key="titleColor" red="0.077407650650000007" green="0.19995591039999999" blue="0.20000121000000001" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                        </state>
                                        <buttonConfiguration key="configuration" style="plain" image="cameraIcon" title="instagram.com/wondfo chicago" imagePadding="10">
                                            <fontDescription key="titleFontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="14"/>
                                            <color key="baseForegroundColor" red="0.077407650650000007" green="0.19995591039999999" blue="0.20000121000000001" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                        </buttonConfiguration>
                                        <connections>
                                            <action selector="didTapInsAction:" destination="KGk-i7-Jjw" eventType="touchUpInside" id="hqO-1c-iy7"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="leading" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Mtw-9y-TGh">
                                        <rect key="frame" x="0.0" y="166" width="248" height="24"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="24" id="JB9-hi-6Ic"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="14"/>
                                        <color key="tintColor" red="0.077407650650000007" green="0.19995591039999999" blue="0.20000121000000001" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="10" maxY="0.0"/>
                                        <state key="normal" title="instagram.com/wondfo chicago" image="cameraIcon">
                                            <color key="titleColor" red="0.077407650650000007" green="0.19995591039999999" blue="0.20000121000000001" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                        </state>
                                        <buttonConfiguration key="configuration" style="plain" image="facebookSmallIcon" title="facebook.com/officialWondfoUSA" imagePadding="10">
                                            <fontDescription key="titleFontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="14"/>
                                            <color key="baseForegroundColor" red="0.077407650650000007" green="0.19995591039999999" blue="0.20000121000000001" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                        </buttonConfiguration>
                                        <connections>
                                            <action selector="didTapFacebookAction:" destination="KGk-i7-Jjw" eventType="touchUpInside" id="pHl-JH-Rw2"/>
                                        </connections>
                                    </button>
                                </subviews>
                            </stackView>
                        </subviews>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstAttribute="trailing" secondItem="uXd-YM-YGx" secondAttribute="trailing" constant="16" id="1uT-p5-IjR"/>
                            <constraint firstAttribute="bottom" secondItem="uXd-YM-YGx" secondAttribute="bottom" constant="20" id="J16-WG-Giq"/>
                            <constraint firstItem="uXd-YM-YGx" firstAttribute="top" secondItem="6mX-aD-ROs" secondAttribute="top" constant="20" id="OWN-aQ-kKH"/>
                            <constraint firstItem="uXd-YM-YGx" firstAttribute="leading" secondItem="6mX-aD-ROs" secondAttribute="leading" constant="16" id="iQe-e1-zy4"/>
                            <constraint firstAttribute="height" constant="230" id="yEo-IR-EMy"/>
                        </constraints>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                <integer key="value" value="4"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </view>
                </subviews>
                <color key="backgroundColor" red="0.9101603627" green="0.88687437769999999" blue="0.85495901110000005" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                <constraints>
                    <constraint firstAttribute="trailing" secondItem="6mX-aD-ROs" secondAttribute="trailing" constant="20" id="9Fq-Zw-gD7"/>
                    <constraint firstItem="6mX-aD-ROs" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" constant="16" id="DLB-Xi-sNc"/>
                    <constraint firstItem="6mX-aD-ROs" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="20" id="S9y-6h-RwB"/>
                    <constraint firstAttribute="bottom" secondItem="6mX-aD-ROs" secondAttribute="bottom" constant="8" id="unV-Im-eoX"/>
                </constraints>
            </tableViewCellContentView>
            <viewLayoutGuide key="safeArea" id="njF-e1-oar"/>
            <connections>
                <outlet property="descriptionLabel" destination="HhQ-ek-6eN" id="9Bi-Ju-iPZ"/>
                <outlet property="facebookButton" destination="Mtw-9y-TGh" id="QNa-E8-xai"/>
                <outlet property="insButton" destination="g2k-bV-EpP" id="CHf-oA-t9O"/>
            </connections>
            <point key="canvasLocation" x="-50.381679389312978" y="-12.67605633802817"/>
        </tableViewCell>
    </objects>
    <resources>
        <image name="cameraIcon" width="24" height="24"/>
        <image name="facebookSmallIcon" width="24" height="24"/>
    </resources>
</document>
