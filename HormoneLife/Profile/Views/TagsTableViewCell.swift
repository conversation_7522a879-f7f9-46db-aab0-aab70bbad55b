//
//  TagsTableViewCell.swift
//  HormoneLife
//
//  Created by Tank on 2024/8/4.
//

import UIKit

class TagsTableViewCell: UITableViewCell {

    var tags: [String] = []
    var tagsIsAdded: Bool = false
    weak var delegate: AddTagViewControllerDelegate?
    
    var isFeedback = false
    
    var backView: UIView = {
        let v = UIView()
        v.backgroundColor = .white
        v.layer.cornerRadius = 8
        return v
    }()
    
    var titleLabel: UILabel = {
        let t = UILabel()
        t.font = .mediumGilroyFont(16)
        t.textColor = .mainTextColor
        t.textAlignment = .left
        t.numberOfLines = 0
        return t
    }()
    
    lazy var layout: LeftAlignedCollectionViewFlowLayout = {
        let layout = LeftAlignedCollectionViewFlowLayout()
        layout.scrollDirection = .vertical
        layout.minimumLineSpacing = 10
        layout.estimatedItemSize = CGSize(width: UIScreen.main.bounds.width - 110, height: 25)
        layout.itemSize = UICollectionViewFlowLayout.automaticSize
        return layout
    }()
    
    lazy var tagsCollectionView: UICollectionView = {
        let tagsC = UICollectionView(frame: .zero, collectionViewLayout: layout)
        tagsC.backgroundColor = .white
        tagsC.dataSource = self
        tagsC.delegate = self
        tagsC.contentInset = UIEdgeInsets(top: 0, left: 0, bottom: 0, right: 0)
        tagsC.register(UINib(nibName: "TagsCollectionViewCell", bundle: nil), forCellWithReuseIdentifier: "TagsCollectionViewCell")
        return tagsC
    }()
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        
        selectionStyle = .none
        contentView.backgroundColor = .themeColor
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func setupCell(title: String = "", dataSource: [String] = [], isAdded: Bool = false, isAllCornerRaduis: Bool = true, delegate: AddTagViewControllerDelegate? = nil) {
        titleLabel.text = title
        tags = dataSource
        tagsIsAdded = isAllCornerRaduis
        self.delegate = delegate
        
        if !isAllCornerRaduis {
            backView.layer.maskedCorners = CACornerMask(rawValue: CACornerMask.layerMinXMinYCorner.rawValue | CACornerMask.layerMaxXMinYCorner.rawValue)
        }
        tagsCollectionView.reloadData()
    }
    
    private func setupUI() {
        contentView.addSubview(backView)
        backView.addSubview(titleLabel)
        backView.addSubview(tagsCollectionView)
        
        backView.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(16)
            make.left.right.equalToSuperview().inset(20)
            make.bottom.equalToSuperview()
        }
        
        titleLabel.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(16)
            make.top.equalToSuperview().inset(20)
        }
        
        tagsCollectionView.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(16)
            make.top.equalTo(titleLabel.snp.bottom).offset(16)
            make.bottom.equalToSuperview().inset(20)
        }
    }
}

extension TagsTableViewCell: UICollectionViewDelegate, UICollectionViewDataSource {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return tags.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let tag = tags[indexPath.item]
        guard let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "TagsCollectionViewCell", for: indexPath) as? TagsCollectionViewCell else { return UICollectionViewCell() }
        let tags = tag.components(separatedBy: ",")
        cell.tagsButton.setTitle(tags.first, for: .normal)
        cell.tagsButton.isUserInteractionEnabled = false
        if self.isFeedback {
             if tag.hasSuffix(",1") {
                cell.setupCell(tag: tags.first ?? "", isAdded: true)
            } else {
                cell.setupCell(tag: tags.first ?? "", isAdded: false)
            }
        } else {
            cell.setupCell(tag: tags.first ?? "", isAdded: tagsIsAdded)
        }
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, canEditItemAt indexPath: IndexPath) -> Bool {
        return true
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        
        if self.isFeedback {
            var temps = [String]()
            for (index, item) in self.tags.enumerated() {
                if index == indexPath.item {
                    let result = item.replacingOccurrences(of: ",0", with: ",1")
                    temps.append(result)
                } else {
                    let result = item.replacingOccurrences(of: ",1", with: ",0")
                    temps.append(result)
                }
            }
            self.tags = temps
            collectionView.reloadData()
            delegate?.didSelectTag(tags[indexPath.item])
            return
        }
        if tagsIsAdded {
            delegate?.didRemoveTag(tags[indexPath.item])
        } else {
            delegate?.didSelectTag(tags[indexPath.item])
        }
    }
}

protocol TagsTableViewFooterViewDelegate: AnyObject {
    func didTapAdd()
}
class TagsTableViewFooterView: UITableViewHeaderFooterView {
    
    weak var delegate: TagsTableViewFooterViewDelegate?
    
    var backView: UIView = {
        let v = UIView()
        v.backgroundColor = .white
        v.layer.cornerRadius = 8
        v.layer.maskedCorners = CACornerMask(rawValue: CACornerMask.layerMinXMaxYCorner.rawValue | CACornerMask.layerMaxXMaxYCorner.rawValue)
        return v
    }()
    
    var addButton: UIButton = {
        let b = UIButton(type: .custom)
        b.setImage(UIImage(named: "addTagsButtonPurple"), for: .normal)
        return b
    }()
    
    override init(reuseIdentifier: String?) {
        super.init(reuseIdentifier: reuseIdentifier)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        contentView.backgroundColor = .themeColor
        contentView.addSubview(backView)
        backView.addSubview(addButton)
        
        backView.snp.makeConstraints { make in
            make.height.equalTo(80)
            make.top.bottom.equalToSuperview()
            make.left.right.equalToSuperview().inset(20)
        }
        
        addButton.snp.makeConstraints { make in
            make.width.equalTo(120)
            make.height.equalTo(40)
            make.top.equalToSuperview().inset(10)
            make.centerX.equalToSuperview()
        }
        
        addButton.addTarget(self, action: #selector(didTapAdd), for: .touchUpInside)
    }
    
    @objc func didTapAdd() {
        delegate?.didTapAdd()
    }
}
