//
//  NoticeTableViewCell.swift
//  HormoneLife
//
//  Created by Tank on 2024/6/16.
//

import UIKit
import SnapKit

class NoticeTableViewCell: UITableViewCell {

    @IBOutlet weak var titleLabel: UILabel!
    @IBOutlet weak var numberLabel: UILabel!
    @IBOutlet weak var button: UIButton!
        
    override func awakeFromNib() {
        super.awakeFromNib()
        // Initialization code

    }

    override func setSelected(_ selected: <PERSON><PERSON>, animated: Bool) {
        super.setSelected(selected, animated: animated)
    }
    
    func refresh(model: RemindMessage) {
        titleLabel.text = model.message
        numberLabel.text = timeFormat(date: model.createTime ?? "", format: "yyyy-MM-dd HH:mm:ss", toFormat: "MM/dd yyyy HH:mm")
    }
    
    func setupWith(_ title: String, numberText: String = "", isButtonHidden: Bool = true, isNumberLabelHidden: Bool = true) {
        titleLabel.text = title
        numberLabel.text = numberText == "0" ? "" : numberText
        
        button.setImage(UIImage(named: "forwarIcon"), for: .normal)
        
        button.isHidden = isButtonHidden
        numberLabel.isHidden = isNumberLabelHidden
    }
}

class RemindSectionHeaderView: UITableViewHeaderFooterView {
    
    var title: UILabel = {
        let t = UILabel()
        t.textColor = .mainTextColor.withAlphaComponent(0.6)
        t.font = .regularGilroyFont(14)
        t.textAlignment = .center
        return t
    }()
    
    override init(reuseIdentifier: String?) {
        super.init(reuseIdentifier: reuseIdentifier)
        
        addSubview(title)
        
        title.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(20)
            make.top.equalToSuperview().inset(10)
            make.bottom.equalToSuperview()
        }
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
}
