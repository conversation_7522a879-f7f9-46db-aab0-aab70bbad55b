//
//  CycleReportCaptureView.swift
//  HormoneLife
//
//  Created by bm on 2024/10/18.
//  Copyright © 2024 HormoneLife. All rights reserved.
//

import UIKit

class CycleReportCaptureView: UIView, HistoryRecordCellDelegate {
    func didTapDeleteButton(testResultId: String) {
        
    }
    
    func didTapEditButton(testResultId: String) {
        
    }
    
    let subView1: CycleReportHeaderSubView = {
        let v = CycleReportHeaderSubView()
        v.title.text = Interface.shared().loggedInUser?.userInfo.menstruationCycleAvg ?? "0"
        v.subtitle.text = "Cycle Length\n（Average days）"
        return v
    }()
    
    let subView2: CycleReportHeaderSubView = {
        let v = CycleReportHeaderSubView()
        v.title.text = Interface.shared().loggedInUser?.userInfo.period ?? "0"
        v.subtitle.text = "Period Length\n（Average days）"
        return v
    }()
    
    
    var yearsPeriodData: [UserPeriodCycleListByYear] = []
        
    private lazy var tableView: UITableView = {
        let tableView = UITableView(frame: .zero, style: .plain)
        tableView.backgroundColor = .themeColor
        tableView.showsVerticalScrollIndicator = false
        tableView.separatorStyle = .none
        
        tableView.register(CycleReportTableViewCell.classForCoder(), forCellReuseIdentifier: CycleReportTableViewCell.description())
        
        tableView.dataSource = self
        tableView.delegate = self
        tableView.rowHeight = UITableView.automaticDimension
        return tableView
    }()
    
    init(frame: CGRect, title:String, cycle: String, rowHeight: CGFloat = 0.0, dataSource: [UserPeriodCycleListByYear] = []) {
        super.init(frame: frame)
        self.yearsPeriodData = dataSource
        self.backgroundColor = .themeColor
        
        let imageView = UIImageView()
        imageView.image = UIImage(named: "capture_homelift_icon")
        self.addSubview(imageView)
        imageView.contentMode = .scaleAspectFit
        imageView.snp.makeConstraints { make in
            make.top.equalTo(80)
            make.height.equalTo(40)
            make.centerX.equalToSuperview()
            make.width.equalTo(206)
        }
        let containerView = UIView()
        containerView.layer.masksToBounds = true
        containerView.backgroundColor = .white
        self.addSubview(containerView)
        containerView.snp.makeConstraints { make in
            make.left.equalTo(15)
            make.right.equalTo(-15)
            make.top.equalTo(imageView.snp.bottom).offset(20)
        }
        self.subView1.title.text = title
        containerView.addSubview(self.subView1)
        self.subView1.snp.makeConstraints { make in
            make.top.equalTo(30)
            make.right.equalTo(containerView.snp.centerX)
            make.height.equalTo(96)
            make.left.equalTo(15)
            
        }
        self.subView2.title.text = cycle
        containerView.addSubview(self.subView2)
        self.subView2.snp.makeConstraints { make in
            make.top.equalTo(30)
            make.left.equalTo(containerView.snp.centerX)
            make.height.equalTo(96)
            make.right.equalTo(-15)
        }
        
        containerView.addSubview(self.tableView)
        self.tableView.backgroundColor = .white
        self.tableView.snp.makeConstraints { make in
            make.left.equalTo(-20)
            make.right.equalTo(20)
            make.top.equalTo(self.subView1.snp.bottom).offset(15)
            make.height.equalTo(rowHeight)
            make.bottom.equalTo(-30)
        }
        
        let qrcode = LBXScanNative.createQR(with: UserDefaults.standard.downloadUrl, qrSize: CGSize(width: 100, height: 100))
        
        let qrImageview = UIImageView()
        qrImageview.image = qrcode
        qrImageview.contentMode = .scaleAspectFit
        addSubview(qrImageview)
        qrImageview.snp.makeConstraints { make in
            make.top.equalTo(containerView.snp.bottom).offset(20)
            make.height.width.equalTo(80)
            make.centerX.equalToSuperview()
        }
        
        let tipLab = UILabel()
        tipLab.textAlignment = .center
        tipLab.textColor = UIColorFromRGB(rgbValue: 0x360C5E, alpha: 0.6)
        tipLab.font = .systemFont(ofSize: 14)
        tipLab.text = "Download, know more about yourself"
        self.addSubview(tipLab)
        tipLab.snp.makeConstraints { make in
            make.top.equalTo(qrImageview.snp.bottom).offset(15)
            make.centerX.equalToSuperview()
            make.height.equalTo(20)
            make.bottom.equalTo(-40)
        }
        self.tableView.reloadData()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
}


extension CycleReportCaptureView: UITableViewDelegate, UITableViewDataSource {
    func numberOfSections(in tableView: UITableView) -> Int {
        1
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {

        return yearsPeriodData.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        guard yearsPeriodData.count > indexPath.section,
              yearsPeriodData[indexPath.section].cycleList.count > indexPath.row,
              let cell = tableView.dequeueReusableCell(withIdentifier: CycleReportTableViewCell.description(), for: indexPath) as? CycleReportTableViewCell else {
            return UITableViewCell()
        }
        
        let periodData = yearsPeriodData[indexPath.section].cycleList[indexPath.row]
        cell.configCell(data: periodData, indexPath: indexPath, sectionCount: yearsPeriodData[indexPath.section].cycleList.count)
        cell.containView.layer.cornerRadius = 0
        return cell
    }
    
}
