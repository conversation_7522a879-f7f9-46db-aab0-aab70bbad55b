<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="22505" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22504"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="Gilroy Medium.otf">
            <string>Gilroy-Medium</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" rowHeight="211" id="KGk-i7-Jjw" customClass="SupportVCDontFindAnswerCell" customModule="HormoneLife" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="320" height="211"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" ambiguous="YES" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="320" height="211"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Zti-rW-hR8">
                        <rect key="frame" x="20" y="8" width="280" height="148"/>
                        <subviews>
                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="l7A-rg-w3c">
                                <rect key="frame" x="24" y="20" width="232" height="108"/>
                                <subviews>
                                    <stackView opaque="NO" contentMode="scaleToFill" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="iKq-MX-s1n">
                                        <rect key="frame" x="0.0" y="0.0" width="232" height="40"/>
                                        <subviews>
                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="messageIcon" translatesAutoresizingMaskIntoConstraints="NO" id="7Y8-Uq-FaV">
                                                <rect key="frame" x="0.0" y="0.0" width="40" height="40"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="40" id="8Rp-BQ-QBn"/>
                                                    <constraint firstAttribute="height" constant="40" id="gCc-gY-JdK"/>
                                                </constraints>
                                            </imageView>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Don't find the answer?" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="vwx-yu-tsp">
                                                <rect key="frame" x="52" y="0.0" width="128" height="40"/>
                                                <fontDescription key="fontDescription" name="Gilroy-Medium" family="Gilroy" pointSize="14"/>
                                                <color key="textColor" red="0.20423525570000001" green="0.075626514850000007" blue="0.36441689729999999" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <button opaque="NO" userInteractionEnabled="NO" contentMode="scaleToFill" contentHorizontalAlignment="right" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Ww1-wU-nkx">
                                                <rect key="frame" x="192" y="0.0" width="40" height="40"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="40" id="UZu-1b-kUM"/>
                                                </constraints>
                                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                <state key="normal" image="circleForwardIcon"/>
                                            </button>
                                        </subviews>
                                    </stackView>
                                    <stackView opaque="NO" contentMode="scaleToFill" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="pqr-Mx-iyq">
                                        <rect key="frame" x="0.0" y="40" width="232" height="68"/>
                                        <subviews>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="jfi-8M-bgY">
                                                <rect key="frame" x="0.0" y="0.0" width="40" height="68"/>
                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="40" id="Q5b-7O-eta"/>
                                                </constraints>
                                            </view>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" usesAttributedText="YES" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Zsa-ba-eJc">
                                                <rect key="frame" x="52" y="0.0" width="180" height="68"/>
                                                <attributedString key="attributedText">
                                                    <fragment content="Please reach out to us via text message or call our Toll Free Number 1-888-444-3657(9:30 a.m. to 5:00 p.m. CDT M-F)">
                                                        <attributes>
                                                            <font key="NSFont" size="12" name="Times-Roman"/>
                                                            <real key="NSKern" value="0.0"/>
                                                            <paragraphStyle key="NSParagraphStyle" alignment="left" lineBreakMode="wordWrapping" baseWritingDirection="leftToRight" paragraphSpacing="12" defaultTabInterval="36" tighteningFactorForTruncation="0.0">
                                                                <tabStops/>
                                                            </paragraphStyle>
                                                            <color key="NSStrokeColor" red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                            <real key="NSStrokeWidth" value="0.0"/>
                                                        </attributes>
                                                    </fragment>
                                                </attributedString>
                                                <nil key="highlightedColor"/>
                                            </label>
                                        </subviews>
                                    </stackView>
                                </subviews>
                            </stackView>
                        </subviews>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="148" id="3VX-L7-Fv3"/>
                            <constraint firstItem="l7A-rg-w3c" firstAttribute="leading" secondItem="Zti-rW-hR8" secondAttribute="leading" constant="24" id="7EJ-nU-5Vl"/>
                            <constraint firstAttribute="bottom" secondItem="l7A-rg-w3c" secondAttribute="bottom" constant="20" id="Elf-Gx-hxL"/>
                            <constraint firstItem="l7A-rg-w3c" firstAttribute="top" secondItem="Zti-rW-hR8" secondAttribute="top" constant="20" id="Ryp-Jk-buM"/>
                            <constraint firstAttribute="trailing" secondItem="l7A-rg-w3c" secondAttribute="trailing" constant="24" id="VIW-C5-Q1o"/>
                        </constraints>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                <integer key="value" value="4"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </view>
                </subviews>
                <color key="backgroundColor" red="0.94169229269999999" green="0.92880314590000002" blue="0.9607291818" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                <constraints>
                    <constraint firstItem="Zti-rW-hR8" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" constant="8" id="Q6F-xO-nXT"/>
                    <constraint firstItem="Zti-rW-hR8" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="20" id="RB1-95-zkp"/>
                    <constraint firstAttribute="bottom" secondItem="Zti-rW-hR8" secondAttribute="bottom" constant="32" id="jAs-Fq-TZk"/>
                    <constraint firstAttribute="trailing" secondItem="Zti-rW-hR8" secondAttribute="trailing" constant="20" id="ns4-Vs-2zx"/>
                </constraints>
            </tableViewCellContentView>
            <viewLayoutGuide key="safeArea" id="njF-e1-oar"/>
            <point key="canvasLocation" x="-50.381679389312978" y="46.126760563380287"/>
        </tableViewCell>
    </objects>
    <resources>
        <image name="circleForwardIcon" width="16" height="16"/>
        <image name="messageIcon" width="40" height="40"/>
    </resources>
</document>
