//
//  ReminderMessageCell.swift
//  HormoneLife
//
//  Created by bm on 2024/10/9.
//  Copyright © 2024 HormoneLife. All rights reserved.
//

import UIKit
import SnapKit

class ReminderMessageCell: BaseTableViewCell {
    
    let dateLab = UILabel()
    let contentLab = UILabel()
    let timeLab = UILabel()
    
    var dateLabHeightContraint: Constraint?
    
    override func createSubviews() {
        self.contentView.backgroundColor = .clear
        self.backgroundColor = .clear
        
        self.dateLab.textAlignment = .center
        self.dateLab.textColor = UIColorFromRGB(rgbValue: 0x360C5E, alpha: 0.6)
        self.dateLab.font = .systemFont(ofSize: 15)
        self.contentView.addSubview(self.dateLab)
        self.dateLab.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(20)
            dateLabHeightContraint = make.height.equalTo(52).constraint
            make.top.equalToSuperview()
        }
        
        let containerView = UIView()
        containerView.backgroundColor = .white
        containerView.layer.cornerRadius = 8
        containerView.layer.masksToBounds = true
        self.contentView.addSubview(containerView)
        containerView.snp.makeConstraints { make in
            make.top.equalTo(dateLab.snp.bottom)
            make.left.right.equalToSuperview().inset(16)
            make.bottom.equalToSuperview()
        }
        
        self.timeLab.textAlignment = .right
        self.timeLab.backgroundColor = .white
        self.timeLab.textColor = UIColorFromRGB(rgbValue: 0x360C5E, alpha: 0.6)
        self.timeLab.font = .systemFont(ofSize: 14)
        containerView.addSubview(self.timeLab)
        self.timeLab.snp.makeConstraints { make in
            make.right.equalTo(-16)
            make.height.equalTo(16)
            make.centerY.equalToSuperview()
            make.width.equalTo(70)
        }
        
        self.contentLab.backgroundColor = .white
        self.contentLab.numberOfLines = 0
        self.contentLab.textColor = UIColorFromRGB(rgbValue: 0x360C5E, alpha: 1)
        self.contentLab.font = .systemFont(ofSize: 16)
        containerView.addSubview(self.contentLab)
        self.contentLab.snp.makeConstraints { make in
            make.left.equalTo(16)
            make.top.equalTo(10)
            make.right.equalTo(self.timeLab.snp.left).offset(-12)
            make.height.greaterThanOrEqualTo(15)
            make.bottom.equalTo(-10)
        }
    }
    
    func refresh(currentModel: RemindMessage, preModel: RemindMessage, isFirstIndexPath: Bool = false) {
        self.contentLab.text = currentModel.message
        
        guard let timeStamp = currentModel.createTime,
              let intTimeStamp = Int(timeStamp),
              let preTimeStamp = preModel.createTime,
              let intPreTimeStamp = Int(preTimeStamp) else  {
            dateLabHeightContraint?.update(offset: 16)
            return
        }
        
        var hour = transformTimeStamp(timeStamp: Double(intTimeStamp), format: "HH")
        if let numHour = Int(hour) {
            if numHour > 13 {
                hour = String(format: "%02d", numHour - 12)
            }
        }
        let minAM = transformTimeStamp(timeStamp: Double(intTimeStamp), format: "mm a")
        self.timeLab.text = "\(hour):\(minAM)"
        
        let currentDate = transformTimeStamp(timeStamp: Double(intTimeStamp), format: "EEEE, MM/dd/yyyy")
        let preDate = transformTimeStamp(timeStamp: Double(intPreTimeStamp), format: "EEEE, MM/dd/yyyy")
        
        if isFirstIndexPath {
            dateLab.text = currentDate
            dateLabHeightContraint?.update(offset: 52)
        } else {
            
            if currentDate == preDate {
                dateLab.text = ""
                dateLabHeightContraint?.update(offset: 18)
            } else {
                dateLab.text = currentDate
                dateLabHeightContraint?.update(offset: 52)
            }
        }
    }
}
