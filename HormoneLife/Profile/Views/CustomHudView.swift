//
//  CustomHudView.swift
//  HormoneLife
//
//  Created by bm on 2024/10/16.
//  Copyright © 2024 HormoneLife. All rights reserved.
//

import UIKit

class CustomHudView: UIView {

    let titleLab = UILabel()
    
    override init(frame: CGRect) {
        super.init(frame: CGRect(x: 0, y: 0, width: kScreenWidth, height: kScreenHeight))
        createSubviews()
    }
    
    func hideNow() {
        self.isHidden = true
    }
    
    func hideHudAfter() {
        DispatchQueue.main.asyncAfter(deadline: DispatchTime.now() + 2) {
            self.isHidden = true
        }
    }
    
    func hide() {
        self.isHidden = true
    }
    
    func showHud(text: String?) {
        self.isHidden = false
        titleLab.text = text ?? "Loading"
        getKeyWindow()?.addSubview(self)
    }
    
    func refresh(text: String?) {
        self.isHidden = false
        titleLab.text = text ?? "Loading"
        getKeyWindow()?.addSubview(self)
    }
    
    
    func createSubviews() {

        self.tag = hl_getCustomHudTag()
        
        self.backgroundColor = UIColorFromRGB(rgbValue: 0x000000, alpha: 0.3)
        let containerView = UIView()
        containerView.layer.cornerRadius = 10
        containerView.backgroundColor = .white
        containerView.layer.masksToBounds = true
        
        self.addSubview(containerView)
        containerView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.equalTo(kScreenWidth * 3 / 5.0)
        }
        
        titleLab.numberOfLines = 0
        titleLab.textAlignment = .center
        titleLab.font = UIFont.systemFont(ofSize: 16)
        titleLab.textColor = UIColorFromRGB(rgbValue: 0x333333)
        containerView.addSubview(titleLab)
        titleLab.snp.makeConstraints { make in
            make.left.equalTo(30)
            make.right.equalTo(-30)
            make.top.equalTo(15)
            make.height.greaterThanOrEqualTo(20)
        }
        
        let loadingIView = UIActivityIndicatorView(style: .medium)
        loadingIView.startAnimating()
        containerView.addSubview(loadingIView)
        loadingIView.snp.makeConstraints { make in
            make.top.equalTo(titleLab.snp.bottom).offset(10)
            make.height.width.equalTo(20)
            make.bottom.equalTo(-15)
            make.centerX.equalToSuperview()
        }
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
}
