//
//  SupportVCCustomerSupportCellTableViewCell.swift
//  HormoneLife
//
//  Created by Tank on 2024/6/23.
//

import UIKit

protocol SupportVCCustomerSupportCellTableViewCellDelegate: AnyObject {
    func didTapIns()
    func didTapFacebook()
}
class SupportVCCustomerSupportCellTableViewCell: UITableViewCell {

    @IBOutlet weak var descriptionLabel: UILabel!
    @IBOutlet weak var insButton: UIButton!
    @IBOutlet weak var facebookButton: UIButton!
    
    weak var delegate: SupportVCCustomerSupportCellTableViewCellDelegate?
    
    override func awakeFromNib() {
        super.awakeFromNib()
        // Initialization code
        
        insButton.titleLabel?.underlineWords()
        facebookButton.titleLabel?.underlineWords()
    }

    override func setSelected(_ selected: Bool, animated: Bool) {
        super.setSelected(selected, animated: animated)

        // Configure the view for the selected state
    }
    
    @IBAction func didTapInsAction(_ sender: Any) {
        delegate?.didTapIns()
    }
    
    @IBAction func didTapFacebookAction(_ sender: Any) {
        delegate?.didTapFacebook()
    }
    
}
