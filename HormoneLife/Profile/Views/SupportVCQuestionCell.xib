<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="21701" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21679"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="gilroy regular.otf">
            <string>Gilroy-Regular</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" rowHeight="55" id="KGk-i7-Jjw" customClass="SupportVCQuestionCell" customModule="HormoneLife" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="320" height="55"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="320" height="55"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="Bnt-Cw-w4Z">
                        <rect key="frame" x="24" y="6" width="272" height="30"/>
                        <subviews>
                            <stackView opaque="NO" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="6Id-wr-dtu">
                                <rect key="frame" x="0.0" y="0.0" width="272" height="29"/>
                                <subviews>
                                    <button opaque="NO" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="249" contentHorizontalAlignment="leading" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="CBv-RX-uXY">
                                        <rect key="frame" x="0.0" y="0.0" width="256" height="29"/>
                                        <fontDescription key="fontDescription" name="Gilroy-Regular" family="Gilroy" pointSize="14"/>
                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                        <state key="normal" title="1 . How to use reagent strips">
                                            <color key="titleColor" red="0.20423525570000001" green="0.075626514850000007" blue="0.36441689729999999" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                        </state>
                                    </button>
                                    <button opaque="NO" userInteractionEnabled="NO" contentMode="scaleToFill" contentHorizontalAlignment="trailing" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="p7X-hO-lek">
                                        <rect key="frame" x="256" y="0.0" width="16" height="29"/>
                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                        <state key="normal" image="forwarIcon"/>
                                    </button>
                                </subviews>
                                <constraints>
                                    <constraint firstAttribute="height" constant="50" id="czH-sw-jcF"/>
                                </constraints>
                            </stackView>
                            <view alpha="0.5" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="apY-XY-hz1">
                                <rect key="frame" x="0.0" y="29" width="272" height="1"/>
                                <color key="backgroundColor" red="0.94169229269999999" green="0.92880314590000002" blue="0.9607291818" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="1" id="V2v-2v-2rQ"/>
                                </constraints>
                            </view>
                        </subviews>
                    </stackView>
                </subviews>
                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                <constraints>
                    <constraint firstItem="Bnt-Cw-w4Z" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" constant="6" id="8dd-yA-ONd"/>
                    <constraint firstItem="Bnt-Cw-w4Z" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="24" id="XkY-Xo-ESi"/>
                    <constraint firstAttribute="bottom" secondItem="Bnt-Cw-w4Z" secondAttribute="bottom" id="jtG-z1-dbK"/>
                    <constraint firstAttribute="trailing" secondItem="Bnt-Cw-w4Z" secondAttribute="trailing" constant="24" id="mbh-5s-2bq"/>
                </constraints>
            </tableViewCellContentView>
            <viewLayoutGuide key="safeArea" id="njF-e1-oar"/>
            <connections>
                <outlet property="questionTitle" destination="CBv-RX-uXY" id="eYc-U2-ZDe"/>
            </connections>
            <point key="canvasLocation" x="-47.328244274809158" y="-8.8028169014084519"/>
        </tableViewCell>
    </objects>
    <resources>
        <image name="forwarIcon" width="16" height="16"/>
    </resources>
</document>
