//
//  EditTestDateCycleCell.swift
//  HormoneLife
//
//  Created by Tank on 2024/6/23.
//

import UIKit

protocol EditTestDateCycleCellDelegate: AnyObject {
    func didSeletCycle(_ value: String)
}

class EditTestDateCycleCell: UITableViewCell {

    lazy var pickerView: PickerView = {
        let pickerView = PickerView()
        pickerView.backgroundColor = .clear
        pickerView.scrollingStyle = .infinite
//        pickerView.selectionStyle = .overlay
        pickerView.delegate = self
        pickerView.dataSource = self
        return pickerView
    }()
    let pickerViewBgView = UIView()
    let pickerSelectedView = UIView()

    let titleView = UIView()
    var title: UILabel = {
        let t = UILabel()
        t.font = .mediumGilroyFont(12)
        t.textColor = .mainTextColor
        t.textAlignment = .left
        return t
    }()
    
    let dayTitle: UILabel = {
        let t = UILabel()
        t.font = .mediumGilroyFont(16)
        t.textColor = .mainTextColor
        t.textAlignment = .left
        t.text = "Day"
        return t
    }()
    
    let subTitle: UILabel = {
        let t = UILabel()
        t.font = .mediumGilroyFont(16)
        t.textColor = .mainTextColor
        t.textAlignment = .left
        t.text = "of your Menstrual Cycle"
        return t
    }()
    
    let options: [String] = {
        let hour = 1...50
        let array = hour.map { "\($0)" }
        return array
    }()
    
    weak var delegate: EditTestDateCycleCellDelegate?
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        
        selectionStyle = .none
        contentView.backgroundColor = .lightGrayContentColor
        contentView.addSubview(titleView)
        titleView.addSubview(title)
        contentView.addSubview(pickerViewBgView)
        pickerViewBgView.addSubview(pickerSelectedView)
        pickerViewBgView.addSubview(pickerView)
        pickerSelectedView.addSubview(dayTitle)
        pickerSelectedView.addSubview(subTitle)
        
        titleView.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.height.equalTo(52)
            make.left.right.equalToSuperview()
        }
        titleView.backgroundColor = .white
        
        title.snp.makeConstraints { make in
            make.bottom.equalToSuperview().inset(8)
            make.left.equalToSuperview().inset(20)
        }
        
        pickerViewBgView.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.height.equalTo(80)
            make.top.equalTo(titleView.snp.bottom)
            make.bottom.equalToSuperview()
        }
        pickerViewBgView.backgroundColor = .white
        
        pickerSelectedView.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(20)
            make.height.equalTo(40)
            make.centerY.equalToSuperview()
        }
        pickerSelectedView.backgroundColor = .lightGrayContentColor
        pickerSelectedView.layer.cornerRadius = 4
        
        dayTitle.snp.makeConstraints { make in
            make.leading.equalToSuperview().inset(25)
            make.centerY.equalToSuperview()
        }
        
        pickerView.snp.makeConstraints { make in
            make.height.equalTo(80)
            make.width.equalTo(40)
            make.leading.equalTo(dayTitle.snp.trailing).offset(10)
            make.centerY.equalToSuperview()
        }
        
        subTitle.snp.makeConstraints { make in
            make.leading.equalTo(pickerView.snp.trailing).offset(10)
            make.centerY.equalToSuperview()
        }
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
}

extension EditTestDateCycleCell: PickerViewDelegate, PickerViewDataSource {
    func pickerViewNumberOfRows(_ pickerView: PickerView) -> Int {
        return options.count
    }
    
    func pickerViewHeightForRows(_ pickerView: PickerView) -> CGFloat {
        40
    }
    
    func pickerView(_ pickerView: PickerView, titleForRow row: Int) -> String {
        options[row]
    }
    
    func pickerView(_ pickerView: PickerView, didSelectRow row: Int) {
        delegate?.didSeletCycle("\(options[row])")
    }
    
    func pickerView(_ pickerView: PickerView, styleForLabel label: UILabel, highlighted: Bool) {
        label.textAlignment = .center
        
        if highlighted {
            label.font = .mediumGilroyFont(16.0)
            label.textColor = .mainTextColor
        } else {
            label.font = .mediumGilroyFont(16.0)
            label.textColor = .mainTextColor.withAlphaComponent(0.3)
        }
    }
}
