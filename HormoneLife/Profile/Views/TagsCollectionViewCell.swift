//
//  TagsCollectionViewCell.swift
//  HormoneLife
//
//  Created by Tank on 2024/6/16.
//

import UIKit

class TagsCollectionViewCell: UICollectionViewCell {

    @IBOutlet weak var cellBgView: UIView!
    @IBOutlet weak var tagsButton: UIButton!
    var itemSelected: Bool = false
    
    override func awakeFromNib() {
        super.awakeFromNib()
        cellBgView.layer.cornerRadius = 4
    }
    
    func setupCell(tag: String, isAdded: Bool = false) {
        itemSelected = isAdded
        tagsButton.setTitle(tag, for: .normal)
        
        if isAdded {
            cellBgView.layer.borderWidth = 0
            cellBgView.backgroundColor = .labelBackColor
            tagsButton.setTitleColor(.white, for: .normal)
        } else {
            cellBgView.backgroundColor = .white
            cellBgView.layer.borderColor = UIColor.labelBackColor.cgColor
            cellBgView.layer.borderWidth = 1
            tagsButton.setTitleColor(.labelBackColor, for: .normal)
        }
    }

    override func prepareForReuse() {
        super.prepareForReuse()
        cellBgView.layer.cornerRadius = 4
        if itemSelected {
            cellBgView.layer.borderWidth = 0
            cellBgView.backgroundColor = .labelBackColor
            tagsButton.setTitleColor(.white, for: .normal)
        } else {
            cellBgView.backgroundColor = .white
            cellBgView.layer.borderColor = UIColor.labelBackColor.cgColor
            cellBgView.layer.borderWidth = 1
            tagsButton.setTitleColor(.labelBackColor, for: .normal)
        }
        
//        if itemSelected {
//            tagsButton.setTitleColor(.white, for: .normal)
//            cellBgView.backgroundColor =  UIColor(red: 194/255, green: 171/255, blue: 152/255, alpha: 1)
//        } else {
//            tagsButton.setTitleColor(UIColor(red: 194/255, green: 171/255, blue: 152/255, alpha: 1), for: .normal)
//            cellBgView.backgroundColor = .white
//        }
    }
}
