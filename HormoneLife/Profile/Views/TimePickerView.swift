//
//  TimePickerView.swift
//  HormoneLife
//
//  Created by Tank on 2024/6/23.
//

import UIKit

protocol TimePickerViewDelegate: AnyObject {
    func didTapCancel()
    func didTapSave(_ time: String)
    func didSelect(_ time: String)
}

class TimePickerView: UIView {

    lazy var hourPickerView: PickerView = {
        let pickerView = PickerView()
        pickerView.tag = 0
        pickerView.backgroundColor = .clear
        pickerView.scrollingStyle = .default
        pickerView.delegate = self
        pickerView.dataSource = self
        return pickerView
    }()
    
    lazy var minPickerView: PickerView = {
        let pickerView = PickerView()
        pickerView.tag = 1
        pickerView.backgroundColor = .clear
        pickerView.scrollingStyle = .infinite
        pickerView.delegate = self
        pickerView.dataSource = self
        return pickerView
    }()
    
    lazy var noonPickerView: PickerView = {
        let pickerView = PickerView()
        pickerView.tag = 2
        pickerView.backgroundColor = .clear
        pickerView.scrollingStyle = .default
        pickerView.delegate = self
        pickerView.dataSource = self
        return pickerView
    }()
    
    let pickerSelectedView = UIView()

    var title: UILabel = {
        let t = UILabel()
        t.text = "Reminder Time"
        t.font = .boldGilroyFont(14)
        t.textColor = .mainTextColor
        t.textAlignment = .center
        return t
    }()
    
    lazy var cancelButton: UIButton = {
        let b = UIButton(type: .custom)
        b.backgroundColor = .white
        b.layer.cornerRadius = 4
        b.layer.borderColor = UIColor.mainTextColor.cgColor
        b.layer.borderWidth = 1
        b.setTitle("Cancel", for: .normal)
        b.titleLabel?.font = .regularGilroyFont(16)
        b.setTitleColor(.mainTextColor, for: .normal)
        b.addTarget(self, action: #selector(didTapCancel), for: .touchUpInside)
        return b
    }()
    
    lazy var saveButton: UIButton = {
        let b = UIButton(type: .custom)
        b.backgroundColor = .mainTextColor
        b.layer.cornerRadius = 4
        b.setTitle("Save", for: .normal)
        b.titleLabel?.font = .regularGilroyFont(16)
        b.setTitleColor(.white, for: .normal)
        b.addTarget(self, action: #selector(didTapSave), for: .touchUpInside)
        return b
    }()
    
    weak var delegate: TimePickerViewDelegate?
    
    lazy var hourOption: [String] = {
        let hour = hourStr == "12" ? 1...12 : 0...23
        let array = hour.map { "\($0)" }
        return array
    }()
    
    let minOption: [String] = {
        let minute = 0...59
        let array = minute.map { String(format: "%02d", $0) }
        return array
    }()
    
    var amAndPm = ["AM", "PM"]

    var hourStr = "12"
    var minStr = "00"
    var noonStr = "AM"
    
    init(is24HoursFormat: Bool = false) {
        super.init(frame: .zero)
        setupUI(is24HoursFormat: is24HoursFormat)
    }

    func setupUI(is24HoursFormat: Bool = false) {
        backgroundColor = .white
        addSubview(title)
        addSubview(pickerSelectedView)
        addSubview(hourPickerView)
        addSubview(minPickerView)
        addSubview(noonPickerView)
        addSubview(cancelButton)
        addSubview(saveButton)
        
        title.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(20)
            make.left.right.equalToSuperview().inset(20)
        }
        
        minPickerView.snp.makeConstraints { make in
            make.width.equalTo(20)
            make.height.equalTo(180)
            make.top.equalTo(title.snp.bottom).offset(10)
            make.centerX.equalToSuperview().offset(is24HoursFormat ? 50 : 0)
        }
        
        hourPickerView.snp.makeConstraints { make in
            make.right.equalTo(minPickerView.snp.left).offset(-80)
            make.height.width.top.equalTo(minPickerView)
        }

        noonPickerView.snp.makeConstraints { make in
            make.left.equalTo(minPickerView.snp.right).offset(80)
            make.height.top.equalTo(minPickerView)
            make.width.equalTo(40)
        }
        
        pickerSelectedView.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(20)
            make.height.equalTo(30)
            make.centerY.equalTo(minPickerView)
        }
        pickerSelectedView.backgroundColor = .themeColor
        pickerSelectedView.layer.cornerRadius = 4
        
        cancelButton.snp.makeConstraints { make in
            make.top.equalTo(minPickerView.snp.bottom).offset(10)
            make.height.equalTo(48)
            make.width.equalTo((UIScreen.main.bounds.width - 60)/2)
            make.left.equalToSuperview().inset(20)
            make.bottom.equalToSuperview().inset(32)
        }
        
        saveButton.snp.makeConstraints { make in
            make.height.width.bottom.centerY.equalTo(cancelButton)
            make.left.equalTo(cancelButton.snp.right).offset(20)
        }
    }
    
    @objc func didTapCancel() {
        delegate?.didTapCancel()
    }
    
    @objc func didTapSave() {
        let time = amAndPm.isEmpty ? "\(hourStr):\(minStr)" : "\(hourStr):\(minStr) \(noonStr)"
        delegate?.didTapSave(time)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
}

extension TimePickerView: PickerViewDelegate, PickerViewDataSource {
    func pickerViewNumberOfRows(_ pickerView: PickerView) -> Int {
        if pickerView.tag == 0 {
            return hourOption.count
        } else if pickerView.tag == 1 {
            return minOption.count
        } else {
            return amAndPm.count
        }
    }
    
    func pickerViewHeightForRows(_ pickerView: PickerView) -> CGFloat {
        30
    }
    
    func pickerView(_ pickerView: PickerView, titleForRow row: Int) -> String {
        if pickerView.tag == 0 {
            return hourOption[row]
        } else if pickerView.tag == 1 {
            return minOption[row]
        } else {
            return amAndPm[row]
        }
    }
    
    func pickerView(_ pickerView: PickerView, didSelectRow row: Int) {
        if pickerView.tag == 0 {
            hourStr = hourOption[row]
        } else if pickerView.tag == 1 {
            minStr = minOption[row]
        } else {
            noonStr = amAndPm[row]
        }
        delegate?.didSelect("\(hourStr):\(minStr) \(noonStr)")
    }
    
    func pickerView(_ pickerView: PickerView, styleForLabel label: UILabel, highlighted: Bool) {
        label.textAlignment = .center
        
        if highlighted {
            label.font = .regularGilroyFont(14.0)
            label.textColor = .mainTextColor
        } else {
            label.font = .regularGilroyFont(14.0)
            label.textColor = UIColor.mainTextColor.withAlphaComponent(0.5)
        }
    }
}
