//
//  MessageTableViewCell.swift
//  HormoneLife
//
//  Created by Tank on 2024/6/16.
//

import UIKit

class MessageTableViewCell: UITableViewCell {

    @IBOutlet weak var titleLabel: UILabel!
    @IBOutlet weak var subTitleLabel: UILabel!
    @IBOutlet weak var contentLabel: UILabel!
    @IBOutlet weak var arrowButton: UIButton!
    
    @IBOutlet weak var contentLabelBackView: UIView!
    @IBOutlet weak var numberLabel: UILabel!
    
    var cellType: MessageCellType = .default
    
    override func awakeFromNib() {
        super.awakeFromNib()
        // Initialization code
    }
    
    override func setSelected(_ selected: Bool, animated: Bool) {
        super.setSelected(selected, animated: animated)
        
    }
    
    func setupWith(_ model: Message, cellType: MessageCellType = .default, isButtonHidden: Bool = false, isNumberLabelHidden: Bool = true) {
        titleLabel.text = model.title
        contentLabel.text = model.content
        contentLabelBackView.isHidden = false
        
        let imageName = cellType == .default ? "messageUnreadDot" : "forwarIcon"
        arrowButton.setImage(UIImage(named: imageName), for: .normal)
        
        arrowButton.isHidden = cellType == .default ? (model.isRead == 1) : false
        
        subTitleLabel.isHidden = true //cellType == .reminder
        numberLabel.isHidden = cellType == .default
        if cellType == .reminder {
            numberLabel.text = isNumberLabelHidden ? "" : "NEW"
            numberLabel.textColor = .white
            numberLabel.backgroundColor = UIColor.labelBackColor
            numberLabel.font = .lightGilroyFont(12)
            contentLabelBackView.isHidden = isNumberLabelHidden
        }
        
        if let cont = model.content {
            let contentAttr = try? NSMutableAttributedString(data: cont.data(using: .unicode)!, options: [.documentType : NSAttributedString.DocumentType.html], documentAttributes: nil)
            
            var createTime = ""
            if let sendTime = model.sendTime, sendTime.count > 3 {
                createTime = String(sendTime)
            } else if model.createTime.count > 3 {
                createTime = String(model.createTime)
            }
            
            let timeAttr = NSAttributedString(string: "\(createTime)  ")
            contentAttr?.insert(timeAttr, at: 0)
            
            contentLabel.attributedText = contentAttr
        } else {
            contentLabelBackView.isHidden = true
        }
    }
}

extension MessageTableViewCell {
    enum MessageCellType {
        case reminder
        case `default`
    }
}
