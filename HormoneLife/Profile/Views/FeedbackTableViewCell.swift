//
//  FeedbackTableViewCell.swift
//  HormoneLife
//
//  Created by Tank on 2024/8/5.
//

import UIKit

protocol FeedbackTableViewCellDelegate: AnyObject {
    func didSelected(_ buttonTag: Int)
    func textFiledDidChange(_ text: String?)
}

class FeedbackTableViewCell: UITableViewCell, UITextViewDelegate {

    weak var delegate: FeedbackTableViewCellDelegate?
    
    var backView: UIView = {
        let v = UIView()
        v.backgroundColor = .white
        v.layer.cornerRadius = 8
        v.layer.maskedCorners = [.layerMinXMinYCorner, .layerMaxXMinYCorner]
        return v
    }()
    
    var titleLabel: UILabel = {
        let t = UILabel()
        t.font = .regularGilroyFont(13)
        t.textColor = .mainTextColor
        t.textAlignment = .left
        t.numberOfLines = 0
        t.text = "We greatly appreciate it if you could provide a detailed description of the issue."
        t.setLineHeight(8)
        return t
    }()
    
    var placeholderLabel: UILabel = {
        let t = UILabel()
        t.font = .regularGilroyFont(14)
        t.textColor = .mainTextColor.withAlphaComponent(0.6)
        t.textAlignment = .left
        t.numberOfLines = 0
        t.text = "Leave Your Message"
        return t
    }()
    
    var textCountLabel: UILabel = {
        let t = UILabel()
        t.font = .regularGilroyFont(12)
        t.textColor = .mainTextColor.withAlphaComponent(0.6)
        t.textAlignment = .left
        t.numberOfLines = 0
        t.text = "0/300"
        return t
    }()
    
    lazy var addButton: UIButton = {
        let b = UIButton(type: .custom)
        b.isHidden = true
        b.setImage(UIImage(named: "addButonIcon"), for: .normal)
        b.addTarget(self, action: #selector(didTapApp), for: .touchUpInside)
        return b
    }()
    
    var textViewBackView: UIView = {
        let v = UIView()
        v.backgroundColor = .themeColor
        v.layer.cornerRadius = 8
        return v
    }()
    
    lazy var inputTextView: UITextView = {
        let t = UITextView()
        t.isScrollEnabled = true
        t.returnKeyType = .next
        t.delegate = self
        t.font = .regularGilroyFont(14)
        t.textColor = .mainTextColor
        t.backgroundColor = .clear
        return t
    }()
    
    lazy var popupBackView: UIView = {
        let v = UIView()
        v.backgroundColor = .white
        v.layer.cornerRadius = 4
        v.showShadow(shadowOpacity: 0.1)
        v.isHidden = true
        return v
    }()
    
    lazy var takePhotoButton: UIButton = {
        let b = UIButton(type: .custom)
        b.setImage(UIImage(named: "circleUnCheckedIcon"), for: .normal)
        b.setImage(UIImage(named: "circleCheckedIcon"), for: .selected)
        b.titleEdgeInsets = UIEdgeInsets(top: 0, left: 10, bottom: 0, right: 0)
        b.setTitle("Take a photo", for: .normal)
        b.setTitleColor(.mainTextColor, for: .normal)
        b.titleLabel?.font = .regularGilroyFont(14)
        b.contentHorizontalAlignment = .left
        b.addTarget(self, action: #selector(takePhotoAction), for: .touchUpInside)
        return b
    }()
    
    lazy var chooseGalleryButton: UIButton = {
        let b = UIButton(type: .custom)
        b.setImage(UIImage(named: "circleUnCheckedIcon"), for: .normal)
        b.setImage(UIImage(named: "circleCheckedIcon"), for: .selected)
        b.titleEdgeInsets = UIEdgeInsets(top: 0, left: 10, bottom: 0, right: 0)
        b.setTitle("Choose from galley", for: .normal)
        b.setTitleColor(.mainTextColor, for: .normal)
        b.titleLabel?.font = .regularGilroyFont(14)
        b.contentHorizontalAlignment = .left
        b.addTarget(self, action: #selector(chooseGalleryAction), for: .touchUpInside)
        return b
    }()
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        
        selectionStyle = .none
        contentView.backgroundColor = .themeColor
        setupUI()
    }
    
    @objc func didEndEditing() {
        inputTextView.resignFirstResponder()
    }
    
    @objc func didTapApp() {
        popupBackView.isHidden = !popupBackView.isHidden
    }
    
    @objc func didTapView() {
        popupBackView.isHidden = true
        didEndEditing()
    }
    
    @objc func takePhotoAction(sender: UIButton) {
        didTapView()
        //sender.isSelected = !sender.isSelected
        delegate?.didSelected(0)
    }
    
    @objc func chooseGalleryAction(sender: UIButton) {
        didTapView()
        //sender.isSelected = !sender.isSelected
        delegate?.didSelected(1)
    }
    
    func textViewDidChange(_ textView: UITextView) {
        if textView.text.count > 300 {
            let prefix = textView.text.prefix(300)
            textView.text = String(prefix)
        }
        delegate?.textFiledDidChange(textView.text)
        placeholderLabel.isHidden = textView.text.count > 0
        textCountLabel.text = "\(textView.text.count)/300"
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        contentView.addSubview(backView)
        backView.addSubview(titleLabel)
        backView.addSubview(textViewBackView)
        
        [placeholderLabel, inputTextView, textCountLabel, addButton, popupBackView].forEach(textViewBackView.addSubview)
        
        popupBackView.addSubview(takePhotoButton)
        popupBackView.addSubview(chooseGalleryButton)
        
        let tapGuesture = UITapGestureRecognizer(target: self, action: #selector(didTapView))
        contentView.addGestureRecognizer(tapGuesture)
        
        backView.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(16)
            make.left.right.equalToSuperview().inset(20)
            make.height.equalTo(288)
            make.bottom.equalToSuperview()
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview().inset(16)
        }
        
        textViewBackView.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(16)
            make.top.equalTo(titleLabel.snp.bottom).offset(12)
            make.bottom.equalToSuperview().inset(15)
        }
        
        inputTextView.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(12)
            make.left.equalToSuperview().inset(16)
            make.bottom.right.equalToSuperview().inset(8)
        }
        
        placeholderLabel.snp.makeConstraints { make in
            make.top.equalTo(inputTextView.snp.top).inset(6)
            make.left.equalTo(inputTextView.snp.left).inset(4)
        }
        
        textCountLabel.snp.makeConstraints { make in
            make.bottom.right.equalTo(inputTextView)
        }
        
        addButton.snp.makeConstraints { make in
            make.bottom.right.equalTo(inputTextView)
        }
        
        popupBackView.snp.makeConstraints { make in
            make.width.equalTo(230)
            make.height.equalTo(88)
            make.right.equalTo(addButton)
            make.bottom.equalTo(addButton.snp.top).offset(-8)
        }
        
        takePhotoButton.snp.makeConstraints { make in
            make.left.top.equalToSuperview().inset(16)
            make.right.equalToSuperview()
            make.height.equalTo(20)
        }
        
        chooseGalleryButton.snp.makeConstraints { make in
            make.left.height.equalTo(takePhotoButton)
            make.right.equalToSuperview()
            make.top.equalTo(takePhotoButton.snp.bottom).offset(16)
        }
    }
}
