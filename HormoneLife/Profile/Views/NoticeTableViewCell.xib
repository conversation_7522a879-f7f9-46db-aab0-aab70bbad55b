<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="21701" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21679"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="Gilroy Medium.otf">
            <string>Gilroy-Medium</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" rowHeight="132" id="KGk-i7-Jjw" customClass="NoticeTableViewCell" customModule="HormoneLife" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="320" height="132"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="320" height="132"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="h0C-GE-KmN">
                        <rect key="frame" x="0.0" y="0.0" width="320" height="16"/>
                        <color key="backgroundColor" red="0.94169229269999999" green="0.92880314590000002" blue="0.**********" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="16" id="USc-1R-0FE"/>
                        </constraints>
                    </view>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="md0-ki-L9Z">
                        <rect key="frame" x="20" y="15.999999999999996" width="280" height="48.666666666666657"/>
                        <subviews>
                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="ejw-4y-QTm">
                                <rect key="frame" x="16" y="16" width="248" height="16.666666666666671"/>
                                <subviews>
                                    <stackView opaque="NO" contentMode="scaleToFill" distribution="fillProportionally" translatesAutoresizingMaskIntoConstraints="NO" id="JVX-hV-7Jg">
                                        <rect key="frame" x="0.0" y="0.0" width="248" height="16.666666666666668"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="System Message" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="gFA-gJ-Qca">
                                                <rect key="frame" x="0.0" y="0.0" width="203" height="16.666666666666668"/>
                                                <fontDescription key="fontDescription" name="Gilroy-Medium" family="Gilroy" pointSize="14"/>
                                                <color key="textColor" red="0.18858519200000001" green="0.057947516439999999" blue="0.3526558876" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="center" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="32" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="luU-PL-eaE">
                                                <rect key="frame" x="203" y="6.6666666666666643" width="30" height="10"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="10" id="6yP-Xn-M4L"/>
                                                    <constraint firstAttribute="width" constant="30" id="AIm-29-9FK"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" name="Gilroy-Medium" family="Gilroy" pointSize="12"/>
                                                <color key="textColor" red="0.50876241919999998" green="0.4311534762" blue="0.6075879931" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <button opaque="NO" userInteractionEnabled="NO" contentMode="scaleToFill" contentHorizontalAlignment="right" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="axd-aV-d6Y">
                                                <rect key="frame" x="233" y="1.6666666666666643" width="15" height="15"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="15" id="SZG-9h-mCh"/>
                                                    <constraint firstAttribute="width" constant="15" id="nis-mT-HqC"/>
                                                </constraints>
                                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                <state key="normal" image="forwarIcon"/>
                                            </button>
                                        </subviews>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="20" id="abB-2X-1cn"/>
                                        </constraints>
                                    </stackView>
                                </subviews>
                            </stackView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="HM6-Do-mlb"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstAttribute="trailing" secondItem="ejw-4y-QTm" secondAttribute="trailing" constant="16" id="M4l-pd-Eod"/>
                            <constraint firstItem="ejw-4y-QTm" firstAttribute="leading" secondItem="md0-ki-L9Z" secondAttribute="leading" constant="16" id="QjX-oM-s9a"/>
                            <constraint firstAttribute="bottom" secondItem="ejw-4y-QTm" secondAttribute="bottom" constant="16" id="gzJ-Jn-4cP"/>
                            <constraint firstItem="ejw-4y-QTm" firstAttribute="top" secondItem="md0-ki-L9Z" secondAttribute="top" constant="16" id="vZU-8U-AHV"/>
                        </constraints>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                <integer key="value" value="4"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </view>
                </subviews>
                <color key="backgroundColor" red="0.94169229269999999" green="0.92880314590000002" blue="0.**********" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                <constraints>
                    <constraint firstItem="h0C-GE-KmN" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" id="0l4-SW-RTm"/>
                    <constraint firstItem="md0-ki-L9Z" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="20" id="34D-oW-ihW"/>
                    <constraint firstAttribute="trailing" secondItem="md0-ki-L9Z" secondAttribute="trailing" constant="20" id="L7R-TC-xR2"/>
                    <constraint firstItem="md0-ki-L9Z" firstAttribute="top" secondItem="h0C-GE-KmN" secondAttribute="bottom" id="UoJ-NZ-4E4"/>
                    <constraint firstAttribute="trailing" secondItem="h0C-GE-KmN" secondAttribute="trailing" id="WuA-XO-DTR"/>
                    <constraint firstItem="h0C-GE-KmN" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" id="YXN-cC-oKn"/>
                    <constraint firstAttribute="bottom" secondItem="md0-ki-L9Z" secondAttribute="bottom" id="b5b-W7-PwV"/>
                </constraints>
            </tableViewCellContentView>
            <viewLayoutGuide key="safeArea" id="njF-e1-oar"/>
            <connections>
                <outlet property="button" destination="axd-aV-d6Y" id="0hS-RJ-87g"/>
                <outlet property="numberLabel" destination="luU-PL-eaE" id="0HF-tW-ly8"/>
                <outlet property="titleLabel" destination="gFA-gJ-Qca" id="bKl-LL-18D"/>
            </connections>
            <point key="canvasLocation" x="77.862595419847324" y="19.718309859154932"/>
        </tableViewCell>
    </objects>
    <resources>
        <image name="forwarIcon" width="16" height="16"/>
    </resources>
</document>
