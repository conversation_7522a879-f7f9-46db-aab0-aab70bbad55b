//
//  CycleReportHeaderView.swift
//  HormoneLife
//
//  Created by Tank on 2024/9/1.
//

import UIKit

class CycleReportHeaderView: UIView {

    let subView1: CycleReportHeaderSubView = {
        let v = CycleReportHeaderSubView()
        v.title.text = Interface.shared().loggedInUser?.userInfo.menstruationCycleAvg ?? "0"
        v.subtitle.text = "Cycle Length\n（Average days）"
        return v
    }()
    
    let subView2: CycleReportHeaderSubView = {
        let v = CycleReportHeaderSubView()
        v.title.text = Interface.shared().loggedInUser?.userInfo.period ?? "0"
        v.subtitle.text = "Period Length\n（Average days）"
        return v
    }()
    
    override init(frame: CGRect) {
        super.init(frame: CGRect(x: 0, y: 0, width: UIScreen.main.bounds.width, height: 132))
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        [subView1, subView2].forEach(addSubview)
        
        subView1.snp.makeConstraints { make in
            make.height.equalTo(96)
            make.top.left.equalToSuperview().inset(20)
            make.bottom.equalToSuperview().inset(16)
            make.right.equalTo(snp.centerX).offset(-8)
        }
        
        subView2.snp.makeConstraints { make in
            make.right.equalToSuperview().inset(20)
            make.top.bottom.equalTo(subView1)
            make.left.equalTo(snp.centerX).offset(8)
        }
    }
}

class CycleReportHeaderSubView: UIView {
    var title: UILabel = {
        let t = UILabel()
        t.font = .mediumGilroyFont(24)
        t.textColor = .mainTextColor
        t.textAlignment = .center
        t.text = "0"
        return t
    }()
    
    var subtitle: UILabel = {
        let t = UILabel()
        t.font = .mediumGilroyFont(12)
        t.textColor = .mainTextColor
        t.textAlignment = .center
        t.numberOfLines = 0
        t.setLineHeight(9)
        t.text = "Cycle Length\n（Average days）"
        return t
    }()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        backgroundColor = .white
        [title, subtitle].forEach(addSubview)
        
        title.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalToSuperview().inset(16)
        }
        
        subtitle.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.left.right.equalToSuperview().inset(20)
            make.top.equalTo(title.snp.bottom).offset(4)
            make.bottom.equalToSuperview().inset(16)
        }
        
        layer.cornerRadius = 8
    }
}
