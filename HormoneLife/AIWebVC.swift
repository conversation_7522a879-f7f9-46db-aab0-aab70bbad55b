import UIKit
import WebKit
import SnapKit

/// A simple WebView controller for AI functionality
class AIWebVC: UIViewController {
    
    // MARK: - Properties
    private var webView = WKWebView(frame: .zero)
    private var loadingIndicator: UIActivityIndicatorView!
    
    // Chat SDK URL discovered from logs
    private let chatSDKURL = "https://welllifeusa.com/chatbot.html"
    // private let chatSDKURL = "https://uchat.com.au"//弃用
    
    // Current loading method - using directToSDK (Mode 3) as it should directly open the chat
    var currentLoadMethod: LoadMethod = .directToSDK
    
    // Available loading methods
    enum LoadMethod: Int {
        case htmlWithJS = 0    // Using HTML with embedded JS
        case directURL = 1     // Directly accessing UChat URL
        case customWebsite = 2 // Accessing custom website
        case directToSDK = 3   // Directly accessing SDK URL
    }
    
    // MARK: - LifeCycle
    override func viewDidLoad() {
        super.viewDidLoad()
        title = "hormonelife"
        view.backgroundColor = .themeColor
        
        setupUI()
        setupWebView()
        loadContent()
    }
    
    // MARK: - Setup Methods
    private func setupUI() {
        // Add loading indicator
        loadingIndicator = UIActivityIndicatorView(style: .large)
        loadingIndicator.color = .gray
        loadingIndicator.hidesWhenStopped = true
        view.addSubview(loadingIndicator)
        loadingIndicator.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }
        loadingIndicator.startAnimating()
    }
    
    private func setupWebView() {
        // Configure WKWebView
        let configuration = WKWebViewConfiguration()
        configuration.allowsInlineMediaPlayback = true
        configuration.mediaTypesRequiringUserActionForPlayback = []
        
        // Enable JavaScript
        configuration.preferences.javaScriptEnabled = true
        
        // Customize user agent for testing
        if let originalUserAgent = WKWebView().value(forKey: "userAgent") as? String {
            configuration.applicationNameForUserAgent = "Mobile/Safari"
        }
        
        let webView = WKWebView(frame: .zero, configuration: configuration)
        self.webView = webView
        
        webView.navigationDelegate = self
        webView.uiDelegate = self
        webView.scrollView.showsHorizontalScrollIndicator = false
        webView.scrollView.showsVerticalScrollIndicator = false
        webView.isOpaque = false
        webView.backgroundColor = .clear
        
        view.addSubview(webView)
        
        // 设置webView的约束
        webView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }
    }
    
    // MARK: - Content Loading Methods
    private func loadContent() {
        loadingIndicator.startAnimating()
        
        switch currentLoadMethod {
        case .htmlWithJS:
            loadUChatWidget()
        case .directURL:
            loadDirectUChatURL()
        case .customWebsite:
            loadCustomWebsite()
        case .directToSDK:
            loadChatSDK()
        }
    }
    
    private func loadUChatWidget() {
        let html = """
        <!DOCTYPE html>
        <html>
          <head>
            <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
            <style>
              body {
                margin: 0;
                padding: 0;
                background-color: #f8f8f8;
                font-family: -apple-system, system-ui, BlinkMacSystemFont;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                height: 100vh;
              }
              .debug-info {
                text-align: center;
                margin-bottom: 20px;
                color: #666;
              }
            </style>
          </head>
          <body>
            <div class="debug-info">
              <p>Loading UChat...</p>
              <p>Please wait a moment</p>
            </div>
            
            <!-- UChat script reference -->
            <script async defer src="https://www.uchat.com.au/js/widget/atqkoxj2suyskv2b/float.js"></script>
            
            <!-- Debug script -->
            <script>
              window.onload = function() {
                document.querySelector('.debug-info').innerHTML = '<p>Page loaded</p><p>If chat is not visible, try another mode</p>';
              }
            </script>
          </body>
        </html>
        """
        
        webView.loadHTMLString(html, baseURL: nil)
    }
    
    private func loadDirectUChatURL() {
        // Access UChat URL directly
        if let url = URL(string: "https://uchat.com.au") {
            var request = URLRequest(url: url, cachePolicy: .reloadIgnoringLocalAndRemoteCacheData)
            request.allHTTPHeaderFields = [
                "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148"
            ]
            webView.load(request)
        }
    }
    
    private func loadCustomWebsite() {
        // Try more possible URLs
        let possibleURLs = [
            "https://welllifeusa.com/uchat.html", 
            "https://app.uchat.com.au",
            "https://www.uchat.com.au/app"
        ]
        
        if let url = URL(string: possibleURLs[0]) {
            let request = URLRequest(url: url, cachePolicy: .reloadIgnoringLocalAndRemoteCacheData)
            webView.load(request)
        }
    }
    
    private func loadChatSDK() {
        // Directly load the SDK URL discovered from the logs
        if let url = URL(string: chatSDKURL) {
            var request = URLRequest(url: url, cachePolicy: .reloadIgnoringLocalAndRemoteCacheData)
            request.allHTTPHeaderFields = [
                "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148"
            ]
            webView.load(request)
        }
    }
    
    @objc private func closeButtonTapped() {
        dismiss(animated: true)
    }
}

// MARK: - WKNavigationDelegate
extension AIWebVC: WKNavigationDelegate {
    func webView(_ webView: WKWebView, didStartProvisionalNavigation navigation: WKNavigation!) {
        loadingIndicator.startAnimating()
    }
    
    func webView(_ webView: WKWebView, decidePolicyFor navigationAction: WKNavigationAction, decisionHandler: @escaping (WKNavigationActionPolicy) -> Void) {
        decisionHandler(.allow)
    }
    
    func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
        loadingIndicator.stopAnimating()
        // 不再动态获取页面标题
    }
    
    func webView(_ webView: WKWebView, didFail navigation: WKNavigation!, withError error: Error) {
        loadingIndicator.stopAnimating()
    }
    
    func webView(_ webView: WKWebView, didFailProvisionalNavigation navigation: WKNavigation!, withError error: Error) {
        // Ignore error -999, which is just a navigation cancellation
        if (error as NSError).code != -999 {
            loadingIndicator.stopAnimating()
        }
    }
}

// MARK: - WKUIDelegate
extension AIWebVC: WKUIDelegate {
    // Handle JavaScript alerts
    func webView(_ webView: WKWebView, runJavaScriptAlertPanelWithMessage message: String, initiatedByFrame frame: WKFrameInfo, completionHandler: @escaping () -> Void) {
        let alert = UIAlertController(title: "提示", message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default, handler: { _ in
            completionHandler()
        }))
        present(alert, animated: true)
    }
    
    // Handle JavaScript confirm panels
    func webView(_ webView: WKWebView, runJavaScriptConfirmPanelWithMessage message: String, initiatedByFrame frame: WKFrameInfo, completionHandler: @escaping (Bool) -> Void) {
        let alert = UIAlertController(title: "确认", message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default, handler: { _ in
            completionHandler(true)
        }))
        alert.addAction(UIAlertAction(title: "取消", style: .cancel, handler: { _ in
            completionHandler(false)
        }))
        present(alert, animated: true)
    }
} 
