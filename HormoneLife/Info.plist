<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key> Privacy - Camera Usage Description</key>
	<string>Do you allow HORMONElife to access the photo album? So that you can use services such as scanning, image uploading, and image saving normally</string>
	<key> Privacy - Photo Library Usage Description</key>
	<string>Do you allow HORMO<PERSON>life to access the photo album? So that you can use services such as scanning, image uploading, and image saving normally</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleURLName</key>
			<string></string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.googleusercontent.apps.162503198833-b3bnn1893dop6hrgk26c4s2ubejf6frq</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleURLName</key>
			<string></string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>fb1309270503387193</string>
			</array>
		</dict>
	</array>
	<key>FacebookAppID</key>
	<string>1309270503387193</string>
	<key>FacebookClientToken</key>
	<string>EAAHGpT7XGkoBOZBAW41uAn2xQNEUKSlWsr1z3YXQ4S90pmtQ05ZCMzEzWHQRqnDMOQy5kofyUqYLIY9Kovnv3it7vYq1jpZB92Yn84ZAWca0zehF1BW8pr0CA2dGORVX9aWaHcTmRqsZB7tWohuuffH3WTKHZCA6j5JW87wi8urpcZCifTIgM1GGKwI3VfWeaCYpPBAhm1ZB1fLEP46Vgrs3ZAHcTkAZDZD</string>
	<key>FacebookDisplayName</key>
	<string>HormoneLife</string>
	<key>FirebaseAutomaticScreenReportingEnabled</key>
	<false/>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>fbapi</string>
		<string>fbapi20130214</string>
		<string>fbapi20130410</string>
		<string>fbapi20130702</string>
		<string>fbapi20131010</string>
		<string>fbapi20131219</string>
		<string>fbapi20140410</string>
		<string>fbapi20140116</string>
		<string>fbapi20150313</string>
		<string>fbapi20150629</string>
		<string>fbapi20160328</string>
		<string>fbauth</string>
		<string>fb-messenger-share-api</string>
		<string>fbauth2</string>
		<string>fbshareextension</string>
	</array>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
	</dict>
	<key>UIAppFonts</key>
	<array>
		<string>Gilroy</string>
	</array>
	<key>UIBackgroundModes</key>
	<array>
		<string>remote-notification</string>
	</array>
</dict>
</plist>
